{"skeleton": {"hash": "BI177BjM2Dz0Ozr+JseX9w/JqxM", "spine": "3.8.99", "x": -74.77, "y": -82.26, "width": 151, "height": 319.56, "images": "./images/", "audio": "C:/Users/<USER>/Desktop/碧蓝航线小游戏切图/动效/海域巡逻"}, "bones": [{"name": "root"}, {"name": "haiyu", "parent": "root"}, {"name": "title", "parent": "haiyu", "x": 1.82, "y": -52.13}, {"name": "dian", "parent": "haiyu", "x": 9.77, "y": 228.79}, {"name": "yuan", "parent": "haiyu"}, {"name": "dian2", "parent": "haiyu", "x": 9.77, "y": 228.79}, {"name": "dian3", "parent": "haiyu", "x": 9.77, "y": 228.79}, {"name": "quan1", "parent": "haiyu", "y": -1}, {"name": "quan2", "parent": "haiyu", "y": -1}], "slots": [{"name": "haiyu", "bone": "haiyu", "attachment": "haiyu"}, {"name": "title", "bone": "title", "attachment": "title"}, {"name": "yuan", "bone": "yuan", "attachment": "yuan", "blend": "additive"}, {"name": "dian", "bone": "dian", "color": "53dcff00", "attachment": "dian", "blend": "additive"}, {"name": "dian2", "bone": "dian2", "color": "53dcff00", "attachment": "dian", "blend": "additive"}, {"name": "dian3", "bone": "dian3", "color": "53dcff00", "attachment": "dian", "blend": "additive"}, {"name": "quan1", "bone": "quan1", "color": "ffffffc7", "attachment": "quan1", "blend": "additive"}, {"name": "quan2", "bone": "quan2", "color": "ffffffc7", "attachment": "quan2", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"dian": {"dian": {"x": -0.25, "y": 0.01, "width": 17, "height": 17}}, "dian2": {"dian": {"x": -0.25, "y": 0.01, "width": 17, "height": 17}}, "dian3": {"dian": {"x": -0.25, "y": 0.01, "width": 17, "height": 17}}, "haiyu": {"haiyu": {"x": 0.73, "y": -4.76, "width": 151, "height": 155}}, "quan1": {"quan1": {"y": 0.75, "width": 106, "height": 106}}, "quan2": {"quan2": {"y": 0.57, "width": 106, "height": 106}}, "title": {"title": {"x": -3.09, "y": 1.86, "width": 101, "height": 30}}, "yuan": {"yuan": {"width": 84, "height": 84}}}}], "animations": {"animation": {"slots": {"dian": {"color": [{"color": "53dcff00", "curve": "stepped"}, {"time": 1, "color": "53dcff00"}, {"time": 1.3333, "color": "53dcffff"}, {"time": 2, "color": "53dcff00", "curve": "stepped"}, {"time": 2.1667, "color": "53dcff00"}, {"time": 2.5, "color": "53dcffff"}, {"time": 3.1667, "color": "53dcff00"}]}, "dian2": {"color": [{"color": "53dcff00", "curve": "stepped"}, {"time": 0.5, "color": "53dcff00"}, {"time": 0.8333, "color": "53dcffff"}, {"time": 1.5, "color": "53dcff00", "curve": "stepped"}, {"time": 2, "color": "53dcff00"}, {"time": 2.3333, "color": "53dcffff"}, {"time": 3, "color": "53dcff00"}]}, "dian3": {"color": [{"color": "53dcff00", "curve": "stepped"}, {"time": 0.1667, "color": "53dcff00"}, {"time": 0.5, "color": "53dcffff"}, {"time": 1.1667, "color": "53dcff00", "curve": "stepped"}, {"time": 2.8333, "color": "53dcff00"}, {"time": 3.1667, "color": "53dcffff"}, {"time": 3.8333, "color": "53dcff00"}]}, "yuan": {"color": [{"color": "ffffffc7"}, {"time": 2, "color": "ffffffff"}, {"time": 4, "color": "ffffffc7"}]}}, "bones": {"yuan": {"rotate": [{}, {"time": 2, "angle": 180}, {"time": 4}]}, "dian": {"translate": [{"x": -14.98, "y": -200.73, "curve": "stepped"}, {"time": 1, "x": -14.98, "y": -200.73, "curve": "stepped"}, {"time": 2, "x": -14.98, "y": -200.73}, {"time": 2.1667, "x": 22.32, "y": -229.61, "curve": "stepped"}, {"time": 3.1667, "x": 22.32, "y": -229.61}], "scale": [{"curve": "stepped"}, {"time": 1}, {"time": 1.3333, "x": 1.5, "y": 1.5}, {"time": 2, "x": 2, "y": 2}, {"time": 2.1667, "x": 0.5, "y": 0.5}, {"time": 2.5}, {"time": 3.1667, "x": 1.5, "y": 1.5}]}, "dian2": {"translate": [{"x": -32.94, "y": -243.22, "curve": "stepped"}, {"time": 0.5, "x": -32.94, "y": -243.22, "curve": "stepped"}, {"time": 1.5, "x": -32.94, "y": -243.22}, {"time": 2, "x": 15.53, "y": -202.29, "curve": "stepped"}, {"time": 3, "x": 15.53, "y": -202.29}], "scale": [{"curve": "stepped"}, {"time": 0.5}, {"time": 0.8333, "x": 2, "y": 2}, {"time": 1.5, "x": 3, "y": 3, "curve": "stepped"}, {"time": 2, "x": 0.5, "y": 0.5}, {"time": 2.3333, "x": 0.7, "y": 0.7}, {"time": 3}]}, "dian3": {"translate": [{"x": -9.62, "y": -261.92, "curve": "stepped"}, {"time": 0.1667, "x": -9.62, "y": -261.92, "curve": "stepped"}, {"time": 1.1667, "x": -9.62, "y": -261.92}, {"time": 2.8333, "x": -31.88, "y": -217.82, "curve": "stepped"}, {"time": 3.8333, "x": -31.88, "y": -217.82}], "scale": [{}, {"time": 0.1667, "x": 0.5, "y": 0.5}, {"time": 0.5, "x": 0.7, "y": 0.7}, {"time": 1.1667, "curve": "stepped"}, {"time": 2.8333, "x": 0.5, "y": 0.5}, {"time": 3.1667, "x": 0.7, "y": 0.7}, {"time": 3.8333}]}, "quan1": {"rotate": [{}, {"time": 4, "angle": 180}]}, "quan2": {"rotate": [{}, {"time": 2, "angle": 90}, {"time": 4, "angle": 180}]}}}}}