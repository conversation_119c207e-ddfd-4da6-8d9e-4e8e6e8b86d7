import blhkc from "../utils/blhkc";
import blhBaseData from "./blhBaseData";



export default class blhBuildData extends blhBaseData {

    /**英雄id */
    heroId: number = 0;
    /**建造概率 */
    buildRate: number = 0;
    /**奖励概率 */
    rewardRate: number = 0;


   
    constructor(conf: any) {
        super(conf);
        this.heroId = parseInt(conf.heroId);
        this.buildRate = parseInt(conf.buildRate);
        this.rewardRate = parseInt(conf.rewardRate);

    }
}