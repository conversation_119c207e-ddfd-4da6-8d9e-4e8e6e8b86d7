import blhkc from "../utils/blhkc";
import blhBaseData from "./blhBaseData";



export default class blhEnemyData extends blhBaseData {

    name: string = null;
    spine: string = null;
    skin: string = null;
    atkAni: string = 'atk';
    deadAni: string = 'dead';

    type: number = 0;
    hp: number = 0;
    // x: number = 0;
    // y: number = 0;
    scale: number = 1;
    atk: number = 0;
    exp: number = 0;
    // bulletId: number = 0;
    // aiId: number = 0;
    // //障碍物爆炸类型
    // boomtype: number = 0;
    /** 是否飞行,飞行时重力值要调整 */
    // isFly: number = 0;
    speed: number = 0;
    /** 是否不被索敌,非0值不被索敌 */
    // notTarget: number = 0;
    /** 需要加载的关联prefab,逗号分隔 */
    // preArr: Array<string> = null;

    // subEnemys: any[] = null;

    /** 特殊属性 */
    sp:string = null;

    // EnemyType: any;

    constructor(conf: any) {
        super(conf);
        this.name = conf.name;
        this.spine = conf.spine;
        this.skin = conf.skin;
        // this.x = conf.x || 0;
        // this.y = conf.y || 0;
        this.scale = conf.scale || 1;
        this.atk = conf.atk || 0;
        this.type = conf.type || 0;
        this.hp = conf.hp || 0;
        // this.bulletId = conf.bulletId || 0;
        // this.aiId = conf.aiId || 0;
        // this.cX = conf.cX || 0;
        // this.cY = conf.cY || 0;
        // this.cW = conf.cW || 0;
        // this.cH = conf.cH || 0;
        // this.isFly = conf.isFly || 0;
        // this.isE2 = conf.isE2 || 0;
        // this.team = conf.team || 0;
        // this.subEnemys = conf.subEnemys || null;
        this.sp = conf.sp || null;
        // this.sp2 = conf.sp2 || null;
        // this.boomtype = conf.boomtype || 0;
        this.speed = conf.speed || 0;
        this.exp = conf.exp || 0;
        // this.atkAni = conf.atkAni || '';
        // this.deadAni = conf.deadAni || '';
        // this.preArr = blhkc.strArr(conf.pre);
        // this.notTarget = conf.notTarget || 0;
    }


    clone(): blhEnemyData {
        return new blhEnemyData(this);
    }

}