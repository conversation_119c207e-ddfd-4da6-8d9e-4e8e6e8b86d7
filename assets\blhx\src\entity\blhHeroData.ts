import blhkc from "../utils/blhkc";
import blhBaseData from "./blhBaseData";



export default class blhHeroData extends blhBaseData {

    name: string = null;
    /** 职业 */
    career: number = 0;
    /** 属性,5大属性之一 */
    prop: number = 0;
    /** 稀有度 */
    rare: number = 0;
    /** 初始星级 */
    star: number = 0;
    hp: number = 0;
    atk: number = 0;
    /** 暴击率 */
    crit: number = 0;
    /** 减伤 */
    deAtk: number = 0;
    /** 初始技能id */
    skillArr:Array<number> = [];

    spine:string = null;


    /** 送礼:[道具编号,增加好感度] */
    gift: Array<number> = null;
    /** 对话:从数组中随机一个 */
    talk: Array<number> = null;
    /** 誓约:[道具,时装] */
    vow: Array<number> = null;
   
    constructor(conf: any) {
        super(conf);
        this.name = blhkc.str(conf.name);
        this.career = parseInt(conf.career);
        this.prop = parseInt(conf.prop);
        this.rare = parseInt(conf.rare);
        this.star = parseInt(conf.star);
        this.hp = parseInt(conf.hp);
        this.atk = parseInt(conf.atk);
        this.crit = parseInt(conf.crit);
        this.deAtk = parseInt(conf.deAtk);
        this.gift = blhkc.strNumArr(conf.gift);
        this.talk = blhkc.strNumArr(conf.talk);
        this.vow = blhkc.strNumArr(conf.vow);
        this.skillArr = blhkc.strNumArr(conf.skills);
        this.spine = blhkc.str(conf.spine);
    }
}