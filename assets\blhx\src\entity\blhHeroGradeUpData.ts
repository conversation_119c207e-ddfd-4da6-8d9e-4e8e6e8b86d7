import blhBaseData from "./blhBaseData";



export default class blhHeroGradeUpData extends blhBaseData {

    /**英雄阶数 */
    grade: number = 0;
    /**阶数对应的最大等级 */
    maxLv: number = 0;
    /**每阶对应的攻击 */
    gradeAtk: number = 0;
    /**每阶对应的生命值 */
    gradeHp: number = 0;
    /**升阶需要消耗的心智单元 */
    costXzdy: number = 0;
    constructor(conf: any) {
        super(conf);
        this.grade = parseInt(conf.grade);
        this.maxLv = parseInt(conf.maxLv);
        this.gradeAtk = parseInt(conf.gradeAtk);
        this.gradeHp = parseInt(conf.gradeHp);
        this.costXzdy = parseInt(conf.costXzdy);
    }
}