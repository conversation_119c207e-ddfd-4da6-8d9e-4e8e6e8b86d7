import blhBaseData from "./blhBaseData";



export default class blhHeroLvUpData extends blhBaseData {

    /**英雄等级 */
    heroLv: number = 0;
    /**英雄攻击力 */
    heroAtk: number = 0;
    /**英雄生命值 */
    heroHp: number = 0;
    /**战术素材(消耗品) */
    costzsjc: number = 0;
    /**通用物资(消耗) */
    costgoods: number = 0;


   
    constructor(conf: any) {
        super(conf);
        this.heroLv = parseInt(conf.heroLv);
        this.heroAtk = parseInt(conf.heroAtk);
        this.heroHp = parseInt(conf.heroHp);
        this.costzsjc = parseInt(conf.costzsjc);
        this.costgoods = parseInt(conf.costgoods);
    }
}