import blhkc from "../utils/blhkc";
import blhBaseData from "./blhBaseData";



export default class blhRankData extends blhBaseData {

    chapter: number = 0;
    part: number = 0;
    drop1: number = 0;
    reward1: number = 0;
    reward2: number = 0;
    reward3: number = 0;

    rewardData: string = null;
   
    constructor(conf: any) {
        super(conf);
        this.rewardData = conf.rewardData.trim();
        this.chapter = parseInt(conf.chapter);
        this.part = parseInt(conf.part);
        this.drop1 = parseFloat(conf.drop1);
        this.reward1 = parseInt(conf.reward1);
        this.reward2 = parseInt(conf.reward2);
        this.reward3 = parseInt(conf.reward3);
    }
}