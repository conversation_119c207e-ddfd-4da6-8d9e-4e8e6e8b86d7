import blhkc from "../utils/blhkc";
import blhBaseData from "./blhBaseData";



export default class blhSkillData extends blhBaseData {

    /** 从属于某个主技能的主技能id */
    sid: number = 0;

    /** 英雄id，不读配置, 由hero使用时赋值 */
    heroId: number = 0;

    name: string = null;
    /** 类型:基础/天赋 */
    type: string = null;
    /** 主动被动 */
    passive: string = null;
    /** 归属于技能 */
    belong: number = 0;
    /** 是否3选1 */
    c3: number = 0;
    /** 功能 */
    func: string = null;
    /** 触发条件 */
    cond: string = null;
    /** 加强技能 */
    enhance: number = 0;
    cd: number = 0;
    /** 限制:如地面 */
    limit: string = null;
    /** 范围:单体/群体 */
    range: string = null;
    /** 攻击次数 */
    atkTimes: number = 0;
    atkFix: number = 0;
    /** 3选1等级要求 */
    c3lv: number = 0;
    /** 3选1前置技能 */
    preSkill: number = 0;
    /** 3选1激活条件:n阶/n星 */
    activeMap: Map<string, number> = null;
    /** 3选1权重 */
    weight: number = 0;
    /** 3选1随机次数,为1时只能选择1次 */
    rand: number = 0;
    /** 描述 */
    desc: string = null;

    constructor(conf: any) {
        super(conf);
        this.sid = parseInt(conf.sid);
        this.name = blhkc.str(conf.name);
        this.type = blhkc.str(conf.type);
        this.passive = blhkc.str(conf.passive);
        this.belong = parseInt(conf.belong);
        this.func = blhkc.str(conf.func);
        this.cond = blhkc.str(conf.cond);
        this.c3lv = parseInt(conf.c3lv);
        this.enhance = parseInt(conf.enhance);
        this.cd = parseInt(conf.cd);
        this.limit = blhkc.str(conf.limit);
        this.range = blhkc.str(conf.range);
        this.atkTimes = parseInt(conf.atkTimes);
        this.atkFix = conf.atkFix ? parseFloat(conf.atkFix) : 0;
        this.activeMap = blhkc.strMap(conf.active);
        this.weight = parseInt(conf.weight);
        this.rand = parseInt(conf.rand);
        this.desc = blhkc.str(conf.desc);
        this.c3 = parseInt(conf.c3);
        this.preSkill = parseInt(conf.preSkill);
    }

    setHero(heroId: number): blhSkillData {
        this.heroId = heroId;
        return this;
    }
}