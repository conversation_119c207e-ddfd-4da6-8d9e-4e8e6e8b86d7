import blhkc from "../utils/blhkc";
import blhBaseData from "./blhBaseData";



export default class blhWaveData extends blhBaseData {

    rank:string = null;
    /** 波次 */
    wave: number = 0;
    /** 波次开始时间 */
    startTime: number = 0;

    expFix: number = 0;
    hpFix: number = 0;
    atkFix: number = 0;
    speedFix: number = 0;
    /** 下一波 */
    nextWave:blhWaveData = null;

    /** 出怪,[[怪id,数量],...] */
    enemyIds: Array<Array<number>> = null;
   
    constructor(conf: any) {
        super(conf);
        this.enemyIds = blhkc.strNumArr2(conf.enemyIds);
        this.rank = blhkc.str(conf.rank);
        this.wave = parseInt(conf.wave);
        this.startTime = parseInt(conf.startTime);
        this.expFix = parseInt(conf.expFix);
        this.hpFix = parseInt(conf.hpFix);
        this.atkFix = parseInt(conf.atkFix);
        this.speedFix = parseInt(conf.speedFix);
    }
}