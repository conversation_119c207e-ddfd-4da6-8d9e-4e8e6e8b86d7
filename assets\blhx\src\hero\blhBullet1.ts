/**
 * 图片类型的子弹
 * 
 */

import blhColliMgr from "../mgr/blhColliMgr";
import { BattleState, Tag } from "../mgr/blhConst";
import blhResMgr from "../mgr/blhResMgr";
import { blhStatic } from "../mgr/blhStatic";
import blhBullet from "../tools/blhBullet";
import blhShooter1 from "./blhShooter1";


const { ccclass, property } = cc._decorator;
@ccclass
export default class blhBullet1 extends blhBullet {
    @property(cc.Sprite)
    sprite: cc.Sprite = null;

    shooter: blhShooter1 = null;

    rotateSpeed: number = 0;

    init(shooter: blhShooter1, tag: number, onInit: () => void = null): blhBullet {
        super.init(shooter, tag, onInit);
        if (this.shooter.bulletConf.pic) {
            this.sprite.spriteFrame = blhResMgr.ins().getImg(this.shooter.bulletConf.pic);
        }
        blhColliMgr.ins().addBulletA(this);
        return this;
    }

    remove(): void {
        this.isRemoved = true;
        blhColliMgr.ins().delBulletA(this.uuid);
        // this.shooter.pool.back(this.node);
        super.remove();
    }

    hitEff(pos: cc.Vec2) {
        if (!this.onHitEffId) {
            return;
        }
        // console.log('子弹击中特效');
        blhStatic.effMgr.createEff(this.onHitEffId, pos.x, pos.y, blhStatic.battle.layer_effect);
    }

    //特殊动作
    spActionMap = {
        //分裂,需要splitNum,splitAngle两者配合
        'splitNum': (pos: cc.Vec2) => {
            const angle = this.orgAngle;
            const sliceAngle = this.spConf.splitAngle / (this.spConf.splitNum - 1);
            let startSplitAngle = angle - this.spConf.splitAngle / 2;
            for (let i = 0; i < this.spConf.splitNum; i++) {
                const splitBullet: blhBullet1 = this.shooter.mkBullet() as blhBullet1;
                const newAngle = startSplitAngle + sliceAngle * i;
                splitBullet.setAnglePosScale(newAngle, pos);
                splitBullet.node.parent = this.node.parent;
                splitBullet.isLast = i === this.spConf.splitNum - 1;
                if (this.spConf.bulletConf) {
                    splitBullet.updateConf(this.spConf.bulletConf);
                    if (this.spConf.bulletConf.pic) {
                        splitBullet.sprite.spriteFrame = blhResMgr.ins().getImg(this.spConf.bulletConf.pic);
                    }
                }
                if (this.spConf.dmgScale) { //伤害变化
                    splitBullet.dmg *= this.spConf.dmgScale;
                }
                const targetPos = this.shooter.angleDistToTarget(pos, newAngle, this.shooter.shootRange);
                this.shooter.shootItem(splitBullet, targetPos, null);
            }
        },
    };

    onHit(other: cc.Collider, self: cc.Collider): void {
        if (other.tag !== Tag.enemy) {
            return;
        }
        const bulPos: cc.Vec2 = this.node.getPosition();
        this.hitEff(bulPos);
        if (!this.canHitThrough()) {
            //一定要从shooter移除
            this.shooter.removeItem(this);
        }
        if (this.spConf) {
            for (const key in this.spConf) {
                const act = this.spActionMap[key];
                if (act) {
                    act.call(this, bulPos);
                }
            }
        }
    }


    protected update(dt: number): void {
        if (blhStatic.battleState !== BattleState.run) {
            return;
        }
        if (this.rotateSpeed) {
            this.node.angle += this.rotateSpeed;
        }
        if (this.node.x > blhStatic.halfViewWidth + 50 || this.node.y > blhStatic.halfViewHeight + 50 || this.node.x < -blhStatic.halfViewWidth - 50 || this.node.y < -blhStatic.halfViewHeight - 50) {
            this.shooter.removeItem(this);
        }
    }
}