/**
 * 图片类型的子弹
 * 
 */

import blhEnemy from "../items/blhEnemy";
import blhColliMgr from "../mgr/blhColliMgr";
import { BattleState, BulletAngleStartType, Tag } from "../mgr/blhConst";
import blhResMgr from "../mgr/blhResMgr";
import { blhStatic } from "../mgr/blhStatic";
import blhBullet from "../tools/blhBullet";
import blhHero from "./blhHero";
import blhShooter1 from "./blhShooter1";


const { ccclass, property } = cc._decorator;
@ccclass
export default class blhBullet1 extends blhBullet {
    @property(cc.Sprite)
    sprite: cc.Sprite = null;

    shooter: blhShooter1 = null;

    rotateSpeed: number = 0;

    /** 命中率 */
    hitRate: number = 1;

    /** 多段伤害 */
    multiHurt: number = 0;
    /** 分裂次数 */
    splitCount: number = 1;

    init(shooter: blhShooter1, tag: number, onInit: () => void = null): blhBullet {
        super.init(shooter, tag, onInit);
        if (this.shooter.bulletConf.pic) {
            this.sprite.spriteFrame = blhResMgr.ins().getImg(this.shooter.bulletConf.pic);
        }
        blhColliMgr.ins().addBulletA(this);
        return this;
    }

    remove(): void {
        this.isRemoved = true;
        blhColliMgr.ins().delBulletA(this.uuid);
        // this.shooter.pool.back(this.node);
        super.remove();
    }

    hitEff(pos: cc.Vec2) {
        if (!this.onHitEffId) {
            return;
        }
        // console.log('子弹击中特效');
        blhStatic.effMgr.createEff(this.onHitEffId, pos.x, pos.y, blhStatic.battle.layer_effect);
    }

    //特殊动作
    spActionMap = {
        //分裂,需要splitNum,splitAngle两者配合
        'splitNum': (pos: cc.Vec2) => {
            if (this.splitCount <= 0) {
                console.log('分裂次数用完');
                return;
            }
            if (this.spConf.splitRate) { //分裂概率
                if (Math.random() > this.spConf.splitRate) {
                    return;
                }
                console.log('概率命中分裂');
            }
            const me = this;
            me.splitCount--;
            const shooter = me.shooter;
            const angle = me.orgAngle;
            const sliceAngle = me.spConf.splitAngle / (me.spConf.splitNum - 1);
            let startSplitAngle = angle - me.spConf.splitAngle / 2;
            const numsPerShoot: number = me.spConf.numsPerShoot || 1;
            console.log('分裂', numsPerShoot, me.spConf.splitNum, sliceAngle);
            for (let n = 0; n < numsPerShoot; n++) {
                for (let i = 0; i < me.spConf.splitNum; i++) {
                    const splitBullet: blhBullet1 = shooter.mkBullet() as blhBullet1;
                    const newAngle = startSplitAngle + sliceAngle * i;
                    splitBullet.setAnglePosScale(me.shooter.bulletAngleStartType === BulletAngleStartType.toEnemy ? newAngle : 0, pos);
                    splitBullet.node.parent = me.node.parent;
                    splitBullet.isLast = i === me.spConf.splitNum - 1;
                    if (me.spConf.bulletConf) {
                        splitBullet.updateConf(me.spConf.bulletConf);
                        if (me.spConf.bulletConf.pic) {
                            splitBullet.sprite.spriteFrame = blhResMgr.ins().getImg(me.spConf.bulletConf.pic);
                        }
                    }
                    if (me.spConf.dmgScale) { //伤害变化
                        splitBullet.dmg = me.shooter.bltDmg * me.spConf.dmgScale;
                    }
                    if (me.spConf.bltScale) { //子弹缩放
                        splitBullet.node.scale = me.spConf.bltScale;
                    }
                    const targetPos = me.shooter.angleDistToTarget(pos, newAngle, me.shooter.shootRange);
                    splitBullet.onShoot(pos, targetPos);
                    shooter.curBulletMap.set(splitBullet.node.uuid, splitBullet);
                    shooter.scheduleOnce(() => {
                        shooter.shootItem(splitBullet, targetPos, null);
                    }, shooter.itemCD * n);
                }
            }
        },
        'deBuffId': (pos: cc.Vec2, enemyNode: cc.Node) => {
            if (!enemyNode || !enemyNode.isValid) {
                return;
            }
            const deBuffId: number = this.spConf.deBuffId;
            if (deBuffId) {
                enemyNode.getComponent(blhEnemy).addDeBuff(deBuffId, this.spConf.deBuffConf);
            }
        },
        //继续保持一段时间
        'persist': (pos: cc.Vec2, enemyNode: cc.Node) => {
            const sec = this.spConf.persist;
            if (sec <= 0) {
                this.shooter.removeItem(this);
                return;
            }
            this.scheduleOnce(() => {
                this.shooter.removeItem(this);
            }, sec);
        }
    };

    /** 对敌人造成伤害 */
    hurtEnemy(other: cc.Collider, self: cc.Collider) {
        if (!this.canAttack || !this.inUse) {
            console.log('不造成伤害');
            return;
        }
        const enemy: blhEnemy = other.getComponent(blhEnemy);
        if (!enemy || !enemy.node || !enemy.node.isValid) {
            return;
        }
        if (this.hitRate < 1 && Math.random() > this.hitRate) {
            console.log('miss', this.hitRate, 'shooter.id:', this.shooter.id);
            return;
        }
        const hero = this.shooter.owner.getComponent(blhHero);
        const dmg = (this.multiHurt) ? this.dmg * this.multiHurt : this.dmg; //多段伤害计算
        enemy.hurt(dmg, hero.crit, hero.critDmg, this.dType, this.dmgType, this.multiHurt);
    }

    onHit(other: cc.Collider, self: cc.Collider): void {
        if (other.tag !== Tag.enemy) {
            return;
        }
        const bulPos: cc.Vec2 = this.node.getPosition();
        this.hitEff(bulPos);
        this.hurtEnemy(other, self); //一定要先造成伤害，再判断穿透，否则会受hurtCD影响
        if (!this.canHitThrough()) {
            //一定要从shooter移除
            this.shooter.removeItem(this);
        }
        //碰撞后的特殊动作
        if (this.spConf) {
            for (const key in this.spConf) {
                const act = this.spActionMap[key];
                if (act) {
                    act.call(this, bulPos, other.node);
                }
            }
        }
    }


    protected update(dt: number): void {
        if (blhStatic.battleState !== BattleState.run) {
            return;
        }
        if (this.rotateSpeed) {
            this.node.angle += this.rotateSpeed;
        }
        if (this.node.x > blhStatic.halfViewWidth + 50 || this.node.y > blhStatic.halfViewHeight + 50 || this.node.x < -blhStatic.halfViewWidth - 50 || this.node.y < -blhStatic.halfViewHeight - 50) {
            this.shooter.removeItem(this);
        }
    }
}