/**
 * 到边缘可反弹子弹
 * 
 * TODO: 定时造成伤害(每x秒造成伤害)
 * 
 */

import { blhStatic } from "../mgr/blhStatic";
import blhBullet1 from "./blhBullet1";


const { ccclass, property } = cc._decorator;
@ccclass
export default class blhBullet2 extends blhBullet1 {


    // protected angleShift: number = 500;
    private dir: cc.Vec2 = null;

    onShoot(startPo: cc.Vec2, targetPo: cc.Vec2): void {
        this.dir = targetPo.sub(startPo).normalize();
    }


    protected update(dt: number): void {

        if (!this.inUse || this.dir === null) {
            return;
        }
 
        const oldPos: cc.Vec2 = this.node.getPosition();
        // this.node.position = cc.v3(this.startPos.lerp(this.targetPos, this.dTime / this.endTime));
        const newPo = oldPos.add(this.dir.mul(this.shooter.bltSpd * dt * 100));
        this.node.setPosition(newPo);
        // this.node.angle += this.angleShift * dt;


        //判断是否到达屏幕边缘
        if (newPo.x >= blhStatic.halfViewWidth && this.dir.x > 0) {
            this.dir.x = -this.dir.x;
        } else if (newPo.x < -blhStatic.halfViewWidth && this.dir.x < 0) {
            this.dir.x = -this.dir.x;
        }
        if (newPo.y >= blhStatic.halfViewHeight && this.dir.y > 0) {
            this.dir.y = -this.dir.y;
        } else if (newPo.y < -blhStatic.halfViewHeight && this.dir.y < 0) {
            this.dir.y = -this.dir.y;
        }

    }

}