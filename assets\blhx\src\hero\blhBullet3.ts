/**
 * 带序列帧子弹,持续伤害,(龙卷)
 * 
 * 
 */

import blhResMgr from "../mgr/blhResMgr";
import blhFrameAnim from "../utils/blhFrameAnim";
import blhkc from "../utils/blhkc";
import blhBullet1 from "./blhBullet1";


const { ccclass, property } = cc._decorator;
@ccclass
export default class blhBullet3 extends blhBullet1 {


    @property(blhFrameAnim)
    frameAnim: blhFrameAnim = null;

    /** 序列帧前缀 */
    framePre: string = null;
    /** 序列帧数量 */
    frameCount: number = 0;
    /** 序列帧时长 */
    frameDuration: number = 0.1;


    onShoot(startPo: cc.Vec2, targetPo: cc.Vec2): void {
        if (!this.framePre || this.frameCount <= 0) {
            return;
        }
        this.orgAngle = blhkc.posToAngle(startPo.x, startPo.y, targetPo.x, targetPo.y);
        const frameArr = [];
        for (let i = 1; i <= this.frameCount; i++) {
            frameArr.push(blhResMgr.ins().getImg(this.framePre + i));
        }
        this.frameAnim.setLoop(true).setDuration(this.frameDuration).setFrames(frameArr).play();
    }

    /** 持续伤害 */
    onCollisionStay(other: cc.Collider, self: cc.Collider) {
        this.onCollisionEnter(other, self);
    }

}