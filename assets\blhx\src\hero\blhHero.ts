/**
 * 英雄基类
 * 
 * 
 * 
 * 
 */

import blhHeroData from "../entity/blhHeroData";
import blhSkillData from "../entity/blhSkillData";
import { BuffOwnerType, BulletAngleStartType, Prefabs, SpineAni } from "../mgr/blhConst";
import blhResMgr from "../mgr/blhResMgr";
import blhSkillMgr from "../mgr/blhSkillMgr";
import { blhStatic } from "../mgr/blhStatic";
import blhBuff from "../tools/blhBuff";
import blhBuffContainer from "../tools/blhBuffContainer";
import crtBuffContainer, { blhBuffOwner } from "../tools/blhBuffContainer";
import blhShooter from "../tools/blhShooter";
import blhShooter1 from "./blhShooter1";

const { ccclass, property } = cc._decorator;
@ccclass
export default class blhHero extends cc.Component implements blhBuffOwner {

    @property(sp.Skeleton)
    spine: sp.Skeleton = null;

    @property(cc.Label)
    t_lv: cc.Label = null;

    // /** 计算好各个buff后的伤害值 */
    // dmg: number = 0;
    //-----------buff-----------
    /** 当前攻击力,注意与data中的atk有区别 */
    atk: number = 0;
    /** 局内英雄的当前技能等级 */
    skillLv: number = 1;
    /** 星级 */
    star: number = 1;
    /** 阶级 */
    grade: number = 1;

    /** 攻击scale */
    atkScale: number = 1;
    /** 针对boss的攻击加加成 */
    atk2Boss: number = 1;

    /** 暴击率 */
    crit: number = 0;
    /** 暴击伤害,默认2 */
    critDmg: number = 2;

    /** 开火位置偏移X */
    fireX: number = 0;
    /** 开火位置偏移Y */
    fireY: number = 100;

    data: blhHeroData = null;

    shooter1: blhShooter = null;
    /** 用于必杀 */
    shooter2: blhShooter = null;


    /** 这里由buffMgr注入 */
    buffContainer: blhBuffContainer = null;
    /** 当前拥有的技能 */
    skillIdArr: Array<number> = [];
    /** 技能属性 */
    skillProps: Map<number, any> = new Map(
        [
            [1000, { //骨头攻击
                shooter1: {
                    id: 1,//普通shooter,直线发射
                    bulletId: 1, //图片子弹
                    bltSpd: 10, //子弹速度
                    bulletAngleStartType: BulletAngleStartType.toEnemy, //子弹角度类型
                    bulletConf: { //子弹配置
                        onHitEffId: 0,
                        pic: 'bullet1', //子弹图片 
                    },
                },

            }],
            [1001, { //扩散骨头,骨头首次命中后，分裂成3个次级骨头，次级骨头造成攻击*50%的物理伤害。
                shooter1: {
                    bulletSpConf: { //子弹特殊配置
                        splitNum: 3, //分裂个数
                        splitAngle: 50, //分裂总角度
                        dmgScale: 0.5,//分裂子弹伤害scale
                        bulletConf: {
                            // maxHitCount: -1, //穿透次数,-1为无限
                            pic: 'bullet1a', //子弹图片 
                            spConf: {},//分裂后不再分裂
                        },
                    }
                },
            }],
            [1010, { //骨头伤害+60%
                shooter1: {
                    _add: { //属性增加 
                        bltDmgScale: 0.6, //伤害scale增加60%
                    }
                },
            }],
            [1011, { //骨头齐发,骨头齐发+1，伤害-20%
                shooter1: {
                    id: 3,
                    _add: { //属性增加 
                        bltDmgScale: -0.2, //伤害scale减少20%
                        bulletNum: 1, //子弹数量+1
                    },
                },
            }],
            [1012, { //骨头连发,骨头连发+1，伤害-20%
                shooter1: {
                    _add: { //属性增加 
                        numsPerShoot: 1, //子弹数量+1
                        bltDmgScale: -0.2, //伤害scale减少20%
                    },
                },
            }],
            [1013, { //次级骨头强化,次级骨头伤害+50%
                shooter1: {
                    _add: { //属性增加 
                        bulletSpConf: {
                            bulletConf: {
                                bltDmgScale: 0.5,
                            },
                        }
                    },
                },
            }],
            [1014, { //散花骨头,骨头首次命中分裂为4个次级骨头。
                shooter1: {
                    bulletSpConf: { //子弹特殊配置
                        splitNum: 4, //分裂个数
                        splitAngle: 50, //分裂总角度
                        dmgScale: 0.5,//分裂子弹伤害scale
                        bulletConf: {
                            maxHitCount: -1, //穿透次数,-1为无限
                            pic: 'bullet1a', //子弹图片 
                            spConf: {},//分裂后不再分裂
                        },
                    }
                },
            }],

            [2000, {
                shooter1: {
                    id: 2, //回旋
                    bulletId: 1,
                    bltSpd: 3,
                    itemCD: 0.5,
                    // cd: 3,
                    shootRange: 900,
                    // numsPerShoot: 3,
                    bulletAngleStartType: BulletAngleStartType.toEnemy,
                    bulletConf: {
                        onHitEffId: 0,
                        pic: 'bullet2',
                        maxHitCount: -1,
                        rotateSpeed: 10,
                    },
                },
            }],

            [2001, {
                shooter1: {
                    bulletSpConf: { //子弹特殊配置
                        splitNum: 10, //分裂个数
                        splitAngle: 360, //分裂总角度
                        // dmgScale: 0.5,//分裂子弹伤害scale
                        bulletConf: {
                            // bltSpd: 1,
                            maxHitCount: -1, //穿透次数,-1为无限
                            pic: 'bullet2', //子弹图片 
                            rotateSpeed: 10,
                            spConf: {},//分裂后不再分裂
                        },
                    }
                },
            }],
            [2010, {
                shooter1: {
                    bulletSpConf: { //子弹特殊配置
                        splitNum: 10, //分裂个数
                        splitAngle: 360, //分裂总角度
                        // dmgScale: 0.5,//分裂子弹伤害scale
                        bulletConf: {
                            // bltSpd: 1,
                            maxHitCount: -1, //穿透次数,-1为无限
                            pic: 'bullet2', //子弹图片 
                            rotateSpeed: 10,
                            spConf: {},//分裂后不再分裂
                        },
                    }
                },
            }],
            [2011, {
                shooter1: {
                    bulletSpConf: { //子弹特殊配置
                        splitNum: 10, //分裂个数
                        splitAngle: 360, //分裂总角度
                        // dmgScale: 0.5,//分裂子弹伤害scale
                        bulletConf: {
                            // bltSpd: 1,
                            maxHitCount: -1, //穿透次数,-1为无限
                            pic: 'bullet2', //子弹图片 
                            rotateSpeed: 10,
                            spConf: {},//分裂后不再分裂
                        },
                    }
                },
            }],

            [3000, {
                shooter1: {
                    id: 4,
                    bulletId: 2,
                    bltSpd: 10,
                    shootRange: 2600,
                    // numsPerShoot:3,
                    bulletAngleStartType: BulletAngleStartType.toEnemy,
                    bulletConf: {
                        onHitEffId: 0,
                        pic: 'bullet1a',
                        maxHitCount: -1, //穿透次数,-1为无限
                    },
                },
            }],
            [4000, {
                shooter1: {
                    id: 2, //回旋
                    bulletId: 1,
                    bltSpd: 3,
                    itemCD: 0.5,
                    cd: 3,
                    shootRange: 1200,
                    numsPerShoot: 1,
                    bulletAngleStartType: BulletAngleStartType.toEnemy,
                    bulletConf: {
                        onHitEffId: 0,
                        pic: 'bullet2',
                        maxHitCount: -1,
                        rotateSpeed: 10,
                    },
                },
            }],
            [5000, {
                shooter1: {
                    id: 2, //回旋
                    bulletId: 1,
                    bltSpd: 3,
                    itemCD: 0.5,
                    cd: 3,
                    shootRange: 1200,
                    numsPerShoot: 1,
                    bulletAngleStartType: BulletAngleStartType.toEnemy,
                    bulletConf: {
                        onHitEffId: 0,
                        pic: 'bullet2',
                        maxHitCount: -1,
                        rotateSpeed: 10,
                    },
                },
            }],
            [6000, {
                shooter1: {
                    id: 2, //回旋
                    bulletId: 1,
                    bltSpd: 3,
                    itemCD: 0.5,
                    cd: 3,
                    shootRange: 1200,
                    numsPerShoot: 1,
                    bulletAngleStartType: BulletAngleStartType.toEnemy,
                    bulletConf: {
                        onHitEffId: 0,
                        pic: 'bullet2',
                        maxHitCount: -1,
                        rotateSpeed: 10,
                    },
                },
            }],
            [7000, {
                shooter1: {
                    id: 2, //回旋
                    bulletId: 1,
                    bltSpd: 3,
                    itemCD: 0.5,
                    cd: 3,
                    shootRange: 1200,
                    numsPerShoot: 1,
                    bulletAngleStartType: BulletAngleStartType.toEnemy,
                    bulletConf: {
                        onHitEffId: 0,
                        pic: 'bullet2',
                        maxHitCount: -1,
                        rotateSpeed: 10,
                    },
                },
            }],
        ]
    );

    //--------- blhBuffOwner实现方法 ------------
    getBaseVal(prop: string): number {
        return (this.data[prop] !== undefined) ? this.data[prop] : this[prop];
    }
    onChange(buff: blhBuff, changeType: number): void {
        // this.resetProps();
        console.log('hero的buff变化', this.data.id, buff.id);
    }
    getContainer(): crtBuffContainer {
        return this.buffContainer;
    }
    getOwnerId(): number {
        return this.data.id;
    }
    getType(): number {
        return BuffOwnerType.hero;
    }
    getComp(): cc.Component {
        return this;
    }
    //--------- blhBuffOwner实现方法结束 ------------


    /** 设置技能，同时更新配套的参数和shooter,buff等 */
    setSkill(skillId: number) {
        const skObj = this.skillProps.get(skillId);
        if (!skObj) {
            console.error('hero技能不存在', skillId, this.data.id);
            return;
        }
        for (let key in skObj) {
            const conf = skObj[key];
            if (key.startsWith('shooter')) {
                //切换或增加shooter
                if (!this[key] || (conf.id && this[key].id !== conf.id)) {
                    //新shooter
                    const oldShooter: blhShooter1 = this[key];
                    const newShooter: blhShooter1 = this.addComponent('blhShooter' + conf.id);
                    this[key] = newShooter;
                    //继承必要属性后删除老shooter
                    if (oldShooter) {
                        newShooter.bltDmg = oldShooter.bltDmg;
                        newShooter.bltDmgScale = oldShooter.bltDmgScale;
                        newShooter.bltSpd = oldShooter.bltSpd;
                        newShooter.bltScale = oldShooter.bltScale;
                        newShooter.bulletId = oldShooter.bulletId;
                        newShooter.targetType = oldShooter.targetType;
                        newShooter.bulletAngleStartType = oldShooter.bulletAngleStartType;
                        newShooter.ownerPosInBullet = oldShooter.ownerPosInBullet;
                        newShooter.shootRange = oldShooter.shootRange;
                        newShooter.numsPerShoot = oldShooter.numsPerShoot;
                        newShooter.itemCD = oldShooter.itemCD;
                        newShooter.cd = oldShooter.cd;
                        newShooter.bulletConf = oldShooter.bulletConf;
                        newShooter.bulletSpConf = oldShooter.bulletSpConf;
                        oldShooter.destroy();//更换时去除旧shooter
                    } else {
                        //初始shooter
                        conf.fireX = this.fireX;
                        conf.fireY = this.fireY;
                        conf.bltDmg = this.atk;
                    }
                    newShooter.setOwner(this.node).initConf(conf).shoot();
                } else {
                    //更新shooter
                    this[key].initConf(conf).shoot();
                }
                continue;
            } else if (key === 'buff') {
                //TODO: 添加buff
                continue;
            }
            this[key] = conf;
        }
        this.skillIdArr.push(skillId);
    }

    /** 吃技能升级 */
    upgradeC3(skill: blhSkillData) {

        this.skillLv++;
        this.skillIdArr.push(skill.id);
        this.t_lv.string = '' + this.skillLv;
        //TODO: 技能升级生效
        console.log('英雄技能升级:', this.data.name, '技能', skill.id, skill.name, '当前等级', this.skillLv);
        this.setSkill(skill.id);

    }

    init(data: blhHeroData, callback: (err: Error) => void): blhHero {
        this.data = data;
        blhResMgr.ins().getSpineAsync('async', this.data.spine, (err, spine) => {
            if (err) {
                console.error('英雄init失败', this.data.id, err);
                callback(err);
                return;
            }
            this.spine.skeletonData = spine;
            callback(null);
        });
        return this;
    }

    /** 重置属性 */
    resetProps() {
        this.crit = this.data.crit;
        this.critDmg = 1.5;
        this.atk = this.data.atk;
        this.atkScale = 1;
        this.atk2Boss = 1;
        this.skillLv = 1;

        this.t_lv.string = this.skillLv.toString();
    }

    /** 战斗开始 */
    battleStart() {
        this.resetProps();
        this.setSkill(this.data.skillArr[0]); //初始技能
        this.spine.setAnimation(0, SpineAni.attack, true);
    }

    pause() {
        if (this.shooter1) {
            this.shooter1.pause();
        }
        if (this.shooter2) {
            this.shooter2.pause();
        }
        if (this.spine) {
            this.spine.paused = true;
        }
    }

    resume() {
        if (this.shooter1) {
            this.shooter1.resume();
        }
        if (this.shooter2) {
            this.shooter2.resume();
        }
        if (this.spine) {
            this.spine.paused = false;
        }
    }


    static create(id: number, parent: cc.Node, x: number, y: number, callback: (err: any, hero?: blhHero) => void = () => { }) {
        const data = blhStatic.heroMgr.getData(id) as blhHeroData;
        if (!data) {
            return callback(new Error('hero id不存在'));
        }
        blhResMgr.ins().getNodeAsync(Prefabs.hero, (err, node) => {
            if (err) {
                console.error('创建英雄失败', data.id, err);
                callback(err);
                return;
            }
            const hero = node.getComponent(blhHero); //后期改为绑定hero1,hero2...
            hero.init(data, (err) => {
                if (err) {
                    return;
                }
                node.parent = parent;
                node.setPosition(x, y);
                callback(null, hero);
            });
        });
    }
}