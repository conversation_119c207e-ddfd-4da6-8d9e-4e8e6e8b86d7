/**
 * 英雄基类
 * 
 * 
 * 
 * 
 */

import blhHeroData from "../entity/blhHeroData";
import blhSkillData from "../entity/blhSkillData";
import { BuffOwnerType, BulletAngleStartType, Prefabs, SpineAni } from "../mgr/blhConst";
import blhResMgr from "../mgr/blhResMgr";
import blhSkillMgr from "../mgr/blhSkillMgr";
import { blhStatic } from "../mgr/blhStatic";
import blhBuff from "../tools/blhBuff";
import blhBuffContainer from "../tools/blhBuffContainer";
import crtBuffContainer, { blhBuffOwner } from "../tools/blhBuffContainer";
import blhShooter from "../tools/blhShooter";
import blhShooter1 from "./blhShooter1";

const { ccclass, property } = cc._decorator;
@ccclass
export default class blhHero extends cc.Component implements blhBuffOwner {

    @property(sp.Skeleton)
    spine: sp.Skeleton = null;

    @property(cc.Label)
    t_lv: cc.Label = null;

    // /** 计算好各个buff后的伤害值 */
    // dmg: number = 0;
    //-----------buff-----------
    /** 当前攻击力,注意与data中的atk有区别 */
    atk: number = 0;
    /** 局内英雄的当前技能等级 */
    skillLv: number = 1;
    /** 星级 */
    star: number = 1;
    /** 阶级 */
    grade: number = 1;

    /** 攻击scale */
    atkScale: number = 1;
    /** 针对boss的攻击加加成 */
    atk2Boss: number = 1;

    /** 暴击率 */
    crit: number = 0;
    /** 暴击伤害,默认2 */
    critDmg: number = 2;

    /** 开火位置偏移X */
    fireX: number = 0;
    /** 开火位置偏移Y */
    fireY: number = 100;

    data: blhHeroData = null;

    shooter1: blhShooter = null;
    /** 用于必杀 */
    shooter2: blhShooter = null;


    /** 这里由buffMgr注入 */
    buffContainer: blhBuffContainer = null;
    /** 当前拥有的技能 */
    skillIdArr: Array<number> = [];
    /** 技能属性 */
    skillProps: Map<number, any> = new Map(
        [
            [1000, { //骨头攻击
                shooter1: {
                    id: 1,//普通shooter,直线发射
                    bulletId: 1, //图片子弹
                    bltSpd: 10, //子弹速度
                    bulletAngleStartType: BulletAngleStartType.toEnemy, //子弹角度类型
                    bulletConf: { //子弹配置
                        onHitEffId: 0,
                        pic: 'bullet1', //子弹图片 
                    },
                },

            }],
            [1001, { //扩散骨头,骨头首次命中后，分裂成3个次级骨头，次级骨头造成攻击*50%的物理伤害。
                shooter1: {
                    bulletSpConf: { //子弹特殊配置
                        splitNum: 3, //分裂个数
                        splitAngle: 50, //分裂总角度
                        dmgScale: 0.5,//分裂子弹伤害scale
                        bulletConf: {
                            // maxHitCount: -1, //穿透次数,-1为无限
                            pic: 'bullet1a', //子弹图片 
                            spConf: {},//分裂后不再分裂
                            bulletWidth: 50,
                            bulletHeight: 50,
                        },
                    }
                },
            }],
            [1010, { //骨头伤害+60%
                shooter1: {
                    _add: { //属性增加 
                        bltDmgScale: 0.6, //伤害scale增加60%
                    }
                },
            }],
            [1011, { //骨头齐发,骨头齐发+1，伤害-20%
                shooter1: {
                    id: 3,
                    _add: { //属性增加 
                        bltDmgScale: -0.2, //伤害scale减少20%
                        bulletNum: 1, //子弹数量+1
                    },
                },
            }],
            [1012, { //骨头连发,骨头连发+1，伤害-20%
                shooter1: {
                    _add: { //属性增加 
                        numsPerShoot: 1, //子弹数量+1
                        bltDmgScale: -0.2, //伤害scale减少20%
                    },
                },
            }],
            [1013, { //次级骨头强化,次级骨头伤害+50%
                shooter1: {
                    _add: { //属性增加 
                        bulletSpConf: {
                            bulletConf: {
                                bltDmgScale: 0.5,
                            },
                        }
                    },
                },
            }],
            [1014, { //散花骨头,骨头首次命中分裂为4个次级骨头。
                shooter1: {
                    bulletSpConf: { //子弹特殊配置
                        splitNum: 4, //分裂个数
                        splitAngle: 50, //分裂总角度
                        dmgScale: 0.5,//分裂子弹伤害scale
                        bulletConf: {
                            // maxHitCount: -1, //穿透次数,-1为无限
                            pic: 'bullet1a', //子弹图片 
                            spConf: {},//分裂后不再分裂
                        },
                    }
                },
            }],

            [2000, {
                shooter1: {
                    id: 2, //回旋
                    bulletId: 1,
                    bltSpd: 3,
                    itemCD: 0.5,
                    // cd: 3,
                    bltDmgScale: 0.3,
                    shootRange: 800,
                    // numsPerShoot: 2,
                    bulletAngleStartType: BulletAngleStartType.toEnemy,
                    bulletConf: {
                        onHitEffId: 0,
                        pic: 'bullet2',
                        maxHitCount: -1,
                        rotateSpeed: 10,
                    },
                },
            }],

            [2001, {
                shooter1: {
                    bulletSpConf: { //子弹特殊配置
                        // splitRate:0.5, //分裂概率
                        splitNum: 10, //分裂个数
                        splitAngle: 360, //分裂总角度
                        // dmgScale: 0.5,//分裂子弹伤害scale
                        bulletConf: {
                            // bltSpd: 1,
                            // maxHitCount: -1, //穿透次数,-1为无限
                            pic: 'bullet2', //子弹图片 
                            rotateSpeed: 10,
                            spConf: {},//分裂后不再分裂
                        },
                    }
                },
            }],
            [2010, {
                shooter1: {
                    numsPerShootRate: 0.5, //50%概率发出双香蕉回旋。
                },
            }],
            [2011, {
                shooter1: {
                    _add: { //香蕉回旋伤害+30%，命中率-10%
                        bltDmgScale: 0.1,
                        bulletConf: {
                            hitRate: -0.1,
                        }
                    }
                },
            }],

            [2012, {
                shooter1: {
                    bulletNumRate: 0.5, //50%概率连发香蕉回旋
                },
            }],

            [2013, {
                shooter1: { //香蕉回旋命中的单位「流血」，每秒损失3%最大生命，持续5秒。（每秒最大不可超过施加者攻击*100%的伤害）
                    _add: {
                        bulletSpConf: {
                            deBuffId: 1,
                        }
                    },
                },
            }],

            [2014, {
                shooter1: { //小香蕉回旋数量+5，命中率-10%
                    _add: {
                        bulletSpConf: {
                            splitNum: 5,
                        },
                        bulletConf: {
                            hitRate: -0.1,
                        }
                    }
                },
            }],

            [2015, {
                shooter1: { //小香蕉回旋连发+1，命中率-10%
                    _add: {
                        bulletSpConf: {
                            numsPerShoot: 1,
                        },
                        bulletConf: {
                            splitCount: 1,
                            hitRate: -0.1,
                        }
                    }
                },
            }],

            [3000, { //操纵水波进行攻击，造成攻击*50%的法术伤害
                shooter1: {
                    id: 1,//普通shooter,直线发射
                    bulletId: 1, //图片子弹
                    bltSpd: 10, //子弹速度
                    bltDmgScale: 0.5,
                    bulletAngleStartType: BulletAngleStartType.toEnemy, //子弹角度类型
                    bulletConf: { //子弹配置
                        onHitEffId: 0,
                        pic: 'bullet1', //子弹图片 
                    },
                },
            }],

            [3001, { //水波命中后分裂出3个小水波，造成攻击*100%的法术伤害
                shooter1: {
                    bulletSpConf: { //子弹特殊配置
                        splitNum: 3, //分裂个数
                        splitAngle: 50, //分裂总角度
                        dmgScale: 2,//分裂子弹伤害scale
                        bulletConf: {
                            // maxHitCount: -1, //穿透次数,-1为无限
                            pic: 'bullet1a', //子弹图片 
                            spConf: {},//分裂后不再分裂
                        },
                    }
                },
            }],

            [3010, { //水波术伤害+60%
                shooter1: {
                    _add: {
                        bltDmgScale: 0.6,
                    },
                },
            }],

            [3011, { //水波穿透+1，冷却+35%
                shooter1: {
                    _add: {
                        maxHitCount: 1,
                        cd: 0.35,
                    },
                },
            }],

            [3012, { //水波连发+1，冷却+35%
                shooter1: {
                    _add: {
                        numsPerShoot: 1,
                        cd: 0.35,
                    },
                },
            }],

            [3013, { //小水波穿透+1
                shooter1: {
                    _add: {
                        bulletSpConf: {
                            bulletConf: {
                                maxHitCount: 1,
                            },
                        },
                    },
                },
            }],

            [3014, { //水波使怪物获得一层「迟缓」，每层减速20%，叠满三层「冰冻」怪物3秒，「迟缓」持续20秒
                shooter1: {
                    bulletSpConf: {
                        deBuffId: 2,
                    }
                },
            }],

            [4000, {
                shooter1: {
                    id: 4,
                    bulletId: 2,
                    bltSpd: 10,
                    shootRange: 2600,
                    // numsPerShoot:3,
                    bulletAngleStartType: BulletAngleStartType.toEnemy,
                    bulletConf: {
                        onHitEffId: 0,
                        pic: 'bullet1a',
                        // maxHitCount: -1,
                    },
                },
            }],
            [4001, {
                shooter1: { //火焰篮球解除边缘将消耗1次贯穿并折回场内。
                    _add: {
                        bulletConf: {
                            maxHitCount: 1,
                        },
                    }
                },
            }],
            [4010, {
                shooter1: { //火焰篮球连击+1
                    _add: {
                        numsPerShoot: 1,
                    }
                },
            }],
            [4011, {
                shooter1: { //火焰篮球贯穿+3，伤害-10%
                    _add: {
                        bltDmgScale: -0.1,
                        bulletConf: {
                            maxHitCount: 3,
                        },
                    }
                },
            }],
            [4012, {
                shooter1: { //火焰篮球有10%概率使怪物燃烧。 TODO: 概率未实现,  暂为100%出
                    bulletSpConf: {
                        deBuffId: 1,
                    }
                },
            }],
            [4013, {
                shooter1: { //火焰篮球连击次数+2
                    _add: {
                        numsPerShoot: 2,
                    }
                },
            }],
            [5000, {
                shooter1: {
                    id: 1,
                    bulletId: 3,
                    bltSpd: 1,
                    // itemCD: 0.5,
                    cd: 7,
                    shootRange: 1200,
                    // numsPerShoot: 1,
                    bulletAngleStartType: BulletAngleStartType.zero,
                    bulletConf: {
                        framePre: '5fx',
                        frameCount: 6,
                        frameDuration: 0.1,
                        maxHitCount: -1,
                        bulletWidth: 150,
                        bulletHeight: 150,
                        // hurtCD: 0.5, //每0.5秒伤害一次
                    },
                },
            }],
            [6000, {
                shooter1: {
                    id: 2, //回旋
                    bulletId: 1,
                    bltSpd: 3,
                    itemCD: 0.5,
                    cd: 3,
                    shootRange: 1200,
                    numsPerShoot: 1,
                    bulletAngleStartType: BulletAngleStartType.toEnemy,
                    bulletConf: {
                        onHitEffId: 0,
                        pic: 'bullet2',
                        maxHitCount: -1,
                        rotateSpeed: 10,
                    },
                },
            }],
            [7000, { //释放飓风对目标造成五段攻击*12%的法术伤害
                shooter1: {
                    id: 1,
                    bulletId: 3,
                    bltSpd: 5,
                    // itemCD: 0.5,
                    cd: 2,
                    shootRange: 1200,
                    // numsPerShoot: 1,
                    bltDmgScale: 0.12,
                    bulletAngleStartType: BulletAngleStartType.zero,
                    bulletConf: {
                        framePre: '5fx',
                        frameCount: 6,
                        frameDuration: 0.1,
                        // maxHitCount: -1,
                        bulletWidth: 100,
                        bulletHeight: 120,
                        hurtCD: 0.3, //每0.5秒伤害一次
                        multiHurt: 5, //多段伤害
                    },
                },
            }],
            [7001, { //飓风在首个命中目标后，生成2个子飓风，子飓风穿透3个目标并造成攻击*30%法术伤害
                shooter1: {
                    bulletSpConf: { //子弹特殊配置
                        splitNum: 2, //分裂个数
                        splitAngle: 30, //分裂总角度
                        dmgScale: 0.3,//分裂子弹伤害scale
                        bltScale: 0.5,//分裂子弹缩放
                        bulletConf: {
                            maxHitCount: 3, //穿透次数,-1为无限
                            spConf: {},//分裂后不再分裂
                            bulletWidth: 70,
                            bulletHeight: 70,
                            multiHurt: 0, //多段伤害重置为0
                        },
                    }
                },
            }],
            [7002, { //乱流[必杀]持续操控巨大龙卷对范围内怪物造成每秒3次攻击*40%的法术伤害，有25%概率牵引目标，施法持续10秒
                shooter2: {
                    id: 1,
                    bulletId: 3, //TODO: 这里需要新的子弹，可以自动索敌，改变方向，定时消失
                    bltSpd: 5,
                    // itemCD: 0.5,
                    cd: 2,
                    shootRange: 1200,
                    bltScale: 3,
                    // numsPerShoot: 1,
                    bltDmgScale: 0.4,
                    bulletAngleStartType: BulletAngleStartType.zero,
                    bulletConf: {
                        framePre: '5fx',
                        frameCount: 6,
                        frameDuration: 0.1,
                        // maxHitCount: -1,
                        bulletWidth: 150,
                        bulletHeight: 150,
                        hurtCD: 0.5, //每0.5秒伤害一次
                        // multiHurt: 5, //多段伤害
                    },
                },
            }],
            [7010, { //飓风命中后可继续滑行2秒，暴击率+5%
                hero: {
                    _add: {
                        crit: 0.05,
                    },
                },
                shooter1: {
                    bulletConf: {
                        maxHitCount: -1,
                    },
                    bulletSpConf: {
                        persist: 2,
                    }
                }
            }],
            [7011, { //飓风连发+1，暴击率+5%
                hero: {
                    _add: {
                        crit: 0.05,
                    },
                },
                shooter1: {
                    _add: {
                        numsPerShoot: 1,
                    }
                }
            }],
            [7012, { //飓风连发+2，暴击率+10%
                hero: {
                    _add: {
                        crit: 0.1,
                    },
                },
                shooter1: {
                    _add: {
                        numsPerShoot: 2,
                    }
                }
            }],
            [7013, { //飓风牵引目标概率+25%，暴击率+5%
                hero: {
                    _add: {
                        crit: 0.05,
                    },
                },
                shooter1: {
                    _add: {
                        bulletSpConf: {
                            trackTarget: 0.25, //TODO: 未实现
                        }
                    }
                }
            }],
            [7014, { //【飓风】命中的敌方减速20%，持续3秒
                shooter1: {
                    bulletSpConf: {
                        deBuffId: 2,
                        debuffConf: {
                            duration: 3,
                        }
                    }
                }
            }],
            [7015, { //【飓风】命中的敌方减速50%，持续5秒
                shooter1: {
                    bulletSpConf: {
                        deBuffId: 2,
                        debuffConf: {
                            duration: 5,
                            slow: 0.5,
                        }
                    }
                }
            }],
            [7016, { //【真空】子飓风命中敌方时，额外制造攻击*40%的范围伤害   TODO:实现不准确
                shooter1: {
                    _add:{
                        bulletSpConf: {
                            dmgScale: 0.4,
                        },
                    }
                }
            }],
            [7017, { //【真空】范围+50%
                shooter1: {
                    _add:{
                        bulletSpConf: {
                            // dmgScale: 0.4,
                        },
                    }
                }
            }],
            [7018, { //子飓风制造的真空中的敌方，被牵引概率+25%
                shooter1: {
                    _add:{
                        bulletSpConf: {
                            // dmgScale: 0.4,
                        },
                    }
                }
            }],
            [7019, { //乱流中的子飓风和真空造成伤害时，暴击率+30%
                shooter1: {
                    _add:{
                        bulletSpConf: {
                            // dmgScale: 0.4,
                        },
                    }
                }
            }],

        ]
    );

    //--------- blhBuffOwner实现方法 ------------
    getBaseVal(prop: string): number {
        return (this.data[prop] !== undefined) ? this.data[prop] : this[prop];
    }
    onChange(buff: blhBuff, changeType: number): void {
        // this.resetProps();
        console.log('hero的buff变化', this.data.id, buff.id);
    }
    getContainer(): crtBuffContainer {
        return this.buffContainer;
    }
    getOwnerId(): number {
        return this.data.id;
    }
    getType(): number {
        return BuffOwnerType.hero;
    }
    getComp(): cc.Component {
        return this;
    }
    //--------- blhBuffOwner实现方法结束 ------------


    /** 设置技能，同时更新配套的参数和shooter,buff等 */
    setSkill(skillId: number) {
        const skObj = this.skillProps.get(skillId);
        if (!skObj) {
            console.error('hero技能不存在', skillId, this.data.id);
            return;
        }
        for (let key in skObj) {
            const conf = skObj[key];
            if (key.startsWith('shooter')) {
                //切换或增加shooter
                if (!this[key] || (conf.id && this[key].id !== conf.id)) {
                    //新shooter
                    const oldShooter: blhShooter1 = this[key];
                    const newShooter: blhShooter1 = this.addComponent('blhShooter' + conf.id);
                    this[key] = newShooter;
                    //继承必要属性后删除老shooter
                    if (oldShooter) {
                        newShooter.bltDmg = oldShooter.bltDmg;
                        newShooter.bltDmgScale = oldShooter.bltDmgScale;
                        newShooter.bltSpd = oldShooter.bltSpd;
                        newShooter.bltScale = oldShooter.bltScale;
                        newShooter.bulletId = oldShooter.bulletId;
                        newShooter.targetType = oldShooter.targetType;
                        newShooter.bulletAngleStartType = oldShooter.bulletAngleStartType;
                        newShooter.ownerPosInBullet = oldShooter.ownerPosInBullet;
                        newShooter.shootRange = oldShooter.shootRange;
                        newShooter.numsPerShoot = oldShooter.numsPerShoot;
                        newShooter.itemCD = oldShooter.itemCD;
                        newShooter.cd = oldShooter.cd;
                        newShooter.bulletConf = oldShooter.bulletConf;
                        newShooter.bulletSpConf = oldShooter.bulletSpConf;
                        oldShooter.destroy();//更换时去除旧shooter
                    } else {
                        //初始shooter
                        conf.fireX = this.fireX;
                        conf.fireY = this.fireY;
                        conf.bltDmg = this.atk;
                    }
                    newShooter.setOwner(this.node).initConf(conf).shoot();
                } else {
                    //更新shooter
                    this[key].initConf(conf).shoot();
                }
                continue;
            } else if (key === 'hero') {
                for (const i in conf) {
                    if (i === '_add') {
                        for (const j in conf[i]) {
                            if (undefined !== this[j]) {
                                this[j] = (this[j] || 0) + conf[i][j];
                            }
                        }
                        continue;
                    }
                    if (undefined !== this[i]) {
                        this[i] = conf[i];
                    }
                }
                continue;
            } else if (key === 'buff') {
                //TODO: 添加buff
                continue;
            }
        }
        this.skillIdArr.push(skillId);
    }

    /** 吃技能升级 */
    upgradeC3(skill: blhSkillData) {

        this.skillLv++;
        this.skillIdArr.push(skill.id);
        this.t_lv.string = '' + this.skillLv;
        console.log('英雄技能升级:', this.data.name, '技能', skill.id, skill.name, '当前等级', this.skillLv);
        this.setSkill(skill.id);

    }

    init(data: blhHeroData, callback: (err: Error) => void): blhHero {
        this.data = data;
        blhResMgr.ins().getSpineAsync('async', this.data.spine, (err, spine) => {
            if (err) {
                console.error('英雄init失败', this.data.id, err);
                callback(err);
                return;
            }
            this.spine.skeletonData = spine;
            if (data.skin) {
                this.spine.setSkin(data.skin);
            }
            callback(null);
        });
        return this;
    }

    /** 重置属性 */
    resetProps() {
        this.crit = this.data.crit;
        this.critDmg = 1.5;
        this.atk = this.data.atk;
        this.atkScale = 1;
        this.atk2Boss = 1;
        this.skillLv = 1;
        this.skillIdArr = [];
        this.t_lv.string = this.skillLv.toString();
    }

    /** 战斗开始 */
    battleStart() {
        this.resetProps();
        this.setSkill(this.data.skillArr[0]); //初始技能
        this.spine.setAnimation(0, SpineAni.attack, true);
    }

    pause() {
        if (this.shooter1) {
            this.shooter1.pause();
        }
        if (this.shooter2) {
            this.shooter2.pause();
        }
        if (this.spine) {
            this.spine.paused = true;
        }
    }

    resume() {
        if (this.shooter1) {
            this.shooter1.resume();
        }
        if (this.shooter2) {
            this.shooter2.resume();
        }
        if (this.spine) {
            this.spine.paused = false;
        }
    }


    static create(id: number, parent: cc.Node, x: number, y: number, callback: (err: any, hero?: blhHero) => void = () => { }) {
        const data = blhStatic.heroMgr.getData(id) as blhHeroData;
        if (!data) {
            return callback(new Error('hero id不存在'));
        }
        blhResMgr.ins().getNodeAsync(Prefabs.hero, (err, node) => {
            if (err) {
                console.error('创建英雄失败', data.id, err);
                callback(err);
                return;
            }
            const hero = node.getComponent(blhHero); //后期改为绑定hero1,hero2...
            node.parent = parent;
            node.setPosition(x, y);
            hero.init(data, (err) => {
                if (err) {
                    return;
                }
                callback(null, hero);
            });
        });
    }
}