/**
 * 普通发射器,直线发射,shooter基类
 * 
 */

import { BulletAngleStartType, Prefabs, ShooterTargetType, Tag } from "../mgr/blhConst";
import blhResMgr from "../mgr/blhResMgr";
import { blhStatic } from "../mgr/blhStatic";
import blhkc from "../utils/blhkc";
import blhBullet from "../tools/blhBullet";
import blhShooter from "../tools/blhShooter";
// import blhBuff from "../tools/blhBuff";
// import blhBulletPool from "../utils/blhBulletPool";


const { ccclass, property } = cc._decorator;
@ccclass
export default class blhShooter1 extends blhShooter {

    id: number = 1;

    // pool: blhBulletPool = new blhBulletPool();
    // poolSize: number = 20;

    // hitBuff: blhBuff = null;

    /** 子弹prefab对应id */
    bulletId: number = null;

    shootRange: number = 1500;

    bltSpd: number = 10;
    /** 子弹scale */
    bltScale: number = 1;
    /** 子弹伤害 */
    bltDmg: number = 1;
    /** 子弹伤害buff */
    bltDmgScale: number = 1;

    targetType: number = 0;

    /** 子弹起始角度类型,为0时角度为0 */
    bulletAngleStartType: BulletAngleStartType = BulletAngleStartType.zero;

    ownerPosInBullet: cc.Vec2 = cc.v2();

    initConf(conf: any): blhShooter {
        super.initConf(conf);
        //这里处理_add方式的配置
        if (conf._add) {
            for (const key in conf._add) {
                if (key === 'bulletConf') {
                    const bulletConf = conf._add[key];
                    if (bulletConf) {
                        for (const i in bulletConf) {
                            if (undefined !== this.bulletConf[i]) {
                                this.bulletConf[i] += bulletConf[i];
                            }
                        }
                    }
                } else if (key === 'bulletSpConf') {
                    const bulletSpConf = conf._add[key];
                    if (bulletSpConf) {
                        if (!this.bulletSpConf) {
                            this.bulletSpConf = {};
                        }
                        for (const i in bulletSpConf) {
                            if (i === 'bulletConf') {
                                const bulletConf = bulletSpConf[i];
                                if (bulletConf) {
                                    if (!this.bulletSpConf.bulletConf) {
                                        this.bulletSpConf.bulletConf = {};
                                    }
                                    for (const j in bulletConf) {
                                        if (undefined !== this.bulletConf[j]) {
                                            this.bulletSpConf.bulletConf[j] += bulletConf[j];
                                        }
                                    }
                                }
                            } else if (undefined !== this.bulletSpConf[i]) {
                                this.bulletSpConf[i] += bulletSpConf[i];
                            }
                        }
                    }
                } else if (undefined !== this[key]) {
                    this[key] += conf._add[key];
                }
            }
        }

        this.ownerPosInBullet = blhkc.getPosFromWorldPos(this.owner, blhStatic.battle.layer_bullet);
        this.ownerPosInBullet.x += this.fireX;
        this.ownerPosInBullet.y += this.fireY;
        return this;
    }

    getFirePos(): cc.Vec2 {
        return this.ownerPosInBullet;
    }

    getTargetPo(firePo: cc.Vec2, target: cc.Node, i: number, plus: any): cc.Vec2 {
        if (ShooterTargetType.up === this.targetType) {
            return cc.v2(firePo.x, firePo.y + this.shootRange);
        }
        return super.getTargetPo(firePo, target, i, plus);
    }

    findTarget(): cc.Node {
        if (ShooterTargetType.nearest === this.targetType) {
            return blhStatic.battle.findNearestEnemy();
        } else if (ShooterTargetType.up === this.targetType) {
            return this.node; //无特定目标时，返回shooter自己，由getTargetPo控制子弹方向
        }

        //默认为最近敌人
        return blhStatic.battle.findNearestEnemy();
    }

    /** 创建bullet但是未确定位置和角度 */
    mkBullet(): blhBullet {
        const bulletNode: cc.Node = blhResMgr.ins().getNode(Prefabs.bullet + this.bulletId);
        const bullet: blhBullet = bulletNode.getComponent(blhBullet);
        bullet.init(this, Tag.bulletA);
        bullet.dmg = this.bltDmg * this.bltDmgScale;
        bulletNode.parent = blhStatic.battle.layer_bullet;
        return bullet;
    }

    createBullet(firePo: cc.Vec2, targetPo: cc.Vec2, i: number, _isLast: boolean, plus: any): blhBullet {
        // let bulletNode: cc.Node = this.pool.get();
        // let bullet: blhBullet;
        // if (bulletNode) {
        //     bullet = bulletNode.getComponent(blhBullet).init(this, Tag.bulletA);
        //     bullet.dmg = this.bltDmg;
        //     bulletNode.parent = blhStatic.battle.layer_bullet;
        // } else {
        //     bullet = this.mkBullet();
        // }
        let bullet: blhBullet = this.mkBullet();
        // const bulletNode: cc.Node = bullet.node;
        //根据类型配置计算角度
        let angle = 0;
        if (this.bulletAngleStartType === BulletAngleStartType.toEnemy) {
            angle = blhkc.posToAngle(firePo.x, firePo.y, targetPo.x, targetPo.y);
        } else if (this.bulletAngleStartType === BulletAngleStartType.up) {
            angle = 90;
        }
        bullet.setAnglePosScale(angle, firePo, this.bltScale);
        bullet.onShoot(firePo, targetPo);
        return bullet;
    }



}