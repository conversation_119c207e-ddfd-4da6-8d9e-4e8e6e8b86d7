/**
 * 回旋发射器
 * 
 */

import blhBullet from "../tools/blhBullet";
import blhShooter1 from "./blhShooter1";


const { ccclass, property } = cc._decorator;
@ccclass
export default class blhShooter2 extends blhShooter1 {


    id: number = 2;

    shootItem(item: blhBullet, targetPo: cc.Vec2, plus: any): void {
        const itemNode = item.node;
        const halfCostTime = (this.shootRange / this.bltSpd / 100) / 2;
        const startPo = itemNode.getPosition();
        cc.tween(itemNode).delay(this.bulletDelay).to(halfCostTime, { x: targetPo.x, y: targetPo.y }, { 'easing': 'quadOut' }).to(halfCostTime, { x: startPo.x, y: startPo.y }, { 'easing': 'quadIn' }).start();
        //tween会被打断,用scheduleOnce
        item.scheduleOnce(() => {
            this.removeItem(item);
        }, halfCostTime * 2);
    }

}