/**
 * 回旋发射器,支持多目标
 * 
 */

import { blhStatic } from "../mgr/blhStatic";
import blhBullet from "../tools/blhBullet";
import blhShooter1 from "./blhShooter1";


const { ccclass, property } = cc._decorator;
@ccclass
export default class blhShooter2 extends blhShooter1 {


    id: number = 2;
    /** 每次发射时计算本次的bulletNum的概率,暂只支持2和1 */
    bulletNumRate: number = 0;

    /** 每次发射子弹的个数 */
    bulletNum = 1;

    getTargetPo(firePo: cc.Vec2, target: cc.Node, i: number, plus: any): cc.Vec2 {
        //从targetArr返回第i个目标的位置
        const cur: cc.Node = blhStatic.battle.nearTargetArr[i];
        if (cur && cur.isValid) {
            return cur.getPosition();
        }
        if (!target || !target.isValid) {
            return null;
        }
        return target.getPosition();
    }

    shootOne(firePo: cc.Vec2, i: number, isLast: boolean, plus: any): boolean {
        if (!this.inUse) {
            return false;
        }
        if (i > 0) { //为0时已经找过target了
            this.checkTarget();
        }
        if (!this.target) {
            return false;
        }

        const bulletNum: number = (this.bulletNumRate > 0) ? (Math.random() < this.bulletNumRate ? 2 : 1) : this.bulletNum; //bulletNumRate暂只支持2和1

        for (let j = 0; j < bulletNum; j++) {
            let oneTargetPo = this.getTargetPo(firePo, this.target, i, plus);
            if (!oneTargetPo) {
                continue;
            }
            let dir: cc.Vec2 = oneTargetPo.sub(firePo).normalize();
            if (dir.magSqr() < 0.0001) {
                // 起点和目标几乎重合，normalize() 会出错（得到 NaN），此时设置默认方向，cc.v2(1, 0)
                dir = cc.v2(0, 1);
            }
            const targetPo: cc.Vec2 = firePo.add(dir.mul(this.shootRange));
            let bullet: blhBullet = this.createBullet(firePo, targetPo, i, isLast, plus);
            this.curBulletMap.set(bullet.node.uuid, bullet);
            bullet.reuse();
            if (j === 0) {
                bullet.isLast = isLast;
            }
            this.shootItem(bullet, targetPo, plus);
        }
        if (isLast) {
            //下一轮cd
            this.unschedule(this.checkNextTurn);
            this.scheduleOnce(this.checkNextTurn);
        }
        return true;
    }

    shootItem(item: blhBullet, targetPo: cc.Vec2, plus: any): void {
        const itemNode = item.node;
        const halfCostTime = (this.shootRange / this.bltSpd / 100) / 2;
        const startPo = itemNode.getPosition();
        cc.tween(itemNode).delay(this.bulletDelay).to(halfCostTime, { x: targetPo.x, y: targetPo.y }, { 'easing': 'quadOut' }).to(halfCostTime, { x: startPo.x, y: startPo.y }, { 'easing': 'quadIn' }).start();
        //tween会被打断,用scheduleOnce
        item.scheduleOnce(() => {
            this.removeItem(item);
        }, halfCostTime * 2);
    }

}