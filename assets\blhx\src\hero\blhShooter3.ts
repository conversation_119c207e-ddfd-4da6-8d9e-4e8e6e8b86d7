/**
 * 带角度一次发射多个,(从主方向开始偏移)
 * 
 */

import blhBullet from "../tools/blhBullet";
import blhShooter1 from "./blhShooter1";


const { ccclass, property } = cc._decorator;
@ccclass
export default class blhShooter3 extends blhShooter1 {


    id: number = 3;

    /** 每次发射子弹的个数 */
    bulletNum = 1;
    /** 每次发射子弹的角度间隔,单位弧度 */
    angleGap = 0.2;


    shootOne(firePo: cc.Vec2, i: number, isLast: boolean, plus: any): boolean {
        if (!this.inUse) {
            return false;
        }
        if (i > 0) { //为0时已经找过target了
            this.checkTarget();
        }
        if (!this.target) {
            return false;
        }
        // const targetPo: cc.Vec2 = cc.v2(0, 800);
        const targetPo: cc.Vec2 = this.getTargetPo(firePo, this.target, i, plus);
        if (!targetPo) {
            return false;
        }

        // const half = Math.floor(this.bulletNum / 2);
        // const dir = targetPo.sub(firePo).normalize();
        // const angle = blhkc.posToAngle(0, 0, dir.x, dir.y);

        for (let j = 0; j < this.bulletNum; j++) {
            // const rotationGap = this.angleGap * (j - half);
            // const oneTargetPo = cc.v2(this.shootRange, 0).rotate(rotationGap).add(firePo);
            const oneTargetPo = targetPo.rotate(j * this.angleGap);
            let bullet: blhBullet = this.createBullet(firePo, oneTargetPo, i, isLast, plus);
            this.curBulletMap.set(bullet.node.uuid, bullet);
            bullet.reuse();
            if (j === 0) {
                bullet.isLast = isLast;
            }
            // bullet.node.angle = angle + rotationGap / blhkc.PI180a;
            this.shootItem(bullet, oneTargetPo, plus);
        }
        if (isLast) {
            //下一轮cd
            this.unschedule(this.checkNextTurn);
            this.scheduleOnce(this.checkNextTurn);
        }
        return true;
    }


}