/**
 * 不直接用tween移动子弹，由子弹自己控制移动(如蓝球)
 * 
 */

import blhBullet from "../tools/blhBullet";
import blhShooter1 from "./blhShooter1";


const { ccclass, property } = cc._decorator;
@ccclass
export default class blhShooter4 extends blhShooter1 {


    id: number = 4;


    shootItem(item: blhBullet, targetPo: cc.Vec2, plus: any): void {
        const costTime = this.shootRange / this.bltSpd / 100;
        item.scheduleOnce(() => {
            this.removeItem(item);
        }, costTime);
    }


}