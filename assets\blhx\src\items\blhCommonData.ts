/**
 * 通用数据,用于存档
 * 
 */

export class OwnedHero {
    instanceId: string; // 唯一ID
    roleId: number; // 角色id
    level: number; // 等级
    star: number; // 星级
    fightState: number; // 状态
    prop: number; //属性 5大属性对应阵营
    rare: number; //稀有度
    career: number;//职业
    pos: number;//编队位置
    grade: number;//英雄阶数
    constructor(data?: Partial<OwnedHero>) {
        this.instanceId = data?.instanceId || '';
        this.roleId = data?.roleId || 0;
        this.level = data?.level || 1;
        this.star = data?.star || 1;
        this.fightState = data?.fightState || 0;
        this.prop = data?.prop || 0;
        this.rare = data?.rare || 0;
        this.career = data?.career || 0;
        this.pos = data?.pos || 0;
        this.grade = data?.grade || 0;
    }

    // /**
    //  * 从普通对象创建 OwnedHero 实例
    //  */
    // static fromObject(obj: any): OwnedHero {
    //     return new OwnedHero(obj);
    // }

    // /**
    //  * 转换为普通对象
    //  */
    // toObject(): any {
    //     return {
    //         instanceId: this.instanceId,
    //         roleId: this.roleId,
    //         level: this.level,
    //         star: this.star,
    //         fightState: this.fightState,
    //         camp: this.camp,
    //     };
    // }

    // /**
    //  * 克隆当前实例
    //  */
    // clone(): OwnedHero {
    //     return new OwnedHero(this.toObject());
    // }
}

import blhkc from "../utils/blhkc";


export default class blhCommonData {

    uid: string = 'bData';
    /**道具数量 */
    propData = {
    }
    /** 建造界面数据*/
    buildData = {
        "watchAd": 0, //许愿次数
        "freeCnt": 0, //免费次数
        "rewardAddCnt": 0 //积分数值
    }
    /**角色数据 */
    roleData:any = [];
    /**登录时间 */
    loginTime: string = null;
    /** 读档 */
    init() {
        const saved = blhkc.getData(this.uid, {});
        for (const key in saved) {
            if (this[key] !== undefined) {
                this[key] = saved[key];
            }
        }
        let date = new Date();
        let today = date.getUTCFullYear() + "-" + date.getUTCMonth() + "-" + date.getUTCDate();
        if(today != this.loginTime){
            this.loginTime = today;
            this.resetNewDayData();
        }
        // if (!blhkc.getData(saveData.regTime, 0)) {
        //     blhkc.saveData(saveData.regTime, Date.now())
        // }
        // if (blhkc.isNewDay(blhkc.getData(saveData.todayloginTime, 0))) {
        //     blhkc.saveData(saveData.todayloginTime, Date.now());
        // }
        // this.level = blhkc.getData(saveData.curRank, 0);
        // this.level_llk = blhkc.getData(saveData.curLLKRank, 0);
        // if (this.stayTime > 0) {
        //     this.stayDay = blhkc.calculateDays(this.stayTime, Date.now());
        // }
    }

    initRoleData(){
        const hero: OwnedHero = new OwnedHero({
            instanceId: "" + 1,
            roleId: 1,
            level: 1,
            star: 4,
            fightState: 1,
            prop: 1,
            rare: 3,
            career: 2,
            pos: 1,
        });
        this.roleData.push(hero);
        const hero1: OwnedHero = new OwnedHero({
            instanceId: "" + 2,
            roleId: 2,
            level: 1,
            star: 4,
            fightState: 1,
            prop: 2,
            rare: 3,
            career: 1,
            pos: 2,
        });
        this.roleData.push(hero1);
        const hero2: OwnedHero = new OwnedHero({
            instanceId: "" + 3,
            roleId: 3,
            level: 1,
            star: 4,
            fightState: 1,
            prop: 4,
            rare: 3,
            career: 2,
            pos: 3,
        });
        this.roleData.push(hero2);
        const hero3: OwnedHero = new OwnedHero({
            instanceId: "" + 4,
            roleId: 4,
            level: 1,
            star: 4,
            fightState: 1,
            prop: 2,
            rare: 3,
            career: 2,
            pos: 4,
        })
        this.roleData.push(hero3);
        this.save();
    }


    resetNewDayData(){
        this.buildData.watchAd = 0;
        this.buildData.freeCnt = 0;
        this.buildData.rewardAddCnt = 0;
        this.save();
    }

    resetData() {
        // if(this.vit < 30){
        //     this.vit = 30;
        // }
        // this.getVitTimes = 5;
        // this.sweepTime = 5;
        // this.adShareCount = 0;
        // this.settleShareCnt = 0;
        // this.chapterSweepTime = [];
        // for(let i = 0; i <= 121; i++){
        //     this.chapterSweepTime.push(5);
        // }
        // if (this.stayTime === 0) {
        //     this.stayTime = Date.now();
        // }
        // this.stayDay = 0;
        this.save();
    }


    toJson() {
        return {
            propData: this.propData,
            buildData: this.buildData,
            roleData: this.roleData,
        };
    }



    save() {
        blhkc.saveData(this.uid, this.toJson());
    }

}
