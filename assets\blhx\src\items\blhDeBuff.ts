/**
 * 敌人挂的debuff基类
 */

import blhHero from "../hero/blhHero";
import blhEnemy from "./blhEnemy";


export default class blhDeBuff {

    /** debuffid */
    id: number = 0;
    /** debuff持续时间,单位秒 */
    duration: number = 0;
    /** debuff生效目标 */
    target: blhEnemy = null;
    /** 最大层数,为0时表示无限层数 */
    maxLayer: number = 0;
    /** 当前层数 */
    curLayer: number = 1;
    /** 效果产生间隔,单位秒,为0时立即生效 */
    effGap: number = 0;

    updateConf(conf: any): blhDeBuff {
        if (!conf) {
            return this;
        }
        for (let key in conf) {
            if (this[key] !== undefined) {
                this[key] = conf[key];
            }
        }
        return this;
    }

    /** 层数增加,会影响eff */
    addLayer(): blhDeBuff {
        if (this.maxLayer > 0 && this.curLayer >= this.maxLayer) {
            return;
        }
        this.curLayer++;
        return this;
    }

    /** 返回是否需要移除 */
    removeLayer(): boolean {
        this.curLayer--;
        if (this.curLayer <= 0) {
            return true;
        }
        return false;
    }

    /** 效果生效 */
    eff(): void {

    }

    setTarget(target: blhEnemy, hero?: blhHero): blhDeBuff {
        this.target = target;
        return this;
    }

    start(): blhDeBuff {
        this.target.unschedule(this.eff);
        if (this.effGap <= 0) {
            //无间隔，立即生效，在duration后停止
            this.eff();
            this.target.scheduleOnce(this.stop.bind(this), this.duration);
            return;
        }
        //根据间隔计算可以生效的时候
        let effTimes = 0;
        if (this.duration > 0) {
            effTimes = Math.floor(this.duration / this.effGap);
        }
        console.log('debuff开始====>', this.id, effTimes);
        this.target.schedule(this.eff.bind(this), this.effGap, effTimes);
        return this;
    }

    stop() {
        if (!this.target) {
            return;
        }
        this.target.unschedule(this.eff);
        this.target.removeDebuff(this.id);
        console.log('debuff移除====>', this.id);
    }

    create(): blhDeBuff {
        return new blhDeBuff();
    }


}