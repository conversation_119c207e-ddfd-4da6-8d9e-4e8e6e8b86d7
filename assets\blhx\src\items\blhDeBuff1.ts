/**
 * 流血debuff
 * 
 * 每秒损失3%最大生命，持续5秒。（每秒最大不可超过施加者攻击*100%的伤害）
 */

import blhHero from "../hero/blhHero";
import { DamageType, DType } from "../mgr/blhConst";
import blhDeBuff from "./blhDeBuff";
import blhEnemy from "./blhEnemy";


export default class blhDeBuff1 extends blhDeBuff {

    /** debuffid */
    id: number = 1;
    /** debuff持续时间,单位秒 */
    duration: number = 5;

    /** 效果产生间隔,单位秒 */
    effGap: number = 1;

    /** 每秒损失最大生命的比例 */
    dmgPerMaxHp: number = 0.3;

    /** 用于计算伤害上限 */
    hero: blhHero = null;

    /** 效果生效 */
    eff(): void {
        if (!this.target || !this.target.isValid) {
            console.log('debuff目标不存在');
            return;
        }
        //根据层数计算伤害
        let dmg = this.target.data.maxHp * this.dmgPerMaxHp * this.curLayer;
        if (this.hero) {
            if (dmg > this.hero.atk) {
                dmg = this.hero.atk;
            }
        }
        console.log('debuff生效', dmg);
        this.target.hurt(dmg, 0, 0, DType.phisic, DamageType.normal);
    }

    setTarget(target: blhEnemy, hero?: blhHero): blhDeBuff {
        this.target = target;
        this.hero = hero;
        return this;
    }

    create(): blhDeBuff1 {
        return new blhDeBuff1();
    }

}