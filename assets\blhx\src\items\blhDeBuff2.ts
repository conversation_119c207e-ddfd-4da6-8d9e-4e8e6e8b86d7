/**
 * 迟缓: 每层减速20%，叠满三层「冰冻」怪物3秒，「迟缓」持续20秒。
 * 
 */

import { DamageType, DType } from "../mgr/blhConst";
import blhDeBuff from "./blhDeBuff";
import blhEnemy from "./blhEnemy";


export default class blhDeBuff2 extends blhDeBuff {

    /** debuffid */
    id: number = 2;
    /** debuff持续时间,单位秒 */
    duration: number = 20;

    maxLayer: number = 3;

    effGap: number = 0;

    /** 每层减速20% */
    slow: number = 0.2;


    /** 效果生效 */
    eff(): void {
        if (!this.target || !this.target.isValid) {
            console.log('debuff目标不存在');
            return;
        }
        if (this.curLayer >= this.maxLayer) {
            console.log('debuff[迟缓]到达最大层数:', this.maxLayer, '冰冻3秒');
            this.target.speed = 0;
            this.target.scheduleOnce(() => {
                if (this.target && this.target.isValid) {
                    console.log('冰冻恢复,速度:', this.target.data.speed);
                    this.target.speed = this.target.data.speed;
                }
            }, 3);
            return;
        }
        //根据层数计算
        let newSpeed = this.target.speed * (1 - this.slow * this.curLayer);
        if (newSpeed < 0) {
            newSpeed = 0;
        }
        this.target.speed = newSpeed;

        console.log('debuff[迟缓]生效,速度:', newSpeed);
    }

    create(): blhDeBuff2 {
        return new blhDeBuff2();
    }

}