/**
 * 伤害数字
 * 
 */

import { Prefabs } from "../mgr/blhConst";
import blhResMgr from "../mgr/blhResMgr";
import { blhStatic } from "../mgr/blhStatic";
import blhkc from "../utils/blhkc";

const { ccclass, property } = cc._decorator;
@ccclass
export default class blhDmgTxt extends cc.Component {

    @property(cc.Label)
    txt: cc.Label = null;

    show(txt: string, parent: cc.Node, x: number, y: number, isCrit: boolean) {
        this.txt.string = txt;
        this.node.parent = parent;
        this.node.x = x;
        this.node.y = y;
        if (isCrit) {
            this.node.color = cc.Color.RED;
        } else {
            this.node.color = cc.Color.WHITE;
        }
        this.node.scale = 0.1;
        // const targetY = y + blhkc.randomInt(40, 80);
        // const targetX = x + blhkc.randomInt(-50, 50);
        const targetY = y + 50;
        const targetX = x;
        cc.tween(this.node).to(0.3, { scale: 2, y: targetY - 20, x: targetX }).to(0.3, { scale: 2, y: targetY }).to(0.3, { scale: 1.7, y: targetY + 20 }).delay(0.5).to(0.2, { opacity: 0, scale: 0.1 }).call(() => {
            this.hide();
        }).start();
    }

    /**回血展示 */
    recoveryShow(txt: string, parent: cc.Node, x: number, y: number){
        this.txt.string = txt;
        this.node.parent = parent;
        this.node.x = x;
        this.node.y = y;
        this.node.color = cc.Color.GREEN;
        this.node.scale = 0.1;
        const targetY = y + blhkc.randomInt(40, 80);
        const targetX = x + blhkc.randomInt(-50, 50);
        cc.tween(this.node).to(0.3, { scale: 2, y: targetY - 20, x: targetX }).to(0.3, { scale: 2, y: targetY }).to(0.3, { scale: 1.7, y: targetY + 20 }).delay(0.5).to(0.2, { opacity: 0, scale: 0.1 }).call(() => {
            this.hide();
        }).start();
    }

    hide() {
        this.node.destroy();
    }

    static create(txt: string, x: number, y: number, isCrit: boolean = false): blhDmgTxt {
        const node: cc.Node = blhResMgr.ins().getNode(Prefabs.dmgTxt);
        const dmgTxt = node.getComponent(blhDmgTxt);
        dmgTxt.show(txt, blhStatic.battle.layer_dmgTxt, x, y, isCrit);
        return dmgTxt;
    }

    static recoveryCreate(txt: string, x: number, y: number): blhDmgTxt{
        const node: cc.Node = blhResMgr.ins().getNode(Prefabs.dmgTxt);
        const dmgTxt = node.getComponent(blhDmgTxt);
        dmgTxt.recoveryShow(txt, blhStatic.battle.layer_dmgTxt, x, y);
        return dmgTxt;
    }

}
