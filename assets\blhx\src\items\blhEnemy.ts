/**
 * 敌人
 * 
 * 
 */

import blhEnemyData from "../entity/blhEnemyData";
import { BattleState, DamageType, DType, Msg, SpineAni, Tag } from "../mgr/blhConst";
import { blhStatic } from "../mgr/blhStatic";
import blhBullet from "../tools/blhBullet";
import blhShooter from "../tools/blhShooter";
import blhFrameAnimComponent from "../utils/blhFrameAnimComponent";
import blhkc from "../utils/blhkc";
import blhDmgTxt from "./blhDmgTxt";
import blhLiveTarget from "./blhLiveTarget";
import blhColliMgr from "../mgr/blhColliMgr";
import blhHero from "../hero/blhHero";
import blhEnemyMgr from "../mgr/blhEnemyMgr";


const { ccclass, property } = cc._decorator;
@ccclass
export default class blhEnemy extends blhLiveTarget {

    @property(sp.Skeleton)
    spine: sp.Skeleton = null;

    data: blhEnemyData = null;

    @property(cc.Collider)
    collider: cc.Collider = null;

    // shooter: blhShooter = null;

    /** 是否是boss */
    isBoss: boolean = false;

    isShooting: boolean = false;

    /** 是否被减速了，防止重复减速 */
    isSlowed: boolean = false;

    // hpBarY: number = 0;

    /** 怪物身上特效 */
    effNode: cc.Node = null;

    /** 怪物无敌状态 */
    noHurt: boolean = false;
    /** 当前atk */
    atk: number = 0;
    /** 当前speed */
    speed: number = 0;


    // lightIdx: string[] = [];

    // isSummon: boolean = false;


    isAtking: boolean = false;

    // /** 物理刚体 */
    // body: cc.RigidBody = null;

    protected onLoad(): void {
        if (this.collider) {
            this.collider.tag = Tag.enemy;
        }
    }


    init(data: blhEnemyData): blhEnemy {
        this.data = data;
        this.hp = this.data.hp;
        this.hpMax = this.hp;
        this.atk = this.data.atk;
        this.speed = this.data.speed;
        this.isAtking = false;
        // this.body = this.node.getComponent(cc.RigidBody);

        // console.log('敌人初始化血量', this.hp, this.data.name);
        // this.node.parent = parent;
        // this.node.setPosition(x, y);
        // this.node.active = false;
        this.node.scale = this.data.scale;

        return this;
    }

    ready(): blhEnemy {
        if (this.hp > 0) {
            blhColliMgr.ins().addEnemy(this);
            this.canBeShoot = true;
        }
        if (this.spine) {
            this.spine.setAnimation(0, SpineAni.move, true);
        }
        return this;
    }

    attack() {
        if (this.isAtking) {
            return;
        }
        this.isAtking = true;
        // console.log('敌人攻击', this.atk);

        const me = this;
        function doAttck() {
            if (blhStatic.battleState !== BattleState.run) {
                return;
            }
            if (me.spine) {
                blhStatic.battle.hurt(me.data.atk);
                me.spine.setAnimation(0, SpineAni.attack, false);
            }
        }
        if (me.spine) {
            me.spine.setCompleteListener(doAttck.bind(me));
            me.spine.setAnimation(0, SpineAni.attack, false);
        }
    }

    remove(): void {
        if (this.node) {
            this.unscheduleAllCallbacks();
            blhStatic.battle.targetMap.delete(this.uuid);
            if (this.spine) {
                this.spine.paused = true;
            }
            blhStatic.battle.killCount++;
            // blhEnemyMgr.ins().removeEnemy(this);
            super.remove();

            // if (this.shooter) {
            //     this.shooter.stop();
            // }
        }
    }


    bossDeadEff() {
        const num = blhkc.randomInt(10, 15);
        this.spine.setAnimation(0, SpineAni.deadState, true);
        // this.spine.paused = true;
        for (let i = 0; i < num; i++) {
            const x = blhkc.randomInt(-90, 90);
            const y = blhkc.randomInt(-150, 200);
            const effId = y < -100 ? 15 : 14;
            this.scheduleOnce(() => {
                blhStatic.effMgr.createEff(effId, x, y, this.node, 2);
            }, 0.3 * i);
        }
        this.scheduleOnce(() => {
            this.spine.paused = false;
            this.spine.setAnimation(0, SpineAni.deadFall, false);
            this.spine.setCompleteListener(() => {
                this.remove();
            });
        }, 0.3 * (num + 1));
    }


    dead(dmgType: DamageType): void {
        // this.playDeadSound();
        // if (dmgType == DamageType.tank) {
        //     if (this.data.type == EnemyType.enemy) {
        //         blhStatic.audioMgr.playSound('collision1');
        //     } else if (this.data.type == EnemyType.item) {
        //         blhStatic.audioMgr.playSound('collision2');
        //     }
        // }
        // /**蜘蛛大王死亡,相机移动 */
        // if (this.data.sp2 == EnemySP.stop) {
        //     blhStatic.battle.stopCameraMove = false;
        // }
        // console.log('怪dead',this.node.uuid);
        this.canBeShoot = false;
        blhColliMgr.ins().delEnemy(this.uuid);
        blhStatic.battle.enemyMap.delete(this.node.uuid);
        // if (this.hpBar) {
        //     this.hpBar.remove();
        //     this.hpBar = null;
        // }

        // if (this.isBoss) {
        //     blhStatic.battle.updateBossHp(0, this.data.name);
        //     blhStatic.battle.addEndFlag(this.node.x);
        // }
        this.dropItem();

        if (!this.data) {
            this.remove();
            return;
        }
        // if (this.isBoss) {
        //     blhStatic.battle.invincible = true;
        //     this.bossDeadEff();
        //     blhStatic.battle.killAllEnemy();
        //     return;
        // }


        this.remove();
    }

    /** 敌人掉落 */
    dropItem() {
        // if (blhStatic.tank.props.addExp()) {
        //     //升级3选1
        //     blhkc.sendMsg(Msg.expLvUp);
        // }
        blhStatic.battle.addExp(this.data.exp);
    }

    hurt(damage: number, crit: number, critDmg: number, dType: DType, dmgType: DamageType): { isCrit: boolean, isHurt: boolean, isDead: boolean, orgDmg: number } {
        // const orgHp = this.hp;
        if (this.noHurt) {
            return;
        }
        const hurtRe = super.hurt(damage, crit, critDmg, dType, dmgType);

        // console.log('受伤', damage, 'orgHp', orgHp, 'hp', this.hp, 'hpMax', this.hpMax, this.data.name);

        if (hurtRe.isDead) {
            this.dead(dmgType);
        } else if (hurtRe.isHurt) {
            // if (this.isBoss) {
            //     blhStatic.battle.updateBossHp(this.hp / this.hpMax, this.data.name);
            // } else {
            //     if (!this.hpBar) {
            //         this.hpBar = blhHpbar.create(blhStatic.battle.layer_hpbar, this, this.hpBarY);
            //     }
            //     this.hpBar.updateHpBar();
            // }
            // this.spine.setAnimation(0, 'hurt', false);

        }
        //显示伤害文字
        blhDmgTxt.create('' + Math.ceil(hurtRe.orgDmg), this.node.x, this.node.y, hurtRe.isCrit);
        if (this.effNode && this.effNode.getComponent(blhFrameAnimComponent)) {
            this.effNode.getComponent(blhFrameAnimComponent).remove();
            this.effNode = null;
        }
        return hurtRe;
    }

    onCollisionEnter(other: cc.Collider, self: cc.Collider) {
        if (!this.canBeShoot) {
            return;
        }
        if (other.tag === Tag.bulletA) {
            const bulletA = other.getComponent(blhBullet);
            if (!bulletA) {
                return;
            }
            const shooter = bulletA.shooter;
            const hero = shooter.owner.getComponent(blhHero);

            this.hurt(bulletA.dmg, hero.crit, hero.critDmg, bulletA.dType, bulletA.dmgType);
            // const wp = (bulletA.shooter as blhWeapon);
            // if (!wp || !wp.data) {
            //     return;
            // }
            // if (bulletA.critChance) {
            //     wp.critChance = bulletA.critChance;
            // }
            // if (wp.hitBack && this.ai) {
            //     this.ai.setHitBackState();
            // }
            // if (wp.palsy && this.ai) {
            //     this.ai.setPalsyState();
            // }
            // //atk2Boss
            // if (this.isBoss && blhStatic.tank.props.atk2Boss > 1) {
            //     this.bossSuffer = blhStatic.tank.props.atk2Boss;
            // }
            // //被<b>旋转飞盾</b>击中的敌人受到的物理伤害+20%
            // if (wp.pAtkSuffer > 1) {
            //     this.pAtkSuffer = wp.pAtkSuffer;
            // }
            // if (wp.eAtkSuffer > 1) {
            //     this.eAtkSuffer = wp.eAtkSuffer;
            // }
            // //被<b>旋转飞盾</b>击中的敌人受到攻击时的暴击率+60%
            // if (wp.critSuffer > 1) {
            //     this.critSuffer = wp.critSuffer;
            // }
            // if (!this.isSlowed && wp.moveSpdScale > 0 && this.ai) {
            //     if (this.ai.moveSpeed > 1) {
            //         this.ai.moveSpeed = this.ai.moveSpeed * (1 - wp.moveSpdScale);//减速
            //         this.isSlowed = true;
            //     }
            // }
            // this.hurt(wp.dmg, wp.critChance, wp.crit, wp.data.dType, bulletA.dmgType);
        }

    }

    getCollider(): cc.Collider {
        return this.collider;
    }

    protected update(dt: number): void {
        if (blhStatic.battleState !== BattleState.run) {
            return;
        }
        if (this.node.y < blhStatic.enemy_atk_y) {
            this.attack();
            return;
        }
        this.node.y -= this.speed * dt;
        // if (this.hpBar) {
        //     this.hpBar.updatePos(this.node.x, this.node.y + this.hpBarY);
        // }



        if (this.node.y < -blhStatic.halfViewHeight) {
            this.dead(DamageType.normal);
        }
    }

}
