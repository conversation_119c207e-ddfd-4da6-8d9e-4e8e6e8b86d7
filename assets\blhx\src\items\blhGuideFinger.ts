/**
 * 引导手指
 * 
 */

import blhResMgr from "../mgr/blhResMgr";

const { ccclass, property } = cc._decorator;
@ccclass
export default class sfrGuideFinger extends cc.Component {

    pos: cc.Vec2 = null;

    toPos: cc.Vec2 = null;

    scaleSpeed: number = 100;

    scaleTw: cc.Tween = cc.tween(this.node).to(0.2, { scale: 1.5 }).to(0.2, { scale: 1 });

    move() {
        cc.Tween.stopAllByTarget(this.node);
        this.node.opacity = 100;
        this.node.setPosition(this.pos);
        cc.tween(this.node).to(0.5, { x: this.toPos.x, y: this.toPos.y, opacity: 255 }).repeat(2, this.scaleTw).call(() => {
            this.move();
        }).start();
    }


    show(pos: cc.Vec2, toPos: cc.Vec2 = null, parent: cc.Node = null) {
        this.node.setPosition(pos);
        this.pos = pos;
        this.toPos = toPos;
        this.node.active = true;
        if (parent) {
            this.node.parent = parent;
        }
        this.node.scale = 1;
        if (toPos) {
            this.move();
            return;
        }
        cc.Tween.stopAllByTarget(this.node);
        this.node.opacity = 255;
        cc.tween(this.node).repeatForever(this.scaleTw).start();
    }

    hide() {
        cc.Tween.stopAllByTarget(this.node);
        this.node.active = false;
    }




    static create(parent: cc.Node): sfrGuideFinger {
        const fingerNode: cc.Node = new cc.Node('finger');
        const picNode: cc.Node = blhResMgr.ins().createPicNode('guide_finger');
        picNode.parent = fingerNode;
        picNode.setPosition(picNode.width / 2 -  10, -picNode.height / 2 - 10);
        const finger: sfrGuideFinger = fingerNode.addComponent(sfrGuideFinger);
        fingerNode.parent = parent;
        fingerNode.active = false;
        return finger;
    }

}
