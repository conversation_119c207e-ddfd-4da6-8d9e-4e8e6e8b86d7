/**
 * 引导提示
 * 
 */

import blhResMgr from "../mgr/blhResMgr";

const { ccclass, property } = cc._decorator;
@ccclass
export default class sfrGuideTip extends cc.Component {


    @property(cc.Label)
    t_name: cc.Label = null;

    @property(cc.Label)
    txt: cc.Label = null;

    @property(cc.Sprite)
    pic: cc.Sprite = null;


    updatePic(pic: string, t_name: string): sfrGuideTip {
        if (!t_name || !pic) {
            return this;
        }
        this.t_name.string = t_name;
        this.pic.spriteFrame = blhResMgr.ins().getImg(pic);
        return this;
    }

    show(pos: cc.Vec2, txt: string) {
        this.node.setPosition(pos);
        this.txt.string = txt;
        this.node.active = true;
    }

    hide() {
        this.node.active = false;
    }


}
