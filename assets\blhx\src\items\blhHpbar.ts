/**
 * 血条
 * 
 */

import blhResMgr from "../mgr/blhResMgr";

const { ccclass, property } = cc._decorator;
@ccclass
export default class blhHpbar extends cc.Component {

    @property(cc.Sprite)
    bg: cc.Sprite = null;
    @property(cc.Sprite)
    bar: cc.Sprite = null;

    source: { hp: number, hpMax: number } = null;

    updateHpBar() {
        if (!this.source) {
            this.node.destroy();
            return;
        }
        this.bar.node.width = this.source.hp / this.source.hpMax * this.bg.node.width;
    }

    updatePos(x: number, y: number) {
        if (!this.node || !this.node.isValid) {
            return;
        }
        this.node.setPosition(x, y);
    }

    remove() {
        this.node.destroy();
    }

    static create(parent: cc.Node, source: { hp: number, hpMax: number }, y: number): blhHpbar {
        const node: cc.Node = blhResMgr.ins().getNode('hpbar');
        node.parent = parent;
        node.y = y;
        const hpbar = node.getComponent(blhHpbar);
        hpbar.source = source;
        return hpbar;
    }

}
