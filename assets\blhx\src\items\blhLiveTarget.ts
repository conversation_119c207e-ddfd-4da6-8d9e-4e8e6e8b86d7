/**
 * 生命体(如敌人，建筑，炮弹等)，有hp, 可作为子弹目标
 * 
 */

import { DamageType, DType } from "../mgr/blhConst";

const { ccclass, property } = cc._decorator;
@ccclass
export default class blhLiveTarget extends cc.Component {

    /** 可否被攻击 */
    canBeShoot: boolean = false;
    /** 当前状态hp,注意与data中的hp不同 */
    hp: number = 0;
    /** 最大hp */
    hpMax: number = 0;
    /** 盾 */
    // armor: number = 0;

    /** 受暴击率提升 */
    // critSuffer: number = 0;
    // /** 元素受伤提升 */
    // eAtkSuffer: number = 0;
    // /** 物理受伤提升 */
    // pAtkSuffer: number = 0;

    /** boss受伤提升 */
    bossSuffer: number = 0;


    dead(dmgType: DamageType) {
        this.canBeShoot = false;
        this.remove();
    }

    /**
     * 受伤计算
     * @param damage 
     * @param crit 
     * @param critDmg 
     * @param dType 元素类型
     * @param dmgType 伤害类型
     * @returns 
     */
    hurt(damage: number, crit: number, critDmg: number, dType: DType, dmgType: DamageType): { isCrit: boolean, isHurt: boolean, isDead: boolean, orgDmg: number } {
        let isCrit = false;
        let isHurt = false;
        let isDead = false;
        if (crit > 0 && critDmg > 0) {
            // if (this.critSuffer > 0) {
            //     critChance += this.critSuffer;
            // }
            if (Math.random() < crit) {
                damage = damage * critDmg;
                isCrit = true;
            }
        }
        // if (dType === WeaponDType.energe && this.eAtkSuffer > 0) {
        //     damage = damage * this.eAtkSuffer;
        // } else if (dType === WeaponDType.physic && this.pAtkSuffer > 0) {
        //     damage = damage * this.pAtkSuffer;
        // }
        if (this.bossSuffer) {
            damage = damage * this.bossSuffer;
        }
        //原始伤害，供伤害文字显示
        const orgDmg = damage;
        // if (this.armor > 0) {
        //     this.armor -= damage;
        //     if (this.armor < 0) {
        //         damage = -this.armor;
        //         this.armor = 0;
        //     } else {
        //         damage = 0;
        //     }
        // }
        if (damage > 0) {
            //仅受伤,不可加血
            this.hp -= damage;
            isHurt = true;
        }
        if (this.hp <= 0) {
            isDead = true;
        }
        return { isCrit, isHurt, isDead, orgDmg };
    }

    remove() {
        this.node.destroy();
    }

}
