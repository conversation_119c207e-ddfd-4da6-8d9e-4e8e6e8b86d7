/**
 * 用户数据,用于存档
 * 
 */

import { saveData } from "../mgr/blhConst";
import blhkc from "../utils/blhkc";


export default class blhUserData {

    uid: string = 'bUser';
    level: number = 0;
    level_llk: number = 0;
    // exp: number = 0;
    // /** 金币,注意coin在tankProp中 */
    // gold: number = 0;
    // /** 钻石 */
    // diamond: number = 0;
    // /** 体力 */
    // vit: number = 0;

    // /**视频卷 */
    // video: number = 0;

    // /**自然回复最大体力 */
    // vitMax: number = 30;
    // /**1格体力恢复时间 */
    // vitRecoveryTime: number = 1200;
    // // vitRecoveryTime: number = 20;
    // /**记录消耗体力时间 */
    // costVitTime: number = 0;


    // /** 视频券 */
    // ticket: number = 0;
    // /**通过章节 */
    // chapter: number = 1;
    // /**通过关卡 */
    // part: number = 0;


    // /**每日获取体力次数 */
    // getVitTimes: number = 0;
    // /**每日扫荡次数 */
    // sweepTime: number = 0;

    /**所有关卡每日扫荡次数 */
    // chapterSweepTime: number[] = [];

    /**首页宝箱相关 */
    // chapterBoxReward = [[3, 4, 5], [4, 7, 10], [4, 7, 10], [4, 7, 10], [4, 7, 10], [4, 7, 10], [4, 7, 10]]

    /**记录宝箱状态 0:没有完成任务 1:完成没有领取  2:完成并且领取 */
    // // boxRewardTasks = {'1_1': 10}
    // boxRewardTasks:any = {};

    // /**视频分享次数 */
    // adShareCount: number = 0;
    // /**视频最大分享次数 */
    // maxAdShareCount: number = 5;
    // /**结算分享次数 */
    // settleShareCnt: number = 0;

    /**关卡停留天数 */
    stayDay: number = 0;

    /**记录关卡停留时间 */
    stayTime: number = 0;


    /** 读档 */
    init() {
        const saved = blhkc.getData('user', {});
        for (const key in saved) {
            if (this[key] !== undefined) {
                this[key] = saved[key];
            }
        }
        if (!blhkc.getData(saveData.regTime, 0)) {
            blhkc.saveData(saveData.regTime, Date.now())
        }
        if (blhkc.isNewDay(blhkc.getData(saveData.todayloginTime, 0))) {
            blhkc.saveData(saveData.todayloginTime, Date.now());
        }
        this.level = blhkc.getData(saveData.curRank, 0);
        this.level_llk = blhkc.getData(saveData.curLLKRank, 0);
        if (this.stayTime > 0) {
            this.stayDay = blhkc.calculateDays(this.stayTime, Date.now());
        }
    }

    resetData() {
        // if(this.vit < 30){
        //     this.vit = 30;
        // }
        // this.getVitTimes = 5;
        // this.sweepTime = 5;
        // this.adShareCount = 0;
        // this.settleShareCnt = 0;
        // this.chapterSweepTime = [];
        // for(let i = 0; i <= 121; i++){
        //     this.chapterSweepTime.push(5);
        // }
        if (this.stayTime === 0) {
            this.stayTime = Date.now();
        }
        this.stayDay = 0;
        this.save();
    }


    toJson() {
        return {
            uid: this.uid,
            level: this.level,
            level_llk: this.level_llk,
            // exp: this.exp,
            // gold: this.gold,
            // vit: this.vit,
            // ticket: this.ticket,
            // diamond: this.diamond,
            // chapter: this.chapter,
            // part: this.part,
            // getVitTimes: this.getVitTimes,
            // sweepTime: this.sweepTime,
            // boxRewardTasks: this.boxRewardTasks,
            // costVitTime: this.costVitTime,
            // chapterSweepTime: this.chapterSweepTime,
            // adShareCount: this.adShareCount,
            stayDay: this.stayDay,
            stayTime: this.stayTime,
            // settleShareCnt: this.settleShareCnt,
        };
    }



    save() {
        blhkc.saveData('user', this.toJson());
    }

}
