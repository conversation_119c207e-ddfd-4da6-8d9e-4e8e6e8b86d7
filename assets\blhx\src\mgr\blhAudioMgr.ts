import { AudioName, Bundles } from "./blhConst";
import blhkc from "../utils/blhkc";
import blhResMgr from "./blhResMgr";



export default class blhAudioMgr {

    private static _me: blhAudioMgr = null;
    public static instance() {
        if (!blhAudioMgr._me) {
            blhAudioMgr._me = new blhAudioMgr();
        }
        return blhAudioMgr._me;
    }

    /** 震动是否打开 */
    isVibOpen: boolean = true;

    /** 背景音是否打开 */
    isBgmOpen: boolean = false;

    /** 音效是否打开 */
    isSoundOpen: boolean = false;

    // currentBgmId: number = -1;
    /** 当前背景音 */
    currentBgmName: string = null;
    private soundTimeMap: Map<cc.AudioClip, number> = new Map();

    /** 保存各个sound的id,audioName: soundId */
    private soundIdMap: Map<string, number> = new Map();

    init(): blhAudioMgr {
        this.isBgmOpen = blhkc.getData('bgmOpen', false);
        this.isSoundOpen = blhkc.getData('soundOpen', false);
        // hejkc.log('hejAudioMgr.init', this.isBgmOpen, this.isSoundOpen);

        // sfrkc.regMsgReciever(Msg.click, () => {
        //     this.playSound(AudioName.click);
        //     return true;
        // });

        // this.setMusicVolume(0.5);
        // this.setEffectVolume(0.3);

        return this;
    }

    /**
     * 播放背景音,会切换掉当前的背景音
     * @param audioName 音乐名
     * @param loop 是否需要循环
     * @param volume 音量大小,默认1
     */
    playBgm(audioName: string, loop: boolean = true) {

        // return;
        blhkc.log('playBgm', this.isBgmOpen, audioName);
        if (!this.isBgmOpen) {
            this.currentBgmName = audioName;
            return;
        }
        // cc.audioEngine.stop(this.currentBgmId);
        cc.audioEngine.stopMusic();
        if (!audioName) {
            audioName = AudioName.bgm;
            blhkc.err('bgm声音未指定,暂使用bgm');
        }
        // sfrkc.log('是否正在播放:',cc.audioEngine.isMusicPlaying(),audioName === this.currentBgmName,audioName,this.currentBgmName);
        this.currentBgmName = audioName;
        // const audio: cc.AudioClip = sfrResMgr.instance().getAudio(audioName);
        blhResMgr.ins().getAudioAsync(Bundles.async, audioName, (err: any, audio: cc.AudioClip) => {
            if (err) {
                console.error(err);
                return;
            }
            const bgmId = cc.audioEngine.playMusic(audio, loop);
            this.soundIdMap.set(audioName, bgmId);
        });
    }

    toggle(): boolean {
        this.isBgmOpen = blhkc.getData('bgmOpen', true);
        if (this.isBgmOpen) {
            this.stopAll();
            return false;
        } else {
            this.openAll();
            return true;
        }
    }

    /**
     * 播放音效
     * @param audioName 音乐名
     * @param loop 是否需要循环
     * @param pauseBgm 播放时是否暂停背景音
     * @param endCall 播放结束的回调
     */
    playSound(audioName: string, loop: boolean = false, pauseBgm: boolean = false, interval?: number, endCall?: (soundId: number) => void) {
        if (!this.isSoundOpen) {
            return;
        }
        // const audio: cc.AudioClip = sfrResMgr.instance().getAudio(audioName);
        blhResMgr.ins().getAudioAsync(Bundles.async, audioName, (err: any, audio: cc.AudioClip) => {
            if (err) {
                console.error(err);
                return;
            }
            if(interval != null){
                let currentTime = Date.now();
                let lastTime = this.soundTimeMap.get(audio);
                if (lastTime != null && (currentTime - lastTime) / 1000 < interval) {
                    return;
                }
                this.soundTimeMap.set(audio, currentTime);
            }
            const soundId = cc.audioEngine.playEffect(audio, loop);
            this.soundIdMap.set(audioName, soundId);
            // hejkc.log('soundId', soundId);
            if (endCall) {
                cc.audioEngine.setFinishCallback(soundId, () => {
                    endCall(soundId);
                });
            }
            if (pauseBgm) {
                this.pauseBgm();
                cc.audioEngine.setFinishCallback(soundId, () => {
                    this.resumeBgm();
                });
            }
        });

        
    }

    // setVolume(audioName: string, vol: number) {
    //     if (this.soundIdMap.has(audioName)) {
    //         cc.audioEngine.setVolume(this.soundIdMap.get(audioName), vol);
    //     }
    // }

    setMusicVolume(vol: number) {
        cc.audioEngine.setMusicVolume(vol);
    }

    setEffectVolume(vol: number){
        cc.audioEngine.setEffectsVolume(vol);
    }

    /** 停止bgm并保存状态 */
    closeBgm() {
        this.isBgmOpen = false;
        cc.audioEngine.stopMusic();
        blhkc.saveData('bgmOpen', false);
    }

    openBgm(audioName?: string) {
        this.isBgmOpen = true;
        this.playBgm(audioName || this.currentBgmName);
        blhkc.saveData('bgmOpen', true);
    }

    /**暂停背景音乐 */
    pauseBgm() {
        this.isBgmOpen = false;
        cc.audioEngine.pauseMusic();
        // hejkc.saveData('bgmOpen', false);
    }

    /** 恢复背景音乐 */
    resumeBgm() {
        this.isBgmOpen = true;
        // hejkc.saveData('bgmOpen', true);
        // if (this.currentBgmId < 0) {
        //     this.playBgm(bgmName);
        // } else {
        //     cc.audioEngine.resume(this.currentBgmId);
        // }
        cc.audioEngine.resumeMusic();
    }

    stopOneSound(audioName: string) {
        if (this.soundIdMap.has(audioName)) {
            cc.audioEngine.stopEffect(this.soundIdMap.get(audioName));
        }
    }

    stopAllSound() {
        cc.audioEngine.stopAllEffects();
    }

    closeSound() {
        this.isSoundOpen = false;
        blhkc.saveData('soundOpen', false);
    }

    openSound() {
        this.isSoundOpen = true;
        blhkc.saveData('soundOpen', true);
    }

    pauseSound(audioName: string) {
        if (this.soundIdMap.has(audioName)) {
            cc.audioEngine.pauseEffect(this.soundIdMap.get(audioName));
        }
    }

    resumeSound(audioName: string) {
        if (this.soundIdMap.has(audioName)) {
            cc.audioEngine.resumeEffect(this.soundIdMap.get(audioName));
        }
    }

    /**停止播放所有音乐 */
    stopAll() {
        this.closeBgm();
        this.closeSound();
        cc.audioEngine.stopAll();
    }

    openAll() {
        this.openBgm(null);
        this.openSound();
    }

    /**暂停所有声音 */
    pauseAll() {
        // this.muteBgm = true;
        // this.muteSound = true;
        cc.audioEngine.pauseAll();
        // hejkc.saveData('soundOpen', false);
        // hejkc.saveData('bgmOpen', false);
    }

    /**恢复所有声音 */
    resumeAll() {
        // this.muteBgm = false;
        // this.muteSound = false;
        // cc.audioEngine.resumeAll();
        if (this.isBgmOpen) {
            this.playBgm(AudioName.bgm);
        }
        // console.log('声音resumeAll');
        // hejkc.saveData('soundOpen', true);
        // hejkc.saveData('bgmOpen', true);
    }

    stopVib() {
        this.isVibOpen = false;
        blhkc.saveData('vibOpen', false);
    }

    openVib() {
        this.isVibOpen = true;
        blhkc.saveData('vibOpen', true);
    }

    vib(sec: number = 1) {
        if (!this.isVibOpen) {
            return;
        }
        if ((window as any).navigator.vibrate) {
            (window as any).navigator.vibrate(sec);
        }
    }
    // /**通用点击音效 */
    // public playSFX() {
    //     const audioName = 'button_click';
    //     this.playSound(audioName);
    // }


}
