import blhBaseData from "../entity/blhBaseData";
import blhResMgr from "./blhResMgr";



export default class blhBaseMgr {


    id: string = 'a1';

    dataMap: Map<number, blhBaseData> = new Map();

    newData(conf: any): blhBaseData {
        return new blhBaseData(conf);
    }

    init(): blhBaseMgr {
        const conf = blhResMgr.ins().getConf(this.id);
        if (!conf) {
            console.error('Mgr init error', this.id);
            return this;
        }
        for (let i = 0; i < conf.length; i++) {
            const one = conf[i];
            this.dataMap.set(one.id, this.newData(one));
        }
        return this;
    }

    getData(id: number): blhBaseData {
        return this.dataMap.get(id);
    }

}
