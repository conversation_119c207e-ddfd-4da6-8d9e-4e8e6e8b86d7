/*

buffMgr, 可由skillMgr初始化值，或由skillMgr替代


*/

import blhBuff from "../tools/blhBuff";
import blhBuffContainer, { blhBuffOwner } from "../tools/blhBuffContainer";
import blhkc from "../utils/blhkc";



export default class blhBuffMgr {


    dataMap: Map<number, blhBuff> = new Map();

    /** 初始添加buff */
    setBuff(buff: blhBuff) {
        this.dataMap.set(buff.id, buff);
    }

    getData(id: number): blhBuff {
        return this.dataMap.get(id);
    }

    /** 权重总数 */
    weightSum: number = 0;
    weightMap: any = {};

    /** 当前状态下(武器位置及受影响因素固定)buffContainer的map,key为weapon的weapnId */
    // weaponBuffContainerMap: Map<number, blhBuffContainer> = new Map();
    /** 当前所有buff按id去重的map,key为buffId */
    curBuffMap: Map<number, blhBuff> = new Map();
    // /** 当前所有武器id的set */
    // curWpIdSet: Set<number> = new Set();
    /** 当前武器等级解锁的buff */
    wpUnlockBuffSet: Set<number> = new Set();

    /** 是否触发buff重新计算 */
    isBuffChanged: boolean = false;

    /** 对应BuffComputeType的计算方法，注意必须与BuffComputeType对应 */
    static computeArr = [
        (buff: blhBuff, baseVal: number, curVal: number) => {
            return buff.val;
        },
        (buff: blhBuff, baseVal: number, curVal: number) => {
            const neg = buff.isNeg ? -1 : 1;
            return buff.val * neg + curVal;
        },
        (buff: blhBuff, baseVal: number, curVal: number) => {
            const neg = buff.isNeg ? -1 : 1;
            return baseVal * (buff.val / 100) * neg + curVal;
        },
        (buff: blhBuff, baseVal: number, curVal: number) => {
            const neg = buff.isNeg ? -1 : 1;
            return baseVal * buff.val * neg + curVal;
        },
        (buff: blhBuff, baseVal: number, curVal: number) => {
            return buff.val;
        },
        (buff: blhBuff, baseVal: number, curVal: number) => {
            return Math.max(buff.val, curVal);
        },
        (buff: blhBuff, baseVal: number, curVal: number) => {
            return Math.min(buff.val, curVal);
        },
        (buff: blhBuff, baseVal: number, curVal: number) => {
            return buff.val * curVal;
        },
    ];


    onceBuffMap: Map<string, (buff: blhBuff) => void> = new Map([

        // [BuffType.itemLv, (buff: blhBuff) => {
        //     //随机一件装备提升1级
        //     blhStatic.tank.randomUpgrade();
        // }],

        // [BuffType.coinAdd, (buff: blhBuff) => {
        //     //立即获得银币
        //     blhStatic.tank.props.addCoin(buff.val);
        // }],
    ]);


    /** 战斗开始时初始化当前buff */
    battleInit() {
        blhkc.log('buff战斗初始化');
        // this.weaponBuffContainerMap.clear();
        this.curBuffMap.clear();
        this.wpUnlockBuffSet.clear();
        // blhStatic.tank.props.buffContainer.clear();
        // //按武器初始化buff容器
        // for (let i = 0; i < blhStatic.tank.weaponArr.length; i++) {
        //     const one = blhStatic.tank.weaponArr[i];
        //     // this.regWeaponContainer(one);
        //     one.buffContainer = new blhBuffContainer(one);
        //     one.updateBid();
        //     // this.curWpIdSet.add(one.data.id);
        //     const arr = blhStatic.upgradeMgr.getBuffidByEquipId(one.data.equipId);
        //     for (let i = 0; i < arr.length; i++) {
        //         const id = arr[i];
        //         this.wpUnlockBuffSet.add(id);
        //     }
        // }

        //加载buff
        this.load();


        this.save();

    }


    computeValWithBase(base: number, buffList: Array<blhBuff>): number {
        let val = base;
        for (let i = 0; i < buffList.length; i++) {
            const buff = buffList[i];
            //只执行一次或不生效的buff不参与计算
            if (buff.isExeOnce || !buff.isValid) {
                continue;
            }
            const computeFunc = blhBuffMgr.computeArr[buff.computeType];
            val = computeFunc(buff, base, val);
            // if (buff.isInt) {
            //     val = Math.round(val);
            // }
        }
        return val;
    }

    getVal(owner: blhBuffOwner, prop: string): number {
        const base = owner.getBaseVal(prop);
        return this.computeValWithBase(base, owner.getContainer().getBuff(prop));
    }


    onBuffChange() {
        // blhStatic.tank.weaponArr.forEach((wp: blhWeapon) => {
        //     wp.init();
        // });
    }


    hasBuff(buffId: number): boolean {
        return this.curBuffMap.has(buffId);
    }

    // getGuide6(): Array<blhBuff> {
    //     return [
    //         this.getData(34) as blhBuff,
    //         this.getData(11) as blhBuff,
    //         this.getData(17) as blhBuff,
    //     ];
    // }

    /** 局内3选1buff列表 */
    prepare3in1(): Array<blhBuff> {
        //准备: 所有当前的武器， 当前已经拥有的buff，


        //rarity需要在一个范围内: 0<rarity<9

        // const uniqMap: Map<string, number> = new Map();
        // this.curBuffMap.forEach((buff: blhBuff) => {
        // if (buff.unique) {
        //     uniqMap.set(buff.unique, 1);
        // }
        // });

        const willOut: Array<blhBuff> = [];
        this.dataMap.forEach((buff: blhBuff) => {
            if (buff.type !== 'tank' && buff.type !== 'equip') {
                return;
            }
            // if (buff.unique && uniqMap.has(buff.unique)) {
            //     return;
            // }
            let canAdd: boolean = true;
            //此buff需要武器(needEquip)，且现已有该武器
            // if (buff.equipLv) {
            //     //是否有武器和前置buff判断
            //     if (!this.wpUnlockBuffSet.has(buff.id) || (buff.needBuff && !this.hasBuff(buff.needBuff))) {
            //         canAdd = false;
            //     }
            // }
            if (canAdd) {
                willOut.push(buff);
            }
        });

        blhkc.shuffle(willOut);

        const out: Array<blhBuff> = [];



        // // -------------测试用!!!  增加指定buff
        // // out.push(this.getData(34) as blhBuff);

        // let needMore = blhStatic.tank.props.lvUpItemCnt - out.length;
        // if (needMore > 0) {
        //     //补默认buff
        //     const addArr = [11, 14, 17]; //缺省池
        //     for (let i = 0; i < needMore; i++) {
        //         out.push(this.getData(addArr[i]) as blhBuff);
        //     }
        //     blhkc.shuffle(out);
        // }

        return out;

    }

    /** 返回添加过的buff */
    addBuff(buffId: number, isInBattle: boolean = false): blhBuff {
        const buff = (this.getData(buffId) as blhBuff).clone();
        buff.isInBattle = isInBattle;
        this.curBuffMap.set(buff.id, buff);
        // blhkc.log('添加buff', buff);
        // if (buff.weaponId === 0) {
        //     const container = blhStatic.tank.props.getContainer();
        //     if (buff.nearType) {
        //         //nearType类型的需要在tank上忽略
        //         buff.isValid = false;
        //     }
        //     container.addBuff(buff);
        //     // blhkc.sendMsg(Msg.addBuff, buff);
        //     container.owner.onChange(buff, 0);
        //     //一次性buff处理
        //     const oneCall = this.onceBuffMap.get(buff.prop);
        //     if (oneCall) {
        //         oneCall(buff);
        //     }
        //     return buff;
        // }

        // for (let i = 0; i < blhStatic.tank.weaponArr.length; i++) {
        //     const one = blhStatic.tank.weaponArr[i];
        //     if (one.data.id !== buff.weaponId) {
        //         continue;
        //     }
        //     one.buffContainer.addBuff(buff);
        //     one.onChange(buff, 0);
        // }


        return buff;
    }

    removeBuff(buffId: number): blhBuff {
        const buff = this.getData(buffId) as blhBuff;
        this.curBuffMap.delete(buff.id);
        // if (buff.weaponId === 0) {
        //     const container = blhStatic.tank.props.getContainer();
        //     container.removeBuff(buff);
        //     container.owner.onChange(buff, 1);
        //     return buff;
        // }
        // for (let i = 0; i < blhStatic.tank.weaponArr.length; i++) {
        //     const one = blhStatic.tank.weaponArr[i];
        //     if (one.data.id !== buff.weaponId) {
        //         continue;
        //     }
        //     one.buffContainer.removeBuff(buff);
        //     one.onChange(buff, 0);
        // }

        return buff;
    }

    private loadToContainer(container: blhBuffContainer, arr: any, inMap: Map<number, number>, isTank: boolean) {
        for (let i = 0; i < arr.length; i++) {
            const buffId = arr[i];
            const buff = (this.getData(buffId) as blhBuff).clone();
            buff.isInBattle = inMap.has(buffId);
            // if (isTank && buff.nearType) {
            //     buff.isValid = false; //nearType类型的需要在tank上忽略
            // }
            container.addBuff(buff);
            this.curBuffMap.set(buff.id, buff);
        }
    }

    resetNewBattle() {
        const data = blhkc.getData('buff', {});
        const dataInBattle = blhkc.getData('buffInBattle', []);
        const inMap: Map<number, number> = new Map();
        for (let i = 0; i < dataInBattle.length; i++) {
            const one = dataInBattle[i];
            inMap.set(one, 1);
        }
        //删除局内的buff
        for (const key in data) {
            if (key !== 'tank') {
                delete data[key];
                continue;
            }
            const arr: [] = data[key];
            //清除tank上的局内buff
            for (let i = 0; i < arr.length; i++) {
                const one = arr[i];
                if (inMap.has(one)) {
                    arr.splice(i, 1);
                    i--;
                }
            }
        }
        blhkc.saveData('buff', data);
        blhkc.saveData('buffInBattle', []);
    }

    load() {
        const data = blhkc.getData('buff', {});
        const dataInBattle = blhkc.getData('buffInBattle', []);
        const inMap: Map<number, number> = new Map();
        for (let i = 0; i < dataInBattle.length; i++) {
            const one = dataInBattle[i];
            inMap.set(one, 1);
        }
        //加载buff
        // for (const key in data) {
        //     if (key === 'tank') {
        //         this.loadToContainer(blhStatic.tank.props.getContainer(), data.tank, inMap, true);
        //         continue;
        //     }
        //     for (let i = 0; i < blhStatic.tank.weaponArr.length; i++) {
        //         const one = blhStatic.tank.weaponArr[i];
        //         if (key === one.bid) {
        //             this.loadToContainer(one.buffContainer, data[key], inMap, false);
        //         }
        //     }
        // }
    }

    save() {
        const saveObj = {};
        // const outArr = blhStatic.tank.props.getContainer().toJsonArr();
        // saveObj['tank'] = outArr[0];
        // const inBattleBuff = outArr[1];
        // const inBattleMap: Set<number> = new Set();
        // for (let i = 0; i < blhStatic.tank.weaponArr.length; i++) {
        //     const one = blhStatic.tank.weaponArr[i];
        //     const arr2 = one.buffContainer.toJsonArr();
        //     saveObj['' + one.bid] = arr2[0];
        //     for (let j = 0; j < arr2[1].length; j++) {
        //         if (!inBattleMap.has(arr2[1][j])) {
        //             inBattleBuff.push(arr2[1][j]);
        //             inBattleMap.add(arr2[1][j]);
        //         }
        //     }
        // }
        // blhkc.saveData('buff', saveObj);
        // blhkc.saveData('buffInBattle', inBattleBuff);
    }

    updateBuff() {
        if (!this.isBuffChanged) {
            return;
        }
        this.isBuffChanged = false;
        this.onBuffChange();
    }


}
