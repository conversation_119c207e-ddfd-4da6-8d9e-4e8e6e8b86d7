
import blhBaseData from "../entity/blhBaseData";
import blhBuildData from "../entity/blhBuildData";
import blhHeroData from "../entity/blhHeroData";
import blhBaseMgr from "./blhBaseMgr";



export default class blhBuildMgr extends blhBaseMgr {

    id: string = 'a8';
    newData(conf: any): blhBaseData {
        const data = new blhBuildData(conf);
        return data;
    }

    getAllData(){
        return this.dataMap;
    }

    
    getBuildData(id: number): blhBuildData {
        return this.dataMap.get(id) as blhBuildData;
    }



}
