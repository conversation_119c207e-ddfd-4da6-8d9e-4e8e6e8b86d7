/**
 * 定制化碰撞检测
 * 
 * 
 */
import { Tag } from "./blhConst";

export default class blhColliMgr {

    private static _me: blhColliMgr = null;
    public static ins() {
        if (!blhColliMgr._me) {
            blhColliMgr._me = new blhColliMgr();
        }
        return blhColliMgr._me;
    }

    /** 已发生过碰撞的map,用于判断onCollisionExit */
    private colliedMap: Map<string, Array<any>> = new Map();

    /** 当前帧移除的collider */
    private removeColliedSet: Set<cc.Collider> = new Set();

    private isOn: boolean = false;

    private cTime: number = 0;


    /** 主角子弹 */
    private bulletAMap: Map<string, { uuid: string, onCollisionEnter: (other: cc.Collider, self: cc.Collider) => void, getCollider: () => cc.Collider }> = new Map();

    // /** 敌人子弹 */
    // private bulletBMap: Map<string, { uuid: string, onCollisionEnter: (other: cc.Collider, self: cc.Collider) => void, getCollider: () => cc.Collider }> = new Map();

    /** 敌人 */
    private enemyMap: Map<string, { uuid: string, onCollisionEnter: (other: cc.Collider, self: cc.Collider) => void, getCollider: () => cc.Collider }> = new Map();


    init(): blhColliMgr {
        return this;
    }


    setOn() {
        this.isOn = true;
    }
    pause() {
        this.isOn = false;
    }
    setOff() {
        this.isOn = false;
        this.bulletAMap.clear();
        // this.bulletBMap.clear();
        this.enemyMap.clear();
    }


    addBulletA(target: { uuid: string, onCollisionEnter: (other: cc.Collider, self: cc.Collider) => void, getCollider: () => cc.Collider }) {
        this.bulletAMap.set(target.uuid, target);
    }
    // addBulletB(target: { uuid: string, onCollisionEnter: (other: cc.Collider, self: cc.Collider) => void, getCollider: () => cc.Collider }) {
    //     this.bulletBMap.set(target.uuid, target);
    // }
    addEnemy(target: { uuid: string, onCollisionEnter: (other: cc.Collider, self: cc.Collider) => void, getCollider: () => cc.Collider }) {
        this.enemyMap.set(target.uuid, target);
    }
    // addGround(target: { uuid: string, onCollisionEnter: (other: cc.Collider, self: cc.Collider) => void, getCollider: () => cc.Collider }) {
    //     this.groundMap.set(target.uuid, target);
    // }

    delBulletA(uuid: string) {
        this.bulletAMap.delete(uuid);
    }
    // delBulletB(uuid: string) {
    //     this.bulletBMap.delete(uuid);
    // }
    delEnemy(uuid: string) {
        this.enemyMap.delete(uuid);
    }
    // delGround(uuid: string) {
    //     this.groundMap.delete(uuid);
    // }


    private getRectFromCollider(c1: cc.Collider): { x: number, y: number, width: number, height: number } {
        const node = c1.node;
        let offset = c1['offset'];
        const w1 = { x: 0, y: 0, width: 0, height: 0 };
        const radius = c1['radius'];
        let po = node.getPosition();
        // if (node.name === 'tank') { //针对tank特殊处理
        //     return blhStatic.tank.getColliderRect();
        // }
        w1.x = po.x + offset.x;
        w1.y = po.y + offset.y;
        if (radius) {
            w1.width = radius;
            w1.height = radius;
        } else {
            const size = c1['size'];
            w1.width = size.width;
            w1.height = size.height;
        }
        return w1;
    }


    /** 非世界坐标的矩形碰矩形 */
    rectRect(a: { x: number, y: number, width: number, height: number }, b: { x: number, y: number, width: number, height: number }): boolean {
        let a_hw = a.width / 2;
        let a_hh = a.height / 2;
        let a_min_x = a.x - a_hw;
        let a_min_y = a.y - a_hh;
        let a_max_x = a.x + a_hw;
        let a_max_y = a.y + a_hh;

        let b_hw = b.width / 2;
        let b_hh = b.height / 2;
        let b_min_x = b.x - b_hw;
        let b_min_y = b.y - b_hh;
        let b_max_x = b.x + b_hw;
        let b_max_y = b.y + b_hh;
        return a_min_x <= b_max_x &&
            a_max_x >= b_min_x &&
            a_min_y <= b_max_y &&
            a_max_y >= b_min_y;
    }
    
    /** 非世界坐标的矩形碰多边形 */
    rectPolygon(c2: cc.Collider, points: Array<cc.Vec2>, x: number, y: number): boolean {
        const offset = c2['offset'];
        const size = c2['size'];
        let x1 = c2.node.x + offset.x - x;
        let y1 = c2.node.y + offset.y - y;
        const hw = size.width / 2;
        const hh = size.height / 2;
        const vArr = [
            cc.v2(x1 - hw, y1 - hh),
            cc.v2(x1 + hw, y1 - hh),
            cc.v2(x1 - hw, y1 + hh),
            cc.v2(x1 + hw, y1 + hh),
        ];
        for (let i = 0; i < vArr.length; i++) {
            const p1 = vArr[i];
            if (cc.Intersection.pointInPolygon(p1, points)) {
                return true;
            }
        }
        return false;
    }



    /** 是否碰撞 */
    private isCollision(c1: cc.Collider, c2: cc.Collider): boolean {
        if (c1.tag === Tag.ground) {
            //地面判断
            const poly = (c1 as cc.PhysicsPolygonCollider).points;
            return this.rectPolygon(c2, poly, c1.node.x, c1.node.y);
        }
        if (this.rectRect(this.getRectFromCollider(c1), this.getRectFromCollider(c2))) {
            return true;
        }
        return false;

    }


    private checkColli(map1: Map<string, { uuid: string, onCollisionEnter: (other: cc.Collider, self: cc.Collider) => void, getCollider: () => cc.Collider }>, mapArr: Array<Map<string, { uuid: string, onCollisionEnter: (other: cc.Collider, self: cc.Collider) => void, getCollider: () => cc.Collider }>>) {
        map1.forEach((co1, key: string) => {
            const c1 = co1.getCollider();
            if (!c1) {
                map1.delete(key);
                return;
            }
            if (!c1.enabled) {
                return;
            }
            // this.collisionMgr['updateCollider'](c1);
            for (let i = 0; i < mapArr.length; i++) {
                const map2 = mapArr[i];
                map2.forEach((co2, key2: string) => {
                    const c2 = co2.getCollider();
                    if (!c2) {
                        map2.delete(key2);
                        return;
                    }
                    if (!c2.enabled) {
                        return;
                    }
                    // this.collisionMgr['updateCollider'](c2);
                    if (this.isCollision(c1, c2)) {
                        if (this.colliedMap.has(co1.uuid + co2.uuid)) {
                            co1['onCollisionStay'] && co1['onCollisionStay'](c2, c1);
                            // console.log('onCollisionStay', co1.uuid);
                        } else {
                            co1.onCollisionEnter(c2, c1);
                            // console.log('onCollisionEnter', co1.uuid);
                        }
                        this.colliedMap.set(co1.uuid + co2.uuid, [this.cTime, co1, co2]);
                        if (this.colliedMap.has(co2.uuid + co1.uuid)) {
                            co2['onCollisionStay'] && co2['onCollisionStay'](c1, c2);
                            // console.log('onCollisionStay', co2.uuid);
                        } else {
                            co2.onCollisionEnter(c1, c2);
                            // console.log('onCollisionEnter', co2.uuid);
                        }
                        this.colliedMap.set(co2.uuid + co1.uuid, [this.cTime, co2, co1]);
                    }
                });
            }
        });
    }

    /** 判定单个物体和其他map */
    private checkColliByOne(c1: cc.Collider, mapArr: Array<Map<string, { uuid: string, onCollisionEnter: (other: cc.Collider, self: cc.Collider) => void, getCollider: () => cc.Collider }>>) {
        // if (!co1) {
        //     return;
        // }
        // const c1 = co1.getCollider();
        if (!c1 || !c1.enabled) {
            return;
        }
        // this.collisionMgr['updateCollider'](c1);
        for (let i = 0; i < mapArr.length; i++) {
            const map2 = mapArr[i];
            map2.forEach((co2) => {
                const c2 = co2.getCollider();
                if (!c2 || !c2.enabled) {
                    return;
                }
                // this.collisionMgr['updateCollider'](c2);
                if (this.isCollision(c1, c2)) {
                    co2.onCollisionEnter(c1, c2);
                }
            });
        }
    }

    private checkCollision(dt: number) {


        this.cTime = Date.now();

        this.checkColli(this.bulletAMap, [this.enemyMap]);
        // this.checkColliByOne(this.tankCollider, [this.bulletBMap, this.enemyMap]);
        // this.checkColli(this.groundMap, [this.bulletAMap, this.bulletBMap]);


        // 判断onCollisionExit
        this.colliedMap.forEach((val: Array<any>, key: string) => {
            if (val[0] !== this.cTime) {
                this.colliedMap.delete(key);
                // console.log('onCollisionExit', val[1].uuid, val[2].uuid);
                val[1]['onCollisionExit'] && val[1]['onCollisionExit'](val[2].getCollider(), val[1].getCollider());
            } else {
                let co1 = val[1].getCollider();
                let co2 = val[2].getCollider();
                if (this.removeColliedSet.has(co1) || this.removeColliedSet.has(co2)) {
                    this.colliedMap.delete(key);
                    // console.log('onCollisionExit', val[1].uuid, val[2].uuid);
                    val[1]['onCollisionExit'] && val[1]['onCollisionExit'](val[2].getCollider(), val[1].getCollider());
                }
            }
        });

        this.removeColliedSet.clear();
    }

    update(dt: number): void {
        if (!this.isOn) {
            return;
        }
        this.checkCollision(dt);
    }

}
