
export const enum Tag {
    tank = 0,
    bulletA = 1,
    enemy = 2,
    bulletB = 3,
    ground = 4,
}
export const enum adAction {
    popWin = 'popWin',
    popWin1 = 'popWin1',
    closePopWin = 'closePopWin',
   
}

export const enum Prefabs {
    battle = 'battle',
    battleLLK = 'battleLLK',
    block = 'block',
    mainUI = 'mainUI',
    dmgTxt = 'dmgTxt',
    enemy = 'enemy',
    bullet = 'bullet',
    hero = 'hero',
    hallUI = "hallUI",
    buildUI = "buildUI",
    boatyarUI = "boatyarUI",
    teamUpUI = "teamUpUI",
}

export const enum Bundles {
    async = 'async',
    preload = 'preload',
    battle = 'battle',
    mainUI = 'mainUI',
    font = 'font',
}
/**
 * 目标类型
 */
export const enum BuffOwnerType {

    hero = 0,
    /** 敌人 */
    enemy = 1,
    // /** boss */
    // boss = 2,
}

export const enum DType {
    /** 物理 */
    phisic = 1,
    /** 法术 */
    spell = 2,

}
export const enum DamageType {
    /** 普通伤害 */
    normal = 0,
    /** 爆炸伤害 */
    boom = 1,
    /** 冰冻伤害 */
    ice = 2,
}
export const enum BulletAngleStartType {
    /** 普通 */
    zero = 0,
    /** 面向敌人 */
    toEnemy = 1,
    /** 向上 */
    up = 2,
}

export const enum ShooterTargetType {
    /** 最近的敌人 */
    nearest = 0,
    /** 向上 */
    up = 1,
}


/**
 * 战斗状态
 */
export const enum BattleState {
    /** 停止状态 */
    stop = 0,
    /** 运行状态 */
    run = 1,
    /** 暂停状态 */
    pause = 2,
    /** 正在结束 */
    ending = 3,
    /** 正在结束2阶段 */
    ending2 = 4,
    /** 进场状态 */
    starting = 5,
}
export const enum SpineAni {
    idle = 'idle',
    move = 'move',
    move2 = 'move2',
    move3 = 'move3',
    move4 = 'move4',
    flee = 'flee',
    flee2 = 'flee2',
    flee3 = 'flee3',
    run = 'run',
    walk = 'walk',
    stand = 'stand',
    attack = 'attack',
    attack1 = 'attack1',
    attack2 = 'attack2',
    death = 'death',
    death1 = 'death1',
    death2 = 'death2',
    death3 = 'death3',
    attack2_skin3 = 'attack2_skin3',
    attack2_skin6 = 'attack2_skin6',
    idle2_skin9 = 'idle2_skin9',
    hurt = 'hurt',
    idle2 = 'idle2',
    deadState = 'deadState',
    deadFall = 'deadFall',
}

export const enum State {
    idle = 'idle',
    init = 'init',
    normal = 'normal',
    flee = 'flee',
    swing = 'swing',
    down = 'down',
    attack = 'attack',
    move = 'move',
    moving = 'moving',
    play = 'play',
    stop = 'stop',
    pause = 'pause',
    hurt = 'hurt',
    hurt1 = 'hurt1',
    hurt2 = 'hurt2',
    hurting = 'hurting',
    dead = 'dead',
    deading = 'deading',
    lock = 'lock',
    run = 'run',
    runing = 'runing',
    moveup = 'moveup',
    movedown = 'movedown',
    inwater = 'inwater',
    jump = 'jump',
}

export const enum BuffType {
    atk = 'atk',
    atkScale = 'atkScale',
    atk2Boss = 'atk2Boss',
    // pAtkScale = 'pAtkScale',
    // eAtkScale = 'eAtkScale',
    atkInc = 'atkInc',
    critChance = 'critChance',
    crit = 'crit',
    exp = 'exp',
    expAdd = 'expAdd',
    tankAtk = 'tankAtk',
    tankBltSpd = 'tankBltSpd',
    heal = 'heal',
    healSpd = 'healSpd',
    bltSpd = 'bltSpd',
    cd = 'cd',
    // pBltSpd = 'pBltSpd',
    // eBltSpd = 'eBltSpd',
    hp = 'hp',
    hpMax = 'hpMax',
    armor = 'armor',
    armorRecover = 'armorRecover',
    armorRecoverScale = 'armorRecoverScale',
    armorRecoverSpd = 'armorRecoverSpd',
    itemLv = 'itemLv',
    itemPrepare = 'itemPrepare',
    coinReward = 'coinReward',
    coinWave = 'coinWave',
    coinAdd = 'coinAdd',
    numsPerShoot = 'numsPerShoot',
    hitThrough = 'hitThrough',
    /**必暴击次数 */
    critSureCnt = 'critSureCnt',
    /**重炮爆炸范围 */
    boomRange = 'boomRange',
    /**投球机攻击n次,可投掷两颗球 */
    ball2Cnt = 'ball2Cnt',
    /**投球机弹跳次数 */
    ballJump = 'ballJump',
    /**小怪受到的物理伤害增加 */
    pAtkSuffer = 'pAtkSuffer',
    /**小怪受到的元素伤害增加 */
    eAtkSuffer = 'eAtkSuffer',
    /**小怪受到的暴击率增加 */
    critSuffer = 'critSuffer',
    /**命中时带击退buff */
    hitBack = 'hitBack',
    /**麻痹效果 */
    palsy = 'palsy',
    /**火焰数X3 */
    bltNum = 'bltNum',
    /** 射程 */
    shootRange = 'shootRange',
    /** 攻击目标增加 */
    targetAdd = 'targetAdd',
    /** 攻击移速调整 */
    moveSpdScale = 'moveSpdScale',

}
/**
 * 工具类型
 */
export const enum ToolType {
    null = -1,
    /** 移出 */
    moveout = 0,
    /** 自动凑齐1种水果 */
    mag = 1,
    /** 打乱格子中的水果位置 */
    reset = 2,
    /** 提示 */
    hint = 3,
    /** 洗牌 */
    refreshLLK = 4,
    /** 颠倒 */
    reverse = 5,
    /** 复活 */
    revive = 6,
    /** 增加时间 */
    clock = 7,
    /** 赠送hint */
    sendHint = 8,
}

/**
 * 道具名称
 * 1: 钻石
 * 2: 物资
 * 3: 战术教材
 * 100: 心智单元
 * 101: 心智魔方 
 * 102: 通用部件
 * 103: 3星布里
 * 104: 4星布里
 * 105: 5星布里
 * 106: 誓约道具
 * 107: 送礼道具
 */
export const enum blPropType{
    diamond = 1,
    goods = 2,
    zsjc = 3,
    xzdy = 100,
    xzmf = 101,
    commonMat = 102,
    bl3 = 103,
    bl4 = 104,
    bl5 = 105,
    sydj = 106,
    sldj = 107,
}



// export const enum SsEventName {
//     login = 'login',
//     loading = 'loading',
//     level = 'level',
//     leveling = 'leveling',
//     guide_complete = 'guide_complete',
//     stage_enter = 'stage_enter',
//     equip_refresh = 'equip_refresh',
//     battle_start = 'battle_start',
//     battle_end = 'battle_end',
//     battle_quit = 'battle_quit',
//     feature_activate = 'feature_activate',
//     stage_settlement = 'stage_settlement',
//     get_gift = 'get_gift',
//     equip_unlock = 'equip_unlock',
//     equip_level_up = 'equip_level_up',
//     equip_reset = 'equip_reset',
//     equip_skill_unlock = 'equip_skill_unlock',
//     combat_equip_configure = 'combat_equip_configure',
//     goods_buy = 'goods_buy',
//     resource_get = 'resource_get',
//     resource_cost = 'resource_cost',
//     ads_exposure = 'ads_exposure',
//     ads_button_click = 'ads_button_click',
//     ads_play_result = 'ads_play_result',
//     system_button_click = 'system_button_click',
//     share = 'share'
// }



export const enum Msg {
    initComplete = 'initComplete',
    closeLoading = 'closeLoading',

    guideClick = 'guideClick',
    guideShow = 'guideShow',
    guideDone = 'guideDone',
    guideHide = 'guideHide',
    /**领取侧边栏奖励 */
    getSliderReward = 'getSliderReward',
    /**重新设置底部常驻banner时间 */
    resetBtmBannerTime = 'resetBtmBannerTime',
    /**发送宝箱点击完之后游戏开始事件 */
    boxEndToGameStart = 'boxEndToGameStart',
    /** 体力变化 */
    updateVit = 'updateVit',

    /**建造ui更新 */
    refreshBuildLab = "refreshBuildLab",

}

export const enum AudioName {
    bgm = 'bgm',
    click = 'click',
    succ = 'succ',
    linkSuc = 'linkSuc',
    linkFail = 'linkFail',
    useTool = 'useTool',
    combine = 'combine',
}


/**保存类型 */
export enum saveData {
    /** 当前关卡 */
    chapter = 'chapter',
    /** 当前关卡 */
    part = 'part',

    isNew = 'isNew',
    regTime = 'regTime',
    /** 今天首次登录时间 */
    todayloginTime = 'todayloginTime',
    /** 英雄 */
    heros = 'heros',
 
}
export enum battleMode {
    /** 普通模式 */
    normal = 'normal',
   
}


// /**进入狂点宝箱类型 */
// export enum sfrBoxSupriseData {
//     //关卡结束
//     partEnd = 1,
//     //关卡开始
//     partStart = 2,
// }

// export class pzaResourceDetail {

//     // 资源路径
//     public url: string = null!;
//     // 资源类型
//     public resourceType: pzaResourceType = null!;
//     // 资源路径
//     public url: string = null!;
//     // 资源类型
//     public resourceType: pzaResourceType = null!;

//     constructor(url: string, resourceType: pzaResourceType = pzaResourceType.Prefab) {
//         this.url = url;
//         this.resourceType = resourceType;
//     }
// }


export class blhConst {

}
