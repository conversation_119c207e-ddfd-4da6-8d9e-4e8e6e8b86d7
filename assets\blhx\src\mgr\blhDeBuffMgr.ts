/**
 * debuff管理
 */

import blhDeBuff from "../items/blhDeBuff";
import blhDeBuff1 from "../items/blhDeBuff1";
import blhDeBuff2 from "../items/blhDeBuff2";
import blhEnemy from "../items/blhEnemy";
import blhkc from "../utils/blhkc";


export default class blhDeBuffMgr {


    private deBuffMap: Map<number, blhDeBuff> = new Map<number, blhDeBuff>([
        [1, new blhDeBuff1()],
        [2, new blhDeBuff2()],
    ]);

    getDebuff(id: number): blhDeBuff {
        return this.deBuffMap.get(id);
    }

    createDeBuff(id: number): blhDeBuff {
        let debuff = this.deBuffMap.get(id);
        if (debuff) {
            return debuff.create();
        }
        return null;
    }

    /** 敌人挂debuff */
    addDeBuff(deBuffId: number, enemy: blhEnemy, debuffConf?:any) {
        let deBuff: blhDeBuff = this.createDeBuff(deBuffId);
        if (!deBuff) {
            console.log('debuff不存在', deBuffId);
            return;
        }
        for (let i = 0; i < enemy.debuffArr.length; i++) {
            const one = enemy.debuffArr[i];
            if (one.id === deBuffId) {
                one.addLayer().updateConf(debuffConf);
                return;
            }
        }
        enemy.debuffArr.push(deBuff.updateConf(debuffConf).setTarget(enemy).start());
    }

    /** 敌人移除debuff */
    removeDebuff(deBuffId: number, enemy: blhEnemy) {
        for (let i = 0; i < enemy.debuffArr.length; i++) {
            const one = enemy.debuffArr[i];
            if (one.id === deBuffId) {
                const isRemove = one.removeLayer();
                if (isRemove) {
                    enemy.debuffArr.splice(i, 1);
                }
                return;
            }
        }
    }

}
