/**
 * 效果管理器,此项目未使用
 */

import blhkc from "../utils/blhkc";
import { Bundles } from "./blhConst";
import blhResMgr from "./blhResMgr";
import { blhStatic } from "./blhStatic";



export default class blhEffMgr {


    private pres: string[] = null;

    /** 
     * 效果map,key为id,val为预置体数组
     * 1: 普通爆炸
     * 2: shake
     * 3: circle1
     * 
     */
    private effMap: Map<number, { pres: Array<string>, otherPath?: string, createFn?: any }> = new Map([
        //普通爆炸
        [1, { pres: ['bomb1', 'smoke1', 'shake'] }],
        [2, { pres: ['shake'] }],
        [3, { pres: ['circle1'] }],
        [4, { pres: ['bomb1', 'smoke4'] }],
        [5, { pres: ['smoke5'] }],
        [6, { pres: ['smoke6'] }],
        [7, { pres: ['smoke1'] }],
        [8, { pres: ['scare'] }],
        [9, { pres: ['bomb1', 'smoke1'] }],
        [10, { pres: ['bomb1', 'smoke2'] }],
        [11, { pres: ['bulletBoomFrame'], otherPath: 'eff/' }],
        [12, { pres: ['smoke7'] }],
        [13, { pres: ['bomb1', 'smoke2'] }],
        [14, { pres: ['bomb1', 'smoke3', 'shake'] }],
        [15, { pres: ['bomb1', 'smoke4', 'shake'] }], //地面爆炸
        [16, { pres: ['fireThrowHitFrame'], otherPath: 'eff/' }] //喷火器特效
    ]);

    /** 部分效果调整zIndex，以实现合批 */
    private zIndexMap: Map<string, number> = new Map([
        ['smoke2', -5],
        ['smoke3', -5],
        ['smoke4', -5],
    ]);

    private tw: cc.Tween = null;

    getPres(): string[] {
        if (!this.pres) {
            const set: Set<string> = new Set();
            this.effMap.forEach((v, k) => {
                for (let i = 0; i < v.pres.length; i++) {
                    const one = v.pres[i];
                    if (one === 'shake') {
                        continue;
                    }
                    let path = v.otherPath || 'eff/';
                    set.add(path + one);
                }
            });
            this.pres = Array.from(set);
        }
        return this.pres;
    }

    defaultCreateFn(i: number, node: cc.Node, parent: cc.Node, x: number, y: number) {
        node.parent = parent;
        node.setPosition(x, y);
    }

    stop(){
        if (this.tw) {
            this.tw.stop();
        }
    }

    /** 注意震动会导致相机位移，需要在战斗结束时stop掉这个tw */
    shake() {
        // const node = sfrStatic.main.cameraNode;
        // const orgX = node.x;
        // const orgY = 0; //y方向不动
        // const gap = 10;
        // const count = 2;
        // const t1 = 0.05;
        // // const totle = count * 2 * t1;
        // if (this.tw) {
        //     this.tw.stop();
        // }
        // this.tw = cc.tween(sfrStatic.main.cameraNode)
        //     .repeat(count, cc.tween()
        //         .to(t1, { x: orgX + gap, y: orgY + gap })
        //         .to(t1, { x: orgX, y: orgY })).call(() => {
        //             node.x = orgX;
        //             node.y = orgY;
        //         }).start();
    }

    createEff(id: number, x: number, y: number, parent: cc.Node, scale: number = 1) {
        const one = this.effMap.get(id);
        const preArr = one.pres;
        for (let i = 0; i < preArr.length; i++) {
            const onePre = preArr[i];
            if (onePre === 'shake') {
                this.shake();
                continue;
            }
            if (onePre.indexOf('bomb') >= 0) {
                blhStatic.audioMgr.playSound('bomb');
            }
            let path = one.otherPath || 'eff/';
            blhResMgr.ins().getPrefabAsync(Bundles.async, path + onePre, (err: any, prefab: cc.Prefab) => {
                if (err) {
                    blhkc.err(err);
                    return;
                }
                if (!prefab) {
                    blhkc.err('eff预制体不存在', onePre);
                    return;
                }
                const node: cc.Node = cc.instantiate(prefab);
                const zIndex = this.zIndexMap.get(onePre);
                if (zIndex) {
                    node.zIndex = zIndex;
                }
                if (i === 0) {
                    node.scale = scale;
                }
                const fn = one.createFn ? one.createFn : this.defaultCreateFn;
                fn(i, node, parent, x, y);
            });
        }
    }


}
