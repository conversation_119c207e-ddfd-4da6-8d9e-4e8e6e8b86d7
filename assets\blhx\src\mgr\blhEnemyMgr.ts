
import blhBaseData from "../entity/blhBaseData";
import blhEnemyData from "../entity/blhEnemyData";
import blhEnemy from "../items/blhEnemy";
// import blhBulletPool from "../utils/blhBulletPool";
import blhkc from "../utils/blhkc";
import blhBaseMgr from "./blhBaseMgr";
import { Bundles, Prefabs, SpineAni, Tag } from "./blhConst";
import blhResMgr from "./blhResMgr";



export default class blhEnemyMgr extends blhBaseMgr {

    private static _me: blhEnemyMgr = null;
    public static ins() {
        if (!blhEnemyMgr._me) {
            blhEnemyMgr._me = new blhEnemyMgr();
        }
        return blhEnemyMgr._me;
    }


    id: string = 'a4';

    // pool: blhBulletPool = new blhBulletPool();
    // poolSize: number = 20;

    newData(conf: any): blhBaseData {
        const data = new blhEnemyData(conf);
        // this.spineMap.set(data.spine, data);
        return data;
    }

    getEnemyData(rankData: any): blhEnemyData {
        const data = (this.getData(rankData.id) as blhEnemyData).clone();

        // if (data.hp > 0) {
        //     data.hp = data.hp * (1 + (rankData.lvl2 * 0.1));
        //     data.atk = data.atk * (1 + (rankData.lvl * 0.1));
        // }
        return data;
    }

    // removeEnemy(enemy: blhEnemy) {
    //     this.pool.back(enemy.node);
    // }

    createEnemy(id: number, x: number, y: number, parent: cc.Node): blhEnemy {

        const data = this.getData(id) as blhEnemyData;
   
        // console.log('出怪', data.name, x, y);


        // let newNode = this.pool.get();
        // if (!newNode) {
        //     newNode = blhResMgr.ins().getNode(Prefabs.enemy);
        // }
        let newNode = blhResMgr.ins().getNode(Prefabs.enemy);
        const enemy = newNode.getComponent(blhEnemy);
        newNode.parent = parent;
        newNode.setPosition(x, y);
        if (data.scale) {
            newNode.scale = data.scale;
        }

        if (data.spine) {
            blhResMgr.ins().getSpineAsync(Bundles.async, data.spine, (err, skd) => {
                if (err) {
                    return blhkc.err(err);
                }
                enemy.spine.skeletonData = skd;
                if (data.skin) {
                    enemy.spine.setSkin(data.skin);
                }
                enemy.ready();
            });
        }
        return enemy.init(data);
    }

}
