/**
 * 引导管理器
 */

// import sfrGuideFinger from "../items/sfrGuideFinger";
// import sfrGuideTip from "../items/sfrGuideTip";
// import sfrkc from "../utils/sfrkc";
// import { sfrStatisticsMgr } from "../utils/sfrStatisticsMgr";
// import { Bundles, GuideAct, GuideHide, Msg, Prefabs, SsEventName } from "./sfrConst";
// import sfrResMgr from "./sfrResMgr";
// import { sfrStatic } from "./sfrStatic";

/** 引导数据 */
// export class sfrGuideData {
//     /** 用于标识引导步骤 */
//     act: number = null;
//     /** 引导tip文字,若无则不显示tip */
//     tipTxt: string = null;
//     /** tip位置 */
//     tipPos: cc.Vec2 = null;
//     tipName: string = null;
//     tipPic: string = null;

//     /** 遮罩位置, */
//     maskPos: cc.Vec2 = null;
//     /** 显示mask时，指定target,为null不显示mask */
//     maskTarget: cc.Node = null;
//     /** 找目标节点的方法 */
//     findMaskTarget: () => cc.Node = null;
//     /** 手指位置,为null不显示手指 */
//     fingerPos: cc.Vec2 = null;
//     /** 手指移动位置 */
//     fingerToPos: cc.Vec2 = null;
//     /** 获取手指两个位置的方法,用于需要即时计算的情况 */
//     fingerPosGet: () => cc.Vec2[] = null;

//     /** 是否显示mask */
//     showMask: boolean = false;
//     /** 点击clickNode阻止执行guideDone */
//     clickNoThrough: boolean = false;
//     /** 在此秒后自动done */
//     autoDoneTime: number = 0;
//     /** 延迟n秒后再执行 */
//     delay: number = 0;

//     /** 在执行时的回调 */
//     onShow: () => void = null;

//     /** 下一个data,点击后处理 */
//     next: sfrGuideData = null;

//     constructor(data: any) {
//         this.act = data.act || 0;
//         this.tipTxt = data.tipTxt;
//         this.tipPos = data.tipPos;
//         this.showMask = data.showMask || false;
//         this.clickNoThrough = data.clickNoThrough || false;
//         this.autoDoneTime = data.autoDoneTime || 0;
//         this.maskTarget = data.maskTarget;
//         this.maskPos = data.maskPos;
//         this.fingerPos = data.fingerPos;
//         this.fingerToPos = data.fingerToPos;
//         this.fingerPosGet = data.fingerPosGet;
//         this.tipPic = data.tipPic;
//         this.tipName = data.tipName;
//         this.delay = data.delay || 0;
//         this.onShow = data.onShow;
//         this.findMaskTarget = data.findMaskTarget;
//     }

//     /** 设置下一个并返回下一步，以支持链式 */
//     setNext(data: any): sfrGuideData {
//         let fatherData: sfrGuideData = this;
//         while (fatherData.next) {
//             fatherData = fatherData.next;
//         }
//         fatherData.next = new sfrGuideData(data);
//         return this;
//     }
// }

export default class sfrGuideMgr {


//     private guideTip: sfrGuideTip = null;

//     private guideFinger: sfrGuideFinger = null;

//     private layer_guide: cc.Node = null;

//     private clickNode: cc.Node = null;

//     private mask: cc.Node = null;

//     private maskBlack: cc.Node = null;

//     isShowing: boolean = false;
//     /** 是否在定时done过程中，防止提前done */
//     private isWaitDone:boolean = false;

//     guideData: sfrGuideData = null;

//     init(layer_guide: cc.Node, clickNode: cc.Node, mask: cc.Node): sfrGuideMgr {
//         this.clickNode = clickNode;
//         this.mask = mask;
//         this.maskBlack = this.mask.getChildByName('black');

//         this.layer_guide = layer_guide;
//         this.isWaitDone = false;

//         this.guideFinger = sfrGuideFinger.create(this.layer_guide);

//         sfrStatic.resMgr.getPrefabAsync(Bundles.async, Prefabs.guide_tip, (err: any, prefab: cc.Prefab) => {
//             if (err) {
//                 sfrkc.err(err);
//                 return;
//             }
//             const tipNode: cc.Node = cc.instantiate(prefab);
//             tipNode.parent = this.layer_guide;
//             tipNode.active = false;
//             this.guideTip = tipNode.getComponent(sfrGuideTip);
//         });



//         this.clickNode.on(cc.Node.EventType.TOUCH_END, () => {
//             if (this.guideData.clickNoThrough) {
//                 return;
//             }
//             // console.log('clickNode.guideDone');
//             this.guideDone();
//         }, this);
//         this.maskBlack.on(cc.Node.EventType.TOUCH_END, (e: cc.Event.EventTouch) => {
//             if (!this.clickNode.active || this.guideData.clickNoThrough) {
//                 return;
//             }
//             this.maskAlert(this.clickNode.getPosition());
//         }, this);

//         this.clickNode['_touchListener'].setSwallowTouches(false); //默认点击可穿透


//         //注册引导事件监听
//         sfrkc.resetMsgReciever(Msg.guideShow, this.guideShow.bind(this));
//         sfrkc.resetMsgReciever(Msg.guideHide, this.guideHide.bind(this));
//         sfrkc.resetMsgReciever(Msg.guideDone, this.guideDone.bind(this));


//         //测试
//         // this.guideFinger.scheduleOnce(() => {
//         //     // this.guideFinger.show(cc.v2(0, 0),cc.v2(0,400));
//         //     sfrkc.sendMsg(Msg.guideShow, new sfrGuideData({
//         //         tipTxt: '测试',
//         //         tipPos: cc.v2(-100, 100),
//         //         // maskTarget: sfrStatic.main,
//         //     }));
//         // }, 2);

//         return this;

//     }


//     maskAlert(pos: cc.Vec2) {
//         const node = sfrResMgr.instance().createPicNode('09FX_6');
//         node.parent = this.layer_guide;
//         node.setPosition(pos);
//         node.opacity = 10;
//         node.scale = 5;
//         const flash = cc.tween().delay(0.07).call(() => {
//             node.opacity = 0;
//         }).delay(0.07).call(() => {
//             node.opacity = 255;
//         });
//         cc.tween(node).to(0.2, { opacity: 255, scale: 1 }).repeat(3, flash).call(() => {
//             node.destroy();
//         }).start();
//     }

//     /** 获取当前引导标识,不在引导状态返回0 */
//     getAct(): number {
//         if (!this.guideData) {
//             return 0;
//         }
//         return this.guideData.act;
//     }

//     /** 隐藏并移动到下一个引导,但不执行guideDone(即不直接执行next) */
//     moveToNext(isHide: boolean) {
//         if (isHide) {
//             this.hideAll();
//         }
//         if (this.guideData && this.guideData.next) {
//             this.guideData = this.guideData.next;
//         }
//     }

//     /** 引导结束，如果有next则执行next引导,否则发送guideClick事件,并hideAll */
//     guideDone(): boolean {
//         if (!this.guideData || this.isWaitDone) {
//             return false;
//         }

//         //数数相关
//         this.sendEvent(this.guideData.act);

//         if (this.guideData.next) {
//             // console.log('guideDone',this.guideData.act);
//             this.guideShow(this.guideData.next);
//             return false;
//         }
//         // console.log('guideDone1',this.guideData.act);
//         this.hideAll();
//         this.guideData = null;
//         // sfrkc.sendMsg(Msg.guideClick, this.guideData);
//         return false;
//     }

//     sendEvent(act: number){
//         let guideId = 0;
//         switch(act){
//             case  GuideAct.guide1:
//                 guideId = 1;
//                 break;
//             case GuideAct.guide2:
//                 guideId = 2;
//                 break;
//             case GuideAct.guide3:
//                 guideId = 3;
//                 break;
//             case GuideAct.guide5:
//                 guideId = 4;
//                 break;
//             case GuideAct.guide6:
//                 guideId = 5;
//                 break;
//             case GuideAct.guide8:
//                 guideId = 6;
//                 break;
//             case GuideAct.guide10:
//                 guideId = 7;
//                 break;
//             case GuideAct.guide12:
//                 guideId = 8;
//                 break;
//             case GuideAct.guide13:
//                 guideId = 9;
//                 break;
//             case GuideAct.guide15:
//                 guideId = 10;
//                 break;
//             case GuideAct.guide17:
//                 guideId = 11;
//                 break;
//         }
//         if(guideId > 0){
//             sfrStatisticsMgr.sendEvent(SsEventName.guide_complete, { name: "guide_id", value: guideId + ''});
//         }
//     }



//     /** 隐藏某个或全部引导,用于不执行guideDone但关闭某部分引导(如手指)的情况 */
//     guideHide(type: number): boolean {
//         if (!type) {
//             this.hideAll();
//             return false;
//         } else if (type === GuideHide.tip) {
//             this.hideTip();
//         } else if (type === GuideHide.finger) {
//             this.hideFinger();
//         } else if (type === GuideHide.mask) {
//             this.hideMask();
//         }
//         return false;
//     }

//     /** 显示引导 */
//     guideShow(data: sfrGuideData): boolean {
//         if (this.isWaitDone) {
//             return;
//         }
//         this.layer_guide.active = true;
//         this.guideData = data;
//         if (this.guideData.delay) {
//             const delay = this.guideData.delay;
//             this.guideData.delay = 0;
//             this.hideAll();
//             sfrStatic.main.scheduleOnce(() => {
//                 this.guideShow(data);
//             }, delay);
//             return;
//         }
//         this.isShowing = true;
//         // console.log('guideShow',data.act);
//         if (data.tipPos && data.tipTxt) {
//             this.showTip(data.tipTxt, data.tipPos, data.tipName, data.tipPic);
//         } else {
//             this.hideTip();
//         }
//         if (data.showMask) {
//             this.showMask(data.maskTarget || (data.findMaskTarget && data.findMaskTarget()), data.maskPos);
//         } else {
//             this.hideMask();
//         }
//         if (data.fingerPos || data.fingerPosGet) {
//             if (data.fingerPos) {
//                 this.guideFinger.show(data.fingerPos, data.fingerToPos);
//             } else {
//                 const posArr = data.fingerPosGet();
//                 this.guideFinger.show(posArr[0] || cc.v2(), posArr[1]);
//             }
//         } else {
//             this.hideFinger();
//         }
//         if (data.autoDoneTime) {
//             this.isWaitDone = true;
//             sfrStatic.main.scheduleOnce(() => {
//                 // console.log('autoDoneTime.guideDone',this.guideData.act);
//                 this.isWaitDone = false;
//                 this.guideDone();
//             }, data.autoDoneTime);
//         }
//         if (data.onShow) {
//             data.onShow();
//         }
//         //点击是否穿透
//         this.clickNode['_touchListener'].setSwallowTouches(data.clickNoThrough);
//         // console.log('穿透',data.clickNoThrough);
//         return false;
//     }

//     /** 再执行一次 */
//     reShow() {
//         if (!this.guideData) {
//             return;
//         }
//         this.guideShow(this.guideData);
//     }

//     showTip(txt: string, pos: cc.Vec2, newName?: string, newPic?: string) {
//         this.guideTip.show(pos, txt);
//         this.guideTip.updatePic(newPic, newName);
//     }

//     showMask(target: cc.Node, pos?: cc.Vec2, autoDoneTime?: number) {
//         this.mask.active = true;
//         if (!target) {
//             // 没有指定位置，显示在屏幕中间
//             this.mask.setPosition(0, 0);
//             this.clickNode.active = false;
//             this.mask.width = 0;
//             this.mask.height = 0;
//         } else {
//             if (!pos) {
//                 const wps = target.convertToWorldSpaceAR(cc.v2());
//                 pos = this.layer_guide.convertToNodeSpaceAR(wps);
//             }
//             this.mask.setPosition(pos);
//             this.mask.width = target.width + 10;
//             this.mask.height = target.height + 10;
//             this.clickNode.width = this.mask.width;
//             this.clickNode.height = this.mask.height;
//             this.clickNode.setPosition(pos);
//             this.clickNode.active = true;
//         }
//         cc.Tween.stopAllByTarget(this.maskBlack);
//         this.maskBlack.opacity = 0;
//         cc.tween(this.maskBlack).to(0.5, { opacity: 170 }).start();
//     }

//     hideFinger() {
//         this.guideFinger.hide();
//     }

//     hideTip() {
//         this.guideTip.hide();
//     }

//     hideMask() {
//         this.mask.active = false;
//         this.clickNode.active = false;
//     }

//     hideAll() {
//         this.layer_guide.active = false;
//         this.hideMask();
//         this.hideTip();
//         this.hideFinger();
//         this.isShowing = false;
//     }




}
