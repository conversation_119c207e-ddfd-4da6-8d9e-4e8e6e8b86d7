
import blhBaseData from "../entity/blhBaseData";
import blhHeroGradeUpData from "../entity/blhHeroGradeUpData";
import blhBaseMgr from "./blhBaseMgr";



export default class blhHeroGradeUpMgr extends blhBaseMgr {

    id: string = 'a10';
    newData(conf: any): blhBaseData {
        const data = new blhHeroGradeUpData(conf);
        return data;
    }

    getAllData(){
        return this.dataMap;
    }

    
    getData(id: number): blhHeroGradeUpData {
        return this.dataMap.get(id) as blhHeroGradeUpData;
    }



}
