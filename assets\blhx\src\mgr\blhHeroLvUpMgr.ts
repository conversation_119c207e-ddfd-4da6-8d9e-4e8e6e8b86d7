
import blhBaseData from "../entity/blhBaseData";
import blhHeroLvUpData from "../entity/blhHeroLvUpData";
import blhBaseMgr from "./blhBaseMgr";



export default class blhHeroLvUpMgr extends blhBaseMgr {

    id: string = 'a9';
    newData(conf: any): blhBaseData {
        const data = new blhHeroLvUpData(conf);
        return data;
    }

    getAllData(){
        return this.dataMap;
    }

    
    getBuildData(id: number): blhHeroLvUpData {
        return this.dataMap.get(id) as blhHeroLvUpData;
    }



}
