
import blhBaseData from "../entity/blhBaseData";
import blhHeroData from "../entity/blhHeroData";
import blhBaseMgr from "./blhBaseMgr";



export default class blhHeroMgr extends blhBaseMgr {


    id: string = 'a5';


    newData(conf: any): blhBaseData {
        const data = new blhHeroData(conf);
        // this.spineMap.set(data.spine, data);
        return data;
    }
    
    getHeroData(id: number): blhHeroData {
        return this.dataMap.get(id) as blhHeroData;
    }



}
