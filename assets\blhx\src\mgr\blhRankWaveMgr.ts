/**
 * 关卡波次管理
 */

import blhRankData from "../entity/blhRankData";
import blhWaveData from "../entity/blhWaveData";
import blhLvExpData from "../entity/blhLvExpData";
import blhBaseMgr from "./blhBaseMgr";
import blhResMgr from "./blhResMgr";

export default class blhRankWaveMgr extends blhBaseMgr {

    id: string = 'a2';
    waveId: string = 'a3';
    expId: string = 'a7';

    rankMap: Map<string, blhRankData> = new Map();
    waveMap: Map<string, Array<blhWaveData>> = new Map();
    /** 每波升级需要经验 */
    expArr: Array<blhLvExpData> = [];


    init(): blhBaseMgr {
        const conf1 = blhResMgr.ins().getConf(this.id);
        if (!conf1) {
            console.error('rank init error', this.id);
            return this;
        }
        for (let i = 0; i < conf1.length; i++) {
            const one = conf1[i];
            this.rankMap.set(one.chapter + '-' + one.part, new blhRankData(one));
        }
        const conf = blhResMgr.ins().getConf(this.waveId);
        if (!conf) {
            console.error('wave init error', this.waveId);
            return this;
        }
        let lastWave: blhWaveData = null;
        let curRank: string = null;
        for (let i = 0, len = conf.length; i < len; i++) {
            const one = new blhWaveData(conf[i]);
            let rankArr = this.waveMap.get(one.rank);
            if (!rankArr) {
                rankArr = new Array<blhWaveData>();
                this.waveMap.set(one.rank, rankArr);
            }
            if (!curRank) {
                curRank = one.rank;
            }
            if (curRank === one.rank) {
                if (!lastWave) {
                    lastWave = one;
                } else {
                    lastWave.nextWave = one;
                    lastWave = one;
                }
            } else {
                lastWave = one;
                curRank = one.rank;
            }
            rankArr.push(one);
        }
        // console.log('waveMap', this.waveMap);

        const conf3 = blhResMgr.ins().getConf(this.expId);
        if (!conf3) {
            console.error('exp init error', this.expId);
            return this;
        }
        for (let i = 0; i < conf3.length; i++) {
            const one = new blhLvExpData(conf3[i]);
            this.expArr.push(one);
        }

        return this;
    }

    getExp(level: number): blhLvExpData {
        let out = this.expArr[level];
        if (!out) {
            return this.expArr[this.expArr.length - 1];
        }
        return out;
    }

    getWaveArr(chapter: number, part: number): Array<blhWaveData> {
        return this.waveMap.get(chapter + '-' + part);
    }

    getRank(chapter: number, part: number): blhRankData {
        return this.rankMap.get(chapter + '-' + part);
    }

}
