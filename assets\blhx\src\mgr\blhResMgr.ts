/**
 * 资源加载和缓存
 */



import { blhChannel } from "../utils/blhChannel";
import blhkc from "../utils/blhkc";
import { Bundles } from "./blhConst";


export default class blhResMgr {

    private static _me: blhResMgr = null;
    public static ins() {
        if (!blhResMgr._me) {
            blhResMgr._me = new blhResMgr();
        }
        return blhResMgr._me;
    }

    private cId: string = blhChannel.verMap[blhChannel.remoteVerKey].id;

    private imgCache: Map<string, cc.SpriteFrame> = new Map<string, cc.SpriteFrame>();
    private prefabCache: Map<string, cc.Prefab> = new Map<string, cc.Prefab>();
    private audioCache: Map<string, cc.AudioClip> = new Map<string, cc.AudioClip>();
    // private fontCache: Map<string, cc.Font> = new Map<string, cc.Font>();
    private spineCache: Map<string, sp.SkeletonData> = new Map<string, sp.SkeletonData>();
    // private altasCacha: Map<string, cc.SpriteAtlas> = new Map<string, cc.SpriteAtlas>();

    // private asyncPrefabCache: Map<string, cc.Prefab> = new Map<string, cc.Prefab>();


    private confCache: Map<string, any> = new Map<string, any>();

    getConf(id: string) {
        return this.confCache.get(id);
    }


    readDC(data: string) {
        const conf: any = JSON.parse(data);
        for (const i in conf) {
            this.confCache.set(i, conf[i][i]);
        }
        blhkc.log('conf loaded:', this.confCache.size);
        blhkc.log('ccv:' + this.getConf('a1')[0].ver);
    }


    private readConf(file): string {
        // @ts-ignore
        const decompressed = fflate.unzipSync(new Uint8Array(file), {
            // 过滤文件名
            filter(file: any) {
                return file.name === 'conf.txt';
            }
        });

        // @ts-ignore
        const str = fflate.strFromU8(decompressed['conf.txt']);
        return str;
    }

    /**
    * 读取配置(先读本地再根据sfrChannel.isConfRemote读远程)，注意需要在loadBundle之后执行
    */
    loadConf(callback: (err: any) => void) {
        const me = this;
        cc.assetManager.loadBundle('conf', (err, bundle) => {
            if (err) {
                blhkc.err(err);
                callback(err);
                return;
            }
            bundle.load('conf', (e, data) => {
                if (e) {
                    blhkc.err(e);
                    callback(e);
                    return;
                }
                //注意！！ios环境下得到的data不正确(没有data._buffer)，这里强制加载为二进制
                cc.assetManager.loadAny({ url: data.nativeUrl, type: 'binary' }, (err, file: Uint8Array) => {
                    if (err) {
                        blhkc.err(err);
                        callback(err);
                        return;
                    }
                    const str = this.readConf(file);
                    me.readDC(str);

                    //是否走远程
                    if (!blhChannel.isConfRemote) {
                        callback(null);
                        return;
                    }
                    blhkc.jget('https://ga.gamejym.com/fcm199/zip/' + me.cId + '.bin', (err: any, res: any) => {
                        if (err) {
                            blhkc.err(err);
                            callback(err);
                            return;
                        }
                        const str = this.readConf(res);
                        me.readDC(str);
                        callback(null);
                    });
                });
            });
        });
    }

    /** loadSection顺序为:['img', 'atlas', 'spine', 'prefab', 'audio'] */
    loadBundleWithProcess(bundleName: string, loadSection = [0.1, 0.1, 0.4, 0.3, 0.1], onProcess?: (process: number) => void, onComplete?: () => void) {
        let loadSec = 0;
        let loadPrepend = 0;
        let lastProgress = 0;
        this.loadBundle(bundleName, (finish: number, total: number) => {
            // sfrkc.log('process:', finish, total, 'loadSec', loadSec, 'loadPrepend', loadPrepend);
            if (finish < 0) {
                // loadSec = 0 - finish - 1;
                loadSec++;
                loadPrepend = lastProgress;
            } else {
                const process = loadSection[loadSec] * (finish / total) + loadPrepend;
                onProcess(process);
                lastProgress = process;
                // sfrkc.log('process', process);
            }
        }, onComplete);
    }
    preloadBundle(bundleName: string) {
        const resArr = ['img', 'atlas', 'spine', 'prefab', 'audio'];
        const typeArr = [cc.SpriteFrame, cc.SpriteAtlas, sp.SkeletonData, cc.Prefab, cc.AudioClip];
        let p = 0;
        function loadOne(bundle: cc.AssetManager.Bundle, resStr: string, type: any, callback: (err: any) => void) {
            if (!resStr) {
                return;
            }
            bundle.preloadDir(resStr, type, () => { }, (err: any, reArr: Array<any>) => {
                loadOne(bundle, resArr.shift(), typeArr.shift(), callback);
            });
        }
        cc.assetManager.loadBundle(bundleName, (err: any, bundle: cc.AssetManager.Bundle) => {
            if (err) {
                console.error(err);
                return;
            }
            const res = resArr.shift();
            loadOne(bundle, res, typeArr.shift(), (err: any) => {
                if (err) {
                    blhkc.err('加载bundle内资源失败', res, err);
                }
            });
        });
    }

    loadBundle(bundleName: string, onProgress?: (finish: number, total: number) => void, onComplete?: () => void) {
        // const resArr = ['img', 'atlas', 'spine', 'prefab', 'audio'];
        const resArr = ['img', 'atlas', 'spine', 'prefab', 'audio'];
        // const typeArr = [cc.SpriteFrame, cc.SpriteAtlas, sp.spine, cc.Prefab, cc.AudioClip];
        // const typeArr = [cc.SpriteFrame, sp.SkeletonData, cc.Prefab, cc.AudioClip];
        const typeArr = [cc.SpriteFrame, cc.SpriteAtlas, sp.SkeletonData, cc.Prefab, cc.AudioClip];
        // const cacheArr = [this.imgCache, this.imgCache, this.spineCache, this.prefabCache, this.audioCache];
        const cacheArr = [this.imgCache, this.imgCache, this.spineCache, this.prefabCache, this.audioCache];
        const me = this;
        let p = 0;
        function loadOne(bundle: cc.AssetManager.Bundle, resStr: string, type: any, cache: Map<string, any>, callback: (err: any) => void) {
            if (!resStr) {
                onComplete();
                return;
            }
            bundle.loadDir(resStr, type, onProgress, (err: any, reArr: Array<any>) => {
                if (err) {
                    return callback(err);
                }
                if (resStr === 'atlas') { //图集处理到img缓存
                    for (let i = 0; i < reArr.length; i++) {
                        const one = reArr[i];
                        const spfArr: cc.SpriteFrame[] = one.getSpriteFrames();
                        for (let j = 0; j < spfArr.length; j++) {
                            const spOne = spfArr[j];
                            cache.set('img/' + spOne.name, spOne);
                            // sfrkc.log('set img cache:',spOne.name);
                        }
                    }
                } else {
                    for (let i = 0; i < reArr.length; i++) {
                        const one = reArr[i];
                        cache.set(resStr + '/' + one.name, one);
                        // sfrkc.log('set cache:',resStr + '/' + one.name);
                    }
                }
                p--;
                onProgress(p, 100);
                // console.log('加载完成', resStr, reArr.length);
                loadOne(bundle, resArr.shift(), typeArr.shift(), cacheArr.shift(), callback);
            });
        }


        cc.assetManager.loadBundle(bundleName, (err: any, bundle: cc.AssetManager.Bundle) => {
            if (err) {
                console.error(err);
                return;
            }
            const res = resArr.shift();
            loadOne(bundle, res, typeArr.shift(), cacheArr.shift(), (err: any) => {
                if (err) {
                    blhkc.err('加载bundle内资源失败2', bundleName, res, err);
                }
            });
        });

    }


    loadResMap(resMap: Map<string, Array<string>>, onProgress: (finish: number, total: number) => void, callback: (err: any) => void, isPreload: boolean = false) {
        const typeMap = {
            'img': { type: cc.SpriteFrame, cache: this.imgCache },
            // 'atlas': { type: cc.SpriteAtlas, cache: this.imgCache }, //暂不支持,由prefab加载
            'spine': { type: sp.SkeletonData, cache: this.spineCache },
            'prefab': { type: cc.Prefab, cache: this.prefabCache },
            'audio': { type: cc.AudioClip, cache: this.audioCache },
        }
        let total = 0;
        let loaded = 0;
        resMap.forEach((value, key) => {
            total += value.length;
        });

        function updateProcess() {
            if (isPreload) {
                return;
            }
            loaded++;
            if (loaded >= total) {
                loaded = total;
                callback(null);
            }
            onProgress(loaded, total);
        }

        cc.assetManager.loadBundle(Bundles.async, (err, bundle) => {
            if (err) {
                console.error(err);
                return;
            }
            const loadFn = (isPreload) ? 'preload' : 'load';
            resMap.forEach((value, type) => {
                // sfrkc.log('load res:', key, value);s
                const obj = typeMap[type];
                if (!obj) {
                    console.error('未知加载类型', type);
                    return;
                }
                for (let i = 0; i < value.length; i++) {
                    const resPath = value[i];
                    const key = type + '/' + resPath;
                    //@ts-ignore
                    bundle[loadFn](key, obj.type, (err, data: any) => {
                        if (err) {
                            console.error(err);
                            updateProcess();
                            return;
                        }
                        if (isPreload) { //预加载不进缓存
                            return;
                        }
                        obj.cache.set(key, data);
                        updateProcess();
                    });
                }
            });
        });

    }

    getAudio(resPath: string): cc.AudioClip {
        return this.audioCache.get('audio/' + resPath);
    }

    getImg(resPath: string): cc.SpriteFrame {
        return this.imgCache.get('img/' + resPath);
    }

    hasPrefab(resPath: string): boolean {
        return this.prefabCache.has('prefab/' + resPath);
    }

    getPrefab(resPath: string): cc.Prefab {
        if (this.prefabCache.get('prefab/' + resPath)) {
            return this.prefabCache.get('prefab/' + resPath);
        } else {
            blhkc.err("prefab not find: ", resPath);
            return null;
        }
    }

    getNode(resPath: string): cc.Node {
        return cc.instantiate(this.getPrefab(resPath));
    }

    // getFont(resPath: string): cc.Font {
    //     return this.fontCache.get('font/' + resPath);
    // }

    // getSpine(resPath: string): sp.SkeletonData {
    getSpine(resPath: string): sp.SkeletonData {
        return this.spineCache.get('spine/' + resPath);
    }

    /** 异步按类型从指定bundle中加载资源 */
    getResAsync(bundleName: string, resPath: string, type: any, callback: (err: any, data: any) => void): void {
        if (type === cc.Prefab && this.prefabCache.has(resPath)) {
            callback && callback(null, this.prefabCache.get(resPath));
            return;
        }
        cc.assetManager.loadBundle(bundleName, (err, bundle) => {
            if (err) {
                callback(err, null);
                return;
            }
            bundle.load(resPath, type, (err, data: any) => {
                if (err) {
                    callback(err, null);
                    return;
                }
                if (type === cc.Prefab) {
                    this.prefabCache.set(resPath, data);
                }
                callback(null, data);
            });
        });
    }

    // /**异步加载关卡相关资源 */
    // getBattleResAsync(bundleName: string, resources: pzaResourceDetail[], finishCall: (succ: boolean) => void, onProgress: (proValue: number) => void = null) {
    //     if (resources == null || resources.length <= 0) {
    //         finishCall && finishCall(true);
    //         return;
    //     }
    //     let resourceCount = resources.length;
    //     let loadCount = 0;
    //     const typeArr = [cc.Prefab];
    //     resources.forEach((resource) => {
    //         this.getResAsync(bundleName, resource.url, typeArr[resource.resourceType], (err, data) => {
    //             if (err) {
    //                 console.error("加载bundle出错: ", resource.url);
    //             }
    //             loadCount++;
    //             onProgress && onProgress(loadCount / resourceCount);
    //             if (loadCount >= resourceCount) {
    //                 finishCall && finishCall(true);
    //             }
    //         });
    //     });
    // }


    getAudioAsync(bundleName: string, resPath: string, callback: (err: any, data: cc.AudioClip) => void) {
        this.getResAsync(bundleName, 'audio/' + resPath, cc.AudioClip, callback);
    }

    getImgAsync(bundleName: string, resPath: string, callback: (err: any, data: cc.SpriteFrame) => void) {
        this.getResAsync(bundleName, 'img/' + resPath, cc.SpriteFrame, callback);
    }


    getPrefabAsync(bundleName: string, resPath: string, callback: (err: any, data: cc.Prefab) => void) {
        this.getResAsync(bundleName, 'prefab/' + resPath, cc.Prefab, callback);
    }

    getNodeAsync(resPath: string, callback: (err: any, data: cc.Node) => void) {
        this.getResAsync(Bundles.async, 'prefab/' + resPath, cc.Prefab, (err, data) => {
            callback(err, cc.instantiate(data));
        });
    }

    getSpineAsync(bundleName: string, resPath: string, callback: (err: any, data: sp.SkeletonData) => void) {
        this.getResAsync(bundleName, 'spine/' + resPath, sp.SkeletonData, callback);
    }

    createPicNode(picName: string): cc.Node {
        const spf = this.getImg(picName);
        if (!spf) {
            blhkc.err('创建picNode失败:' + picName);
            return;
        }
        const node: cc.Node = new cc.Node('pic');
        node.addComponent(cc.Sprite).spriteFrame = spf;
        return node;
    }

    createSpineNode(spineName: string): sp.Skeleton {
        const spine = this.getSpine(spineName);
        if (!spine) {
            blhkc.err('创建spineNode失败:' + spineName);
            return;
        }
        const node: cc.Node = new cc.Node('spine');
        const out = node.addComponent(sp.Skeleton);
        out.skeletonData = spine;
        return out;
    }

    createSpineNodeAsync(spineName: string, callback: (err: any, node: cc.Node) => void): void {
        const cached = this.getSpine(spineName);
        if (cached) {
            const node: cc.Node = new cc.Node('spine');
            node.addComponent(sp.Skeleton).skeletonData = cached;
            return callback(null, node);
        }
        this.getSpineAsync(Bundles.async, spineName, (err: any, spine: sp.SkeletonData) => {
            if (err) {
                return callback(err, null);
            }
            if (!spine) {
                blhkc.err('创建spineNode失败:' + spineName);
                return callback(new Error('createSpineNodeAsync失败:' + spineName), null);
            }
            this.spineCache.set(spineName, spine);
            const node: cc.Node = new cc.Node('spine');
            node.addComponent(sp.Skeleton).skeletonData = spine;
            return callback(null, node);
        });
    }

}
