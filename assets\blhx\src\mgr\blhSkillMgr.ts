
import blhSkillData from "../entity/blhSkillData";
import blhHero from "../hero/blhHero";
import blhkc from "../utils/blhkc";
import blhC3 from "../view/blhC3";
import blhBaseMgr from "./blhBaseMgr";
import blhResMgr from "./blhResMgr";
import { blhStatic } from "./blhStatic";



export default class blhSkillMgr extends blhBaseMgr {

    private static _me: blhSkillMgr = null;
    public static ins() {
        if (!blhSkillMgr._me) {
            blhSkillMgr._me = new blhSkillMgr();
        }
        return blhSkillMgr._me;
    }

    id: string = 'a6';

    /** 3选1map,按sid归成数组 */
    c3_1_map: Map<number, Array<blhSkillData>> = new Map();

    getSkill(id: number): blhSkillData {
        return this.dataMap.get(id) as blhSkillData;
    }

    init(): blhBaseMgr {
        const conf = blhResMgr.ins().getConf(this.id);
        if (!conf) {
            console.error('Mgr init error', this.id);
            return this;
        }
        for (let i = 0; i < conf.length; i++) {
            const one = new blhSkillData(conf[i]);
            if (one.c3) {
                //3选1技能
                let arr: Array<blhSkillData> = this.c3_1_map.get(one.sid);
                if (!arr) {
                    arr = [];
                    this.c3_1_map.set(one.sid, arr);
                }
                arr.push(one);
            }
            this.dataMap.set(one.id, one);
        }
        return this;
    }

    /**
     * 按英雄当前状态获取可升级的技能
     * @param hero 局内英雄
     */
    getUpSkillByHero(hero: blhHero, outArr: Array<blhSkillData>, weightSum: number): Array<blhSkillData> {
        const sidArr = hero.data.skillArr;
        for (let j = 0; j < sidArr.length; j++) {
            const sid = sidArr[j];
            const arr: Array<blhSkillData> = this.c3_1_map.get(sid);
            if (!arr) {
                console.log('此主技能不存在', sid);
                continue;
            }
            for (let i = 0; i < arr.length; i++) {
                const one = arr[i];
                if (!one.c3) {
                    continue;
                }
                //技能等级要求
                if (one.c3lv > hero.skillLv) {
                    continue;
                }
                //前置技能要求
                if (one.preSkill && hero.skillIdArr.indexOf(one.preSkill) === -1) {
                    continue;
                }
                //随机次数,为1时只能出1次
                if (one.rand && hero.skillIdArr.indexOf(one.id) > -1) {
                    continue;
                }
                //激活条件,星级star，阶级grade
                if (one.activeMap) {
                    let isActive = true;
                    one.activeMap.forEach((val, key) => {
                        if (hero[key] !== undefined && hero[key] < val) {
                            isActive = false;
                            return;
                        }
                    });
                    if (!isActive) {
                        continue;
                    }
                }
                outArr.push(one.setHero(hero.data.id));
                weightSum += one.weight;
            }
        }
        return outArr;
    }

    /**
     * 显示3选1的选项集合
     * @param num 需要选择的数量
     * 
     * 
     */
    getC3ItemsData(num: number): Array<blhSkillData> {
        const heroArr: blhHero[] = blhStatic.battle.heroArr;
        const selectPool: Array<blhSkillData> = [];
        let out: Array<blhSkillData> = [];
        let weightSum = 0;
        //先按战斗中的当前所有英雄选择技能组
        for (let i = 0; i < heroArr.length; i++) {
            const hero = heroArr[i];
            this.getUpSkillByHero(hero, selectPool, weightSum);
        }
    
        out = blhkc.drawByWeight(selectPool, num, weightSum) as Array<blhSkillData>;
        return out;
    }

    /**
     * 显示3选1
     * @param num 选项数量
     */
    showC3(num: number) {
        blhC3.create(blhStatic.battle.layer_pop, num);
    }


    setC3Skill(skill: blhSkillData) {
        const heroArr = blhStatic.battle.heroArr;
        for (let i = 0; i < heroArr.length; i++) {
            const hero = heroArr[i];
            if (skill.heroId === hero.data.id) {
                hero.setSkill(skill.id);
                return;
            }
        }
    }

}
