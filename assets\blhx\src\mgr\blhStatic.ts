// import sfrCollitionMgr from "./sfrCollisionMgr";
import blhUserData from "../items/blhUserData";
import blhAudioMgr from "./blhAudioMgr";
import blhkc from "../utils/blhkc";
import blhEffMgr from "./blhEffMgr";
// import sfrGuideMgr from "./sfrGuideMgr";
import blhMain from "../view/blhMain";
import blhBattle from "../view/blhBattle";
import blhMainUI from "../view/blhMainUI";
import blhRankWaveMgr from "./blhRankWaveMgr";
import { BattleState } from "./blhConst";
import blhHeroMgr from "./blhHeroMgr";
import blhCommonData from "../items/blhCommonData";
import blhBuildMgr from "./blhBuildMgr";
import blhDeBuffMgr from "./blhDeBuffMgr";
import blhHeroLvUpMgr from "./blhHeroLvUpMgr";
import blhHeroGradeUpMgr from "./blhHeroGradeUpMgr";

export class blhStatic {



    static main: blhMain = null;

    static battle: blhBattle = null;
    static mainUI: blhMainUI = null;
    // static battleNormal: sfrBattleNormal = null;
    // static battleLLK: sfrBattleLLK = null;

    /** 屏幕宽度 */
    static viewWidth: number = 0;
    /** 半个屏幕宽度 */
    static halfViewWidth: number = 0;
    /** 半个屏幕高度 */
    static halfViewHeight: number = 0;

    /** 创建敌人间隔,from */
    static createEnemyGapFrom: number = 0;
    /** 创建敌人间隔,to */
    static createEnemyGapTo: number = 1;


    // /**是不是同一天 */
    // static isSameDay: boolean = false;
    // static resMgr: sfrResMgr = null;

    static audioMgr: blhAudioMgr = null;
    static rankMgr: blhRankWaveMgr = null;
    static effMgr: blhEffMgr = null;
    // static enemyMgr: blhEnemyMgr = null;
    static heroMgr: blhHeroMgr = null;
    static debuffMgr:blhDeBuffMgr = null;
    // static skillMgr: blhSkillMgr = null;
    static buildMgr: blhBuildMgr = null;
    //英雄升级控制器
    static heroLvUpMgr: blhHeroLvUpMgr = null;
    //英雄升阶控制器
    static heroGradeUpMgr: blhHeroGradeUpMgr = null;
    
    static battleState: BattleState = BattleState.stop;

    //各种颜色

    static selectColor: cc.Color = new cc.Color().fromHEX('#FFF48E');
    static hintColor: cc.Color = new cc.Color().fromHEX('#9EFF8E');
    static hpRedColor: cc.Color = new cc.Color().fromHEX('#CB0202');
    static clockRedColor: cc.Color = new cc.Color().fromHEX('#FF3E3E');
    static canNotColor: cc.Color = cc.Color.RED;


    static userData: blhUserData = new blhUserData();
    //通用数据
    static commonData: blhCommonData = new blhCommonData();

    static gameSpeed: number = 1;

    /** 我方防线y坐标,小于此值时怪物开始攻击 */
    static enemy_atk_y: number = 1;

    // //loading强弹广告数量
    // static forceAdCnt: number = 0;
    // //游戏内是否开启了强弹视频
    // static openForceAd: boolean = false;
    // //游戏内强弹视频秒数
    // static froceAdInGameTimes: number = 0;
    // //华为切onHide是否叠插屏
    // static hwShowCustomAd: boolean = false;
    // //华为loading强弹视频
    // static hwForceAd: boolean = false;
    // //华为loading强弹比例
    // static hwForceRates: number[] = [];



    static init(main: blhMain) {
        blhStatic.main = main;
        //本地数据初始化
        blhStatic.commonData.init();
        // sfrStatic.resMgr = sfrResMgr.instance();
        // sfrStatic.resMgr.getPrefabAsync(Bundles.async, Prefabs.block, (err: any, data: cc.Prefab) => {
        //     if (err) {
        //         sfrkc.err(err);
        //         return;
        //     }
        //     sfrStatic.block = cc.instantiate(data);
        // });
        // sfrStatic.block = sfrResMgr.instance().getNode(Prefabs.block);
        // sfrStatic.mainUI = sfrResMgr.instance().getNode(Prefabs.mainUI).getComponent(sfrMainUI);
        // sfrStatic.guideMgr = new sfrGuideMgr().init(main.layer_guide, main.guide_click, main.guide_mask);

        if (!blhStatic.rankMgr) {
            // blhStatic.enemyMgr = new blhEnemyMgr().init() as blhEnemyMgr;
            blhStatic.rankMgr = new blhRankWaveMgr().init() as blhRankWaveMgr;
            blhStatic.heroMgr = new blhHeroMgr().init() as blhHeroMgr;
            blhStatic.buildMgr = new blhBuildMgr().init() as blhBuildMgr;
            blhStatic.heroLvUpMgr = new blhHeroLvUpMgr().init() as blhHeroLvUpMgr;
            blhStatic.heroGradeUpMgr = new blhHeroGradeUpMgr().init() as blhHeroGradeUpMgr;
            // blhStatic.skillMgr = new blhSkillMgr().init() as blhSkillMgr;

            blhStatic.effMgr = new blhEffMgr();
            blhStatic.debuffMgr = new blhDeBuffMgr();
            blhStatic.audioMgr = blhAudioMgr.instance().init();
        }


        //用户数据初始化
        blhStatic.userData.init();
        // sfrStatic.isSameDay = sfrkc.isSameDay(Date.now(), sfrkc.getData(saveData.regTime, null));
        // if (!sfrStatic.isSameDay) {
        //     sfrStatic.userData.resetData();
        // }
        if (!blhkc.getData('user', null)) {
            blhStatic.userData.resetData();
        }
        blhStatic.userData.save();


        blhStatic.initView();
        cc.view.setResizeCallback(blhStatic.initView); //改变屏幕大小时更新viewHalfWidth

        if(blhStatic.commonData.roleData.length <= 0){
            console.error("角色数据为空 需初始化");
            blhStatic.commonData.initRoleData();
        }

    }




    static initView() {
        const viewSize = cc.view.getVisibleSize();
        blhStatic.viewWidth = viewSize.width;
        blhStatic.halfViewWidth = viewSize.width / 2;
        blhStatic.halfViewHeight = viewSize.height / 2;
        if (viewSize.width > viewSize.height) {
            cc.Canvas.instance.fitHeight = true;
            cc.Canvas.instance.fitWidth = false;
            console.log('横屏....');
        }
    }

}