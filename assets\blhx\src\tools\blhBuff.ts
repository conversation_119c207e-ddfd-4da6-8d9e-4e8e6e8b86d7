/**
 * buff对象
 * 
 */

export default class blhBuff {

    //------定制属性--------

    // /** 武器id */
    // weaponId: number = 0;
    // /** 装备等级 */
    // equipLv: number = 0;
    /** 稀有度 */
    rarity: number = 0;
    // /** 前置buff的id */
    // needBuff: number = 0;
    // /** 影响相邻武器的类型 */
    // nearType: number = 0;
    /** 用于标记类型,供mgr使用 */
    type: string = null;
    /** 图标名 */
    icon: string = null;
    // /** 唯一性,刷新buff时同此值buff只会出一个 */
    // unique: string = null;
    /** 是否局内buff */
    isInBattle: boolean = false;
    /** 为true时不进存档 */
    notSave: boolean = true;

    //---------------------

    id: number = 0;
    // seq: string = ''; //每个buff唯一的序列号

    /** buff影响的属性,目前为单个 */
    prop: string = null;
    name: string = null;
    desc: string = null;


    /** buff的等级,等级提升由buff管理器读表实现实现相关数值的变化 */
    lv: number = 0;

    /** buff数值,这里是绝对值 */
    val: number = 0;
    /** 值的类型,如绝对值,相对值,与BuffComputeType对应,每个类型对应一种计算方法,可包含自定义计算方法 */
    computeType: number = 0;
    /** 优先级,prop相同时,优先级越大越先计算,此值相同时按buff添加顺序计算 */
    // priority: number = 0;
    /** 有效时间,单位毫秒,为-1表示一直有效 */
    // effectTime: number = -1;
    /** 失效时间点,单位毫秒时间戳，为-1表示不会失效 */
    // expireTimePoint: number = -1;

    /** 当prop和computeType相同时,mergeType决定如何合并，比如取最大值,叠加值等,0为不可能合并 */
    // mergeType: number = 0;
    /** 是否取整 */
    // isInt: boolean = false;

    /** 是否为负数 */
    isNeg: boolean = false;
    // type: string = null;
    /** 是否立即执行影响属性值，执行后将不再计算 */
    isExeOnce: boolean = false;
    /** 同id的buff加入时，是否按升级处理 */
    canUpgrade: boolean = false;
    /** 是否有效,某些buff执行后会失效,仅作为记录,如tank上挂的nearType有值的buff，需要在计算时忽略 */
    isValid: boolean = true;

    constructor(conf: any) {
        this.id = conf.id;
        this.name = conf.name;
        this.prop = conf.prop;
        this.val = conf.val || 0;
        this.desc = conf.desc || '';
        // this.effectTime = conf.effectTime || -1;
        this.isNeg = !!conf.isNeg;
        // this.priority = conf.priority || 0;
        this.computeType = conf.computeType || 0;
        // this.mergeType = conf.mergeType || 0;
        this.lv = conf.level || 0;
        this.type = conf.type;
        // this.expireTimePoint = conf.expireTimePoint || -1;
        this.isValid = conf.isValid || true;
        this.rarity = conf.rarity || 0;
        // this.weaponId = conf.weaponId || 0;
        this.isExeOnce = !!conf.isExeOnce;
        // this.isExeOnce = conf.isExeOnce || false;
        this.canUpgrade = conf.canUpgrade || false;
        // this.equipLv = conf.equipLv || 0;
        // if (this.equipLv) {
        //     this.icon = 'skillId' + this.weaponId + '_' + this.equipLv;
        // } else {
        // }
        this.icon = (conf.icon) ? conf.icon.trim() : 'buff_' + this.prop;
        // this.unique = conf.unique;
        // this.needBuff = conf.needBuff || 0;
        // this.nearType = conf.nearType || 0;
        this.notSave = !!conf.notSave;
        // this.isInt = !!conf.isInt;
    }

    clone() {
        return new blhBuff(this);
    }

    // /** 需要根据实际情况实现具体逻辑,发送消息等 */
    // upgrade() {
    //     this.lv++;
    // }
}