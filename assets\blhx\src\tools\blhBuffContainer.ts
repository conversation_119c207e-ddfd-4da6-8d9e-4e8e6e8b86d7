/**
 * buff容器,继承组件挂到具体的node节点
 * 
 */

import blhBuff from "./blhBuff";


/**
 * buff计算类型
 */
export const enum BuffComputeType {
    /** 不计算 */
    none = 0,
    /** 值相加 */
    valAdd = 1,
    /** 百分比相加 */
    percentAdd = 2,
    /** 小数乘后相加 */
    floatAdd = 3,
    /** 直接覆盖 */
    cover = 4,
    /** 取最大值 */
    max = 5,
    /** 取最小值 */
    min = 6,
    /** 直接乘当前值 */
    multi = 7,
    /** 背包里每有一个装备，伤害+n% */
    equipCntAtk = 8,


}
/**
 * buff合并类型,当prop和valType相同时,mergeType决定如何合并 ---本游戏不需要
 */
export const enum BuffMergeType {
    /** 值相加 */
    valAdd = 0,
    /** 百分比相关 */
    percentAdd = 1,
    /** 直接覆盖 */
    cover = 2,
    /** 取最大值 */
    max = 3,
    /** 取最小值 */
    min = 4,

}

/**
 * 可持有buff对象的接口
 */
export interface blhBuffOwner {

    /** 获取buff对象的对应属性基础值 */
    getBaseVal(prop: string): number;

    /** 在buff值变化时调用,包含0新增，1删除，2升级等 */
    onChange(buff: blhBuff, changeType: number): void;

    getContainer(): crtBuffContainer;

    getOwnerId(): number;
    /** 类型，如tank,武器,enemy等 */
    getType(): number;
    /** 用于挂载schedule相关的处理,一般使用owner自身组件 */
    getComp(): cc.Component;

}

export default class crtBuffContainer {

    //按prop归集的buff
    buffMap: Map<string, Array<blhBuff>> = new Map();


    /** 具有所有buff原始基础值的对象 */
    owner: blhBuffOwner = null;


    constructor(owner: blhBuffOwner) {
        this.owner = owner;
    }

    /**
     * 新增或升级buff
     * @param upgradeItem 
     * @returns 
     */
    addBuff(newBuff: blhBuff): crtBuffContainer {
        const arr = this.buffMap.get(newBuff.prop);
        if (arr) {
            if (newBuff.computeType === BuffComputeType.cover) {
                //覆盖需要清除之前的同类buff
                for (let i = 0; i < arr.length; i++) {
                    if (arr[i].id === newBuff.id) {
                        arr.splice(i, 1);
                        break; //因为cover只可能存在1个,所以这里直接break
                    }
                }
            }
            // 本游戏无buff升级
            // else if (newBuff.canUpgrade) {
            //     //升级buff的level
            //     for (let i = 0; i < arr.length; i++) {
            //         const one = arr[i];
            //         if (one.id === newBuff.id) {
            //             one.upgrade();
            //             return this; //只可能存在1个,这里直接return 
            //         }
            //     }
            // }

            arr.push(newBuff);
        } else {
            this.buffMap.set(newBuff.prop, []);
            this.buffMap.get(newBuff.prop).push(newBuff);
        }

        return this;
    }

    /** 用于存档,返回两个数组,第一个是所有buff的id,第二个是inBattle的buff的id */
    toJsonArr() {
        const out = [];
        const inBattle = [];
        this.buffMap.forEach((value, key) => {
            for (let i = 0; i < value.length; i++) {
                if (value[i].notSave) {
                    continue;
                }
                out.push(value[i].id);
                if (value[i].isInBattle) {
                    inBattle.push(value[i].id);
                }
            }
        });
        return [out, inBattle];
    }

    hasBuff(prop: string): boolean {
        const arr = this.buffMap.get(prop);
        return arr && arr.length > 0;
    }

    getBuff(prop: string): Array<blhBuff> {
        return this.buffMap.get(prop);
    }

    /**
     * 直接删除buff,无论等级多少,可用于buff时间到后自动失效
     * @param buff 
     * @returns 
     */
    removeBuff(buff: blhBuff): crtBuffContainer {
        const bArr: Array<blhBuff> = this.buffMap.get(buff.prop);
        if (!bArr) {
            return this;
        }
        for (let i = 0; i < bArr.length; i++) {
            if (bArr[i].id === buff.id) {
                this.buffMap.get(buff.prop).splice(i, 1);
                break;
            }
        }
        return this;
    }

    // getVal(prop: string): number {

    //     return 0;
    // }

    // computeBuff(prop: string): crtBuffContainer {

    //     return this;
    // }

    // mergeBuff(prop: string, newBuff: crtBuff): crtBuffContainer {

    //     return this;
    // }


    clear(): crtBuffContainer {
        this.buffMap.clear();
        return this;
    }

}