/**
 * 通用发射器对应的子弹
 * 
 */

import { DamageType, DType, Tag } from "../mgr/blhConst";
import blhShooter from "./blhShooter";


const { ccclass, property } = cc._decorator;
@ccclass
export default class blhBullet extends cc.Component {


    @property(cc.BoxCollider)
    collider: cc.BoxCollider = null;

    /** 子弹特殊配置,如onHit后分裂次数,分裂角度等 */
    spConf: any = null;

    /** 伤害类型 */
    dmgType: DamageType = DamageType.normal;

    /** 物理/元素伤害 */
    dType: DType = DType.phisic;

    inUse = false;

    bulletWidth: number = 50;
    bulletHeight: number = 50;

    // /** 暴击概率 ----由发射器owner配置 */
    // critChance: number = 0;
    /** 子弹原始角度 */
    orgAngle: number = 0;

    /** 子弹伤害 */
    dmg: number = 0;

    /** 命中次数 */
    hitCount: number = 0;
    /** 最大命中次数，若为负值则可无限命中(无限穿透) */
    maxHitCount: number = 0;

    /** 伤害间隔,单位秒,为0表示无持续伤害 */
    hurtCD: number = 0;

    /** 子弹击中特效 */
    onHitEffId: number = 0;

    /** 是否是最后一个，以便在cd后进行下次shoot */
    isLast: boolean = false;

    isRemoved: boolean = false;

    shooter: blhShooter = null;

    tag: number = Tag.bulletA;

    canAttack: boolean = true;

    init(shooter: blhShooter, tag: number, onInit: () => void = null): blhBullet {
        this.shooter = shooter;
        this.tag = tag;
        // this.critChance = 0;
        if (this.collider) {
            this.collider.tag = tag;
        }
        if (this.shooter.bulletSpConf) {
            this.spConf = this.shooter.bulletSpConf;
        }
        if (this.shooter.bulletConf) {
            this.updateConf(this.shooter.bulletConf);
        }
        if (onInit) {
            onInit.call(this);
        }
        this.reuse();
        return this;
    }

    onShoot(startPo: cc.Vec2, targetPo: cc.Vec2) {

    }

    /** 更新参数 */
    updateConf(conf: any) {
        for (const key in conf) {
            if (undefined !== conf[key]) {
                this[key] = conf[key];
            }
        }
        if (this.collider && (conf.bulletWidth || conf.bulletHeight)) {
            this.collider.size.width = this.bulletWidth;
            this.collider.size.height = this.bulletHeight;
        }
    }

    // protected onLoad(): void {
    // this.collider = this.node.getComponent(cc.Collider);
    // this.collider.tag = this.tag;
    // }

    // /** 通过方法配置onHit,减少extends */
    // setOnHit(onHit: (other: cc.Collider, self: cc.Collider) => void): blhBullet {
    //     this.onHit = onHit;
    //     return this;
    // }

    // /** 通过方法配置remove,实现子弹回池等 */
    // setRemove(onRemove: () => void): blhBullet {
    //     this.remove = onRemove;
    //     return this;
    // }

    setAnglePosScale(angle: number, pos: cc.Vec2, scale: number = 1) {
        this.orgAngle = angle;
        this.node.angle = angle;
        this.node.setPosition(pos);
        this.node.scale = scale;
    }

    /** 实现具体的碰撞逻辑,伤害计算,受击效果等 */
    onHit(other: cc.Collider, self: cc.Collider) {
        // blhkc.log('onHit', other.uuid, self.uuid);
    }


    unuse() {
        this.inUse = false;
        this.hitCount = 0;
        this.isRemoved = false;
    }

    reuse() {
        this.inUse = true;
        this.hitCount = 0;
        this.canAttack = true;
        // if (this.shooter.itemSize) {
        //     this.node.scale = this.shooter.itemSize;
        // }
    }

    pause(): void {
        this.inUse = false;
        if (this.node) {
            this.node.pauseAllActions();
        }
        cc.director.getScheduler().pauseTarget(this);
    }

    resume(): void {
        this.inUse = true;
        if (this.node) {
            this.node.resumeAllActions();
        }
        cc.director.getScheduler().resumeTarget(this);
    }



    /** 子弹穿透计数,在onHit中可选调用,返回false表示穿透已结束 */
    canHitThrough(): boolean {
        if (this.maxHitCount === 0) {
            //无穿透效果
            return false;
        }
        if (this.hurtCD) {
            const me = this;
            me.canAttack = false;
            // console.log('hurtCD', me.hurtCD);
            me.scheduleOnce(() => {
                if (me.node && me.node.isValid) {
                    me.canAttack = true;
                }
            }, me.hurtCD);
        }
        if (this.maxHitCount < 0) {
            //无限穿透
            return true;
        }
        this.hitCount++;
        if (this.hitCount >= this.maxHitCount) {
            return false;
        }

        return true;
    }


    onCollisionEnter(other: cc.Collider, self: cc.Collider) {
        if (!this.inUse || !this.canAttack) {
            return;
        }
        this.onHit(other, self);
    }

    getCollider(): cc.Collider {
        return this.collider;
    }

    /** 仅单纯删除子弹!完整移除要调用shooter.removeItem. 此方法来实现destroy或回池 */
    remove() {
        this.isRemoved = true;
        if (this.node) {
            this.node.destroy();
        }
    }

}