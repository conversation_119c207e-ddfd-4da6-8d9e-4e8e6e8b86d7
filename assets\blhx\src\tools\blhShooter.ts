/**
 * 通用发射器,可根据不同参数发射子弹. 仅实现子弹发射,瞄准器,受击等由外部实现.
 * 
 */
import blhBullet from "./blhBullet";


const { ccclass, property } = cc._decorator;
@ccclass
export default class blhShooter extends cc.Component {

    /** 是否在用,为false时不会发射子弹,aimer也不会动 */
    inUse = false;

    // aimer: cc.Node = null;
    /** 子弹速度 */
    bltSpd: number = 1;

    /** 发射一轮后的cd, 单位秒, 大于0的cd表示每轮发射发起时间之间的间隔, 小于0的cd由最后一发子弹消失时确定下次发射时间 */
    cd: number = 2;
    /** 射击位置偏移x */
    fireX: number = 0;
    /** 射击位置偏移y */
    fireY: number = 0;

    shootRange: number = 1000;

    /** 子弹发射延时，用于在发射动画播到一定时间后开始发射, 单位秒 */
    bulletDelay: number = 0;
    /** 子弹普通参数配置,用于更新子弹参数 */
    bulletConf: any = null;
    /** 子弹特殊配置,如onHit后分裂次数,分裂角度等 */
    bulletSpConf: any = null;

    /** 每次发射多少子弹 */
    numsPerShoot: number = 1;
    /** 每轮射击中各发子弹之间的cd */
    itemCD: number = 0.15;
    /** 无目标时cd */
    noTargetCD: number = 0.5;

    // /** item大小,用于子弹从池中拿到时重置大小 */
    // itemSize: number = 1;
    // /** 范围伤害大小 */
    // boomSize: number = 0;

    /** 首次CD时间，用于敌人多种武器组合时使用(CD相同,firstCD不同达到交替发射效果) */
    firstCD: number = 0;

    /** 可否被其他子弹阻挡,主角和敌人的此字段均为true时，可实现阻挡, 由外部逻辑实现 */
    bulletCanStop: boolean = false;

    // isFindingTarget: boolean = false;

    /** 武器所属node，决定了子弹的位置 */
    owner: cc.Node = null;

    target: cc.Node = null;

    // /** 是否每个子弹发射时都检测target */
    // isCheckTargetPerBullet: boolean = false;

    /** 子弹map,key为子弹node的uuid */
    curBulletMap: Map<string, blhBullet> = new Map<string, blhBullet>();

    // delayshoot: number = 0;
    // hasAimer(): boolean {
    //     return !!this.aimer;
    // }

    setOwner(owner: cc.Node): blhShooter {
        this.owner = owner;
        return this;
    }

    initConf(conf: any): blhShooter {
        for (const key in conf) {
            if (undefined !== this[key]) {
                this[key] = conf[key];
            }
        }
        // if(conf.delayshoot !== undefined){
        //     this.delayshoot = conf.delayshoot;
        // }
        return this;
    }

    /** 
     * 这里需要重写或直接设置,功能上需要完成子弹位置的初始化,子弹创建后需要设置其onHit函数
     * @param firePo 发射点
     * @param targetPo 发射点
     * @param i 表示第几个子弹
     * @param isLast 是否为最后一个子弹
     * @param plus 附加的参数
     */
    createBullet(firePo: cc.Vec2, targetPo: cc.Vec2, i: number, isLast: boolean, plus: any): blhBullet {
        return new blhBullet().init(this, null);
    }

    /** 在每轮子弹发射时执行,用于特效或瞄准器调整等,返回false时会阻止发射. 每单次发射的效果由createBullet方法内部实现 */
    onShoot(): boolean {
        return !!this.target;
    }

    /** 索敌器,需要外部实现 */
    findTarget(): cc.Node {
        return null;
    }

    /** 如果每个子弹发射时都检测target或重新findTarget，则在此执行对this.target的检查 */
    checkTarget() {
        if (!this.target || !this.target.isValid) {
            this.target = this.findTarget();
        }
    }
    /** 子弹发射位置  */
    getFirePos(): cc.Vec2 {
        return cc.v2(this.owner.x + this.fireX, this.owner.y + this.fireY);
    }

    /** 按angle角度方向距离dist得到目标位置 */
    angleDistToTarget(startPos: cc.Vec2, angle: number, dist: number): cc.Vec2 {
        const rad = cc.misc.degreesToRadians(angle);
        const dx = dist * Math.cos(rad);
        const dy = dist * Math.sin(rad);
        return cc.v2(startPos.x + dx, startPos.y + dy);
    }

    /** 获取目标位置，可能需要重写或设置,plus用于临时存放发射需要的参数,如角度等 */
    getTargetPo(firePo: cc.Vec2, target: cc.Node, i: number, plus: any): cc.Vec2 {
        if (!target || !target.isValid) {
            return null;
        }
        // const ownerPo: cc.Vec2 = this.getFirePos();
        const dir: cc.Vec2 = target.getPosition().sub(firePo).normalize();
        const targetPo: cc.Vec2 = cc.v2(firePo.add(dir.mul(this.shootRange)));;
        return targetPo;
    }
    // setAimer(aimer: cc.Node): blhShooter {
    //     this.aimer = aimer;
    //     return this;
    // }

    stop(isClearBullet: boolean = false): blhShooter {
        this.inUse = false;
        this.unschedule(this.shootOnce);
        if (isClearBullet) {
            this.curBulletMap.forEach((item: blhBullet) => {
                this.removeItem(item);
            });
        }
        return this;
    }

    // start(): blhShooter {
    //     this.inUse = true;
    //     return this;
    // }



    removeItem(item: blhBullet) {
        if (item.isRemoved) {
            return;
        }
        item.unuse();
        cc.Tween.stopAllByTarget(item.node);
        if (this.curBulletMap && item.node) {
            this.curBulletMap.delete(item.node.uuid);
        }
        // if (item.isLast && this.cd < 0) {
        //     //小于0的cd由最后一发子弹消失时确定下次发射时间
        //     if (item.isLast && this.inUse) {
        //         item.isLast = false;
        //         this.unschedule(this.shootOnce);
        //         this.scheduleOnce(this.shootOnce, -this.cd);
        //     }
        // }
        if (item.remove) {
            item.remove();
        }
    }

    pause(): void {
        this.inUse = false;
        this.curBulletMap.forEach(item => {
            item.pause();
        });
        cc.director.getScheduler().pauseTarget(this);
    }

    resume(): void {
        this.inUse = true;
        this.curBulletMap.forEach(item => {
            item.resume();
        });
        cc.director.getScheduler().resumeTarget(this);
    }

    reset() {
        this.stop();
    }



    shootItem(item: blhBullet, targetPo: cc.Vec2, plus: any) {
        // item.tail.reset();
        const itemNode = item.node;
        // itemNode.angle = blhkc.posToAngle(firePo.x, firePo.y, targetPo.x, targetPo.y); //默认不调整角度,由createBullet中调整
        // const costTime = (targetPo.sub(firePo).mag()) / this.bltSpd / 100; //子弹速度根据射程标准化
        const costTime = this.shootRange / this.bltSpd / 100; //子弹速度根据射程标准化
        cc.tween(itemNode).delay(this.bulletDelay).to(costTime, { x: targetPo.x, y: targetPo.y }).start();
        //tween会被打断,用scheduleOnce
        item.scheduleOnce(() => {
            this.removeItem(item);
        }, costTime);
    }

    checkNextTurn() {
        this.unschedule(this.shootOnce);
        this.scheduleOnce(this.shootOnce, this.cd);
    }

    /** 单次子弹(也可能每次发多个)射出 */
    shootOne(firePo: cc.Vec2, i: number, isLast: boolean, plus: any): boolean {
        if (!this.inUse) {
            return false;
        }
        if (i > 0) { //为0时已经找过target了
            this.checkTarget();
        }
        if (!this.target) {
            return false;
        }
        const targetPo: cc.Vec2 = this.getTargetPo(firePo, this.target, i, plus);
        if (!targetPo) {
            return false;
        }
        let bullet: blhBullet = this.createBullet(firePo, targetPo, i, isLast, plus);
        this.curBulletMap.set(bullet.node.uuid, bullet);
        bullet.reuse();
        bullet.isLast = isLast;
        this.shootItem(bullet, targetPo, plus);
        if (isLast) {
            //下一轮cd
            this.unschedule(this.checkNextTurn);
            this.scheduleOnce(this.checkNextTurn);
        }
        return true;
    }

    /** 连续射击时，通过判断目标和owner的状态确定是否可发射 */
    checkCanShoot(): boolean {
        return true;
    }
    // /** 可用于schedule的撤消 */
    // shootOnce: () => void = null;

    noTargetCheck() {
        this.target = this.findTarget();
        if (!this.target) {
            return;
        }
        this.unschedule(this.shootOnce);
        this.shootOnce();
        // if (this.cd > 0) {
        //     //大于0的cd无视最后子弹的发射时间,
        //     this.schedule(this.shootOnce, this.cd, cc.macro.REPEAT_FOREVER);
        // }
    }

    /** 发起一轮射击 */
    shootOnce() {
        if (!this.inUse) {
            return;
        }
        this.unschedule(this.noTargetCheck);

        this.target = this.findTarget();
        if (!this.onShoot() || !this.target) {
            // this.isFindingTarget = true;
            this.schedule(this.noTargetCheck, this.noTargetCD, cc.macro.REPEAT_FOREVER);
            return;
        }
        if (this.numsPerShoot < 2) {
            if (!this.shootOne(this.getFirePos(), 0, true, null)) {
                // this.isFindingTarget = true;
                this.schedule(this.noTargetCheck, this.noTargetCD, cc.macro.REPEAT_FOREVER);
            }
            return;
        }
        for (let i = 0; i < this.numsPerShoot; i++) {
            this.scheduleOnce(() => {
                if (!this.inUse || !this.checkCanShoot()) {
                    return;
                }
                // const isLast: boolean = this.cd < 0 ? (i + 1 === this.numsPerShoot) : false;
                const isLast: boolean = (i + 1 === this.numsPerShoot);
                if (!this.shootOne(this.getFirePos(), i, isLast, null)) {
                    // if (!this.isFindingTarget) {
                    //     this.isFindingTarget = true;
                    this.unschedule(this.noTargetCheck);
                    this.schedule(this.noTargetCheck, this.noTargetCD, cc.macro.REPEAT_FOREVER);
                    // }
                }
            }, i * this.itemCD);
        }
    }



    /** 发起射击 */
    shoot() {
        // if (!this.owner) {
        //     return;
        // }
        this.inUse = true;
        this.unschedule(this.shootOnce);
        if (this.firstCD) {
            this.scheduleOnce(this.shootOnce, this.firstCD);
        } else {
            this.shootOnce();
        }
        // if (this.cd > 0) {
        //     //大于0的cd无视最后子弹的发射时间,
        //     this.schedule(this.shootOnce, this.cd, cc.macro.REPEAT_FOREVER);
        //     return;
        // }
    }


    protected onDestroy(): void {
        this.inUse = false;
        this.unschedule(this.shootOnce);
        this.unschedule(this.noTargetCheck);
        this.unschedule(this.checkNextTurn);
        // this.unscheduleAllCallbacks();
    }

    // //创建子弹散开动画
    // createBulletBoomFrame(pos: cc.Vec2) {
    //     let boomNode = blhStatic.resMgr.getNode('bulletBoomFrame');
    //     boomNode.parent = blhStatic.battle.layer_effect;
    //     boomNode.x = pos.x;
    //     boomNode.y = pos.y;
    // }

    // //创建地面反馈烟
    // createGroundSmogFrame(pos: cc.Vec2) {
    //     let smog = blhStatic.resMgr.getNode('groundSmogFrame');
    //     smog.parent = blhStatic.battle.layer_effect;
    //     smog.x = pos.x;
    //     smog.y = pos.y;
    // }

    // /**创建炮台烟雾 */
    // createBarbetteSmogFrame(pos: cc.Vec2, angle: number) {
    //     let barbetteSmog = blhStatic.resMgr.getNode('barbetteSmogFrame');
    //     barbetteSmog.parent = blhStatic.battle.layer_effect;
    //     barbetteSmog.x = pos.x;
    //     barbetteSmog.y = pos.y;
    //     barbetteSmog.angle = angle;
    // }

    // /** */

    // /**创建导弹击打特效 */
    // createMortarHitFrame(pos: cc.Vec2) {
    //     let hitNode = blhStatic.resMgr.getNode('mortarHitFrame');
    //     hitNode.parent = blhStatic.battle.layer_effect;
    //     hitNode.x = pos.x;
    //     hitNode.y = pos.y;
    // }

    // /**创建导弹发射图 */
    // createMortarFire(pos: cc.Vec2) {
    //     let fireNode = blhStatic.resMgr.getNode('bullet3FireEff');
    //     fireNode.parent = blhStatic.battle.layer_effect;
    //     fireNode.x = pos.x + 5;
    //     fireNode.y = pos.y + 10;
    //     this.scheduleOnce(() => {
    //         fireNode.destroy();
    //     }, 0.1);
    // }

    // /**创建喷火器击打特效 */
    // createFireThrowHitFrame(pos: cc.Vec2) {
    //     let throwHitNode = blhStatic.resMgr.getNode('fireThrowHitFrame');
    //     throwHitNode.parent = blhStatic.battle.layer_effect;
    //     throwHitNode.x = pos.x;
    //     throwHitNode.y = pos.y;
    // }



    // /**创建机枪发射枪口烟 */
    // createGunFireFrame(pos: cc.Vec2, angle: number) {
    //     let gunFireNode = blhStatic.resMgr.getNode('gunFireFrame');
    //     gunFireNode.parent = blhStatic.battle.layer_effect;
    //     gunFireNode.angle = angle;
    //     gunFireNode.setPosition(pos);
    // }

    // /**创建重炮爆炸光圈 */
    // createCannonBoomFrame(pos: cc.Vec2) {
    //     let boomCircle = blhStatic.resMgr.getNode('boomCircleFrame');
    //     boomCircle.parent = blhStatic.battle.layer_effect;
    //     boomCircle.setPosition(pos);
    // }

    // /**创建重炮爆炸烟雾 */
    // createCannonBoomSmogFrame(pos: cc.Vec2) {
    //     let boomSmog = blhStatic.resMgr.getNode('cannonBoomFrame');
    //     boomSmog.parent = blhStatic.battle.layer_effect;
    //     boomSmog.setPosition(pos);
    // }


}