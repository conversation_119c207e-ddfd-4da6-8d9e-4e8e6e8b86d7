/**
 * 扇形散弹发射器,每次发射多个子弹
 * 
 */

import blhkc from "../utils/blhkc";
import blhBullet from "./blhBullet";
import blhShooter from "./blhShooter";


const { ccclass, property } = cc._decorator;
@ccclass
export default class blhShooterA extends blhShooter {

    /** 每次发射子弹的个数 */
    bulletNum = 5;
    /** 每次发射子弹的角度间隔,单位弧度 */
    angleGap = 0.2;


    shootOne(firePo: cc.Vec2, i: number, isLast: boolean, plus: any): boolean {
        if (!this.inUse) {
            return false;
        }
        if (i > 0) { //为0时已经找过target了
            this.checkTarget();
        }
        if (!this.target) {
            return false;
        }
        const targetPo: cc.Vec2 = this.getTargetPo(firePo, this.target, i, plus);
        if (!targetPo) {
            return false;
        }
        const half = Math.floor(this.bulletNum / 2);
        const dir = targetPo.sub(firePo).normalize();
        const angle = blhkc.posToAngle(0, 0, dir.x, dir.y);

        for (let j = 0; j < this.bulletNum; j++) {
            const rotationGap = this.angleGap * (j - half);
            const oneTargetPo = cc.v2(this.shootRange, 0).rotate(rotationGap).add(firePo);
            let bullet: blhBullet = this.createBullet(firePo, oneTargetPo, i, isLast, plus);
            this.curBulletMap.set(bullet.node.uuid, bullet);
            bullet.reuse();
            if (j === 0) {
                bullet.isLast = isLast;
            }
            bullet.node.angle = angle + rotationGap / blhkc.PI180a;
            this.shootItem(bullet, oneTargetPo, plus);
        }
        return true;
    }

}