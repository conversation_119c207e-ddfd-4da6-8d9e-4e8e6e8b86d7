import Live2dComponent from '../../../live2d/Live2dComponent';

const { ccclass, property } = cc._decorator;

/**
 * Live2D手部物品位置修正测试组件
 * 用于测试和验证手部物品位置修正是否生效
 */
@ccclass
export class Live2dHandCorrectionTest extends cc.Component {

    @property(Live2dComponent)
    live2dComponent: Live2dComponent = null;

    @property
    testModelName: string = 'dafeng_7';

    @property
    autoTest: boolean = true;

    private testInterval: number = 0;
    private debugMode: boolean = false;
    private debugTimer: number = 0;

    onLoad() {
        if (this.autoTest) {
            this.scheduleOnce(() => {
                this.startTest();
            }, 2); // 等待2秒让模型加载完成
        
        }
    }

    start() {
        // 监听键盘事件进行手动测试
        cc.systemEvent.on(cc.SystemEvent.EventType.KEY_DOWN, this.onKeyDown, this);
    }

    onDestroy() {
        cc.systemEvent.off(cc.SystemEvent.EventType.KEY_DOWN, this.onKeyDown, this);
        if (this.testInterval) {
            clearInterval(this.testInterval);
        }
        this.stopDebugMonitor();
    }

    private onKeyDown(event: cc.Event.EventKeyboard) {
        switch (event.keyCode) {
            case cc.macro.KEY.space:
                this.testMotion();
                break;
            case cc.macro.KEY.p:
                this.printParameterValues();
                break;
            case cc.macro.KEY.c:
                this.checkCorrection();
                break;
            case cc.macro.KEY.r:
                this.reloadModelAndTest();
                break;
            case cc.macro.KEY.d:
                this.toggleDebugMode();
                break;
        }
    }

    /**
     * 开始自动测试
     */
    public startTest() {
        if (!this.live2dComponent) {
            console.log('Live2dComponent未设置');
            return;
        }

        console.log('=== Live2D手部修正测试开始 ===');
        
        // 加载测试模型
        this.live2dComponent.loadModel(this.testModelName);
        
        // 等待模型加载完成后开始测试
        this.scheduleOnce(() => {
            this.runTestSequence();
        }, 3);
    }

    /**
     * 运行测试序列
     */
    private runTestSequence() {
        console.log('开始测试序列...');
        
        // 测试1: 播放问题动画
        this.testMotion();
        
        // 测试2: 检查参数修正
        this.scheduleOnce(() => {
            this.checkCorrection();
        }, 2);
        
        // 测试3: 打印参数值
        this.scheduleOnce(() => {
            this.printParameterValues();
        }, 4);
    }

    /**
     * 测试播放动画
     */
    public testMotion() {
        if (!this.live2dComponent) return;
        
        console.log('播放测试动画: Idle_3 (idle3_motion3.json)');
        this.live2dComponent.startMotion('Idle', 0, 100);
    }

    /**
     * 检查修正是否生效
     */
    public checkCorrection() {
        if (!this.live2dComponent || !this.live2dComponent.live2d) {
            console.log('Live2D组件未准备好');
            return;
        }

        const model = this.live2dComponent.live2d.getModel(0);
        if (!model) {
            console.log('模型未加载');
            return;
        }

        console.log('=== 检查手部参数修正 ===');
        
        // 检查关键手部参数
        const handParams = [
            'ParamHandLAngle',
            'ParamHandRAngle', 
            'ParamHandLAngleX',
            'ParamHandLAngleY',
            'ParamHandRAngleX',
            'ParamHandRAngleY'
        ];

        handParams.forEach(paramName => {
            try {
                // 这里需要通过模型的公共方法获取参数值
                console.log(`参数 ${paramName}: 修正系统已配置`);
            } catch (e) {
                console.log(`参数 ${paramName}: 获取失败 - ${e}`);
            }
        });
    }

    /**
     * 打印当前参数值（用于调试）
     */
    public printParameterValues() {
        console.log('=== 🔍 Live2D模型参数分析 ===');
        console.log('模型名称:', this.testModelName);

        if (this.live2dComponent && this.live2dComponent.live2d) {
            const live2dModel = this.live2dComponent.live2d.getModel(0);
            if (live2dModel) {
                const cubismModel = live2dModel.getModel();
                if (cubismModel) {
                    console.log('=== 📋 模型所有参数列表 ===');
                    const paramCount = cubismModel.getParameterCount();
                    console.log(`参数总数: ${paramCount}`);

                    // 通过底层模型获取参数名称
                    const coreModel = (cubismModel as any)._model;
                    if (coreModel && coreModel.parameters) {
                        const paramIds = coreModel.parameters.ids;

                        console.log('=== 🖐️ 手部相关参数搜索 ===');
                        let foundHandParams = false;

                        // 搜索可能的手部参数
                        const handKeywords = ['hand', 'arm', 'finger', '手', '腕', '指', 'wrist', 'palm'];
                        for (let i = 0; i < paramCount && i < paramIds.length; i++) {
                            const paramName = paramIds[i];
                            const paramNameLower = paramName.toLowerCase();

                            for (const keyword of handKeywords) {
                                if (paramNameLower.includes(keyword)) {
                                    const value = cubismModel.getParameterValueByIndex(i);
                                    const min = cubismModel.getParameterMinimumValue(i);
                                    const max = cubismModel.getParameterMaximumValue(i);
                                    console.log(`✅ 找到手部参数 [${i}]: ${paramName} = ${value.toFixed(3)} (范围: ${min.toFixed(1)} ~ ${max.toFixed(1)})`);
                                    foundHandParams = true;
                                    break;
                                }
                            }
                        }

                        if (!foundHandParams) {
                            console.log('❌ 未找到明显的手部参数');
                            console.log('=== 📝 显示前20个参数供参考 ===');
                            for (let i = 0; i < Math.min(20, paramCount, paramIds.length); i++) {
                                const paramName = paramIds[i];
                                const value = cubismModel.getParameterValueByIndex(i);
                                console.log(`[${i}]: ${paramName} = ${value.toFixed(3)}`);
                            }
                        }

                        console.log('=== 🎯 角度相关参数搜索 ===');
                        const angleKeywords = ['angle', 'rotation', 'rotate', '角度', '回転'];
                        for (let i = 0; i < paramCount && i < paramIds.length; i++) {
                            const paramName = paramIds[i];
                            const paramNameLower = paramName.toLowerCase();

                            for (const keyword of angleKeywords) {
                                if (paramNameLower.includes(keyword)) {
                                    const value = cubismModel.getParameterValueByIndex(i);
                                    console.log(`🔄 角度参数 [${i}]: ${paramName} = ${value.toFixed(3)}`);
                                    break;
                                }
                            }
                        }

                    } else {
                        console.log('❌ 无法访问底层模型参数信息');
                    }
                } else {
                    console.log('❌ CubismModel 未准备好');
                }
            } else {
                console.log('❌ Live2dModel 未准备好');
            }
        } else {
            console.log('❌ Live2dComponent 未准备好');
        }

        console.log('=== ⚙️ 当前修正配置 ===');
        console.log('修正系统状态: 已启用 - 每帧持续应用');
        console.log('目标: 修正杆子位置，使其更贴近腿部');
        console.log('建议: 找到实际的手部参数后，调整修正值');
    }

    /**
     * 手动调整测试
     */
    public manualAdjustTest(paramName: string, value: number) {
        console.log(`手动调整测试: ${paramName} = ${value}`);
        // 这里可以添加手动调整逻辑
    }

    /**
     * 重新加载模型并测试（按R键）
     */
    public reloadModelAndTest() {
        if (!this.live2dComponent) {
            console.log('Live2dComponent未设置');
            return;
        }

        console.log('=== 重新加载模型并应用最新修正 ===');

        // 重新加载模型
        this.live2dComponent.loadModel(this.testModelName);

        // 等待模型加载完成后播放测试动画
        this.scheduleOnce(() => {
            console.log('模型重新加载完成，播放测试动画...');
            this.testMotion();
        }, 2);
    }

    /**
     * 切换调试模式（按D键）
     */
    public toggleDebugMode() {
        this.debugMode = !this.debugMode;

        if (this.debugMode) {
            console.log('=== 开启调试模式 ===');
            console.log('将每秒输出手部参数值');
            this.startDebugMonitor();
        } else {
            console.log('=== 关闭调试模式 ===');
            this.stopDebugMonitor();
        }
    }

    private startDebugMonitor() {
        this.debugTimer = setInterval(() => {
            if (this.live2dComponent && this.live2dComponent.live2d) {
                const live2dModel = this.live2dComponent.live2d.getModel(0);
                if (live2dModel) {
                    const cubismModel = live2dModel.getModel();
                    if (cubismModel) {
                        console.log('=== 实时参数值 ===');
                        const params = ['ParamHandLAngle', 'ParamHandRAngle', 'ParamHandLAngleX', 'ParamHandLAngleY', 'ParamHandRAngleX', 'ParamHandRAngleY'];
                        params.forEach(paramName => {
                            try {
                                const paramId = (window as any).Live2DCubismFramework.CubismFramework.getIdManager().getId(paramName);
                                const value = cubismModel.getParameterValueById(paramId);
                                console.log(`${paramName}: ${value.toFixed(3)}`);
                            } catch (e) {
                                console.log(`${paramName}: 参数不存在`);
                            }
                        });
                    }
                }
            }
        }, 1000);
    }

    private stopDebugMonitor() {
        if (this.debugTimer) {
            clearInterval(this.debugTimer);
            this.debugTimer = 0;
        }
    }

}
