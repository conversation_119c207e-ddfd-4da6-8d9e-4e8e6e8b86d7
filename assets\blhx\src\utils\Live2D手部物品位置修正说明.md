# Live2D 手部物品位置修正解决方案

## 问题描述
`dafeng_7` 模型在播放 `idle3_motion3.json` 动画时，手里的物品（台球杆）位置会发生偏移，而 `ankeleiqi_2_hx` 模型播放相同动画时没有问题。

## 问题根本原因（已确认）
通过分析 `idle3_motion3.json` 文件发现：

1. **关键参数有复杂动画曲线**：
   - `ParamHandLAngle`: 从 -0.582 开始的复杂曲线
   - `ParamHandRAngle`: 从 -0.578 开始的复杂曲线

2. **其他手部参数为静态值**：
   - `ParamHandLChange`, `ParamHandLAnimation` 等都是 0 或 8 的静态值

3. **模型差异**：`ankeleiqi_2_hx` 模型没有这些复杂的手部参数

## 解决方案（最新版本）

### 1. 精确的参数修正系统
基于动画文件分析，在 `LAppModel.ts` 中实现了精确的手部参数修正：

```typescript
private getHandItemCorrections(modelName: string): { [key: string]: number } {
    const modelAdjustments: { [key: string]: any } = {
        'dafeng_7': {
            // 主要修正参数 - 针对动画中的复杂曲线进行补偿
            'ParamHandLAngle': 0.15,        // 左手角度修正（减少，让杆子向腿部方向）
            'ParamHandRAngle': 0.15,        // 右手角度修正（减少，让杆子向腿部方向）

            // 次要修正参数 - 微调静态值，让杆子向腿部方向移动
            'ParamHandLAngleX': -0.08,      // 左手X轴角度（负值让杆子向内）
            'ParamHandLAngleY': -0.12,      // 左手Y轴角度（更负让杆子向下）
            'ParamHandRAngleX': -0.08,      // 右手X轴角度（负值让杆子向内）
            'ParamHandRAngleY': -0.12,      // 右手Y轴角度（更负让杆子向下）
        }
    };
    return modelAdjustments[modelName] || {};
}
```

### 2. 动画更新时机修正
在 `LAppModel.ts` 的 `update()` 方法中，在动画更新**后立即**应用修正：

```typescript
motionUpdated = this._motionManager.updateMotion(this._model, deltaTimeSeconds);

// 在动画更新后立即应用手部物品位置修正
if (motionUpdated) {
    this.applyHandItemCorrection();
}
```

### 3. 调试工具
创建了 `blhLive2dDebugger.ts` 调试组件，支持：
- **A/B键**：调整X轴位置偏移
- **↑↓键**：调整Y轴位置偏移  
- **Q/E键**：调整缩放
- **数字键1-6**：调整手部参数
- **T键**：测试播放动画
- **R键**：重置所有参数

## 使用方法

### 自动修正（推荐）
1. 直接加载 `dafeng_7` 模型，系统会自动应用预设的修正参数
2. 播放 `idle_motion3` 动画查看效果

### 手动调试
1. 在场景中添加 `blhLive2dDebugger` 组件
2. 设置 `showDebugPanel = true`
3. 运行游戏，使用键盘调试参数
4. 调试完成后调用 `exportCurrentConfig()` 导出配置

### 测试方法
使用 `Live2dHandCorrectionTest` 组件进行测试：
- **空格键**: 播放测试动画
- **P键**: 打印当前参数状态
- **C键**: 检查修正配置
- **R键**: 重新加载模型并测试（应用最新修正）
- **D键**: 切换调试模式（实时监控参数值）

### 重要修正
- **修正时机**: 现在在每一帧都应用修正，而不是只在动画更新时
- **修正方式**: 使用 `addParameterValueById` 在动画值基础上叠加修正值
- **持续性**: 确保修正在整个动画播放过程中持续生效

### 代码集成
```typescript
// 在blhHallUI.ts中
this.l2d.loadModel('dafeng_7'); // 自动应用修正

// 或手动设置
this.l2d.offsetX = -15;
this.l2d.offsetY = 8;
this.l2d.modelScaleAdjust = 0.95;
```

## 参数说明

### 位置偏移参数
- `offsetX`: X轴偏移（负值向左，正值向右）
- `offsetY`: Y轴偏移（负值向下，正值向上）
- `modelScaleAdjust`: 模型缩放调整（1.0为原始大小）

### 手部修正参数
- `ParamHandLAngle`: 左手角度调整
- `ParamHandLChange`: 左手变化参数
- `ParamHandLAnimation`: 左手动画参数
- `ParamHandRAngle`: 右手角度调整
- `ParamHandRChange`: 右手变化参数
- `ParamHandRAnimation`: 右手动画参数

## 扩展性
系统支持为其他模型添加特定的修正配置，只需在 `MODEL_ADJUSTMENTS` 中添加新的模型配置即可。

## 注意事项
1. 参数修正使用较小的权重(0.3)进行平滑过渡，避免突兀的变化
2. 如果参数不存在会自动跳过，不会影响其他模型
3. 建议先使用调试工具找到合适的参数值，再更新到配置中
