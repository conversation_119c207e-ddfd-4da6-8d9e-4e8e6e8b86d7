/**
 * 子弹池,注意地图移动时，有可能会移到池子放物体的坐标，所以需要保持物体透明且不可用
 */

import { blhStatic } from "../mgr/blhStatic";
import blhkc from "./blhkc";


export default class blhBulletPool {
    //  unuseMap: any = {};
    //  inuseMap: any = {};
    unuseMap: Map<string, cc.Node> = new Map();
    inuseMap: Map<string, cc.Node> = new Map();
    private outPosX: number = blhStatic.halfViewWidth + 100 * blhkc.randomInt(0, 20);
    private outPosY: number = -5000; //将y坐标错开,可能会减少碰撞计算
    private outPosDist: number = 100;

    /** 预填 */
    put(item: cc.Node): blhBulletPool {
        item.opacity = 0;
        item.x = this.outPosX;
        this.outPosY += this.outPosDist;
        item.y = this.outPosY;
        this.unuseMap.set(item.uuid, item);
        //  this.unuseMap[item.uuid] = item;
        return this;
    }
    /** 放回 */
    back(item: cc.Node): blhBulletPool {
        // item.destroy();
        // return;
        item.opacity = 0;
        // item.x = this.outPosX;
        this.outPosY += this.outPosDist;
        // item.y = this.outPosY;
        if (this.outPosY > 5000) {
            this.outPosY = -5000;
        }
        this.inuseMap.delete(item.uuid);
        this.unuseMap.set(item.uuid, item);
        //  this.unuseMap[item.uuid] = item;
        //  delete this.inuseMap[item.uuid];
        return this;
    }

    /** 回收所有 */
    backAll() {
        this.inuseMap.forEach((val: cc.Node) => {
            this.back(val);
        });
    }

    del(item: cc.Node) {
        //  delete this.inuseMap[item.uuid];
        //  delete this.unuseMap[item.uuid];
        this.inuseMap.delete(item.uuid);
        this.unuseMap.delete(item.uuid);
    }

    /** 获取 */
    get(): cc.Node {
        const it = this.unuseMap.keys().next();
        if (it.done) {
            return null;
        }
        const re = this.unuseMap.get(it.value);
        this.unuseMap.delete(it.value);
        this.inuseMap.set(it.value, re);
        re.opacity = 255;
        //  let re = null;
        //  for (const i in this.unuseMap) {
        //      re = this.unuseMap[i];
        //      this.inuseMap[i] = re;
        //      delete this.unuseMap[i];
        //      break;
        //  }
        return re;
    }

    destroy(): void {
        this.unuseMap.forEach((node: cc.Node) => {
            node.destroy();
        });
        this.inuseMap.forEach((node: cc.Node) => {
            node.destroy();
        });
        this.unuseMap.clear();
        this.inuseMap.clear();
    }

}
