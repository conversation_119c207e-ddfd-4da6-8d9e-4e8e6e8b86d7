import { blhChannelWeb } from "./blhChannelWeb";
import blhkc from "./blhkc";

/**
 * 分渠道打包处理,出包时配置其中的channel值为渠道对象
 *
 */
export class blhChannel {

    private static _me: blhChannel;
    public static instance() {
        if (!blhChannel._me) {
            blhChannel._me = new blhChannel();
        }
        return blhChannel._me;
    }

    /** 统计需要的版本号 */
    static version = '1.0.1';
    static verMap = {
        'debug': {
            'id': '684fe8accff47572c39fd4e0', //key: rZb8LZ5eu16839dE
        },
    };

    /** 当前渠道,出包在此配置渠道  */
    channel: string = 'web';

    /** 是否读取远程配置,出正式包时设置为false */
    static isConfRemote: boolean = true;

    /** 读取远程配置的key,用于指定读哪个配置 */
    static remoteVerKey: string = 'debug';

    /** 游戏名称，用于远程存档,隐私政策等显示用 */
    static gameName: string = '碧蓝航线';



    channelMap: Map<String, blhChannelWeb> = new Map([
        ['web', new blhChannelWeb()],
        // ['bl', new sfrChannelBiLi()],
    ]);



    getGameName() {
        return this.getChannel().getGameName();
    }
    login({ successCall, failCall }: { successCall: () => void, failCall: (code: number) => void }): void {
        this.getChannel().login({ successCall, failCall });
    }
    /**
     * 上传解密微信私密数据
     * @param appId 
     * @param encryptedData 加密数据
     * @param iv 加密iv
     * @param signature 加密签名
     * @param code 微信登录authCode
     * @param callback 
     */
    static decodeWxData(appId: string, encryptedData: string, iv: string, code: string, signature: string, callback: (data: Array<{ dataType: { type: number }, value: number }>) => void) {
        this.instance().getChannel().jpost('https://ga.gamejym.com/fcm/gameApi/decodeWxData', { appId, encryptedData, iv, code, signature }, (err: any, re: any) => {
            if (err) {
                console.error(err);
                callback(null);
                return;
            }
            if (re.code !== 0) {
                callback(null);
                return;
            }
            callback(re.data && re.data.dataList);
        });
    }


    /**
     * 订阅消息
     * @param appId 
     * @param openId 
     * @param template_id 模板id
     * @param signDay 签到天数
     * @param callback 
     */
    static subMsg(appId: string, openId: string, template_id: string, signDay: number, callback: (code: number) => void) {
        this.instance().getChannel().jpost('https://ga.gamejym.com/fcm/gameApi/wxOrderSubMsg', { appId, openId, template_id, signDay }, (err: any, re: any) => {
            if (err) {
                console.error(err);
                callback(-1);
                return;
            }
            if (re.code !== 0) {
                callback(-1);
                return;
            }
            callback(0);
        });
    }

    /**
     * 兑换码兑换
     * @param gameId 
     * @param userId 
     * @param redeemCode 兑换码
     * @param callback Array<{ key: string, val: string }> 如: [{key:'金币',val:'1000'},{key:'解锁',val:'weapon1'}]
     */
    static redeem(gameId: string, userId: string, redeemCode: string, callback: (arr: Array<{ key: string, val: string }>) => void) {
        this.instance().getChannel().jpost('https://ga.gamejym.com/fcm/gameApi/redeem', { 'code': redeemCode, gameId, userId }, (err?: any, re?: any) => {
            if (err) {
                console.error(err);
                callback(null);
                return;
            }
            if (re.code !== 0) {
                callback(null);
                return;
            }

            const dataArr = blhkc.strArr(re.data);
            if (!dataArr) {
                callback(null);
                return;
            }
            const out = [];
            for (let i = 0; i < dataArr.length; i++) {
                const arr = dataArr[i].split(':');
                if (arr.length < 2) {
                    console.error('兑换码配置有误', dataArr[i]);
                    continue;
                }
                out.push({ key: arr[0], val: arr[1] });
            }
            callback(out);
        });
    }

    /**
     * 拉取分享图,callback中返回Array<{ picId: string, picUrl: string, txt: string }>
     * @param gameId 
     * @param callback 
     */
    static getSharePics(gameId: string, callback: (arr: Array<{ picId: string, picUrl: string, txt: string }>) => void) {
        const reqObj = {
            'game': gameId,
            'query': 'aaa',
            'v': '1.2.0',
            's': 's',
        };
        this.instance().getChannel().jpost('https://ga.gamejym.com/htApi/ht_api/getSharePic', reqObj, (err: any, re: any) => {
            if (err) {
                blhkc.err(err);
                callback([]);
                return;
            }
            // sfrkc.log('re', re);
            if (!re || (re.re != 0) || !re.data || re.data.length <= 0) {
                return callback([]);
            }
            const out = [];
            for (let i = 0; i < re.data.length; i++) {
                const one = re.data[i];
                const arr = one.split('|');
                out.push({ picId: arr[0], txt: arr[1], picUrl: arr[2] });
            }
            callback(out);
        });
    }


    /**
     * 上报用户排行榜数据
     * @param gameId 用于区分游戏以及不同的排行榜
     * @param openId 需要openId,会从上报昵称的表中按openId取昵称等信息
     * @param score 用于排行计算的值
     * @param callback 返回{oldRank: number, newRank: number}，即上报数据后用户自己的当前排名,null为异常
     */
    static leaderBoradUpdate(gameId: string, openId: string, score: number, callback: (re: { oldRank: number, newRank: number }) => void) {
        this.instance().getChannel().jpost('https://ga.gamejym.com/fcm/gameApi/leaderBoradUpdate', { gameId, userId: openId, score }, (err: any, re: any) => {
            if (err) {
                blhkc.err(err);
                callback(null);
                return;
            }
            // sfrkc.log('re', re);
            if (re.code) {
                blhkc.err(re.code);
                callback(null);
                return;
            }
            callback(re.data);
        });
    }

    /**
     * 获取用户排行榜排名
     * @param gameId 用于区分游戏以及不同的排行榜
     * @param openId 需要openId,会从上报昵称的表中按openId取昵称等信息
     * @param callback 返回newRank，即上报数据后用户自己的当前排名,-1为异常或无排名
     */
    static leaderBoradRank(gameId: string, openId: string, callback: (newRank: number) => void) {
        this.instance().getChannel().jpost('https://ga.gamejym.com/fcm/gameApi/leaderBoradRank', { gameId: gameId, userId: openId }, (err: any, re: any) => {
            if (err) {
                blhkc.err(err);
                callback(-1);
                return;
            }
            // sfrkc.log('re', re);
            if (re.code) {
                blhkc.err(re.code);
                callback(-1);
                return;
            }
            callback(re.data);
        });
    }


    /**
     * 拉取实时排行榜
     * @param gameId 排行榜id
     * @param callback 参数为按score倒排的数组
     */
    static getLeaderBorad(gameId: string, callback: (list: Array<{
        nickName: string,
        avatar: string,
        score: number, //排行数值
    }>) => void) {
        this.instance().getChannel().jpost('https://ga.gamejym.com/fcm/gameApi/getLeaderBorad', { gameId }, (err: any, re: any) => {
            if (err) {
                blhkc.err(err);
                callback([]);
                return;
            }
            // sfrkc.log('re', re, re.ts);
            if (re.code) {
                blhkc.err(re.code);
                callback([]);
                return;
            }
            callback(re.data);
        });
    }


    /**
     * 上报用户排行榜数据
     * @param gameId 用于区分游戏以及不同的排行榜
     * @param myUserId 用户自己id
     * @param myUserName 用户昵称
     * @param userIcon 用户icon
     * @param value 用于排行计算的值
     */
    static reportToRankList(gameId: string, myUserId: string, myUserName: string, userIcon: string, value: string, callback?: () => void) {
        this.instance().getChannel().jpost('https://ga.gamejym.com/fcm/gameApi/phbUpdate', { userId: myUserId, gameId: gameId, userName: myUserName, userIcon, sortVal: value }, (err: any) => {
            callback && callback();
            if (err) {
                blhkc.err(err);
                return;
            }
            // sfrkc.log('re', re);
        });
    }

    /**
     * 拉取排行榜
     * @param gameId 排行榜id
     * @param callback 参数为按value倒排的数组
     */
    static getRankList(gameId: string, callback: (list: Array<{
        'userId': string,
        'userName': string,
        'userIcon': string,
        'sortVal': number, //排行数值
    }>) => void) {
        this.instance().getChannel().jpost('https://ga.gamejym.com/fcm/gameApi/phbList', { gameId }, (err: any, re: any) => {
            if (err) {
                blhkc.err(err);
                callback([]);
                return;
            }
            // sfrkc.log('re', re);
            if (re.code) {
                blhkc.err(re.code);
                callback([]);
                return;
            }
            callback(re.data);
        });
    }


    /**
     * 加载远程存档
     * @param openId 
     * @param callback 
     * @returns 
     */
    static loadFromRemote(openId: string, callback: (err: any, saveData?: any) => void): void {
        //web为测试，不上报数据
        if (blhChannel.isConfRemote) {
            callback(null);
            return;
        }

        blhkc.log('加载远程存档开始....', openId);
        this.instance().getChannel().jpost('https://ga.gamejym.com/fcm/userSave/getUserData', { 'game_id': blhChannel.gameName + blhChannel.instance().channel, 'user_id': openId }, (err, re) => {
            if (err) {
                blhkc.err(err);
                callback(err);
                return;
            }
            // sfrkc.log('loadFromRemote完成:' + JSON.stringify(re));
            if (re.code === 0 && re.data) {
                delete re.data.user_id;
                delete re.data.game_id;
                delete re.data._id;

                // 这里看情况是否存本地
                // sfrkc.saveUpdate(re.data.saveData);
                blhkc.log('远程存档加载完成');
                callback(null, re.data.saveData);
                return;
            }
            callback(null);
        });
    }


    /**
     * 执行远程存档
     * @param openId 
     * @param saveData 
     * @returns 
     */
    static saveToRemote(openId: string, saveData: any, isTipShow: boolean = false): void {
        //web为测试，不存远程
        if (blhChannel.isConfRemote) {
            return;
        }
        this.instance().getChannel().jpost('https://ga.gamejym.com/fcm/userSave/saveUserData', { 'game_id': blhChannel.gameName + blhChannel.instance().channel, 'user_id': openId, saveData }, (err, re) => {
            if (err) {
                blhkc.err(err);
                return;
            }
            blhkc.log('saveToRemote完成:' + JSON.stringify(re));
            // if (isTipShow) {
            //     sfrTipManager.showHomeTip("数据上传成功", null, 3);
            // }
        });
    }
    /** 分享图上报次数 */
    static picShareTo(gameId: string, wxPicId: string) {
        const reqObj = {
            'game': gameId,
            'query': 'aaa',
            'v': '1.2.0',
            's': 's',
            'wxPicId': wxPicId,
        };
        this.instance().getChannel().jpost('https://ga.gamejym.com/htApi/ht_api/shareTo', reqObj, (err: any, re: any) => {
            if (err) {
                blhkc.err(err);
                return;
            }
            // sfrkc.log('re', re);
        });
    }
    /** 分享图上报次数 */
    static picShareFrom(gameId: string, wxPicId: string) {
        const reqObj = {
            'game': gameId,
            'query': 'aaa',
            'v': '1.2.0',
            's': 's',
            'wxPicId': wxPicId,
        };
        this.instance().getChannel().jpost('https://ga.gamejym.com/htApi/ht_api/shareFrom', reqObj, (err: any, re: any) => {
            if (err) {
                blhkc.err(err);
                return;
            }
            // sfrkc.log('re', re);
        });
    }


    getPara(key: string): string {
        return this.getChannel().getPara(key);
    }

    getChannel(): blhChannelWeb {
        return this.channelMap.get(this.channel);
    }

    /** 获取软著文字 */
    getRuanZhu(): string {
        return this.getChannel().getRuanZhu();
    }


    exit() {
        this.getChannel().exit();
    }

    /** getSystemInfoSync */
    getSysInfo(): any {
        return this.getChannel().getSysInfo();
    }

    /* 是否有隐私政策 */
    hasPrivacy(): boolean {
        return this.getChannel().hasPrivacy();
    }
    getCPara(key: string): any {
        return this.getChannel().getCPara(key);
    }
    privacyReject(): number {
        return this.getChannel().privacyReject();
    }
    /** 同意隐私政策 */
    privacyAgree(): number {
        return this.getChannel().privacyAgree();
    }
    privacyNextPage(pageType: number): string {
        return this.getChannel().privacyNextPage(pageType);
    }
    privacyPrePage(pageType: number): string {
        return this.getChannel().privacyPrePage(pageType);
    }
    privacyPage1(pageType: number): string {
        return this.getChannel().privacyPage1(pageType);
    }

    getCtrl(key: string): number {
        return this.getChannel().getCtrl(key);
    }

    getPrivacyShort(): string {
        return this.getChannel().getPrivacyShort();
    }

    getPrivacy(): string {
        return this.getChannel().getPrivacy();
    }

    init(loginCall: () => void) {
        //注意web渠道不会调loginCall
        this.getChannel().init(loginCall);
    }

    /** 
      * 分渠道的广告特定动作，actId
      * @param actName 区分哪个动作
      * @param para 为执行动作需要的对象 
      */
    doAction(actName: string, para: any = null) {
        this.getChannel().doAction(actName, para);
    }
    /** 是否支持分享 */
    canShare(): boolean {
        return this.getChannel().canShare();
    }

    /** 分享 */
    shareAppMessage(paras: { title: string, act: string, adsid: string }, sucCall: any = null, failCall: any = null) {
        this.getChannel().shareAppMessage(paras, sucCall, failCall);
    }


    /** 复制到剪贴板 */
    copyToClip(contentStr: string, callback: (err?: string) => void) {
        this.getChannel().copyToClip(contentStr, callback);
    }


    showVideo(sucCall: any, failCall: any, endCall: any, posName: string) {
        if (cc.sys.isBrowser) {
            return new Promise<void>((resolve, reject) => {
                blhkc.log('showVideo 当前是浏览器环境，视频广告默认成功。');
                sucCall && sucCall();
                endCall && endCall();
                resolve();
            });
        }
        else {
            return new Promise<void>((resolve, reject) => {
                const wrappedCallbacks = {
                    sucCall: () => {
                        sucCall?.();
                    },
                    failCall: () => {
                        failCall?.();
                    },
                    endCall: () => {
                        endCall?.();
                        setTimeout(resolve, this.getChannel().getVideoShowDelay() * 1000);
                    }
                };
                this.getChannel().showVideo(wrappedCallbacks.sucCall, wrappedCallbacks.failCall, wrappedCallbacks.endCall, posName);
            });
        }
    }
    // showVideo(paras: { sucCall: any, failCall: any, endCall: any, posName?: string; adsid?: string } = null) {

    // }

    addGird9Icon(parent: cc.Node, pos: cc.Vec2) {
        this.getChannel().addGird9Icon(parent, pos);
    }

    gird9IconClick() {
        this.getChannel().gird9IconClick();
    }

    /** 9宫格 */
    showGird9(): void {
        this.getChannel().showGird9();
    }

    /** 是否主动(定时)显示9宫格 */
    canAutoGird9(): boolean {
        return this.getChannel().canAutoGird9();
    }

    closeGird9(): void {
        this.getChannel().closeGird9();
    }

    showBanner(isForce: boolean = false, styleFn: any = null, where: string = null): void {
        if (cc.sys.isBrowser) {
            blhkc.log('showBanner，当前为浏览器，不显示banner广告');
        }
        else {
            this.getChannel().showBanner(isForce, styleFn, where);
        }
    }

    closeBannerAndCustom(delay: number): void {
        setTimeout(() => {
            blhChannel.instance().closeBanner();
            blhChannel.instance().closeCustomAd();
        }, delay);
    }

    closeBanner(): void {
        this.getChannel().closeBanner();
    }

    showBannerByTime(isForce: boolean = false) {
        this.getChannel().showBannerByTime(isForce);
    }

    closeBannerByTime() {
        this.getChannel().closeBannerByTime();
    }

    checkNativeInsert(adKey: string): boolean {
        return this.getChannel().checkNativeInsert(adKey);
    }
    showNative(parent: cc.Node, pos: cc.Vec2, adKey: string, isForce: boolean = false, where: string = null) {
        this.getChannel().showNative(parent, pos, adKey, isForce, where);
    }

    loadAndShowNative(parent: cc.Node, pos: cc.Vec2, adKey: string) {
        this.getChannel().loadAndShowNative(parent, pos, adKey);
    }

    loadNative(adKey: string, callback: (adList: any, newAdKey: string) => void) {
        this.getChannel().loadNative(adKey, callback);
    }

    /** 找到当前任意一个原生并触发点击 */
    findAndReport() {
        this.getChannel().findAndReport();
    }

    reportAdShow(adKey: string, node: cc.Node) {
        this.getChannel().reportAdShow(adKey, node);
    }

    reportAdClick(adKey: string) {
        this.getChannel().reportAdClick(adKey);
    }

    closeNative(adKey: string) {
        this.getChannel().closeNative(adKey);
    }

    /** native广告,adKey为paraMap中的key,返回原生load成功的资源 */
    getNativeData(adKey: string): any {
        this.getChannel().getNativeData(adKey);
    }

    /** customAd */
    showCustomAd(isBanner: boolean, isForce: boolean = false, style: any = null): void {
        if (cc.sys.isBrowser) {
            blhkc.log('showCustomAd，当前为浏览器，不显示广告');
        }
        else {
            this.getChannel().showCustomAd(isBanner, isForce, style);
        }
    }
    closeCustomAd(isBanner: boolean = false): void {
        this.getChannel().closeCustomAd(isBanner);
    }


    /** inter */
    showInter(isForce: boolean = false): void {
        if (cc.sys.isBrowser) {
            blhkc.log('showInter，当前为浏览器，不显示广告');
        }
        else {
            this.getChannel().showInter(isForce);
        }
    }
    closeInter(): void {
        this.getChannel().closeInter();
    }


    showDesktop(parent: cc.Node, pos: cc.Vec2): void {
        this.getChannel().showDesktop(parent, pos);
    }

    checkShortcut(paras: { sucCall: any, failCall: any }) {
        this.getChannel().checkShortcut(paras);
    }

    addShortcut(paras: { sucCall: any, failCall: any }): void {
        this.getChannel().addShortcut(paras);
    }

    recorderStart() {
        this.getChannel().recorderStart();
    }

    recorderStop() {
        this.getChannel().recorderStop();
    }

    /** 获取录屏时长，单位毫秒 */
    getRecordTime(): number {
        return this.getChannel().getRecordTime();
    }
    shareVideo(callback: any = null) {
        this.getChannel().shareVideo(callback);
    }

    /** 互推跳转 */
    jumpTo(appId: string, sucCall = () => { }, failCall = () => { }, completeCall = () => { }) {
        this.getChannel().jumpTo(appId, sucCall, failCall, completeCall);
    }


    canMoreGame(): boolean {
        return this.getChannel().canMoreGame();
    }

    /** 显示更多游戏的盒子 */
    showMoreGameBox(parent: cc.Node, pos: cc.Vec2, isShow: boolean) {
        this.getChannel().showMoreGameBox(parent, pos, isShow);
    }

    showComm() {
        if (this.getChannel().channel !== 'ios') {
            return;
        }
        this.getChannel()['showComm']();
    }

    /**是否为ov平台 */
    isOvPlatForm() {
        return this.getChannel().isOvPlatForm();
    }
    /**是否为微信字节平台 */
    isWxTtPlantForm() {
        return this.getChannel().isWxTtPlantForm();
    }

    addCommon(sucCall?: any, failCall?: any) {
        this.getChannel().addCommon(sucCall, failCall);
    }

}
