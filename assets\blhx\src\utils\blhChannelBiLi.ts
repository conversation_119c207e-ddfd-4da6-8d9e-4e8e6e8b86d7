import { blhChannel } from "./blhChannel";
import { blhChannelWeb } from "./blhChannelWeb";
import blhkc from "./blhkc";
import { Msg, saveData } from "../mgr/blhConst";
import blhAudioMgr from "../mgr/blhAudioMgr";
import { blhStatic } from "../mgr/blhStatic";
// import sfrBlinkEff from "../view/sfrBlinkEff";
import { blhStatisticsMgr } from "./blhStatisticsMgr";
export class blhChannelBiLi extends blhChannelWeb {

    channel: string = 'bl';
    channelId = 6;
    paraMap: Map<String, string> = new Map([
        ['appId', 'biligame5e68cb3c0f1320fe'],
        ['insertAd', ''],
        ['videoId', 'bili-211734060949804528'],
        ['bannerAd', ''],
        ['ctrlId', ''], //策略开头id
        ['gid', ''], //gid
    ]);
    private sharePicsArr = [
    ]

    shareTitle: string = '';
    shareImgId: string = '';

    channelObj: any = (window as any).bl;

    /** 从分享进来的query中拿到的sid参数 */
    sid: string = null;

    static unionid: string = null;
    ruanzhu: string = '著作权人: 咪咕互动娱乐有限公司;上海美术电影制片厂有限公司\n登记号:2020SR0552703';

    //b站onshow回调,执行一次后移除,用于动态监听onshow
    private onShowCall: () => void = null;
    // 授权按钮
    private authorizationButton: any = null;



    /** getSystemInfoSync */
    getSysInfo(): any {
        if (!this.sysInfo) {
            this.sysInfo = this.channelObj.getSystemInfoSync();
        }
        return this.sysInfo;
    }

    /* 是否同意了隐私政策 */
    hasPrivacy(): boolean {
        return true;
    }

    exit(): void {
        this.channelObj.exitMiniProgram({
            isFullExit: true
        });
    }

    init(loginCall: () => void) {
        console.log("bl init");
        super.init(loginCall);
        const bl = this.channelObj;
        bl.onShow((res) => {
            cc.game.resume();
            blhAudioMgr.instance().resumeAll();
            this.onShowCall && this.onShowCall();
            this.onShowCall = null;
        })
        //把游戏挂到后台,调出分享界面
        bl.onHide(() => {
            cc.game.pause();
            if (this.isShare) {//从分享进入
                this.shareTime = new Date().getTime();
            }
        });
        const reqData = { 'id': this.getPara('ctrlId') };
        blhkc.jpost('https://ga.gametdd.com/adCtrl/agd/conf', reqData, (err, re) => {
            if (err) {
                blhkc.err(err);
                return;
            }
            if ('' + re.code !== '0') {
                blhkc.err('返回值错误:' + JSON.stringify(re));
                return;
            }
            this.ctrl = re.data;
            blhkc.log('b站返回', this.ctrl);
        });
        this.scene = this.channelObj.getLaunchOptionsSync().scene;
        //登录
        this.blLogin((e: any) => {
            loginCall && loginCall();
        });

        // let hasData = blhkc.getData(saveData.sidebar, null);
        // let hasget = { today: "", canGet: '' };
        // if (hasData) {
        //     hasget = hasData;
        // }
        // if (bl.checkScene) {
        //     bl.checkScene({
        //         scene: "sidebar",
        //         success: (res) => {
        //             console.log("check scene success: ", res.isExist);
        //             //成功回调逻辑
        //             hasget.canGet = '0';
        //             blhkc.saveData(saveData.sidebar, hasget);
        //         },
        //         fail: (res) => {
        //             console.log("check scene fail:", res);
        //             //失败回调逻辑
        //             hasget.canGet = '-1';
        //             blhkc.saveData(saveData.sidebar, hasget);
        //         }
        //     });
        // } else {
        //     console.log("没有checkScene");
        // }
        //保持屏幕常亮
        bl.setKeepScreenOn({
            keepScreenOn: true
        })


    }

    initSharePic(arr: Array<{ picId: string, picUrl: string, txt: string }>): void {
        if (arr == null || arr.length <= 0) {
            return;
        }
        this.sharePicsArr = [];
    }
    /** 登录成功如果是从分享进来的，更新sid用于拉取分享结果 */
    onLoginSuc() {
        const query = this.channelObj.getLaunchOptionsSync().query;
        if (!query || !query.act || !query.sid) {
            return;
        }
        this.sid = query.sid;
        this.shareFrom();
        this.getUserInfo(null);
    }




    /**
     * 从分享进来，通知分享成功结果给来源
     */
    shareFrom() {
        if (!this.sid || !this.channelUserId) {
            return;
        }
    }

    public getUserInfoToWeb() {
        // // 已获取用户信息后不再需要授权
        // if (pzaLocalData.data.playerNick != null && pzaLocalData.data.playerAvatar != null) {
        //     return;
        // }
        // this.getUserInfo(null);
    }



    /**
     * 获取分享结果
     * @param act 
     * @param callback (userArr: Array<{ nickName: string, avatar: string })
     * @returns 
     */
    getShareResult(act: string, callback: (userArr: Array<{ nickName: string, avatar: string }>) => void) {
        if (!this.channelUserId) {
            return callback([]);
        }
        const reqObj = {
            appId: this.getPara('appId'),
            openId: this.channelUserId,
            act
        }
        blhkc.jpost('https://ga.gamejym.com/fcm/zijie/getShareResult', reqObj, (err: any, resp: any) => {
            if (err) {
                blhkc.err(err);
                callback([]);
                return;
            }
            blhkc.log('getShareResult返回', resp);
            if (resp && resp.code === 0) {
                callback(resp.data);
            } else {
                callback([]);
            }
        });
    }

    /**登录获取openid等 */
    blLogin(callback: (e: any) => void = (e) => { if (e) { console.error(e); } }) {
        const bl = (window as any).bl;
        const me = this;
        bl.login({
            force: true,
            success(res) {
                console.log('login 调用成功', res.code);
                // me.getUserInfoToWeb();
                let options = bl.getLaunchOptionsSync();
                blhkc.jpost('https://ga.gamejym.com/fcm/bili/getOpenId', { appId: me.getPara('appId'), code: res.code }, (err: any, resp: any) => {
                    if (err) {
                        blhkc.err(err);
                        callback(err);
                        return;
                    }
                    blhkc.log('获取openId', resp);
                    if (!resp.openid) {
                        blhkc.err('获取openId失败', resp);
                        callback(resp);
                        return;
                    }
                    me.channelUserId = resp.openid || '';
                    callback(null);
                });
            },
            fail(res) {
                callback(res.errMsg);
                console.log("login调用失败");
            }
        })
    }
    private shareIndex: number = 0;

    getShareObj() {
        // const index = this.shareIndex;
        // this.shareIndex++;
        // if (this.shareIndex >= this.sharePicsArr.length) {
        //     this.shareIndex = 0;
        // }
        let index = Math.floor(Math.random() * this.sharePicsArr.length);
        return this.sharePicsArr[index];
    }
    //上传用户信息
    sendUserInfo(userInfo: { nickName: string, avatarUrl: string }) {
        if (userInfo == null) {
            return;
        }

        if (!this.channelUserId) {
            console.error('sendUserInfo无openId');
            return;
        }
        const reqObj = {
            appId: this.getPara('appId'),
            openId: this.channelUserId,
            nickName: userInfo.nickName || '',
            avatar: userInfo.avatarUrl || '',
        }
        blhkc.jpost('https://ga.gamejym.com/fcm/zijie/sendUserInfo', reqObj, (err: any, resp: any) => {
            if (err) {
                blhkc.err(err);
                return;
            }
            blhkc.log('sendUserInfo返回', resp);
        });
    }

    /** 获取并上传用户信息，这里可能需要用户授权 */
    getUserInfo(successCall: () => void, style?: any, destroyBtn: boolean = true) {
        const my = this.channelObj;
        const me = this;
        my.getUserInfo({
            success(res) {
                console.log("getUserInfo调用成功: ", res.userInfo);
                me.sendUserInfo({ nickName: res.userInfo.nickName, avatarUrl: res.userInfo.avatarUrl });
            },
            fail(res) {
                console.log("getUserInfo调用失败: ", res.errMsg);
            }
        })
    }

    destroyAuthorizationButton(): void {
        try {
            if (!this.authorizationButton) {
                return;
            }
            this.authorizationButton.destroy();
            this.authorizationButton = null;
        } catch (error) {
            this.authorizationButton = null;
            console.error(error);
        }
    }

    canShare(): boolean {
        return true;
    }

    showBanner(isForce?: boolean, styleFn?: any, where?: string): void {
        const bannerId = this.getPara('bannerAd');
        if (!bannerId) {
            return;
        }
        blhkc.log('showBanner isShow', bannerId);
        const bl = this.channelObj;
        const conf = {
            'adUnitId': bannerId,
        }
        this.bannerAd = bl.createBannerAd(conf);

        this.bannerAd.onResize((size) => {
            console.log("banner onResize===", size.height, size.width);
            const style = (styleFn) ? styleFn(size) : { top: this.getSysInfo().windowHeight - size.height, left: (this.getSysInfo().windowWidth - size.width) / 2 };
            this.bannerAd.style.top = style.top;
            this.bannerAd.style.left = style.left;
            // this.bannerAd.style['left'] = 30;
        })
        this.bannerAd.onLoad(() => {
            this.bannerAd.show().then(() => {
                blhkc.log('banner加载成功');
            }).catch(err => {
                this.bannerAd = null;
                blhkc.log('showBanner err:' + JSON.stringify(err));
            });
        });
    }


    closeBanner(): void {
        if (this.bannerAd) {
            this.bannerAd.destroy();
            this.bannerAd = null;
        }
    }


    showBannerByTime(isForce: boolean = false) {

    }

    closeBannerByTime() {
        this.closeBanner();
    }


    /** 复制到剪贴板 */
    copyToClip(contentStr: string, callback: (err?: string) => void) {
        this.channelObj.setClipboardData({
            data: contentStr,
            success: () => {
                callback(null);
            },
            fail: (errMsg) => {
                callback(errMsg);
            },
        });
    }

    startTime: number = 0;
    showVideo(sucCall: any, failCall: any, endCall: any, posName: string) {
        const id = this.getPara('videoId');
        if (!id) {
            return;
        }
        // blhStatisticsMgr.sendEvent(SsEventName.ads_button_click, { name: "ad_id", value: id }, { name: "ad_name", value: posName });
        cc.game.pause();
        this.rewardedVideoAd = this.channelObj.createRewardedVideoAd({
            adUnitId: id
        });
        if (this.videoFns['onLoad']) {
            this.rewardedVideoAd.offLoad(this.videoFns['onLoad']);
        }
        if (this.videoFns['onError']) {
            this.rewardedVideoAd.offError(this.videoFns['onError']);
        }
        if (this.videoFns['onClose']) {
            this.rewardedVideoAd.offClose(this.videoFns['onClose']);
        }
        this.rewardedVideoAd.offLoad();
        this.rewardedVideoAd.offError();
        this.rewardedVideoAd.offClose();
        const me = this;
        me.videoFns['onLoad'] = () => {
            blhkc.log('激励视频 广告加载成功');
        };
        this.startTime = Date.now();
        me.videoFns['onError'] = (err) => {
            blhkc.err("onError", err);
            cc.game.resume();
            // sfrStatic.main.blinkEff.hideMask();
            blhAudioMgr.instance().resumeAll();
            failCall && failCall();
            endCall && endCall();
            let dur = Math.round((Date.now() - this.startTime) / 1000);
            // blhStatisticsMgr.sendEvent(SsEventName.ads_play_result, { name: "ad_id", value: id }, { name: "ad_name", value: posName }, { name: "play_result", value: -1 + '' }, { name: "ad_duration", value: dur });
            // 视频失败切换分享
            // if (sfrStatic.userData.adShareCount < sfrStatic.userData.maxAdShareCount) {
            //     this.shareAppMessage({ title: null, act: null, adsid: paras.adsid }, () => {
            //         sfrStatic.userData.adShareCount++;
            //         paras.sucCall && paras.sucCall();
            //         sfrStatic.userData.save();
            //         sfrStatisticsMgr.sendEvent(SsEventName.share, { name: "share_id", value: 2 + ''}, { name: "share_result", value: 1 + ''});
            //     }, ()=>{
            //         sfrStatisticsMgr.sendEvent(SsEventName.share, { name: "share_id", value: 2 + ''}, { name: "share_result", value: 0 + ''});
            //     });
            // } else {
            this.channelObj.showToast({
                title: '暂无广告',
            });
            // }
        }
        me.videoFns['onClose'] = (res) => {
            // 用户点击了【关闭广告】按钮
            // 小于 2.1.0 的基础库版本，res 是一个 undefined
            cc.game.resume();
            // sfrStatic.main.blinkEff.hideMask();
            blhAudioMgr.instance().resumeAll();
            let dur = Math.round((Date.now() - this.startTime) / 1000);
            blhStatisticsMgr.setUser_PlayVideoAdCount(1);
            blhStatisticsMgr.setUser_PlayVideoAdTime(dur)
            if (res && res.isEnded) {
                // 正常播放结束，可以下发游戏奖励
                sucCall && sucCall();
                endCall && endCall();
                // blhStatisticsMgr.sendEvent(SsEventName.ads_play_result, { name: "ad_id", value: id }, { name: "ad_name", value: posName }, { name: "play_result", value: 0 + '' }, { name: "ad_duration", value: dur });
                blhkc.eventReport('广告完成-' + posName);
                if (!blhkc.getData('ttAdReport', 0) || !me.channelUserId) {
                    console.log('需要先激活才能回传关键行为');
                    return;
                }
                let options = this.channelObj.getLaunchOptionsSync();
                let clickid = options.query.clickid;
                if (!clickid) {
                    console.log('无clickid');
                    return;
                }
                blhkc.jpost('https://ga.gamejym.com/fcm/ttAdReport/report', { appId: me.getPara('appId'), type: 'game_addiction', 'openId': me.channelUserId, 'clickid': clickid }, (err: any, resp: any) => {
                    if (err) {
                        blhkc.err(err);
                        return;
                    }
                    blhkc.log('回传关键行为返回', resp);
                });
            } else {
                // blhStatisticsMgr.sendEvent(SsEventName.ads_play_result, { name: "ad_id", value: id }, { name: "ad_name", value: posName }, { name: "play_result", value: 2 + '' }, { name: "ad_duration", value: dur });
                failCall && failCall();
                endCall && endCall();
            }
        };
        me.rewardedVideoAd.onLoad(me.videoFns['onLoad']);
        me.rewardedVideoAd.onError(me.videoFns['onError']);
        me.rewardedVideoAd.onClose(me.videoFns['onClose']);

        me.rewardedVideoAd.show()
            .then(() => {
                blhkc.log('激励视频 广告显示')
            }).catch(err => {
                me.rewardedVideoAd.load().then(() => {
                    blhkc.log('手动加载成功');
                    // 加载成功后需要再显示广告
                    return me.rewardedVideoAd.show().then(() => {
                    }).catch((err) => {
                        cc.game.resume();
                        // sfrStatic.main.blinkEff.hideMask();
                    });
                }).catch((err) => {
                    cc.game.resume();
                    // sfrStatic.main.blinkEff.hideMask();
                });
            });

    }


    showInter(isForce?: boolean): void {
        const interId = this.getPara('insertAd');
        if (!interId) {
            return;
        }
        const bl = this.channelObj;
        const p1 = (this.ctrl && this.ctrl.p1) ? (parseInt(this.ctrl.p1)) : 0;
        if (!p1 && !isForce) {
            blhkc.log('插屏不展示');
            return;
        }
        blhkc.log('showInter isShow', interId);
        this.interstitialAd = bl.createInterstitialAd({
            adUnitId: interId,
        });
        // this.interstitialAd.onError(err => {
        //     console.log("插屏广告加载失败" + JSON.stringify(err));
        // });
        this.interstitialAd.show().then(() => {
            blhkc.log('插屏广告展示完成');
        }).catch((err) => {
            blhkc.log('插屏广告展示失败' + JSON.stringify(err));
        });
    }

    closeInter(): void {
        if (this.interstitialAd && this.interstitialAd.close) {
            this.interstitialAd.close();
        }
    }

    showDesktop(parent: cc.Node, pos: cc.Vec2): void {
        // if (!this.getCtrl('p1')) {
        //     return;
        // }
        // const node = cc.instantiate(sfrResMgr.instance().getPrefab('desktop_bt'));
        // node.parent = parent;
        // node.setPosition(pos);
        // node.active = true;
    }
    checkShortcut(paras: { sucCall: any, failCall: any }) {
        (window as any).bl.checkShortcut({
            success(res) {
                if (res.status == 1) {
                    paras.sucCall();
                } else {
                    paras.failCall();
                }
            },
            fail(res) {
                paras.failCall();
            }
        });
    }

    addShortcut(paras: { sucCall: any, failCall: any }): void {
        this.channelObj.addShortcut({
            success() {
                blhkc.log('添加桌面成功');
                paras.sucCall();
            },
            fail(err) {
                blhkc.log('添加桌面失败:' + err.errMsg);
                paras.failCall();
            },
        });
    }

    /** 分享 */
    shareAppMessage(paras: { title: string, act: string, adsid: string }, sucCall: any = null, failCall: any = null) {
        if (!this.canShare()) {
            // sfrStatisticsMgr.sendEvent("Share", { name: "Shareid", value: parseInt(paras.adsid) }, { name: "ShareR", value: 0 });
            return;
        }
        const shareObj = this.getShareObj();
        let act = '';
        if (paras && paras.act) {
            act = paras.act;
        }

        if (!act) {
            this.shareFailBack = failCall;
            this.shareSucBack = sucCall;
            this.isShare = true;
            const that = this;
            //无指定act分享
            this.channelObj.shareAppMessage({
                // title: shareObj.title,
                // imageUrl: shareObj.url,
                // imageUrlId: shareObj.id, // 图片 URL
                success() {
                    console.log("11share succ");
                    that.shareSucBack && that.shareSucBack();
                },
                fail(e) {
                    console.log("22share fail");
                    that.shareFailBack && that.shareFailBack();
                }
            }
            );
            return;
        }

        //带act分享
        const me = this;
        me.shareFailBack = null;
        me.shareSucBack = null;
        me.isShare = false;
        if (paras.act == "recruit_share") {
            me.isShare = true;
            me.shareFailBack = failCall;
            me.shareSucBack = sucCall;
        }
        //注册一个分享到server
        const reqObj = {
            appId: this.getPara('appId'),
            openId: this.channelUserId,
            act,
        };
        blhkc.jpost('https://ga.gamejym.com/fcm/zijie/shareTo', reqObj, (err: any, resp: any) => {
            if (err) {
                blhkc.err(err);
                return;
            }
            blhkc.log('shareTo返回', resp);
            const sharePara = {
                title: shareObj.title,
                imageUrl: shareObj.url,
                imageUrlId: shareObj.id,
                query: null,
            }
            if (resp && resp.code === 0) {
                sharePara.query = 'sid=' + resp.data.sid + '&act=' + act;
                blhkc.log('shareQuery', sharePara.query);
            }
            me.channelObj.shareAppMessage({
                title: shareObj.title,
                imageUrl: shareObj.url,
                imageUrlId: shareObj.id,
                query: sharePara.query,
                success() {
                    console.log("act share succ");
                    me.shareSucBack && me.shareSucBack();
                },
                fail(e) {
                    console.log("act share fail");
                    me.shareFailBack && me.shareFailBack();
                }
            });
        });
    }

    showSidebar(call?: Function) {
        // let hasgetData = blhkc.getData(saveData.sidebar, { today: '', canGet: '-1' });
        // (window as any).bl.navigateToScene({
        //     scene: 'sidebar',
        //     success: (res) => {
        //         console.log("navigate to scene success");
        //         //跳转成功回调逻辑
        //         hasgetData.canGet = '1';
        //         blhkc.saveData(saveData.sidebar, hasgetData);
        //         call && call(true);
        //     },
        //     fail: (res) => {
        //         console.log("navigate to scene fail: ", res);
        //         //跳转失败回调逻辑
        //         hasgetData.canGet = '0';
        //         blhkc.saveData(saveData.sidebar, hasgetData);
        //         call && call(false)
        //     }
        // });
    }

    reportScene() {
        this.channelObj.reportScene({
            sceneId: 7,
            success(res) {
                // 上报接口执行完成后的回调，用于检查上报数据是否符合预期
                console.log(res)
            },
            fail(res) {
                // 上报报错时的回调，用于查看上报错误的原因：如参数类型错误等
                console.log(res)
            }
        })
    }

}