import { adAction, battleMode, saveData, ToolType } from "../mgr/blhConst";
import { blhStatic } from "../mgr/blhStatic";
import blhToast from "../view/blhToast";
import { blhChannel } from "./blhChannel";
import blhkc from "./blhkc";


export class blhChannelWeb {

    channel: string = 'web';

    paraMap: Map<String, string> = new Map([
        // ['gid', '63d4e261f9d13c5453ee7a17'], //gid
        // ['bid1', '63d73601f9d13c5453eec7cc'], //bid
        // ['bid2', '63d7361ff9d13c5453eec7ce'], //bid
    ]);

    // ruanzhu: string = '\n著作权人：南京天豆互动网络科技有限公司\n登记号：2022SR1111655';
    protected name: string = '水果切切切3D';
    ruanzhu: string = '著作权人:南京天豆互动网络科技有限公司\n登记号:2020SR0682872';
    company: string = '南京天豆互动网络科技有限公司';

    /** 是否读远程存档 */
    isReadRemoteUserData: boolean = true;

    channelId = 0;
    channelObj: any = window;

    /** 策略控制 */
    ctrl: any = null;

    interstitialAd: any = null;
    bannerAd: any = null;
    rewardedVideoAd: any = null;
    /** 原生banner */
    customAd1: any = null;
    /** 原生插屏 */
    customAd2: any = null;
    /** 9宫格 */
    boxPortalAd: any = null;

    // 渠道用户id
    channelUserId: string = null;

    //场景值
    scene: string = null;

    setParaMap(paraMap: Map<String, string>): void {
        this.paraMap = paraMap;
    }

    getPara(key: string): string {
        return this.paraMap.get(key);
    }
    getGameName() {
        return this.name;
    }
    login({ successCall, failCall }: { successCall: () => void, failCall: (code: number) => void }): void {
        successCall && successCall();
    }
    /** 获取软著文字 */
    getRuanZhu(): string {
        return this.ruanzhu;
    }

    exit() {
        //tt
        // this.channelObj.exitMiniProgram({
        //     isFullExit: true
        // });
    }

    sysInfo: any = null;
    /** getSystemInfoSync */
    getSysInfo(): any {
        if (!this.sysInfo) {
            this.sysInfo = { windowHeight: cc.view.getVisibleSize().height, windowWidth: cc.view.getVisibleSize().width };
        }
        return this.sysInfo;
    }


    // cMap = {
    //     'fakeLoading': true,
    // };

    /** 根据key返回不同渠道的控制参数,如key为vit时返回是否有体力 */
    getCPara(key: string): any {
        if (key == "fakeLoading") {
            return true;
        }
        return null;
    }


    /** 
     * 分渠道的广告特定动作，actId
     * @param actName 区分哪个动作
     * @param para 为执行动作需要的对象 
     */
    doAction(actName: string, para: any = null) {
        console.log("doAction:", actName);
       
    }

    // /** inter */
    // showIn(isForce: boolean = false, call?:()=>void): void {
    //     if(call){
    //         call && call();
    //         console.error("return return");
    //     }else{
    //         console.error("call 为空");
    //     }
    // }


    privacyStr: Array<string> = [`更新日期: 2021年12月12日 生效日期: 2021年12月12日

${this.company}（以下简称“我们”） 非常重视您的个人信息及隐私安全。 

我们制定本“隐私政策”希望您在向我们提供某些信息（其中很可能包括您的个人信息）以及允许我们处理并分享某些信息之前，能够清晰地了解这些信息收集的目的、可能的用途以及其他方面的内容。本隐私政策可能会不时更新。建议您定期查阅本隐私政策，以了解任何相关变更。

产品名称:

《${blhChannel.gameName}》

在此温馨提示您仔细阅读并理解本隐私政策。如果您有任何隐私方面的疑问，请使用以下联系方式:

电子邮件: <EMAIL>

公司地址:南京建邺区嘉陵江东街50号康缘智慧港3栋602

请您知悉，本政策仅适用于我们向您提供服务所收集的信息，不适用于通过接入我们产品或服务的第三方所收集的信息。

请您仔细阅读并充分理解相关内容：

为向您提供服务，我们将依据本隐私政策收集、使用、存储必要的信息。
基于您的明示授权，我们可能会申请开启您的设备权限，您有权拒绝或取消授权。
您可以访问、更正、删除您的个人信息，还可以撤回授权同意、注销账号、投诉举报以及调整其他隐私设置。
我们已采取符合业界标准的安全防护措施保护您的个人信息。
如您是未成年人，请您和您的监护人仔细阅读本隐私政策，并在征得您的监护人授权同意的前提下使用我们的服务或向我们提供个人信息。
设备权限调用情况

在您使用我们产品的过程中，我们需要在必要范围内向您申请获取设备权限。请您知悉，我们不会默认开启您设备的权限，仅在您主动确认开启的情况下，才有可能通过设备权限收集您的信息。调用的权限、调用权限的目的，以及调用权限前向您询问的情况如下：

设备权限	调用权限的目的	是否询问	用户可否关闭权限
联网能力及获取联网状态和Wi-Fi状态，以及网络相关信息, 如:IP地址、WLAN接入点(如SSID、BSSID等)	连接网络	是	可
读取电话状态,获取设备标识信息AndroidId,OAID,IMEI,mac地址等	识别用户，广告SDK需要	是	可
读取和写入外部存储	下载并保存内容	是	可
震动	用于提示用户或提供游戏体验	是	可
其他权限	广告SDK需要的权限	是	可
如您在首次授权开启权限后希望关闭权限，您可以在设备的设置功能中选择关闭权限，从而拒绝我们收集相应的个人信息。

1. 如何收集和使用个人信息

我们会按照如下方式收集您在使用服务时主动提供的，以及通过自动化手段收集您在使用功能或接受服务过程中产生的信息：

1.1 注册、登录

当您注册时我们会收集您的昵称、头像、手机号码、邮箱等，收集这些信息是为了帮助您完成注册保护您的帐号安全。手机号码属于敏感信息，收集此类信息是为了满足相关法律法规的网络实名制要求。若您不提供这类信息，您可能无法正常使用我们的服务。
您也可以使用微信、邮箱或者各平台ID登录并使用，您将授权我们获取您在第三方平台注册的公开信息（头像、昵称以及您授权的其他信息），使您可以直接登录并使用本产品和相关服务。
1.2 认证

在您使用身份认证的功能或服务时，根据相关法律法规，您可能需要提供您的真实身份信息：真实姓名、身份证号码、电话号码以完成实名验证。 这些信息属于个人敏感信息，您可以拒绝提供，但您将可能无法获得相关服务。

1.3 信息发布

您发布内容时，我们将收集您发布的信息，并展示您的昵称、头像、发布内容。

1.4 安全运行

1.4.1 安全保障功能

我们致力于为您提供安全、可信的产品与使用环境，提供优质而可靠的服务是我们的核心目标。为实现安全保障功能所收集的信息是必要信息。

1.4.2 日志信息

为了保障软件与服务的安全运行，我们会收集您的操作、使用、服务日志。

1.5 收集、使用个人信息目的变更

原则上，当新功能或服务与我们当前提供的功能或服务相关时，收集与使用的个人信息将与原处理目的具有直接或合理关联。在与原处理目的无直接或合理关联的场景下，我们收集、使用您的个人信息，会再次进行告知，并征得您的同意。

1.6 依法豁免征得同意收集和使用的个人信息

请您理解，在下列情形中，根据法律法规及相关国家标准，我们收集和使用您的个人信息无需征得您的授权同意：

与国家安全、国防安全直接相关的；
与公共安全、公共卫生、重大公共利益直接相关的；
与犯罪侦查、起诉、审判和判决执行等直接相关的；
出于维护个人信息主体或其他个人的生命、财产等重大合法权益但又很难得到本人同意的；
所收集的您的个人信息是您自行向社会公众公开的；
从合法公开披露的信息中收集的您的个人信息的，如合法的新闻报道、政府信息公开等渠道；
根据您的要求签订或履行合同所必需的；
用于维护软件及相关服务的安全稳定运行所必需的，例如发现、处置软件及相关服务的故障；
为合法的新闻报道所必需的；
学术研究机构基于公共利益开展统计或学术研究所必要，且对外提供学术研究或描述的结果时，对结果中所包含的个人信息进行去标识化处理的；
法律法规规定的其他情形。
2.对 Cookie 和同类技术的使用

我们可能会设置认证与保障安全性的Cookie或匿名标识符，以确认您是否安全登录服务，在确认您已正常登录的情况下才会向您提供服务，从而避免账号盗用、信息泄露等情况发生。
使用Cookie可以帮助您省去重复填写个人信息、输入搜索内容的步骤和流程。
您可以在浏览器“设置”功能中进行Cookie数据清除。如您进行清除，您可能无法使用我们提供的依赖于Cookie的相关服务和功能，但不影响其他功能与服务的正常使用。
3.我们如何共享、转让、公开披露个人信息

3.1共享

我们不会共享您的个人信息给任何其他第三方，除非征得您的明确同意。

3.2 转让

我们不会转让您的个人信息给任何其他第三方，除非征得您的明确同意。
随着我们业务的持续发展，我们将有可能进行合并、收购、资产转让，您的个人信息有可能因此而被转移。在发生前述变更时，我们将按照法律法规及不低于本隐私政策所载明的安全标准要求继受方保护您的个人信息，否则我们将要求继受方重新征得您的授权同意。
3.3 披露

我们不会公开披露您的信息，除非遵循国家法律法规规定或者获得您的同意。我们公开披露您的个人信息会采用符合行业内标准的安全保护措施.
对违规帐号、欺诈行为进行处罚公告时，我们会披露相关帐号的信息。
“寻人”、“追逃”等公益项目，经根据符合法律法规的规定和明确授权后，我们会公开披露与事件相关的信息。当事件结束后，我们将及时删除已披露的个人敏感信息。
3.4 依法豁免征得同意共享、转让、公开披露的个人信息

请您理解，在下列情形中，根据法律法规及国家标准，我们共享、转让、公开披露您的个人信息无需征得您的授权同意：

与国家安全、国防安全直接相关的；
与公共安全、公共卫生、重大公共利益直接相关的；
与犯罪侦查、起诉、审判和判决执行等直接相关的；
出于维护您或其他个人的生命、财产等重大合法权益但又很难得到本人同意的；
您自行向社会公众公开的个人信息；
从合法公开披露的信息中收集个人信息的，如合法的新闻报道、政府信息公开等渠道。
4.我们如何存储个人信息

4.1 存储地点

我们会按照法律法规规定，将境内收集的用户个人信息存储于中国境内。

4.2 存储期限

我们仅在提供服务期间保留您的个人信息；您发布的内容在您未删除或注销帐号期间会予以保留。超出必要期限后，您的个人信息会被删除或匿名化处理，但法律法规另有规定的除外。

5.我们如何保护个人信息的安全

我们努力为用户的信息和隐私安全提供包保障，防止信息的丢失、不当使用、未经授权访问或披露。
我们会以业界成熟的安全标准收集、使用、存储和传输用户信息：例如在进行网络传输时会对个人信息进行加密存储（SSL、md5等）。存在于Cookie里的个人安全认证信息也会被进行加密和混淆处理。
我们会建立专门的安全部门、安全管理制度和数据安全流程来保障您的个人信息安全。采取严格的数据访问和使用制度，确保只有授权人员才能访问您的个人信息，并适时对数据和技术进行安全审计。
我们会制定应急处理预案，并在发生用户信息安全事件时立即启动应急预案，努力阻止安全事件的影响扩大。一旦发生用户信息安全事件（泄露、丢失），我们将按照法律法规的要求及时向您告知。
6.管理您的个人信息

我们非常重视您对个人信息的管理，并尽全力保护您对于您个人信息的查询、访问、修改、删除、撤回同意授权、注销帐号、投诉举报以及设置隐私功能的相关权利，以使您有能力保障您的隐私和信息安全。

6.1 访问、删除、更正您的个人信息

6.1.1 访问、修改个人帐号信息

您可以查询、访问您的头像、用户名、简介，并且可以在我们产品的“帐号中心”中修改您的头像、简介。

6.1.2 删除发布的内容

删除发布内容：进入我们产品“内容管理”—点击文章右侧“操作”下的“下线”按钮。
特别提示您注意，出于安全性和身份识别（如号码申诉服务）的考虑，您可能无法自主修改注册时提交的某些初始注册信息。如您确有需要修改该类注册信息，请根据本隐私政策载明的联系方式联系我们。
6.2 注销帐号

您可以通过发送请求注销帐号的邮件至 ***************申请注销帐号。在您注销帐号前，我们将验证您的个人身份、安全状态、设备信息等。注销帐号的行为是不可逆的行为，当您注销帐号后，我们将删除您的相关信息或将您的信息进行匿名化处理，但法律法规另有规定的除外。

6.3 投诉举报

如果您认为您的个人信息权利可能受到侵害，或者发现侵害个人信息权利的线索，请通过以下邮箱地址 <EMAIL> 联系我们。我们核查后会及时处理您的投诉举报。

6.4 访问隐私政策

您可以在产品注册,登录,设置,关于等界面查看本隐私政策的全部内容。

6.5 停止运营向您告知

如我们停止运营，我们将及时停止收集您个人信息的活动，将停止运营的通知以公告的形式通知您，并对所持有的您的个人信息进行删除或匿名化处理。

7.未成年人条款

我们产品主要面向成年人。若您是未满18周岁的未成年人，在使用本产品及相关服务前，应在您的父母或其他监护人监护、指导下共同阅读并同意本隐私政策。
如您的监护人不同意您按照本政策使用我们的服务或向我们提供信息，请您立即终止使用我们的服务并及时通知我们，以便我们采取相应的措施。
若您是未成年人的监护人，当您对您所监护的未成年人的个人信息有相关疑问时，请通过本隐私政策公示的联系方式与我们联系。
8.隐私政策的修订和通知

我们会适时对本隐私政策进行修订，如该等变更会导致您在本政策项下权利的实质减损，我们会在变更生效前在页面显著位置通知您。
若您不同意该等变更可以停止使用我们的产品，若您继续使用我们的产品或服务，即表示您同意受修订后的本隐私政策的约束。`,


    //用户协议
    `更新日期: 2021年12月12日 生效日期: 2021年12月12日
    
        欢迎您使用由${this.company} 提供的服务。
        
        本《用户服务协议》（以下称“本协议”）是用户（以下称“您”）接受${this.company}（以下简称为“本公司”）所提供的服务所使用的法律服务条款。 本公司在此特别提醒您认真阅读本协议的全部条款，特别是其中免除或者限制本公司责任的免责声明条款以及用户须遵守的条款和其它限制用户权利的条款，这些条款应在中国法律所允许的范围内最大程度地适用。除非用户 接受本协议的全部条款，否则无权安装运行客户端软件或以其它方式使用本公司提供的任何服务内容。
        
        当您在线点击“同意”或“接受”后，则视为您已详细阅读并同意本协议的全部内容，并同意遵守本协议的规定。
        
        如果您未满18周岁，请在法定监护人的陪同下阅读本协议。
        
        一、特别提示
        
        本公司同意按照本协议的规定及其不时发布的操作规则提供基于互联网的相关 服务(以下称“网络服务”)，为获得网络服务，您同意本协议的全部条款并按照页面上的提示完成全部程序。您在进行程序过程中点击“同意”按钮即表示用户完全接受本协议项下的全部条款。这些条款可由本公司随时更新，协议条款一旦发生变动，本公司将会在相关的页面上提示变更内容。修改后的协议一旦在页面上公布即有效代替原来的协议。您在使用本公司提供的软件产品之前，应仔细阅读本协议，如不同意本服务协议及或随时对其的修改，请停止使用本公司提供的服务内容。
        特别申明，未成年人应在法定监护人的陪同下审阅和接受本协议。未成年人在使用本公司的服务前，应事先取得父母（监护人）的同意。若父母（监护人）希望未成年人（尤其是十岁以下子女）得以使用本服务，必须以法定监护人身份加以判断本服务是否符合于未成年人。未成年人用户应当在合理程度内使用本公司提供的服务内容，不得因使用本公司提供的服务内容而影响了日常的学习生活。用户理解本公司无义务对本款前述事项进行任何形式的审查和确认。
        二、权属声明
        
        本公司发布的所有软件均受中华人民共和国版权法保护，用户可以免费下载并使用，但未经本公司许可，用户不得修改本公司游戏的客户端程序；不得制作、传播或使用扰 乱正常游戏秩序或有损本公司利益的任何第三方软件。
        本公司游戏服务的经营权属于本公司所有，除非与本公司另签协议，用户同意本服务仅供个人且仅于本服务平台范围内非商业性质的使用。用户同时承诺不经本公司书面同意，不得利用本服务进行广告、销售、商业展示等商业性用途。
        用户发表于本公司游戏及配套网站的所有内容仅代表用户自己的立场和观点，与本公司无关，由用户本人承担一切法律责任。
        三、用户隐私制度
        
        本公司绝对不会修改用户的个人资料，或编辑或透露用户在注册资料中的密码、姓名、地址、电话、身份证号码及保存在本公司中的非公开内容，除非有法律许可要求或本公司在诚信的基础上认为透露这些信息在以下四种情况是必要的：
        
        遵守有关法律规定，遵从合法服务程序。
        维护本公司的商标所有权。
        在紧急情况下竭力维护用户个人和社会大众的隐私安全。
        符合其他相关的要求。
        四、游戏数据的特别规定
        
        本公司严禁以下行为：
        
        发布违法信息、严重违背社会公德、以及其他违反法律禁止性规定的行为；
        利用游戏作弊工具或者外挂、游戏BUG获取非法利益，严重侵犯本公司利益的行为；
        论坛、游戏中传播非法讯息、木马病毒、外挂软件等的行为；
        因游戏中举办的活动结束，相关道具被删除或兑换，用户不得提出索赔等要求，本公司对于活动有最终的解释权。
        五、服务的停止和更改
        
        发生下列情形之一时，本公司有权停止或中断向用户提供的服务：
        用户有发布违法信息、严重违背社会公德、以及其他违反法律禁止性规定的行为；
        发布不道德信息、广告、言论、辱骂骚扰他人，扰乱正常的网络秩序和游戏秩序的行为，以及实施违反本协议和相关规定、管理办法、公告、重要提示，对本公司和其他用户利益造成损害的其他行为；
        由于相关政府机构的要求。
        在本服务条款约定的情形下，本公司就停止或更改或终止向用户所提供的服务而可能产生的不便或损害，本公司对用户本人或任何第三人均不负任何损害赔偿责任。
        本公司有权仅根据其判断，单方决定新增、修改、删除、暂停或终止其所提供的全部或部分服务（包括且不限于增加、暂停或终止某个游戏的运营），且无需另行个别通知用户，用户不得因此要求任何补偿或赔偿。
        本公司对其服务不保证不出现任何程序BUG，并对由此可能产生的问题不承担任何赔偿责任。
        六、服务条款的修改
        
        本公司保留随时修改本服务条款的权利，修改本服务条款时，本公司将于相关网站公告修改的事实，而不另对用户进行个别通知。若用户不同意修改的内容，可停止使用本公司的服务。若用户继续使用本公司的服务，即视为用户已接受本公司所修订的内容。
        
        七、法律适用和争议解决
        
        本协议的订立、执行和解释及争议的解决均应适用中国法律。
        本协议构成双方对本协议之约定事项及其他有关事宜的完整协议，除本协议规定的之外，未赋予本协议各方其他权利。
        如本协议中的任何条款无论因何种原因完全或部分无效或不具有执行力，本协议的其余条款仍应有效并且有约束力。
        本协议中的标题仅为方便而设，不具法律或契约效果。
        如双方就本协议内容或其执行发生任何争议，双方应尽量友好协商解决；协商不成时，任何一方均可向本公司所在地的人民法院提起诉讼。
        在适用法律允许的最大范围内，本公司保留对本协议的最终解释权。用户如对本协议有任何疑问，请联系***************。
        `];

    /* 是否同意了隐私政策 */
    hasPrivacy(): boolean {
        return false;
    }

    /** 同意隐私政策 */
    privacyAgree(): number {
        if (!blhkc.getData('isAgree', 0)) {
            blhkc.saveData('isAgree', 1);
        }
        return 1;
    }
    privacyReject(): number {
        blhkc.saveData('isAgree', 0);
        return 0;
    }

    privacyPage = 0;
    privacyPageTxtCount = 1400;
    privacyPageSum = [Math.ceil(this.privacyStr[0].length / this.privacyPageTxtCount), Math.ceil(this.privacyStr[1].length / this.privacyPageTxtCount)];
    privacyNextPage(pageType: number): string {
        if (this.privacyPage + 1 < this.privacyPageSum[pageType]) {
            this.privacyPage++;
        }
        const start = this.privacyPage * this.privacyPageTxtCount;
        const out = this.privacyStr[pageType].substring(start, start + this.privacyPageTxtCount);
        return out;
    }
    privacyPrePage(pageType: number): string {
        if (this.privacyPage - 1 >= 0) {
            this.privacyPage--;
        }
        const start = this.privacyPage * this.privacyPageTxtCount;
        const out = this.privacyStr[pageType].substring(start, start + this.privacyPageTxtCount);
        return out;
    }
    privacyPage1(pageType: number): string {
        this.privacyPage = 0;
        return this.privacyStr[pageType].substring(0, this.privacyPageTxtCount);
    }

    getCtrl(key: string): number {
        return (this.ctrl && this.ctrl[key]) ? parseFloat(this.ctrl[key]) : 0;
    }

    getPrivacyShort(): string {
        return ``;
    }

    getPrivacy(): string {
        return ``;
    }

    init(loginCall: () => void) {
        blhkc.log('init1目标===>' + this.channel + ',R:' + blhChannel.isConfRemote + ',gid:' + this.getPara('gid'));
    }


    /** 是否在分享状态 */
    isShare: boolean = false;
    /** 分享消耗的时间 */
    shareTime: number = 0;
    /** 分享失败回调 */
    shareFailBack: any = null;
    /** 分享成功回调 */
    shareSucBack: any = null;
    /**
    * 切出去时间太短，false判断失败
    */
    isShareSuc(): boolean {
        let curTime = new Date().getTime();
        if (curTime - this.shareTime < 3 * 1000) {
            return false;
        }
        this.shareTime = new Date().getTime();
        return true;
    }

    /** 是否支持分享 */
    canShare(): boolean {
        return false;
    }

    /** 分享 */
    shareAppMessage(paras: { title: string, act: string, adsid: string }, sucCall: any = null, failCall: any = null) {
        sucCall && sucCall();
        // if (!this.canShare()) {
        //     return;
        // }
        // this.shareFnMap[this.channel](paras);
        // this.shareFailBack = failCall;
        // this.shareSucBack = sucCall;
        // this.isShare = true;
    }


    /** 复制到剪贴板 */
    copyToClip(contentStr: string, callback: (err?: string) => void) {
        const el = document.createElement('textarea');
        el.value = contentStr;

        // Prevent keyboard from showing on mobile
        el.setAttribute('readonly', '');
        //el.style.contain = 'strict';
        el.style.position = 'absolute';
        el.style.left = '-9999px';
        el.style.fontSize = '12pt'; // Prevent zooming on iOS

        const selection = getSelection()!;
        let originalRange;
        if (selection.rangeCount > 0) {
            originalRange = selection.getRangeAt(0);
        }

        document.body.appendChild(el);
        el.select();

        // Explicit selection workaround for iOS
        el.selectionStart = 0;
        el.selectionEnd = contentStr.length;

        let success = false;
        try {
            success = document.execCommand('copy');
            callback();
        } catch (err) {
            blhkc.err(err);
            callback('复制失败!');
        }

        document.body.removeChild(el);

        if (originalRange) {
            selection.removeAllRanges();
            selection.addRange(originalRange);
        }
    }


    /** 用于存放各个回调方法，便于off，如onLoad,onError等 */
    videoFns = {};

    showVideo(sucCall: any, failCall: any, endCall: any, posName: string) {
        // sfrStatic.main.blinkEff.hideMask();
        blhkc.log('showVideo 模拟成功');
        sucCall && sucCall();
        endCall && endCall();
    }
    getVideoShowDelay(): number {
        return 0;
    }
    addGird9Icon(parent: cc.Node, pos: cc.Vec2) {
    }

    gird9IconClick() {
    }

    /** 9宫格 */
    showGird9(): void {
    }

    /** 是否主动(定时)显示9宫格 */
    canAutoGird9(): boolean {
        return false;
    }

    closeGird9(): void {
    }

    showBanner(isForce: boolean = false, styleFn: any = null, where: string = null): void {
    }

    closeBanner(): void {
    }

    showBannerByTime(isForce: boolean = false) {

    }

    closeBannerByTime() {
    }

    checkNativeInsert(adKey: string): boolean {
        return false;
    }

    nativeAds = {};
    nativeAdListMap = {};
    nativeAdCallMap = {};
    nativeNodeMap = {};
    showNative(parent: cc.Node, pos: cc.Vec2, adKey: string, isForce: boolean = false, where: string = null) {
    }

    loadAndShowNative(parent: cc.Node, pos: cc.Vec2, adKey: string) {

    }

    loadNative(adKey: string, callback: (adList: any, newAdKey: string) => void) {
    }

    /** 找到当前任意一个原生并触发点击 */
    findAndReport() {
    }

    reportAdShow(adKey: string, node: cc.Node) {
    }

    reportAdClick(adKey: string) {

    }

    closeNative(adKey: string) {
    }

    /** native广告,adKey为paraMap中的key,返回原生load成功的资源 */
    getNativeData(adKey: string): any {

    }

    /** customAd */
    showCustomAd(isBanner: boolean, isForce: boolean = false, style: any = null): void {
    }
    closeCustomAd(isBanner: boolean = false): void {
    }


    /** inter */
    showInter(isForce: boolean = false): void {
    }
    closeInter(): void {
    }


    showDesktop(parent: cc.Node, pos: cc.Vec2): void {
    }


    addShortcut(paras: { sucCall: any, failCall: any }): void {
    }

    checkShortcut(paras: { sucCall: any, failCall: any }) {

    }

    recorderStart() {
    }

    recorderStop() {
    }

    /** 获取录屏时长，单位毫秒 */
    getRecordTime(): number {
        return 0;
    }
    shareVideo(callback: any = null) {
    }

    /** 互推跳转 */
    jumpTo(appId: string, sucCall = () => { }, failCall = () => { }, completeCall = () => { }) {

    }


    canMoreGame(): boolean {
        return false;
    }

    /** 显示更多游戏的盒子 */
    showMoreGameBox(parent: cc.Node, pos: cc.Vec2, isShow: boolean) {
    }

    showSidebar(call?: Function) {
        call && call();
    }

    jpost(url: string, data: any, callback?: any): void {
        blhkc.jpost(url, data, callback);
    }

    isOvPlatForm() {
        if (this.channel.indexOf('vivo') >= 0 || this.channel.indexOf('oppo') >= 0) {
            return true;
        }
        return false;
    }

    isWxTtPlantForm() {
        if (this.channel.indexOf('wx') >= 0 || this.channel.indexOf('tt') >= 0) {
            return true;
        }
        return false;
    }

    addCommon(sucCall?: any, failCall?: any) {
        sucCall && sucCall();
    }

}