const { ccclass, property } = cc._decorator;
@ccclass
export default class blhFrameAnim extends cc.Component {

    @property({ type: [cc.SpriteFrame] })
    spriteFrames: Array<cc.SpriteFrame> = [];
    @property({ tooltip: "每一帧时长" })
    duration: number = 0.1;
    @property(cc.Boolean)
    loop: boolean = false;
    @property(cc.Boolean)
    playOnload: boolean = false;
    @property(cc.Boolean)
    autoRemove: boolean = false;

    private endCallback: any = null;
    private sprite: cc.Sprite;
    private isPlaying: boolean = false;
    private playTime: number = 0;
    private spriteLen: number = 0;
    private spriteIndex: number = 0;


    onLoad() {
        if (!this.sprite) {
            this.sprite = this.node.getComponent(cc.Sprite);
            this.spriteLen = this.spriteFrames.length;
        }
        if (!this.sprite) {
            console.error('====[ERR]FrameAnim, 未配置sprite');
            return;
        }
        this.sprite.sizeMode = cc.Sprite.SizeMode.RAW;
        this.sprite.trim = false;
        // 判断是否是加载立即播放
        if (this.playOnload) {
            this.play();
        }
    }


    remove(){
        this.node.destroy();
    }
    
    setFrames(spfArr: cc.SpriteFrame[]): blhFrameAnim {
        this.spriteFrames = spfArr;
        this.spriteLen = this.spriteFrames.length;
        this.node.getComponent(cc.Sprite).spriteFrame = spfArr[0];
        return this;
    }

    /**
     * 每一帧时长
     * @param duration 
     * @returns 
     */
    setDuration(duration: number): blhFrameAnim {
        this.duration = duration;
        return this;
    }
    setLoop(isLoop: boolean): blhFrameAnim {
        this.loop = isLoop;
        return this;
    }
    setEndFn(endFn): blhFrameAnim {
        this.endCallback = endFn;
        return this;
    }

    play(): blhFrameAnim {
        if (this.spriteLen <= 0) {
            console.error('====[ERR]FrameAnim, spriteLen为空');
            return this;
        }
        this.isPlaying = true;
        return this;
    }
    stop() {
        this.isPlaying = false;
        return this;
    }


    reset(): blhFrameAnim {
        this.spriteIndex = 0;
        this.playTime = 0;
        this.sprite.spriteFrame = this.spriteFrames[0];
        return this;
    }


    updateFrame(dt: number) {
        if (!this.isPlaying) {
            return;
        }
        this.playTime += dt;
        if (this.playTime < this.duration) {
            return;
        }
        this.spriteIndex++;
        this.playTime = 0;
        if (this.spriteIndex >= this.spriteLen) {
            if (!this.loop) {
                this.isPlaying = false;
                if (this.endCallback) {
                    this.endCallback();
                }
                if (this.autoRemove) {
                    this.remove();
                }
                return;
            }
            this.spriteIndex = 0;
        }
        this.sprite.spriteFrame = this.spriteFrames[this.spriteIndex];
    }
}
