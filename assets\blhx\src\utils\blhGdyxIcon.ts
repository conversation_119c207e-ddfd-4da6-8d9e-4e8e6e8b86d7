import { blhChannel } from "./blhChannel";

const { ccclass, property } = cc._decorator;

@ccclass
export default class sfrGdyxIcon extends cc.Component {

    @property(cc.Node)
    ball: cc.Node = null;
    @property(cc.Label)
    txt: cc.Label = null;
    @property(cc.Sprite)
    pic: cc.Sprite = null;

    appId: string = null;
    adList: any = null;

    curId: number = 0;

    onLoad() {
        this.node.on(cc.Node.EventType.TOUCH_END, (e: cc.Event.EventTouch) => {
            blhChannel.instance().jumpTo(this.appId);
            this.ball.active = false;
        });
    }


    show(parent: cc.Node, pos: cc.Vec2) {
        this.node.active = true;
        this.node.parent = parent;
        this.node.setPosition(pos);
        //获取推荐列表，根据权重随机排序
        let isRandomSort = false; //是否使用权重算法排序后返回，否则按后台配置原样返回。默认为false,即按后台配置原样返回。
        let count = 0; //返回数量，如果全部返回可设置为0.默认全部返回
        let score = 0; // 根据需要传入当前玩家的等级|分数|游戏时长等数值
        // @ts-ignore
        hzzxsdk.getRecommedAdList(isRandomSort, count, score).then(adList => {
            //返回ad列表
            console.log('推荐列表广告：', adList);
            if (!adList || adList.length <= 0) {
                return;
            }
            this.adList = adList;
            this.showOneIcon();
            this.schedule(this.showOneIcon, 3, cc.macro.REPEAT_FOREVER);
        });
    }

    showOneIcon() {
        let item = this.adList[this.curId];
        if (!item) {
            this.curId = 0;
            item = this.adList[this.curId];
            if (!item) {
                return;
            }
        }

        this.shake();
        this.appId = item.appid;
        const imgUrl = item.image;
        if (!imgUrl) {
            return;
        }
        let type = '.png'
        if (imgUrl.indexOf('.jpg') != -1) {
            type = '.jpg'
        }
        cc.assetManager.loadRemote(imgUrl, { ext: type }, (err, res: any) => {
            if (err) {
                console.log('err==', err);
                return;
            }
            let spriteFrame = new cc.SpriteFrame(res);
            this.pic.spriteFrame = spriteFrame;
            this.txt.string = item.title || '';
        });
        
        this.curId++;
        if (this.curId >= this.adList.length) {
            this.curId = 0;
        }
    }

    shake() {
        cc.tween(this.node).repeat(5, cc.tween().to(0.05, { angle: 10 }).to(0.05, { angle: 0 }).to(0.05, { angle: -10 }).to(0.05, { angle: 0 })).start();
    }

}
