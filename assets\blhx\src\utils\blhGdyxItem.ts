import { blhChannel } from "./blhChannel";

const { ccclass, property } = cc._decorator;

@ccclass
export default class blhGdyxItem extends cc.Component {

    @property(cc.Node)
    gdyx_img: cc.Node = null;
    appId: string = null;
    page: string = null;

    onLoad() {
        this.node.on(cc.Node.EventType.TOUCH_END, this.on_tiaozhuan, this);
    }
    on_tiaozhuan() {
        blhChannel.instance().jumpTo(this.appId);
    }
    init(imgUrl, title, description, appId, page) {
        this.appId = appId;
        this.page = page;
        let type = '.png'
        if (imgUrl.indexOf('.jpg') != -1) {
            type = '.jpg'
        }
        cc.assetManager.loadRemote(imgUrl, { ext: type }, (err, res: any) => {
            if (err) {
                console.log('err==', err)
                return
            }
            let spriteFrame = new cc.SpriteFrame(res)
            this.gdyx_img.getComponent(cc.Sprite).spriteFrame = spriteFrame
        })
    }

}
