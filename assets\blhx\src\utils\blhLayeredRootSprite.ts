/**
 * 按层合批的渲染，如排行榜之类，本Sprite挂在根结点(一般是ScrollView/view[mask]/content上)，必须要挂个spriteFrame(可随便挂一个)，大小无所谓(width和height可为1)。
 * 其内部的合批，将按层级(node节点层级)进行，可以将图放在一个层级, label 放在图的内部一个层级，这样同一层级的将会合批。
 * 注意其中的所有label需要使用cacheMode，为char或Bitmap都可以，否则合批会被打断。
 * 注意其中的图需要在一个图集，否则合批会被打断。
 * 
 * 
 */


import blhLayeredAssembler from "./blhLayeredAssembler";



const {ccclass, property} = cc._decorator;

@ccclass
export default class sfrLayeredRootSprite extends cc.Sprite {
    onEnable() {
        super.onEnable();
        if (!CC_EDITOR){
            // @ts-ignore
            this.node._renderFlag |= cc.RenderFlow.FLAG_POST_RENDER;
        }

        // this.node._renderFlag |= cc.RenderFlow.FLAG_RENDER;
    }

    _resetAssembler() {
        // @ts-ignore
        this.setVertsDirty();
        // @ts-ignore
        let assembler = this._assembler = new blhLayeredAssembler();
        // @ts-ignore
        assembler.init(this);
    }
}
