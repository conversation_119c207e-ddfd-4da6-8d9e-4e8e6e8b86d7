import blhkc from "./blhkc";

/**
 * 解析条件运算字符串,求bool结果。
 * 带变量bool条件求结果,仅支持比较运算,变量仅支持独立变量名(不能有a.b或a[b]，也就是不能有.和[])，且变量值均为数字，支持数字直接参与比较
 *
 */
export class sfrParseCond {

    private static condMap = {
        '>': (a, b) => {
            // console.log('判断>', a, b);
            return a > b;
        },
        '<': (a, b) => {
            // console.log('判断<', a, b);
            return a < b;
        },
        '>=': (a, b) => {
            // console.log('判断>=', a, b);
            return a >= b;
        },
        '<=': (a, b) => {
            // console.log('判断<=', a, b);
            return a <= b;
        },
        '==': (a, b) => {
            // console.log('判断==', a, b);
            return a == b;
        },
        '=': (a, b) => {
            // console.log('判断==', a, b);
            return a == b;
        },
        '!=': (a, b) => {
            // console.log('判断!=', a, b);
            return a != b;
        },
        '!': (a, b) => {
            //a不作判断
            // console.log('判断!', a, b);
            return !b;
        },
    };

    private static orAndMap = {
        '&': (a, b) => a && b,
        '&&': (a, b) => a && b,
        '|': (a, b) => a || b,
        '||': (a, b) => a || b,
    };

    private static parseVar = function (inStr, ctx) {
        // if (typeof inStr === 'boolean') {
        //   return inStr;
        // }
        if (typeof inStr === 'number' || !isNaN(Number(inStr))) {
            return Number(inStr);
        }
        const out = ctx[inStr];
        if (out === undefined || typeof out !== 'number') {
            // console.log('inStr', inStr);
            throw 'parseVar失败,未知变量:' + inStr;
        }
        return Number(out);
    };

    private static scanBracketEnd = function (inStr) {
        //解析括号内的内容,定位结束位置
        let pairDeep = 0;
        const pairStart = 0; //必须是有'('后才进此方法判断，所以0位必定是'(';
        let pairEnd = -1;
        const len = inStr.length;
        for (let i = 0; i < len; i++) {
            const one = inStr[i];
            if (one === '(') {
                pairDeep++;
            } else if (one === ')') {
                pairDeep--;
                // console.log('pairDeep--', pairDeep, i);
                if (pairDeep === 0) {
                    pairEnd = i;
                    //括号范围确定
                    break;
                }
            }
        }
        // if (pairStart < 0) {
        //   console.log('未找到括号',inStr);
        //   return { 'outStr': inStr, 'endPos': len - 1 };
        // } else
        if (pairEnd < 0) {
            throw '[ERR]scanBracketEnd失败:' + inStr;
        }
        // console.log('pairDeep++', pairDeep, i);
        return { 'outStr': inStr.substring(pairStart + 1, pairEnd), 'endPos': pairEnd };
    };

    private static scanOrAndNext = function (inStr) {
        //向右找到第2个||或&& ，如果有括号则跳过
        let pairEnd = -1;
        const len = inStr.length;
        for (let i = 0; i < len; i++) {
            const one = inStr[i];
            if ((one === '|' && inStr[i + 1] === '|') || (one === '&' && inStr[i + 1] === '&')) {
                //找到结束
                pairEnd = i;
                break;
            } else if (one === '(') {
                const bRe = sfrParseCond.scanBracketEnd(inStr.substring(i));
                i = bRe.endPos + 1;
            }
        }
        if (pairEnd < 0) {
            return { 'outStr': inStr, 'endPos': len - 1 };
        }
        return { 'outStr': inStr.substring(0, pairEnd), 'endPos': pairEnd };
    };



    private static parseCond = function (inStr, ctx) {
        // console.log('开始parseCond', inStr);
        let left = '';
        let condFn = null;
        let right = '';
        let state = -1; //-1为left查找中 ， 1为right查找中
        let lastResult = null;
        for (let i = 0, len = inStr.length; i < len; i++) {
            let one = inStr[i];
            let theCondFn = sfrParseCond.condMap[one];
            if (theCondFn) {
                if (i + 1 !== len && sfrParseCond.condMap[one + inStr[i + 1]]) {
                    one = one + inStr[i + 1];
                    theCondFn = sfrParseCond.condMap[one];
                    i++;
                }
                state = 1;
                condFn = theCondFn;
                // console.log('找到cond', one, 'left:' + left);
                continue;
            }
            let orAnd = sfrParseCond.orAndMap[one];
            if (orAnd) {
                if (i + 1 !== len && sfrParseCond.orAndMap[one + inStr[i + 1]]) {
                    one = one + inStr[i + 1];
                    orAnd = sfrParseCond.orAndMap[one];
                    i++;
                }
                if (i + 1 >= len || (state < 0 && lastResult === null)) {
                    throw '[ERR]parseCond失败,无效orAnd位置:' + i + '|' + inStr;
                }
                // console.log('state', state, condFn);
                const orAndRe = sfrParseCond.scanOrAndNext(inStr.substring(i + 1));
                if (state > 0) {
                    //这里意味着right结束，可执行condFn
                    lastResult = condFn(sfrParseCond.parseVar(left, ctx), sfrParseCond.parseVar(right, ctx));
                    left = '';
                    right = '';
                    condFn = null;
                    // console.log('lastResult1:' + lastResult);
                }
                lastResult = orAnd(lastResult, sfrParseCond.parseCond(orAndRe.outStr, ctx));
                // console.log('lastResult2:' + lastResult, orAndRe.outStr);
                i = i + orAndRe.endPos + 1;
                state = -1;
                continue;
                //TODO 这里直接orAnd不正确，需要将前部分打入暂存，继续定位到比较的右端结束，
                // return orAnd(re1, parseCond(inStr.substring(i + 1), ctx));
            }
            if (one === '(') {
                //解析括号内的内容,定位结束位置
                const bracketRe = sfrParseCond.scanBracketEnd(inStr.substring(i));
                if (bracketRe.endPos + 2 === len) {
                    if (state > 0 && condFn) {
                        //这里处理!方法
                        return condFn(left, sfrParseCond.parseCond(bracketRe.outStr, ctx));
                    }
                    return sfrParseCond.parseCond(bracketRe.outStr, ctx);
                } else {
                    //后面还有
                    lastResult = sfrParseCond.parseCond(bracketRe.outStr, ctx);
                    if (state > 0 && condFn) {
                        //这里处理!方法
                        lastResult = condFn(left, sfrParseCond.parseCond(bracketRe.outStr, ctx));
                    }
                    // console.log('lastResult3:' + lastResult, bracketRe.outStr);
                    state = -1;
                    i = i + bracketRe.endPos + 1;
                    continue;
                }
                // console.log('pairDeep++', pairDeep, i);
            }
            if (state < 0) {
                left += one;
                // console.log('找到left', left);
            } else if (state > 0) {
                right += one;
                // console.log('找到right', right);
            }
        }
        if (state < 0 || !condFn) {
            if (lastResult !== null) {
                // console.log('lastResult4:' + lastResult, inStr);
                return lastResult;
            }
            // throw '[ERR]parseCond失败:' + inStr;
            blhkc.err('[ERR]parseCond失败:' + inStr,ctx);
            return null;
        }
        return condFn(sfrParseCond.parseVar(left, ctx), sfrParseCond.parseVar(right, ctx));
    };

    /**
     * 解析条件运算,求bool结果。
     * 带变量bool条件求结果,仅支持比较运算,变量仅支持独立变量名(不能有a.b或a[b]，也就是不能有.和[])，且变量值均为数字，支持数字直接参与比较
     *
     */
    public static condParse = function (inStr, ctx) {
        if (!inStr) {
            return true;
        }
        //先定位到括号,从子括号开始
        inStr = inStr.replace(/\s/g, ''); //去掉空格
        // return parsePair(inStr, ctx);
        return sfrParseCond.parseCond(inStr, ctx);
    };


    static test = function () {
        const a1 = 12;
        const b1 = 23;
        const c1 = 2;
        const d1 = 3;
        const e1 = 5;
        const f1 = 15;
        const g1 = 20;
        const ctx = { a1, b1, c1, d1, e1, f1, g1 };
        const realRe = a1 > b1 || !((b1 < a1 || c1 > f1) || b1 > a1);
        blhkc.log('[realRe]=====', realRe);
        blhkc.log('[parseCond]=====', sfrParseCond.condParse('a1 > b1 || !((b1 < a1 || c1 > f1) || b1 > a1 )', ctx));
    };


}
