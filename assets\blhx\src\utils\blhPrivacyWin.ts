import { blhChannel } from "./blhChannel";
import { adAction, Msg } from "../mgr/blhConst";
import blhkc from "./blhkc";
import blhResMgr from "../mgr/blhResMgr";

const { ccclass, property } = cc._decorator;

@ccclass
export default class blhPrivacyWin extends cc.Component {


    @property(cc.Node)
    txt_main: cc.Node = null;
    @property(cc.Node)
    bt_agree: cc.Node = null;
    @property(cc.Node)
    bt_reject: cc.Node = null;
    @property(cc.Node)
    win2: cc.Node = null;

    @property(cc.RichText)
    txt: cc.RichText = null;
    @property(cc.Node)
    bt_next: cc.Node = null;
    @property(cc.Node)
    bt_pre: cc.Node = null;
    @property(cc.Node)
    close: cc.Node = null;
    @property(cc.Node)
    bt_type1: cc.Node = null;
    @property(cc.Node)
    bt_type2: cc.Node = null;
    @property(cc.ScrollView)
    scrollView: cc.ScrollView = null;


    /** 隐私政策还是用户协议,0为前者，1为后者 */
    pType: number = 0;

    isSendMsg: boolean = true;

    static isShow: boolean = false;

    onLoad() {

        this.close.on(cc.Node.EventType.TOUCH_END, (e: cc.Event.EventTouch) => {
            this.closeDetail();
        });

        this.bt_pre.on(cc.Node.EventType.TOUCH_END, (e: cc.Event.EventTouch) => {
            this.txt.string = blhChannel.instance().privacyPrePage(this.pType);
            this.scrollView.scrollToTop();
        });

        this.bt_next.on(cc.Node.EventType.TOUCH_END, (e: cc.Event.EventTouch) => {
            this.txt.string = blhChannel.instance().privacyNextPage(this.pType);
            this.scrollView.scrollToTop();
        });

        this.bt_type1.on(cc.Node.EventType.TOUCH_END, (e: cc.Event.EventTouch) => {
            this.pType = 0;
            this.showType();
        });
        this.bt_type2.on(cc.Node.EventType.TOUCH_END, (e: cc.Event.EventTouch) => {
            this.pType = 1;
            this.showType();
        });


        this.txt.string = blhChannel.instance().privacyPage1(this.pType);

        this.bt_agree.on(cc.Node.EventType.TOUCH_END, (e: cc.Event.EventTouch) => {
            this.agree();
        });

        this.bt_reject.on(cc.Node.EventType.TOUCH_END, (e: cc.Event.EventTouch) => {
            this.node.active = false;
            blhChannel.instance().privacyReject();
            blhChannel.instance().exit();
        });

        this.txt_main.on(cc.Node.EventType.TOUCH_END, (e: cc.Event.EventTouch) => {
            this.win2.active = true;
        });
        this.win2.active = false;

        this.txt_main.getComponent(cc.RichText).string = '欢迎您进入《' + blhChannel.instance().getGameName() + '》，我们非常重视您的个人信息和隐私保护。为更好地保障您的个人权益，请您仔细阅读我们的<color=#009EFF>《用户服务协议》</c>和<color=#009EFF>《隐私政策》</c>。通过这些条款您可以了解我们如何收集、使用、保护和管理这些信息。点击“同意”即表示您已阅读完毕并同意上述协议和政策中的全部内容。';


        this.bt_type1.color = new cc.Color().fromHEX('#98B7FF');
    }

    showType() {
        this.txt.string = blhChannel.instance().privacyPage1(this.pType);
        if (this.pType === 0) {
            this.bt_type1.color = new cc.Color().fromHEX('#98B7FF');
            this.bt_type2.color = cc.Color.WHITE;
        } else {
            this.bt_type1.color = cc.Color.WHITE;
            this.bt_type2.color = new cc.Color().fromHEX('#98B7FF');
        }
    }

    closeDetail() {
        this.win2.active = false;
    }

    protected onDisable(): void {
        blhPrivacyWin.isShow = false;
    }

    agree() {
        blhChannel.instance().privacyAgree();
        this.node.active = false;
        console.log('agree', this.isSendMsg);
        if (this.isSendMsg) {
            blhkc.sendMsg(Msg.closeLoading);
        }
        blhChannel.instance().doAction(adAction.privacy_agree);
    }
    public static show(parent: cc.Node, isSendMsg: boolean = true) {
        if (blhPrivacyWin.isShow) {
            return;
        }
        blhPrivacyWin.isShow = true;
        blhResMgr.ins().getNodeAsync('privacyWin', (err: any, node: cc.Node) => {
            if (err) {
                console.log(err);
                return;
            }
            node.parent = parent;
            if (!isSendMsg) {
                node.getComponent(blhPrivacyWin).isSendMsg = false;
            }
        });
    }

}
