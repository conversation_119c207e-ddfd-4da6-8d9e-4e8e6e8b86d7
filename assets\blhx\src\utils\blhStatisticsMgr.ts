import { saveData, SsEventName } from "../mgr/blhConst";
import { blhStatic } from "../mgr/blhStatic";
import { blhChannel } from "./blhChannel";
import blhkc from "./blhkc";

export class blhStatisticsMgr {

    static openInit: boolean = false;
    /** 数数统计初始化 */
    public static init() {

        const config = {
            appId: "3bb5083565ac4ada9357113e3d9b6566", // 项目 APP ID
            serverUrl: "https://ss.gamejym.com", // 上报地址
            autoTrack: {
                // appLaunch: true, // 自动采集 ta_mp_launch
                appShow: true, // 自动采集 ta_mp_show
                appHide: true, // 自动采集 ta_mp_hide
                // pageShare: true, // 自动采集 ta_mp_share
            },
            enableLog: false, //不输出日志
        };


        // 初始化
        blhStatisticsMgr.openInit = true;
        let isNewUser = blhkc.getData('isNew', 0) === 0;
        this.setUserProp(true);
        this.initPubProp(isNewUser);
        this.setPubProp();
        this.sendEvent(SsEventName.login, { name: "is_new", value: isNewUser });
        // 设置统计用户属性, 此时还未登录
    }

  
    public static afterLogin() {
        if (!this.openInit) {
            return;
        }
        const openid = blhChannel.instance().getChannel().channelUserId;
        if (openid) {
            console.log('statist login: ', openid);
            // 设置统计用户属性
            // TDAnalytics.login(openid);
            this.setUserProp(false);
            this.initPubProp(blhkc.getData('isNew', 0) === 0);
        }
    }

    // 设置用户属性
    public static setUserProp(isInit: boolean): void {
        if (!this.openInit) {
            return;
        }
        // return;  twtrace_id  game_id没有上报
        let channel = blhChannel.instance().getChannel();
        let properties = {
            // openid: channel.channelUserId,
            // game_version: sfrChannel.version,
        };
        if (channel.channelUserId) {
            properties['openid'] = channel.channelUserId;
        }
        if (isInit) {
            properties['twtrace_id'] = channel.scene || '';
            properties['trace_time'] = new Date();
            properties['channel'] = blhChannel.instance().channel;
            properties['game_version'] = blhChannel.version;
            // properties['game_id'] = '001';
           
            if (blhkc.getData('isNew', 0) === 0) {
                properties['is_new'] = true;
                blhkc.saveData('isNew', 1);
            } else {
                properties['is_new'] = false;
            }
            // TDAnalytics.userSetOnce({ properties: properties });
        } else {
            properties['current_level'] = blhkc.getData(saveData.curRank, 0);
            // properties['current_level_llk'] = blhkc.getData(saveData.curLLKRank, 0);
            properties['current_level_days'] = blhStatic.userData.stayDay;

            // TDAnalytics.userSet({ properties: properties });
        }
    }

    //初始静态公共属性
    public static initPubProp(isNew: boolean) {
        if (!this.openInit) {
            return;
        }
        var superProperties = {
            is_new: isNew,
            channel: blhChannel.instance().channel, //字符串
        };
        if (blhChannel.instance().getChannel().channelUserId) {
            superProperties['openid'] = blhChannel.instance().getChannel().channelUserId;
        }
        // TDAnalytics.setSuperProperties(superProperties);
    }


    // 设置动态公共属性
    public static setPubProp(): void {
        if (!this.openInit) {
            return;
        }
        var superProperties = {
            level: blhStatic.userData.level,
            level_llk: blhStatic.userData.level_llk,
        };
        // TDAnalytics.setDynamicSuperProperties(superProperties);
    }

    /**添加广告次数 */
    public static setUser_PlayVideoAdCount(count: number) {
        if (!this.openInit) {
            return;
        }
        // TDAnalytics.userAdd({
        //     properties: {
        //         total_ad_num: count
        //     }
        // });
    }

    /**添加观看广告总时长 */
    public static setUser_PlayVideoAdTime(time: number) {
        if (!this.openInit) {
            return;
        }
        // TDAnalytics.userAdd({
        //     properties: {
        //         total_ad_time: time
        //     }
        // });
    }


    // 发送事件
    public static sendEvent(eventName: string, ...props: Array<{ name: string, value: any }>): void {
        if (!this.openInit) {
            return;
        }
        // this.setPubProp();
        let properties = {};

        if (props != null) {
            props.forEach((prop) => {
                properties[prop.name] = prop.value;
            });
        }
        // TDAnalytics.track({
        //     eventName: eventName,
        //     properties: properties
        // });
    }
}