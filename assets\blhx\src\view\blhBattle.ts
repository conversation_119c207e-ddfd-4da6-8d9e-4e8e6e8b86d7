import blhWaveData from "../entity/blhWaveData";
import blhHero from "../hero/blhHero";
import blhEnemy from "../items/blhEnemy";
import blhLiveTarget from "../items/blhLiveTarget";
import blhColliMgr from "../mgr/blhColliMgr";
import { BattleState, saveData } from "../mgr/blhConst";
import blhEnemyMgr from "../mgr/blhEnemyMgr";
import blhResMgr from "../mgr/blhResMgr";
import blhSkillMgr from "../mgr/blhSkillMgr";
import { blhStatic } from "../mgr/blhStatic";
import blhkc from "../utils/blhkc";


/**
 * Battle基类
 */
const { ccclass, property } = cc._decorator;

@ccclass
export default class blhBattle extends cc.Component {
    @property(cc.ProgressBar)
    progress_exp: cc.ProgressBar = null;
    @property(cc.ProgressBar)
    progress_hp: cc.ProgressBar = null;

    /** 战斗当前秒数 */
    @property(cc.Label)
    t_sec: cc.Label = null;

    @property(cc.Node)
    layer_ground: cc.Node = null;
    @property(cc.Node)
    layer_enemy: cc.Node = null;
    @property(cc.Node)
    layer_bullet: cc.Node = null;
    @property(cc.Node)
    layer_effect: cc.Node = null;
    @property(cc.Node)
    layer_dmgTxt: cc.Node = null;
    @property(cc.Node)
    layer_pop: cc.Node = null;
    @property(cc.Node)
    blaskMask: cc.Node = null;

    @property(cc.Node)
    deck: cc.Node = null;

    // @property(cc.Node)
    // top: cc.Node = null;

    @property(cc.Node)
    bottom: cc.Node = null;
    /** 甲板,用于标定敌人攻击线 */
    @property(cc.Node)
    board: cc.Node = null;

    @property(cc.Label)
    t_rank: cc.Label = null;
    @property(cc.Label)
    t_wave: cc.Label = null;
    @property(cc.Label)
    t_hp: cc.Label = null;

    @property(cc.Node)
    bt_speed: cc.Node = null;
    @property(cc.Node)
    bt_race: cc.Node = null;
    @property(cc.Node)
    bt_auto: cc.Node = null;
    @property(cc.Node)
    bt_pause: cc.Node = null;

    @property(cc.Label)
    lab_gameSpeed: cc.Label = null;


    // mode: battleMode = battleMode.normal;

    /** 战斗是否结束 */
    isEnd: boolean = false;
    /** 是否已经复活 */
    isRevived: boolean = false;

    chapter: number = 0;

    part: number = 0;

    /** 是否正在处理失败,防止gameFail执行多次 */
    protected isFailing: boolean = false;

    /** 下一波时间,为-1时表示没有下一波了 */
    private nextWaveSec: number = 0;
    /** 战斗时间 */
    private battleSec: number = 0;
    /** 当前波次 */
    private curWave: blhWaveData = null;
    /** 总波数 */
    waveCount: number = 0;
    /** 经验值 */
    exp: number = 0;
    /** 生命 */
    hp: number = 0;
    hpMax: number = this.hp;
    /** 等级 */
    level: number = 0;
    /** 下一级需要的经验值 */
    nextExp: number = 20;
    /** 击杀数量 */
    killCount: number = 0;

    enemyMap: Map<string, blhEnemy> = new Map();

    /** 所有可攻击目标,key为crtLiveTarget的uuid */
    targetMap: Map<string, blhLiveTarget> = new Map();

    /** 当前英雄 */
    heroArr: blhHero[] = [];

    // protected guideFinger: cc.Node = null;
    // protected guideStep: number = 0;
    // /** 引导穿透防快速点击 */
    // protected guideClickLock: boolean = false;

    // protected rankStartTime: number = 0;

    /** 最近的目标数组 */
    nearTargetArr: cc.Node[] = [];
    /** 最近的目标数量 */
    nearTargetCount: number = 2;

    updateTargetArr() {
        this.nearTargetArr = this.findNearEnemy(this.nearTargetCount);
    }


    init() {
        // if (blhkc.isNewDay(blhkc.getData(saveData.todayloginTime, 0))) {
        //     blhkc.saveData(saveData.share_count, 0);
        // }

    }

    protected onLoad(): void {
        this.bt_speed.on(cc.Node.EventType.TOUCH_END, this.changeSpeed, this);
        this.bt_pause.on(cc.Node.EventType.TOUCH_END, () => {
            if (blhStatic.battleState === BattleState.pause) {
                this.resume();
            } else {
                this.pause();
            }
        }, this);
    }

    /**改变游戏速度 */
    changeSpeed() {
        blhStatic.gameSpeed++;
        if (blhStatic.gameSpeed > 4) {
            blhStatic.gameSpeed = 1;
        }
        blhkc.changeGameSpeed(blhStatic.gameSpeed);
        this.lab_gameSpeed.string = "X" + blhStatic.gameSpeed;
    }


    /**  战斗开始 */
    battleStart(chapter: number, part: number) {
        console.log('battleStart', chapter, part);
        this.chapter = chapter;
        this.part = part;
        this.battleSec = 0;
        this.curWave = null;
        const waveArr = blhStatic.rankMgr.getWaveArr(chapter, part);
        this.nextWaveSec = waveArr[0].startTime;
        this.isEnd = false;
        blhStatic.battleState = BattleState.run;
        this.t_sec.string = '0';
        this.waveCount = waveArr[waveArr.length - 1].wave;
        this.t_wave.string = '第 1/' + this.waveCount + ' 波';
        this.t_rank.string = chapter + '-' + part;


        this.level = 0;
        this.exp = 0;
        this.nextExp = blhStatic.rankMgr.getExp(this.level).exp;
        this.progress_exp.progress = 0;
        this.killCount = 0;

        this.hp = 5000;
        this.hpMax = this.hp;
        this.progress_hp.progress = 1;
        this.t_hp.string = '' + this.hp;

        blhColliMgr.ins().setOn();
        
        // const initConf = blhResMgr.ins().getConf('a1')[0].initConf;
        // const testHero = initConf.hero;
        const testHero = [
            // { id: 1, x: -250, y: -70 },
            // { id: 2, x: -120, y: 0 },
            // { id: 3, x: 10, y: -70 },
            // { id: 4, x: 120, y: 0 },
            { id: 7, x: 250, y: -70 },
        ];
        
        const heros = blhkc.getData(saveData.heros, testHero); //FIXME: 暂时用testHero
        for (let i = 0; i < heros.length; i++) {
            const heroObj = heros[i];
            blhHero.create(heroObj.id, this.deck, heroObj.x, heroObj.y, (err, hero?: blhHero) => {
                if (err) {
                    return;
                }
                hero.battleStart();
                this.heroArr.push(hero);
            });
        }

        this.scheduleOnce(() => {
            blhStatic.enemy_atk_y = blhkc.getPosFromWorldPos(this.board, this.layer_enemy).y + this.board.height / 2;
            // this.progress_hp.node.parent = this.layer_enemy; //测试这个位置
            // this.progress_hp.node.y = blhStatic.enemy_atk_y;
        });

        //每0.5秒更新最近的目标数组
        this.updateTargetArr();
        this.schedule(this.updateTargetArr.bind(this), 0.5, cc.macro.REPEAT_FOREVER);
    }

    /** 加经验 */
    addExp(expAdd: number) {
        this.exp += expAdd;
        const expDiff = this.exp - this.nextExp;
        if (expDiff >= 0) {
            this.exp = expDiff;
            this.upgrade();
        }
        this.progress_exp.progress = this.exp / this.nextExp;
    }

    /** 升级 */
    upgrade() {
        this.level++;
        console.log('升级', this.level, '杀敌数', this.killCount);
        const nextExp = blhStatic.rankMgr.getExp(this.level);
        this.nextExp = nextExp.exp;
        //普通升级,3个选项
        blhSkillMgr.ins().showC3(3);
    }

    /** 受击 */
    hurt(dmg: number) {
        this.hp -= dmg;
        if (this.hp < 0) {
            this.hp = 0;
            this.gameFail();
        }
        this.progress_hp.progress = this.hp / this.hpMax;
        this.t_hp.string = '' + this.hp;
    }

    pause() {
        blhStatic.battleState = BattleState.pause;
        for (let i = 0; i < this.heroArr.length; i++) {
            const hero = this.heroArr[i];
            hero.pause();
        }
    }

    resume() {
        blhStatic.battleState = BattleState.run;
        for (let i = 0; i < this.heroArr.length; i++) {
            const hero = this.heroArr[i];
            hero.resume();
        }
    }

    /** 创建一波 */
    createWave() {
        //更新当前波
        this.curWave = (this.curWave) ? this.curWave.nextWave : blhStatic.rankMgr.getWaveArr(this.chapter, this.part)[0];
        if (this.curWave.nextWave) {
            this.nextWaveSec = this.curWave.nextWave.startTime;
            console.log('createWave下一波', this.nextWaveSec, this.curWave);
        } else {
            //本波是最后一波
            this.nextWaveSec = -1;
        }
        this.t_wave.string = '第 ' + this.curWave.wave + '/' + this.waveCount + ' 波';
        //创建敌人 
        const enemyArr = this.curWave.enemyIds;
        for (let i = 0; i < enemyArr.length; i++) {
            const arr = enemyArr[i];
            const id = arr[0];
            let count = arr[1];
            for (let j = 0; j < count; j++) {
                const y: number = blhStatic.halfViewHeight + blhkc.randomInt(50, 100);
                const x: number = blhkc.randomInt(-blhStatic.halfViewWidth + 30, blhStatic.halfViewWidth - 30);
                const gap: number = blhkc.randomInt(blhStatic.createEnemyGapFrom, blhStatic.createEnemyGapTo);
                this.scheduleOnce(() => {
                    const enemy = blhEnemyMgr.ins().createEnemy(id, x, y, this.layer_enemy);
                    this.enemyMap.set(enemy.node.uuid, enemy);
                }, gap);
            }
        }
    }

    // fingerPointAt(x: number, y: number) {
    //     this.guide_click.setPosition(x, y);
    //     if (!this.guideFinger) {
    //         this.guideFinger = blhResMgr.instance().createPicNode('guide_finger');
    //         this.guideFinger.parent = this.layer_guide;
    //     }
    //     this.guideFinger.anchorX = 0;
    //     this.guideFinger.anchorY = 1;
    //     this.guideFinger.setPosition(x, y);
    //     cc.Tween.stopAllByTarget(this.guideFinger);
    //     cc.tween(this.guideFinger).repeatForever(cc.tween().to(0.3, { x: x + 20, y: y - 20 }).to(0.3, { x: x, y: y })).start();

    // }

    // showGuideInfo(txt: string) {
    //     this.t_guide.string = txt;
    //     this.guide_info.active = true;
    //     this.guide_info.scale = 0.1;
    //     cc.Tween.stopAllByTarget(this.guide_info);
    //     cc.tween(this.guide_info).to(0.2, { scale: 1 }, { 'easing': 'backOut' }).start();
    // }

    // hideGuideInfo(callback: () => void) {
    //     cc.Tween.stopAllByTarget(this.guide_info);
    //     cc.tween(this.guide_info).to(0.2, { scale: 0.1 }, { 'easing': 'backIn' }).call(() => {
    //         this.guide_info.active = false;
    //         callback();
    //     }).start();
    // }
    // showGuide() {
    //     this.top.active = false;
    //     this.bottom.active = false;
    //     this.layer_guide.active = true;

    //     //注意mask3穿透区域必须足够小，防止点到边缘时水果未被点中!!!

    //     this.guide_click['_touchListener'].setSwallowTouches(false); //默认点击可穿透

    //     this.guideDoStep();
    //     if (this.mode === battleMode.normal) {
    //         blhStatisticsMgr.sendEvent(SsEventName.loading, { name: "loading_step", value: 4 });
    //     }

    // }

    // guideDoStep() {

    // }

    // closeGuide() {
    //     this.hideGuideInfo(() => {
    //         this.top.active = true;
    //         this.bottom.active = true;
    //         this.layer_guide.active = false;
    //     });
    // }


    /** 这里只从nearTargetArr中找 */
    findNearestEnemy(): cc.Node {
        return this.nearTargetArr[0] || null;
    }

    /** 原始的遍历所有敌人 */
    findNearestEnemyFromAll(): cc.Node {
        let out: cc.Node = null;
        let y: number = 10000;
        const topY: number = blhStatic.halfViewHeight - 100;
        for (let i = 0; i < this.layer_enemy.childrenCount; i++) {
            const eNode: cc.Node = this.layer_enemy.children[i];
            if (!eNode || eNode.y > topY || !eNode.isValid) {
                continue;
            }
            if (eNode.y < y) {
                y = eNode.y;
                out = eNode;
            }
        }
        return out;
    }

    /** 最近的n个敌人，注意num不能太大,否则效率不高 */
    findNearEnemy(num: number): cc.Node[] {
        const out: cc.Node[] = [];
        const topY = blhStatic.halfViewHeight - 100;
        if (num <= 0) {
            return out;
        }
        for (let i = 0; i < this.layer_enemy.childrenCount; i++) {
            const eNode = this.layer_enemy.children[i];
            // 跳过无效节点
            if (!eNode || !eNode.isValid || eNode.y > topY) {
                continue;
            }

            //排序，当out要变化时需要重排序，其他情况不需要处理
            if (out.length < num) {
                out.push(eNode);
                out.sort((a, b) => a.y - b.y);
            } else if (eNode.y < out[0].y) {
                out[0] = eNode;
                out.sort((a, b) => a.y - b.y);
            }

            // 无排序实现，这里不用，实际为照顾nearest和shooter多目标排序，还是需要排序
            // if (out.length < num) {
            //     out.push(eNode);
            //     continue;
            // }
            // let maxIndex = 0;
            // let maxY = out[0].y;
            // for (let j = 1; j < out.length; j++) {
            //     if (out[j].y > maxY) {
            //         maxY = out[j].y;
            //         maxIndex = j;
            //     }
            // }
            // if (eNode.y < maxY) {
            //     out[maxIndex] = eNode;
            // }
        }
        return out;
    }


    restart() {
        this.battleStart(this.chapter, this.part);
        // if (this.rankId < this.maxRankDataId) {
        //     this.loadRank(this.rankId);
        // } else {
        //     this.rankStart(this.row, this.col, 20);
        // }
    }

    battleEnd() {
        blhColliMgr.ins().setOff();
        this.pause();
        blhStatic.battleState = BattleState.stop;
        this.isEnd = true;
        this.unschedule(this.updateTargetArr);
    }


    gameWin() {
        console.log('gameWin', this.chapter, this.part);
        this.battleEnd();
    }

    gameFail() {
        console.log('gameFail', this.chapter, this.part);
        this.battleEnd();
    }

    remove(): void {
        this.unscheduleAllCallbacks();
        this.node.destroy();
        blhStatic.battle = null;
    }

    /** 战斗每秒执行 */
    battleTick() {
        this.t_sec.string = blhkc.showPassTime(this.battleSec * 1000);
        if (this.nextWaveSec >= 0 && this.battleSec > this.nextWaveSec) {
            this.createWave();
        }
    }

    private dtTick: number = 0;
    private cd: number = 1;
    protected update(dt: number): void {
        if (this.isEnd || blhStatic.battleState !== BattleState.run) {
            return;
        }
        this.cd -= dt;
        if (this.cd <= 0) {
            this.cd = 1;
        }
        this.dtTick += dt;
        if (this.dtTick >= 1) {
            this.dtTick = 0;
            this.battleSec++;
            this.battleTick();
        }
        blhColliMgr.ins().update(dt);
    }
}