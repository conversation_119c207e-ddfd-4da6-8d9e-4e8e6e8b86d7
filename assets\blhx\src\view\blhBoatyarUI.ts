import { battleMode, Prefabs, blPropType, Msg } from "../mgr/blhConst";
import blhResMgr from "../mgr/blhResMgr";
import { blhStatic } from "../mgr/blhStatic";
import blhBoatyarRoleItem from "./blhBoatyarRoleItem";

const { ccclass, property } = cc._decorator;

@ccclass
export default class blhBoatyarUI extends cc.Component {

    @property({
        displayName: "金币数量",
        type: cc.Label
    })
    lab_goods: cc.Label = null;

    @property({
        displayName: "钻石数量",
        type: cc.Label
    })
    lab_diamond: cc.Label = null;

    @property({
        displayName: "容器节点",
        type: cc.Node
    })
    node_content: cc.Node = null;

    @property({
        displayName: "等级按钮",
        type: cc.Node
    })
    btn_level: cc.Node = null;

    @property({
        displayName: "筛选按钮",
        type: cc.Node
    })
    btn_sift: cc.Node = null;


    @property({
        displayName: "筛选弹窗",
        type: cc.Node
    })
    pop_sift: cc.Node = null;

    @property({
        displayName: "筛选确认按钮",
        type: cc.Node
    })
    btn_confrimSift: cc.Node = null;

    @property({
        displayName: "筛选关闭按钮",
        type: cc.Node
    })
    btn_closeSift: cc.Node = null;
    




    //当前阵营 0:全部分类
    nowCamp: number = 0;
    //当前排序方式  0: 等级 1:战力 2: 稀有度 3:好感度
    nowOrder: number = 0;
    //当前职业分类  0:全部职业
    nowCareer: number = 0;
    //当前稀有度
    nowRare: number = 0;



    protected onEnable(): void {
        this.btn_level.on(cc.Node.EventType.TOUCH_END, this.clickSort, this);
        this.btn_confrimSift.on(cc.Node.EventType.TOUCH_END, this.clickConfirmSift, this);
        this.btn_closeSift.on(cc.Node.EventType.TOUCH_END, this.clickCloseSift, this);
        this.btn_sift.on(cc.Node.EventType.TOUCH_END, this.clickSift, this);
        this.pop_sift.active = false;
        this.clickSort();

    }

    //点击等级筛选
    clickSort() {
        // const heros = this.levelSort();
        const sortedHeros = blhStatic.commonData.roleData.slice().sort((a, b) => {
            return b.level - a.level;
        });
        console.error("等级筛选后角色数据: ", Math.round(sortedHeros.length / 4));
        this.node_content.removeAllChildren();
        let len = Math.round(sortedHeros.length / 4);
        this.node_content.height = len * 280 + (len - 1) * 10;
        sortedHeros.forEach(element => {
            blhBoatyarRoleItem.create(this.node_content, element);
        });
    }

    //点击筛选按钮
    clickSift(){
        this.pop_sift.active = true;
    }

    //点击排序toggle
    clickToggleOrder(event, customEventData){
        console.error("customEventData: ", customEventData);
        let order = parseInt(customEventData);
        if(this.nowOrder == order){
            return;
        }
        this.nowOrder = order;
    }

    //点击职业的toggle
    clickToggleCareer(event, customEventData){
        console.error("carrer customEventData: ", customEventData);
        let carrer = parseInt(customEventData);
        if(this.nowCareer == carrer){
            return;
        }
        this.nowCareer = carrer;
    }

    //点击稀有度toggle
    clickToggleRare(event, customEventData){
        console.error("rare customEventData: ", customEventData);
        let rare = parseInt(customEventData);
        if(this.nowRare == rare){
            return;
        }
        this.nowRare = rare;
    }

    //点击筛选确认
    clickConfirmSift(){
        let sortedHeros = null;
        this.node_content.removeAllChildren();
        if(this.nowOrder == 0){
            sortedHeros = blhStatic.commonData.roleData.slice().sort((a, b) => {
                return b.level - a.level;
            });
        }else if(this.nowOrder == 1){
            sortedHeros = blhStatic.commonData.roleData.slice().sort((a, b) => {
                return b.atk - a.atk;
            });
        }
        //阵营
        if(this.nowCamp >= 1){
            sortedHeros = sortedHeros.filter(item => {
                return item.prop == this.nowCamp;
            });
        }
        //职业
        if(this.nowCareer >= 1){
            sortedHeros = sortedHeros.filter(item => {
                return item.career == this.nowCareer;
            });
        }
        //稀有度
        if(this.nowRare >= 1){
            sortedHeros = sortedHeros.filter(item => {
                return item.rare == this.nowRare;
            });
        }
        console.error("heros: ", sortedHeros);
        this.node_content.height = (sortedHeros.length) * 280 + ((sortedHeros.length) - 1) * 10;
        sortedHeros.forEach(element => {
            blhBoatyarRoleItem.create(this.node_content, element);
        });



    }

    //点击关闭筛选
    clickCloseSift(){
        this.pop_sift.active = false;
    }




    //点击阵营(英雄属性)
    clickCamp(event, customEventData) {
        this.node_content.removeAllChildren();
        let camp = parseInt(customEventData);
        this.nowCamp = camp;
        let itemLen = 0;
        blhStatic.commonData.roleData.forEach(element => {
            if (camp == 0) {
                blhBoatyarRoleItem.create(this.node_content, element);
                itemLen++;
            } else {
                if (element.prop === camp) {
                    blhBoatyarRoleItem.create(this.node_content, element);
                    itemLen++;
                }
            }
        });
        let len = Math.round(itemLen / 4);
        this.node_content.height = len * 280 + (len - 1) * 10;
    }


    // //等级筛选
    // levelSort() {
    //     console.error("角色数据: ", blhStatic.commonData.manyRoleData);
    //     const sortedHeros = blhStatic.commonData.manyRoleData.slice().sort((a, b) => {
    //         if (b.star === a.star) {
    //             return b.level - a.level;
    //         }
    //         return b.star - a.star;
    //     })
    //     // console.error("排序后数据: ", sortedHeros);
    //     return sortedHeros;
    // }


    //刷新labe
    refreshLabel() {
        this.lab_goods.string = `${blhStatic.commonData.propData["" + blPropType.goods]}`;
        this.lab_diamond.string = `${blhStatic.commonData.propData["" + blPropType.diamond]}`
    }

    static create(parent: cc.Node): void {
        const node: cc.Node = cc.instantiate(blhResMgr.ins().getNode(Prefabs.boatyarUI));
        node.parent = parent;
    }


}