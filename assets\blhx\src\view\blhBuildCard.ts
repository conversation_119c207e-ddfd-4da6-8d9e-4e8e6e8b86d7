import blhBuildData from "../entity/blhBuildData";
import { blPropType, Msg } from "../mgr/blhConst";
import blhHeroMgr from "../mgr/blhHeroMgr";
import blhResMgr from "../mgr/blhResMgr";
import { blhStatic } from "../mgr/blhStatic";
import blhkc from "../utils/blhkc";
import blhBuildOneCard from "./blhBuildOneCard";

const { ccclass, property } = cc._decorator;

@ccclass
export default class blhBuildCard extends cc.Component {

    @property(cc.Node)
    content_reward: cc.Node = null;

    @property({
        displayName: "确认按钮",
        type: cc.Node
    })
    node_confirm: cc.Node = null;

    @property({
        displayName: "再建造按钮",
        type: cc.Node
    })
    node_buildAgain: cc.Node = null;

    @property({
        displayName: "消耗显示文字",
        type: cc.Label
    })
    lab_cost: cc.Label = null;

    //奖励数组
    rewardsArr: number[] = [];
    //显示再建造节点
    showBuildAgain: boolean = true;


    protected onEnable(): void {
        this.node_confirm.on(cc.Node.EventType.TOUCH_END, this.clickClose, this);
        this.node_buildAgain.on(cc.Node.EventType.TOUCH_END, this.clickBuildAgain, this);
    }

    init(rewards: number[], rewardBuild: boolean) {
        this.rewardsArr = rewards;
        for (let i = 0; i < rewards.length; i++) {
            let roleId = rewards[i];
            blhBuildOneCard.create(this.content_reward, roleId);
        }
        this.lab_cost.string = rewards.length > 1 ? "X10" : "X1";
        if(rewardBuild){
            this.node_buildAgain.active = false;
        }
    }

    clickClose() {
        this.node.destroy();
    }

    /**点击再次建造 */
    clickBuildAgain() {
        if (this.rewardsArr.length < 5) {
            if(blhStatic.commonData.propData["" + blPropType.xzmf]){
                blhStatic.commonData.propData["" + blPropType.xzmf] -= 1;
                this.createRole(1);
            }else if(blhStatic.commonData.propData["" + blPropType.diamond] >= 200){
                blhStatic.commonData.propData["" + blPropType.diamond] -= 200;
                this.createRole(1);
            }else{
                console.error("货币不足");
            }
        } else if(this.rewardsArr.length >= 5){
            if(blhStatic.commonData.propData["" + blPropType.xzmf] >= 10){//卡劵足够,消耗卡劵
                blhStatic.commonData.propData["" + blPropType.xzmf] -= 10;
                this.createRole(10);
            }else{//卡劵不足,消耗卡劵和钻石
                let cnt = blhStatic.commonData.propData["" + blPropType.xzmf];
                let diamondCnt = 10 - cnt;
                diamondCnt = diamondCnt * 200;
                if(blhStatic.commonData.propData["" + blPropType.diamond] >= diamondCnt){
                    blhStatic.commonData.propData["" + blPropType.xzmf] = 0;
                    blhStatic.commonData.propData["" + blPropType.diamond] -= diamondCnt;
                    this.createRole(10);
                }else{
                    console.error("货币不足");
                }
            }
        }
    }

    //创建角色
    public createRole(num: number) {
        blhStatic.commonData.buildData.rewardAddCnt += num;
        blhStatic.commonData.save();
        console.error("创建角色个数: ", num, blhStatic.commonData.propData);
        let allRewardId = [];
        for (let i = 0; i < num; i++) {
            let allData = blhStatic.buildMgr.getAllData();
            let allRate = [];
            allData.forEach((value, key) => {
                allRate.push((value as blhBuildData).buildRate);
            });
            let idx = blhkc.getRandomIndexByWeight(allRate);
            allRewardId.push(idx);
        }
        this.rewardsArr = allRewardId;
        this.content_reward.removeAllChildren();
        for (let i = 0; i < this.rewardsArr.length; i++) {
            let roleId = this.rewardsArr[i];
            blhBuildOneCard.create(this.content_reward, roleId);
        }
        blhkc.sendMsg(Msg.refreshBuildLab);
    }

    static create(parent: cc.Node, rewards: number[], rewardBuild: boolean): void {
        const node: cc.Node = cc.instantiate(blhResMgr.ins().getNode("buildCard"));
        node.parent = parent;
        node.getComponent(blhBuildCard).init(rewards, rewardBuild);
    }

}
