
import blhResMgr from "../mgr/blhResMgr";
import { blhStatic } from "../mgr/blhStatic";

const { ccclass, property } = cc._decorator;

@ccclass
export default class blhBuildCardAni extends cc.Component {

    @property({
        displayName: "角色名称",
        type: cc.Label
    })
    lab_roleName: cc.Label = null;

    @property({
        displayName: "跳过按钮",
        type: cc.Node
    })
    btn_skip: cc.Node = null;



    callBack: ()=>void;
    endBack: ()=>void;

    protected onEnable(): void {
        this.node.on(cc.Node.EventType.TOUCH_END, this.clickNext, this);
        this.btn_skip.on(cc.Node.EventType.TOUCH_END, this.clickSkip, this);
    }

    protected onDisable(): void {
        this.node.off(cc.Node.EventType.TOUCH_END, this.clickNext, this);
        this.btn_skip.off(cc.Node.EventType.TOUCH_END, this.clickSkip, this);
    }


    init(roleId: number, callBack, endBack){
        this.callBack = callBack;
        this.endBack = endBack;
        let heroData = blhStatic.heroMgr.getHeroData(roleId + 1);
        this.lab_roleName.string = heroData.name;
    }

    //点击跳转到下一个
    clickNext(){
        this.callBack && this.callBack();
        this.node.destroy();
    }


    //点击跳过
    clickSkip(){
        this.endBack && this.endBack();
        this.node.destroy();
    }


    static create(parent: cc.Node, roleId: number,callback: ()=>void, endback: ()=>void): void {
        const node: cc.Node = cc.instantiate(blhResMgr.ins().getNode("buildCardAni"));
        node.parent = parent;
        node.getComponent(blhBuildCardAni).init(roleId, callback, endback);
    }

}
