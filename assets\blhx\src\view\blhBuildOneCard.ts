import { OwnedHero } from "../items/blhCommonData";
import blhHeroMgr from "../mgr/blhHeroMgr";
import blhResMgr from "../mgr/blhResMgr";
import { blhStatic } from "../mgr/blhStatic";
import blhkc from "../utils/blhkc";

const {ccclass, property} = cc._decorator;

@ccclass
export default class blhBuildOneCard extends cc.Component {

    @property({
        displayName: "英雄名",
        type: cc.Label
    })
    lab_hero: cc.Label = null;

    start () {
    }

    init(roleId: number){
        let heroData = blhStatic.heroMgr.getHeroData(roleId + 1);
        console.error("buildOneCard: ", roleId, heroData);
        this.lab_hero.string = heroData.name;
        let len = blhStatic.commonData.roleData.length + 1;
        console.error("length: ", len);
        const hero: OwnedHero = new OwnedHero({
            instanceId: "" + len,
            roleId: roleId + 1,
            level: blhkc.randomInt(1, 100),
            star: heroData.star,
            fightState: 0,
            prop: heroData.prop,
            rare: heroData.rare,
            career: heroData.career,
        })
        blhStatic.commonData.roleData.push(hero)
        blhStatic.commonData.save();
        
    }


    static create(parent: cc.Node, roleId: number): void {
        const node: cc.Node = cc.instantiate(blhResMgr.ins().getNode("buildOneCard"));
        node.parent = parent;
        node.getComponent(blhBuildOneCard).init(roleId);
    }

}
