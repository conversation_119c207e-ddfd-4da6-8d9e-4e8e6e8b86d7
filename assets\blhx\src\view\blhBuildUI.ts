import Live2dComponent from "../../../live2d/Live2dComponent";
import blhBuildData from "../entity/blhBuildData";
import blhHeroData from "../entity/blhHeroData";
import { battleMode, Prefabs, blPropType, Msg } from "../mgr/blhConst";
import blhResMgr from "../mgr/blhResMgr";
import { blhStatic } from "../mgr/blhStatic";
import blhkc from "../utils/blhkc";
import blhBuildCard from "./blhBuildCard";
import blhBuildCardAni from "./blhBuildCardAni";

const { ccclass, property } = cc._decorator;

@ccclass
export default class blhBuildUI extends cc.Component {

    @property({
        displayName: "卡劵数量",
        type: cc.Label
    })
    lab_xzmf: cc.Label = null;

    @property({
        displayName: "钻石数量",
        type: cc.Label
    })
    lab_diamond: cc.Label = null;

    @property({
        displayName: "积分label",
        type: cc.Label
    })
    lab_rewardAdd: cc.Label = null;

    @property({
        displayName: "积分按钮",
        type: cc.Node
    })
    btn_rewardAdd: cc.Node = null;
    @property({
        displayName: "获取卡劵按钮",
        type: cc.Node
    })
    btn_getXzmf: cc.Node = null;

    @property({
        displayName: "获取钻石按钮",
        type: cc.Node
    })
    btn_getDiamond: cc.Node = null;

    @property({
        displayName: "许愿按钮",
        type: cc.Node
    })
    btn_wish: cc.Node = null;

    @property({
        displayName: "许愿次数(看广告建造)",
        type: cc.Label
    })
    lab_watchAd: cc.Label = null;

    @property({
        displayName: "免费倒计时label",
        type: cc.Label
    })
    lab_freeCD: cc.Label = null;

    @property({
        displayName: "显示建造一次节点",
        type: cc.Node
    })
    node_buildOneShow: cc.Node = null;


    @property({
        displayName: "建造一次按钮",
        type: cc.Node
    })
    btn_createOne: cc.Node = null;

    @property({
        displayName: "建造十次按钮",
        type: cc.Node
    })
    btn_createTen: cc.Node = null;

    //积分兑换值
    rewardChangeNum: number = 100;
    //奖励角色数组
    allRewards: number[] = [];
    //是否为奖励兑换
    buildReward: boolean = false;



    protected onEnable(): void {

        blhkc.regMsgReciever(Msg.refreshBuildLab, () => {
            this.refreshLabelUI();
            return true;
        });
        this.refreshLabelUI();
        this.btn_getXzmf.on(cc.Node.EventType.TOUCH_END, this.clickGetXzmf, this);
        this.btn_getDiamond.on(cc.Node.EventType.TOUCH_END, this.clickGetDiamond, this);
        this.btn_wish.on(cc.Node.EventType.TOUCH_END, this.clickWish, this);
        this.btn_createOne.on(cc.Node.EventType.TOUCH_END, this.clickBuildOne, this);
        this.btn_createTen.on(cc.Node.EventType.TOUCH_END, this.clickBuildTen, this);
        this.btn_rewardAdd.on(cc.Node.EventType.TOUCH_END, this.clickRewardAdd, this);
        if (blhStatic.commonData.buildData.freeCnt > 0) {
            this.node_buildOneShow.active = true;
            this.lab_freeCD.node.active = true;
            this.schedule(() => {
                this.countDown();
            }, 1, cc.macro.REPEAT_FOREVER);
        } else {
            this.node_buildOneShow.active = false;
            this.lab_freeCD.node.active = false;
        }


    }
    //刷新所有数据label
    refreshLabelUI() {
        this.lab_xzmf.string = blhStatic.commonData.propData["" + blPropType.xzmf] || 0;
        this.lab_diamond.string = blhStatic.commonData.propData["" + blPropType.diamond] || 0;
        this.lab_watchAd.string = `许愿(${blhStatic.commonData.buildData["watchAd"]}/1)`;
        this.lab_rewardAdd.string = `(${blhStatic.commonData.buildData.rewardAddCnt}/1000)`;
    }

    //获取卡劵(心智魔方)
    clickGetXzmf() {
        let coin = blhStatic.commonData.propData["" + blPropType.xzmf] || 0;
        coin += 100;
        blhStatic.commonData.propData["" + blPropType.xzmf] = coin;
        blhStatic.commonData.save();
        this.refreshLabelUI();
    }

    //获取钻石
    clickGetDiamond() {
        let coin = blhStatic.commonData.propData["" + blPropType.diamond] || 0;
        coin += 200;
        blhStatic.commonData.propData["" + blPropType.diamond] = coin;
        blhStatic.commonData.save();
        this.refreshLabelUI();
    }

    //点击许愿按钮
    clickWish() {
        if (blhStatic.commonData.buildData["watchAd"] >= 1) {
            console.error("今日许愿次数已达上限");
            return;
        }
        blhStatic.commonData.buildData["watchAd"] = 1;
        this.createRole(1);
    }

    //点击积分兑换
    clickRewardAdd() {
        if (blhStatic.commonData.buildData.rewardAddCnt < this.rewardChangeNum) {
            return;
        }
        console.error("积分不足");
        blhStatic.commonData.buildData.rewardAddCnt -= this.rewardChangeNum;
        this.createRole(1, true);
    }

    //倒计时刷新(每日倒计时/每周倒计时)
    public countDown() {
        const now = new Date();
        const remainingSeconds = blhkc.getRemainingSeconds();
        if (remainingSeconds > 0) {
            const formattedTime = blhkc.format(remainingSeconds);
            this.lab_freeCD.string = "免费倒计时\n" + formattedTime + "";
        }
    }


    //点击建造一次
    public clickBuildOne() {
        if (!blhStatic.commonData.buildData.freeCnt) {
            blhStatic.commonData.buildData.freeCnt = 1;
            this.node_buildOneShow.active = true;
            this.lab_freeCD.node.active = true;
            this.schedule(() => {
                this.countDown();
            }, 1, cc.macro.REPEAT_FOREVER);
            this.createRole(1);
            return;
        }

        //如果有卡劵,消耗卡劵
        if (blhStatic.commonData.propData["" + blPropType.xzmf]) {
            blhStatic.commonData.propData["" + blPropType.xzmf] -= 1;
            this.createRole(1);
        } else if (blhStatic.commonData.propData["" + blPropType.diamond] && blhStatic.commonData.propData["" + blPropType.diamond] >= 200) {
            blhStatic.commonData.propData["" + blPropType.diamond] -= 200;
            this.createRole(1);
        } else {
            console.error("货币不足");
        }
    }

    //点击建造十次
    public clickBuildTen() {
        //如果有卡劵,消耗卡劵
        if (blhStatic.commonData.propData["" + blPropType.xzmf] && blhStatic.commonData.propData["" + blPropType.xzmf] >= 10) {
            blhStatic.commonData.propData["" + blPropType.xzmf] -= 10;
            this.createRole(10);
        } else {//卡劵不足消耗卡劵和钻石
            let cnt = blhStatic.commonData.propData["" + blPropType.xzmf];
            let diamondCnt = 10 - cnt;
            diamondCnt = diamondCnt * 200;
            if (blhStatic.commonData.propData["" + blPropType.diamond] >= diamondCnt) {
                blhStatic.commonData.propData["" + blPropType.xzmf] = 0;
                blhStatic.commonData.propData["" + blPropType.diamond] -= diamondCnt;
                this.createRole(10);
            } else {
                console.error("货币不足");
            }
        } 
    }
    //创建角色个数
    public createRole(num: number, rewardBuild: boolean = false) {
        blhStatic.commonData.buildData.rewardAddCnt += num;
        blhStatic.commonData.save();
        console.error("创建角色个数: ", num, blhStatic.commonData.propData);
        this.buildReward = rewardBuild;
        this.allRewards = [];
        for (let i = 0; i < num; i++) {
            let allData = blhStatic.buildMgr.getAllData();
            let allRate = [];
            allData.forEach((value, key) => {
                if(rewardBuild){
                    allRate.push((value as blhBuildData).rewardRate);
                }else{
                    allRate.push((value as blhBuildData).buildRate);
                }
            });
            let idx = blhkc.getRandomIndexByWeight(allRate);
            this.allRewards.push(idx);
        }
        console.error("奖励角色下标: ", this.allRewards);
        this.refreshLabelUI();
        this.popupCount = this.allRewards.length;
        this.currentIndex = 0;
        this.showRoleAni();
    }
    popupCount = 10;
    currentIndex = 0;
    //展示角色动画
    showRoleAni(){
        if(this.currentIndex >= this.popupCount){
            console.error("所有弹窗播放完毕");
            this.sendRewards(this.allRewards, this.buildReward);
            return;
        }
        this.currentIndex++;
        blhBuildCardAni.create(blhStatic.main.layer_pop, this.allRewards[this.currentIndex - 1],()=>{
            this.showRoleAni();
        }, ()=>{
            this.sendRewards(this.allRewards, this.buildReward);
        });


    }



    //发放奖励弹窗
    public sendRewards(rewards: number[], rewardBuild: boolean) {
        blhBuildCard.create(blhStatic.main.layer_pop, rewards, rewardBuild);
    }





    static create(parent: cc.Node): void {
        const node: cc.Node = cc.instantiate(blhResMgr.ins().getNode(Prefabs.buildUI));
        node.parent = parent;
    }


}