import blhSkillData from "../entity/blhSkillData";
import blhResMgr from "../mgr/blhResMgr";
import blhSkillMgr from "../mgr/blhSkillMgr";
import { blhStatic } from "../mgr/blhStatic";
import blhC3_item from "./blhC3_item";


const { ccclass, property } = cc._decorator;

@ccclass
export default class blhC3 extends cc.Component {

    @property(cc.Node)
    container: cc.Node = null;
    @property(cc.Node)
    bt1: cc.Node = null;

    canChoose: boolean = false;
    /** 选项数量 */
    num: number = 3;

    protected onLoad(): void {
        this.bt1.on(cc.Node.EventType.TOUCH_END, this.refresh, this);
    }

    refresh() {
        if (!this.canChoose) {
            return;
        }
        this.canChoose = false;
        this.show(this.num);
    }

    /** 选中升级 */
    onChoose(skill: blhSkillData) {
        if (!skill.heroId) {
            console.error('3选1技能未绑定英雄id', skill.id);
            return;
        }
        const arr = blhStatic.battle.heroArr;
        for (let i = 0; i < arr.length; i++) {
            const hero = arr[i];
            if (hero.data.id == skill.heroId) {
                hero.upgradeC3(skill);
                break;
            }
        }
        this.hide();
    }

    show(num: number) {
        this.num = num;
        this.container.removeAllChildren();
        const datas: Array<blhSkillData> = blhSkillMgr.ins().getC3ItemsData(num);
        for (let i = 0; i < datas.length; i++) {
            const data = datas[i];
            blhC3_item.create(data, this);
        }
        this.node.scale = 0.1;
        this.node.active = true;
        blhStatic.battle.blaskMask.active = true;
        cc.tween(this.node).to(0.2, { scale: 1 }, { 'easing': 'backOut' }).call(() => {
            this.canChoose = true;
        }).start();
    }

    hide() {
        this.node.destroy();
        blhStatic.battle.blaskMask.active = false;
        blhStatic.battle.resume();
        // blhChannel.instance().doAction(adAction.closePopWin);
    }



    static create(parent: cc.Node, num: number = 3): blhC3 {
        if (!blhStatic.battle || blhStatic.battle.isEnd) {
            return;
        }
        blhStatic.battle.pause();
        const node: cc.Node = blhResMgr.ins().getNode('c3');
        node.parent = parent;
        const out = node.getComponent(blhC3);
        out.show(num);
        return out;
    }

}