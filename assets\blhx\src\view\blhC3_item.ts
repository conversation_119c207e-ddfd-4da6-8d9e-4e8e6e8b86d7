import blhHeroData from "../entity/blhHeroData";
import blhSkillData from "../entity/blhSkillData";
import blhResMgr from "../mgr/blhResMgr";
import { blhStatic } from "../mgr/blhStatic";
import blhC3 from "./blhC3";


const { ccclass, property } = cc._decorator;

@ccclass
export default class blhC3_item extends cc.Component {

    @property(cc.Label)
    t_skill: cc.Label = null;

    c3: blhC3 = null;

    skill: blhSkillData = null;

    protected onLoad(): void {
        this.node.on(cc.Node.EventType.TOUCH_END, () => {
            this.choose();
        }, this);
    }

    /** 3选1选中技能,升级 */
    choose() {
        if (!this.c3 || !this.c3.canChoose) {
            return;
        }
        this.c3.onChoose(this.skill);
    }



    static create(skill: blhSkillData, c3: blhC3): blhC3_item {
        if (!blhStatic.battle || blhStatic.battle.isEnd) {
            return;
        }
        const node: cc.Node = blhResMgr.ins().getNode('c3_item');
        node.parent = c3.container;
        const out = node.getComponent(blhC3_item);
        out.c3 = c3;
        out.skill = skill;
        let str = skill.name;
        if (skill.heroId) {
            str += '\n\n' + blhStatic.heroMgr.getHeroData(skill.heroId).name;
        }
        out.t_skill.string = str;
        return out;
    }

}