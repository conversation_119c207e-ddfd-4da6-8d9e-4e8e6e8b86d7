import Live2dComponent from "../../../live2d/Live2dComponent";
import { battleMode, Prefabs, blPropType } from "../mgr/blhConst";
import blhResMgr from "../mgr/blhResMgr";
import { blhStatic } from "../mgr/blhStatic";
import blhBoatyarUI from "./blhBoatyarUI";
import blhBuildUI from "./blhBuildUI";

const { ccclass, property } = cc._decorator;

@ccclass
export default class blhHallUI extends cc.Component {

    l2d: Live2dComponent = null;

    @property({
        displayName: "l2d互动节点",
        type: cc.Node
    })
    node_touch: cc.Node = null;

    @property({
        displayName: "所有按钮节点",
        type: cc.Node
    })
    allBtnNode: cc.Node = null;

    @property({
        displayName: "建造节点",
        type: cc.Node
    })
    node_build: cc.Node = null;

    @property({
        displayName: "船坞节点",
        type: cc.Node
    })
    node_boatyarUI: cc.Node = null;



    protected onLoad(): void {
        this.scheduleOnce(() => {
            this.l2d = this.node.getChildByName('l2d').getComponent(Live2dComponent);
            // this.l2d.loadModel('ankeleiqi_2_hx');
            // this.l2d.loadModel('shi_2');
            this.l2d.loadModel('dafeng_7');
            this.l2d.setMotionEndCall(() => {
                console.log('l2d动作结束');
                this.l2d.node.opacity = 255;
            });
            // this.l2d.startMotion('Idle', 0, 1);
        }, 2);
        this.node_touch.on(cc.Node.EventType.TOUCH_END, this.touchRole, this);
    }

    protected onEnable(): void {
        this.node_build.on(cc.Node.EventType.TOUCH_END, this.clickBuild, this);
        this.node_boatyarUI.on(cc.Node.EventType.TOUCH_END, this.clickBoatyar, this);
    }

    //点击船坞
    clickBoatyar(){
        blhBoatyarUI.create(this.node.parent);
    }

    //点击建造按钮
    public clickBuild(){
        blhBuildUI.create(this.node.parent);
    }

    //触碰角色
    touchRole(){
        if (this.l2d) {
            console.log('testNode点击, l2d动作');
            this.l2d.startMotion('Other', 0, 100);
        }
    }

    //点击表情按钮
    clickMotionBtn(event, customEventData){
        let arr = customEventData.split(",");
        let group = arr[0];
        let idx = parseInt(arr[1]);
        this.allBtnNode.active = false;
        this.scheduleOnce(()=>{
            if (this.l2d) {
                // console.error("播放动画组idx: ", group, idx);
                this.l2d.startMotion(group, idx, 100);
            }
        }, 0.5);
    }

    //点击显示所有安妮
    clickShowAllBtn(){
        this.allBtnNode.active = true;
    }


    static create(parent: cc.Node): void {
        const node : cc.Node = cc.instantiate(blhResMgr.ins().getNode(Prefabs.hallUI));
        node.parent = parent;
    }


}