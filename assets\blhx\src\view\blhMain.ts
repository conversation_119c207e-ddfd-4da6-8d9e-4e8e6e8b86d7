import Live2dComponent from "../../../live2d/Live2dComponent";
import Live2dModel from "../../../live2d/src/LAppModel";
import blhAudioMgr from "../mgr/blhAudioMgr";
import { adAction, AudioName, battleMode, Bundles, Msg, Prefabs, saveData } from "../mgr/blhConst";
import blhEnemyMgr from "../mgr/blhEnemyMgr";
import blhResMgr from "../mgr/blhResMgr";
import blhSkillMgr from "../mgr/blhSkillMgr";
import { blhStatic } from "../mgr/blhStatic";
import { blhChannel } from "../utils/blhChannel";
import blhkc from "../utils/blhkc";
import blhPrivacyWin from "../utils/blhPrivacyWin";
import { blhStatisticsMgr } from "../utils/blhStatisticsMgr";
import blhBattle from "./blhBattle";
import blhMainUI from "./blhMainUI";


const { ccclass, property } = cc._decorator;

@ccclass
export default class blhMain extends cc.Component {

    @property(cc.ProgressBar)
    loadingProgress: cc.ProgressBar = null;
    @property(cc.Node)
    layer_loading: cc.Node = null;
    @property(cc.Label)
    lab_loading: cc.Label = null;

    @property(cc.Node)
    layer_main: cc.Node = null;
    @property(cc.Node)
    layer_battle: cc.Node = null;
    // @property(cc.Node)
    // layer_vit: cc.Node = null;

    @property(cc.Node)
    layer_mainUI: cc.Node = null;

    @property(cc.Node)
    layer_pop: cc.Node = null;


    @property(cc.Node)
    testNode: cc.Node = null;
    @property(cc.Node)
    testNode2: cc.Node = null;



    @property(cc.Label)
    ruanzhu: cc.Label = null;

    @property(cc.Node)
    privacyView: cc.Node = null;
    @property(cc.Node)
    fcm_12: cc.Node = null;

    l2d: Live2dComponent = null;

    // @property(cc.Label)
    // ver: cc.Label = null;

    /** loading结束跳到哪里, 0为到mainUI,1为到Battle */
    goWhereAfterLoading: number = 1;


    private isLoadingDone: boolean = false;


    protected onLoad(): void {
        this.layer_loading.active = true;
        this.layer_main.active = false;
        blhkc.init();

        let loginCall = () => {
            blhChannel.instance().login({
                successCall: async () => {
                    // if (blhStatic.forceAdCnt > 0 || blhStatic.hwForceAd) {
                    //     if (blhChannel.instance().getCPara('fakeLoading')) {
                    //         this.fakeLoad();
                    //     } else {
                    //         this.startLoad();
                    //     }
                    // } else {
                    //     this.startLoad();
                    // }
                    this.startLoad();
                },
                failCall: (code) => {
                    // hwLogin.create(this.node, this, code);
                }
            })
        }

        blhChannel.instance().init(() => {
            blhkc.log('登录完成');
            // blhStatisticsMgr.afterLogin();
            // blhChannel.instance().doAction("hwIdleMonitor", this);
            // blhkc.log('注册硬件闲置检测事件');
            // this.node.on(cc.Node.EventType.TOUCH_END, (event: cc.Event.EventTouch) => {
            //     blhkc.log('有点击操作，重置闲置事件');
            //     this._timer = 0;
            // }, this);
            // this.node["_touchListener"].setSwallowTouches(false);
            // loginCall();
        });

        if (blhChannel.instance().channel == "web") {
            loginCall();
        }

        this.ruanzhu.string += blhChannel.instance().getChannel().ruanzhu;

        // blhStatisticsMgr.sendEvent(SsEventName.loading, { name: "loading_step", value: 1 });

        this.fcm_12.on(cc.Node.EventType.TOUCH_END, () => {
            if (blhChannel.instance().isOvPlatForm()) {
                this.privacyView.active = !this.privacyView.active;
            }
        }, this);
        this.privacyView.on(cc.Node.EventType.TOUCH_END, () => {
            this.privacyView.active = false;
        }, this);

        blhkc.regMsgReciever(Msg.closeLoading, () => {
            if (!this.isLoadingDone) {
                this.closeLoading();
            }
            return false;
        });



        blhStatic.initView();

        // -------- live2d测试 ------------
        // this.testNode.on(cc.Node.EventType.TOUCH_END, () => {
        //     if (this.l2d) {
        //         console.log('testNode点击, l2d动作3333');
        //         this.l2d.startMotion('Other', 2, 100);
        //     }
        // }, this);
        // this.testNode2.on(cc.Node.EventType.TOUCH_END, () => {
        //     if (this.l2d) {
        //         console.log('testNode2点击, l2d动作');
        //         this.l2d.startMotion('Other', 3, 100);
        //     }
        // }, this);
        // 必须延迟加载
        // this.scheduleOnce(() => {
        //     console.log('加载live2d');
        //     this.l2d = this.layer_main.getChildByName('l2d').getComponent(Live2dComponent);
        //     // this.l2d.loadModel('ankeleiqi_2_hx');
        //     this.l2d.loadModel('shi_2');
        //     this.l2d.setMotionEndCall(() => {
        //         console.log('l2d动作结束');
        //         this.l2d.node.opacity = 255;
        //     });
        //     // this.l2d.startMotion('Idle', 0, 1);
        // }, 0.1);

    }


    startLoad() {
        const me = this;
        //进度条比例分配 ['img', 'atlas', 'spine', 'prefab', 'audio']
        blhResMgr.ins().loadBundleWithProcess(Bundles.preload, [0, 0.7, 0, 0.3, 0], (process: number) => {
            const p = process * 0.8;
            me.loadingProgress.progress = p;
            me.lab_loading.string = Math.floor(p * 100) + '%';
        }, () => {
            // me.lab_loading_txt.string = '正在加载场景资源';
            blhkc.log('preload加载完成');
            // blhStatisticsMgr.sendEvent(SsEventName.loading, { name: "loading_step", value: 2 });

            this.init();

            return;

            // if (blhkc.getData(saveData.chapter, 0) <= 0) {
            //     me.goWhereAfterLoading = 1;
            //     //初次进,直接进普通模式教程
            //     blhResMgr.ins().loadBundleWithProcess(Bundles.battle, [0.1, 0, 0.5, 0.4, 0], (process: number) => {
            //         const p = 0.8 + process * 0.2;
            //         me.loadingProgress.progress = p;
            //         me.lab_loading.string = Math.floor(p * 100) + '%';
            //     }, () => {
            //         // me.lab_loading_txt.string = '正在加载场景资源';
            //         blhkc.log('battle资源加载完成');
            //         me.init();
            //     });

            // } else {
            //     //非初次进游戏，直接进mainUI
            //     me.goWhereAfterLoading = 0;
            //     blhResMgr.ins().loadBundleWithProcess(Bundles.mainUI, [0, 0, 0, 1, 0], (process: number) => {
            //         const p = 0.8 + process * 0.2;
            //         me.loadingProgress.progress = p;
            //         me.lab_loading.string = Math.floor(p * 100) + '%';
            //     }, () => {
            //         // me.lab_loading_txt.string = '正在加载场景资源';
            //         blhkc.log('mainUI资源加载完成');
            //         me.init();
            //     });
            // }
        });

    }

    init() {
        blhResMgr.ins().loadConf(() => {
            //初始化各管理器,复杂的管理器不进static,减少循环引用
            blhEnemyMgr.ins().init();
            blhSkillMgr.ins().init();
            blhStatic.init(this);


            this.closeLoading();
            this.isLoadingDone = true;
            // if (sfrResMgr.instance().getConf('a1')[0]['ver']) {
            //     this.ver.string = 'v' + sfrResMgr.instance().getConf('a1')[0]['ver'];
            // }
            // if (!blhChannel.instance().hasPrivacy()) {
            //     blhPrivacyWin.show(this.node);
            // } else if (blhChannel.instance().getCPara('stopOnLoading')) {
            //     blhkc.log('stopOnLoading....=====>');
            // } else {
            //     this.closeLoading();
            // }
        });
    }

    closeLoading() {
        this.layer_main.active = true;
        this.layer_loading.active = false;

        // if (this.goWhereAfterLoading === 0) {
        //     this.gotoMainUI();
        // } else {
        //     this.gotoBattle(battleMode.normal);
        // }

        // this.gotoBattle(battleMode.normal);
        this.gotoMainUI();
        blhAudioMgr.instance().playBgm(AudioName.bgm);
        // blhChannel.instance().doAction(adAction.show_banner);
    }

    showMiniLoading() {
        console.log('显示小loading');
        this.layer_loading.active = true;
        this.layer_loading.getChildByName('gameName').active = false;
        this.layer_loading.getChildByName('ruanzhu').active = false;
        this.layer_loading.getChildByName('fcm_12').active = false;
        this.layer_loading.getChildByName('privacyView').active = false;
        this.loadingProgress.progress = 0;
        if (blhStatic.battle) {
            blhStatic.battle.remove();
        }
        if (blhStatic.mainUI) {
            blhStatic.mainUI.hide();
        }
    }

    hideMiniLoading() {
        this.layer_loading.active = false;
    }

    gotoMainUI() {
        if (blhStatic.mainUI) {
            if (blhStatic.battle) {
                blhStatic.battle.remove();
            }
            blhkc.sendMsg(Msg.updateVit);
            blhChannel.instance().doAction(adAction.closePopWin);
            blhStatic.mainUI.show();
            return;
        }
        // this.showMiniLoading();
        blhResMgr.ins().loadBundleWithProcess(Bundles.mainUI, [0, 0, 0.5, 0.5, 0], (process: number) => {
            const p = process;
            this.loadingProgress.progress = p;
            this.lab_loading.string = Math.floor(p * 100) + '%';
        }, () => {
            blhkc.log('mainUI加载完成');
            // this.hideMiniLoading();
            const node: cc.Node = cc.instantiate(blhResMgr.ins().getNode(Prefabs.mainUI));
            node.parent = this.layer_mainUI;
            blhStatic.mainUI = node.getComponent(blhMainUI);
            if (blhStatic.battle) {
                blhStatic.battle.remove();
            }
            blhStatic.mainUI.show();
            blhkc.sendMsg(Msg.updateVit);

        });
    }

    gotoBattle(mode: battleMode) {
        // blhChannel.instance().doAction(adAction.on_game_start, mode === battleMode.normal);
        // let rankKey = (mode === battleMode.llk ? saveData.curLLKRank : saveData.curRank);

        
        if (blhStatic.battle) {
            if(blhStatic.mainUI){
                blhStatic.mainUI.hide();
            }
            const chapter = blhkc.getData(saveData.chapter, 1);
            const part = blhkc.getData(saveData.part, 1);
            blhStatic.battle.battleStart(chapter, part);
            return;
        }

        // this.showMiniLoading();

        blhResMgr.ins().loadBundleWithProcess(Bundles.battle, [0.1, 0.1, 0.4, 0.4, 0], (process: number) => {
            // const p = process;
            // this.loadingProgress.progress = p;
            // this.lab_loading.string = Math.floor(p * 100) + '%';
        }, () => {
            blhkc.log('battle加载完成');
            // this.hideMiniLoading();
            // let prefab = mode === battleMode.llk ? Prefabs.battleLLK : Prefabs.battle;
            const node: cc.Node = cc.instantiate(blhResMgr.ins().getNode(Prefabs.battle));
            node.parent = this.layer_battle;
            blhStatic.battle = node.getComponent(blhBattle);
            const chapter = blhkc.getData(saveData.chapter, 1);
            const part = blhkc.getData(saveData.part, 1);
            blhStatic.battle.battleStart(chapter, part);
            if(blhStatic.mainUI){
                blhStatic.mainUI.hide();
            }

        });
    }

    protected lateUpdate(dt: number): void {
        blhkc.saveExe();
    }

}