import { battleMode } from "../mgr/blhConst";
import { blhStatic } from "../mgr/blhStatic";
import blhHallUI from "./blhHallUI";

const { ccclass, property } = cc._decorator;

@ccclass
export default class blhMainUI extends cc.Component {

    @property({
        displayName: "底部按钮节点",
        type: [cc.Node]
    })
    nodes_btn: cc.Node[] = [];

    @property({
        displayName: "底部按钮图片组",
        type: [cc.Sprite]
    })
    sprites_btm: cc.Sprite[] = [];



    @property({
        displayName: "港区精灵组",
        type: [cc.SpriteFrame]
    })
    spris_port: cc.SpriteFrame[] = [];

    @property({
        displayName: "好友精灵组",
        type: [cc.SpriteFrame]
    })
    spris_friend: cc.SpriteFrame[] = [];

    @property({
        displayName: "大厅精灵组",
        type: [cc.SpriteFrame]
    })
    spris_hall: cc.SpriteFrame[] = [];

    @property({
        displayName: "仓库精灵组",
        type: [cc.SpriteFrame]
    })
    spris_warehouse: cc.SpriteFrame[] = [];

    @property({
        displayName: "paretnt",
        type: cc.Node
    })
    parent_node: cc.Node = null;

    // hallUI



    //当前选中的底部按钮
    selectIdx: number = -1;

    protected onLoad(): void {
        for (let i = 0; i < this.nodes_btn.length; i++) {
            this.nodes_btn[i].on(cc.Node.EventType.TOUCH_END, () => {
                this.chooseBtn(i);
            }, this);
        }
    }

    protected onEnable(): void {
        this.chooseBtn(2);
    }


    //选择底部按钮
    chooseBtn(idx: number) {
        if (idx == this.selectIdx) {
            return;
        }
        this.selectIdx = idx;
        this.changeBtmBtn();
        switch (idx) {
            case 0:
                break;
            case 1:
                break;
            case 2:
                blhHallUI.create(this.parent_node);
                break;
            case 3:
                break;
            case 4:
                blhStatic.main.gotoBattle(battleMode.normal);
                break;
        }
    }
    // 改变底部按钮状态
    changeBtmBtn() {
        const normalColor = new cc.Color(255, 255, 255);
        const selectedColor = new cc.Color(255, 236, 113);

        const spriteFrames = [
            this.spris_port,
            this.spris_friend,
            this.spris_hall,
            this.spris_warehouse
        ];

        // 设置按钮颜色（共5个）
        for (let i = 0; i < this.nodes_btn.length; i++) {
            this.nodes_btn[i].color = (i === this.selectIdx) ? selectedColor : normalColor;
        }

        // 设置底部按钮精灵帧（仅前4个）
        for (let i = 0; i < this.sprites_btm.length; i++) {
            const frameIndex = (i === this.selectIdx) ? 1 : 0;
            this.sprites_btm[i].spriteFrame = spriteFrames[i][frameIndex];
        }
    }

    show() {
        this.node.active = true;
    }

    hide() {
        this.node.active = false;
    }

}