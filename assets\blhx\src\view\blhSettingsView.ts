import blhAudioMgr from "../mgr/blhAudioMgr";
import { adAction, AudioName, battleMode, saveData } from "../mgr/blhConst";
import blhResMgr from "../mgr/blhResMgr";
import { blhStatic } from "../mgr/blhStatic";
import { blhChannel } from "../utils/blhChannel";
import blhkc from "../utils/blhkc";
import blhPrivacyWin from "../utils/blhPrivacyWin";


const { ccclass, property } = cc._decorator;

@ccclass
export default class blhSettingsView extends cc.Component {


    @property(cc.Node)
    close: cc.Node = null;
    @property(cc.Node)
    bt_music: cc.Node = null;
    @property(cc.Node)
    bt_sound: cc.Node = null;
    @property(cc.Node)
    bt_home: cc.Node = null;
    @property(cc.Node)
    bt_reset: cc.Node = null;
    @property(cc.Node)
    switch_music: cc.Node = null;
    @property(cc.Node)
    switch_sound: cc.Node = null;
    @property(cc.Node)
    toggle_music: cc.Node = null;
    @property(cc.Node)
    toggle_sound: cc.Node = null;
    @property(cc.Label)
    t_home: cc.Label = null;
    @property(cc.Label)
    t_ver: cc.Label = null;


    isBgmOpen: boolean = false;
    isSoundOpen: boolean = false;

    protected onLoad(): void {
        this.close.on(cc.Node.EventType.TOUCH_END, () => {
            this.hide();
        }, this);

        this.bt_music.on(cc.Node.EventType.TOUCH_END, () => {
            if (this.isBgmOpen) {
                blhAudioMgr.instance().closeBgm();
            } else {
                blhAudioMgr.instance().openBgm();
            }
            this.updateState();
        }, this);

        this.bt_sound.on(cc.Node.EventType.TOUCH_END, () => {
            if (this.isSoundOpen) {
                blhAudioMgr.instance().closeSound();
            } else {
                blhAudioMgr.instance().openSound();
            }
            this.updateState();
        }, this);

        this.bt_home.on(cc.Node.EventType.TOUCH_END, () => {

            if (blhChannel.instance().getCPara('dirBack')) {
                blhStatic.main.gotoMainUI();
                return;
            }

            // sfrExitView.create(this.node, '返回主页', () => {
            //     blhStatic.main.gotoMainUI();
            //     blhAudioMgr.instance().playSound(AudioName.click);
            // }, this);

            // if (this.t_home.string === '返回主页') {
            // } else {
            //     if (!sfrStatic.battle) {
            //         return;
            //     }
            //     //切换模式
            //     sfrStatic.battle.changeMode();
            // }
        }, this);
        this.bt_reset.on(cc.Node.EventType.TOUCH_END, () => {
            if (!blhStatic.battle || blhStatic.battle.isEnd) {
                return;
            }
            // blhStatic.battle.doRestart();
            blhAudioMgr.instance().playSound(AudioName.click);
            this.hide();
        }, this);

        this.updateState();

        console.log('===showPrivacy===', blhChannel.instance().getCPara('showPrivacy'));
        if (blhChannel.instance().getCPara('showPrivacy')) {
            this.t_ver.string = '隐私政策';
            this.t_ver.node.on(cc.Node.EventType.TOUCH_END, () => {
                blhPrivacyWin.show(blhStatic.main.node);
            }, this);
        }

        // blhChannel.instance().doAction(adAction.popWin1);
        // blhChannel.instance().doAction(adAction.settings_show, this);
        // blhChannel.instance().doAction(adAction.wx_p2);
    }




    updateState() {
        this.isBgmOpen = blhkc.getData('bgmOpen', true);
        this.isSoundOpen = blhkc.getData('soundOpen', true);
        if (this.isBgmOpen) {
            this.switch_music.x = 90;
            this.toggle_music.active = true;
        } else {
            this.switch_music.x = -90;
            this.toggle_music.active = false;
        }
        if (this.isSoundOpen) {
            this.switch_sound.x = 90;
            this.toggle_sound.active = true;
        } else {
            this.switch_sound.x = -90;
            this.toggle_sound.active = false;
        }

        if (!blhStatic.battle) {
            this.bt_home.active = false;
            this.bt_reset.active = false;
        } else {
            this.bt_home.active = true;
            this.bt_reset.active = true;
        }

        // if (!sfrkc.getData(saveData.unlock_llk, 0)) {
        //     this.t_home.string = '返回主页';
        // } else {
        //     this.t_home.string = '切换模式';
        // }
    }

    hide() {
        this.node.destroy();
        blhChannel.instance().doAction(adAction.closePopWin);
    }



    static create(parent: cc.Node) {
        // if (sfrStatic.battle && sfrStatic.battle.isEnd) {
        //     return;
        // }
        const node: cc.Node = blhResMgr.ins().getNode('settingsView');
        node.parent = parent;
    }

}