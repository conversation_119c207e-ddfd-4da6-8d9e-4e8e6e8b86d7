import { OwnedHero } from "../items/blhCommonData";
import { blPropType } from "../mgr/blhConst";
import blhResMgr from "../mgr/blhResMgr";
import { blhStatic } from "../mgr/blhStatic";
import blhkc from "../utils/blhkc";
const { ccclass, property } = cc._decorator;

@ccclass
export default class blhTeamMemberItem extends cc.Component {

    @property({
        displayName: "角色名称lab",
        type: cc.Label
    })
    lab_roleName: cc.Label = null;

    @property({
        displayName: "角色战力lab",
        type: cc.Label
    })
    lab_roleFight: cc.Label = null;

    @property({
        displayName: "等级进度条",
        type: cc.ProgressBar
    })
    pro_lvup: cc.ProgressBar = null;

    @property({
        displayName: "阶数lab",
        type: cc.Label
    })
    lab_grade: cc.Label = null;

    @property({
        displayName: "等级lab",
        type: cc.Label
    })
    lab_lv: cc.Label = null;

    @property({
        displayName: "消耗战术教材lab",
        type: cc.Label
    })
    lab_costZsjc: cc.Label = null;

    @property({
        displayName: "消耗通用金币lab",
        type: cc.Label
    })
    lab_costGoods: cc.Label = null;

    @property({
        displayName: "消耗心智单元lab",
        type: cc.Label
    })
    lab_costXzdy: cc.Label = null;


    @property({
        displayName: "升级按钮",
        type: cc.Node
    })
    btn_lvup: cc.Node = null;

    @property({
        displayName: "升级按钮文字",
        type: cc.Label
    })
    lab_btnlvup: cc.Label = null;


    @property({
        displayName: "升级箭头",
        type: cc.Node
    })
    node_lvupArrow: cc.Node = null;

    roleData: OwnedHero = null;
    //是否可以点击升级按钮
    canClikLvUpBtn: boolean = true;

    protected onEnable(): void {
        this.btn_lvup.on(cc.Node.EventType.TOUCH_END, this.clickLvUpBtn, this);
    }

    //初始化
    init(roleData: OwnedHero){
        console.error("角色数据: ", roleData);
        this.roleData = roleData;
        this.refreshLabel();
        this.refreshLvState();
    }

    /**更新label */
    refreshLabel(){
        let heroData = blhStatic.heroMgr.getHeroData(this.roleData.roleId);
        this.lab_roleName.string = heroData.name;
        this.lab_lv.string = this.roleData.level + "级";

        // 获取当前阶数
        const currentGrade = this.getCurrentGrade();
        this.lab_grade.string = currentGrade + "阶";

        // 修复进度条显示
        const maxLevelForCurrentGrade = this.getMaxLevelForGrade(currentGrade);
        const minLevelForCurrentGrade = currentGrade === 0 ? 1 : this.getMaxLevelForGrade(currentGrade - 1) + 1;
        const levelInCurrentGrade = this.roleData.level - minLevelForCurrentGrade + 1;
        const totalLevelsInCurrentGrade = maxLevelForCurrentGrade - minLevelForCurrentGrade + 1;

        this.pro_lvup.progress = levelInCurrentGrade / totalLevelsInCurrentGrade;

        console.error(`等级${this.roleData.level}, 阶数${currentGrade}, 阶内等级${levelInCurrentGrade}/${totalLevelsInCurrentGrade}, 进度${this.pro_lvup.progress}`);
    }

    refreshLvState(){
        // 检查是否需要升阶
        if (this.canGradeUp()) {
            this.showGradeUpState();
        } else {
            this.showLevelUpState();
        }
    }

    /**
     * 检查是否可以升阶
     */
    private canGradeUp(): boolean {
        const currentGrade = this.getCurrentGrade();
        const maxLevelForCurrentGrade = this.getMaxLevelForGrade(currentGrade);

        // 如果当前等级已达到当前阶数的最大等级，则可以升阶
        return this.roleData.level >= maxLevelForCurrentGrade;
    }

    /**
     * 显示升阶状态
     */
    private showGradeUpState() {
        const currentGrade = this.getCurrentGrade();
        const nextGradeData = blhStatic.heroGradeUpMgr.getData(currentGrade + 1); // 数据表ID从1开始，下一阶是+2
        blhkc.err("升阶状态: ", nextGradeData);
        this.lab_costXzdy.string = "X" + nextGradeData.costXzdy;
        if (nextGradeData) {
            const currentXzdy = blhStatic.commonData.propData["" + blPropType.xzdy] || 0;

            if (currentXzdy >= nextGradeData.costXzdy) {
                // 材料足够，可以升阶
                this.canClikLvUpBtn = true;
                this.btn_lvup.color = new cc.Color(255, 255, 255);
                this.node_lvupArrow.active = true;
                this.lab_btnlvup.string = "升阶";
            } else {
                // 材料不足
                this.canClikLvUpBtn = false;
                this.btn_lvup.color = new cc.Color(82, 56, 56);
                this.node_lvupArrow.active = false;
                this.lab_btnlvup.string = "升阶";
            }

            // 隐藏升级材料显示
            this.lab_costZsjc.node.parent.active = false;
            this.lab_costGoods.node.parent.active = false;
            this.lab_costXzdy.node.parent.active = true;
        }
    }

    /**
     * 显示升级状态
     */
    private showLevelUpState() {
        // 显示升级材料
        this.lab_costZsjc.node.parent.active = true;
        this.lab_costGoods.node.parent.active = true;
        this.lab_costXzdy.node.parent.active = false;

        // 获取当前拥有的材料数量
        const currentZsjc = blhStatic.commonData.propData["" + blPropType.zsjc] || 0;
        const currentGoods = blhStatic.commonData.propData["" + blPropType.goods] || 0;

        // 计算可升级的最大等级数
        const maxUpgradeLevels = this.calculateMaxUpgradeLevels(currentZsjc, currentGoods);

        if (maxUpgradeLevels > 0) {
            // 计算升级所需的总材料
            const totalCost = this.calculateTotalCost(maxUpgradeLevels);

            // 设置按钮状态为可点击
            this.canClikLvUpBtn = true;
            this.btn_lvup.color = new cc.Color(255, 255, 255);
            this.node_lvupArrow.active = true;

            // 设置按钮文字和消耗显示
            if (maxUpgradeLevels === 1) {
                this.lab_btnlvup.string = "升一级";
            } else {
                this.lab_btnlvup.string = `升${maxUpgradeLevels}级`;
            }

            this.lab_costZsjc.string = "X" + totalCost.zsjc;
            this.lab_costGoods.string = "X" + totalCost.goods;
        } else {
            // 材料不足，设置按钮状态为不可点击
            this.canClikLvUpBtn = false;
            this.btn_lvup.color = new cc.Color(82, 56, 56);
            this.lab_btnlvup.string = "升级";
            this.node_lvupArrow.active = false;

            // 显示升一级所需的材料
            const nextLvData = blhStatic.heroLvUpMgr.getData(this.roleData.level + 1);
            if (nextLvData) {
                this.lab_costZsjc.string = "X" + nextLvData.costzsjc;
                this.lab_costGoods.string = "X" + nextLvData.costgoods;
            }
        }
    }

    /**
     * 获取当前角色的阶数
     * @returns 当前阶数
     */
    private getCurrentGrade(): number {
        const currentGrade = this.roleData.grade;
        // 如果没找到，返回0阶
        return currentGrade;
    }

    /**
     * 获取当前阶数的最大等级
     * @param grade 阶数
     * @returns 该阶数的最大等级
     */
    private getMaxLevelForGrade(grade: number): number {
        const gradeData = blhStatic.heroGradeUpMgr.getData(grade + 1); // 数据表ID从1开始
        let maxLv = gradeData ? gradeData.maxLv : 0;
        blhkc.err("最大等级: ", maxLv);
        return maxLv;
    }

    /**
     * 计算可升级的最大等级数
     * @param currentZsjc 当前拥有的战术教材数量
     * @param currentGoods 当前拥有的通用材料数量
     * @returns 可升级的最大等级数
     */
    private calculateMaxUpgradeLevels(currentZsjc: number, currentGoods: number): number {
        let maxLevels = 0;
        let tempZsjc = currentZsjc;
        let tempGoods = currentGoods;
        let currentLevel = this.roleData.level;

        // 获取当前阶数和该阶数的最大等级
        const currentGrade = this.getCurrentGrade();
        const maxLevelForCurrentGrade = this.getMaxLevelForGrade(currentGrade);

        console.error(`当前等级: ${currentLevel}, 当前阶数: ${currentGrade}, 当前阶最大等级: ${maxLevelForCurrentGrade}`);

        // 最多升5级
        for (let i = 1; i <= 5; i++) {
            const nextLevel = currentLevel + i;

            // 检查是否超过当前阶数的最大等级
            if (nextLevel > maxLevelForCurrentGrade) {
                console.error(`等级${nextLevel}超过当前阶数${currentGrade}的最大等级${maxLevelForCurrentGrade}，需要升阶`);
                break; // 超过当前阶数最大等级，需要升阶
            }

            // 检查材料是否足够
            const lvData = blhStatic.heroLvUpMgr.getData(nextLevel);
            if (lvData && tempZsjc >= lvData.costzsjc && tempGoods >= lvData.costgoods) {
                tempZsjc -= lvData.costzsjc;
                tempGoods -= lvData.costgoods;
                maxLevels = i;
                console.error(`可以升到等级${nextLevel}，剩余材料 - 战术教材: ${tempZsjc}, 通用材料: ${tempGoods}`);
            } else {
                console.error(`材料不足升到等级${nextLevel}`);
                break; // 材料不足就停止
            }
        }

        console.error(`最终可升级等级数: ${maxLevels}`);
        return maxLevels;
    }

    /**
     * 计算升级指定等级数所需的总材料
     * @param levels 要升级的等级数
     * @returns 总消耗的材料
     */
    private calculateTotalCost(levels: number): {zsjc: number, goods: number} {
        let totalZsjc = 0;
        let totalGoods = 0;
        console.error("levels: ", levels);
        let nowLv = this.roleData.level;
        for (let i = nowLv; i < levels + nowLv; i++) {
            const nextLevel = i;
            const lvData = blhStatic.heroLvUpMgr.getData(nextLevel);
            console.error("++++: ", lvData.costzsjc);
            if (lvData) {
                totalZsjc += lvData.costzsjc;
                totalGoods += lvData.costgoods;
            }
        }

        return {zsjc: totalZsjc, goods: totalGoods};
    }


    /**点击升级按钮 */
    clickLvUpBtn(){
        if(this.canClikLvUpBtn){
            if (this.canGradeUp()) {
                this.performGradeUp();
            } else {
                this.performLevelUp();
            }
        } else {
            console.error("升级按钮不可点击");
        }
    }

    /**
     * 执行升阶操作
     */
    private performGradeUp() {
        console.error("点击升阶按钮");

        const currentGrade = this.getCurrentGrade();
        const nextGradeData = blhStatic.heroGradeUpMgr.getData(currentGrade + 1); // 数据表ID从1开始

        if (nextGradeData) {
            const currentXzdy = blhStatic.commonData.propData["" + blPropType.xzdy] || 0;

            if (currentXzdy >= nextGradeData.costXzdy) {
                // 扣除心智单元
                blhStatic.commonData.propData["" + blPropType.xzdy] = currentXzdy - nextGradeData.costXzdy;

                // 升阶后等级变为下一阶的起始等级
                const prevGradeMaxLevel = this.getMaxLevelForGrade(currentGrade);
                this.roleData.level = prevGradeMaxLevel + 1;

                // 保存数据
                blhStatic.commonData.save();

                console.error(`升阶成功！从${currentGrade}阶升到${currentGrade + 1}阶，当前等级：${this.roleData.level}`);
                console.error(`消耗心智单元：${nextGradeData.costXzdy}`);

                // 刷新UI显示
                this.refreshLabel();
                this.refreshLvState();

                // 通知父界面刷新材料显示
                this.notifyParentRefresh();
            }
        }
    }

    /**
     * 执行升级操作
     */
    private performLevelUp() {
        console.error("点击升级按钮");

        // 获取当前拥有的材料数量
        const currentZsjc = blhStatic.commonData.propData["" + blPropType.zsjc] || 0;
        const currentGoods = blhStatic.commonData.propData["" + blPropType.goods] || 0;

        // 计算可升级的最大等级数
        const maxUpgradeLevels = this.calculateMaxUpgradeLevels(currentZsjc, currentGoods);

        if (maxUpgradeLevels > 0) {
            // 计算升级所需的总材料
            const totalCost = this.calculateTotalCost(maxUpgradeLevels);

            // 扣除材料
            blhStatic.commonData.propData["" + blPropType.zsjc] = currentZsjc - totalCost.zsjc;
            blhStatic.commonData.propData["" + blPropType.goods] = currentGoods - totalCost.goods;

            // 提升角色等级
            this.roleData.level += maxUpgradeLevels;

            // 保存数据
            blhStatic.commonData.save();

            console.error(`升级成功！提升了${maxUpgradeLevels}级，当前等级：${this.roleData.level}`);
            console.error(`消耗材料 - 战术教材：${totalCost.zsjc}，通用材料：${totalCost.goods}`);

            // 刷新UI显示
            this.refreshLabel();
            this.refreshLvState();

            // 通知父界面刷新材料显示
            this.notifyParentRefresh();
        }
    }

    /**
     * 通知父界面刷新材料显示
     */
    private notifyParentRefresh() {
        // 查找父界面并刷新材料显示
        let parent = this.node.parent;
        while (parent) {
            const teamUpUI = parent.getComponent('blhTeamUpUI');
            if (teamUpUI && teamUpUI.refreshLabel) {
                teamUpUI.refreshLabel();
                break;
            }
            parent = parent.parent;
        }
    }




    static create(parent: cc.Node, roleData: OwnedHero): void {
        const node: cc.Node = cc.instantiate(blhResMgr.ins().getNode("teamMemberItem"));
        node.parent = parent;
        node.getComponent(blhTeamMemberItem).init(roleData);
    }


}