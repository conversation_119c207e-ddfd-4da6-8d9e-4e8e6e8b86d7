import { OwnedHero } from "../items/blhCommonData";
import { blPropType } from "../mgr/blhConst";
import blhResMgr from "../mgr/blhResMgr";
import { blhStatic } from "../mgr/blhStatic";
import blhkc from "../utils/blhkc";
const { ccclass, property } = cc._decorator;

@ccclass
export default class blhTeamMemberItem extends cc.Component {

    @property({
        displayName: "角色名称lab",
        type: cc.Label
    })
    lab_roleName: cc.Label = null;

    @property({
        displayName: "角色战力lab",
        type: cc.Label
    })
    lab_roleFight: cc.Label = null;

    @property({
        displayName: "等级进度条",
        type: cc.ProgressBar
    })
    pro_lvup: cc.ProgressBar = null;

    @property({
        displayName: "阶数lab",
        type: cc.Label
    })
    lab_grade: cc.Label = null;

    @property({
        displayName: "等级lab",
        type: cc.Label
    })
    lab_lv: cc.Label = null;

    @property({
        displayName: "消耗战术教材lab",
        type: cc.Label
    })
    lab_costZsjc: cc.Label = null;

    @property({
        displayName: "消耗通用金币lab",
        type: cc.Label
    })
    lab_costGoods: cc.Label = null;

    @property({
        displayName: "升级按钮",
        type: cc.Node
    })
    btn_lvup: cc.Node = null;

    @property({
        displayName: "升级按钮文字",
        type: cc.Label
    })
    lab_btnlvup: cc.Label = null;


    @property({
        displayName: "升级箭头",
        type: cc.Node
    })
    node_lvupArrow: cc.Node = null;

    roleData: OwnedHero = null;
    //是否可以点击升级按钮
    canClikLvUpBtn: boolean = true;

    protected onEnable(): void {
        blhStatic.commonData.propData["" + blPropType.zsjc] = 100000;
        blhStatic.commonData.propData["" + blPropType.goods] = 100000;
        blhStatic.commonData.save();
        this.btn_lvup.on(cc.Node.EventType.TOUCH_END, this.clickLvUpBtn, this);
    }

    //初始化
    init(roleData: OwnedHero){
        console.error("角色数据: ", roleData);
        this.roleData = roleData;
        this.refreshLabel();
        this.refreshLvState();
    }

    /**更新label */
    refreshLabel(){
        let heroData = blhStatic.heroMgr.getHeroData(this.roleData.roleId);
        this.lab_roleName.string = heroData.name;
        this.lab_lv.string = this.roleData.level + "级";
        let grade = Math.floor(this.roleData.level / 10);
        if(grade > 0){
            this.lab_grade.string = grade + "阶";
        }
        let pro = this.roleData.level % 10;
        this.pro_lvup.progress = pro / 10;
    }

    refreshLvState(){
        // 获取当前拥有的材料数量
        const currentZsjc = blhStatic.commonData.propData["" + blPropType.zsjc] || 0;
        const currentGoods = blhStatic.commonData.propData["" + blPropType.goods] || 0;

        // 计算可升级的最大等级数
        const maxUpgradeLevels = this.calculateMaxUpgradeLevels(currentZsjc, currentGoods);

        if (maxUpgradeLevels > 0) {
            // 计算升级所需的总材料
            const totalCost = this.calculateTotalCost(maxUpgradeLevels);

            // 设置按钮状态为可点击
            this.canClikLvUpBtn = true;
            this.btn_lvup.color = new cc.Color(255, 255, 255);
            this.node_lvupArrow.active = true;

            // 设置按钮文字和消耗显示
            if (maxUpgradeLevels === 1) {
                this.lab_btnlvup.string = "升一级";
            } else {
                this.lab_btnlvup.string = `升${maxUpgradeLevels}级`;
            }

            this.lab_costZsjc.string = "X" + totalCost.zsjc;
            this.lab_costGoods.string = "X" + totalCost.goods;
        } else {
            // 材料不足，设置按钮状态为不可点击
            this.canClikLvUpBtn = false;
            this.btn_lvup.color = new cc.Color(82, 56, 56);
            this.lab_btnlvup.string = "升级";
            this.node_lvupArrow.active = false;

            // 显示升一级所需的材料
            const nextLvData = blhStatic.heroLvUpMgr.getData(this.roleData.level);
            if (nextLvData) {
                this.lab_costZsjc.string = "X" + nextLvData.costzsjc;
                this.lab_costGoods.string = "X" + nextLvData.costgoods;
            }
        }
    }

    /**
     * 计算可升级的最大等级数
     * @param currentZsjc 当前拥有的战术教材数量
     * @param currentGoods 当前拥有的通用材料数量
     * @returns 可升级的最大等级数
     */
    private calculateMaxUpgradeLevels(currentZsjc: number, currentGoods: number): number {
        let maxLevels = 0;
        let tempZsjc = currentZsjc;
        let tempGoods = currentGoods;
        let currentLevel = this.roleData.level;

        // 最多升5级
        for (let i = 1; i <= 5; i++) {
            const nextLevel = currentLevel + i;

            // 检查是否会跨越升阶（每10级一次升阶）
            const currentGrade = Math.floor(currentLevel / 10);
            const nextGrade = Math.floor(nextLevel / 10);

            // 如果跨越升阶，只能升到升阶前的最后一级
            if (nextGrade > currentGrade) {
                // 计算到升阶前还能升几级
                const levelsToGrade = (currentGrade + 1) * 10 - currentLevel;
                if (levelsToGrade > 0 && i <= levelsToGrade) {
                    // 检查材料是否足够升到升阶前
                    const lvData = blhStatic.heroLvUpMgr.getData(nextLevel);
                    if (lvData && tempZsjc >= lvData.costzsjc && tempGoods >= lvData.costgoods) {
                        tempZsjc -= lvData.costzsjc;
                        tempGoods -= lvData.costgoods;
                        maxLevels = i;
                    }
                }
                break; // 遇到升阶就停止
            }

            // 检查材料是否足够
            const lvData = blhStatic.heroLvUpMgr.getData(nextLevel);
            if (lvData && tempZsjc >= lvData.costzsjc && tempGoods >= lvData.costgoods) {
                tempZsjc -= lvData.costzsjc;
                tempGoods -= lvData.costgoods;
                maxLevels = i;
            } else {
                break; // 材料不足就停止
            }
        }

        return maxLevels;
    }

    /**
     * 计算升级指定等级数所需的总材料
     * @param levels 要升级的等级数
     * @returns 总消耗的材料
     */
    private calculateTotalCost(levels: number): {zsjc: number, goods: number} {
        let totalZsjc = 0;
        let totalGoods = 0;
        console.error("levels: ", levels);
        let nowLv = this.roleData.level;
        for (let i = nowLv; i < levels + nowLv; i++) {
            const nextLevel = i;
            const lvData = blhStatic.heroLvUpMgr.getData(nextLevel);
            console.error("++++: ", lvData.costzsjc);
            if (lvData) {
                totalZsjc += lvData.costzsjc;
                totalGoods += lvData.costgoods;
            }
        }

        return {zsjc: totalZsjc, goods: totalGoods};
    }


    /**点击升级按钮 */
    clickLvUpBtn(){
        if(this.canClikLvUpBtn){
            console.error("点击升级按钮");

            // 获取当前拥有的材料数量
            const currentZsjc = blhStatic.commonData.propData["" + blPropType.zsjc] || 0;
            const currentGoods = blhStatic.commonData.propData["" + blPropType.goods] || 0;

            // 计算可升级的最大等级数
            const maxUpgradeLevels = this.calculateMaxUpgradeLevels(currentZsjc, currentGoods);

            if (maxUpgradeLevels > 0) {
                // 计算升级所需的总材料
                const totalCost = this.calculateTotalCost(maxUpgradeLevels);

                // 扣除材料
                blhStatic.commonData.propData["" + blPropType.zsjc] = currentZsjc - totalCost.zsjc;
                blhStatic.commonData.propData["" + blPropType.goods] = currentGoods - totalCost.goods;

                // 提升角色等级
                this.roleData.level += maxUpgradeLevels;

                // 保存数据
                blhStatic.commonData.save();

                console.error(`升级成功！提升了${maxUpgradeLevels}级，当前等级：${this.roleData.level}`);
                console.error(`消耗材料 - 战术教材：${totalCost.zsjc}，通用材料：${totalCost.goods}`);

                // 刷新UI显示
                this.refreshLabel();
                this.refreshLvState();

                // 通知父界面刷新材料显示（如果需要的话）
                // 可以发送事件或直接调用父界面的刷新方法
                this.notifyParentRefresh();
            }
        } else {
            console.error("升级按钮不可点击");
        }
    }

    /**
     * 通知父界面刷新材料显示
     */
    private notifyParentRefresh() {
        // 查找父界面并刷新材料显示
        let parent = this.node.parent;
        while (parent) {
            const teamUpUI = parent.getComponent('blhTeamUpUI');
            if (teamUpUI && teamUpUI.refreshLabel) {
                teamUpUI.refreshLabel();
                break;
            }
            parent = parent.parent;
        }
    }




    static create(parent: cc.Node, roleData: OwnedHero): void {
        const node: cc.Node = cc.instantiate(blhResMgr.ins().getNode("teamMemberItem"));
        node.parent = parent;
        node.getComponent(blhTeamMemberItem).init(roleData);
    }


}