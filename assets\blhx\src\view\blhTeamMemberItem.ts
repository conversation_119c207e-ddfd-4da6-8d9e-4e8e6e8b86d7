import { OwnedHero } from "../items/blhCommonData";
import { blPropType } from "../mgr/blhConst";
import blhResMgr from "../mgr/blhResMgr";
import { blhStatic } from "../mgr/blhStatic";
import blhkc from "../utils/blhkc";
const { ccclass, property } = cc._decorator;

@ccclass
export default class blhTeamMemberItem extends cc.Component {

    @property({
        displayName: "角色名称lab",
        type: cc.Label
    })
    lab_roleName: cc.Label = null;

    @property({
        displayName: "角色战力lab",
        type: cc.Label
    })
    lab_roleFight: cc.Label = null;

    @property({
        displayName: "等级进度条",
        type: cc.ProgressBar
    })
    pro_lvup: cc.ProgressBar = null;

    @property({
        displayName: "阶数lab",
        type: cc.Label
    })
    lab_grade: cc.Label = null;

    @property({
        displayName: "等级lab",
        type: cc.Label
    })
    lab_lv: cc.Label = null;

    @property({
        displayName: "消耗战术教材lab",
        type: cc.Label
    })
    lab_costZsjc: cc.Label = null;

    @property({
        displayName: "消耗通用金币lab",
        type: cc.Label
    })
    lab_costGoods: cc.Label = null;

    @property({
        displayName: "升级按钮",
        type: cc.Node
    })
    btn_lvup: cc.Node = null;

    @property({
        displayName: "升级按钮文字",
        type: cc.Label
    })
    lab_btnlvup: cc.Label = null;


    @property({
        displayName: "升级箭头",
        type: cc.Node
    })
    node_lvupArrow: cc.Node = null;

    roleData: OwnedHero = null;
    //是否可以点击升级按钮
    canClikLvUpBtn: boolean = true;

    protected onEnable(): void {
        this.btn_lvup.on(cc.Node.EventType.TOUCH_END, this.clickLvUpBtn, this);
    }

    //初始化
    init(roleData: OwnedHero){
        console.error("角色数据: ", roleData);
        this.roleData = roleData;
        this.refreshLabel();
        this.refreshLvState();
    }

    /**更新label */
    refreshLabel(){
        let heroData = blhStatic.heroMgr.getHeroData(this.roleData.roleId);
        this.lab_roleName.string = heroData.name;
        this.lab_lv.string = this.roleData.level + "级";
        let grade = Math.floor(this.roleData.level / 10);
        if(grade > 0){
            this.lab_grade.string = grade + "阶";
        }
        let pro = this.roleData.level % 10;
        this.pro_lvup.progress = pro / 10;
    }

    refreshLvState(){
        // 获取当前拥有的材料数量
        const currentZsjc = blhStatic.commonData.propData["" + blPropType.zsjc] || 0;
        const currentGoods = blhStatic.commonData.propData["" + blPropType.goods] || 0;

        // 计算可升级的最大等级数
        const maxUpgradeLevels = this.calculateMaxUpgradeLevels(currentZsjc, currentGoods);

        if (maxUpgradeLevels > 0) {
            // 计算升级所需的总材料
            const totalCost = this.calculateTotalCost(maxUpgradeLevels);

            // 设置按钮状态为可点击
            this.canClikLvUpBtn = true;
            this.btn_lvup.color = new cc.Color(255, 255, 255);
            this.node_lvupArrow.active = true;

            // 设置按钮文字和消耗显示
            if (maxUpgradeLevels === 1) {
                this.lab_btnlvup.string = "升一级";
            } else {
                this.lab_btnlvup.string = `升${maxUpgradeLevels}级`;
            }

            this.lab_costZsjc.string = "X" + totalCost.zsjc;
            this.lab_costGoods.string = "X" + totalCost.goods;
        } else {
            // 材料不足，设置按钮状态为不可点击
            this.canClikLvUpBtn = false;
            this.btn_lvup.color = new cc.Color(82, 56, 56);
            this.lab_btnlvup.string = "升级";
            this.node_lvupArrow.active = false;

            // 显示升一级所需的材料
            const nextLvData = blhStatic.heroLvUpMgr.getData(this.roleData.level + 1);
            if (nextLvData) {
                this.lab_costZsjc.string = "X" + nextLvData.costzsjc;
                this.lab_costGoods.string = "X" + nextLvData.costgoods;
            }
        }
    }


    /**点击升级按钮 */
    clickLvUpBtn(){
        if(this.canClikLvUpBtn){
            console.error("点击升级按钮");
        }else{
            console.error("升级按钮不可点击");
        }
    }




    static create(parent: cc.Node, roleData: OwnedHero): void {
        const node: cc.Node = cc.instantiate(blhResMgr.ins().getNode("teamMemberItem"));
        node.parent = parent;
        node.getComponent(blhTeamMemberItem).init(roleData);
    }


}