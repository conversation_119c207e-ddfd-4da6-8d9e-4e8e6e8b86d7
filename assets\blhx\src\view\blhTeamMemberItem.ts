import { OwnedHero } from "../items/blhCommonData";
import { blPropType } from "../mgr/blhConst";
import blhResMgr from "../mgr/blhResMgr";
import { blhStatic } from "../mgr/blhStatic";
import blhkc from "../utils/blhkc";
const { ccclass, property } = cc._decorator;

@ccclass
export default class blhTeamMemberItem extends cc.Component {

    @property({
        displayName: "角色名称lab",
        type: cc.Label
    })
    lab_roleName: cc.Label = null;

    @property({
        displayName: "角色战力lab",
        type: cc.Label
    })
    lab_roleFight: cc.Label = null;

    @property({
        displayName: "等级进度条",
        type: cc.ProgressBar
    })
    pro_lvup: cc.ProgressBar = null;

    @property({
        displayName: "阶数lab",
        type: cc.Label
    })
    lab_grade: cc.Label = null;

    @property({
        displayName: "等级lab",
        type: cc.Label
    })
    lab_lv: cc.Label = null;

    @property({
        displayName: "消耗战术教材lab",
        type: cc.Label
    })
    lab_costZsjc: cc.Label = null;

    @property({
        displayName: "消耗通用金币lab",
        type: cc.Label
    })
    lab_costGoods: cc.Label = null;

    @property({
        displayName: "升级按钮",
        type: cc.Node
    })
    btn_lvup: cc.Node = null;

    @property({
        displayName: "升级按钮文字",
        type: cc.Label
    })
    lab_btnlvup: cc.Label = null;


    @property({
        displayName: "升级箭头",
        type: cc.Node
    })
    node_lvupArrow: cc.Node = null;

    roleData: OwnedHero = null;
    //是否可以点击升级按钮
    canClikLvUpBtn: boolean = true;

    protected onEnable(): void {
        this.btn_lvup.on(cc.Node.EventType.TOUCH_END, this.clickLvUpBtn, this);
    }

    //初始化
    init(roleData: OwnedHero){
        console.error("角色数据: ", roleData);
        this.roleData = roleData;
        this.refreshLabel();
        this.refreshLvState();
    }

    /**更新label */
    refreshLabel(){
        let heroData = blhStatic.heroMgr.getHeroData(this.roleData.roleId);
        this.lab_roleName.string = heroData.name;
        this.lab_lv.string = this.roleData.level + "级";
        let grade = Math.floor(this.roleData.level / 10);
        if(grade > 0){
            this.lab_grade.string = grade + "阶";
        }
        let pro = this.roleData.level % 10;
        this.pro_lvup.progress = pro / 10;
    }

    refreshLvState(){
        //升级需要的材料
        let lvData = blhStatic.heroLvUpMgr.getData(this.roleData.level);
        // let costZsjc = lvData.costzsjc;
        // let costgoods = lvData.costgoods;
        // this.lab_costZsjc.string = "X" + costZsjc;
        // this.lab_costGoods.string = "X" + costgoods;
        // if(costZsjc >= blhStatic.commonData.propData["" + blPropType.zsjc] && costgoods >= blhStatic.commonData.propData["" + blPropType.goods]){
        //     this.canClikLvUpBtn = true;
        //     this.btn_lvup.color = new cc.Color(255, 255, 255);
        //     this.lab_btnlvup.string = "升一级";
        //     this.node_lvupArrow.active = true;
        // }else{
        //     this.canClikLvUpBtn = false;
        //     this.btn_lvup.color = new cc.Color(82, 56, 56);
        //     this.lab_btnlvup.string = "升级";
        //     this.node_lvupArrow.active = false;
        // }
    }


    /**点击升级按钮 */
    clickLvUpBtn(){
        if(this.canClikLvUpBtn){
            console.error("点击升级按钮");
        }else{
            console.error("升级按钮不可点击");
        }
    }




    static create(parent: cc.Node, roleData: OwnedHero): void {
        const node: cc.Node = cc.instantiate(blhResMgr.ins().getNode("teamMemberItem"));
        node.parent = parent;
        node.getComponent(blhTeamMemberItem).init(roleData);
    }


}