import { battleMode, Prefabs, blPropType } from "../mgr/blhConst";
import blhResMgr from "../mgr/blhResMgr";
import { blhStatic } from "../mgr/blhStatic";
import blhTeamMemberItem from "./blhTeamMemberItem";

const { ccclass, property } = cc._decorator;

@ccclass
export default class blhTeamUpUI extends cc.Component {
    @property({
        displayName: "战术教材数量",
        type: cc.Label
    })
    lab_zsjc: cc.Label = null;

    @property({
        displayName: "心智单元数量",
        type: cc.Label
    })
    lab_xzdy: cc.Label = null;
    @property({
        displayName: "金币数量",
        type: cc.Label
    })
    lab_coin: cc.Label = null;

    @property({
        displayName: "增加战术教材按钮",
        type: cc.Node
    })
    btn_addZsjc: cc.Node = null;

    @property({
        displayName: "增加心智单元按钮",
        type: cc.Node
    })
    btn_addXzdy: cc.Node = null;

    @property({
        displayName: "增加金币按钮",
        type: cc.Node
    })
    btn_addCoin: cc.Node = null;

    @property({
        displayName: "总等级",
        type: cc.Label
    })
    lab_allLevel: cc.Label = null;

    @property({
        displayName: "舰队成员Content",
        type: cc.Node
    })
    content_member: cc.Node = null;





    protected onEnable(): void {
        this.btn_addZsjc.on(cc.Node.EventType.TOUCH_END, this.clickAddZsjc, this);
        this.btn_addXzdy.on(cc.Node.EventType.TOUCH_END, this.clickAddXzdy, this);
        this.btn_addCoin.on(cc.Node.EventType.TOUCH_END, this.clickAddCoin, this);
        this.refreshLabel();
        this.loadMemberItem();
    }

    /**点击增加战术教材 */
    public clickAddZsjc() {
        let coin = blhStatic.commonData.propData["" + blPropType.zsjc] || 0;
        coin += 100;
        blhStatic.commonData.propData["" + blPropType.zsjc] = coin;
        blhStatic.commonData.save();
        this.refreshLabel();
    }
    /**点击增加心智单元 */
    public clickAddXzdy() {
        let coin = blhStatic.commonData.propData["" + blPropType.xzdy] || 0;
        coin += 100;
        blhStatic.commonData.propData["" + blPropType.xzdy] = coin;
        blhStatic.commonData.save();
        this.refreshLabel();
    }
    /**点击增加金币 */
    public clickAddCoin() {
        let coin = blhStatic.commonData.propData["" + blPropType.goods] || 0;
        coin += 100;
        blhStatic.commonData.propData["" + blPropType.goods] = coin;
        blhStatic.commonData.save();
        this.refreshLabel();
    }

    /**加载战队出战成员 */
    loadMemberItem(){
        blhStatic.commonData.roleData.forEach(element => {
            if(element && element.fightState){
                blhTeamMemberItem.create(this.content_member);
            }
        });
    }

    refreshLabel() {
        this.lab_zsjc.string = blhStatic.commonData.propData["" + blPropType.zsjc] || 0;
        this.lab_xzdy.string = blhStatic.commonData.propData["" + blPropType.xzdy] || 0;
        this.lab_coin.string = blhStatic.commonData.propData["" + blPropType.goods] || 0;
    }


    static create(parent: cc.Node): void {
        const node: cc.Node = cc.instantiate(blhResMgr.ins().getNode(Prefabs.teamUpUI));
        node.parent = parent;
    }


}