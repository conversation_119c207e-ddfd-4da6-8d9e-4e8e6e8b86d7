import { battleMode, Prefabs, blPropType } from "../mgr/blhConst";
import blhResMgr from "../mgr/blhResMgr";
import { blhStatic } from "../mgr/blhStatic";

const { ccclass, property } = cc._decorator;

@ccclass
export default class blhTeamUpUI extends cc.Component {
    @property({
        displayName: "战术教材数量",
        type: cc.Label
    })
    lab_zsjc: cc.Label = null;

    @property({
        displayName: "心智单元数量",
        type: cc.Label
    })
    lab_xzdy: cc.Label = null;
    @property({
        displayName: "金币数量",
        type: cc.Label
    })
    lab_coin: cc.Label = null;


    protected onEnable(): void {
        this.refreshLabel();
    }


    refreshLabel(){
        this.lab_zsjc.string = blhStatic.commonData.propData["" + blPropType.zsjc] || 0;
        this.lab_xzdy.string = blhStatic.commonData.propData["" + blPropType.xzdy] || 0;
        this.lab_coin.string = blhStatic.commonData.propData["" + blPropType.goods] || 0;
    }


    static create(parent: cc.Node): void {
        const node : cc.Node = cc.instantiate(blhResMgr.ins().getNode(Prefabs.teamUpUI));
        node.parent = parent;
    }


}