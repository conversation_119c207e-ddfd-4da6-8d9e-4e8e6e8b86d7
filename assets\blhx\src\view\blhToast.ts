import blhResMgr from "../mgr/blhResMgr";


const { ccclass, property } = cc._decorator;

@ccclass
export default class blhToast extends cc.Component {


    @property(cc.Label)
    txt: cc.Label = null;
    @property(cc.Node)
    layout: cc.Node = null;


    resetLayout() {
        if (this.layout.childrenCount <= 1) {
            return;
        }
        for (let i = 1; i < this.layout.childrenCount; i++) {
            const one = this.layout.children[i];
            one.removeFromParent();
            one.destroy();
        }
    }


    show(txt: string, parent: cc.Node, otherNodes: Array<cc.Node>, startY: number = 0) {
        this.txt.string = txt;
        this.node.parent = parent;
        this.node.y = startY - 100;
        this.node.opacity = 10;
        this.node.zIndex = 3000;
        this.resetLayout();
        if (otherNodes && otherNodes.length > 0) {
            for (let i = 0; i < otherNodes.length; i++) {
                const one = otherNodes[i];
                this.layout.addChild(one);
            }
        }

        cc.tween(this.node).to(0.3, { y: startY + 100, opacity: 255 }).to(1.2, { y: startY + 120, opacity: 255 }).to(0.3, { y: startY + 200, opacity: 0 }).call(() => {
            this.node.destroy();
        }).start();

        // this.scheduleOnce(() => {
        //     if (this.node && this.node.isValid) {
        //         this.node.destroy();
        //     }
        // }, 1);
    }

    static create(parent: cc.Node, txt: string, otherNodes: Array<cc.Node> = null, startY: number = 0) {
        blhResMgr.ins().getNodeAsync('toast', (err: any, newNode: cc.Node) => {
            newNode.getComponent(blhToast).show(txt, parent, otherNodes, startY);
        });
    }

}