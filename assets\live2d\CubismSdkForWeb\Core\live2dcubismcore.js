/**
 * Copyright(c) Live2D Inc. All rights reserved.
 *
 * Use of this source code is governed by the Live2D Proprietary Software license
 * that can be found at https://www.live2d.com/eula/live2d-proprietary-software-license-agreement_en.html.
 */
var Live2DCubismCore;
(function (Live2DCubismCore) {
    /** C calls. */
    var _csm = /** @class */ (function () {
        function _csm() {
        }
        _csm.getVersion = function () {
            return _em.ccall("csmGetVersion", "number", [], []);
        };
        _csm.getLatestMocVersion = function () {
            return _em.ccall("csmGetLatestMocVersion", "number", [], []);
        };
        _csm.getMocVersion = function (moc) {
            return _em.ccall("csmGetMocVersion", "number", ["number"], [moc]);
        };
        _csm.getSizeofModel = function (moc) {
            return _em.ccall("csmGetSizeofModel", "number", ["number"], [moc]);
        };
        _csm.reviveMocInPlace = function (memory, mocSize) {
            return _em.ccall("csmReviveMocInPlace", "number", ["number", "number"], [memory, mocSize]);
        };
        _csm.initializeModelInPlace = function (moc, memory, modelSize) {
            return _em.ccall("csmInitializeModelInPlace", "number", ["number", "number", "number"], [moc, memory, modelSize]);
        };
        _csm.getParameterCount = function (model) {
            return _em.ccall("csmGetParameterCount", "number", ["number"], [model]);
        };
        _csm.getParameterIds = function (model) {
            return _em.ccall("csmGetParameterIds", "number", ["number"], [model]);
        };
        _csm.getParameterMinimumValues = function (model) {
            return _em.ccall("csmGetParameterMinimumValues", "number", ["number"], [model]);
        };
        _csm.getParameterMaximumValues = function (model) {
            return _em.ccall("csmGetParameterMaximumValues", "number", ["number"], [model]);
        };
        _csm.getParameterDefaultValues = function (model) {
            return _em.ccall("csmGetParameterDefaultValues", "number", ["number"], [model]);
        };
        _csm.getParameterValues = function (model) {
            return _em.ccall("csmGetParameterValues", "number", ["number"], [model]);
        };
        _csm.getPartCount = function (model) {
            return _em.ccall("csmGetPartCount", "number", ["number"], [model]);
        };
        _csm.getPartIds = function (model) {
            return _em.ccall("csmGetPartIds", "number", ["number"], [model]);
        };
        _csm.getPartOpacities = function (model) {
            return _em.ccall("csmGetPartOpacities", "number", ["number"], [model]);
        };
        _csm.getPartParentPartIndices = function (model) {
            return _em.ccall("csmGetPartParentPartIndices", "number", ["number"], [model]);
        };
        _csm.getDrawableCount = function (model) {
            return _em.ccall("csmGetDrawableCount", "number", ["number"], [model]);
        };
        _csm.getDrawableIds = function (model) {
            return _em.ccall("csmGetDrawableIds", "number", ["number"], [model]);
        };
        _csm.getDrawableConstantFlags = function (model) {
            return _em.ccall("csmGetDrawableConstantFlags", "number", ["number"], [model]);
        };
        _csm.getDrawableDynamicFlags = function (model) {
            return _em.ccall("csmGetDrawableDynamicFlags", "number", ["number"], [model]);
        };
        _csm.getDrawableTextureIndices = function (model) {
            return _em.ccall("csmGetDrawableTextureIndices", "number", ["number"], [model]);
        };
        _csm.getDrawableDrawOrders = function (model) {
            return _em.ccall("csmGetDrawableDrawOrders", "number", ["number"], [model]);
        };
        _csm.getDrawableRenderOrders = function (model) {
            return _em.ccall("csmGetDrawableRenderOrders", "number", ["number"], [model]);
        };
        _csm.getDrawableOpacities = function (model) {
            return _em.ccall("csmGetDrawableOpacities", "number", ["number"], [model]);
        };
        _csm.getDrawableMaskCounts = function (model) {
            return _em.ccall("csmGetDrawableMaskCounts", "number", ["number"], [model]);
        };
        _csm.getDrawableMasks = function (model) {
            return _em.ccall("csmGetDrawableMasks", "number", ["number"], [model]);
        };
        _csm.getDrawableVertexCounts = function (model) {
            return _em.ccall("csmGetDrawableVertexCounts", "number", ["number"], [model]);
        };
        _csm.getDrawableVertexPositions = function (model) {
            return _em.ccall("csmGetDrawableVertexPositions", "number", ["number"], [model]);
        };
        _csm.getDrawableVertexUvs = function (model) {
            return _em.ccall("csmGetDrawableVertexUvs", "number", ["number"], [model]);
        };
        _csm.getDrawableIndexCounts = function (model) {
            return _em.ccall("csmGetDrawableIndexCounts", "number", ["number"], [model]);
        };
        _csm.getDrawableIndices = function (model) {
            return _em.ccall("csmGetDrawableIndices", "number", ["number"], [model]);
        };
        _csm.mallocMoc = function (mocSize) {
            return _em.ccall("csmMallocMoc", "number", ["number"], [mocSize]);
        };
        _csm.mallocModelAndInitialize = function (moc) {
            return _em.ccall("csmMallocModelAndInitialize", "number", ["number"], [moc]);
        };
        _csm.malloc = function (size) {
            return _em.ccall("csmMalloc", "number", ["number"], [size]);
        };
        _csm.setLogFunction = function (handler) {
            _em.ccall("csmSetLogFunction", null, ["number"], [handler]);
        };
        _csm.updateModel = function (model) {
            _em.ccall("csmUpdateModel", null, ["number"], [model]);
        };
        _csm.readCanvasInfo = function (model, outSizeInPixels, outOriginInPixels, outPixelsPerUnit) {
            _em.ccall("csmReadCanvasInfo", null, ["number", "number", "number", "number"], [model, outSizeInPixels, outOriginInPixels, outPixelsPerUnit]);
        };
        _csm.resetDrawableDynamicFlags = function (model) {
            _em.ccall("csmResetDrawableDynamicFlags", null, ["number"], [model]);
        };
        _csm.free = function (memory) {
            _em.ccall("csmFree", null, ["number"], [memory]);
        };
        return _csm;
    }());
    ;
    /** Cubism version. */
    var Version = /** @class */ (function () {
        function Version() {
        }
        /**
         * Queries Core version.
         *
         * @return Core version.
         */
        Version.csmGetVersion = function () {
            return _csm.getVersion();
        };
        /**
         * Gets Moc file supported latest version.
         *
         * @return Moc file latest format version.
         */
        Version.csmGetLatestMocVersion = function () {
            return _csm.getLatestMocVersion();
        };
        /**
         * Gets Moc file format version.
         *
         * @param moc Moc
         *
         * @return csmMocVersion
         */
        Version.csmGetMocVersion = function (moc) {
            return _csm.getMocVersion(moc._ptr);
        };
        return Version;
    }());
    Live2DCubismCore.Version = Version;
    /** Cubism logging. */
    var Logging = /** @class */ (function () {
        function Logging() {
        }
        /**
         * Sets log handler.
         *
         * @param handler  Handler to use.
         */
        Logging.csmSetLogFunction = function (handler) {
            // Cache log handler.
            Logging.logFunction = handler;
            // Wrap function to pointer.
            var pointer = _em.addFunction(Logging.wrapLogFunction, 'vi');
            // Sets log handler.
            _csm.setLogFunction(pointer);
        };
        /**
         * Queries log handler.
         *
         * @return Log handler.
         */
        Logging.csmGetLogFunction = function () {
            return Logging.logFunction;
        };
        /**
         * Wrap log function.
         *
         * @param messagePtr number
         *
         * @return string
         */
        Logging.wrapLogFunction = function (messagePtr) {
            // Pointer to string.
            var messageStr = _em.UTF8ToString(messagePtr);
            // Run log function.
            Logging.logFunction(messageStr);
        };
        return Logging;
    }());
    Live2DCubismCore.Logging = Logging;
    /** Cubism moc. */
    var Moc = /** @class */ (function () {
        /**
         * Initializes instance.
         *
         * @param mocBytes Moc bytes.
         */
        function Moc(mocBytes) {
            // Allocate memory.
            var memory = _csm.mallocMoc(mocBytes.byteLength);
            if (!memory) {
                return;
            }
            // Initialize memory.
            var destination = new Uint8Array(_em.HEAPU8.buffer, memory, mocBytes.byteLength);
            destination.set(new Uint8Array(mocBytes));
            // Revive moc.
            this._ptr = _csm.reviveMocInPlace(memory, mocBytes.byteLength);
            if (!this._ptr) {
                _csm.free(memory);
            }
        }
        /** Creates [[Moc]] from [[ArrayBuffer]].
         *
         * @param buffer Array buffer
         *
         * @return [[Moc]] on success; [[null]] otherwise.
         */
        Moc.fromArrayBuffer = function (buffer) {
            if (!buffer) {
                return null;
            }
            var moc = new Moc(buffer);
            return (moc._ptr)
                ? moc
                : null;
        };
        /** Releases instance. */
        Moc.prototype._release = function () {
            _csm.free(this._ptr);
            this._ptr = 0;
        };
        return Moc;
    }());
    Live2DCubismCore.Moc = Moc;
    /** Cubism model. */
    var Model = /** @class */ (function () {
        /**
         * Initializes instance.
         *
         * @param moc Moc
         */
        function Model(moc) {
            this._ptr = _csm.mallocModelAndInitialize(moc._ptr);
            if (!this._ptr) {
                return;
            }
            this.parameters = new Parameters(this._ptr);
            this.parts = new Parts(this._ptr);
            this.drawables = new Drawables(this._ptr);
            this.canvasinfo = new CanvasInfo(this._ptr);
        }
        /**
         * Creates [[Model]] from [[Moc]].
         *
         * @param moc Moc
         *
         * @return [[Model]] on success; [[null]] otherwise.
         */
        Model.fromMoc = function (moc) {
            var model = new Model(moc);
            return (model._ptr)
                ? model
                : null;
        };
        /** Updates instance. */
        Model.prototype.update = function () {
            _csm.updateModel(this._ptr);
        };
        /** Releases instance. */
        Model.prototype.release = function () {
            _csm.free(this._ptr);
            this._ptr = 0;
        };
        return Model;
    }());
    Live2DCubismCore.Model = Model;
    /** Canvas information interface. */
    var CanvasInfo = /** @class */ (function () {
        /**
         * Initializes instance.
         *
         * @param modelPtr Native model pointer.
         */
        function CanvasInfo(modelPtr) {
            if (!modelPtr) {
                return;
            }
            // Preserve the pointer ant heap for get data throw args.
            var _canvasSize_data = new Float32Array(2);
            var _canvasSize_nDataBytes = _canvasSize_data.length * _canvasSize_data.BYTES_PER_ELEMENT;
            var _canvasSize_dataPtr = _csm.malloc(_canvasSize_nDataBytes);
            var _canvasSize_dataHeap = new Uint8Array(_em.HEAPU8.buffer, _canvasSize_dataPtr, _canvasSize_nDataBytes);
            _canvasSize_dataHeap.set(new Uint8Array(_canvasSize_data.buffer));
            var _canvasOrigin_data = new Float32Array(2);
            var _canvasOrigin_nDataBytes = _canvasOrigin_data.length * _canvasOrigin_data.BYTES_PER_ELEMENT;
            var _canvasOrigin_dataPtr = _csm.malloc(_canvasOrigin_nDataBytes);
            var _canvasOrigin_dataHeap = new Uint8Array(_em.HEAPU8.buffer, _canvasOrigin_dataPtr, _canvasOrigin_nDataBytes);
            _canvasOrigin_dataHeap.set(new Uint8Array(_canvasOrigin_data.buffer));
            var _canvasPPU_data = new Float32Array(1);
            var _canvasPPU_nDataBytes = _canvasPPU_data.length * _canvasPPU_data.BYTES_PER_ELEMENT;
            var _canvasPPU_dataPtr = _csm.malloc(_canvasPPU_nDataBytes);
            var _canvasPPU_dataHeap = new Uint8Array(_em.HEAPU8.buffer, _canvasPPU_dataPtr, _canvasPPU_nDataBytes);
            _canvasPPU_dataHeap.set(new Uint8Array(_canvasPPU_data.buffer));
            // Call function and get result
            _csm.readCanvasInfo(modelPtr, _canvasSize_dataHeap.byteOffset, _canvasOrigin_dataHeap.byteOffset, _canvasPPU_dataHeap.byteOffset);
            _canvasSize_data = new Float32Array(_canvasSize_dataHeap.buffer, _canvasSize_dataHeap.byteOffset, _canvasSize_dataHeap.length);
            _canvasOrigin_data = new Float32Array(_canvasOrigin_dataHeap.buffer, _canvasOrigin_dataHeap.byteOffset, _canvasOrigin_dataHeap.length);
            _canvasPPU_data = new Float32Array(_canvasPPU_dataHeap.buffer, _canvasPPU_dataHeap.byteOffset, _canvasPPU_dataHeap.length);
            this.CanvasWidth = _canvasSize_data[0];
            this.CanvasHeight = _canvasSize_data[1];
            this.CanvasOriginX = _canvasOrigin_data[0];
            this.CanvasOriginY = _canvasOrigin_data[1];
            this.PixelsPerUnit = _canvasPPU_data[0];
            // Free heap memory
            _csm.free(_canvasSize_dataHeap.byteOffset);
            _csm.free(_canvasOrigin_dataHeap.byteOffset);
            _csm.free(_canvasPPU_dataHeap.byteOffset);
        }
        return CanvasInfo;
    }());
    Live2DCubismCore.CanvasInfo = CanvasInfo;
    /** Cubism model parameters */
    var Parameters = /** @class */ (function () {
        /**
         * Initializes instance.
         *
         * @param modelPtr Native model.
         */
        function Parameters(modelPtr) {
            var length = 0;
            this.count = _csm.getParameterCount(modelPtr);
            length = _csm.getParameterCount(modelPtr);
            this.ids = new Array(length);
            var _ids = new Uint32Array(_em.HEAPU32.buffer, _csm.getParameterIds(modelPtr), length);
            for (var i = 0; i < _ids.length; i++) {
                this.ids[i] = _em.UTF8ToString(_ids[i]);
            }
            length = _csm.getParameterCount(modelPtr);
            this.minimumValues = new Float32Array(_em.HEAPF32.buffer, _csm.getParameterMinimumValues(modelPtr), length);
            length = _csm.getParameterCount(modelPtr);
            this.maximumValues = new Float32Array(_em.HEAPF32.buffer, _csm.getParameterMaximumValues(modelPtr), length);
            length = _csm.getParameterCount(modelPtr);
            this.defaultValues = new Float32Array(_em.HEAPF32.buffer, _csm.getParameterDefaultValues(modelPtr), length);
            length = _csm.getParameterCount(modelPtr);
            this.values = new Float32Array(_em.HEAPF32.buffer, _csm.getParameterValues(modelPtr), length);
        }
        return Parameters;
    }());
    Live2DCubismCore.Parameters = Parameters;
    /** Cubism model parts */
    var Parts = /** @class */ (function () {
        /**
         * Initializes instance.
         *
         * @param modelPtr Native model.
         */
        function Parts(modelPtr) {
            var length = 0;
            this.count = _csm.getPartCount(modelPtr);
            length = _csm.getPartCount(modelPtr);
            this.ids = new Array(length);
            var _ids = new Uint32Array(_em.HEAPU32.buffer, _csm.getPartIds(modelPtr), length);
            for (var i = 0; i < _ids.length; i++) {
                this.ids[i] = _em.UTF8ToString(_ids[i]);
            }
            length = _csm.getPartCount(modelPtr);
            this.opacities = new Float32Array(_em.HEAPF32.buffer, _csm.getPartOpacities(modelPtr), length);
            length = _csm.getPartCount(modelPtr);
            this.parentIndices = new Int32Array(_em.HEAP32.buffer, _csm.getPartParentPartIndices(modelPtr), length);
        }
        return Parts;
    }());
    Live2DCubismCore.Parts = Parts;
    /** Cubism model drawables */
    var Drawables = /** @class */ (function () {
        /**
         * Initializes instance.
         *
         * @param modelPtr Native model.
         */
        function Drawables(modelPtr) {
            this._modelPtr = modelPtr;
            var length = 0;
            var length2 = null;
            this.count = _csm.getDrawableCount(modelPtr);
            length = _csm.getDrawableCount(modelPtr);
            this.ids = new Array(length);
            var _ids = new Uint32Array(_em.HEAPU32.buffer, _csm.getDrawableIds(modelPtr), length);
            for (var i = 0; i < _ids.length; i++) {
                this.ids[i] = _em.UTF8ToString(_ids[i]);
            }
            length = _csm.getDrawableCount(modelPtr);
            this.constantFlags = new Uint8Array(_em.HEAPU8.buffer, _csm.getDrawableConstantFlags(modelPtr), length);
            length = _csm.getDrawableCount(modelPtr);
            this.dynamicFlags = new Uint8Array(_em.HEAPU8.buffer, _csm.getDrawableDynamicFlags(modelPtr), length);
            length = _csm.getDrawableCount(modelPtr);
            this.textureIndices = new Int32Array(_em.HEAP32.buffer, _csm.getDrawableTextureIndices(modelPtr), length);
            length = _csm.getDrawableCount(modelPtr);
            this.drawOrders = new Int32Array(_em.HEAP32.buffer, _csm.getDrawableDrawOrders(modelPtr), length);
            length = _csm.getDrawableCount(modelPtr);
            this.renderOrders = new Int32Array(_em.HEAP32.buffer, _csm.getDrawableRenderOrders(modelPtr), length);
            length = _csm.getDrawableCount(modelPtr);
            this.opacities = new Float32Array(_em.HEAPF32.buffer, _csm.getDrawableOpacities(modelPtr), length);
            length = _csm.getDrawableCount(modelPtr);
            this.maskCounts = new Int32Array(_em.HEAP32.buffer, _csm.getDrawableMaskCounts(modelPtr), length);
            length = _csm.getDrawableCount(modelPtr);
            this.vertexCounts = new Int32Array(_em.HEAP32.buffer, _csm.getDrawableVertexCounts(modelPtr), length);
            length = _csm.getDrawableCount(modelPtr);
            this.indexCounts = new Int32Array(_em.HEAP32.buffer, _csm.getDrawableIndexCounts(modelPtr), length);
            length = _csm.getDrawableCount(modelPtr);
            length2 = new Int32Array(_em.HEAP32.buffer, _csm.getDrawableMaskCounts(modelPtr), length);
            this.masks = new Array(length);
            var _masks = new Uint32Array(_em.HEAPU32.buffer, _csm.getDrawableMasks(modelPtr), length);
            for (var i = 0; i < _masks.length; i++) {
                this.masks[i] = new Int32Array(_em.HEAP32.buffer, _masks[i], length2[i]);
            }
            length = _csm.getDrawableCount(modelPtr);
            length2 = new Int32Array(_em.HEAP32.buffer, _csm.getDrawableVertexCounts(modelPtr), length);
            this.vertexPositions = new Array(length);
            var _vertexPositions = new Uint32Array(_em.HEAPU32.buffer, _csm.getDrawableVertexPositions(modelPtr), length);
            for (var i = 0; i < _vertexPositions.length; i++) {
                this.vertexPositions[i] = new Float32Array(_em.HEAPF32.buffer, _vertexPositions[i], length2[i] * 2);
            }
            length = _csm.getDrawableCount(modelPtr);
            length2 = new Int32Array(_em.HEAP32.buffer, _csm.getDrawableVertexCounts(modelPtr), length);
            this.vertexUvs = new Array(length);
            var _vertexUvs = new Uint32Array(_em.HEAPU32.buffer, _csm.getDrawableVertexUvs(modelPtr), length);
            for (var i = 0; i < _vertexUvs.length; i++) {
                this.vertexUvs[i] = new Float32Array(_em.HEAPF32.buffer, _vertexUvs[i], length2[i] * 2);
            }
            length = _csm.getDrawableCount(modelPtr);
            length2 = new Int32Array(_em.HEAP32.buffer, _csm.getDrawableIndexCounts(modelPtr), length);
            this.indices = new Array(length);
            var _indices = new Uint32Array(_em.HEAPU32.buffer, _csm.getDrawableIndices(modelPtr), length);
            for (var i = 0; i < _indices.length; i++) {
                this.indices[i] = new Uint16Array(_em.HEAPU16.buffer, _indices[i], length2[i]);
            }
        }
        /** Resets all dynamic drawable flags.. */
        Drawables.prototype.resetDynamicFlags = function () {
            _csm.resetDrawableDynamicFlags(this._modelPtr);
        };
        return Drawables;
    }());
    Live2DCubismCore.Drawables = Drawables;
    /** Utility functions. */
    var Utils = /** @class */ (function () {
        function Utils() {
        }
        /**
         * Checks whether flag is set in bitfield.
         *
         * @param bitfield Bitfield to query against.
         *
         * @return [[true]] if bit set; [[false]] otherwise
        */
        Utils.hasBlendAdditiveBit = function (bitfield) {
            return (bitfield & (1 << 0)) == (1 << 0);
        };
        /**
         * Checks whether flag is set in bitfield.
         *
         * @param bitfield Bitfield to query against.
         *
         * @return [[true]] if bit set; [[false]] otherwise
        */
        Utils.hasBlendMultiplicativeBit = function (bitfield) {
            return (bitfield & (1 << 1)) == (1 << 1);
        };
        /**
         * Checks whether flag is set in bitfield.
         *
         * @param bitfield Bitfield to query against.
         *
         * @return [[true]] if bit set; [[false]] otherwise
        */
        Utils.hasIsDoubleSidedBit = function (bitfield) {
            return (bitfield & (1 << 2)) == (1 << 2);
        };
        /**
         * Checks whether flag is set in bitfield.
         *
         * @param bitfield Bitfield to query against.
         *
         * @return [[true]] if bit set; [[false]] otherwise
        */
        Utils.hasIsInvertedMaskBit = function (bitfield) {
            return (bitfield & (1 << 3)) == (1 << 3);
        };
        /**
         * Checks whether flag is set in bitfield.
         *
         * @param bitfield Bitfield to query against.
         *
         * @return [[true]] if bit set; [[false]] otherwise
        */
        Utils.hasIsVisibleBit = function (bitfield) {
            return (bitfield & (1 << 0)) == (1 << 0);
        };
        /**
         * Checks whether flag is set in bitfield.
         *
         * @param bitfield Bitfield to query against.
         *
         * @return [[true]] if bit set; [[false]] otherwise
        */
        Utils.hasVisibilityDidChangeBit = function (bitfield) {
            return (bitfield & (1 << 1)) == (1 << 1);
        };
        /**
         * Checks whether flag is set in bitfield.
         *
         * @param bitfield Bitfield to query against.
         *
         * @return [[true]] if bit set; [[false]] otherwise
        */
        Utils.hasOpacityDidChangeBit = function (bitfield) {
            return (bitfield & (1 << 2)) == (1 << 2);
        };
        /**
         * Checks whether flag is set in bitfield.
         *
         * @param bitfield Bitfield to query against.
         *
         * @return [[true]] if bit set; [[false]] otherwise
        */
        Utils.hasDrawOrderDidChangeBit = function (bitfield) {
            return (bitfield & (1 << 3)) == (1 << 3);
        };
        /**
         * Checks whether flag is set in bitfield.
         *
         * @param bitfield Bitfield to query against.
         *
         * @return [[true]] if bit set; [[false]] otherwise
        */
        Utils.hasRenderOrderDidChangeBit = function (bitfield) {
            return (bitfield & (1 << 4)) == (1 << 4);
        };
        /**
         * Checks whether flag is set in bitfield.
         *
         * @param bitfield Bitfield to query against.
         *
         * @return [[true]] if bit set; [[false]] otherwise
        */
        Utils.hasVertexPositionsDidChangeBit = function (bitfield) {
            return (bitfield & (1 << 5)) == (1 << 5);
        };
        return Utils;
    }());
    Live2DCubismCore.Utils = Utils;
    /** Emscripten Cubism Core module. */
    var _em_module=function(){var _scriptDir="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0;return function(_em_module){var b;_em_module=_em_module||{},b=b||(void 0!==_em_module?_em_module:{});var n,l={};for(n in b)b.hasOwnProperty(n)&&(l[n]=b[n]);var t,p=!1,q=!1,r=!1;p="object"==typeof window,q="function"==typeof importScripts,r="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node&&!p&&!q,t=!p&&!r&&!q;var v,w,x,y,u="";r?(u=__dirname+"/",v=function(a,c){var d;return(d=z(a))?c?d:d.toString():(x=x||require("fs"),a=(y=y||require("path")).normalize(a),x.readFileSync(a,c?null:"utf8"))},w=function(a){return(a=v(a,!0)).buffer||(a=new Uint8Array(a)),assert(a.buffer),a},1<process.argv.length&&process.argv[1].replace(/\\/g,"/"),process.argv.slice(2),process.on("uncaughtException",function(a){throw a}),process.on("unhandledRejection",B),b.inspect=function(){return"[Emscripten Module object]"}):t?("undefined"!=typeof read&&(v=function(a){var c=z(a);return c?C(c):read(a)}),w=function(a){var c;return(c=z(a))?c:"function"==typeof readbuffer?new Uint8Array(readbuffer(a)):(assert("object"==typeof(c=read(a,"binary"))),c)},"undefined"!=typeof print&&("undefined"==typeof console&&(console={}),console.log=print,console.warn=console.error="undefined"!=typeof printErr?printErr:print)):(p||q)&&(q?u=self.location.href:document.currentScript&&(u=document.currentScript.src),_scriptDir&&(u=_scriptDir),u=0!==u.indexOf("blob:")?u.substr(0,u.lastIndexOf("/")+1):"",v=function(a){try{var c=new XMLHttpRequest;return c.open("GET",a,!1),c.send(null),c.responseText}catch(d){if(a=z(a))return C(a);throw d}},q&&(w=function(a){try{var c=new XMLHttpRequest;return c.open("GET",a,!1),c.responseType="arraybuffer",c.send(null),new Uint8Array(c.response)}catch(d){if(a=z(a))return a;throw d}}));var F,D=b.print||console.log.bind(console),E=b.printErr||console.warn.bind(console);for(n in l)l.hasOwnProperty(n)&&(b[n]=l[n]);function da(){return{exports:function(asmLibraryArg,wasmMemory,wasmTable){var scratchBuffer=new ArrayBuffer(8),b=new Int32Array(scratchBuffer),c=new Float32Array(scratchBuffer),d=new Float64Array(scratchBuffer);function e(index){return b[index]}function f(index,value){b[index]=value}function g(){return d[0]}function h(value){d[0]=value}function j(value){c[0]=value}function k(){return c[0]}var mem,U,global,env,buffer,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,C,H,I,J,K,L,M,T=(mem=wasmMemory.buffer,U=new Uint8Array(mem),function(offset,s){var V,W;if("undefined"==typeof Buffer)for(V=atob(s),W=0;W<V.length;W++)U[offset+W]=V.charCodeAt(W);else for(V=Buffer.from(s,"base64"),W=0;W<V.length;W++)U[offset+W]=V[W]});return T(1024,"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"),T(2572,"Cw=="),T(2611,"//////8="),T(2680,"GBgAAC0rICAgMFgweAAobnVsbCkAAAAAEQAKABEREQAAAAAFAAAAAAAACQAAAAAL"),T(2736,"EQAPChEREQMKBwABEwkLCwAACQYLAAALAAYRAAAAERER"),T(2785,"Cw=="),T(2794,"EQAKChEREQAKAAACAAkLAAAACQALAAAL"),T(2843,"DA=="),T(2855,"DAAAAAAMAAAAAAkMAAAAAAAMAAAM"),T(2901,"Dg=="),T(2913,"DQAAAAQNAAAAAAkOAAAAAAAOAAAO"),T(2959,"EA=="),T(2971,"DwAAAAAPAAAAAAkQAAAAAAAQAAAQAAASAAAAEhIS"),T(3026,"EgAAABISEgAAAAAAAAk="),T(3075,"Cw=="),T(3087,"CgAAAAAKAAAAAAkLAAAAAAALAAAL"),T(3133,"DA=="),T(3145,"DAAAAAAMAAAAAAkMAAAAAAAMAAAMAAAwMTIzNDU2Nzg5QUJDREVGLTBYKzBYIDBYLTB4KzB4IDB4AGluZgBJTkYAbmFuAE5BTgAu"),T(3232,"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"),T(6019,"QPsh+T8AAAAALUR0PgAAAICYRvg8AAAAYFHMeDsAAACAgxvwOQAAAEAgJXo4AAAAgCKC4zYAAAAAHfNpNThj7T7aD0k/Xph7P9oPyT9pN6wxaCEiM7QPFDNoIaIz2w9JP9sPSb/kyxZA5MsWwAAAAAAAAACA2w9JQNsPScAAAIA/AADAPwAAAADcz9E1AAAAAADAFT8="),T(6168,"BQ=="),T(6180,"DA=="),T(6204,"DQAAAA4AAADIGQAAAAQ="),T(6228,"AQ=="),T(6243,"Cv////8="),T(6500,"9B0="),global={Int8Array:Int8Array,Int16Array:Int16Array,Int32Array:Int32Array,Uint8Array:Uint8Array,Uint16Array:Uint16Array,Uint32Array:Uint32Array,Float32Array:Float32Array,Float64Array:Float64Array,NaN:NaN,Infinity:1/0,Math:Math},env=asmLibraryArg,buffer=wasmMemory.buffer,m=env.memory,n=wasmTable,o=new global.Int8Array(buffer),p=new global.Int16Array(buffer),q=new global.Int32Array(buffer),r=new global.Uint8Array(buffer),s=new global.Uint16Array(buffer),t=new global.Uint32Array(buffer),u=new global.Float32Array(buffer),v=new global.Float64Array(buffer),w=global.Math.imul,x=global.Math.fround,y=global.Math.abs,z=global.Math.clz32,A=global.Math.min,global.Math.max,C=global.Math.floor,global.Math.ceil,global.Math.sqrt,env.abort,global.NaN,H=global.Infinity,I=env.a,J=env.b,K=env.c,L=5251088,M=0,n[1]=function(a,df,ef,ff){a|=0,df|=0,ef|=0,ff|=0;var If,gf=0,hf=x(0),jf=x(0),kf=0,lf=x(0),mf=x(0),nf=x(0),of=0,pf=x(0),qf=x(0),rf=x(0),sf=x(0),tf=x(0),uf=x(0),vf=x(0),wf=x(0),xf=x(0),yf=x(0),zf=x(0),Af=x(0),Bf=x(0),Cf=x(0),Df=0,Ef=0,Ff=x(0),Gf=x(0),Hf=0,Jf=0,Kf=x(0),Lf=x(0),Mf=0,Nf=0,Of=0,Pf=0,Qf=0,Rf=0,Sf=0,Tf=0,Uf=0,Vf=x(0),Wf=x(0),Xf=x(0),Yf=x(0),Zf=x(0),_f=x(0),$f=x(0),ag=0,bg=0;if(L=If=L-32|0,1<=(0|ff))for(Qf=(Df=q[a+4>>2])+-1|0,Rf=(Jf=q[a>>2])+-1|0,Sf=Nf=(of=q[a+20>>2])+(Df<<3)|0,Tf=Of=((Mf=w(Jf,Ef=Df+1|0))<<3)+of|0,Uf=Pf=(Df+Mf<<3)+of|0,Kf=x(0|Jf),Lf=x(0|Df),ag=q[a+8>>2],a=0;;){nf=u[4+(gf=(Hf=a<<3)+df|0)>>2],jf=x(nf*Kf),pf=u[gf>>2],hf=x(pf*Lf),gf=nf>=x(1);a:if(nf<x(0)^1&&!(gf|pf>=x(1)|pf<x(0)))gf=x(y(uf=jf))<x(2147483648)?~~jf:-2147483648,mf=x(uf-x(0|gf)),kf=x(y(jf=hf))<x(2147483648)?~~hf:-2147483648,lf=x(jf-x(0|kf)),gf=kf+w(gf,Ef)|0,jf=ag?(hf=x(x(1)-mf),jf=x(x(1)-lf),kf=(gf<<3)+of|0,gf=(gf+Ef<<3)+of|0,u[ef+Hf>>2]=x(x(x(hf*x(jf*u[kf>>2]))+x(hf*x(lf*u[kf+8>>2])))+x(mf*x(jf*u[gf>>2])))+x(mf*x(lf*u[gf+8>>2])),hf=x(x(x(hf*x(jf*u[kf+4>>2]))+x(hf*x(lf*u[kf+12>>2])))+x(mf*x(jf*u[gf+4>>2]))),x(mf*x(lf*u[gf+12>>2]))):x(lf+mf)<=x(1)?(hf=x(x(x(1)-lf)-mf),kf=(gf<<3)+of|0,gf=(gf+Ef<<3)+of|0,u[ef+Hf>>2]=x(x(hf*u[kf>>2])+x(lf*u[kf+8>>2]))+x(mf*u[gf>>2]),hf=x(x(hf*u[kf+4>>2])+x(lf*u[kf+12>>2])),x(mf*u[gf+4>>2])):(hf=x(x(lf+x(-1))+mf),kf=(gf+Ef<<3)+of|0,jf=x(x(1)-lf),nf=x(x(1)-mf),gf=(gf<<3)+of|0,u[ef+Hf>>2]=x(x(hf*u[kf+8>>2])+x(jf*u[kf>>2]))+x(nf*u[gf+8>>2]),hf=x(x(hf*u[kf+12>>2])+x(jf*u[kf+4>>2])),x(nf*u[gf+12>>2]));else{if(bg||(uf=u[Uf+4>>2],Vf=u[of+4>>2],qf=x(uf-Vf),Wf=u[Sf+4>>2],Xf=u[Tf+4>>2],rf=x(Wf-Xf),vf=x(x(qf-rf)*x(.5)),Yf=u[Pf>>2],Zf=u[of>>2],sf=x(Yf-Zf),_f=u[Nf>>2],$f=u[Of>>2],tf=x(_f-$f),wf=x(x(sf-tf)*x(.5)),rf=x(x(rf+qf)*x(.5)),tf=x(x(tf+sf)*x(.5)),bg=1,qf=x(x(x(x(x(Vf+Wf)+Xf)+uf)*x(.25))-x(qf*x(.5))),sf=x(x(x(x(x(Zf+_f)+$f)+Yf)*x(.25))-x(sf*x(.5)))),!(nf<x(3)^1|pf>x(-2)^1|pf<x(3)^1|nf>x(-2)^1)){b:if(pf<=x(0)){if(nf<=x(0)){mf=x(x(nf+x(2))*x(.5)),lf=x(x(pf+x(2))*x(.5)),hf=x(vf+vf),Ff=x(qf-hf),jf=x(wf+wf),Gf=x(sf-jf),Bf=x(qf-x(rf+rf)),xf=x(Bf-hf),Cf=x(sf-x(tf+tf)),yf=x(Cf-jf),zf=u[of+4>>2],Af=u[of>>2];break b}if(gf){hf=x(vf*x(3)),jf=x(qf-x(rf+rf)),Bf=x(hf+jf),xf=x(wf*x(3)),yf=x(sf-x(tf+tf)),Cf=x(xf+yf),mf=x(x(nf+x(-1))*x(.5)),lf=x(x(pf+x(2))*x(.5)),zf=x(hf+qf),Af=x(xf+sf),xf=x(vf+jf),yf=x(wf+yf),Ff=u[Tf+4>>2],Gf=u[Of>>2];break b}hf=x(qf-x(rf+rf)),gf=Rf,kf=x(y(jf))<x(2147483648)?~~jf:-2147483648,mf=x(0|(gf=(0|kf)==(0|Jf)?gf:kf)),lf=x(mf/Kf),xf=x(x(lf*vf)+hf),uf=x(lf*wf),lf=x(sf-x(tf+tf)),yf=x(uf+lf),nf=x(x(0|(kf=gf+1|0))/Kf),Bf=x(x(nf*vf)+hf),Cf=x(x(nf*wf)+lf),lf=x(x(pf+x(2))*x(.5)),mf=x(jf-mf),gf=(w(gf,Ef)<<3)+of|0,Ff=u[gf+4>>2],Gf=u[gf>>2],gf=(w(kf,Ef)<<3)+of|0,zf=u[gf+4>>2],Af=u[gf>>2]}else if(pf>=x(1)){if(nf<=x(0)){mf=x(x(nf+x(2))*x(.5)),lf=x(x(pf+x(-1))*x(.5)),hf=x(vf+vf),xf=x(x(rf+qf)-hf),jf=x(wf+wf),yf=x(x(tf+sf)-jf),zf=x(x(rf*x(3))+qf),Ff=x(zf-hf),Af=x(x(tf*x(3))+sf),Gf=x(Af-jf),Bf=u[Sf+4>>2],Cf=u[Nf>>2];break b}if(gf){hf=x(vf*x(3)),Bf=x(hf+x(rf+qf)),jf=x(wf*x(3)),Cf=x(jf+x(tf+sf)),uf=hf,hf=x(x(rf*x(3))+qf),zf=x(uf+hf),uf=jf,jf=x(x(tf*x(3))+sf),Af=x(uf+jf),mf=x(x(nf+x(-1))*x(.5)),lf=x(x(pf+x(-1))*x(.5)),Ff=x(vf+hf),Gf=x(wf+jf),xf=u[Uf+4>>2],yf=u[Pf>>2];break b}hf=x(x(rf*x(3))+qf),gf=Rf,kf=x(y(jf))<x(2147483648)?~~jf:-2147483648,mf=x(0|(gf=(0|kf)==(0|Jf)?gf:kf)),lf=x(mf/Kf),Ff=x(x(lf*vf)+hf),uf=x(lf*wf),lf=x(x(tf*x(3))+sf),Gf=x(uf+lf),nf=x(x(0|(kf=gf+1|0))/Kf),zf=x(x(nf*vf)+hf),Af=x(x(nf*wf)+lf),lf=x(x(pf+x(-1))*x(.5)),mf=x(jf-mf),gf=(w(gf,Ef)+Df<<3)+of|0,xf=u[gf+4>>2],yf=u[gf>>2],gf=(w(kf,Ef)+Df<<3)+of|0,Bf=u[gf+4>>2],Cf=u[gf>>2]}else nf<=x(0)?(mf=x(x(nf+x(2))*x(.5)),gf=Qf,kf=x(y(jf=hf))<x(2147483648)?~~hf:-2147483648,hf=x(0|(gf=(0|kf)==(0|Df)?gf:kf)),lf=x(jf-hf),hf=x(hf/Lf),jf=x(vf+vf),xf=x(x(x(hf*rf)+qf)-jf),uf=x(x(hf*tf)+sf),hf=x(wf+wf),yf=x(uf-hf),nf=x(x(0|(kf=gf+1|0))/Lf),Ff=x(x(x(nf*rf)+qf)-jf),Gf=x(x(x(nf*tf)+sf)-hf),Bf=u[4+(gf=(gf<<3)+of|0)>>2],Cf=u[gf>>2],zf=u[4+(gf=(kf<<3)+of|0)>>2],Af=u[gf>>2]):gf?(uf=jf=x(vf*x(3)),gf=Qf,kf=x(y(hf))<x(2147483648)?~~hf:-2147483648,lf=x(0|(gf=(0|kf)==(0|Df)?gf:kf)),mf=x(lf/Lf),Bf=x(uf+x(x(mf*rf)+qf)),pf=x(wf*x(3)),Cf=x(pf+x(x(mf*tf)+sf)),uf=jf,jf=x(x(0|(kf=gf+1|0))/Lf),zf=x(uf+x(x(jf*rf)+qf)),Af=x(pf+x(x(jf*tf)+sf)),mf=x(x(nf+x(-1))*x(.5)),lf=x(hf-lf),xf=u[4+(gf=(gf+Mf<<3)+of|0)>>2],yf=u[gf>>2],Ff=u[4+(gf=(kf+Mf<<3)+of|0)>>2],Gf=u[gf>>2]):(v[16+If>>3]=nf,q[If>>2]=a,v[8+If>>3]=pf,Y(4,1104,If));if(x(lf+mf)<=x(1)){u[ef+Hf>>2]=x(yf+x(x(Gf-yf)*lf))+x(x(Cf-yf)*mf),hf=x(xf+x(x(Ff-xf)*lf)),jf=x(x(Bf-xf)*mf);break a}hf=x(x(1)-lf),jf=x(x(1)-mf),u[ef+Hf>>2]=x(Af+x(x(Cf-Af)*hf))+x(x(Gf-Af)*jf),hf=x(zf+x(x(Bf-zf)*hf)),jf=x(x(Ff-zf)*jf);break a}u[ef+Hf>>2]=x(nf*wf)+x(x(pf*tf)+sf),hf=x(nf*vf),jf=x(x(pf*rf)+qf)}if(u[4+(ef+Hf|0)>>2]=hf+jf,(0|ff)==(0|(a=a+1|0)))break}L=32+If|0},n[2]=function(a,se,te){a|=0,se|=0;var Ie,Je,Ke,He=0;if(Je=q[140+(te|=0)>>2],Ke=q[te+136>>2],Ie=q[a+24>>2],-1==(0|(He=q[a+4>>2])))return q[(a=se<<2)+Ke>>2]=q[Ie+16>>2],void(q[a+Je>>2]=1065353216);te=q[te+4>>2]+(He<<5)|0,He=q[Ie+20>>2],n[q[te+20>>2]](q[te+24>>2],He,He,q[Ie+12>>2]),se<<=2,a=q[a+4>>2]<<2,u[se+Ke>>2]=u[Ie+16>>2]*u[a+Ke>>2],q[se+Je>>2]=q[a+Je>>2]},n[3]=function(a,se,te,Le){a|=0,se|=0,te|=0,Le|=0;var Ue,Me=x(0),Ne=x(0),Oe=x(0),Pe=x(0),Qe=0,Re=x(0),Se=0,Te=x(0),Ve=x(0),We=x(0);if(Qe=q[a+28>>2],Ne=function(a){var Tb,Rb=x(0),Sb=0,Ub=0,Vb=0;L=Tb=L-16|0,j(a);a:if((Sb=2147483647&(Ub=e(0)))>>>0<=1061752794){if(Rb=x(1),Sb>>>0<964689920)break a;Rb=aa(+a)}else if(Sb>>>0<=1081824209){if(Vb=+a,1075235812<=Sb>>>0){Rb=x(-aa(((0|Ub)<0?3.141592653589793:-3.141592653589793)+Vb));break a}if((0|Ub)<=-1){Rb=$(Vb+1.5707963267948966);break a}Rb=$(1.5707963267948966-Vb)}else if(Sb>>>0<=1088565717){if(1085271520<=Sb>>>0){Rb=aa(+a+((0|Ub)<0?6.283185307179586:-6.283185307179586));break a}if((0|Ub)<=-1){Rb=$(-4.71238898038469-a);break a}Rb=$(a-4.71238898038469)}else if(Rb=x(a-a),!(2139095040<=Sb>>>0))if((Sb=3&la(a,8+Tb|0))>>>0<=2){b:switch(Sb-1|0){default:Rb=aa(v[8+Tb>>3]);break a;case 0:Rb=$(-v[8+Tb>>3]);break a;case 1:}Rb=x(-aa(v[8+Tb>>3]))}else Rb=$(v[8+Tb>>3]);return L=16+Tb|0,a=Rb}(Me=x(x(x(u[a>>2]+u[a+20>>2])*x(3.1415927410125732))/x(180))),Oe=u[a+8>>2],Ue=q[a+24>>2],Me=function(a){var Ob,Hb=0,Pb=0,Qb=0;L=Ob=L-16|0,j(a);a:if((Hb=2147483647&(Qb=e(0)))>>>0<=1061752794){if(Hb>>>0<964689920)break a;a=$(+a)}else if(Hb>>>0<=1081824209){if(Pb=+a,Hb>>>0<=1075235811){if((0|Qb)<=-1){a=x(-aa(Pb+1.5707963267948966));break a}a=aa(Pb+-1.5707963267948966);break a}a=$(-(((0|Qb)<0?3.141592653589793:-3.141592653589793)+Pb))}else if(Hb>>>0<=1088565717){if(Pb=+a,Hb>>>0<=1085271519){if((0|Qb)<=-1){a=aa(Pb+4.71238898038469);break a}a=x(-aa(Pb+-4.71238898038469));break a}a=$(((0|Qb)<0?6.283185307179586:-6.283185307179586)+Pb)}else if(2139095040<=Hb>>>0)a=x(a-a);else if((Hb=3&la(a,8+Ob|0))>>>0<=2){b:switch(Hb-1|0){default:a=$(v[8+Ob>>3]);break a;case 0:a=aa(v[8+Ob>>3]);break a;case 1:}a=$(-v[8+Ob>>3])}else a=x(-aa(v[8+Ob>>3]));return L=16+Ob|0,a}(Me),0<(0|Le))for(Ne=x(Oe*Ne),Re=x(Qe?-1:1),Ve=x(Ne*Re),Pe=x(Ue?-1:1),We=x(Pe*x(Oe*Me)),Ne=x(Pe*Ne),Oe=x(x(Oe*x(-Me))*Re),Me=u[a+16>>2],Re=u[a+12>>2];Qe=(a=Se<<3)+te|0,Pe=u[(a=a+se|0)>>2],Te=u[a+4>>2],u[Qe+4>>2]=Me+x(x(We*Pe)+x(Ve*Te)),u[Qe>>2]=Re+x(x(Ne*Pe)+x(Oe*Te)),(0|(Se=Se+1|0))!=(0|Le););},n[4]=function(a,se,te){a|=0,se|=0,te|=0;var ue,ve,Be,Ce,De,we=0,xe=x(0),ye=x(0),ze=0,Ae=x(0),Ee=x(0),Fe=x(0),Ge=x(0);if(L=ue=L+-64|0,Be=q[te+140>>2],Ce=q[te+136>>2],ve=q[a+24>>2],-1!=(0|(De=q[a+4>>2]))){we=q[te+4>>2],ze=q[ve+12>>2],q[24+ue>>2]=ze,te=q[ve+16>>2],q[28+ue>>2]=te,q[16+ue>>2]=0,Ee=1==q[8+(we=we+(De<<5)|0)>>2]?x(-10):x(-.10000000149011612),u[20+ue>>2]=Ee,q[60+ue>>2]=te,q[56+ue>>2]=ze,n[q[we+20>>2]](q[we+24>>2],56+ue|0,48+ue|0,1),Ae=x(1),ze=9;b:{for(;;){if(te=ze,Fe=x(Ae*x(0)),u[32+ue>>2]=Fe+u[56+ue>>2],Ge=x(Ee*Ae),u[36+ue>>2]=Ge+u[60+ue>>2],n[q[we+20>>2]](q[we+24>>2],32+ue|0,40+ue|0,1),ye=x(u[44+ue>>2]-u[52+ue>>2]),u[44+ue>>2]=ye,xe=x(u[40+ue>>2]-u[48+ue>>2]),u[40+ue>>2]=xe,ye!=x(0)||xe!=x(0)){te=q[44+ue>>2],q[8+ue>>2]=q[40+ue>>2],q[12+ue>>2]=te;break b}if(u[32+ue>>2]=u[56+ue>>2]-Fe,u[36+ue>>2]=u[60+ue>>2]-Ge,n[q[we+20>>2]](q[we+24>>2],32+ue|0,40+ue|0,1),ye=x(u[40+ue>>2]-u[48+ue>>2]),u[40+ue>>2]=ye,xe=x(u[44+ue>>2]-u[52+ue>>2]),(u[44+ue>>2]=xe)!=x(0)||ye!=x(0)){u[12+ue>>2]=-xe,u[8+ue>>2]=-ye;break b}if(ze=te+-1|0,Ae=x(Ae*x(.10000000149011612)),!te)break}Y(3,1265,0)}xe=function(a,fh){var gh=x(0);if((gh=x(Aa(u[a+4>>2],u[a>>2])-Aa(u[fh+4>>2],u[fh>>2])))<x(-3.1415927410125732))for(;(gh=x(gh+x(6.2831854820251465)))<x(-3.1415927410125732););if(gh>x(3.1415927410125732))for(;(gh=x(gh+x(-6.2831854820251465)))>x(3.1415927410125732););return gh}(16+ue|0,8+ue|0),n[q[we+20>>2]](q[we+24>>2],24+ue|0,24+ue|0,1),q[ve+12>>2]=q[24+ue>>2],q[ve+16>>2]=q[28+ue>>2],u[ve+20>>2]=u[ve+20>>2]+x(x(xe*x(-180))/x(3.1415927410125732)),se<<=2,a=q[a+4>>2]<<2,u[se+Ce>>2]=u[ve+4>>2]*u[a+Ce>>2],xe=x(u[ve+8>>2]*u[a+Be>>2]),u[se+Be>>2]=xe,u[ve+8>>2]=xe}else q[(a=se<<2)+Ce>>2]=q[ve+4>>2],q[a+Be>>2]=q[ve+8>>2];L=64+ue|0},n[5]=function(a){a|=0;var jg,ng,df=0,ef=0,ff=0,cg=0,dg=0,eg=x(0),fg=0,gg=0,hg=0,ig=0,kg=0,lg=0,mg=0,og=0,pg=0,qg=0,rg=0;if(hg=q[a+248>>2],ng=q[a+204>>2],jg=q[a+200>>2],!((0|(dg=q[a+220>>2]))<1)){for(fg=q[a+236>>2],cg=q[a+216>>2],ff=q[a+224>>2];u[(ef=df<<2)+fg>>2]=u[ef+ff>>2]*u[ef+cg>>2],(0|dg)!=(0|(df=df+1|0)););if(!((0|dg)<1))for(fg=q[a+240>>2],cg=q[a+216>>2],ff=q[a+228>>2],df=0;u[(ef=df<<2)+fg>>2]=u[ef+ff>>2]*u[ef+cg>>2],(0|dg)!=(0|(df=df+1|0)););}if(1<=(0|jg))for(og=q[a+208>>2],dg=fg=0;;){if(q[hg>>2]){ig=(ff=q[(ef=fg<<2)+q[a+212>>2]>>2])+dg|0;c:{if((0|ff)<=0)q[ef+q[a+276>>2]>>2]=0;else{for(cg=q[a+236>>2],eg=x(0),df=dg;eg=x(eg+u[cg+(df<<2)>>2]),(0|(df=df+1|0))<(0|ig););if(u[ef+q[a+276>>2]>>2]=eg,!((0|ff)<1)){for(cg=q[a+240>>2],eg=x(0),df=dg;eg=x(eg+u[cg+(df<<2)>>2]),(0|(df=df+1|0))<(0|ig););break c}}eg=x(0)}if(df=ef+q[a+268>>2]|0,eg=x(eg+x(.0010000000474974513)),cg=x(y(eg))<x(2147483648)?~~eg:-2147483648,q[df>>2]=cg,cg=(df=q[12+((fg<<4)+ng|0)>>2])<<1,kg=q[ef+q[a+272>>2]>>2],(df=(0|df)<1)||ba(kg,0,(1<(0|cg)?cg:1)<<2),!(df|(0|ff)<1))for(lg=q[a+244>>2],pg=q[a+216>>2],qg=q[a+232>>2],ef=dg;;){for(gg=(df=ef<<2)+pg|0,rg=q[df+qg>>2],ff=df=0;u[lg+(mg=ff<<2)>>2]=u[rg+mg>>2]*u[gg>>2],(0|(ff=ff+1|0))<(0|cg););for(;u[(gg=(ff=df<<2)+kg|0)>>2]=u[ff+lg>>2]+u[gg>>2],(0|(df=df+1|0))<(0|cg););if(!((0|(ef=ef+1|0))<(0|ig)))break}}if(hg=hg+4|0,dg=q[(fg<<2)+og>>2]+dg|0,(0|jg)==(0|(fg=fg+1|0)))break}},n[6]=function(a){a|=0;var Ng,Rg,Eg=0,Fg=0,Gg=0,Hg=0,Ig=0,Jg=0,Kg=x(0),Lg=0,Mg=0,Og=0,Pg=0,Qg=0,Sg=0,Tg=0,Ug=0,Vg=0,Wg=0;if(Mg=q[a+172>>2],Ng=q[a- -64>>2],Rg=q[a+68>>2],1<=(0|(Gg=q[a+92>>2])))for(Ig=q[a+104>>2],Jg=q[a+88>>2],Fg=q[a+96>>2];u[(Hg=Eg<<2)+Ig>>2]=u[Fg+Hg>>2]*u[Jg+Hg>>2],(0|Gg)!=(0|(Eg=Eg+1|0)););if(1<=(0|Ng))for(Sg=q[a+80>>2],Hg=Ig=0;;){if(q[Mg>>2]){if(Og=(Eg=q[q[a+84>>2]+(Ig<<2)>>2])+Hg|0,Gg=(0|Eg)<1)Kg=x(0);else for(Jg=q[a+104>>2],Kg=x(0),Eg=Hg;Kg=x(Kg+u[Jg+(Eg<<2)>>2]),(0|(Eg=Eg+1|0))<(0|Og););if(Eg=w(Ig,24)+Rg|0,u[Eg+16>>2]=Kg,Jg=(Fg=q[Eg+12>>2])<<1,(Fg=(0|Fg)<1)||ba(q[Eg+20>>2],0,(1<(0|Jg)?Jg:1)<<2),!(Fg|Gg))for(Tg=q[Eg+20>>2],Pg=q[a+108>>2],Ug=q[a+88>>2],Vg=q[a+100>>2],Gg=Hg;;){for(Lg=(Eg=Gg<<2)+Ug|0,Wg=q[Eg+Vg>>2],Fg=Eg=0;u[(Qg=Fg<<2)+Pg>>2]=u[Wg+Qg>>2]*u[Lg>>2],(0|(Fg=Fg+1|0))<(0|Jg););for(;u[(Lg=(Fg=Eg<<2)+Tg|0)>>2]=u[Fg+Pg>>2]+u[Lg>>2],(0|(Eg=Eg+1|0))<(0|Jg););if(!((0|(Gg=Gg+1|0))<(0|Og)))break}}if(Mg=Mg+4|0,Hg=q[(Ig<<2)+Sg>>2]+Hg|0,(0|Ng)==(0|(Ig=Ig+1|0)))break}},n[7]=function(a){a|=0;var eh,Xg=0,Yg=x(0),Zg=0,_g=0,$g=0,ah=0,bh=0,ch=0,dh=0;if(bh=q[a+36>>2],eh=q[a+4>>2],1<=(0|(_g=q[a+24>>2])))for(ch=q[a+32>>2],dh=q[a+20>>2],Zg=q[a+28>>2];u[($g=Xg<<2)+ch>>2]=u[Zg+$g>>2]*u[$g+dh>>2],(0|_g)!=(0|(Xg=Xg+1|0)););if(1<=(0|eh))for($g=q[a+12>>2],Zg=0;;){if(q[bh>>2]){if((0|(Xg=q[(_g=ah<<2)+q[a+16>>2]>>2]))<1)Yg=x(0);else for(ch=Xg+Zg|0,dh=q[a+32>>2],Yg=x(0),Xg=Zg;Yg=x(Yg+u[dh+(Xg<<2)>>2]),(0|(Xg=Xg+1|0))<(0|ch););Xg=_g+q[a+44>>2]|0,Yg=x(Yg+x(.0010000000474974513)),x(y(Yg))<x(2147483648)?q[Xg>>2]=~~Yg:q[Xg>>2]=-2147483648}if(bh=bh+4|0,Zg=q[$g+(ah<<2)>>2]+Zg|0,(0|eh)==(0|(ah=ah+1|0)))break}},n[8]=function(a){a|=0;var Ag,Dg,sg=0,tg=0,ug=x(0),vg=0,wg=0,xg=0,yg=0,zg=0,Bg=0,Cg=0;if(Cg=q[a+176>>2],Dg=q[a+72>>2],Ag=q[a+76>>2],!((0|(xg=q[a+124>>2]))<1)){for(yg=q[a+148>>2],vg=q[a+120>>2],wg=q[a+128>>2];u[(tg=sg<<2)+yg>>2]=u[tg+wg>>2]*u[tg+vg>>2],(0|xg)!=(0|(sg=sg+1|0)););if(!((0|xg)<1)){for(yg=q[a+152>>2],vg=q[a+120>>2],wg=q[a+132>>2],sg=0;u[(tg=sg<<2)+yg>>2]=u[tg+wg>>2]*u[tg+vg>>2],(0|xg)!=(0|(sg=sg+1|0)););if(!((0|xg)<1)){for(yg=q[a+156>>2],vg=q[a+120>>2],wg=q[a+136>>2],sg=0;u[(tg=sg<<2)+yg>>2]=u[tg+wg>>2]*u[tg+vg>>2],(0|xg)!=(0|(sg=sg+1|0)););if(!((0|xg)<1)){for(yg=q[a+160>>2],vg=q[a+120>>2],wg=q[a+140>>2],sg=0;u[(tg=sg<<2)+yg>>2]=u[tg+wg>>2]*u[tg+vg>>2],(0|xg)!=(0|(sg=sg+1|0)););if(!((0|xg)<1))for(yg=q[a+164>>2],vg=q[a+120>>2],wg=q[a+144>>2],sg=0;u[(tg=sg<<2)+yg>>2]=u[tg+wg>>2]*u[tg+vg>>2],(0|xg)!=(0|(sg=sg+1|0)););}}}}if(1<=(0|Dg))for(yg=q[a+112>>2],wg=0;;){if(q[Cg>>2]){b:{c:{d:{e:{if((0|(tg=q[q[a+116>>2]+(zg<<2)>>2]))<=0)q[4+((zg<<5)+Ag|0)>>2]=0;else{for(Bg=tg+wg|0,vg=q[a+148>>2],ug=x(0),sg=wg;ug=x(ug+u[vg+(sg<<2)>>2]),(0|(sg=sg+1|0))<(0|Bg););if(u[4+(xg=(zg<<5)+Ag|0)>>2]=ug,!(tg=(0|tg)<1)){for(vg=q[a+152>>2],ug=x(0),sg=wg;ug=x(ug+u[vg+(sg<<2)>>2]),(0|(sg=sg+1|0))<(0|Bg););if(u[xg+20>>2]=ug,tg)break e;for(vg=q[a+156>>2],ug=x(0),sg=wg;ug=x(ug+u[vg+(sg<<2)>>2]),(0|(sg=sg+1|0))<(0|Bg););if(u[xg+12>>2]=ug,tg)break d;for(vg=q[a+160>>2],ug=x(0),sg=wg;ug=x(ug+u[vg+(sg<<2)>>2]),(0|(sg=sg+1|0))<(0|Bg););if(u[xg+16>>2]=ug,tg)break c;for(vg=q[a+164>>2],ug=x(0),sg=wg;ug=x(ug+u[vg+(sg<<2)>>2]),(0|(sg=sg+1|0))<(0|Bg););break b}}q[20+((zg<<5)+Ag|0)>>2]=0}q[12+((zg<<5)+Ag|0)>>2]=0}q[16+((zg<<5)+Ag|0)>>2]=0}ug=x(0)}u[8+((zg<<5)+Ag|0)>>2]=ug}if(Cg=Cg+4|0,wg=q[yg+(zg<<2)>>2]+wg|0,(0|Dg)==(0|(zg=zg+1|0)))break}},n[9]=function(a){a|=0;var ce,Wd=0,Xd=0,Yd=0,Zd=0,_d=0,$d=x(0),ae=0,be=0,de=0,ee=0;if(ce=q[a+340>>2],1<=(0|(Yd=q[a+360>>2])))for(ae=q[a+368>>2],be=q[a+356>>2],Xd=q[a+364>>2];u[(Zd=Wd<<2)+ae>>2]=u[Xd+Zd>>2]*u[Zd+be>>2],(0|Yd)!=(0|(Wd=Wd+1|0)););if(1<=(0|ce))for(de=q[a+348>>2],ee=q[a+344>>2],Zd=q[a+352>>2],Xd=0;;){if((0|(Wd=q[(Yd=_d<<2)+Zd>>2]))<1)$d=x(0);else for(ae=Wd+Xd|0,be=q[a+368>>2],$d=x(0),Wd=Xd;$d=x($d+u[be+(Wd<<2)>>2]),(0|(Wd=Wd+1|0))<(0|ae););if(u[20+(w(_d,24)+ee|0)>>2]=$d,Xd=q[Yd+de>>2]+Xd|0,(0|ce)==(0|(_d=_d+1|0)))break}},n[10]=function(a){var wd=0,xd=0,yd=0,zd=0,Ad=0,Bd=0,Cd=0;if(!(q[380+(a|=0)>>2]||(0|(wd=q[a+200>>2]))<1))for(Bd=(xd=q[a+204>>2])+(wd<<4)|0,wd=q[a+248>>2],zd=q[a+272>>2];;){if(q[wd>>2]&&(a=1,!((0|(yd=q[xd+12>>2]))<1)))for(yd<<=1,Cd=q[zd>>2];u[(Ad=(a<<2)+Cd|0)>>2]=-u[Ad>>2],(0|(a=a+2|0))<(0|yd););if(zd=zd+4|0,wd=wd+4|0,!((xd=xd+16|0)>>>0<Bd>>>0))break}},n[11]=function(a,Hc,pd){Hc|=0,pd|=0;var rd,qd=0;return fa(rd=q[20+(a|=0)>>2],Hc,qd=pd>>>0<(qd=q[a+16>>2]-rd|0)>>>0?pd:qd),q[a+20>>2]=qd+q[a+20>>2],0|pd},n[12]=function(a){return 0},n[13]=function(a,Hc,id){Hc|=0,id|=0;var kd,jd=0,ld=0,md=0,nd=0,od=0;for(L=kd=L-32|0,jd=q[28+(a|=0)>>2],q[16+kd>>2]=jd,md=q[a+20>>2],q[28+kd>>2]=id,q[24+kd>>2]=Hc,Hc=md-jd|0,md=(q[20+kd>>2]=Hc)+id|0,nd=2,Hc=16+kd|0;;){a:{if((ld=(jd=0)|K(q[a+60>>2],0|Hc,0|nd,12+kd|0))&&(q[1906]=ld,jd=-1),(0|(jd=jd?q[12+kd>>2]=-1:q[12+kd>>2]))!=(0|md)){if(-1<(0|jd))break a;q[a+28>>2]=0,q[a+16>>2]=0,q[a+20>>2]=0,q[a>>2]=32|q[a>>2],2!=((a=0)|nd)&&(a=id-q[Hc+4>>2]|0)}else Hc=q[a+44>>2],q[a+28>>2]=Hc,q[a+20>>2]=Hc,q[a+16>>2]=Hc+q[a+48>>2],a=id;return L=32+kd|0,0|a}ld=jd-((od=(ld=q[Hc+4>>2])>>>0<jd>>>0)?ld:0)|0,q[(Hc=od?Hc+8|0:Hc)>>2]=ld+q[Hc>>2],q[Hc+4>>2]=q[Hc+4>>2]-ld,md=md-jd|0,nd=nd-od|0}},n[14]=function(a,Hc,id,jd){return M=0},n[15]=function(a,Wb,Hc,Ic,Jc,Kc){a|=0,Wb=+Wb,Hc|=0,Ic|=0,Jc|=0,Kc|=0;var Pc,Lc=0,Mc=0,Nc=0,Oc=0,Qc=0,Rc=0,Sc=0,Tc=0,Uc=0,Vc=0,Wc=0,Xc=0,Yc=0,Zc=0,_c=0,$c=0,ad=0,bd=0,cd=0,fd=0,gd=0;if(q[44+(L=Pc=L-560|0)>>2]=0,h(+Wb),Lc=0|e(1),cd=1,fd=4294967295<e(0)>>>0?0:1,$c=(((gd=0)|Lc)<-1?cd:(0|Lc)<=-1?fd:gd)?(h(+(Wb=-Wb)),Lc=0|e(1),e(0),_c=1,3184):2048&Jc?(_c=1,3187):(_c=1&Jc)?3190:3185,2146435072!=(2146435072&Lc))if(Wb=function na(a,pa){var ra,sa,qa=0;if(h(+a),qa=0|e(1),ra=0|e(0),2047!=(0|(qa=(sa=qa)>>>20&2047))){if(!qa)return qa=pa,pa=0==a?0:(a=na(0x10000000000000000*a,pa),q[pa>>2]+-64|0),q[qa>>2]=pa,a;q[pa>>2]=qa+-1022,f(0,0|ra),f(1,-2146435073&sa|1071644672),a=+g()}return a}(Wb,44+Pc|0),0!=(Wb+=Wb)&&(q[44+Pc>>2]=q[44+Pc>>2]+-1),Xc=16+Pc|0,97!=(0|(ad=32|Kc))){for(Lc=(0|Ic)<0,0!=Wb?(Oc=q[44+Pc>>2]+-28|0,q[44+Pc>>2]=Oc,Wb*=268435456):Oc=q[44+Pc>>2],Sc=Lc?6:Ic,Nc=Vc=(0|Oc)<0?48+Pc|0:336+Pc|0;Lc=Wb<4294967296&0<=Wb?~~Wb>>>0:0,Nc=(Ic=Nc)+4|0,0!=(Wb=1e9*(Wb-((q[Ic>>2]=Lc)>>>0))););if((0|Oc)<1)Lc=Nc,Mc=Vc;else for(Mc=Vc;;){if(Uc=(0|Oc)<29?Oc:29,!((Lc=Nc+-4|0)>>>0<Mc>>>0)){for(Ic=Uc,Tc=0;Rc=0,bd=Tc,Tc=q[(Wc=Lc)>>2],Qc=31&Ic,Qc=32<=(63&Ic)>>>0?(Oc=Tc<<Qc,0):(Oc=(1<<Qc)-1&Tc>>>32-Qc,Tc<<Qc),Rc=Oc+Rc|0,Rc=(Tc=bd+Qc|0)>>>0<Qc>>>0?Rc+1|0:Rc,bd=Wc,Wc=gc(Tc=hc(Qc=Tc,Rc,1e9),M,1e9),q[bd>>2]=Qc-Wc,Mc>>>0<=(Lc=Lc+-4|0)>>>0;);(Ic=Tc)&&(q[(Mc=Mc+-4|0)>>2]=Ic)}for(;Mc>>>0<(Lc=Nc)>>>0&&!q[(Nc=Lc+-4|0)>>2];);if(Oc=q[44+Pc>>2]-Uc|0,Nc=Lc,!(0<(0|(q[44+Pc>>2]=Oc))))break}if((0|Oc)<=-1)for(Zc=1+((Sc+25|0)/9|0)|0,Uc=102==(0|ad);;){if(Tc=(0|Oc)<-9?9:0-Oc|0,Lc>>>0<=Mc>>>0)Mc=q[Mc>>2]?Mc:Mc+4|0;else{for(Wc=1e9>>>Tc,Qc=-1<<Tc^-1,Oc=0,Nc=Mc;Ic=q[Nc>>2],q[Nc>>2]=(Ic>>>Tc)+Oc,Oc=w(Wc,Ic&Qc),(Nc=Nc+4|0)>>>0<Lc>>>0;);Mc=q[Mc>>2]?Mc:Mc+4|0,Oc&&(q[Lc>>2]=Oc,Lc=Lc+4|0)}if(Oc=Tc+q[44+Pc>>2]|0,Lc=(0|Zc)<Lc-(Ic=Uc?Vc:Mc)>>2?Ic+(Zc<<2)|0:Lc,!((0|(q[44+Pc>>2]=Oc))<0))break}if(!(Lc>>>(Nc=0)<=Mc>>>0||(Nc=w(Vc-Mc>>2,9),Oc=10,(Ic=q[Mc>>2])>>>0<10)))for(;Nc=Nc+1|0,(Oc=w(Oc,10))>>>0<=Ic>>>0;);if((0|(Ic=(Sc-(102==(0|ad)?0:Nc)|0)-(103==(0|ad)&0!=(0|Sc))|0))<(w(Lc-Vc>>2,9)+-9|0)){if(Rc=(Vc+((Ic=(0|(Qc=Ic+9216|0))/9|0)<<2)|0)-4092|0,Oc=10,(0|(Ic=1+(Qc-w(Ic,9)|0)|0))<=8)for(;Oc=w(Oc,10),9!=(0|(Ic=Ic+1|0)););if(Zc=Rc+4|0,((Uc=(Wc=q[Rc>>2])-w(Oc,Qc=(Wc>>>0)/(Oc>>>0)|0)|0)||(0|Zc)!=(0|Lc))&&(Yc=Uc>>>0<(Ic=Oc>>>1)>>>0?.5:(0|Lc)==(0|Zc)&&(0|Ic)==(0|Uc)?1:1.5,Wb=1&Qc?9007199254740994:9007199254740992,!_c|45!=r[0|$c]||(Yc=-Yc,Wb=-Wb),Ic=Wc-Uc|0,q[Rc>>2]=Ic,Wb+Yc!=Wb)){if(Ic=Ic+Oc|0,1e9<=(q[Rc>>2]=Ic)>>>0)for(;(Rc=Rc+-4|(q[Rc>>2]=0))>>>0<Mc>>>0&&(q[(Mc=Mc+-4|0)>>2]=0),Ic=q[Rc>>2]+1|0,999999999<(q[Rc>>2]=Ic)>>>0;);if(Nc=w(Vc-Mc>>2,9),Oc=10,!((Ic=q[Mc>>2])>>>0<10))for(;Nc=Nc+1|0,(Oc=w(Oc,10))>>>0<=Ic>>>0;);}Lc=(Ic=Rc+4|0)>>>0<Lc>>>0?Ic:Lc}j:{for(;;){if((Uc=Lc)>>>(Wc=0)<=Mc>>>0)break j;if(q[(Lc=Uc+-4|0)>>2])break}Wc=1}if(103==(0|ad)){if(Sc=((Ic=(0|Nc)<(0|(Lc=Sc||1))&-5<(0|Nc))?-1^Nc:-1)+Lc|0,Kc=(Ic?-1:-2)+Kc|0,!(Qc=8&Jc)){if(Lc=9,Wc&&(Qc=q[Uc+-4>>2])&&!((Qc>>>(Lc=0))%(Ic=10)))for(;Lc=Lc+1|0,!((Qc>>>0)%((Ic=w(Ic,10))>>>0)););Ic=w(Uc-Vc>>2,9)+-9|0,Sc=102!=(32|Kc)?((Qc=0)|Sc)<(0|(Ic=0<(0|(Ic=(Ic+Nc|0)-Lc|0))?Ic:0))?Sc:Ic:((Qc=0)|Sc)<(0|(Ic=0<(0|(Ic=Ic-Lc|0))?Ic:0))?Sc:Ic}}else Qc=8&Jc;if(Rc=0!=(0|(Oc=Sc|Qc)),Ic=a,bd=Hc,Lc=0<(0|Nc)?Nc:0,102!=(0|(Tc=32|Kc))){if((Xc-(Lc=ea((Lc=Nc>>31)+Nc^Lc,0,Xc))|0)<=1)for(;o[0|(Lc=Lc+-1|0)]=48,(Xc-Lc|0)<2;);o[0|(Zc=Lc+-2|0)]=Kc,o[Lc+-1|0]=(0|Nc)<0?45:43,Lc=Xc-Zc|0}if(_(Ic,32,bd,Rc=1+(Lc+(Rc+(Sc+_c|0)|0)|0)|0,Jc),Z(a,$c,_c),_(a,48,Hc,Rc,65536^Jc),102!=(0|Tc)){q:if(!((0|Sc)<0))for(Kc=Wc?Uc:Mc+4|0,Ic=16+Pc|8,Vc=16+Pc|9,Nc=Mc;;){(0|Vc)==(0|(Lc=ea(q[Nc>>2],0,Vc)))&&(o[24+Pc|0]=48,Lc=Ic);r:if((0|Mc)==(0|Nc))Z(a,Lc,1),Lc=Lc+1|0,(0|Sc)<1&&!Qc||Z(a,3219,1);else{if(Lc>>>0<=16+Pc>>>0)break r;for(;o[0|(Lc=Lc+-1|0)]=48,16+Pc>>>0<Lc>>>0;);}if(Z(a,Tc=Lc,(0|(Lc=Vc-Lc|0))<(0|Sc)?Lc:Sc),Sc=Sc-Lc|0,Kc>>>0<=(Nc=Nc+4|0)>>>0)break q;if(!(-1<(0|Sc)))break}_(a,48,Sc+18|0,18,0),Z(a,Zc,Xc-Zc|0)}else{for(Ic=16+Pc|8,Nc=16+Pc|9,Mc=Kc=Vc>>>0<Mc>>>0?Vc:Mc;;){Lc=ea(q[Mc>>2],0,Nc);o:if((0|Kc)==(0|Mc))(0|Lc)==(0|Nc)&&(o[24+Pc|0]=48,Lc=Ic);else{if(Lc>>>0<=16+Pc>>>0)break o;for(;o[0|(Lc=Lc+-1|0)]=48,16+Pc>>>0<Lc>>>0;);}if(Z(a,Lc,Nc-Lc|0),!((Mc=Mc+4|0)>>>0<=Vc>>>0))break}Oc&&Z(a,3219,1);p:if(!((0|Sc)<1|Uc>>>0<=Mc>>>0))for(;;){if(16+Pc>>>0<(Lc=ea(q[Mc>>2],0,Nc))>>>0)for(;o[0|(Lc=Lc+-1|0)]=48,16+Pc>>>0<Lc>>>0;);if(Z(a,Lc,(0|Sc)<9?Sc:9),Sc=Sc+-9|0,Uc>>>0<=(Mc=Mc+4|0)>>>0)break p;if(!(0<(0|Sc)))break}_(a,48,Sc+9|0,9,0)}}else{if(Wc=(Vc=32&Kc)?$c+9|0:$c,!(11<Ic>>>0)&&(Lc=12-Ic|0)){for(Yc=8;Yc*=16,Lc=Lc+-1|0;);Wb=45!=r[0|Wc]?Wb+Yc-Yc:-(Yc+(-Wb-Yc))}for((0|Xc)==(0|(Lc=ea((Nc=(Lc=q[44+Pc>>2])>>31)^Lc+Nc,0,Xc)))&&(o[15+Pc|0]=48,Lc=15+Pc|0),Qc=2|_c,Nc=q[44+Pc>>2],o[0|(Uc=Lc+-2|0)]=Kc+15,o[Lc+-1|0]=(0|Nc)<0?45:43,Lc=8&Jc,Mc=16+Pc|0;Kc=Mc,Tc=Vc,Nc=y(Wb)<2147483648?~~Wb:-2147483648,o[0|Mc]=Tc|r[Nc+3168|0],1!=((Mc=Kc+1|0)-(16+Pc|0)|0)|(0==(Wb=16*(Wb-(0|Nc)))?!(Lc|0<(0|Ic)):0)||(o[Kc+1|0]=46,Mc=Kc+2|0),0!=Wb;);_(a,32,Hc,Rc=(Kc=!Ic|(0|Ic)<=((Mc-Pc|0)-18|0)?((Xc-(16+Pc|0)|0)-Uc|0)+Mc|0:2+((Ic+Xc|0)-Uc|0)|0)+Qc|0,Jc),Z(a,Wc,Qc),_(a,48,Hc,Rc,65536^Jc),Z(a,16+Pc|0,Ic=Mc-(16+Pc|0)|0),_(a,48,Kc-((Lc=Ic)+(Ic=Xc-Uc|0)|0)|0,0,0),Z(a,Uc,Ic)}else _(a,32,Hc,Rc=_c+3|0,-65537&Jc),Z(a,$c,_c),Ic=Kc>>>5&1,Z(a,Wb!=Wb?Ic?3211:3215:Ic?3203:3207,3);return _(a,32,Hc,Rc,8192^Jc),L=560+Pc|0,0|((0|Rc)<(0|Hc)?Hc:Rc)},n[16]=function(a,Wb){var wc,Fc,Gc;a|=0,Wb=q[(wc=Wb|=0)>>2]+15&-16,q[wc>>2]=Wb+16,Fc=a,Gc=Ea(q[Wb>>2],q[Wb+4>>2],q[Wb+8>>2],q[Wb+12>>2]),v[Fc>>3]=Gc},{d:function(){},e:function(){return 67108864},f:function(){return 3},g:function(a,Wb){return Wb|=0,L=Wb=L-16|0,a=(a|=0)?ma(a)?(Y(4,1533,0),0):r[a+4|0]:(q[Wb+4>>2]=1246,q[Wb>>2]=1671,Y(4,1087,Wb),0),L=Wb+16|0,0|a},h:function(a){a|=0,q[1641]=a},i:function(a,ri){var ti;return ri|=0,L=ti=L-48|0,a=(a|=0)?(a+63&-64)==(0|a)?(ri+63&-64)==(0|ri)&&ri?jb(a):(q[20+ti>>2]=1592,q[16+ti>>2]=1688,Y(4,1087,16+ti|0),0):(q[36+ti>>2]=1441,q[32+ti>>2]=1688,Y(4,1087,32+ti|0),0):(q[4+ti>>2]=1246,q[ti>>2]=1688,Y(4,1087,ti),0),L=48+ti|0,0|a},j:function(a,$h,ai,bi){var ci;$h|=0,ai|=0,bi|=0,L=ci=L+-64|0,(a|=0)?$h?ai?bi?(a=q[q[a>>2]+708>>2],q[$h>>2]=q[a+12>>2],q[$h+4>>2]=q[a+16>>2],q[ai>>2]=q[a+4>>2],q[ai+4>>2]=q[a+8>>2],q[bi>>2]=q[a>>2]):(q[52+ci>>2]=1782,q[48+ci>>2]=1708,Y(4,1087,48+ci|0)):(q[36+ci>>2]=1753,q[32+ci>>2]=1708,Y(4,1087,32+ci|0)):(q[20+ci>>2]=1726,q[16+ci>>2]=1708,Y(4,1087,16+ci|0)):(q[4+ci>>2]=1651,q[ci>>2]=1708,Y(4,1087,ci)),L=64+ci|0},k:wa,l:va,m:function(a){var _h;L=_h=L-16|0,(a|=0)?ta(a):(q[4+_h>>2]=1651,q[_h>>2]=1890,Y(4,1087,_h)),L=16+_h|0},n:function(a){var Zh;return L=Zh=L-16|0,a=(a|=0)?q[a+292>>2]:(q[4+Zh>>2]=1651,q[Zh>>2]=1905,Y(4,1087,Zh),-1),L=16+Zh|0,0|a},o:function(a){var Yh;return L=Yh=L-16|0,a=(a|=0)?q[q[a>>2]+900>>2]:(q[4+Yh>>2]=1651,q[Yh>>2]=1926,Y(4,1087,Yh),0),L=16+Yh|0,0|a},p:function(a){var Xh;return L=Xh=L-16|0,a=(a|=0)?q[q[a>>2]+912>>2]:(q[4+Xh>>2]=1651,q[Xh>>2]=1945,Y(4,1087,Xh),0),L=16+Xh|0,0|a},q:function(a){var Wh;return L=Wh=L-16|0,a=(a|=0)?q[q[a>>2]+908>>2]:(q[4+Wh>>2]=1651,q[Wh>>2]=1974,Y(4,1087,Wh),0),L=16+Wh|0,0|a},r:function(a){var Vh;return L=Vh=L-16|0,a=(a|=0)?q[q[a>>2]+916>>2]:(q[4+Vh>>2]=1651,q[Vh>>2]=2003,Y(4,1087,Vh),0),L=16+Vh|0,0|a},s:function(a){var Th;return L=Th=L-16|0,a=(a|=0)?q[a+300>>2]:(q[4+Th>>2]=1651,q[Th>>2]=2032,Y(4,1087,Th),0),L=16+Th|0,0|a},t:function(a){var Sh;return L=Sh=L-16|0,a=(a|=0)?q[a+4>>2]:(q[4+Sh>>2]=1651,q[Sh>>2]=2054,Y(4,1087,Sh),-1),L=16+Sh|0,0|a},u:function(a){var Rh;return L=Rh=L-16|0,a=(a|=0)?q[q[a>>2]+712>>2]:(q[4+Rh>>2]=1651,q[Rh>>2]=2070,Y(4,1087,Rh),0),L=16+Rh|0,0|a},v:function(a){var Qh;return L=Qh=L-16|0,a=(a|=0)?q[a+52>>2]:(q[4+Qh>>2]=1651,q[Qh>>2]=2084,Y(4,1087,Qh),0),L=16+Qh|0,0|a},w:function(a){var Ph;return L=Ph=L-16|0,a=(a|=0)?q[q[a>>2]+740>>2]:(q[4+Ph>>2]=1651,q[Ph>>2]=2104,Y(4,1087,Ph),0),L=16+Ph|0,0|a},x:function(a){var Oh;return L=Oh=L-16|0,a=(a|=0)?q[a+200>>2]:(q[4+Oh>>2]=1651,q[Oh>>2]=2132,Y(4,1087,Oh),-1),L=16+Oh|0,0|a},y:function(a){var Nh;return L=Nh=L-16|0,a=(a|=0)?q[q[a>>2]+820>>2]:(q[4+Nh>>2]=1651,q[Nh>>2]=2152,Y(4,1087,Nh),0),L=16+Nh|0,0|a},z:function(a){var Mh;return L=Mh=L-16|0,a=(a|=0)?q[q[a>>2]+872>>2]:(q[4+Mh>>2]=1651,q[Mh>>2]=2170,Y(4,1087,Mh),0),L=16+Mh|0,0|a},A:function(a){var Lh;return L=Lh=L-16|0,a=(a|=0)?q[a+260>>2]:(q[4+Lh>>2]=1651,q[Lh>>2]=2198,Y(4,1087,Lh),0),L=16+Lh|0,0|a},B:function(a){var Kh;return L=Kh=L-16|0,a=(a|=0)?q[q[a>>2]+868>>2]:(q[4+Kh>>2]=1651,q[Kh>>2]=2225,Y(4,1087,Kh),0),L=16+Kh|0,0|a},C:function(a){var Jh;return L=Jh=L-16|0,a=(a|=0)?q[a+268>>2]:(q[4+Jh>>2]=1651,q[Jh>>2]=2254,Y(4,1087,Jh),0),L=16+Jh|0,0|a},D:function(a){var Ih;return L=Ih=L-16|0,a=(a|=0)?q[a+264>>2]:(q[4+Ih>>2]=1651,q[Ih>>2]=2279,Y(4,1087,Ih),0),L=16+Ih|0,0|a},E:function(a){var Hh;return L=Hh=L-16|0,a=(a|=0)?q[a+276>>2]:(q[4+Hh>>2]=1651,q[Hh>>2]=2306,Y(4,1087,Hh),0),L=16+Hh|0,0|a},F:function(a){var Gh;return L=Gh=L-16|0,a=(a|=0)?q[q[a>>2]+896>>2]:(q[4+Gh>>2]=1651,q[Gh>>2]=2330,Y(4,1087,Gh),0),L=16+Gh|0,0|a},G:function(a){var Fh;return L=Fh=L-16|0,a=(a|=0)?q[q[a>>2]+832>>2]:(q[4+Fh>>2]=1651,q[Fh>>2]=2355,Y(4,1087,Fh),0),L=16+Fh|0,0|a},H:function(a){var Eh;return L=Eh=L-16|0,a=(a|=0)?q[q[a>>2]+876>>2]:(q[4+Eh>>2]=1651,q[Eh>>2]=2375,Y(4,1087,Eh),0),L=16+Eh|0,0|a},I:function(a){var Dh;return L=Dh=L-16|0,a=(a|=0)?q[a+272>>2]:(q[4+Dh>>2]=1651,q[Dh>>2]=2402,Y(4,1087,Dh),0),L=16+Dh|0,0|a},J:function(a){var Ch;return L=Ch=L-16|0,a=(a|=0)?q[q[a>>2]+824>>2]:(q[4+Ch>>2]=1651,q[Ch>>2]=2432,Y(4,1087,Ch),0),L=16+Ch|0,0|a},K:function(a){var ih;return L=ih=L-16|0,a=(a|=0)?q[q[a>>2]+888>>2]:(q[4+ih>>2]=1651,q[ih>>2]=2456,Y(4,1087,ih),0),L=16+ih|0,0|a},L:function(a){var hh;return L=hh=L-16|0,a=(a|=0)?q[q[a>>2]+828>>2]:(q[4+hh>>2]=1651,q[hh>>2]=2482,Y(4,1087,hh),0),L=16+hh|0,0|a},M:function(a){var fh;L=fh=L-16|0,(a|=0)?q[a+256>>2]=1:(q[4+fh>>2]=1651,q[fh>>2]=2504,Y(4,1087,fh)),L=16+fh|0},N:function(a){var td;return ya(12+(L=td=L-16|0)|0,64,a|=0),L=16+td|0,q[12+td>>2]},O:function(a){var Hc,pd=0,sd=0;return L=Hc=L-16|0,(a|=0)&&(ya(12+Hc|0,16,sd=wa(a))||(pd=va(a,q[12+Hc>>2],sd))||(za(q[12+Hc>>2]),pd=0)),L=16+Hc|0,0|pd},P:function(a){return 0|ja(a|=0)},Q:function(a){za(a|=0)},R:function(){return 0|L},S:function(a){return 0|(L=a=L-(a|=0)&-16)},T:function(a){L=a|=0},U:function(a){return 0|function(pagesToAdd){pagesToAdd|=0;var P=0|N(),Q=P+pagesToAdd|0;if(P<Q&&Q<65536){var R=new ArrayBuffer(w(Q,65536)),S=new global.Int8Array(R);S.set(o),o=S,o=new global.Int8Array(R),p=new global.Int16Array(R),q=new global.Int32Array(R),r=new global.Uint8Array(R),s=new global.Uint16Array(R),t=new global.Uint32Array(R),u=new global.Float32Array(R),v=new global.Float64Array(R),buffer=R,m.buffer=R}return P}(0|(a|=0))},V:function(a,$h){$h|=0,n[a|=0]($h)}};function X(a,b,c){var d=0,e=0,f=0;if(c)for(;;){if(c=c+-1|0,a>>>0<(d=(e=a+b|0)+-1|0)>>>0)for(;f=r[0|a],o[0|a]=r[0|d],o[0|d]=f,(a=a+1|0)>>>0<(d=d+-1|0)>>>0;);if(a=e,!c)break}}function Y(a,b,c){var g;L=g=L-272|0,t[1640]>a>>>0||(a=q[1641])&&(sa(16+g|0,b,q[12+g>>2]=c),n[a](16+g|0)),L=272+g|0}function Z(a,b,c){32&r[0|a]||function(a,Wb,Hc){var Ic=0,Jc=0,Kc=0;a:{if(!(Ic=q[Hc+16>>2])){if(function(a){var Wb=0;if(Wb=r[a+74|0],o[a+74|0]=Wb+-1|Wb,8&(Wb=q[a>>2]))return q[a>>2]=32|Wb,-1;return q[a+4>>2]=0,q[a+8>>2]=0,Wb=q[a+44>>2],q[a+28>>2]=Wb,q[a+20>>2]=Wb,q[a+16>>2]=Wb+q[a+48>>2],0}(Hc))break a;Ic=q[Hc+16>>2]}if(Kc=q[Hc+20>>2],Ic-Kc>>>0<Wb>>>0)return n[q[Hc+36>>2]](Hc,a,Wb);b:if(!(o[Hc+75|0]<0)){for(Ic=Wb;;){if(!(Jc=Ic))break b;if(10==r[(Ic=Jc+-1|0)+a|0])break}if(n[q[Hc+36>>2]](Hc,a,Jc)>>>0<Jc>>>0)break a;Wb=Wb-Jc|0,a=a+Jc|0,Kc=q[Hc+20>>2]}fa(Kc,a,Wb),q[Hc+20>>2]=q[Hc+20>>2]+Wb}}(b,c,a)}function _(a,b,c,h,i){var j,k=0,l=0;if(L=j=L-256|0,!(73728&i|(0|c)<=(0|h))){if(ba(j,b,(k=(i=c-h|0)>>>0<256)?i:256),b=a,l=j,!k){for(c=c-h|0;Z(a,j,256),255<(i=i+-256|0)>>>0;);i=255&c}Z(b,l,i)}L=256+j|0}function $(a){var b,c;return x((b=a*a)*b*(c=b*a)*(2718311493989822e-21*b-.00019839334836096632)+(c*(.008333329385889463*b-.16666666641626524)+a))}function aa(a){var h;return x(-.499999997251031*(a*=a)+1+.04166662332373906*(h=a*a)+a*h*(2439044879627741e-20*a-.001388676377460993))}function ba(a,i,m){var n=0,p=0,r=0,s=0;if(m&&(o[(n=a+m|0)+-1|0]=i,o[0|a]=i,!(m>>>0<3||(o[n+-2|0]=i,o[a+1|0]=i,o[n+-3|0]=i,o[a+2|0]=i,m>>>0<7||(o[n+-4|0]=i,o[a+3|0]=i,m>>>0<9||(p=(n=0-a&3)+a|0,i=w(255&i,16843009),q[p>>2]=i,q[(n=(m=m-n&-4)+p|0)+-4>>2]=i,m>>>0<9||(q[p+8>>2]=i,q[p+4>>2]=i,q[n+-8>>2]=i,q[n+-12>>2]=i,m>>>0<25||(q[p+24>>2]=i,q[p+20>>2]=i,q[p+16>>2]=i,q[p+12>>2]=i,q[n+-16>>2]=i,q[n+-20>>2]=i,q[n+-24>>2]=i,q[n+-28>>2]=i,(m=m-(s=4&p|24)|0)>>>0<32))))))))for(r=n=i,i=p+s|0;q[i+24>>2]=r,q[i+28>>2]=n,q[i+16>>2]=r,q[i+20>>2]=n,q[i+8>>2]=r,q[i+12>>2]=n,q[i>>2]=r,q[i+4>>2]=n,i=i+32|0,31<(m=m+-32|0)>>>0;);return a}function ca(a,i){var m=0;if(a>>>0<(i=(a+i|0)-1|0)>>>0)for(;m=r[0|a],o[0|a]=r[0|i],o[0|i]=m,(a=a+1|0)>>>0<(i=i+-1|0)>>>0;);}function da(a){var o,i=0;return o=N(),(a=(i=q[2052])+a|0)>>>0<=o<<16>>>0||J(0|a)?(q[2052]=a,i):(q[1906]=48,-1)}function ea(a,q,t){var u=0,v=0,x=0;if(1==(0|q)&a>>>0<0|q>>>0<1)u=a;else for(;v=gc(u=hc(a,q,10),x=v=M,10),o[0|(t=t+-1|0)]=a-v|48,v=9==(0|q)&4294967295<a>>>0|9<q>>>0,a=u,q=x,v;);if(u)for(;a=(u>>>0)/10|0,o[0|(t=t+-1|0)]=u-w(a,10)|48,q=9<u>>>0,u=a,q;);return t}function fa(a,t,w){var y,z=0;if(8192<=w>>>0)I(0|a,0|t,0|w);else{y=a+w|0;a:if(3&(a^t))if(y>>>0<4)w=a;else if((z=y-4|0)>>>0<a>>>0)w=a;else for(w=a;o[0|w]=r[0|t],o[w+1|0]=r[t+1|0],o[w+2|0]=r[t+2|0],o[w+3|0]=r[t+3|0],t=t+4|0,(w=w+4|0)>>>0<=z>>>0;);else{b:if((0|w)<1)w=a;else if(3&a)for(w=a;;){if(o[0|w]=r[0|t],t=t+1|0,y>>>0<=(w=w+1|0)>>>0)break b;if(!(3&w))break}else w=a;if(!((a=-4&y)>>>0<64||(z=a+-64|0)>>>0<w>>>0))for(;q[w>>2]=q[t>>2],q[w+4>>2]=q[t+4>>2],q[w+8>>2]=q[t+8>>2],q[w+12>>2]=q[t+12>>2],q[w+16>>2]=q[t+16>>2],q[w+20>>2]=q[t+20>>2],q[w+24>>2]=q[t+24>>2],q[w+28>>2]=q[t+28>>2],q[w+32>>2]=q[t+32>>2],q[w+36>>2]=q[t+36>>2],q[w+40>>2]=q[t+40>>2],q[w+44>>2]=q[t+44>>2],q[w+48>>2]=q[t+48>>2],q[w+52>>2]=q[t+52>>2],q[w+56>>2]=q[t+56>>2],q[w+60>>2]=q[t+60>>2],t=t- -64|0,(w=w- -64|0)>>>0<=z>>>0;);if(a>>>0<=w>>>0)break a;for(;q[w>>2]=q[t>>2],t=t+4|0,(w=w+4|0)>>>0<a>>>0;);}if(w>>>0<y>>>0)for(;o[0|w]=r[0|t],t=t+1|0,(0|y)!=(0|(w=w+1|0)););}}function ga(a){return a+-48>>>0<10}function ha(a,q){var t=0;a:if(1024<=(0|q)){if(a*=898846567431158e293,(0|(t=q+-1023|0))<1024){q=t;break a}a*=898846567431158e293,q=((0|q)<3069?q:3069)+-2046|0}else-1023<(0|q)||(a*=22250738585072014e-324,q=-1023<(0|(t=q+1022|0))?t:(a*=22250738585072014e-324,(-3066<(0|q)?q:-3066)+2044|0));return f(0,0),f(1,q+1023<<20),a*g()}function ia(a,A,B,C,D,E,F){var G,S,V,H=0,I=0,J=0,K=0,M=0,N=0,O=0,P=0,Q=0,R=0,T=0,U=0;q[76+(L=G=L-80|0)>>2]=A,V=55+G|0,S=56+G|0,A=0;a:{b:{c:for(;;){(0|Q)<0||(Q=(2147483647-Q|0)<(0|A)?(q[1906]=61,-1):A+Q|0);e:{f:{g:{h:{i:{j:{k:{l:{m:{n:{o:{p:{q:{if(K=q[76+G>>2],J=r[0|(A=K)]){for(;;){r:{s:{t:if(H=255&J){if(37!=(0|H))break s;for(J=A;;){if(37!=r[A+1|0])break t;if(H=A+2|0,q[76+G>>2]=H,J=J+1|0,I=r[A+2|0],A=H,37!=(0|I))break}}else J=A;if(A=J-K|0,a&&Z(a,K,A),A)continue c;R=-1,J=1,M=!ga(o[q[76+(H=G)>>2]+1|0]),A=q[76+G>>2],M|36!=r[A+2|0]||(R=o[A+1|0]+-48|0,T=1,J=3),A=J+A|0,q[H+76>>2]=A;u:if(31<(I=(O=o[(J=0)|A])+-32|0)>>>0)H=A;else if(H=A,75913&(I=1<<I))for(;;){if(H=A+1|0,q[76+G>>2]=H,J|=I,31<(I=(O=o[A+1|0])+-32|0)>>>0)break u;if(A=H,!(75913&(I=1<<I)))break}v:if(42!=(0|O)){if((0|(P=qa(76+G|0)))<0)break b;A=q[76+G>>2]}else{if(M=G,ga(o[H+1|0])&&(A=q[76+G>>2],36==r[A+2|0]))q[((o[A+1|0]<<2)+D|0)-192>>2]=10,P=q[((o[A+1|0]<<3)+C|0)-384>>2],T=1,A=A+3|0;else{if(T)break b;P=T=0,a&&(A=q[B>>2],q[B>>2]=A+4,P=q[A>>2]),A=q[76+G>>2]+1|0}if(q[M+76>>2]=A,-1<(0|P))break v;P=0-P|0,J|=8192}I=-1;y:if(46==r[0|A])if(42!=r[A+1|0])q[76+G>>2]=A+1,I=qa(76+G|0),A=q[76+G>>2];else{if(ga(o[A+2|0])&&(A=q[76+G>>2],36==r[A+3|0])){q[((o[A+2|0]<<2)+D|0)-192>>2]=10,I=q[((o[A+2|0]<<3)+C|0)-384>>2],A=A+4|0,q[76+G>>2]=A;break y}if(T)break b;I=a?(A=q[B>>2],q[B>>2]=A+4,q[A>>2]):0,A=q[76+G>>2]+2|0,q[76+G>>2]=A}for(H=0;;){if(U=H,N=-1,57<o[0|A]+-65>>>0)break a;if(O=A+1|0,q[76+G>>2]=O,H=o[0|A],A=O,!((H=r[2639+(H+w(U,58)|0)|0])+-1>>>0<8))break}if(!H)break a;A:{B:{C:{if(19==(0|H)){if((0|R)<=-1)break C;break a}if((0|R)<0)break B;q[(R<<2)+D>>2]=H,H=q[(A=(R<<3)+C|0)+4>>2],q[64+G>>2]=q[A>>2],q[68+G>>2]=H}if(A=0,!a)continue c;break A}if(!a)break e;pa(G+64|0,H,B,F),O=q[76+G>>2]}if(M=-65537&J,J=8192&J?M:J,R=2684,H=S,A=o[O+-1|(N=0)],(O=(A=U&&3==(15&A)?-33&A:A)+-88|0)>>>0<=32)break r;D:{E:{F:{G:{if(6<(M=A+-65|0)>>>0){if(83!=(0|A))break f;if(!I)break G;H=q[64+G>>2];break E}switch(M-1|0){case 1:break F;case 0:case 2:break f;default:break q}}_(a,32,P,A=0,J);break D}q[12+G>>2]=0,q[8+G>>2]=q[64+G>>2],q[64+G>>2]=8+G,I=-1,H=8+G|0}A=0;H:{for(;;){if(!(K=q[H>>2]))break H;if((M=(0|(K=oa(4+G|0,K)))<0)|I-A>>>0<K>>>0)break;if(H=H+4|0,!((A=A+K|0)>>>0<I>>>0))break H}if(N=-1,M)break a}if(_(a,32,P,A,J),A)for(I=0,H=q[64+G>>2];;){if(!(K=q[H>>2]))break D;if((0|A)<(0|(I=(K=oa(4+G|0,K))+I|0)))break D;if(Z(a,4+G|0,K),H=H+4|0,!(I>>>0<A>>>0))break}else A=0}_(a,32,P,A,8192^J),A=(0|A)<(0|P)?P:A;continue c}H=A+1|0,q[76+G>>2]=H,J=r[A+1|0],A=H;continue}break}switch(O-1|0){case 28:break i;case 21:break j;case 23:break l;case 22:break m;case 11:case 16:break n;case 10:break o;case 26:break p;case 8:case 12:case 13:case 14:break q;case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:case 9:case 15:case 17:case 18:case 19:case 20:case 24:case 25:case 27:case 29:case 30:break f;default:break k}}if(N=Q,a)break a;if(!T)break e;for(A=1;;){if(a=q[(A<<2)+D>>2]){if(pa((A<<3)+C|0,a,B,F),10!=(0|(A=A+(N=1)|0)))continue;break a}break}if(N=1,9<A>>>0)break a;if(N=-1,q[(A<<2)+D>>2])break a;for(;!q[((A=A+1|0)<<2)+D>>2]&&10!=(0|A););N=A>>>0<10?-1:1;break a}A=0|n[E](a,v[64+G>>3],P,I,J,A);continue}H=(A=Ia(K=(A=q[64+G>>2])||2694,I))||I+K|0,J=M,I=A?A-K|0:I;break f}o[55+G|0]=q[64+G>>2],I=1,K=V,J=M;break f}if(A=M=q[68+G>>2],K=q[64+G>>2],(0|A)<-1||(0|A)<=-1&&!(4294967295<K>>>0)){A=0-(A+(0<K>>>0)|0)|0,K=0-K|0,q[64+G>>2]=K,q[68+G>>2]=A,N=1,R=2684;break h}if(2048&J){N=1,R=2685;break h}R=(N=1&J)?2686:2684;break h}if(K=Oa(q[64+G>>2],q[68+G>>2],S),!(8&J))break g;I=(0|(A=S-K|0))<(0|I)?I:A+1|0;break g}I=8<I>>>0?I:8,J|=8,A=120}if(K=Na(q[64+G>>2],q[68+G>>2],S,32&A),!(8&J)|!(q[64+G>>2]|q[68+G>>2]))break g;R=2684+(A>>>4)|0,N=2;break g}if(7<(H=255&U)>>>(A=0))continue;I:switch(H-1|0){default:case 0:q[q[64+G>>2]>>2]=Q;continue;case 1:H=q[64+G>>2],q[H>>2]=Q,q[H+4>>2]=Q>>31;continue;case 2:p[q[64+G>>2]>>1]=Q;continue;case 3:o[q[64+G>>2]]=Q;continue;case 5:q[q[64+G>>2]>>2]=Q;continue;case 4:continue;case 6:break I}H=q[64+G>>2],q[H>>2]=Q,q[H+4>>2]=Q>>31;continue}K=q[64+G>>2],A=q[68+G>>2],R=2684}K=ea(K,A,S)}J=-1<(0|I)?-65537&J:J,I=!!((M=A=q[68+G>>2])|(O=q[64+G>>2]))|I?(0|(A=!(M|O)+(S-K|0)|0))<(0|I)?I:A:(K=S,0)}_(a,32,A=(0|P)<(0|(H=(I=(0|I)<(0|(M=H-K|0))?M:I)+N|0))?H:P,H,J),Z(a,R,N),_(a,48,A,H,65536^J),_(a,48,I,M,0),Z(a,K,M),_(a,32,A,H,8192^J);continue}break}N=0;break a}N=-1}return L=80+G|0,N}function ja(a){var Z,w=0,A=0,B=0,C=0,D=0,E=0,F=0,W=0,X=0,Y=0,_=0,$=0;L=Z=L-16|0;a:{b:{c:{d:{e:{f:{g:{h:{i:{j:{k:{if(a>>>0<=244){if(3&(w=(D=q[1923])>>>(a=(E=a>>>0<11?16:a+11&-8)>>>3))){a=(w=q[(C=(A=a+(1&(-1^w))|0)<<3)+7740>>2])+8|0,(0|(B=q[w+8>>2]))!=(0|(C=C+7732|0))?(q[B+12>>2]=C,q[C+8>>2]=B):(_=7692,$=jc(A)&D,q[_>>2]=$),A<<=3,q[w+4>>2]=3|A,q[(w=w+A|0)+4>>2]=1|q[w+4>>2];break a}if(E>>>0<=(W=q[1925])>>>0)break k;if(w){A=w=(a=(0-(a=(0-(A=2<<a)|A)&w<<a)&a)-1|0)>>>12&16,A|=w=(a>>>=w)>>>5&8,A|=w=(a>>>=w)>>>2&4,w=q[(B=(A=((A|=w=(a>>>=w)>>>1&2)|(w=(a>>>=w)>>>1&1))+(a>>>w)|0)<<3)+7740>>2],(0|(a=q[w+8>>2]))!=(0|(B=B+7732|0))?(q[a+12>>2]=B,q[B+8>>2]=a):(D=jc(A)&D,q[1923]=D),a=w+8|0,q[w+4>>2]=3|E,C=(A<<=3)-E|0,q[(F=w+E|0)+4>>2]=1|C,q[w+A>>2]=C,W&&(w=7732+((A=W>>>3)<<3)|0,B=q[1928],A=(A=1<<A)&D?q[w+8>>2]:(q[1923]=A|D,w),q[w+8>>2]=B,q[A+12>>2]=B,q[B+12>>2]=w,q[B+8>>2]=A),q[1928]=F,q[1925]=C;break a}if(!(Y=q[1924]))break k;for(A=w=(a=(Y&0-Y)-1|0)>>>12&16,A|=w=(a>>>=w)>>>5&8,A|=w=(a>>>=w)>>>2&4,w=q[7996+(((A|=w=(a>>>=w)>>>1&2)|(w=(a>>>=w)>>>1&1))+(a>>>w)<<2)>>2],B=(-8&q[w+4>>2])-E|0,A=w;a=(a=q[A+16>>2])||q[A+20>>2];)B=(A=(C=(-8&q[a+4>>2])-E|0)>>>0<B>>>0)?C:B,w=A?a:w,A=a;if(X=q[w+24>>2],(0|(C=q[w+12>>2]))!=(0|w)){a=q[w+8>>2],q[a+12>>2]=C,q[C+8>>2]=a;break b}if(!(a=q[(A=w+20|0)>>2])){if(!(a=q[w+16>>2]))break j;A=w+16|0}for(;F=A,(a=q[(A=(C=a)+20|0)>>2])||(A=C+16|0,a=q[C+16>>2]););q[F>>2]=0;break b}if(E=-1,!(4294967231<a>>>0)&&(E=-8&(w=a+11|0),W=q[1924])){A=0-E|0,D=0,(w>>>=8)&&(D=31,16777215<E>>>0||(D=28+((a=((D=(w<<=B=w+1048320>>>16&8)<<(a=w+520192>>>16&4))<<(w=D+245760>>>16&2)>>>15)-(w|a|B)|0)<<1|E>>>a+21&1)|0));q:{r:{if(B=q[7996+(D<<2)>>2])for(w=E<<(31==(0|D)?0:25-(D>>>1)|0),a=0;;){if(!(A>>>0<=(F=(-8&q[B+4>>2])-E|0)>>>0||(C=B,A=F))){A=0,a=B;break r}if(F=q[B+20>>2],B=q[16+((w>>>29&4)+B|0)>>2],a=F?(0|F)==(0|B)?a:F:a,w<<=0!=(0|B),!B)break}else a=0;if(!(a|C)){if(!(a=(0-(a=2<<D)|a)&W))break k;B=w=(a=(a&0-a)-1|0)>>>12&16,B|=w=(a>>>=w)>>>5&8,B|=w=(a>>>=w)>>>2&4,a=q[7996+(((B|=w=(a>>>=w)>>>1&2)|(w=(a>>>=w)>>>1&1))+(a>>>w)<<2)>>2]}if(!a)break q}for(;A=(w=(B=(-8&q[a+4>>2])-E|0)>>>0<A>>>0)?B:A,C=w?a:C,a=(w=q[a+16>>2])||q[a+20>>2];);}if(!(!C|A>>>0>=q[1925]-E>>>0)){if(F=q[C+24>>2],(0|C)!=(0|(w=q[C+12>>2]))){a=q[C+8>>2],q[a+12>>2]=w,q[w+8>>2]=a;break c}if(!(a=q[(B=C+20|0)>>2])){if(!(a=q[C+16>>2]))break i;B=C+16|0}for(;D=B,(a=q[(B=(w=a)+20|0)>>2])||(B=w+16|0,a=q[w+16>>2]););q[D>>2]=0;break c}}}if(E>>>0<=(w=q[1925])>>>0){a=q[1928],16<=(A=w-E|0)>>>0?(q[1925]=A,B=a+E|0,q[1928]=B,q[B+4>>2]=1|A,q[a+w>>2]=A,q[a+4>>2]=3|E):(q[1928]=0,q[1925]=0,q[a+4>>2]=3|w,q[(w=a+w|0)+4>>2]=1|q[w+4>>2]),a=a+8|0;break a}if(E>>>0<(B=q[1926])>>>0){w=B-E|0,q[1926]=w,A=(a=q[1929])+E|0,q[1929]=A,q[A+4>>2]=1|w,q[a+4>>2]=3|E,a=a+8|0;break a}if((A=(D=(A=C=E+47|(a=0))+(w=q[2041]?q[2043]:(q[2044]=-1,q[2045]=-1,q[2042]=4096,q[2043]=4096,q[2041]=12+Z&-16^1431655768,q[2046]=0,q[2034]=0,4096))|0)&(F=0-w|0))>>>0<=E>>>0)break a;if((w=q[2033])&&(X=(W=q[2031])+A|0)>>>0<=W>>>0|w>>>0<X>>>0)break a;if(4&r[8136])break f;v:{w:{if(w=q[1929])for(a=8140;;){if((W=q[a>>2])+q[a+4>>2]>>>0>w>>>0&&W>>>0<=w>>>0)break w;if(!(a=q[a+8>>2]))break}if(-1==(0|(w=da(0))))break g;if(D=A,(B=(a=q[2042])+-1|0)&w&&(D=(A-w|0)+(w+B&0-a)|0),D>>>0<=E>>>0|2147483646<D>>>0)break g;if((a=q[2033])&&(F=(B=q[2031])+D|0)>>>0<=B>>>0|a>>>0<F>>>0)break g;if((0|w)!=(0|(a=da(D))))break v;break e}if(2147483646<(D=F&D-B)>>>0)break g;if((0|(w=da(D)))==(q[a>>2]+q[a+4>>2]|0))break h;a=w}if(!(E+48>>>0<=D>>>0|2147483646<D>>>0|-1==(0|(w=a)))){if(2147483646<(a=(a=q[2043])+(C-D|0)&0-a)>>>0)break e;if(-1!=(0|da(a))){D=a+D|0;break e}da(0-D|0);break g}if(-1!=(0|w))break e;break g}C=0;break b}w=0;break c}if(-1!=(0|w))break e}q[2034]=4|q[2034]}if(2147483646<A>>>0)break d;if(w=da(A),(a=da(0))>>>0<=w>>>0|-1==(0|w)|-1==(0|a))break d;if((D=a-w|0)>>>0<=E+40>>>0)break d}a=q[2031]+D|0,(q[2031]=a)>>>0>t[2032]&&(q[2032]=a);x:{y:{z:{if(A=q[1929]){for(a=8140;;){if(((B=q[a>>2])+(C=q[a+4>>2])|0)==(0|w))break z;if(!(a=q[a+8>>2]))break}break y}for((a=q[1927])>>>0<=w>>>0&&a||(q[1927]=w),a=0,q[2036]=D,q[2035]=w,q[1931]=-1,q[1932]=q[2041],q[2038]=0;B=(A=a<<3)+7732|0,q[A+7740>>2]=B,q[A+7744>>2]=B,32!=(0|(a=a+1|0)););B=(a=D+-40|0)-(A=w+8&7?-8-w&7:0)|0,q[1926]=B,A=w+A|0,q[1929]=A,q[A+4>>2]=1|B,q[4+(a+w|0)>>2]=40,q[1930]=q[2045];break x}if(!(8&r[a+12|0]|w>>>0<=A>>>0|A>>>0<B>>>0)){q[a+4>>2]=C+D,w=(a=A+8&7?-8-A&7:0)+A|0,q[1929]=w,a=(B=q[1926]+D|0)-a|0,q[1926]=a,q[w+4>>2]=1|a,q[4+(A+B|0)>>2]=40,q[1930]=q[2045];break x}}w>>>0<(C=q[1927])>>>0&&(q[1927]=w,C=0),B=w+D|0,a=8140;A:{B:{C:{D:{E:{F:{for(;(0|B)!=q[a>>2];)if(!(a=q[a+8>>2]))break F;if(!(8&r[a+12|0]))break E}for(a=8140;;){if((B=q[a>>2])>>>0<=A>>>0&&A>>>0<(C=B+q[a+4>>2]|0)>>>0)break D;a=q[a+8>>2]}}if(q[a>>2]=w,q[a+4>>2]=q[a+4>>2]+D,q[(X=(w+8&7?-8-w&7:0)+w|0)+4>>2]=3|E,a=((w=B+(B+8&7?-8-B&7:0)|0)-X|0)-E|0,F=E+X|0,(0|w)==(0|A)){q[1929]=F,a=q[1926]+a|0,q[1926]=a,q[F+4>>2]=1|a;break B}if(q[1928]==(0|w)){q[1928]=F,a=q[1925]+a|0,q[1925]=a,q[F+4>>2]=1|a,q[a+F>>2]=a;break B}if(1==(3&(A=q[w+4>>2]))){Y=-8&A;G:if(A>>>0<=255){if(C=A>>>3,A=q[w+8>>2],(0|(B=q[w+12>>2]))==(0|A)){_=7692,$=q[1923]&jc(C),q[_>>2]=$;break G}q[A+12>>2]=B,q[B+8>>2]=A}else{if(W=q[w+24>>2],(0|(D=q[w+12>>2]))==(0|w))if((E=q[(B=w+20|0)>>2])||(E=q[(B=w+16|0)>>2])){for(;A=B,(E=q[(B=(D=E)+20|0)>>2])||(B=D+16|0,E=q[D+16>>2]););q[A>>2]=0}else D=0;else A=q[w+8>>2],q[A+12>>2]=D,q[D+8>>2]=A;if(W){A=q[w+28>>2];J:{if(q[(B=7996+(A<<2)|0)>>2]==(0|w)){if(q[B>>2]=D)break J;_=7696,$=q[1924]&jc(A),q[_>>2]=$;break G}if(!(q[W+(q[W+16>>2]==(0|w)?16:20)>>2]=D))break G}q[D+24>>2]=W,(A=q[w+16>>2])&&(q[D+16>>2]=A,q[A+24>>2]=D),(A=q[w+20>>2])&&(q[D+20>>2]=A,q[A+24>>2]=D)}}w=w+Y|0,a=a+Y|0}if(q[w+4>>2]=-2&q[w+4>>2],q[F+4>>2]=1|a,(q[a+F>>2]=a)>>>0<=255){a=7732+((w=a>>>3)<<3)|0,w=(A=q[1923])&(w=1<<w)?q[a+8>>2]:(q[1923]=w|A,a),q[a+8>>2]=F,q[w+12>>2]=F,q[F+12>>2]=a,q[F+8>>2]=w;break B}if(w=0,(B=a>>>8)&&(w=31,16777215<a>>>0||(w=28+((w=((E=(B<<=C=B+1048320>>>16&8)<<(w=B+520192>>>16&4))<<(B=E+245760>>>16&2)>>>15)-(B|w|C)|0)<<1|a>>>w+21&1)|0)),q[(A=F)+28>>2]=w,q[F+16>>2]=0,A=7996+(w<<2)|(q[F+20>>2]=0),(B=q[1924])&(C=1<<w)){for(B=a<<(31==(0|w)?0:25-(w>>>1)|0),w=q[A>>2];;){if((-8&q[(A=w)+4>>2])==(0|a))break C;if(w=B>>>29,B<<=1,!(w=q[(C=(4&w)+A|0)+16>>2]))break}q[C+16>>2]=F}else q[1924]=B|C,q[A>>2]=F;q[F+24>>2]=A,q[F+12>>2]=F,q[F+8>>2]=F;break B}for(F=(a=D+-40|0)-(B=w+8&7?-8-w&7:0)|0,q[1926]=F,B=w+B|0,q[1929]=B,q[B+4>>2]=1|F,q[4+(a+w|0)>>2]=40,q[1930]=q[2045],q[(B=(a=(C+(C+-39&7?39-C&7:0)|0)-47|0)>>>0<A+16>>>0?A:a)+4>>2]=27,a=q[2038],q[B+16>>2]=q[2037],q[B+20>>2]=a,a=q[2036],q[B+8>>2]=q[2035],q[B+12>>2]=a,q[2037]=B+8,q[2036]=D,q[2035]=w,a=B+24|(q[2038]=0);q[a+4>>2]=7,w=a+8|0,a=a+4|0,w>>>0<C>>>0;);if((0|A)==(0|B))break x;if(q[B+4>>2]=-2&q[B+4>>2],C=B-A|0,q[A+4>>2]=1|C,(q[B>>2]=C)>>>0<=255){a=7732+((w=C>>>3)<<3)|0,w=(B=q[1923])&(w=1<<w)?q[a+8>>2]:(q[1923]=w|B,a),q[a+8>>2]=A,q[w+12>>2]=A,q[A+12>>2]=a,q[A+8>>2]=w;break x}if(q[A+16>>2]=0,a=q[A+20>>2]=0,(B=C>>>8)&&(a=31,16777215<C>>>0||(a=28+((a=((F=(B<<=D=B+1048320>>>16&8)<<(a=B+520192>>>16&4))<<(B=F+245760>>>16&2)>>>15)-(B|a|D)|0)<<1|C>>>a+21&1)|0)),w=7996+((q[(w=A)+28>>2]=a)<<2)|0,(B=q[1924])&(D=1<<a)){for(a=C<<(31==(0|a)?0:25-(a>>>1)|0),w=q[w>>2];;){if((0|C)==(-8&q[(B=w)+4>>2]))break A;if(w=a>>>29,a<<=1,!(w=q[(D=B+(4&w)|0)+16>>2]))break}q[D+16>>2]=A,q[A+24>>2]=B}else q[1924]=B|D,q[w>>2]=A,q[A+24>>2]=w;q[A+12>>2]=A,q[A+8>>2]=A;break x}a=q[A+8>>2],q[a+12>>2]=F,q[A+8>>2]=F,q[F+24>>2]=0,q[F+12>>2]=A,q[F+8>>2]=a}a=X+8|0;break a}a=q[B+8>>2],q[a+12>>2]=A,q[B+8>>2]=A,q[A+24>>2]=0,q[A+12>>2]=B,q[A+8>>2]=a}if(!((a=q[1926])>>>0<=E>>>0)){w=a-E|0,q[1926]=w,A=(a=q[1929])+E|0,q[1929]=A,q[A+4>>2]=1|w,q[a+4>>2]=3|E,a=a+8|0;break a}}q[1906]=48,a=0;break a}Q:if(F){a=q[C+28>>2];R:{if(q[(B=7996+(a<<2)|0)>>2]==(0|C)){if(q[B>>2]=w)break R;W=jc(a)&W,q[1924]=W;break Q}if(!(q[F+(q[F+16>>2]==(0|C)?16:20)>>2]=w))break Q}q[w+24>>2]=F,(a=q[C+16>>2])&&(q[w+16>>2]=a,q[a+24>>2]=w),(a=q[C+20>>2])&&(q[w+20>>2]=a,q[a+24>>2]=w)}S:if(A>>>0<=15)a=A+E|0,q[C+4>>2]=3|a,q[(a=a+C|0)+4>>2]=1|q[a+4>>2];else if(q[C+4>>2]=3|E,q[(B=C+E|0)+4>>2]=1|A,(q[A+B>>2]=A)>>>0<=255)a=7732+((w=A>>>3)<<3)|0,w=(A=q[1923])&(w=1<<w)?q[a+8>>2]:(q[1923]=w|A,a),q[a+8>>2]=B,q[w+12>>2]=B,q[B+12>>2]=a,q[B+8>>2]=w;else{a=0,(E=A>>>8)&&(a=31,16777215<A>>>0||(a=28+((a=((F=(E<<=D=E+1048320>>>16&8)<<(a=E+520192>>>16&4))<<(E=F+245760>>>16&2)>>>15)-(E|a|D)|0)<<1|A>>>a+21&1)|0)),q[(w=B)+28>>2]=a,q[B+16>>2]=0,w=7996+(a<<2)|(q[B+20>>2]=0);V:{if((E=1<<a)&W){for(a=A<<(31==(0|a)?0:25-(a>>>1)|0),E=q[w>>2];;){if((-8&q[(w=E)+4>>2])==(0|A))break V;if(E=a>>>29,a<<=1,!(E=q[(D=(4&E)+w|0)+16>>2]))break}q[D+16>>2]=B}else q[1924]=E|W,q[w>>2]=B;q[B+24>>2]=w,q[B+12>>2]=B,q[B+8>>2]=B;break S}a=q[w+8>>2],q[a+12>>2]=B,q[w+8>>2]=B,q[B+24>>2]=0,q[B+12>>2]=w,q[B+8>>2]=a}a=C+8|0;break a}X:if(X){a=q[w+28>>2];Y:{if(q[(A=7996+(a<<2)|0)>>2]==(0|w)){if(q[A>>2]=C)break Y;_=7696,$=jc(a)&Y,q[_>>2]=$;break X}if(!(q[X+(q[X+16>>2]==(0|w)?16:20)>>2]=C))break X}q[C+24>>2]=X,(a=q[w+16>>2])&&(q[C+16>>2]=a,q[a+24>>2]=C),(a=q[w+20>>2])&&(q[C+20>>2]=a,q[a+24>>2]=C)}B>>>0<=15?(a=B+E|0,q[w+4>>2]=3|a,q[(a=a+w|0)+4>>2]=1|q[a+4>>2]):(q[w+4>>2]=3|E,q[(E=w+E|0)+4>>2]=1|B,q[B+E>>2]=B,W&&(a=7732+((A=W>>>3)<<3)|0,C=q[1928],A=(A=1<<A)&D?q[a+8>>2]:(q[1923]=A|D,a),q[a+8>>2]=C,q[A+12>>2]=C,q[C+12>>2]=a,q[C+8>>2]=A),q[1928]=E,q[1925]=B),a=w+8|0}return L=16+Z|0,a}function ka(a){var q=0,L=x(0),aa=0,ba=0,ca=x(0),da=x(0);j(a);a:{if(1283457024<=(q=2147483647&(ba=e(0)))>>>0){if(2139095040<q>>>0)break a;return x((0|ba)<0?-1.570796251296997:1.570796251296997)}b:{if(q>>>0<=1054867455){if(aa=-1,964689920<=q>>>0)break b;break a}if(a=x(y(a)),q>>>0<=1066926079){if(q>>>0<=1060110335){a=x(x(x(a+a)+x(-1))/x(a+x(2))),aa=0;break b}a=x(x(a+x(-1))/x(a+x(1))),aa=1}else aa=q>>>0<=1075576831?(a=x(x(a+x(-1.5))/x(x(a*x(1.5))+x(1))),2):(a=x(x(-1)/a),3)}if(q=aa,ca=x(a*a),L=x(ca*ca),da=x(L*x(x(L*x(-.106480173766613))+x(-.19999158382415771))),L=x(ca*x(x(L*x(x(L*x(.06168760731816292))+x(.14253635704517365)))+x(.333333283662796))),(0|q)<=-1)return x(a-x(a*x(da+L)));a=x(u[(q<<=2)+6080>>2]-x(x(x(a*x(da+L))-u[q+6096>>2])-a)),a=(0|ba)<0?x(-a):a}return a}function la(a,ea){var ha,fa=0,ga=0,ia=0,ja=0,ka=0;L=ha=L-16|0,j(a);a:if((fa=2147483647&(ia=e(0)))>>>0<=1305022426){if(ga=.6366197723675814*(ja=+a)+6755399441055744-6755399441055744,v[ea>>3]=ja+-1.5707963109016418*ga+-1.5893254773528196e-8*ga,y(ga)<2147483648){fa=~~ga;break a}fa=-2147483648}else 2139095040<=fa>>>0?(v[ea>>3]=x(a-a),fa=0):(fa=((ka=fa)>>>23)-150|0,v[8+ha>>3]=(f(0,ka-(fa<<23)|0),k()),fa=Da(8+ha|0,ha,fa),ga=v[ha>>3],(0|ia)<=-1?(v[ea>>3]=-ga,fa=0-fa|0):v[ea>>3]=ga);return L=16+ha|0,fa}function ma(a){var ea=0,la=0,ma=0,na=0,oa=0;ma=4,la=1082;a:if(ea=r[0|a]){for(;!((0|(na=r[0|la]))!=(0|ea)||!(ma=ma+-1|0)|!na);)if(la=la+1|0,ea=r[a+1|0],a=a+1|0,!ea)break a;oa=ea}return(255&oa)-r[0|la]|0}function oa(a,pa){return a?function(a,Wb){a:{if(a){if(Wb>>>0<=127)break a;if(q[q[1625]>>2]){if(Wb>>>0<=2047)return o[a+1|0]=63&Wb|128,o[0|a]=Wb>>>6|192,2;if(!(57344!=(-8192&Wb)&&55296<=Wb>>>0))return o[a+2|0]=63&Wb|128,o[0|a]=Wb>>>12|224,o[a+1|0]=Wb>>>6&63|128,3;if(Wb+-65536>>>0<=1048575)return o[a+3|0]=63&Wb|128,o[0|a]=Wb>>>18|240,o[a+2|0]=Wb>>>6&63|128,o[a+1|0]=Wb>>>12&63|128,4}else if(57216==(-128&Wb))break a;q[1906]=25,a=-1}else a=1;return a}return o[0|a]=Wb,1}(a,pa):0}function pa(a,pa,ta,ua){a:{if(!(20<pa>>>0||9<(pa=pa+-9|0)>>>0)){c:switch(pa-1|0){default:return pa=q[ta>>2],q[ta>>2]=pa+4,void(q[a>>2]=q[pa>>2]);case 0:return pa=q[ta>>2],q[ta>>2]=pa+4,pa=q[pa>>2],q[a>>2]=pa,void(q[a+4>>2]=pa>>31);case 1:return pa=q[ta>>2],q[ta>>2]=pa+4,q[a>>2]=q[pa>>2],void(q[a+4>>2]=0);case 3:return pa=q[ta>>2],q[ta>>2]=pa+4,pa=p[pa>>1],q[a>>2]=pa,void(q[a+4>>2]=pa>>31);case 4:return pa=q[ta>>2],q[ta>>2]=pa+4,q[a>>2]=s[pa>>1],void(q[a+4>>2]=0);case 5:return pa=q[ta>>2],q[ta>>2]=pa+4,pa=o[0|pa],q[a>>2]=pa,void(q[a+4>>2]=pa>>31);case 6:return pa=q[ta>>2],q[ta>>2]=pa+4,q[a>>2]=r[0|pa],void(q[a+4>>2]=0);case 2:case 7:break a;case 8:break c}n[ua](a,ta)}return}pa=q[ta>>2]+7&-8,q[ta>>2]=pa+8,ta=q[pa+4>>2],q[a>>2]=q[pa>>2],q[a+4>>2]=ta}function qa(a){var pa=0,ta=0,ua=0;if(ga(o[q[a>>2]]))for(;pa=q[a>>2],ua=o[0|pa],q[a>>2]=pa+1,ta=(w(ta,10)+ua|0)-48|0,ga(o[pa+1|0]););return ta}function ra(a,va,wa,xa,ya){var za,Aa=0,Ba=0;q[204+(L=za=L-208|0)>>2]=wa,ba(160+za|(wa=0),0,40),q[200+za>>2]=q[204+za>>2],(0|ia(0,va,200+za|0,80+za|0,160+za|0,xa,ya))<0||(wa=0<=q[a+76>>2]?1:wa,Aa=q[a>>2],o[a+74|0]<=0&&(q[a>>2]=-33&Aa),Ba=32&Aa,q[a+48>>2]?ia(a,va,200+za|0,80+za|0,160+za|0,xa,ya):(q[a+48>>2]=80,q[a+16>>2]=80+za,q[a+28>>2]=za,q[a+20>>2]=za,Aa=q[a+44>>2],ia(a,va,200+(q[a+44>>2]=za)|0,80+za|0,160+za|0,xa,ya),Aa&&(n[q[a+36>>2]](a,0,0),q[a+48>>2]=0,q[a+44>>2]=Aa,q[a+28>>2]=0,q[a+16>>2]=0,q[a+20>>2]=0)),q[a>>2]=q[a>>2]|Ba),L=208+za|0}function sa(a,va,wa){var xa,ya=0;fa(8+(L=xa=L-160|0)|0,2536,144),q[52+xa>>2]=a,ya=(ya=-2-(q[28+xa>>2]=a)|0)>>>0<256?ya:256,a=a+(q[56+xa>>2]=ya)|0,q[36+xa>>2]=a,q[24+xa>>2]=a,ra(8+xa|0,va,wa,15,16),ya&&(a=q[28+xa>>2],o[a-((0|a)==q[24+xa>>2])|0]=0),L=160+xa|0}function ta(a){var va=0,wa=0,Ca=0,Da=0,Ea=0,Fa=0,Ga=0,Ha=x(0),Ia=0,Ja=0,Ka=0,La=0,Ma=0,Na=0,Oa=0,Pa=x(0),Qa=0,Ra=x(0),Sa=0,Ta=0,Ua=x(0),Va=x(0),Wa=x(0),Xa=0,Ya=0,Za=0,_a=0,$a=0,ab=0,bb=0;if(q[a+256>>2]&&(va=q[a+200>>2]<<2,fa(q[a+280>>2],q[a+264>>2],va),fa(q[a+284>>2],q[a+268>>2],va),fa(q[a+288>>2],q[a+276>>2],va)),!((0|(Ea=q[a+292>>2]))<1)){for(Ia=(Ga=q[a+296>>2])+w(Ea,40)|0,Ca=q[a+300>>2],va=Ga;Wa=u[Ca>>2],(Ra=u[va+32>>2])==(Ha=(wa=q[va+12>>2])?(Ua=u[va>>2],Ha=x(Wa-Ua),Wa=u[va+8>>2],Pa=x(Ha/Wa),Ha=x(C(Pa)),Da=x(y(Ha))<x(2147483648)?~~Ha:-2147483648,x(Ua+x(Wa*x(Pa-x(0|Da))))):(Pa=u[va>>2],Ha=u[va+4>>2],Wa<Pa?Pa:Ha<Wa?Ha:Wa))?q[va+36>>2]=0:(u[va+32>>2]=Ha,q[va+36>>2]=1),wa||(u[Ca>>2]=Ha),Ca=Ca+4|0,(va=va+40|0)>>>0<Ia>>>0;);if(!((0|Ea)<1))for(Qa=q[a+308>>2],Oa=q[a+376>>2];;){e:if(!((0|(wa=q[Ga+28>>2]))<1))if(Ja=(va=Qa+w(q[Ga+24>>2],28)|0)+w(wa,28)|0,Wa=u[Ga+20>>2],Va=u[Ga+16>>2],Ra=u[Ga+32>>2],Oa)for(;;){Ua=x(Ea=0);l:{m:{n:{if((0|(Fa=q[va>>2]))<1)Ca=Da=0;else if(wa=q[va+4>>2],Pa=u[wa>>2],Ha=x(Pa-Va),1!=(0|Fa)){if(Ra<Ha){Da=1,Ca=0;break m}if(Da=0,Ra<x(Va+Pa))Ca=0;else{if(Ca=1,Ha=u[wa+4>>2],!(Ra<x(Va+Ha)))for(;;){if((0|Fa)==(0|(Ca=Ca+1|0)))break n;if(Pa=Ha,Ha=u[wa+(Ca<<2)>>2],Ra<x(Va+Ha))break}x(Ha-Va)<Ra||(Ca=Ca+-1|0,(Ha=x(Ha-Pa))<Wa||(Ua=x(x(Ra-Pa)/Ha)))}}else Da=Ra<x(Va+Pa)^1|Ha<Ra^1,Ca=0;if(Da)break m;if(Na=Fa=1,!q[va+16>>2])break m;break l}Ca=Fa+-1|0,Da=1}Na=(Fa=(Ha=u[va+12>>2])!=Ua)&(Ua==x(0)|Ha==x(0))|q[va+8>>2]!=(0|Ca),Ea=Da}if(q[va+20>>2]=Na,q[va+24>>2]=Fa,u[va+12>>2]=Ua,q[va+16>>2]=Ea,q[va+8>>2]=Ca,!((va=va+28|0)>>>0<Ja>>>0))break}else{if(!q[Ga+36>>2])for(;;)if(q[va+20>>2]=0,!((va=va+28|(q[va+24>>2]=0))>>>0<Ja>>>0))break e;for(;;){Ua=x(Ea=0);f:{g:{h:{i:if(!(((Da=0)|(Ca=q[(wa=va)>>2]))<1)){if(La=q[va+4>>2],Pa=u[La>>2],Ha=x(Pa-Va),1!=(0|Ca)){if(!(Ra<Ha)){if(Da=0,Ra<x(Va+Pa))break i;Fa=1;j:if(Ha=u[La+4>>2],!(Ra<x(Va+Ha))){for(Ca=Ca+-1|0;Pa=Ha,(0|Ca)!=(0|Fa);)if(Ha=u[La+((Fa=Fa+1|0)<<2)>>2],Ra<x(Va+Ha))break j;Da=1;break g}if(Da=0,x(Ha-Va)<Ra){Ca=Fa;break h}if(Ca=Fa+-1|0,(Ha=x(Ha-Pa))<Wa)break h;Ua=x(x(Ra-Pa)/Ha);break h}Da=1,Ca=0;break g}Da=Ra<x(Va+Pa)^1|Ha<Ra^1}Ca=0}if(!Da&&(La=Fa=1,q[va+16>>2]))break f}Ea=Da,La=(Fa=(Ha=u[va+12>>2])!=Ua)&(Ua==x(0)|Ha==x(0))|q[va+8>>2]!=(0|Ca)}if(q[wa+20>>2]=La,q[va+24>>2]=Fa,u[va+12>>2]=Ua,q[va+16>>2]=Ea,q[va+8>>2]=Ca,!((va=va+28|0)>>>0<Ja>>>0))break}}if(!((Ga=Ga+40|0)>>>0<Ia>>>0))break}}if(1<=(0|(va=q[a+312>>2])))for(La=(Ka=q[a+316>>2])+w(va,36)|0,Ia=q[a+376>>2];;){$a=(wa=q[Ka+12>>2])+((Ga=q[Ka>>2])<<2)|0,Sa=q[a+308>>2],va=wa;p:{if(!(Ga=((Fa=Da=Ea=0)|Ga)<1))for(;;){if(Ca=Sa+w(q[va>>2],28)|0,q[Ca+16>>2]){Na=1,Ta=_a=0;break p}if(Ea=Ea||q[Ca+24>>2],Fa=Fa||q[Ca+20>>2],Da=(u[Ca+12>>2]!=x(0))+Da|0,!((va=va+4|0)>>>0<$a>>>0))break}if(Na=0,(Ta=Ia?1:Fa)|(_a=Ia?1:Ea)&&(Za=1<<Da,q[Ka+8>>2]=Za,31!=(0|Da))){for(Ea=q[Ka+20>>2],Ca=(va=q[Ka+16>>2])+(Fa=Za<<2)|0,Xa=ba(Ma=va,0,4+((Ja=-1^va)+((va=va+4|0)>>>0<Ca>>>0?Ca:va)|0)&-4),Fa=Ea+Fa|0,va=Ea;q[va>>2]=1065353216,(va=va+4|0)>>>0<Fa>>>0;);if(!Ga)if(Fa=Ga=1,Da)for(;;){if(Ma=Sa+w(q[wa>>2],28)|0,Da=q[Ma+8>>2],Ja=w(Da,Ga),va=0,(Ha=u[Ma+12>>2])==x(0))for(;q[(Da=Xa+(va<<2)|0)>>2]=Ja+q[Da>>2],(0|Za)!=(0|(va=va+1|0)););else{for(q[Xa>>2]=Ja+q[Xa>>2],u[Ea>>2]=x(x(1)-Ha)*u[Ea>>2],Qa=w(Da+(va=1)|0,Ga);Ha=u[Ma+12>>2],Ca=va&Fa,q[(Da=(Oa=va<<2)+Xa|0)>>2]=q[Da>>2]+(Ca?Qa:Ja),u[(Da=Ea+Oa|0)>>2]=(Ca?Ha:x(x(1)-Ha))*u[Da>>2],(0|Za)!=(0|(va=va+1|0)););Fa<<=1}if(Ga=w(q[Ma>>2],Ga),!((wa=wa+4|0)>>>0<$a>>>0))break}else for(;;){if(Ca=Sa+w(q[wa>>2],28)|0,Fa=w(q[Ca+8>>2],Ga),va=0,(Ha=u[Ca+12>>2])==x(0))for(;q[(Da=Xa+(va<<2)|0)>>2]=Fa+q[Da>>2],(0|Za)!=(0|(va=va+1|0)););else q[Xa>>2]=Fa+q[Xa>>2],u[Ea>>2]=x(x(1)-Ha)*u[Ea>>2];if(Ga=w(q[Ca>>2],Ga),!((wa=wa+4|0)>>>0<$a>>>0))break}}}if(q[Ka+32>>2]=Na,q[Ka+24>>2]=Ta,q[Ka+28>>2]=_a,!((Ka=Ka+36|0)>>>0<La>>>0))break}if(!((0|(Da=q[a+4>>2]))<=0)){for(wa=(va=q[a+52>>2])+(Da<<2)|0;Ha=u[va>>2],u[va>>2]=Ha<x(0)?x(0):x(A(Ha,x(1))),(va=va+4|0)>>>0<wa>>>0;);if(!((0|Da)<1)){for(Ga=(va=q[a+8>>2])+(Da<<3)|0,Ja=q[a+316>>2],Ea=q[a+40>>2],Ca=Fa=q[a+36>>2];Da=0,q[va+4>>2]&&(wa=q[va>>2],!q[Fa+(wa<<2)>>2]&&-1!=(0|wa)||(Da=!q[32+(Ja+w(q[Ea>>2],36)|0)>>2])),q[Ca>>2]=Da,Ea=Ea+4|0,Ca=Ca+4|0,(va=va+8|0)>>>0<Ga>>>0;);if(!((0|(Ia=q[a+4>>2]))<1))for(La=q[a>>2],Qa=q[La+724>>2],wa=q[a+40>>2],Fa=Ga=0;;){if(Ma=Ja+w(q[wa>>2],36)|0,(q[Ma+28>>2]||q[Ma+24>>2])&&(q[(Ea=Ga<<2)+q[a+16>>2]>>2]=q[Ma+8>>2],q[Ma+24>>2]&&!((0|(Da=q[Ma+8>>2]))<1)))for(Oa=(va=q[Ma+16>>2])+(Da<<2)|0,Ea=q[Ea+Qa>>2],Ca=q[a+28>>2]+(Fa<<2)|0,Da=q[La+936>>2];q[Ca>>2]=q[Da+(Ea+q[va>>2]<<2)>>2],Ca=Ca+4|0,(va=va+4|0)>>>0<Oa>>>0;);if(q[Ma+28>>2]&&!((0|(Da=q[Ma+8>>2]))<1))for(Da=(va=q[Ma+20>>2])+(Da<<2)|0,Ca=q[a+20>>2]+(Fa<<2)|0;q[Ca>>2]=q[va>>2],Ca=Ca+4|0,(va=va+4|0)>>>0<Da>>>0;);if(wa=wa+4|0,Fa=q[Ma+4>>2]+Fa|0,(0|Ia)==(0|(Ga=Ga+1|0)))break}}}if(n[q[1644]](a),ab=q[a+316>>2],1<=(0|(wa=q[a+56>>2]))){for(Ia=(va=q[a+60>>2])+(wa<<5)|0,La=q[a+176>>2],Qa=q[a+172>>2],Oa=q[a+36>>2],Da=q[a+180>>2],Ea=Ca=q[a+168>>2];;){Fa=Ea,wa=0,q[va+28>>2]&&(-1!=(0|(Ga=q[va>>2]))&&(wa=0,!q[Oa+(Ga<<2)>>2])||-1!=(0|(Ga=q[va+4>>2]))&&(wa=0,!q[Ca+(Ga<<2)>>2])||(wa=!q[32+(w(q[Da>>2],36)+ab|0)>>2])),q[Fa>>2]=wa;x:if((Fa=q[va+8>>2])>>>0<=1){if(Fa-1){q[Qa+(q[va+12>>2]<<2)>>2]=wa;break x}q[La+(q[va+12>>2]<<2)>>2]=wa}else Y(4,1473,0);if(Da=Da+4|0,Ea=Ea+4|0,!((va=va+32|0)>>>0<Ia>>>0))break}ab=q[a+316>>2]}if(Ga=q[a>>2],1<=(0|(Ia=q[a- -64>>2]))){for(La=q[Ga+988>>2],Qa=q[Ga+784>>2],Na=q[a+184>>2],Ka=wa=0;;){if(Ja=w(q[Na>>2],36)+ab|0,(q[Ja+28>>2]||q[Ja+24>>2])&&(q[(Ea=wa<<2)+q[a+84>>2]>>2]=q[Ja+8>>2],q[Ja+24>>2]&&!((0|(Da=q[Ja+8>>2]))<1)))for(Oa=(va=q[Ja+16>>2])+(Da<<2)|0,Fa=q[Ea+Qa>>2],Ca=(Da=Ka<<2)+q[a+100>>2]|0,Ea=Da+q[a+96>>2]|0;Da=Fa+q[va>>2]<<2,q[Ca>>2]=La+(q[Da+q[Ga+944>>2]>>2]<<2),q[Ea>>2]=q[Da+q[Ga+940>>2]>>2],Ea=Ea+4|0,Ca=Ca+4|0,(va=va+4|0)>>>0<Oa>>>0;);if(q[Ja+28>>2]&&!((0|(Da=q[Ja+8>>2]))<1))for(Da=(va=q[Ja+20>>2])+(Da<<2)|0,Ca=q[a+88>>2]+(Ka<<2)|0;q[Ca>>2]=q[va>>2],Ca=Ca+4|0,(va=va+4|0)>>>0<Da>>>0;);if(Na=Na+4|0,Ka=q[Ja+4>>2]+Ka|0,(0|Ia)==(0|(wa=wa+1|0)))break}ab=q[a+316>>2],Ga=q[a>>2]}if(1<=(0|(Za=q[a+72>>2])))for($a=q[a+76>>2],Sa=q[Ga+808>>2],_a=q[a+188>>2],Ta=Ka=0;;){if(Ya=w(q[_a>>2],36)+ab|0,(q[Ya+28>>2]||q[Ya+24>>2])&&(q[(wa=Ka<<2)+q[a+116>>2]>>2]=q[Ya+8>>2],q[Ya+24>>2])){if(va=q[Ya+16>>2],Xa=q[wa+Sa>>2],1<=(0|(wa=q[Ya+8>>2])))for(Ma=va+(wa<<2)|0,Ca=(wa=Ta<<2)+q[a+132>>2]|0,Ea=wa+q[a+136>>2]|0,Da=wa+q[a+140>>2]|0,Fa=wa+q[a+144>>2]|0,Na=wa+q[a+128>>2]|0,Ja=q[Ga+948>>2],Ia=q[Ga+964>>2],La=q[Ga+960>>2],Qa=q[Ga+956>>2],Oa=q[Ga+952>>2],wa=va;bb=Xa+q[wa>>2]<<2,q[Ca>>2]=q[bb+Oa>>2],q[Ea>>2]=q[Qa+bb>>2],q[Da>>2]=q[La+bb>>2],q[Fa>>2]=q[Ia+bb>>2],q[Na>>2]=q[Ja+bb>>2],Na=Na+4|0,Fa=Fa+4|0,Da=Da+4|0,Ea=Ea+4|0,Ca=Ca+4|0,(wa=wa+4|0)>>>0<Ma>>>0;);wa=$a+(Ka<<5)|0,va=Xa+q[va>>2]<<2,q[wa+24>>2]=q[va+q[Ga+968>>2]>>2],q[wa+28>>2]=q[va+q[Ga+972>>2]>>2]}if(q[Ya+28>>2]&&!((0|(wa=q[Ya+8>>2]))<1))for(wa=(va=q[Ya+20>>2])+(wa<<2)|0,Ca=q[a+120>>2]+(Ta<<2)|0;q[Ca>>2]=q[va>>2],Ca=Ca+4|0,(va=va+4|0)>>>0<wa>>>0;);if(_a=_a+4|0,Ta=q[Ya+4>>2]+Ta|0,(0|Za)==(0|(Ka=Ka+1|0)))break}if(n[q[1642]](a),n[q[1645]](a),!((0|(wa=q[a+200>>2]))<1)){for(Oa=(va=q[a+204>>2])+(wa<<4)|0,Ga=q[a+168>>2],Fa=q[a+36>>2],Ja=q[a+316>>2],Ea=q[a+252>>2],Ca=q[a+248>>2];Da=0,q[va+8>>2]&&(wa=q[va>>2],!q[Fa+(wa<<2)>>2]&&-1!=(0|wa)||(wa=q[va+4>>2],!q[Ga+(wa<<2)>>2]&&-1!=(0|wa)||(Da=!q[32+(Ja+w(q[Ea>>2],36)|0)>>2]))),q[Ca>>2]=Da,Ea=Ea+4|0,Ca=Ca+4|0,(va=va+16|0)>>>0<Oa>>>0;);if(!((0|(La=q[a+200>>2]))<1))for(Ma=q[a>>2],Qa=q[Ma+988>>2],Oa=q[Ma+844>>2],wa=q[a+252>>2],Na=Ka=0;;){if(Sa=Ja+w(q[wa>>2],36)|0,(q[Sa+28>>2]||q[Sa+24>>2])&&(q[(Ea=Ka<<2)+q[a+212>>2]>>2]=q[Sa+8>>2],q[Sa+24>>2]&&!((0|(Da=q[Sa+8>>2]))<1)))for(Ga=(va=q[Sa+16>>2])+(Da<<2)|0,Fa=q[Ea+Oa>>2],Ca=(Da=Na<<2)+q[a+232>>2]|0,Ea=Da+q[a+224>>2]|0,Da=Da+q[a+228>>2]|0;Ia=Fa+q[va>>2]<<2,q[Ca>>2]=Qa+(q[Ia+q[Ma+984>>2]>>2]<<2),q[Ea>>2]=q[Ia+q[Ma+976>>2]>>2],q[Da>>2]=q[Ia+q[Ma+980>>2]>>2],Da=Da+4|0,Ea=Ea+4|0,Ca=Ca+4|0,(va=va+4|0)>>>0<Ga>>>0;);if(q[Sa+28>>2]&&!((0|(Da=q[Sa+8>>2]))<1))for(Da=(va=q[Sa+20>>2])+(Da<<2)|0,Ca=q[a+216>>2]+(Na<<2)|0;q[Ca>>2]=q[va>>2],Ca=Ca+4|0,(va=va+4|0)>>>0<Da>>>0;);if(wa=wa+4|0,Na=q[Sa+4>>2]+Na|0,(0|La)==(0|(Ka=Ka+1|0)))break}}if(n[q[1643]](a),function(a){var me,fe=0,ge=0,he=0,ie=0,je=0,ke=0,le=0,ne=0,oe=0,pe=0,qe=0,re=0;if(1<=(0|(me=q[a+340>>2])))for(oe=q[a+316>>2],ne=q[a>>2],pe=q[ne+1072>>2],je=q[a+372>>2];;){if(he=w(q[je>>2],36)+oe|0,(q[he+28>>2]||q[he+24>>2])&&(q[(fe=ke<<2)+q[a+352>>2]>>2]=q[he+8>>2],q[he+24>>2]&&!((0|(ie=q[he+8>>2]))<1)))for(ge=q[he+16>>2],ie=ge+(ie<<2)|0,qe=q[fe+pe>>2],fe=q[a+364>>2]+(le<<2)|0,re=q[ne+1104>>2];q[fe>>2]=q[(q[ge>>2]+qe<<2)+re>>2],fe=fe+4|0,(ge=ge+4|0)>>>0<ie>>>0;);if(q[he+28>>2]&&!((0|(fe=q[he+8>>2]))<1))for(ge=q[he+20>>2],ie=ge+(fe<<2)|0,fe=q[a+356>>2]+(le<<2)|0;q[fe>>2]=q[ge>>2],fe=fe+4|0,(ge=ge+4|0)>>>0<ie>>>0;);if(je=je+4|0,le=q[he+4>>2]+le|0,(0|me)==(0|(ke=ke+1|0)))break}}(a),n[q[1646]](a),wa=q[a+48>>2],1<=(0|(va=q[a+4>>2])))for(Ga=(Ea=q[a+8>>2])+(va<<3)|0,Da=q[a+52>>2],Ca=q[a+36>>2],va=wa;q[Ca>>2]&&(Ha=u[Da>>2],u[va>>2]=Ha,-1!=(0|(Fa=q[Ea>>2]))&&(u[va>>2]=Ha*u[(Fa<<2)+wa>>2])),va=va+4|0,Da=Da+4|0,Ca=Ca+4|0,(Ea=Ea+8|0)>>>0<Ga>>>0;);if(1<=(0|(Da=q[a+56>>2]))){for(wa=a+56|0,va=q[a+60>>2],Ca=q[a+168>>2],Ea=0;q[Ca>>2]&&n[q[va+16>>2]](va,Ea,wa),Ca=Ca+4|0,va=va+32|0,(0|Da)!=(0|(Ea=Ea+1|0)););wa=q[a+48>>2]}if(1<=(0|(Da=q[a+200>>2])))for(La=(va=q[a+204>>2])+(Da<<4)|0,Qa=q[a+192>>2],Oa=q[a+60>>2],Ea=q[a+248>>2],Ca=q[a+276>>2],Da=q[a+272>>2];q[Ea>>2]&&(-1!=(0|(Fa=q[va>>2]))&&(u[Ca>>2]=u[(Fa<<2)+wa>>2]*u[Ca>>2]),-1!=(0|(Fa=q[va+4>>2]))&&(u[Ca>>2]=u[Qa+(Fa<<2)>>2]*u[Ca>>2],Ga=Oa+(Fa<<5)|0,Fa=q[Da>>2],n[q[Ga+20>>2]](q[Ga+24>>2],Fa,Fa,q[va+12>>2]))),Da=Da+4|0,Ca=Ca+4|0,Ea=Ea+4|0,(va=va+16|0)>>>0<La>>>0;);if(!function(a){var Dd=0,Ed=0,Fd=0,Gd=0,Hd=x(0),Id=x(0),Jd=x(0),Kd=x(0),Ld=x(0),Md=0,Nd=0,Od=0,Pd=0,Qd=0,Rd=x(0),Sd=0,Td=0,Ud=x(0),Vd=0;if(1<=(0|(Fd=q[a+340>>2])))for(Dd=q[a+344>>2],Vd=Dd+w(Fd,24)|0,Fd=q[a+272>>2];;){if((a=0)<(0|(Md=q[Dd+8>>2])))for(Nd=q[Fd+(q[Dd+4>>2]<<2)>>2],Od=q[Fd+(q[Dd>>2]<<2)>>2],Hd=u[Dd+20>>2],Pd=q[Dd+16>>2],Qd=q[Dd+12>>2];Rd=u[((Ed=1|a)<<2)+Qd>>2],Gd=s[(a<<1)+Pd>>1]<<3&262136,Id=u[(Sd=(4|Gd)+Od|0)>>2],Ed=s[(Ed<<1)+Pd>>1]<<3&262136,Jd=u[(Td=(4|Ed)+Nd|0)>>2],Kd=u[(Gd=Gd+Od|0)>>2],Ud=u[(a<<2)+Qd>>2],Ld=u[(Ed=Ed+Nd|0)>>2],u[Gd>>2]=Kd+x(Hd*x(Ud*x(Ld-Kd))),u[Sd>>2]=Id+x(Hd*x(Ud*x(Jd-Id))),u[Ed>>2]=Ld+x(Hd*x(Rd*x(Kd-Ld))),u[Td>>2]=Jd+x(Hd*x(Rd*x(Id-Jd))),(0|(a=a+2|0))<(0|Md););if(!((Dd=Dd+24|0)>>>0<Vd>>>0))break}}(a),n[q[1647]](a),Na=q[a+248>>2],Ka=q[a+268>>2],1<=(0|(Ja=q[a+320>>2]))){for(La=(Da=q[a+324>>2])+w(Ja,28)|0,Qa=q[a+44>>2],Oa=q[a+36>>2],wa=Da;;){if(1<=(0|(Ca=q[wa+4>>2])))for(Ga=wa+20|0,Fa=q[wa+12>>2],va=0;Ia=q[(Ea=Fa+(va<<4)|0)+4>>2]<<2,Ea=1==q[(Ma=Ea)>>2],q[Ma+12>>2]=q[(q[Ia+(Ea?Oa:Na)>>2]?Ia+(Ea?Qa:Ka)|0:Ga)>>2],(0|Ca)!=(0|(va=va+1|0)););if(!((wa=wa+28|0)>>>0<La>>>0))break}for(Qa=q[a+264>>2],Ta=0;;){if(Ia=Da+w(Ta,28)|0,!(q[(La=Ia)+24>>2]<1)){for(Ea=q[a+328>>2],va=0;q[Ea+(va<<2)>>2]=-1,(0|(va=va+1|0))<(0|(wa=q[La+24>>2])););if(!((0|wa)<1))for(wa=q[a+336>>2],va=0;q[wa+(va<<2)>>2]=-1,(0|(va=va+1|0))<q[La+24>>2];);}if(!(q[Ia+4>>2]<1)){for(Oa=q[a+332>>2],va=0;q[Oa+(va<<2)>>2]=-1,(0|(va=va+1|0))<(0|(wa=q[Ia+4>>2])););if(!((0|wa)<1))for(Ca=q[Ia+12>>2],Ga=q[a+336>>2],va=0;Fa=q[12+(Ca+(va<<4)|0)>>2]-q[Ia+20>>2]<<2,wa=-1!=(0|(wa=q[(Ea=Fa+Ga|0)>>2]))?Oa+(wa<<2)|0:Fa+q[a+328>>2]|0,q[wa>>2]=va,(0|(va=(q[Ea>>2]=va)+1|0))<q[Ia+4>>2];);}if(1<=(0|(Ea=q[La+24>>2])))for(Ca=q[Ia+8>>2],Oa=q[a+328>>2],Ga=0;;){if(-1!=(0|(va=q[Oa+(Ga<<2)>>2]))){for(Fa=q[a+332>>2],Ea=q[Ia+12>>2];Ca=(wa=1!=q[(wa=Ea+(va<<4)|0)>>2]?(q[Qa+(q[wa+4>>2]<<2)>>2]=Ca,1):(wa=Da+w(q[wa+8>>2],28)|0,q[wa+8>>2]=Ca,q[wa>>2]))+Ca|0,(0|va)<(0|(wa=q[Fa+(va<<2)>>2]))&&-1!=(0|(va=wa)););Ea=q[La+24>>2]}if(!((0|(Ga=Ga+1|0))<(0|Ea)))break}if((0|Ja)==(0|(Ta=Ta+1|0)))break}}Fa=q[a+200>>2];M:{if(q[a+376>>2]){if(va=0,((q[a+256>>2]=0)|Fa)<1)break M;for(;;){if(Ca=62,Ea=q[a+260>>2]+va|0,!q[(wa=va<<2)+Na>>2]|u[wa+q[a+276>>2]>>2]==x(0)||(Ca=63),o[0|Ea]=Ca,(0|Fa)==(0|(va=va+1|0)))break M;Na=q[a+248>>2]}}if(!q[a+256>>2]){if((0|Fa)<1)break M;for(va=0;;){if(!q[(wa=va<<2)+Na>>2]|u[wa+q[a+276>>2]>>2]==x(0)?(wa=q[a+260>>2]+va|0,o[0|wa]=254&r[0|wa]):(wa=q[a+260>>2]+va|0,o[0|wa]=1|r[0|wa]),(0|Fa)==(0|(va=va+1|0)))break M;Na=q[a+248>>2]}}if(!(((q[a+256>>2]=0)|Fa)<1))for(Ca=0;;){if(Ha=u[(Ea=Ca<<2)+q[a+276>>2]>>2],Da=q[Ea+Na>>2],va=Ha!=x(0)&0!=(0|Da),wa=q[a+260>>2]+Ca|0,va=(0|va)==(1&o[0|wa])?va:2|va,va=Ha!=u[Ea+q[a+288>>2]>>2]?4|va:va,va=q[Ea+Ka>>2]==q[Ea+q[a+284>>2]>>2]?va:8|va,va=q[Ea+q[a+264>>2]>>2]==q[Ea+q[a+280>>2]>>2]?va:16|va,o[0|wa]=Da?32|va:va,(0|Fa)==(0|(Ca=Ca+1|0)))break M;Ka=q[a+268>>2],Na=q[a+248>>2]}}q[a+376>>2]=0}function ua(a,cb,db){var eb=0,fb=0,gb=0,hb=0,ib=0,jb=0,kb=0,lb=0,mb=0,nb=0,ob=0;if(q[cb>>2]=384,fb=q[a>>2],1<=(0|(hb=q[fb>>2]))){for(jb=q[a+16>>2],kb=q[a+296>>2];gb=(1<<q[kb+(q[jb+(eb<<2)>>2]<<2)>>2])+gb|0,(0|hb)!=(0|(eb=eb+1|0)););eb=gb<<2}if(q[cb+4>>2]=hb<<3,q[cb+8>>2]=q[fb>>2]<<2,q[cb+12>>2]=q[fb>>2]<<2,q[cb+16>>2]=q[fb>>2]<<2,q[cb+20>>2]=q[fb>>2]<<2,q[cb+24>>2]=q[fb>>2]<<2,gb=q[fb>>2],q[cb+40>>2]=eb,q[cb+36>>2]=eb,q[cb+32>>2]=eb,q[cb+28>>2]=gb<<2,1<=((gb=eb=0)|(kb=q[fb+8>>2]))){for(mb=q[a+296>>2],nb=q[a+88>>2],lb=q[a+76>>2],hb=0;eb=(0|eb)<(0|(jb=q[(ob=gb<<2)+nb>>2]))?jb:eb,ib=(15+(jb<<3)&-16)+ib|0,hb=(1<<q[mb+(q[lb+ob>>2]<<2)>>2])+hb|0,(0|kb)!=(0|(gb=gb+1|0)););gb=eb<<3,eb=hb<<2}if(q[cb+44>>2]=q[fb+4>>2]<<5,q[cb+48>>2]=w(q[fb+8>>2],24),q[cb+52>>2]=q[fb+12>>2]<<5,q[cb+56>>2]=q[fb+4>>2]<<2,q[cb+60>>2]=q[fb+8>>2]<<2,q[cb+64>>2]=q[fb+12>>2]<<2,q[cb+68>>2]=q[fb+4>>2]<<2,hb=q[fb+4>>2],q[cb+76>>2]=ib,q[cb+72>>2]=hb<<2,q[cb+80>>2]=q[fb+8>>2]<<2,hb=q[fb+8>>2],q[cb+104>>2]=gb,q[cb+100>>2]=eb,q[cb+96>>2]=eb,q[cb+92>>2]=eb,q[cb+88>>2]=eb,q[cb+84>>2]=hb<<2,1<=((eb=ib=0)|(hb=q[fb+12>>2]))){for(jb=q[a+296>>2],kb=q[a+100>>2],gb=0;gb=(1<<q[jb+(q[kb+(eb<<2)>>2]<<2)>>2])+gb|0,(0|hb)!=(0|(eb=eb+1|0)););eb=gb<<2}if(q[cb+108>>2]=hb<<2,gb=q[fb+12>>2],q[cb+156>>2]=eb,q[cb+152>>2]=eb,q[cb+148>>2]=eb,q[cb+144>>2]=eb,q[cb+140>>2]=eb,q[cb+136>>2]=eb,q[cb+132>>2]=eb,q[cb+128>>2]=eb,q[cb+124>>2]=eb,q[cb+120>>2]=eb,q[cb+116>>2]=eb,q[cb+112>>2]=gb<<2,1<=((gb=eb=0)|(jb=q[fb+16>>2]))){for(mb=q[a+136>>2],nb=q[a+296>>2],lb=q[a+172>>2],hb=0;eb=(0|eb)<(0|(kb=q[(ob=gb<<2)+lb>>2]))?kb:eb,ib=(15+(kb<<3)&-16)+ib|0,hb=(1<<q[nb+(q[mb+ob>>2]<<2)>>2])+hb|0,(0|jb)!=(0|(gb=gb+1|0)););gb=eb<<3,eb=hb<<2}if(q[cb+160>>2]=jb<<4,q[cb+164>>2]=q[fb+16>>2]<<2,q[cb+168>>2]=q[fb+16>>2],q[cb+172>>2]=q[fb+16>>2]<<2,q[cb+176>>2]=q[fb+16>>2]<<2,hb=q[fb+16>>2],q[cb+184>>2]=ib,q[cb+180>>2]=hb<<2,q[cb+188>>2]=q[fb+16>>2]<<2,q[cb+192>>2]=q[fb+16>>2]<<2,q[cb+196>>2]=q[fb+16>>2]<<2,q[cb+200>>2]=q[fb+16>>2]<<2,q[cb+204>>2]=q[fb+16>>2]<<2,hb=q[fb+16>>2],q[cb+236>>2]=gb,q[cb+232>>2]=eb,q[cb+228>>2]=eb,q[cb+224>>2]=eb,q[cb+220>>2]=eb,q[cb+216>>2]=eb,q[cb+212>>2]=eb,q[cb+208>>2]=hb<<2,q[cb+240>>2]=w(q[fb+20>>2],40),q[cb+244>>2]=q[fb+20>>2]<<2,q[cb+248>>2]=w(q[fb+52>>2],28),1<=((eb=hb=0)|(ib=q[fb+48>>2]))){for(jb=q[a+296>>2],gb=0;gb=(1<<q[jb+(eb<<2)>>2])+gb|0,(0|ib)!=(0|(eb=eb+1|0)););eb=gb<<2}if(q[cb+260>>2]=eb,q[cb+256>>2]=eb,q[cb+252>>2]=w(ib,36),q[cb+264>>2]=w(q[fb+72>>2],28),1<=((eb=0)|(jb=q[fb+72>>2]))){for(kb=q[a+340>>2],mb=q[a+336>>2],nb=q[a+328>>2],gb=0;gb=(0|(lb=q[(ib=hb<<2)+mb>>2]-q[ib+kb>>2]|0))<(0|gb)?gb:lb+1|0,eb=(0|eb)<(0|(ib=q[ib+nb>>2]))?ib:eb,(0|jb)!=(0|(hb=hb+1|0)););hb=gb<<2,eb<<=2}if(gb=q[fb+76>>2],q[cb+280>>2]=hb,q[cb+276>>2]=eb,q[cb+272>>2]=hb,q[cb+268>>2]=gb<<4,1<=((eb=0)|(hb=q[fb+80>>2]))){for(ib=q[a+364>>2],a=q[a+296>>2],gb=0;gb=(1<<q[a+(q[ib+(eb<<2)>>2]<<2)>>2])+gb|0,(0|hb)!=(0|(eb=eb+1|0)););eb=gb<<2}for(q[cb+284>>2]=w(hb,24),q[cb+288>>2]=q[fb+80>>2]<<2,a=q[fb+80>>2],q[cb+304>>2]=eb,q[cb+300>>2]=eb,q[cb+296>>2]=eb,q[cb>>2]=0,q[cb+292>>2]=a<<2,eb=384,gb=1;eb=((fb=q[(a=(gb<<2)+cb|0)>>2])+15&-16)+(q[a>>2]=eb)|0,77!=(0|(gb=gb+1|0)););q[db>>2]=eb}function va(a,cb,db){cb|=0,db|=0;var pb;L=pb=L+-64|0;a:{if(a|=0)if(cb)if((cb+15&-16)==(0|cb)){if(cb=function(a,hh,ih){var lh,jh=0,kh=0,mh=0,nh=0,oh=0,ph=0,qh=0,rh=0,sh=0,th=0,uh=0,vh=0,wh=0,xh=0,yh=0,zh=0,Ah=0,Bh=x(0);if(ba(16+(L=lh=L-336|0)|0,0,308),ua(a+704|0,16+lh|0,12+lh|0),(kh=q[12+lh>>2])>>>0<=ih>>>0){if(hh=ba(hh,ih=0,kh),jh=hh+q[16+lh>>2]|0,q[jh+8>>2]=hh+q[20+lh>>2],q[jh+36>>2]=hh+q[24+lh>>2],q[jh+44>>2]=hh+q[28+lh>>2],q[jh+48>>2]=hh+q[32+lh>>2],q[jh+52>>2]=hh+q[36+lh>>2],q[jh+12>>2]=hh+q[40+lh>>2],q[jh+16>>2]=hh+q[44+lh>>2],q[jh+20>>2]=hh+q[48+lh>>2],q[jh+28>>2]=hh+q[52+lh>>2],q[jh+32>>2]=hh+q[56+lh>>2],kh=q[a+704>>2],q[jh+60>>2]=hh+q[60+lh>>2],ph=hh+q[64+lh>>2]|0,q[jh+68>>2]=ph,q[jh+76>>2]=hh+q[68+lh>>2],q[jh+168>>2]=hh+q[72+lh>>2],q[jh+172>>2]=hh+q[76+lh>>2],q[jh+176>>2]=hh+q[80+lh>>2],q[jh+192>>2]=hh+q[84+lh>>2],q[jh+196>>2]=hh+q[88+lh>>2],1<=(0|(kh=q[kh+8>>2])))for(mh=hh+q[92+lh>>2]|0,qh=q[a+792>>2];q[20+(ph+w(ih,24)|0)>>2]=mh,mh=(15+(q[qh+(ih<<2)>>2]<<3)&-16)+mh|0,(0|kh)!=(0|(ih=ih+1|0)););if(q[jh+80>>2]=hh+q[96+lh>>2],q[jh+84>>2]=hh+q[100+lh>>2],q[jh+88>>2]=hh+q[104+lh>>2],q[jh+96>>2]=hh+q[108+lh>>2],q[jh+100>>2]=hh+q[112+lh>>2],q[jh+104>>2]=hh+q[116+lh>>2],q[jh+108>>2]=hh+q[120+lh>>2],q[jh+112>>2]=hh+q[124+lh>>2],q[jh+116>>2]=hh+q[128+lh>>2],q[jh+120>>2]=hh+q[132+lh>>2],q[jh+128>>2]=hh+q[136+lh>>2],q[jh+132>>2]=hh+q[140+lh>>2],q[jh+136>>2]=hh+q[144+lh>>2],q[jh+140>>2]=hh+q[148+lh>>2],q[jh+144>>2]=hh+q[152+lh>>2],q[jh+148>>2]=hh+q[156+lh>>2],q[jh+152>>2]=hh+q[160+lh>>2],q[jh+156>>2]=hh+q[164+lh>>2],q[jh+160>>2]=hh+q[168+lh>>2],q[jh+164>>2]=hh+q[172+lh>>2],ih=q[a+704>>2],q[jh+204>>2]=hh+q[176+lh>>2],q[jh+248>>2]=hh+q[180+lh>>2],q[jh+260>>2]=hh+q[184+lh>>2],q[jh+264>>2]=hh+q[188+lh>>2],q[jh+268>>2]=hh+q[192+lh>>2],kh=hh+q[196+lh>>2]|0,q[jh+272>>2]=kh,!((0|(ph=q[ih+16>>2]))<1)&&(mh=hh+q[200+lh>>2]|0,q[kh>>2]=mh,(ih=1)!=(0|ph)))for(kh=0;mh=(15+(q[q[a+876>>2]+(kh<<2)>>2]<<3)&-16)+mh|0,q[q[jh+272>>2]+(ih<<2)>>2]=mh,(0|ph)!=(0|(ih=(kh=ih)+1|0)););if(q[jh+276>>2]=hh+q[204+lh>>2],q[jh+280>>2]=hh+q[208+lh>>2],q[jh+284>>2]=hh+q[212+lh>>2],q[jh+288>>2]=hh+q[216+lh>>2],q[jh+208>>2]=hh+q[220+lh>>2],q[jh+212>>2]=hh+q[224+lh>>2],q[jh+216>>2]=hh+q[228+lh>>2],q[jh+224>>2]=hh+q[232+lh>>2],q[jh+228>>2]=hh+q[236+lh>>2],q[jh+232>>2]=hh+q[240+lh>>2],q[jh+236>>2]=hh+q[244+lh>>2],q[jh+240>>2]=hh+q[248+lh>>2],q[jh+244>>2]=hh+q[252+lh>>2],ih=q[256+lh>>2],oh=hh+q[260+lh>>2]|0,q[jh+300>>2]=oh,rh=hh+ih|0,q[jh+296>>2]=rh,th=hh+q[264+lh>>2]|0,q[jh+308>>2]=th,ih=q[a+704>>2],kh=q[276+lh>>2],mh=q[272+lh>>2],qh=hh+q[268+lh>>2]|0,q[jh+316>>2]=qh,1<=(0|(ph=q[ih+48>>2])))for(mh=hh+mh|0,kh=hh+kh|0,nh=q[a+1e3>>2],ih=0;sh=qh+w(ih,36)|0,q[sh+20>>2]=kh,q[sh+16>>2]=mh,sh=1<<q[nh+(ih<<2)>>2]<<2,kh=sh+kh|0,mh=mh+sh|0,(0|ph)!=(0|(ih=ih+1|0)););if(ih=q[a+704>>2],kh=hh+q[280+lh>>2]|0,q[jh+324>>2]=kh,1<=(0|(ph=q[ih+72>>2])))for(mh=hh+q[284+lh>>2]|0,nh=q[a+1032>>2],ih=0;q[12+(kh+w(ih,28)|0)>>2]=mh,mh=(q[nh+(ih<<2)>>2]<<4)+mh|0,(0|ph)!=(0|(ih=ih+1|0)););if(q[jh+328>>2]=hh+q[288+lh>>2],q[jh+332>>2]=hh+q[292+lh>>2],q[jh+336>>2]=hh+q[296+lh>>2],q[jh+344>>2]=hh+q[300+lh>>2],q[jh+348>>2]=hh+q[304+lh>>2],q[jh+352>>2]=hh+q[308+lh>>2],q[jh+356>>2]=hh+q[312+lh>>2],q[jh+364>>2]=hh+q[316+lh>>2],ih=q[320+lh>>2],q[jh+376>>2]=1,q[jh+368>>2]=hh+ih,q[jh+380>>2]=1&o[q[a+708>>2]+20|0],ph=q[a+704>>2],kh=q[ph+20>>2],1<=(0|(q[jh+292>>2]=kh))){for(sh=q[a+932>>2],uh=q[a+928>>2],yh=q[a+924>>2],nh=q[a+916>>2],vh=q[a+920>>2],zh=q[a+908>>2],Ah=q[a+912>>2],mh=kh;hh=rh+w(mh=mh+-1|0,40)|0,wh=(ih=mh<<2)+Ah|0,q[hh>>2]=q[wh>>2],xh=ih+zh|0,q[hh+4>>2]=q[xh>>2],u[hh+8>>2]=u[xh>>2]-u[wh>>2],q[hh+12>>2]=q[ih+vh>>2],q[hh+32>>2]=q[ih+nh>>2],Bh=$b(x(q[ih+yh>>2])),u[hh+16>>2]=Bh,u[hh+20>>2]=Bh*x(1.5),q[hh+24>>2]=q[ih+uh>>2],ih=q[ih+sh>>2],q[hh+36>>2]=1,q[hh+28>>2]=ih,0<(0|mh););for(;q[(hh=(kh=kh+-1|0)<<2)+oh>>2]=q[hh+nh>>2],0<(0|kh););}if(ih=q[ph+52>>2],1<=(0|(q[jh+304>>2]=ih)))for(kh=q[a+1004>>2],mh=q[a+1012>>2],nh=q[a+1008>>2];hh=th+w(ih=ih+-1|0,28)|0,oh=ih<<2,q[hh>>2]=q[oh+nh>>2],oh=q[kh+oh>>2],q[hh+20>>2]=1,q[hh+24>>2]=1,q[hh+12>>2]=0,q[hh+4>>2]=mh+(oh<<2),0<(0|ih););if(ih=q[ph+48>>2],1<=(0|(q[jh+312>>2]=ih)))for(kh=q[a+996>>2],mh=q[a+992>>2],nh=q[a+1e3>>2];hh=qh+w(ih=ih+-1|0,36)|0,rh=q[(oh=ih<<2)+nh>>2],q[hh>>2]=rh,q[hh+4>>2]=1<<rh,oh=q[kh+oh>>2],q[hh+24>>2]=1,q[hh+28>>2]=1,q[hh+12>>2]=mh+(oh<<2),0<(0|ih););if(mh=q[ph>>2],q[jh+4>>2]=mh,kh=q[a+720>>2],q[jh+40>>2]=kh,(0|mh)<1)ih=0;else{for(nh=q[a+732>>2],oh=q[a+736>>2],rh=q[a+740>>2],th=q[jh+52>>2],sh=q[jh+8>>2],ih=mh;hh=(ih=ih+-1|0)<<2,q[(uh=sh+(ih<<3)|0)>>2]=q[hh+rh>>2],q[uh+4>>2]=q[hh+oh>>2],u[hh+th>>2]=q[hh+nh>>2]?x(1):x(0),0<(0|ih););for(nh=q[jh+12>>2],ih=0;hh=q[4+(qh+w(q[(oh=(mh=mh+-1|0)<<2)+kh>>2],36)|0)>>2],q[nh+oh>>2]=hh,ih=hh+ih|0,0<(0|mh););}if(q[jh+24>>2]=ih,mh=q[ph+4>>2],q[jh+56>>2]=mh,q[jh+180>>2]=q[a+752>>2],q[jh+184>>2]=q[a+780>>2],q[jh+188>>2]=q[a+804>>2],1<=(0|mh)){for(;;){mh=mh+-1|0,hh=q[jh+60>>2]+(mh<<5)|0,ih=mh<<2,q[hh>>2]=q[ih+q[a+764>>2]>>2],q[hh+4>>2]=q[ih+q[a+768>>2]>>2],kh=q[ih+q[a+772>>2]>>2],q[hh+8>>2]=kh,ph=q[ih+q[a+776>>2]>>2],q[hh+12>>2]=ph,q[hh+28>>2]=q[ih+q[a+760>>2]>>2];c:if(kh>>>0<=1){if(kh-1){q[hh+20>>2]=1,q[hh+16>>2]=2,q[hh+24>>2]=q[jh+68>>2]+w(ph,24);break c}q[hh+20>>2]=3,q[hh+16>>2]=4,q[hh+24>>2]=q[jh+76>>2]+(ph<<5)}else Y(4,1026,0);if(!(0<(0|mh)))break}ph=q[a+704>>2]}mh=q[ph+8>>2];d:if(!((0|(q[jh+64>>2]=mh))<1)){if(ih=mh+-1|0,qh=q[a+792>>2],nh=q[a+800>>2],oh=q[a+796>>2],rh=q[jh+68>>2],r[a+4|0]<2)for(;;)if(hh=rh+w(ih,24)|0,kh=ih<<2,q[hh>>2]=q[kh+oh>>2],q[hh+4>>2]=q[kh+nh>>2],kh=q[kh+qh>>2],q[hh+8>>2]=0,q[hh+12>>2]=kh,hh=0<(0|ih),ih=ih+-1|0,!hh)break d;for(th=q[a+1108>>2];hh=rh+w(ih,24)|0,kh=ih<<2,q[hh>>2]=q[kh+oh>>2],q[hh+4>>2]=q[kh+nh>>2],q[hh+12>>2]=q[kh+qh>>2],q[hh+8>>2]=q[kh+th>>2],hh=0<(0|ih),ih=ih+-1|0,hh;);}if(hh=q[ph+12>>2],1<=(0|(q[jh+72>>2]=hh)))for(kh=q[a+816>>2],qh=q[jh+76>>2],ih=hh;q[qh+((ih=ih+-1|0)<<5)>>2]=q[kh+(ih<<2)>>2],0<(0|ih););if(1<=((kh=ih=0)|mh)){for(qh=q[jh+80>>2],nh=q[jh+184>>2],oh=q[jh+316>>2];hh=q[4+(oh+w(q[(rh=(mh=mh+-1|0)<<2)+nh>>2],36)|0)>>2],q[qh+rh>>2]=hh,kh=hh+kh|0,0<(0|mh););hh=q[jh+72>>2]}if(q[jh+92>>2]=kh,1<=(0|hh))for(mh=q[jh+112>>2],qh=q[jh+188>>2],nh=q[jh+316>>2];kh=q[4+(nh+w(q[(oh=(hh=hh+-1|0)<<2)+qh>>2],36)|0)>>2],q[mh+oh>>2]=kh,ih=ih+kh|0,0<(0|hh););if(q[jh+124>>2]=ih,kh=q[ph+16>>2],q[jh+200>>2]=kh,qh=q[a+840>>2],q[jh+252>>2]=qh,(0|kh)<1)ih=0;else{for(nh=q[a+856>>2],oh=q[a+876>>2],rh=q[a+864>>2],th=q[a+860>>2],sh=q[jh+204>>2],ih=kh;mh=(ih=ih+-1|0)<<2,q[(hh=sh+(ih<<4)|0)>>2]=q[mh+th>>2],q[hh+4>>2]=q[mh+rh>>2],q[hh+12>>2]=q[mh+oh>>2],q[hh+8>>2]=q[mh+nh>>2],0<(0|ih););for(mh=q[jh+208>>2],nh=q[jh+316>>2],ih=0;hh=q[4+(nh+w(q[(oh=(kh=kh+-1|0)<<2)+qh>>2],36)|0)>>2],q[mh+oh>>2]=hh,ih=hh+ih|0,0<(0|kh););}if(q[jh+220>>2]=ih,mh=q[ph+72>>2],1<=(0|(q[jh+320>>2]=mh)))for(oh=q[a+1028>>2],rh=q[a+1044>>2],th=q[a+1040>>2],sh=q[a+1036>>2],uh=q[a+1032>>2],yh=q[jh+324>>2],kh=0;;){if(hh=yh+w(kh,28)|0,qh=q[(ih=kh<<2)+uh>>2],q[hh+4>>2]=qh,q[hh>>2]=q[ih+sh>>2],nh=q[ih+th>>2],q[hh+16>>2]=nh,vh=q[ih+rh>>2],q[hh+20>>2]=vh,q[hh+8>>2]=0,q[hh+24>>2]=1+(nh-vh|0),1<=(0|qh))for(vh=q[ih+oh>>2],zh=q[hh+12>>2],Ah=q[a+1056>>2],wh=q[a+1048>>2],xh=q[a+1052>>2],ih=0;nh=ih+vh<<2,q[(hh=zh+(ih<<4)|0)+4>>2]=q[nh+xh>>2],q[hh>>2]=q[nh+wh>>2],nh=q[nh+Ah>>2],q[hh+12>>2]=0,q[hh+8>>2]=nh,(0|qh)!=(0|(ih=ih+1|0)););if((0|mh)==(0|(kh=kh+1|0)))break}if(hh=q[ph+80>>2],q[jh+340>>2]=hh,mh=q[a+1068>>2],q[jh+372>>2]=mh,(0|hh)<1)hh=0;else{for(ph=q[a+1100>>2],qh=q[a+1088>>2],nh=q[a+1096>>2],oh=q[a+1092>>2],rh=q[a+1084>>2],th=q[a+1080>>2],sh=q[jh+344>>2];ih=sh+w(hh=hh+-1|0,24)|0,kh=hh<<2,q[ih>>2]=q[kh+th>>2],q[ih+4>>2]=q[kh+rh>>2],q[ih+8>>2]=q[kh+oh>>2],kh=q[kh+qh>>2],q[ih+16>>2]=ph+(kh<<1),q[ih+12>>2]=nh+(kh<<2),0<(0|hh););if((0|(ih=q[jh+340>>2]))<1)hh=0;else for(ph=q[jh+348>>2],qh=q[jh+316>>2],hh=0;kh=q[4+(qh+w(q[(nh=(ih=ih+-1|0)<<2)+mh>>2],36)|0)>>2],q[nh+ph>>2]=kh,hh=hh+kh|0,0<(0|ih););}q[jh>>2]=a,q[jh+360>>2]=hh,ta(jh)}return L=336+lh|0,jh}(a,cb,db))break a;q[36+pb>>2]=1872,q[32+pb>>2]=1846,Y(4,1087,32+pb|0)}else q[52+pb>>2]=1441,q[48+pb>>2]=1846,Y(4,1087,48+pb|0);else q[20+pb>>2]=1246,q[16+pb>>2]=1846,Y(4,1087,16+pb|0);else q[4+pb>>2]=1828,q[pb>>2]=1846,Y(4,1087,pb);cb=0}return L=pb+64|0,0|cb}function wa(a){var cb;return L=cb=L-16|0,a=(a|=0)?function(a){var Uh;return ba(16+(L=Uh=L-336|0)|0,0,308),ua(a+704|0,16+Uh|0,12+Uh|0),L=336+Uh|0,q[12+Uh>>2]}(a):(q[4+cb>>2]=1828,q[cb>>2]=1810,Y(4,1087,cb),0),L=16+cb|0,0|a}function xa(a,db){var qb=0,rb=0,sb=0,tb=0,ub=0,vb=0,wb=0,xb=0;tb=a+db|0;a:{b:if(!(1&(qb=q[a+4>>2]))){if(!(3&qb))break a;if(db=(qb=q[a>>2])+db|0,(0|(a=a-qb|0))==q[1928]){if(3==(3&(qb=q[tb+4>>2])))return q[1925]=db,q[tb+4>>2]=-2&qb,q[a+4>>2]=1|db,void(q[tb>>2]=db)}else{if(qb>>>0<=255){if(sb=qb>>>3,qb=q[a+8>>2],(0|(rb=q[a+12>>2]))==(0|qb)){wb=7692,xb=q[1923]&jc(sb),q[wb>>2]=xb;break b}q[qb+12>>2]=rb,q[rb+8>>2]=qb;break b}if(vb=q[a+24>>2],(0|(qb=q[a+12>>2]))==(0|a))if((sb=q[(rb=a+20|0)>>2])||(sb=q[(rb=a+16|0)>>2])){for(;ub=rb,(sb=q[(rb=(qb=sb)+20|0)>>2])||(rb=qb+16|0,sb=q[qb+16>>2]););q[ub>>2]=0}else qb=0;else rb=q[a+8>>2],q[rb+12>>2]=qb,q[qb+8>>2]=rb;if(!vb)break b;rb=q[a+28>>2];e:{if(q[(sb=7996+(rb<<2)|0)>>2]==(0|a)){if(q[sb>>2]=qb)break e;wb=7696,xb=q[1924]&jc(rb),q[wb>>2]=xb;break b}if(!(q[vb+(q[vb+16>>2]==(0|a)?16:20)>>2]=qb))break b}if(q[qb+24>>2]=vb,(rb=q[a+16>>2])&&(q[qb+16>>2]=rb,q[rb+24>>2]=qb),!(rb=q[a+20>>2]))break b;q[qb+20>>2]=rb,q[rb+24>>2]=qb}}f:{if(!(2&(qb=q[tb+4>>2]))){if(q[1929]==(0|tb)){if(q[1929]=a,db=q[1926]+db|0,q[1926]=db,q[a+4>>2]=1|db,q[1928]!=(0|a))break a;return q[1925]=0,void(q[1928]=0)}if(q[1928]==(0|tb))return q[1928]=a,db=q[1925]+db|0,q[1925]=db,q[a+4>>2]=1|db,void(q[a+db>>2]=db);db=(-8&qb)+db|0;g:if(qb>>>0<=255){if(sb=qb>>>3,qb=q[tb+8>>2],(0|(rb=q[tb+12>>2]))==(0|qb)){wb=7692,xb=q[1923]&jc(sb),q[wb>>2]=xb;break g}q[qb+12>>2]=rb,q[rb+8>>2]=qb}else{if(vb=q[tb+24>>2],(0|tb)==(0|(qb=q[tb+12>>2])))if((sb=q[(rb=tb+20|0)>>2])||(sb=q[(rb=tb+16|0)>>2])){for(;ub=rb,(sb=q[(rb=(qb=sb)+20|0)>>2])||(rb=qb+16|0,sb=q[qb+16>>2]););q[ub>>2]=0}else qb=0;else rb=q[tb+8>>2],q[rb+12>>2]=qb,q[qb+8>>2]=rb;if(vb){rb=q[tb+28>>2];j:{if(q[(sb=7996+(rb<<2)|0)>>2]==(0|tb)){if(q[sb>>2]=qb)break j;wb=7696,xb=q[1924]&jc(rb),q[wb>>2]=xb;break g}if(!(q[vb+(q[vb+16>>2]==(0|tb)?16:20)>>2]=qb))break g}q[qb+24>>2]=vb,(rb=q[tb+16>>2])&&(q[qb+16>>2]=rb,q[rb+24>>2]=qb),(rb=q[tb+20>>2])&&(q[qb+20>>2]=rb,q[rb+24>>2]=qb)}}if(q[a+4>>2]=1|db,q[a+db>>2]=db,q[1928]!=(0|a))break f;return void(q[1925]=db)}q[tb+4>>2]=-2&qb,q[a+4>>2]=1|db,q[a+db>>2]=db}if(db>>>0<=255)return db=7732+((qb=db>>>3)<<3)|0,qb=(rb=q[1923])&(qb=1<<qb)?q[db+8>>2]:(q[1923]=qb|rb,db),q[db+8>>2]=a,q[qb+12>>2]=a,q[a+12>>2]=db,void(q[a+8>>2]=qb);q[a+16>>2]=0,qb=q[a+20>>2]=0,(sb=db>>>8)&&(qb=31,16777215<db>>>0||(qb=28+((qb=((tb=(sb<<=ub=sb+1048320>>>16&8)<<(qb=sb+520192>>>16&4))<<(sb=tb+245760>>>16&2)>>>15)-(sb|qb|ub)|0)<<1|db>>>qb+21&1)|0)),sb=7996+((q[(rb=a)+28>>2]=qb)<<2)|0;m:{if((rb=q[1924])&(ub=1<<qb)){for(rb=db<<(31==(0|qb)?0:25-(qb>>>1)|0),qb=q[sb>>2];;){if((-8&q[(sb=qb)+4>>2])==(0|db))break m;if(qb=rb>>>29,rb<<=1,!(qb=q[(ub=sb+(4&qb)|0)+16>>2]))break}q[ub+16>>2]=a}else q[1924]=rb|ub,q[sb>>2]=a;return q[a+24>>2]=sb,q[a+12>>2]=a,void(q[a+8>>2]=a)}db=q[sb+8>>2],q[db+12>>2]=a,q[sb+8>>2]=a,q[a+24>>2]=0,q[a+12>>2]=sb,q[a+8>>2]=db}}function ya(a,db,yb){var zb=0;a:{if(8!=(0|db)){if(zb=28,3&db|1!=(0|function(a){var ri=0,ui=0;for(;ui=ri,a;)a&=a-1,ri=ri+1|0;return ui}(db>>>2)))break a;if(zb=48,-64-db>>>0<yb>>>0)break a;db=function(a,$h){var ai=0,bi=0,di=0,ei=0,fi=0;if((bi=a>>>0>(ai=16)?a:16)+-1&bi)for(;ai=(a=ai)<<1,a>>>0<bi>>>0;);else a=bi;if(-64-a>>>0<=$h>>>0)return q[1906]=48,0;if(!(ai=ja(12+((bi=$h>>>0<11?16:$h+11&-8)+a|0)|0)))return 0;$h=ai+-8|0;ai&a+-1?(fi=q[(ei=ai+-4|0)>>2],di=(-8&fi)-(ai=(a=15<(ai=((a+ai|0)-1&0-a)-8|0)-$h>>>0?ai:a+ai|0)-$h|0)|0,3&fi?(q[a+4>>2]=di|1&q[a+4>>2]|2,q[(di=a+di|0)+4>>2]=1|q[di+4>>2],q[ei>>2]=ai|1&q[ei>>2]|2,q[a+4>>2]=1|q[a+4>>2],xa($h,ai)):($h=q[$h>>2],q[a+4>>2]=di,q[a>>2]=$h+ai)):a=$h;3&($h=q[a+4>>2])&&((ai=-8&$h)>>>0<=bi+16>>>0||(q[a+4>>2]=bi|1&$h|2,$h=a+bi|0,bi=ai-bi|0,q[$h+4>>2]=3|bi,q[(ai=a+ai|0)+4>>2]=1|q[ai+4>>2],xa($h,bi)));return a+8|0}(16<db>>>0?db:16,yb)}else db=ja(yb);if(!db)return 48;q[a>>2]=db,zb=0}return zb}function za(a){var db=0,yb=0,Ab=0,Bb=0,Cb=0,Db=0,Eb=0,Fb=0,Gb=0;a:if(a){Cb=(Ab=a+-8|0)+(a=-8&(yb=q[a+-4>>2]))|0;b:if(!(1&yb)){if(!(3&yb))break a;if((Ab=Ab-(yb=q[Ab>>2])|0)>>>0<t[1927])break a;if(a=a+yb|0,q[1928]==(0|Ab)){if(3==(3&(yb=q[Cb+4>>2])))return q[1925]=a,q[Cb+4>>2]=-2&yb,q[Ab+4>>2]=1|a,void(q[a+Ab>>2]=a)}else{if(yb>>>0<=255){if(Bb=q[Ab+8>>2],yb>>>=3,(0|(db=q[Ab+12>>2]))==(0|Bb)){Fb=7692,Gb=q[1923]&jc(yb),q[Fb>>2]=Gb;break b}q[Bb+12>>2]=db,q[db+8>>2]=Bb;break b}if(Eb=q[Ab+24>>2],(0|Ab)==(0|(yb=q[Ab+12>>2])))if((db=q[(Bb=Ab+20|0)>>2])||(db=q[(Bb=Ab+16|0)>>2])){for(;Db=Bb,(db=q[(Bb=(yb=db)+20|0)>>2])||(Bb=yb+16|0,db=q[yb+16>>2]););q[Db>>2]=0}else yb=0;else db=q[Ab+8>>2],q[db+12>>2]=yb,q[yb+8>>2]=db;if(!Eb)break b;Bb=q[Ab+28>>2];e:{if(q[(db=7996+(Bb<<2)|0)>>2]==(0|Ab)){if(q[db>>2]=yb)break e;Fb=7696,Gb=q[1924]&jc(Bb),q[Fb>>2]=Gb;break b}if(!(q[Eb+(q[Eb+16>>2]==(0|Ab)?16:20)>>2]=yb))break b}if(q[yb+24>>2]=Eb,(db=q[Ab+16>>2])&&(q[yb+16>>2]=db,q[db+24>>2]=yb),!(db=q[Ab+20>>2]))break b;q[yb+20>>2]=db,q[db+24>>2]=yb}}if(!(Cb>>>0<=Ab>>>0)&&1&(yb=q[Cb+4>>2])){f:{if(!(2&yb)){if(q[1929]==(0|Cb)){if(q[1929]=Ab,a=q[1926]+a|0,q[1926]=a,q[Ab+4>>2]=1|a,q[1928]!=(0|Ab))break a;return q[1925]=0,void(q[1928]=0)}if(q[1928]==(0|Cb))return q[1928]=Ab,a=q[1925]+a|0,q[1925]=a,q[Ab+4>>2]=1|a,void(q[a+Ab>>2]=a);a=(-8&yb)+a|0;g:if(yb>>>0<=255){if(yb>>>=3,(0|(db=q[Cb+8>>2]))==(0|(Bb=q[Cb+12>>2]))){Fb=7692,Gb=q[1923]&jc(yb),q[Fb>>2]=Gb;break g}q[db+12>>2]=Bb,q[Bb+8>>2]=db}else{if(Eb=q[Cb+24>>2],(0|Cb)==(0|(yb=q[Cb+12>>2])))if((db=q[(Bb=Cb+20|0)>>2])||(db=q[(Bb=Cb+16|0)>>2])){for(;Db=Bb,(db=q[(Bb=(yb=db)+20|0)>>2])||(Bb=yb+16|0,db=q[yb+16>>2]););q[Db>>2]=0}else yb=0;else db=q[Cb+8>>2],q[db+12>>2]=yb,q[yb+8>>2]=db;if(Eb){Bb=q[Cb+28>>2];j:{if(q[(db=7996+(Bb<<2)|0)>>2]==(0|Cb)){if(q[db>>2]=yb)break j;Fb=7696,Gb=q[1924]&jc(Bb),q[Fb>>2]=Gb;break g}if(!(q[Eb+(q[Eb+16>>2]==(0|Cb)?16:20)>>2]=yb))break g}q[yb+24>>2]=Eb,(db=q[Cb+16>>2])&&(q[yb+16>>2]=db,q[db+24>>2]=yb),(db=q[Cb+20>>2])&&(q[yb+20>>2]=db,q[db+24>>2]=yb)}}if(q[Ab+4>>2]=1|a,q[a+Ab>>2]=a,q[1928]!=(0|Ab))break f;return void(q[1925]=a)}q[Cb+4>>2]=-2&yb,q[Ab+4>>2]=1|a,q[a+Ab>>2]=a}if(a>>>0<=255)return yb=7732+((a>>>=3)<<3)|0,a=(db=q[1923])&(a=1<<a)?q[yb+8>>2]:(q[1923]=a|db,yb),q[yb+8>>2]=Ab,q[a+12>>2]=Ab,q[Ab+12>>2]=yb,void(q[Ab+8>>2]=a);q[Ab+16>>2]=0,db=q[Ab+20>>2]=0,(Bb=a>>>8)&&(db=31,16777215<a>>>0||(db=Bb,db<<=Bb=Bb+1048320>>>16&8,db=28+((db=((db<<=Eb=db+520192>>>16&4)<<(Db=db+245760>>>16&2)>>>15)-(Db|Bb|Eb)|0)<<1|a>>>db+21&1)|0)),Db=7996+((q[(yb=Ab)+28>>2]=db)<<2)|0;m:if((Bb=q[1924])&(yb=1<<db)){Bb=a<<(31==(0|db)?0:25-(db>>>1)|0),yb=q[Db>>2];n:{for(;;){if((-8&q[(db=yb)+4>>2])==(0|a))break n;if(yb=Bb>>>29,Bb<<=1,!(yb=q[(Db=db+(4&yb)|0)+16>>2]))break}q[Db+16>>2]=Ab,q[Ab+12>>2]=Ab,q[Ab+24>>2]=db,q[Ab+8>>2]=Ab;break m}a=q[db+8>>2],q[a+12>>2]=Ab,q[db+8>>2]=Ab,q[Ab+24>>2]=0,q[Ab+12>>2]=db,q[Ab+8>>2]=a}else q[1924]=yb|Bb,q[Db>>2]=Ab,q[Ab+12>>2]=Ab,q[Ab+24>>2]=Db,q[Ab+8>>2]=Ab;if(a=q[1931]+-1|0,!(q[1931]=a)){for(Ab=8148;Ab=(a=q[Ab>>2])+8|0,a;);q[1931]=-1}}}}function Aa(a,Hb){var Nb,Ib=0,Jb=0,Kb=0,Lb=0,Mb=x(0);if(j(Hb),!((Kb=2147483647&(Ib=e(0)))>>>0<=2139095040&&(j(a),(Jb=2147483647&(Lb=e(0)))>>>0<2139095041)))return x(a+Hb);if(1065353216==(0|Ib))return ka(a);Ib=(Nb=Ib>>>30&2)|Lb>>>31;b:{c:{d:{e:{if(!Jb){f:switch(Ib-2|0){case 0:break e;case 1:break f;default:break d}return x(-3.1415927410125732)}if(2139095040!=(0|Kb)){if(!Kb|!(Jb>>>0<=Kb+218103808>>>0&&2139095040!=(0|Jb)))break b;if(Jb+218103808>>>0<Kb>>>0&&(Mb=x(0),Nb)||(Mb=ka(x(y(x(a/Hb))))),a=Mb,Ib>>>0<=2){h:switch(Ib-1|0){case 0:return x(-a);case 1:break h;default:break d}return x(x(3.1415927410125732)-x(a+x(8.742277657347586e-8)))}return x(x(a+x(8.742277657347586e-8))+x(-3.1415927410125732))}if(2139095040==(0|Jb))break c;return u[6128+(Ib<<2)>>2]}a=x(3.1415927410125732)}return a}return u[6112+(Ib<<2)>>2]}return x((0|Lb)<0?-1.5707963705062866:1.5707963705062866)}function Da(a,Wb,Xb){var $b,ec,ic,jc,mc,Yb=0,Zb=0,_b=0,ac=0,bc=0,cc=0,dc=0,fc=0,gc=0,hc=0,kc=0,lc=0;if(L=$b=L-560|0,dc=(Zb=Xb)+w(ic=0<(0|(Xb=(Xb+-3|0)/24|0))?Xb:0,-24)|0,0<=(0|(ec=q[808])))for(Zb=ec+1|0,Xb=ic;v[(320+$b|0)+(_b<<3)>>3]=(0|Xb)<0?0:+q[3248+(Xb<<2)>>2],Xb=Xb+1|0,(0|Zb)!=(0|(_b=_b+1|0)););for(bc=dc+-24|0,Zb=0;;){for(Yb=Xb=0;Yb+=v[(Xb<<3)+a>>3]*v[(320+$b|0)+(Zb-Xb<<3)>>3],1!=(0|(Xb=Xb+1|0)););if(v[(Zb<<3)+$b>>3]=Yb,Xb=(0|Zb)<(0|ec),Zb=Zb+1|0,!Xb)break}mc=23-bc|0,jc=24-bc|0,Zb=ec;a:{for(;;){if(Yb=v[(Zb<<3)+$b>>3],!(gc=((Xb=0)|(_b=Zb))<1))for(;cc=(480+$b|0)+(Xb<<2)|0,fc=Yb,ac=y(Yb*=5.960464477539063e-8)<2147483648?~~Yb:-2147483648,ac=y(fc+=-16777216*(Yb=+(0|ac)))<2147483648?~~fc:-2147483648,q[cc>>2]=ac,Yb=v[((_b=_b+-1|0)<<3)+$b>>3]+Yb,(0|Zb)!=(0|(Xb=Xb+1|0)););Yb=ha(Yb,bc),Yb+=-8*C(.125*Yb),Yb-=0|(cc=y(Yb)<2147483648?~~Yb:-2147483648);e:{f:{g:{if(kc=(0|bc)<1){if(bc)break g;ac=q[476+((Zb<<2)+$b|0)>>2]>>23}else hc=_b=(Zb<<2)+$b|0,_b=(ac=q[_b+476>>2])-((Xb=ac>>jc)<<jc)|0,cc=Xb+cc|0,ac=(q[hc+476>>2]=_b)>>mc;if((0|ac)<1)break e;break f}if(ac=2,!(.5<=Yb)){ac=0;break e}}if(_b=Xb=0,!gc)for(;;){gc=q[(lc=(480+$b|0)+(Xb<<2)|0)>>2],hc=16777215;i:{j:{if(!_b){if(!gc)break j;hc=16777216,_b=1}q[lc>>2]=hc-gc;break i}_b=0}if((0|Zb)==(0|(Xb=Xb+1|0)))break}kc||1<(Xb=bc+-1|0)>>>0||(q[(Xb=(Zb<<2)+$b|0)+476>>2]=Xb-1?8388607&q[Xb+476>>2]:4194303&q[Xb+476>>2]),cc=cc+1|0,2==(0|ac)&&(Yb=1-Yb,ac=2,_b&&(Yb-=ha(1,bc)))}if(0!=Yb)break;if(!(((_b=0)|(Xb=Zb))<=(0|ec))){for(;_b=q[(480+$b|0)+((Xb=Xb+-1|0)<<2)>>2]|_b,(0|ec)<(0|Xb););if(_b){for(dc=bc;dc=dc+-24|0,!q[(480+$b|0)+((Zb=Zb+-1|0)<<2)>>2];);break a}}for(Xb=1;Xb=(_b=Xb)+1|0,!q[(480+$b|0)+(ec-_b<<2)>>2];);for(_b=Zb+_b|0;;){for(Zb=cc=Zb+1|0,v[(320+$b|0)+(cc<<3)>>3]=q[3248+(ic+Zb<<2)>>2],Yb=Xb=0;Yb+=v[(Xb<<3)+a>>3]*v[(320+$b|0)+(cc-Xb<<3)>>3],1!=(0|(Xb=Xb+1|0)););if(v[(Zb<<3)+$b>>3]=Yb,!((0|Zb)<(0|_b)))break}Zb=_b}16777216<=(Yb=ha(Yb,0-bc|0))?(a=(480+$b|0)+(Zb<<2)|0,fc=Yb,Xb=y(Yb*=5.960464477539063e-8)<2147483648?~~Yb:-2147483648,_b=y(Yb=fc+-16777216*(0|Xb))<2147483648?~~Yb:-2147483648,q[a>>2]=_b,Zb=Zb+1|0):(Xb=y(Yb)<2147483648?~~Yb:-2147483648,dc=bc),q[(480+$b|0)+(Zb<<2)>>2]=Xb}if(Yb=ha(1,dc),!((0|Zb)<=-1)){for(Xb=Zb;v[(Xb<<3)+$b>>3]=Yb*q[(480+$b|0)+(Xb<<2)>>2],Yb*=5.960464477539063e-8,a=0<(0|Xb),Xb=Xb+-1|0,a;);if(!((0|Zb)<=-1))for(Xb=Zb;;){for(bc=Zb-(a=Xb)|0,Xb=Yb=0;Yb+=v[6016+(Xb<<3)>>3]*v[(a+Xb<<3)+$b>>3],!((0|ec)<=(0|Xb))&&(dc=Xb>>>0<bc>>>0,Xb=Xb+1|0,dc););if(v[(160+$b|0)+(bc<<3)>>3]=Yb,Xb=a+-1|0,!(0<(0|a)))break}}if((Yb=0)<=(0|Zb))for(;Yb+=v[(160+$b|0)+(Zb<<3)>>3],a=0<(0|Zb),Zb=Zb+-1|0,a;);return v[Wb>>3]=ac?-Yb:Yb,L=560+$b|0,7&cc}function Ea(a,Wb,Xb,nc){var qc,sc,tc,uc,oc=0,pc=0,rc=0;L=qc=L-32|0,oc=(sc=oc=2147483647&nc)+-1006698496|0,(pc=rc=Xb)>>>0<0&&(oc=oc+1|0),tc=pc,pc=oc,oc=sc+-1140785152|0,(uc=rc)>>>0<0&&(oc=oc+1|0);a:if((0|oc)==(0|pc)&tc>>>0<uc>>>0|pc>>>0<oc>>>0){if(oc=nc<<4|Xb>>>28,Xb=Xb<<4|Wb>>>28,134217728==(0|(rc=Wb&=268435455))&1<=a>>>0|134217728<Wb>>>0){oc=oc+1073741824|0,(a=Xb+1|0)>>>0<1&&(oc=oc+1|0),pc=a;break a}if(oc=oc-(((pc=Xb)>>>0<0)+-1073741824|0)|0,a|134217728^rc)break a;(a=pc+(1&pc)|0)>>>0<pc>>>0&&(oc=oc+1|0),pc=a}else(!rc&2147418112==(0|sc)?!(a|Wb):2147418112==(0|sc)&rc>>>0<0|sc>>>0<2147418112)?(oc=2146435072,1140785151==((pc=0)|sc)&4294967295<rc>>>0|1140785151<sc>>>0||(rc=sc>>>16)>>>(oc=0)<15249||(function(a,Wb,Xb,nc,vc,wc){var Bc=0,Cc=0,Dc=0,Ec=0;a:if(64&wc)Wb=31&(Xb=wc+-64|0),Wb=32<=(63&Xb)>>>0?(Xb=0,vc>>>Wb):(Xb=vc>>>Wb,((1<<Wb)-1&vc)<<32-Wb|nc>>>Wb),vc=nc=0;else{if(!wc)break a;Cc=vc,Dc=nc,Bc=31&(Ec=64-wc|0),Ec=32<=(63&Ec)>>>0?(Cc=Dc<<Bc,0):(Cc=(1<<Bc)-1&Dc>>>32-Bc|Cc<<Bc,Dc<<Bc),Dc=Wb,Wb=31&(Bc=wc),Wb=32<=(63&Bc)>>>0?(Bc=0,Xb>>>Wb):(Bc=Xb>>>Wb,((1<<Wb)-1&Xb)<<32-Wb|Dc>>>Wb),Wb|=Ec,Xb=Bc|Cc,Bc=nc,nc=31&wc,nc=32<=(63&wc)>>>0?(Cc=0,vc>>>nc):(Cc=vc>>>nc,((1<<nc)-1&vc)<<32-nc|Bc>>>nc),vc=Cc}q[a>>2]=Wb,q[a+4>>2]=Xb,q[a+8>>2]=nc,q[a+12>>2]=vc}(qc,a,Wb,Xb,oc=65535&nc|65536,15361-rc|0),function(a,Wb,Xb,nc,vc,wc){var xc=0,yc=0,zc=0,Ac=0;64&wc?(nc=Wb,Wb=31&(vc=wc+-64|0),32<=(63&vc)>>>0?(vc=nc<<Wb,nc=0):(vc=(1<<Wb)-1&nc>>>32-Wb|Xb<<Wb,nc<<=Wb),Xb=Wb=0):wc&&(xc=nc,nc=31&(zc=wc),Ac=32<=(63&wc)>>>0?(yc=xc<<nc,0):(yc=(1<<nc)-1&xc>>>32-nc|vc<<nc,xc<<nc),nc=Xb,xc=Wb,vc=31&(wc=64-wc|0),32<=(63&wc)>>>0?(wc=0,nc>>>=vc):(wc=nc>>>vc,nc=((1<<vc)-1&nc)<<32-vc|xc>>>vc),nc|=Ac,vc=wc|yc,wc=Wb,Wb=31&zc,Wb=32<=(63&zc)>>>0?(yc=wc<<Wb,0):(yc=(1<<Wb)-1&wc>>>32-Wb|Xb<<Wb,wc<<Wb),Xb=yc);q[a>>2]=Wb,q[a+4>>2]=Xb,q[a+8>>2]=nc,q[a+12>>2]=vc}(16+qc|0,a,Wb,Xb,oc,rc+-15233|0),Xb=q[4+qc>>2],a=q[8+qc>>2],oc=q[12+qc>>2]<<4|a>>>28,pc=a<<4|Xb>>>28,134217728==(0|(Xb=a=268435455&Xb))&1<=(Wb=q[qc>>2]|0!=(q[16+qc>>2]|q[24+qc>>2])|0!=(q[20+qc>>2]|q[28+qc>>2]))>>>0|134217728<a>>>0?((a=pc+1|0)>>>0<1&&(oc=oc+1|0),pc=a):Wb|134217728^Xb||((a=pc+(1&pc)|0)>>>0<pc>>>0&&(oc=oc+1|0),pc=a))):(pc=Xb<<4|Wb>>>28,oc=524287&(oc=nc<<4|Xb>>>28)|2146959360);return L=32+qc|0,f(0,0|pc),f(1,-2147483648&nc|oc),+g()}function Ia(a,Wb){var Xb=0,nc=0,vc=0;Xb=0!=(0|Wb);a:{b:{c:{d:if(!(!Wb|!(3&a)))for(;;){if(!r[0|a])break c;if(a=a+1|0,Xb=0!=(0|(Wb=Wb+-1|0)),!Wb)break d;if(!(3&a))break}if(!Xb)break b}if(!r[0|a])break a;e:{if(4<=Wb>>>0){for(Xb=(Xb=Wb+-4|0)-(nc=-4&Xb)|0,nc=4+(a+nc|0)|0;;){if((-1^(vc=q[a>>2]))&vc+-16843009&-2139062144)break e;if(a=a+4|0,!(3<(Wb=Wb+-4|0)>>>0))break}Wb=Xb,a=nc}if(!Wb)break b}for(;;){if(!r[0|a])break a;if(a=a+1|0,!(Wb=Wb+-1|0))break}}return 0}return a}function Na(a,Wb,Hc,Ic){if(a|Wb)for(;o[0|(Hc=Hc+-1|0)]=r[3168+(15&a)|0]|Ic,(a=(15&Wb)<<28|a>>>4)|(Wb>>>=4););return Hc}function Oa(a,Wb,Hc){if(a|Wb)for(;o[0|(Hc=Hc+-1|0)]=7&a|48,(a=(7&Wb)<<29|a>>>3)|(Wb>>>=3););return Hc}function ab(a){var ud;q[(L=ud=L-16|0)>>2]=a,function(a,Hc){var id;ra(a,1176,q[12+(L=id=L-16|0)>>2]=Hc,0,0),L=16+id|0}(q[670],ud),L=16+ud|0}function jb(a){var Xe,se=0,te=0,Le=0,Ye=0,Ze=0,_e=0,$e=0,af=0,bf=0,cf=0;if(q[24+(L=Xe=L-32|0)>>2]=0,q[16+Xe>>2]=4,function(a){var vd;sa(16+(L=vd=L-272|0)|0,1611,q[12+vd>>2]=a),ab(16+vd|0),L=272+vd|0}(16+Xe|(q[20+Xe>>2]=0)),ma(a))Y(4,1183,0),a=0;else if(4<=(te=r[a+4|0])>>>0)q[4+Xe>>2]=te,q[Xe>>2]=3,Y(4,1332,Xe),a=0;else{for(1!=(0|(Ye=!r[a+5|0]))&&(ca(a+4|0,1),X(a- -64|0,4,160),o[a+5|0]=0),se=a- -64|0,Le=102,te=a+704|0;q[te>>2]=q[se>>2]+a,te=te+4|0,se=se+4|0,Le=Le+-1|0;);if(1!=(0|Ye)&&(te=r[a+4|0],X(q[a+704>>2],4,32),ca(q[a+708>>2],4),ca(q[a+708>>2]+4|0,4),ca(q[a+708>>2]+8|0,4),ca(q[a+708>>2]+12|0,4),ca(q[a+708>>2]+16|0,4),ca(q[a+708>>2]+20|0,1),X(q[a+720>>2],4,q[q[a+704>>2]>>2]),X(q[a+724>>2],4,q[q[a+704>>2]>>2]),X(q[a+728>>2],4,q[q[a+704>>2]>>2]),X(q[a+732>>2],4,q[q[a+704>>2]>>2]),X(q[a+736>>2],4,q[q[a+704>>2]>>2]),X(q[a+740>>2],4,q[q[a+704>>2]>>2]),X(q[a+752>>2],4,q[q[a+704>>2]+4>>2]),X(q[a+756>>2],4,q[q[a+704>>2]+4>>2]),X(q[a+760>>2],4,q[q[a+704>>2]+4>>2]),X(q[a+764>>2],4,q[q[a+704>>2]+4>>2]),X(q[a+768>>2],4,q[q[a+704>>2]+4>>2]),X(q[a+772>>2],4,q[q[a+704>>2]+4>>2]),X(q[a+776>>2],4,q[q[a+704>>2]+4>>2]),X(q[a+780>>2],4,q[q[a+704>>2]+8>>2]),X(q[a+784>>2],4,q[q[a+704>>2]+8>>2]),X(q[a+788>>2],4,q[q[a+704>>2]+8>>2]),X(q[a+792>>2],4,q[q[a+704>>2]+8>>2]),X(q[a+796>>2],4,q[q[a+704>>2]+8>>2]),X(q[a+800>>2],4,q[q[a+704>>2]+8>>2]),X(q[a+804>>2],4,q[q[a+704>>2]+12>>2]),X(q[a+808>>2],4,q[q[a+704>>2]+12>>2]),X(q[a+812>>2],4,q[q[a+704>>2]+12>>2]),X(q[a+816>>2],4,q[q[a+704>>2]+12>>2]),X(q[a+840>>2],4,q[q[a+704>>2]+16>>2]),X(q[a+844>>2],4,q[q[a+704>>2]+16>>2]),X(q[a+848>>2],4,q[q[a+704>>2]+16>>2]),X(q[a+852>>2],4,q[q[a+704>>2]+16>>2]),X(q[a+856>>2],4,q[q[a+704>>2]+16>>2]),X(q[a+860>>2],4,q[q[a+704>>2]+16>>2]),X(q[a+864>>2],4,q[q[a+704>>2]+16>>2]),X(q[a+868>>2],4,q[q[a+704>>2]+16>>2]),X(q[a+872>>2],1,q[q[a+704>>2]+16>>2]),X(q[a+876>>2],4,q[q[a+704>>2]+16>>2]),X(q[a+880>>2],4,q[q[a+704>>2]+16>>2]),X(q[a+884>>2],4,q[q[a+704>>2]+16>>2]),X(q[a+888>>2],4,q[q[a+704>>2]+16>>2]),X(q[a+892>>2],4,q[q[a+704>>2]+16>>2]),X(q[a+896>>2],4,q[q[a+704>>2]+16>>2]),X(q[a+908>>2],4,q[q[a+704>>2]+20>>2]),X(q[a+912>>2],4,q[q[a+704>>2]+20>>2]),X(q[a+916>>2],4,q[q[a+704>>2]+20>>2]),X(q[a+920>>2],4,q[q[a+704>>2]+20>>2]),X(q[a+924>>2],4,q[q[a+704>>2]+20>>2]),X(q[a+928>>2],4,q[q[a+704>>2]+20>>2]),X(q[a+932>>2],4,q[q[a+704>>2]+20>>2]),X(q[a+936>>2],4,q[q[a+704>>2]+24>>2]),X(q[a+940>>2],4,q[q[a+704>>2]+28>>2]),X(q[a+944>>2],4,q[q[a+704>>2]+28>>2]),X(q[a+948>>2],4,q[q[a+704>>2]+32>>2]),X(q[a+952>>2],4,q[q[a+704>>2]+32>>2]),X(q[a+956>>2],4,q[q[a+704>>2]+32>>2]),X(q[a+960>>2],4,q[q[a+704>>2]+32>>2]),X(q[a+964>>2],4,q[q[a+704>>2]+32>>2]),X(q[a+968>>2],4,q[q[a+704>>2]+32>>2]),X(q[a+972>>2],4,q[q[a+704>>2]+32>>2]),X(q[a+976>>2],4,q[q[a+704>>2]+36>>2]),X(q[a+980>>2],4,q[q[a+704>>2]+36>>2]),X(q[a+984>>2],4,q[q[a+704>>2]+36>>2]),X(q[a+988>>2],4,q[q[a+704>>2]+40>>2]),X(q[a+992>>2],4,q[q[a+704>>2]+44>>2]),X(q[a+996>>2],4,q[q[a+704>>2]+48>>2]),X(q[a+1e3>>2],4,q[q[a+704>>2]+48>>2]),X(q[a+1004>>2],4,q[q[a+704>>2]+52>>2]),X(q[a+1008>>2],4,q[q[a+704>>2]+52>>2]),X(q[a+1012>>2],4,q[q[a+704>>2]+56>>2]),X(q[a+1016>>2],4,q[q[a+704>>2]+60>>2]),X(q[a+1020>>2],2,q[q[a+704>>2]+64>>2]),X(q[a+1024>>2],4,q[q[a+704>>2]+68>>2]),X(q[a+1028>>2],4,q[q[a+704>>2]+72>>2]),X(q[a+1032>>2],4,q[q[a+704>>2]+72>>2]),X(q[a+1036>>2],4,q[q[a+704>>2]+72>>2]),X(q[a+1040>>2],4,q[q[a+704>>2]+72>>2]),X(q[a+1044>>2],4,q[q[a+704>>2]+72>>2]),X(q[a+1048>>2],4,q[q[a+704>>2]+76>>2]),X(q[a+1052>>2],4,q[q[a+704>>2]+76>>2]),X(q[a+1056>>2],4,q[q[a+704>>2]+76>>2]),X(q[a+1068>>2],4,q[q[a+704>>2]+80>>2]),X(q[a+1072>>2],4,q[q[a+704>>2]+80>>2]),X(q[a+1076>>2],4,q[q[a+704>>2]+80>>2]),X(q[a+1080>>2],4,q[q[a+704>>2]+80>>2]),X(q[a+1084>>2],4,q[q[a+704>>2]+80>>2]),X(q[a+1088>>2],4,q[q[a+704>>2]+80>>2]),X(q[a+1092>>2],4,q[q[a+704>>2]+80>>2]),X(q[a+1096>>2],4,q[q[a+704>>2]+84>>2]),X(q[a+1100>>2],2,q[q[a+704>>2]+84>>2]),X(q[a+1104>>2],4,q[q[a+704>>2]+88>>2]),te>>>0<2||X(q[a+1108>>2],4,q[q[a+704>>2]+8>>2])),q[1643]=5,q[1642]=6,q[1644]=7,q[1645]=8,q[1646]=9,q[1647]=10,se=q[a+704>>2],1<=q[se>>2])for(te=0;q[q[a+712>>2]+(te<<2)>>2]=q[a+716>>2]+(te<<6),te=te+1|0,se=q[a+704>>2],(0|te)<q[se>>2];);if(1<=q[se+4>>2])for(te=0;q[q[a+744>>2]+(te<<2)>>2]=q[a+748>>2]+(te<<6),te=te+1|0,se=q[a+704>>2],(0|te)<q[se+4>>2];);if(1<=q[se+16>>2])for(te=0;q[(se=te<<2)+q[a+820>>2]>>2]=q[a+836>>2]+(te<<6),q[se+q[a+824>>2]>>2]=q[a+1016>>2]+(q[se+q[a+880>>2]>>2]<<2),q[se+q[a+828>>2]>>2]=q[a+1020>>2]+(q[se+q[a+884>>2]>>2]<<1),q[se+q[a+832>>2]>>2]=q[a+1024>>2]+(q[se+q[a+892>>2]>>2]<<2),te=te+1|0,se=q[a+704>>2],(0|te)<q[se+16>>2];);if(1<=q[se+20>>2])for(te=0;q[q[a+900>>2]+(te<<2)>>2]=q[a+904>>2]+(te<<6),te=te+1|0,se=q[a+704>>2],(0|te)<q[se+20>>2];);if(1<=q[se+80>>2])for(te=0;q[q[a+1060>>2]+(te<<2)>>2]=q[a+1064>>2]+(te<<6),te=te+1|0,se=q[a+704>>2],(0|te)<q[se+80>>2];);if(!(1&o[q[a+708>>2]+20|0]||(0|(Ye=q[se+16>>2]))<1)){for(_e=q[a+888>>2],$e=q[a+884>>2],Ze=q[a+1020>>2],te=0;;){if(0<(0|(af=q[(se=te<<2)+_e>>2]+-1|0)))for(bf=Ze+(q[se+$e>>2]<<1)|0,se=0;cf=s[(Le=(se<<1)+bf|0)>>1],p[Le>>1]=s[Le+4>>1],p[Le+4>>1]=cf,(0|(se=se+3|0))<(0|af););if((0|Ye)==(0|(te=te+1|0)))break}for(te=q[a+876>>2],_e=q[a+880>>2],$e=q[a+1016>>2],Le=0;;){if(1<=(0|(Ze=q[(se=Le<<2)+te>>2])))for(Ze=(se=$e+(q[se+_e>>2]<<2)|0)+(Ze<<3)|0,se=se+4|0;u[se>>2]=x(1)-u[se>>2],(se=se+8|0)>>>0<Ze>>>0;);if((0|Ye)==(0|(Le=Le+1|0)))break}}}return L=32+Xe|0,a}function $b(a){var $h=x(0),gi=x(0),hi=0,ii=0,ji=x(0),ki=x(0),li=x(0),mi=x(0),ni=0,oi=x(0),pi=x(0),qi=0;a:{b:{if(j(a),ii=2147483647&(hi=e(0))){if(!(ii>>>0<2139095041))return x(x(.10000000149011612)+a);if(1065353216==(0|ii))return x(-1<(0|hi)?.10000000149011612:10);if(2139095040==(0|ii))return x(-1<(0|hi)?0:-a);if(1073741824==(0|hi))return x(.010000000707805157);if(1056964608==(0|hi))return x(.3162277638912201);if(1291845633<=ii>>>0)return x((0|hi)<0?H:0);if(ji=u[1537],ki=x(x(1.600000023841858)-ji),li=x(x(1)/x(ji+x(1.600000023841858))),f(0,-4096&(j(gi=x(ki*li)),e(0))),$h=k(),mi=x($h*$h),pi=u[1541],ji=x(li*x(x(ki-x((oi=$h)*x(3.099609375)))-x($h*x(x(1.600000023841858)-x(x(3.099609375)-ji))))),li=x(x(gi+$h)*ji),$h=x(gi*gi),ki=x(li+x(x($h*$h)*x(x($h*x(x($h*x(x($h*x(x($h*x(x($h*x(.20697501301765442))+x(.23066075146198273)))+x(.2727281153202057)))+x(.3333333432674408)))+x(.4285714328289032)))+x(.6000000238418579)))),f(0,-4096&(j(x(x(mi+x(3))+ki)),e(0))),$h=k(),li=x(oi*$h),gi=x(x(ji*$h)+x(gi*x(ki-x(x($h+x(-3))-mi)))),f(0,-4096&(j(x(li+gi)),e(0))),$h=k(),ji=x($h*x(.9619140625)),mi=x(u[1539]+x(x(x(gi-x($h-li))*x(.9617967009544373))+x($h*x(-.00011736857413779944)))),f(0,-4096&(j(x(x(pi+x(ji+mi))+x(-4))),e(0))),gi=k(),f(0,-4096&hi),ki=k(),$h=x(gi*ki),a=x(x(x(mi-x(x(x(gi-x(-4))-pi)-ji))*a)+x(x(a-ki)*gi)),j(gi=x($h+a)),1124073473<=(0|(hi=e(0))))break b;d:{e:{if((ii=1124073472)==(0|hi)){if(!(x(a+x(4.299566569443414e-8))>x(gi-$h)))break e;break b}if(ii=2147483647&hi,!(a<=x(gi-$h)^1|-1021968384!=(0|hi))|1125515265<=ii>>>0)break a;if(ii>>>0<1056964609)break d}ni=(8388607&(ii=(8388608>>>(ii>>>23)-126)+hi|0)|8388608)>>>150-(qi=ii>>>23&255),ni=(0|hi)<0?0-ni|0:ni,$h=x($h-(f(0,ii&-8388608>>qi+-127),k())),j(x(a+$h)),hi=e(0)}f(0,-32768&hi),gi=k(),ji=x(gi*x(.693145751953125)),gi=x(x(gi*x(14286065379565116e-22))+x(x(a-x(gi-$h))*x(.6931471824645996))),a=x(ji+gi),$h=x(a*a),$h=x(a-x($h*x(x($h*x(x($h*x(x($h*x(x($h*x(4.138136944220605e-8))+x(-16533901998627698e-22)))+x(661375597701408e-19)))+x(-.0027777778450399637)))+x(.1666666716337204)))),oi=x(x(a*$h)/x($h+x(-2))),$h=x(gi-x(a-ji)),a=(0|(hi=0|(j(a=x(x(a-x(oi-x($h+x(a*$h))))+x(1))),e(0)+(ni<<23))))<=8388607?function(a,ri){var si=0;a:if(128<=(0|ri)){if(a=x(a*x(17014118346046923e22)),(0|(si=ri+-127|0))<128){ri=si;break a}a=x(a*x(17014118346046923e22)),ri=((0|ri)<381?ri:381)+-254|0}else-127<(0|ri)||(a=x(a*x(11754943508222875e-54)),ri=-127<(0|(si=ri+126|0))?si:(a=x(a*x(11754943508222875e-54)),(-378<(0|ri)?ri:-378)+252|0));return x(a*(f(0,1065353216+(ri<<23)|0),k()))}(a,ni):(f(0,hi),k()),a=x(x(1)*a)}else a=x(1);return a}return x(H)}return x(0)}function dc(a,ri){!function(low,high){b[0]=low,b[1]=high}(0|a,0|ri)}function gc(a,ri,ui){return function(a,ri,ui){var wi,xi,yi,zi,vi=0;return zi=w(wi=ui>>>16,vi=a>>>16),a=(65535&(vi=((yi=w(xi=65535&ui,a&=65535))>>>16)+w(vi,xi)|0))+w(a,wi)|0,M=((zi+w(ri,ui)|0)+(vi>>>16)|0)+(a>>>16)|0,65535&yi|a<<16}(a,ri,ui)}function hc(a,ri,ui){return function(a,ri,ui){var Ai=0,Bi=0,Ci=0,Di=0,Ei=0,Fi=0,Gi=0,Hi=0,Ii=0;a:{b:{c:{d:{e:{f:{g:{h:{i:{if(Bi=ri){if(!(Ai=ui))break i;break h}return dc((ri=a)-w(a=(a>>>0)/(ui>>>0)|0,ui)|0,0),M=0,a}if(!a)break g;break f}if(!((Di=Ai+-1|0)&Ai))break e;Ei=0-(Di=(z(Ai)+33|0)-z(Bi)|0)|0;break c}return dc(0,Bi-w(a=(Bi>>>0)/0|0,0)|0),M=0,a}if((Ai=32-z(Bi)|0)>>>0<31)break d;break b}if(dc(a&Di,0),1==(0|Ai))break a;return ui=31&(Ai=Ai?31-z(Ai+-1^Ai)|0:32),a=32<=(63&Ai)>>>0?(Bi=0,ri>>>ui):(Bi=ri>>>ui,((1<<ui)-1&ri)<<32-ui|a>>>ui),M=Bi,a}Di=Ai+1|0,Ei=63-Ai|0}if(Ai=ri,Ci=31&(Bi=63&Di),Ci=32<=Bi>>>0?(Bi=0,Ai>>>Ci):(Bi=Ai>>>Ci,((1<<Ci)-1&Ai)<<32-Ci|a>>>Ci),Ai=31&(Ei&=63),32<=Ei>>>0?(ri=a<<Ai,a=0):(ri=(1<<Ai)-1&a>>>32-Ai|ri<<Ai,a<<=Ai),Di)for((Ei=ui+(Ai=-1)|0)>>>0<4294967295&&(Ai=0);Ci=(Gi=Fi=Ci<<1|ri>>>31)-(Hi=ui&(Fi=Ai-((Bi=Bi<<1|Ci>>>31)+(Ei>>>0<Fi>>>0)|0)>>31))|0,Bi=Bi-(Gi>>>0<Hi>>>0)|0,ri=ri<<1|a>>>31,a=Ii|a<<1,Ii=Fi&=1,Di=Di+-1|0;);return dc(Ci,Bi),M=ri<<1|a>>>31,Fi|a<<1}dc(a,ri),ri=a=0}return M=ri,a}(a,ri,ui)}function jc(a){var Ji;return(-1>>>(Ji=31&a)&-2)<<Ji|(-1<<(a=0-a&31)&-2)>>>a}function N(){return buffer.byteLength/65536|0}}(H,I,J)}}l=null,b.wasmBinary&&(F=b.wasmBinary);var fa=Error,WebAssembly={};F=[],"object"!=typeof WebAssembly&&E("no native wasm support detected");var I,J=new function(a){var c=Array(17);return c.grow=function(){18<=c.length&&B("Unable to grow wasm table. Use a higher value for RESERVED_FUNCTION_POINTERS or set ALLOW_TABLE_GROWTH."),c.push(null)},c.set=function(a,e){c[a]=e},c.get=function(a){return c[a]},c}({initial:17,maximum:18,element:"anyfunc"}),K=!1;function assert(a,c){a||B("Assertion failed: "+c)}var buffer,M,L,N,ia="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function ja(a,c,d){var e=c+d;for(d=c;a[d]&&!(e<=d);)++d;if(16<d-c&&a.subarray&&ia)return ia.decode(a.subarray(c,d));for(e="";c<d;){var f=a[c++];if(128&f){var g=63&a[c++];if(192==(224&f))e+=String.fromCharCode((31&f)<<6|g);else{var m=63&a[c++];(f=224==(240&f)?(15&f)<<12|g<<6|m:(7&f)<<18|g<<12|m<<6|63&a[c++])<65536?e+=String.fromCharCode(f):(f-=65536,e+=String.fromCharCode(55296|f>>10,56320|1023&f))}}else e+=String.fromCharCode(f)}return e}function ka(a,c){return a?ja(L,a,c):""}function la(a){return 0<a%65536&&(a+=65536-a%65536),a}function ma(a){buffer=a,b.HEAP8=M=new Int8Array(a),b.HEAP16=new Int16Array(a),b.HEAP32=N=new Int32Array(a),b.HEAPU8=L=new Uint8Array(a),b.HEAPU16=new Uint16Array(a),b.HEAPU32=new Uint32Array(a),b.HEAPF32=new Float32Array(a),b.HEAPF64=new Float64Array(a)}"undefined"!=typeof TextDecoder&&new TextDecoder("utf-16le");var G=b.TOTAL_MEMORY||16777216;function O(a){for(;0<a.length;){var c=a.shift();if("function"==typeof c)c();else{var d=c.X;"number"==typeof d?void 0===c.W?b.dynCall_v(d):b.dynCall_vi(d,c.W):d(void 0===c.W?null:c.W)}}}(I=b.wasmMemory?b.wasmMemory:new function(){return{buffer:new ArrayBuffer(G/65536*65536),grow:function(a){return ca(a)}}})&&(buffer=I.buffer),G=buffer.byteLength,ma(buffer),N[2052]=5251248;var na=[],oa=[],pa=[],qa=[];function ra(){var a=b.preRun.shift();na.unshift(a)}if(Math.imul&&-5===Math.imul(4294967295,5)||(Math.imul=function(a,c){var d=65535&a,e=65535&c;return d*e+((a>>>16)*e+d*(c>>>16)<<16)|0}),!Math.fround){var sa=new Float32Array(1);Math.fround=function(a){return sa[0]=a,sa[0]}}Math.clz32||(Math.clz32=function(a){var c=32,d=a>>16;return d&&(c-=16,a=d),(d=a>>8)&&(c-=8,a=d),(d=a>>4)&&(c-=4,a=d),(d=a>>2)&&(c-=2,a=d),a>>1?c-2:c-a}),Math.trunc||(Math.trunc=function(a){return a<0?Math.ceil(a):Math.floor(a)});var P=0,Q=null,U=null;function B(a){throw b.onAbort&&b.onAbort(a),D(a),E(a),K=!0,new fa("abort("+a+"). Build with -s ASSERTIONS=1 for more info.")}b.preloadedImages={},b.preloadedAudios={};var V="data:application/octet-stream;base64,";function W(a){return String.prototype.startsWith?a.startsWith(V):0===a.indexOf(V)}var X="_em_module.wasm";if(!W(X)){var ta=X;X=b.locateFile?b.locateFile(ta,u):u+ta}function ua(){try{if(F)return new Uint8Array(F);var a=z(X);if(a)return a;if(w)return w(X);throw"both async and sync fetching of the wasm failed"}catch(c){B(c)}}oa.push({X:function(){wa()}});var xa=[null,[],[]],ya=!1;function C(a){for(var c=[],d=0;d<a.length;d++){var e=a[d];255<e&&(ya&&assert(!1,"Character code "+e+" ("+String.fromCharCode(e)+")  at offset "+d+" not in 0x00-0xFF."),e&=255),c.push(String.fromCharCode(e))}return c.join("")}var za="function"==typeof atob?atob:function(a){var c="",d=0;a=a.replace(/[^A-Za-z0-9\+\/=]/g,"");do{var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(a.charAt(d++)),f="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(a.charAt(d++)),g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(a.charAt(d++)),m="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(a.charAt(d++));e=e<<2|f>>4,f=(15&f)<<4|g>>2;var h=(3&g)<<6|m;c+=String.fromCharCode(e),64!==g&&(c+=String.fromCharCode(f)),64!==m&&(c+=String.fromCharCode(h))}while(d<a.length);return c};function z(a){if(W(a)){if(a=a.slice(V.length),"boolean"==typeof r&&r){try{var c=Buffer.from(a,"base64")}catch(g){c=new Buffer(a,"base64")}var d=new Uint8Array(c.buffer,c.byteOffset,c.byteLength)}else try{var e=za(a),f=new Uint8Array(e.length);for(c=0;c<e.length;++c)f[c]=e.charCodeAt(c);d=f}catch(g){throw Error("Converting base64 string to bytes failed.")}return d}}var H={a:function(a,c,d){L.set(L.subarray(c,c+d),a)},b:function(a){if(2147418112<a)return!1;for(var c=Math.max(M.length,16777216);c<a;)c=c<=536870912?la(2*c):Math.min(la((3*c+2147483648)/4),2147418112);a:{try{I.grow(c-buffer.byteLength+65535>>16),ma(I.buffer);var d=1;break a}catch(e){}d=void 0}return!!d},c:function(a,c,d,e){try{for(var f=0,g=0;g<d;g++){for(var m=N[c+8*g>>2],h=N[c+(8*g+4)>>2],A=0;A<h;A++){var R=L[m+A],S=xa[a];0===R||10===R?((1===a?D:E)(ja(S,0)),S.length=0):S.push(R)}f+=h}return N[e>>2]=f,0}catch(T){return"undefined"!=typeof FS&&T instanceof FS.Y||B(T),T.Z}},memory:I,table:J},Aa=function(){function a(a){b.asm=a.exports,P--,b.monitorRunDependencies&&b.monitorRunDependencies(P),0==P&&(null!==Q&&(clearInterval(Q),Q=null),U&&(a=U,U=null,a()))}function c(c){a(c.instance)}function d(a){return(F||!p&&!q||"function"!=typeof fetch?new Promise(function(a){a(ua())}):fetch(X,{credentials:"same-origin"}).then(function(a){if(!a.ok)throw"failed to load wasm binary file at '"+X+"'";return a.arrayBuffer()}).catch(function(){return ua()})).then(function(){return{then:function(a){a({instance:new da})}}}).then(a,function(a){E("failed to asynchronously prepare wasm: "+a),B(a)})}var e={env:H,wasi_unstable:H};if(P++,b.monitorRunDependencies&&b.monitorRunDependencies(P),b.instantiateWasm)try{return b.instantiateWasm(e,a)}catch(f){return E("Module.instantiateWasm callback failed with error: "+f),!1}return function(){if(F||"function"!=typeof WebAssembly.instantiateStreaming||W(X)||"function"!=typeof fetch)return d(c);fetch(X,{credentials:"same-origin"}).then(function(a){return WebAssembly.instantiateStreaming(a,e).then(c,function(a){E("wasm streaming compile failed: "+a),E("falling back to ArrayBuffer instantiation"),d(c)})})}(),{}}();b.asm=Aa;var wa=b.___wasm_call_ctors=function(){return b.asm.d.apply(null,arguments)};b._csmGetVersion=function(){return b.asm.e.apply(null,arguments)},b._csmGetLatestMocVersion=function(){return b.asm.f.apply(null,arguments)},b._csmGetMocVersion=function(){return b.asm.g.apply(null,arguments)},b._csmSetLogFunction=function(){return b.asm.h.apply(null,arguments)},b._csmReviveMocInPlace=function(){return b.asm.i.apply(null,arguments)},b._csmReadCanvasInfo=function(){return b.asm.j.apply(null,arguments)},b._csmGetSizeofModel=function(){return b.asm.k.apply(null,arguments)},b._csmInitializeModelInPlace=function(){return b.asm.l.apply(null,arguments)},b._csmUpdateModel=function(){return b.asm.m.apply(null,arguments)},b._csmGetParameterCount=function(){return b.asm.n.apply(null,arguments)},b._csmGetParameterIds=function(){return b.asm.o.apply(null,arguments)},b._csmGetParameterMinimumValues=function(){return b.asm.p.apply(null,arguments)},b._csmGetParameterMaximumValues=function(){return b.asm.q.apply(null,arguments)},b._csmGetParameterDefaultValues=function(){return b.asm.r.apply(null,arguments)},b._csmGetParameterValues=function(){return b.asm.s.apply(null,arguments)},b._csmGetPartCount=function(){return b.asm.t.apply(null,arguments)},b._csmGetPartIds=function(){return b.asm.u.apply(null,arguments)},b._csmGetPartOpacities=function(){return b.asm.v.apply(null,arguments)},b._csmGetPartParentPartIndices=function(){return b.asm.w.apply(null,arguments)},b._csmGetDrawableCount=function(){return b.asm.x.apply(null,arguments)},b._csmGetDrawableIds=function(){return b.asm.y.apply(null,arguments)},b._csmGetDrawableConstantFlags=function(){return b.asm.z.apply(null,arguments)},b._csmGetDrawableDynamicFlags=function(){return b.asm.A.apply(null,arguments)},b._csmGetDrawableTextureIndices=function(){return b.asm.B.apply(null,arguments)},b._csmGetDrawableDrawOrders=function(){return b.asm.C.apply(null,arguments)},b._csmGetDrawableRenderOrders=function(){return b.asm.D.apply(null,arguments)},b._csmGetDrawableOpacities=function(){return b.asm.E.apply(null,arguments)},b._csmGetDrawableMaskCounts=function(){return b.asm.F.apply(null,arguments)},b._csmGetDrawableMasks=function(){return b.asm.G.apply(null,arguments)},b._csmGetDrawableVertexCounts=function(){return b.asm.H.apply(null,arguments)},b._csmGetDrawableVertexPositions=function(){return b.asm.I.apply(null,arguments)},b._csmGetDrawableVertexUvs=function(){return b.asm.J.apply(null,arguments)},b._csmGetDrawableIndexCounts=function(){return b.asm.K.apply(null,arguments)},b._csmGetDrawableIndices=function(){return b.asm.L.apply(null,arguments)},b._csmResetDrawableDynamicFlags=function(){return b.asm.M.apply(null,arguments)},b._csmMallocMoc=function(){return b.asm.N.apply(null,arguments)},b._csmMallocModelAndInitialize=function(){return b.asm.O.apply(null,arguments)},b._csmMalloc=function(){return b.asm.P.apply(null,arguments)},b._csmFree=function(){return b.asm.Q.apply(null,arguments)};var Y,Ba=b.stackSave=function(){return b.asm.R.apply(null,arguments)},Ca=b.stackAlloc=function(){return b.asm.S.apply(null,arguments)},Da=b.stackRestore=function(){return b.asm.T.apply(null,arguments)},ca=b.__growWasmMemory=function(){return b.asm.U.apply(null,arguments)};function Z(){function a(){if(!Y&&(Y=!0,!K)){if(O(oa),O(pa),b.onRuntimeInitialized&&b.onRuntimeInitialized(),b.postRun)for("function"==typeof b.postRun&&(b.postRun=[b.postRun]);b.postRun.length;){var a=b.postRun.shift();qa.unshift(a)}O(qa)}}if(!(0<P)){if(b.preRun)for("function"==typeof b.preRun&&(b.preRun=[b.preRun]);b.preRun.length;)ra();O(na),0<P||(b.setStatus?(b.setStatus("Running..."),setTimeout(function(){setTimeout(function(){b.setStatus("")},1),a()},1)):a())}}if(b.dynCall_vi=function(){return b.asm.V.apply(null,arguments)},b.asm=Aa,b.ccall=function(a,c,d,e){var f={string:function(a){var c=0;if(null!=a&&0!==a){var d=1+(a.length<<2),e=c=Ca(d),f=L;if(0<d){d=e+d-1;for(var g=0;g<a.length;++g){var k=a.charCodeAt(g);if(55296<=k&&k<=57343)k=65536+((1023&k)<<10)|1023&a.charCodeAt(++g);if(k<=127){if(d<=e)break;f[e++]=k}else{if(k<=2047){if(d<=e+1)break;f[e++]=192|k>>6}else{if(k<=65535){if(d<=e+2)break;f[e++]=224|k>>12}else{if(d<=e+3)break;f[e++]=240|k>>18,f[e++]=128|k>>12&63}f[e++]=128|k>>6&63}f[e++]=128|63&k}}f[e]=0}}return c},array:function(a){var c=Ca(a.length);return M.set(a,c),c}},g=function(a){var c=b["_"+a];return assert(c,"Cannot call unknown function "+a+", make sure it is exported"),c}(a),m=[];if(a=0,e)for(var h=0;h<e.length;h++){var A=f[d[h]];A?(0===a&&(a=Ba()),m[h]=A(e[h])):m[h]=e[h]}return d=function(a){return"string"===c?ka(a):"boolean"===c?!!a:a}(d=g.apply(null,m)),0!==a&&Da(a),d},b.UTF8ToString=ka,b.addFunction=function(a,c){var d=J.length;try{J.grow(1)}catch(e){if(!e instanceof RangeError)throw e;throw"Unable to grow wasm table. Use a higher value for RESERVED_FUNCTION_POINTERS or set ALLOW_TABLE_GROWTH."}try{J.set(d,a)}catch(e){if(!e instanceof TypeError)throw e;assert(void 0!==c,"Missing signature argument to addFunction"),J.set(d,a)}return d},b.then=function(a){if(Y)a(b);else{var c=b.onRuntimeInitialized;b.onRuntimeInitialized=function(){c&&c(),a(b)}}return b},U=function Ea(){Y||Z(),Y||(U=Ea)},b.run=Z,b.preInit)for("function"==typeof b.preInit&&(b.preInit=[b.preInit]);0<b.preInit.length;)b.preInit.pop()();return Z(),_em_module}}();"object"==typeof exports&&"object"==typeof module?module.exports=_em_module:"function"==typeof define&&define.amd?define([],function(){return _em_module}):"object"==typeof exports&&(exports._em_module=_em_module);var _em = _em_module();
})(Live2DCubismCore || (Live2DCubismCore = {}));
//# sourceMappingURL=live2dcubismcore.js.map