/**
 * Copyright(c) Live2D Inc. All rights reserved.
 *
 * Use of this source code is governed by the Live2D Open Software license
 * that can be found at https://www.live2d.com/eula/live2d-open-software-license-agreement_en.html.
 */

/**
 * @brief パラメータIDのデフォルト値を保持する定数<br>
 *         デフォルト値の仕様は以下のマニュアルに基づく<br>
 *         https://docs.live2d.com/cubism-editor-manual/standard-parametor-list/
 */
export namespace Live2DCubismFramework
{
    // パーツID
    export const HitAreaPrefix: string = "HitArea";
    export const HitAreaHead: string = "Head";
    export const HitAreaBody: string = "Body";
    export const PartsIdCore: string = "Parts01Core";
    export const PartsArmPrefix: string = "Parts01Arm_";
    export const PartsArmLPrefix: string = "Parts01ArmL_";
    export const PartsArmRPrefix: string = "Parts01ArmR_";

    // パラメータID
    export const ParamAngleX: string = "ParamAngleX";
    export const ParamAngleY: string = "ParamAngleY";
    export const ParamAngleZ: string = "ParamAngleZ";
    export const ParamEyeLOpen: string = "ParamEyeLOpen";
    export const ParamEyeLSmile: string = "ParamEyeLSmile";
    export const ParamEyeROpen: string = "ParamEyeROpen";
    export const ParamEyeRSmile: string = "ParamEyeRSmile";
    export const ParamEyeBallX: string = "ParamEyeBallX";
    export const ParamEyeBallY: string = "ParamEyeBallY";
    export const ParamEyeBallForm: string = "ParamEyeBallForm";
    export const ParamBrowLY: string = "ParamBrowLY";
    export const ParamBrowRY: string = "ParamBrowRY";
    export const ParamBrowLX: string = "ParamBrowLX";
    export const ParamBrowRX: string = "ParamBrowRX";
    export const ParamBrowLAngle: string = "ParamBrowLAngle";
    export const ParamBrowRAngle: string = "ParamBrowRAngle";
    export const ParamBrowLForm: string = "ParamBrowLForm";
    export const ParamBrowRForm: string = "ParamBrowRForm";
    export const ParamMouthForm: string = "ParamMouthForm";
    export const ParamMouthOpenY: string = "ParamMouthOpenY";
    export const ParamCheek: string = "ParamCheek";
    export const ParamBodyAngleX: string = "ParamBodyAngleX";
    export const ParamBodyAngleY: string = "ParamBodyAngleY";
    export const ParamBodyAngleZ: string = "ParamBodyAngleZ";
    export const ParamBreath: string = "ParamBreath";
    export const ParamArmLA: string = "ParamArmLA";
    export const ParamArmRA: string = "ParamArmRA";
    export const ParamArmLB: string = "ParamArmLB";
    export const ParamArmRB: string = "ParamArmRB";
    export const ParamHandL: string = "ParamHandL";
    export const ParamHandR: string = "ParamHandR";
    export const ParamHairFront: string = "ParamHairFront";
    export const ParamHairSide: string = "ParamHairSide";
    export const ParamHairBack: string = "ParamHairBack";
    export const ParamHairFluffy: string = "ParamHairFluffy";
    export const ParamShoulderY: string = "ParamShoulderY";
    export const ParamBustX: string = "ParamBustX";
    export const ParamBustY: string = "ParamBustY";
    export const ParamBaseX: string = "ParamBaseX";
    export const ParamBaseY: string = "ParamBaseY";
    export const ParamNONE: string = "NONE:";
}
