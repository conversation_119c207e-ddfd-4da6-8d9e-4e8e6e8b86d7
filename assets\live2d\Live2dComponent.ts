import Live2dDelegate from "./src/LAppDelegate";
import Live2dDefine from "./src/LAppDefine";
import Live2dModel from "./src/LAppModel";

const { ccclass, property } = cc._decorator;

@ccclass
export default class Live2dComponent extends cc.Component {

    public static get EvtMotionFinish(): string { return "Live2d动作结束" }

    @property({ type: cc.RenderTexture })
    renderTexture: cc.RenderTexture = null

    @property()
    _scale: number = 3.3
    @property()
    get scale(): number { return this._scale }
    set scale(s: number) { this._scale = s; if (this.live2d != null) { this.live2d.scale = this.scale } }

    @property({ tooltip: "模型位置偏移X" })
    offsetX: number = 0

    @property({ tooltip: "模型位置偏移Y" })
    offsetY: number = 0

    @property({ tooltip: "针对特定模型的缩放调整" })
    modelScaleAdjust: number = 1.0

    @property()
    fps: number = 30

    @property()
    clearColor: cc.Color = cc.Color.TRANSPARENT

    @property({ tooltip: "是否循环Idle动作" })
    loopIdleMotion: boolean = true

    @property()
    modelName: string = ""

    live2d: Live2dDelegate = null;
    private _sprite: cc.Sprite = null;
    private _frameBuffer: WebGLFramebuffer = null;

    /** 当前动作，这里设定为除了Idle组，其他动作都只播一次，播完后currentMotion为null */
    currentMotion: string = null;

    stepTime: number = 1 / this.fps;

    public get width(): number { return this.node.width }
    public get height(): number { return this.node.height }
    public get gl(): WebGLRenderingContext { return this.renderTexture["_texture"]._device._gl }
    public get frameBuffer(): WebGLFramebuffer { return this._frameBuffer }
    // public get stepTime(): number { return 1 / this.fps }

    /** 动作结束的回调 */
    motionEndCall: (model: Live2dModel) => void = null;

    onLoad() {
        if (this.renderTexture == null) {
            this.renderTexture = new cc.RenderTexture()
            this.renderTexture.initWithSize(this.node.width, this.node.height)
            this.renderTexture.setFlipY(true)

            this._sprite = this.node.addComponent(cc.Sprite)
            this._sprite.spriteFrame = new cc.SpriteFrame(this.renderTexture)
        }

        let gl = this.gl;

        this._frameBuffer = gl.createFramebuffer()
        gl.bindFramebuffer(gl.FRAMEBUFFER, this._frameBuffer)
        const attachmentPoint = gl.COLOR_ATTACHMENT0;
        gl.framebufferTexture2D(gl.FRAMEBUFFER, attachmentPoint, gl.TEXTURE_2D, this.renderTexture["_texture"]._glID, 0)
        gl.bindFramebuffer(gl.FRAMEBUFFER, null);
        //this._frameBuffer = this.renderTexture["_framebuffer"]._glID
        this.stepTime = 1 / this.fps;

        if (this.modelName) {
            this.loadModel(this.modelName);
        }

        this.node.on(Live2dComponent.EvtMotionFinish, (model: Live2dModel) => {
            if (this.motionEndCall) {
                this.motionEndCall.call(this, model);
            }
        });
    }

    setMotionEndCall(call: (model: Live2dModel) => void): Live2dComponent {
        this.motionEndCall = call;
        return this;
    }

    startMotion(group: string, num: number, priority: number): Live2dComponent {
        if (this.currentMotion && !this.currentMotion.startsWith('Idle')) {
            console.log('动作正在进行:', this.currentMotion);
            return this;
        }
        this.currentMotion = group + '_' + num;
        this.live2d.getModel(0).startMotion(group, num, priority);
        return this;
    }

    setExpression(expression: string): Live2dComponent {
        this.live2d.getModel(0).setExpression(expression);
        return this;
    }

    /**
     * 动态调整模型位置和缩放
     * @param offsetX X轴偏移
     * @param offsetY Y轴偏移
     * @param scaleAdjust 缩放调整
     */
    adjustModel(offsetX?: number, offsetY?: number, scaleAdjust?: number): Live2dComponent {
        if (offsetX !== undefined) this.offsetX = offsetX;
        if (offsetY !== undefined) this.offsetY = offsetY;
        if (scaleAdjust !== undefined) this.modelScaleAdjust = scaleAdjust;

        // 重新应用调整
        this.applyModelAdjustments();
        return this;
    }

    loadModel(modelName: string) {
        this.modelName = modelName;
        this.live2d = new Live2dDelegate(this.gl, this.width, this.height, this.frameBuffer)
        this.live2d.loadModel(this.modelName)

        // 应用缩放和位置调整
        this.applyModelAdjustments();

        let self = this;
        this.live2d.onFinishMotion = (): boolean => {
            if (self.currentMotion && !self.currentMotion.startsWith('Idle')) {
                self.currentMotion = null;
            }
            self.node.emit(Live2dComponent.EvtMotionFinish);
            return self.loopIdleMotion;
        }
    }

    /**
     * 模型特定的调整配置
     */
    private static MODEL_ADJUSTMENTS = {
        'dafeng_7': {
            scaleAdjust: 0.95,
            offsetX: -15,
            offsetY: 8,
            // 手部物品位置修正参数
            handItemCorrection: {
                'ParamHandLAngle': -0.05,      // 左手角度微调
                'ParamHandLChange': 0.1,       // 左手变化参数
                'ParamHandLAnimation': -0.02,  // 左手动画参数
                'ParamHandRAngle': -0.03,      // 右手角度微调
                'ParamHandRChange': 0.08,      // 右手变化参数
                'ParamHandRAnimation': -0.015  // 右手动画参数
            }
        },
        'ankeleiqi_2_hx': {
            scaleAdjust: 1.0,
            offsetX: 0,
            offsetY: 0,
            handItemCorrection: {}
        }
        // 可以继续添加其他模型的配置
    };

    /**
     * 应用模型调整参数
     */
    private applyModelAdjustments() {
        if (!this.live2d) return;

        // 获取模型特定的调整配置
        const modelConfig = Live2dComponent.MODEL_ADJUSTMENTS[this.modelName];

        let finalScale = this.scale * this.modelScaleAdjust;
        let finalOffsetX = this.offsetX;
        let finalOffsetY = this.offsetY;

        // 如果有模型特定配置且用户没有手动设置偏移，则使用默认配置
        if (modelConfig) {
            finalScale *= modelConfig.scaleAdjust;
            if (this.offsetX === 0) finalOffsetX = modelConfig.offsetX;
            if (this.offsetY === 0) finalOffsetY = modelConfig.offsetY;
        }

        this.live2d.scale = finalScale;
        this.live2d.offsetX = finalOffsetX;
        this.live2d.offsetY = finalOffsetY;

        console.log(`Live2D模型 ${this.modelName} 调整参数:`, {
            scale: finalScale,
            offsetX: finalOffsetX,
            offsetY: finalOffsetY
        });
    }

    private _deltaTime: number = 0
    update(dt: number) {
        if (!this.modelName) {
            return;
        }
        this._deltaTime += dt
        let draw = false
        if (this._deltaTime > this.stepTime) {
            draw = true
            this._deltaTime -= this.stepTime
            this.live2d.loop()
        }
        //if (this._deltaTime < 0) { this._deltaTime = 0 }
        if (draw) {
            //TODO: 这里需要重置渲染状态,不知道怎么避免(估计要参考相机的render)
            let device = this.renderTexture["_texture"]._device
            device._current.reset()
            device._gl.viewport(device._vx, device._vy, device._vw, device._vh)
        }
    }

    onDestroy() {
        if (this.live2d) {
            this.live2d.release()
        }
    }
}