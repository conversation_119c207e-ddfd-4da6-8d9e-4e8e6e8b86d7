import Live2dDelegate from "./src/LAppDelegate";
import Live2dDefine from "./src/LAppDefine";
import Live2dModel from "./src/LAppModel";

const { ccclass, property } = cc._decorator;

@ccclass
export default class Live2dComponent extends cc.Component {

    public static get EvtMotionFinish(): string { return "Live2d动作结束" }

    @property({ type: cc.RenderTexture })
    renderTexture: cc.RenderTexture = null

    @property()
    _scale: number = 3.3
    @property()
    get scale(): number { return this._scale }
    set scale(s: number) { this._scale = s; if (this.live2d != null) { this.live2d.scale = this.scale } }

    @property()
    fps: number = 30

    @property()
    clearColor: cc.Color = cc.Color.TRANSPARENT

    @property({ tooltip: "是否循环Idle动作" })
    loopIdleMotion: boolean = true

    @property()
    modelName: string = ""

    live2d: Live2dDelegate = null;
    private _sprite: cc.Sprite = null;
    private _frameBuffer: WebGLFramebuffer = null;

    /** 当前动作，这里设定为除了Idle组，其他动作都只播一次，播完后currentMotion为null */
    currentMotion: string = null;

    stepTime: number = 1 / this.fps;

    public get width(): number { return this.node.width }
    public get height(): number { return this.node.height }
    public get gl(): WebGLRenderingContext { return this.renderTexture["_texture"]._device._gl }
    public get frameBuffer(): WebGLFramebuffer { return this._frameBuffer }
    // public get stepTime(): number { return 1 / this.fps }

    /** 动作结束的回调 */
    motionEndCall: (model: Live2dModel) => void = null;

    onLoad() {
        if (this.renderTexture == null) {
            this.renderTexture = new cc.RenderTexture()
            this.renderTexture.initWithSize(this.node.width, this.node.height)
            this.renderTexture.setFlipY(true)

            this._sprite = this.node.addComponent(cc.Sprite)
            this._sprite.spriteFrame = new cc.SpriteFrame(this.renderTexture)
        }

        let gl = this.gl;

        this._frameBuffer = gl.createFramebuffer()
        gl.bindFramebuffer(gl.FRAMEBUFFER, this._frameBuffer)
        const attachmentPoint = gl.COLOR_ATTACHMENT0;
        gl.framebufferTexture2D(gl.FRAMEBUFFER, attachmentPoint, gl.TEXTURE_2D, this.renderTexture["_texture"]._glID, 0)
        gl.bindFramebuffer(gl.FRAMEBUFFER, null);
        //this._frameBuffer = this.renderTexture["_framebuffer"]._glID
        this.stepTime = 1 / this.fps;

        if (this.modelName) {
            this.loadModel(this.modelName);
        }

        this.node.on(Live2dComponent.EvtMotionFinish, (model: Live2dModel) => {
            if (this.motionEndCall) {
                this.motionEndCall.call(this, model);
            }
        });
    }

    setMotionEndCall(call: (model: Live2dModel) => void): Live2dComponent {
        this.motionEndCall = call;
        return this;
    }

    startMotion(group: string, num: number, priority: number): Live2dComponent {
        if (this.currentMotion && !this.currentMotion.startsWith('Idle')) {
            console.log('动作正在进行:', this.currentMotion);
            return this;
        }
        this.currentMotion = group + '_' + num;
        this.live2d.getModel(0).startMotion(group, num, priority);
        return this;
    }

    setExpression(expression: string): Live2dComponent {
        this.live2d.getModel(0).setExpression(expression);
        return this;
    }

    loadModel(modelName: string) {
        this.modelName = modelName;
        this.live2d = new Live2dDelegate(this.gl, this.width, this.height, this.frameBuffer)
        this.live2d.loadModel(this.modelName)
        this.live2d.scale = this.scale;

        let self = this;
        this.live2d.onFinishMotion = (): boolean => {
            if (self.currentMotion && !self.currentMotion.startsWith('Idle')) {
                self.currentMotion = null;
            }
            self.node.emit(Live2dComponent.EvtMotionFinish);
            return self.loopIdleMotion;
        }
    }

    private _deltaTime: number = 0
    update(dt: number) {
        if (!this.modelName) {
            return;
        }
        this._deltaTime += dt
        let draw = false
        if (this._deltaTime > this.stepTime) {
            draw = true
            this._deltaTime -= this.stepTime
            this.live2d.loop()
        }
        //if (this._deltaTime < 0) { this._deltaTime = 0 }
        if (draw) {
            //TODO: 这里需要重置渲染状态,不知道怎么避免(估计要参考相机的render)
            let device = this.renderTexture["_texture"]._device
            device._current.reset()
            device._gl.viewport(device._vx, device._vy, device._vw, device._vh)
        }
    }

    onDestroy() {
        if (this.live2d) {
            this.live2d.release()
        }
    }
}