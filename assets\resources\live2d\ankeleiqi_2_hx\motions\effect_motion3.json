{"Version": 3, "Meta": {"Duration": 40, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 48, "TotalSegmentCount": 1113, "TotalPointCount": 2997, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Param86", "Segments": [0, 3.9, 0, 13.033, 10, 2, 13.067, 0, 1, 15.989, 0, 18.911, 0, 21.833, 0, 0, 34.867, 10, 2, 34.9, 0, 1, 36.6, 0, 38.3, 0, 40, 0]}, {"Target": "Parameter", "Id": "Param88", "Segments": [0, 5, 1, 0.333, 5, 0.667, -5, 1, -5, 1, 1.333, -5, 1.667, 5, 2, 5, 1, 2.333, 5, 2.667, -5, 3, -5, 1, 3.333, -5, 3.667, 5, 4, 5, 1, 4.333, 5, 4.667, -5, 5, -5, 1, 5.333, -5, 5.667, 5, 6, 5, 1, 6.333, 5, 6.667, -5, 7, -5, 1, 7.333, -5, 7.667, 5, 8, 5, 1, 8.333, 5, 8.667, -5, 9, -5, 1, 9.333, -5, 9.667, 5, 10, 5, 1, 10.333, 5, 10.667, -5, 11, -5, 1, 11.333, -5, 11.667, 5, 12, 5, 1, 12.333, 5, 12.667, -5, 13, -5, 1, 13.333, -5, 13.667, 5, 14, 5, 1, 14.333, 5, 14.667, -5, 15, -5, 1, 15.333, -5, 15.667, 5, 16, 5, 1, 16.333, 5, 16.667, -5, 17, -5, 1, 17.333, -5, 17.667, 5, 18, 5, 1, 18.333, 5, 18.667, -5, 19, -5, 1, 19.333, -5, 19.667, 5, 20, 5, 1, 20.333, 5, 20.667, -5, 21, -5, 1, 21.333, -5, 21.667, 5, 22, 5, 1, 22.333, 5, 22.667, -5, 23, -5, 1, 23.333, -5, 23.667, 5, 24, 5, 1, 24.333, 5, 24.667, -5, 25, -5, 1, 25.333, -5, 25.667, 5, 26, 5, 1, 26.333, 5, 26.667, -5, 27, -5, 1, 27.333, -5, 27.667, 5, 28, 5, 1, 28.333, 5, 28.667, -5, 29, -5, 1, 29.333, -5, 29.667, 5, 30, 5, 1, 30.333, 5, 30.667, -5, 31, -5, 1, 31.333, -5, 31.667, 5, 32, 5, 1, 32.333, 5, 32.667, -5, 33, -5, 1, 33.333, -5, 33.667, 5, 34, 5, 1, 34.333, 5, 34.667, -5, 35, -5, 1, 35.333, -5, 35.667, 5, 36, 5, 1, 36.333, 5, 36.667, -5, 37, -5, 1, 37.333, -5, 37.667, 5, 38, 5, 1, 38.333, 5, 38.667, -5, 39, -5, 1, 39.333, -5, 39.667, 5, 40, 5]}, {"Target": "Parameter", "Id": "Param89", "Segments": [0, 0, 1, 0.167, 0, 0.333, -5, 0.5, -5, 1, 0.833, -5, 1.167, 5, 1.5, 5, 1, 1.833, 5, 2.167, -5, 2.5, -5, 1, 2.833, -5, 3.167, 5, 3.5, 5, 1, 3.833, 5, 4.167, -5, 4.5, -5, 1, 4.833, -5, 5.167, 5, 5.5, 5, 1, 5.833, 5, 6.167, -5, 6.5, -5, 1, 6.833, -5, 7.167, 5, 7.5, 5, 1, 7.833, 5, 8.167, -5, 8.5, -5, 1, 8.833, -5, 9.167, 5, 9.5, 5, 1, 9.833, 5, 10.167, -5, 10.5, -5, 1, 10.833, -5, 11.167, 5, 11.5, 5, 1, 11.833, 5, 12.167, -5, 12.5, -5, 1, 12.833, -5, 13.167, 5, 13.5, 5, 1, 13.833, 5, 14.167, -5, 14.5, -5, 1, 14.833, -5, 15.167, 5, 15.5, 5, 1, 15.833, 5, 16.167, -5, 16.5, -5, 1, 16.833, -5, 17.167, 5, 17.5, 5, 1, 17.833, 5, 18.167, -5, 18.5, -5, 1, 18.833, -5, 19.167, 5, 19.5, 5, 1, 19.833, 5, 20.167, -5, 20.5, -5, 1, 20.833, -5, 21.167, 5, 21.5, 5, 1, 21.833, 5, 22.167, -5, 22.5, -5, 1, 22.833, -5, 23.167, 5, 23.5, 5, 1, 23.833, 5, 24.167, -5, 24.5, -5, 1, 24.833, -5, 25.167, 5, 25.5, 5, 1, 25.833, 5, 26.167, -5, 26.5, -5, 1, 26.833, -5, 27.167, 5, 27.5, 5, 1, 27.833, 5, 28.167, -5, 28.5, -5, 1, 28.833, -5, 29.167, 5, 29.5, 5, 1, 29.833, 5, 30.167, -5, 30.5, -5, 1, 30.833, -5, 31.167, 5, 31.5, 5, 1, 31.833, 5, 32.167, -5, 32.5, -5, 1, 32.833, -5, 33.167, 5, 33.5, 5, 1, 33.833, 5, 34.167, -5, 34.5, -5, 1, 34.833, -5, 35.167, 5, 35.5, 5, 1, 35.833, 5, 36.167, -5, 36.5, -5, 1, 36.833, -5, 37.167, 5, 37.5, 5, 1, 37.833, 5, 38.167, -5, 38.5, -5, 1, 38.833, -5, 39.167, 5, 39.5, 5, 1, 39.667, 5, 39.833, 0, 40, 0]}, {"Target": "Parameter", "Id": "Param87", "Segments": [0, 0, 1, 0.55, 0, 1.1, 0, 1.65, 0, 0, 14.683, 10, 2, 14.733, 0, 1, 23.156, 0, 31.578, 0, 40, 0]}, {"Target": "Parameter", "Id": "Param91", "Segments": [0, 5, 1, 0.333, 5, 0.667, -5, 1, -5, 1, 1.333, -5, 1.667, 5, 2, 5, 1, 2.333, 5, 2.667, -5, 3, -5, 1, 3.333, -5, 3.667, 5, 4, 5, 1, 4.333, 5, 4.667, -5, 5, -5, 1, 5.333, -5, 5.667, 5, 6, 5, 1, 6.333, 5, 6.667, -5, 7, -5, 1, 7.333, -5, 7.667, 5, 8, 5, 1, 8.333, 5, 8.667, -5, 9, -5, 1, 9.333, -5, 9.667, 5, 10, 5, 1, 10.333, 5, 10.667, -5, 11, -5, 1, 11.333, -5, 11.667, 5, 12, 5, 1, 12.333, 5, 12.667, -5, 13, -5, 1, 13.333, -5, 13.667, 5, 14, 5, 1, 14.333, 5, 14.667, -5, 15, -5, 1, 15.333, -5, 15.667, 5, 16, 5, 1, 16.333, 5, 16.667, -5, 17, -5, 1, 17.333, -5, 17.667, 5, 18, 5, 1, 18.333, 5, 18.667, -5, 19, -5, 1, 19.333, -5, 19.667, 5, 20, 5, 1, 20.333, 5, 20.667, -5, 21, -5, 1, 21.333, -5, 21.667, 5, 22, 5, 1, 22.333, 5, 22.667, -5, 23, -5, 1, 23.333, -5, 23.667, 5, 24, 5, 1, 24.333, 5, 24.667, -5, 25, -5, 1, 25.333, -5, 25.667, 5, 26, 5, 1, 26.333, 5, 26.667, -5, 27, -5, 1, 27.333, -5, 27.667, 5, 28, 5, 1, 28.333, 5, 28.667, -5, 29, -5, 1, 29.333, -5, 29.667, 5, 30, 5, 1, 30.333, 5, 30.667, -5, 31, -5, 1, 31.333, -5, 31.667, 5, 32, 5, 1, 32.333, 5, 32.667, -5, 33, -5, 1, 33.333, -5, 33.667, 5, 34, 5, 1, 34.333, 5, 34.667, -5, 35, -5, 1, 35.333, -5, 35.667, 5, 36, 5, 1, 36.333, 5, 36.667, -5, 37, -5, 1, 37.333, -5, 37.667, 5, 38, 5, 1, 38.333, 5, 38.667, -5, 39, -5, 1, 39.333, -5, 39.667, 5, 40, 5]}, {"Target": "Parameter", "Id": "Param90", "Segments": [0, 0, 1, 0.167, 0, 0.333, -5, 0.5, -5, 1, 0.833, -5, 1.167, 5, 1.5, 5, 1, 1.833, 5, 2.167, -5, 2.5, -5, 1, 2.833, -5, 3.167, 5, 3.5, 5, 1, 3.833, 5, 4.167, -5, 4.5, -5, 1, 4.833, -5, 5.167, 5, 5.5, 5, 1, 5.833, 5, 6.167, -5, 6.5, -5, 1, 6.833, -5, 7.167, 5, 7.5, 5, 1, 7.833, 5, 8.167, -5, 8.5, -5, 1, 8.833, -5, 9.167, 5, 9.5, 5, 1, 9.833, 5, 10.167, -5, 10.5, -5, 1, 10.833, -5, 11.167, 5, 11.5, 5, 1, 11.833, 5, 12.167, -5, 12.5, -5, 1, 12.833, -5, 13.167, 5, 13.5, 5, 1, 13.833, 5, 14.167, -5, 14.5, -5, 1, 14.833, -5, 15.167, 5, 15.5, 5, 1, 15.833, 5, 16.167, -5, 16.5, -5, 1, 16.833, -5, 17.167, 5, 17.5, 5, 1, 17.833, 5, 18.167, -5, 18.5, -5, 1, 18.833, -5, 19.167, 5, 19.5, 5, 1, 19.833, 5, 20.167, -5, 20.5, -5, 1, 20.833, -5, 21.167, 5, 21.5, 5, 1, 21.833, 5, 22.167, -5, 22.5, -5, 1, 22.833, -5, 23.167, 5, 23.5, 5, 1, 23.833, 5, 24.167, -5, 24.5, -5, 1, 24.833, -5, 25.167, 5, 25.5, 5, 1, 25.833, 5, 26.167, -5, 26.5, -5, 1, 26.833, -5, 27.167, 5, 27.5, 5, 1, 27.833, 5, 28.167, -5, 28.5, -5, 1, 28.833, -5, 29.167, 5, 29.5, 5, 1, 29.833, 5, 30.167, -5, 30.5, -5, 1, 30.833, -5, 31.167, 5, 31.5, 5, 1, 31.833, 5, 32.167, -5, 32.5, -5, 1, 32.833, -5, 33.167, 5, 33.5, 5, 1, 33.833, 5, 34.167, -5, 34.5, -5, 1, 34.833, -5, 35.167, 5, 35.5, 5, 1, 35.833, 5, 36.167, -5, 36.5, -5, 1, 36.833, -5, 37.167, 5, 37.5, 5, 1, 37.833, 5, 38.167, -5, 38.5, -5, 1, 38.833, -5, 39.167, 5, 39.5, 5, 1, 39.667, 5, 39.833, 0, 40, 0]}, {"Target": "Parameter", "Id": "Param197", "Segments": [0, 0, 0, 40, 0]}, {"Target": "Parameter", "Id": "Param69", "Segments": [0, 10, 1, 13.333, 10, 26.667, 10, 40, 10]}, {"Target": "Parameter", "Id": "Param68", "Segments": [0, 6.37, 1, 1, 6.37, 2, 2.843, 3, 2.843, 1, 3.889, 2.843, 4.778, 6.369, 5.667, 6.369, 1, 6.689, 6.369, 7.711, 2.843, 8.733, 2.843, 1, 9.578, 2.843, 10.422, 6.369, 11.267, 6.369, 1, 12.289, 6.369, 13.311, 2.843, 14.333, 2.843, 1, 15.233, 2.843, 16.133, 6.369, 17.033, 6.369, 1, 18.044, 6.369, 19.056, 2.843, 20.067, 2.843, 1, 20.944, 2.843, 21.822, 6.369, 22.7, 6.369, 1, 23.722, 6.369, 24.744, 2.843, 25.767, 2.843, 1, 26.656, 2.843, 27.544, 6.369, 28.433, 6.369, 1, 29.456, 6.369, 30.478, 2.843, 31.5, 2.843, 1, 32.378, 2.843, 33.256, 6.369, 34.133, 6.369, 1, 35.178, 6.369, 36.222, 2.843, 37.267, 2.843, 1, 38.178, 2.843, 39.089, 6.37, 40, 6.37]}, {"Target": "Parameter", "Id": "Param76", "Segments": [0, 0, 1, 13.333, 0, 26.667, 0, 40, 0]}, {"Target": "Parameter", "Id": "Param78", "Segments": [0, 1, 1, 13.333, 1, 26.667, 1, 40, 1]}, {"Target": "Parameter", "Id": "Param109", "Segments": [0, -17, 1, 1, -17, 2, 8, 3, 8, 1, 4, 8, 5, -17, 6, -17, 1, 7, -17, 8, 8, 9, 8, 1, 10, 8, 11, -17, 12, -17, 1, 13, -17, 14, 8, 15, 8, 1, 16, 8, 17, -17, 18, -17, 1, 19, -17, 20, 8, 21, 8, 1, 22, 8, 23, -17, 24, -17, 1, 25, -17, 26, 8, 27, 8, 1, 28, 8, 29, -17, 30, -17, 1, 31, -17, 32, 8, 33, 8, 1, 34, 8, 35, -17, 36, -17, 1, 36.822, -17, 37.644, -7.49, 38.467, -7.49, 1, 38.978, -7.49, 39.489, -17, 40, -17]}, {"Target": "Parameter", "Id": "Param110", "Segments": [0, -14, 1, 0.167, -14, 0.333, -15.47, 0.5, -15.47, 1, 0.667, -15.47, 0.833, -8.04, 1, -8.04, 1, 1.167, -8.04, 1.333, -9.588, 1.5, -9.588, 1, 1.667, -9.588, 1.833, 4.2, 2, 4.2, 1, 2.167, 4.2, 2.333, -3.187, 2.5, -3.187, 1, 2.667, -3.187, 2.833, 9, 3, 9, 1, 3.167, 9, 3.333, 1.59, 3.5, 1.59, 1, 3.667, 1.59, 3.833, 1.833, 4, 1.833, 1, 4.167, 1.833, 4.333, -7.092, 4.5, -7.092, 1, 4.667, -7.092, 4.833, -1.992, 5, -1.992, 1, 5.167, -1.992, 5.333, -11.34, 5.5, -12.351, 1, 5.667, -13.361, 5.833, -13.296, 6, -14, 1, 6.167, -14.704, 6.333, -15.47, 6.5, -15.47, 1, 6.667, -15.47, 6.833, -8.04, 7, -8.04, 1, 7.167, -8.04, 7.333, -9.588, 7.5, -9.588, 1, 7.667, -9.588, 7.833, 4.2, 8, 4.2, 1, 8.167, 4.2, 8.333, -3.187, 8.5, -3.187, 1, 8.667, -3.187, 8.833, 9, 9, 9, 1, 9.167, 9, 9.333, 1.59, 9.5, 1.59, 1, 9.667, 1.59, 9.833, 1.833, 10, 1.833, 1, 10.167, 1.833, 10.333, -7.092, 10.5, -7.092, 1, 10.667, -7.092, 10.833, -1.992, 11, -1.992, 1, 11.167, -1.992, 11.333, -11.341, 11.5, -12.351, 1, 11.667, -13.361, 11.833, -13.296, 12, -14, 1, 12.167, -14.704, 12.333, -15.47, 12.5, -15.47, 1, 12.667, -15.47, 12.833, -8.04, 13, -8.04, 1, 13.167, -8.04, 13.333, -9.588, 13.5, -9.588, 1, 13.667, -9.588, 13.833, 4.2, 14, 4.2, 1, 14.167, 4.2, 14.333, -3.187, 14.5, -3.187, 1, 14.667, -3.187, 14.833, 9, 15, 9, 1, 15.167, 9, 15.333, 1.59, 15.5, 1.59, 1, 15.667, 1.59, 15.833, 1.833, 16, 1.833, 1, 16.167, 1.833, 16.333, -7.092, 16.5, -7.092, 1, 16.667, -7.092, 16.833, -1.992, 17, -1.992, 1, 17.167, -1.992, 17.333, -11.341, 17.5, -12.351, 1, 17.667, -13.361, 17.833, -13.296, 18, -14, 1, 18.167, -14.704, 18.333, -15.47, 18.5, -15.47, 1, 18.667, -15.47, 18.833, -8.04, 19, -8.04, 1, 19.167, -8.04, 19.333, -9.588, 19.5, -9.588, 1, 19.667, -9.588, 19.833, 4.2, 20, 4.2, 1, 20.167, 4.2, 20.333, -3.187, 20.5, -3.187, 1, 20.667, -3.187, 20.833, 9, 21, 9, 1, 21.167, 9, 21.333, 1.59, 21.5, 1.59, 1, 21.667, 1.59, 21.833, 1.833, 22, 1.833, 1, 22.167, 1.833, 22.333, -7.092, 22.5, -7.092, 1, 22.667, -7.092, 22.833, -1.992, 23, -1.992, 1, 23.167, -1.992, 23.333, -11.341, 23.5, -12.351, 1, 23.667, -13.361, 23.833, -13.296, 24, -14, 1, 24.167, -14.704, 24.333, -15.47, 24.5, -15.47, 1, 24.667, -15.47, 24.833, -8.04, 25, -8.04, 1, 25.167, -8.04, 25.333, -9.588, 25.5, -9.588, 1, 25.667, -9.588, 25.833, 4.2, 26, 4.2, 1, 26.167, 4.2, 26.333, -3.187, 26.5, -3.187, 1, 26.667, -3.187, 26.833, 9, 27, 9, 1, 27.167, 9, 27.333, 1.59, 27.5, 1.59, 1, 27.667, 1.59, 27.833, 1.833, 28, 1.833, 1, 28.167, 1.833, 28.333, -7.092, 28.5, -7.092, 1, 28.667, -7.092, 28.833, -1.992, 29, -1.992, 1, 29.167, -1.992, 29.333, -11.341, 29.5, -12.351, 1, 29.667, -13.361, 29.833, -13.296, 30, -14, 1, 30.167, -14.704, 30.333, -15.47, 30.5, -15.47, 1, 30.667, -15.47, 30.833, -8.04, 31, -8.04, 1, 31.167, -8.04, 31.333, -9.588, 31.5, -9.588, 1, 31.667, -9.588, 31.833, 4.2, 32, 4.2, 1, 32.167, 4.2, 32.333, -3.187, 32.5, -3.187, 1, 32.667, -3.187, 32.833, 9, 33, 9, 1, 33.167, 9, 33.333, 1.59, 33.5, 1.59, 1, 33.667, 1.59, 33.833, 1.833, 34, 1.833, 1, 34.167, 1.833, 34.333, -7.092, 34.5, -7.092, 1, 34.667, -7.092, 34.833, -1.992, 35, -1.992, 1, 35.167, -1.992, 35.333, -11.341, 35.5, -12.351, 1, 35.667, -13.361, 35.833, -13.296, 36, -14, 1, 36.167, -14.704, 36.333, -15.47, 36.5, -15.47, 1, 36.667, -15.47, 36.833, -8.04, 37, -8.04, 1, 37.167, -8.04, 37.333, -9.588, 37.5, -9.588, 1, 37.667, -9.588, 37.833, 4.2, 38, 4.2, 1, 38.167, 4.2, 38.333, -0.437, 38.5, -3.187, 1, 39, -11.436, 39.5, -14, 40, -14]}, {"Target": "Parameter", "Id": "Param111", "Segments": [0, 10, 1, 0.933, 10, 1.867, -17, 2.8, -17, 1, 3.744, -17, 4.689, 10, 5.633, 10, 1, 6.578, 10, 7.522, -17, 8.467, -17, 1, 9.411, -17, 10.356, 10, 11.3, 10, 1, 12.244, 10, 13.189, -17, 14.133, -17, 1, 15.072, -17, 16.011, 10, 16.95, 10, 1, 17.894, 10, 18.839, -17, 19.783, -17, 1, 20.728, -17, 21.672, 10, 22.617, 10, 1, 23.561, 10, 24.506, -17, 25.45, -17, 1, 26.394, -17, 27.339, 10, 28.283, 10, 1, 29.217, 10, 30.15, -17, 31.083, -17, 1, 32.122, -17, 33.161, 10, 34.2, 10, 1, 35.133, 10, 36.067, -17, 37, -17, 1, 38, -17, 39, 10, 40, 10]}, {"Target": "Parameter", "Id": "Param116", "Segments": [0, -0.97, 1, 0.25, -0.97, 0.5, 12, 0.75, 12, 1, 1.417, 12, 2.083, -29, 2.75, -29, 1, 3.417, -29, 4.083, 12, 4.75, 12, 1, 5.417, 12, 6.083, -29, 6.75, -29, 1, 7.417, -29, 8.083, 12, 8.75, 12, 1, 9.417, 12, 10.083, -29, 10.75, -29, 1, 11.417, -29, 12.083, 12, 12.75, 12, 1, 13.417, 12, 14.083, -29, 14.75, -29, 1, 15.417, -29, 16.083, 12, 16.75, 12, 1, 17.417, 12, 18.083, -29, 18.75, -29, 1, 19.417, -29, 20.083, 12, 20.75, 12, 1, 21.417, 12, 22.083, -29, 22.75, -29, 1, 23.417, -29, 24.083, 12, 24.75, 12, 1, 25.417, 12, 26.083, -29, 26.75, -29, 1, 27.417, -29, 28.083, 12, 28.75, 12, 1, 29.417, 12, 30.083, -29, 30.75, -29, 1, 31.417, -29, 32.083, 12, 32.75, 12, 1, 33.417, 12, 34.083, -29, 34.75, -29, 1, 35.417, -29, 36.083, 12, 36.75, 12, 1, 37.417, 12, 38.083, -29, 38.75, -29, 1, 39.167, -29, 39.583, -0.97, 40, -0.97]}, {"Target": "Parameter", "Id": "Param67", "Segments": [0, 0, 0, 39.983, 10, 2, 40, 0]}, {"Target": "Parameter", "Id": "Param108", "Segments": [0, 0, 0, 39.983, 700, 0, 40, 0]}, {"Target": "Parameter", "Id": "Param92", "Segments": [0, 0, 0, 39.983, 500, 0, 40, 0]}, {"Target": "Parameter", "Id": "Param79", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 1, 0.4, 0, 0.7, 10, 1, 10, 2, 1.033, 0, 1, 1.739, 0, 2.444, 0, 3.15, 0, 1, 3.483, 0, 3.817, 10, 4.15, 10, 2, 4.183, 0, 1, 5.05, 0, 5.917, 0, 6.783, 0, 1, 7.117, 0, 7.45, 10, 7.783, 10, 2, 7.817, 0, 1, 8.767, 0, 9.717, 0, 10.667, 0, 1, 10.967, 0, 11.267, 10, 11.567, 10, 2, 11.6, 0, 1, 12.306, 0, 13.011, 0, 13.717, 0, 1, 14.05, 0, 14.383, 10, 14.717, 10, 2, 14.75, 0, 1, 15.617, 0, 16.483, 0, 17.35, 0, 1, 17.683, 0, 18.017, 10, 18.35, 10, 2, 18.383, 0, 1, 19.283, 0, 20.183, 0, 21.083, 0, 1, 21.383, 0, 21.683, 10, 21.983, 10, 2, 22.017, 0, 1, 22.722, 0, 23.428, 0, 24.133, 0, 1, 24.467, 0, 24.8, 10, 25.133, 10, 2, 25.167, 0, 1, 26.033, 0, 26.9, 0, 27.767, 0, 1, 28.1, 0, 28.433, 10, 28.767, 10, 2, 28.8, 0, 1, 29.489, 0, 30.178, 0, 30.867, 0, 1, 31.167, 0, 31.467, 10, 31.767, 10, 2, 31.8, 0, 1, 32.506, 0, 33.211, 0, 33.917, 0, 1, 34.25, 0, 34.583, 10, 34.917, 10, 2, 34.95, 0, 1, 35.817, 0, 36.683, 0, 37.55, 0, 1, 37.883, 0, 38.217, 10, 38.55, 10, 2, 38.583, 0, 1, 39.056, 0, 39.528, 0, 40, 0]}, {"Target": "Parameter", "Id": "Param82", "Segments": [0, 0, 1, 0.2, 0, 0.4, 0, 0.6, 0, 1, 0.933, 0, 1.267, 10, 1.6, 10, 2, 1.633, 0, 1, 2.5, 0, 3.367, 0, 4.233, 0, 1, 4.567, 0, 4.9, 10, 5.233, 10, 2, 5.267, 0, 1, 6.4, 0, 7.533, 0, 8.667, 0, 1, 9, 0, 9.333, 10, 9.667, 10, 2, 9.7, 0, 1, 10.189, 0, 10.678, 0, 11.167, 0, 1, 11.5, 0, 11.833, 10, 12.167, 10, 2, 12.2, 0, 1, 13.067, 0, 13.933, 0, 14.8, 0, 1, 15.133, 0, 15.467, 10, 15.8, 10, 2, 15.833, 0, 1, 16.967, 0, 18.1, 0, 19.233, 0, 1, 19.567, 0, 19.9, 10, 20.233, 10, 2, 20.267, 0, 1, 20.706, 0, 21.144, 0, 21.583, 0, 1, 21.917, 0, 22.25, 10, 22.583, 10, 2, 22.617, 0, 1, 23.483, 0, 24.35, 0, 25.217, 0, 1, 25.55, 0, 25.883, 10, 26.217, 10, 2, 26.25, 0, 1, 27.383, 0, 28.517, 0, 29.65, 0, 1, 29.983, 0, 30.317, 10, 30.65, 10, 2, 30.683, 0, 1, 30.911, 0, 31.139, 0, 31.367, 0, 1, 31.7, 0, 32.033, 10, 32.367, 10, 2, 32.4, 0, 1, 33.267, 0, 34.133, 0, 35, 0, 1, 35.333, 0, 35.667, 10, 36, 10, 2, 36.033, 0, 1, 37.167, 0, 38.3, 0, 39.433, 0, 1, 39.583, 0, 39.733, 10, 39.883, 10, 2, 39.917, 0, 1, 39.944, 0, 39.972, 0, 40, 0]}, {"Target": "Parameter", "Id": "Param80", "Segments": [0, 0, 1, 0.444, 0, 0.889, 0, 1.333, 0, 1, 1.639, 0, 1.944, 10, 2.25, 10, 2, 2.283, 0, 1, 2.867, 0, 3.45, 0, 4.033, 0, 1, 4.339, 0, 4.644, 10, 4.95, 10, 2, 4.983, 0, 1, 5.85, 0, 6.717, 0, 7.583, 0, 1, 7.889, 0, 8.194, 10, 8.5, 10, 2, 8.533, 0, 1, 9.656, 0, 10.778, 0, 11.9, 0, 1, 12.206, 0, 12.511, 10, 12.817, 10, 2, 12.85, 0, 1, 13.433, 0, 14.017, 0, 14.6, 0, 1, 14.906, 0, 15.211, 10, 15.517, 10, 2, 15.55, 0, 1, 16.417, 0, 17.283, 0, 18.15, 0, 1, 18.456, 0, 18.761, 10, 19.067, 10, 2, 19.1, 0, 1, 20.172, 0, 21.244, 0, 22.317, 0, 1, 22.622, 0, 22.928, 10, 23.233, 10, 2, 23.267, 0, 1, 23.85, 0, 24.433, 0, 25.017, 0, 1, 25.322, 0, 25.628, 10, 25.933, 10, 2, 25.967, 0, 1, 26.833, 0, 27.7, 0, 28.567, 0, 1, 28.872, 0, 29.178, 10, 29.483, 10, 2, 29.517, 0, 1, 30.378, 0, 31.239, 0, 32.1, 0, 1, 32.406, 0, 32.711, 10, 33.017, 10, 2, 33.05, 0, 1, 33.633, 0, 34.217, 0, 34.8, 0, 1, 35.106, 0, 35.411, 10, 35.717, 10, 2, 35.75, 0, 1, 36.617, 0, 37.483, 0, 38.35, 0, 1, 38.656, 0, 38.961, 10, 39.267, 10, 2, 39.3, 0, 1, 39.533, 0, 39.767, 0, 40, 0]}, {"Target": "Parameter", "Id": "Param83", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.578, 0, 0.856, 10, 1.133, 10, 2, 1.167, 0, 1, 1.806, 0, 2.444, 0, 3.083, 0, 1, 3.361, 0, 3.639, 10, 3.917, 10, 2, 3.95, 0, 1, 4.978, 0, 6.006, 0, 7.033, 0, 1, 7.311, 0, 7.589, 10, 7.867, 10, 2, 7.9, 0, 1, 8.889, 0, 9.878, 0, 10.867, 0, 1, 11.144, 0, 11.422, 10, 11.7, 10, 2, 11.733, 0, 1, 12.372, 0, 13.011, 0, 13.65, 0, 1, 13.928, 0, 14.206, 10, 14.483, 10, 2, 14.517, 0, 1, 15.544, 0, 16.572, 0, 17.6, 0, 1, 17.878, 0, 18.156, 10, 18.433, 10, 2, 18.467, 0, 1, 19.406, 0, 20.344, 0, 21.283, 0, 1, 21.561, 0, 21.839, 10, 22.117, 10, 2, 22.15, 0, 1, 22.789, 0, 23.428, 0, 24.067, 0, 1, 24.344, 0, 24.622, 10, 24.9, 10, 2, 24.933, 0, 1, 25.961, 0, 26.989, 0, 28.017, 0, 1, 28.294, 0, 28.572, 10, 28.85, 10, 2, 28.883, 0, 1, 29.611, 0, 30.339, 0, 31.067, 0, 1, 31.344, 0, 31.622, 10, 31.9, 10, 2, 31.933, 0, 1, 32.572, 0, 33.211, 0, 33.85, 0, 1, 34.128, 0, 34.406, 10, 34.683, 10, 2, 34.717, 0, 1, 35.744, 0, 36.772, 0, 37.8, 0, 1, 38.078, 0, 38.356, 10, 38.633, 10, 2, 38.667, 0, 1, 39.111, 0, 39.556, 0, 40, 0]}, {"Target": "Parameter", "Id": "Param81", "Segments": [0, 0, 1, 0.656, 0, 1.311, 0, 1.967, 0, 1, 2.189, 0, 2.411, 10, 2.633, 10, 2, 2.667, 0, 1, 3.25, 0, 3.833, 0, 4.417, 0, 1, 4.656, 0, 4.894, 10, 5.133, 10, 2, 5.167, 0, 1, 5.811, 0, 6.456, 0, 7.1, 0, 1, 7.339, 0, 7.578, 10, 7.817, 10, 2, 7.85, 0, 1, 9.411, 0, 10.972, 0, 12.533, 0, 1, 12.756, 0, 12.978, 10, 13.2, 10, 2, 13.233, 0, 1, 13.817, 0, 14.4, 0, 14.983, 0, 1, 15.222, 0, 15.461, 10, 15.7, 10, 2, 15.733, 0, 1, 16.378, 0, 17.022, 0, 17.667, 0, 1, 17.906, 0, 18.144, 10, 18.383, 10, 2, 18.417, 0, 1, 19.928, 0, 21.439, 0, 22.95, 0, 1, 23.172, 0, 23.394, 10, 23.617, 10, 2, 23.65, 0, 1, 24.233, 0, 24.817, 0, 25.4, 0, 1, 25.639, 0, 25.878, 10, 26.117, 10, 2, 26.15, 0, 1, 26.794, 0, 27.439, 0, 28.083, 0, 1, 28.322, 0, 28.561, 10, 28.8, 10, 2, 28.833, 0, 1, 30.133, 0, 31.433, 0, 32.733, 0, 1, 32.956, 0, 33.178, 10, 33.4, 10, 2, 33.433, 0, 1, 34.017, 0, 34.6, 0, 35.183, 0, 1, 35.422, 0, 35.661, 10, 35.9, 10, 2, 35.933, 0, 1, 36.578, 0, 37.222, 0, 37.867, 0, 1, 38.106, 0, 38.344, 10, 38.583, 10, 2, 38.617, 0, 1, 39.078, 0, 39.539, 0, 40, 0]}, {"Target": "Parameter", "Id": "Param84", "Segments": [0, 0, 1, 0.344, 0, 0.689, 0, 1.033, 0, 1, 1.244, 0, 1.456, 10, 1.667, 10, 2, 1.7, 0, 1, 2.761, 0, 3.822, 0, 4.883, 0, 1, 5.089, 0, 5.294, 10, 5.5, 10, 2, 5.533, 0, 1, 6.506, 0, 7.478, 0, 8.45, 0, 1, 8.661, 0, 8.872, 10, 9.083, 10, 2, 9.117, 0, 1, 9.944, 0, 10.772, 0, 11.6, 0, 1, 11.811, 0, 12.022, 10, 12.233, 10, 2, 12.267, 0, 1, 13.328, 0, 14.389, 0, 15.45, 0, 1, 15.656, 0, 15.861, 10, 16.067, 10, 2, 16.1, 0, 1, 17.072, 0, 18.044, 0, 19.017, 0, 1, 19.228, 0, 19.439, 10, 19.65, 10, 2, 19.683, 0, 1, 20.461, 0, 21.239, 0, 22.017, 0, 1, 22.228, 0, 22.439, 10, 22.65, 10, 2, 22.683, 0, 1, 23.744, 0, 24.806, 0, 25.867, 0, 1, 26.072, 0, 26.278, 10, 26.483, 10, 2, 26.517, 0, 1, 27.489, 0, 28.461, 0, 29.433, 0, 1, 29.644, 0, 29.856, 10, 30.067, 10, 2, 30.1, 0, 1, 30.667, 0, 31.233, 0, 31.8, 0, 1, 32.011, 0, 32.222, 10, 32.433, 10, 2, 32.467, 0, 1, 33.528, 0, 34.589, 0, 35.65, 0, 1, 35.856, 0, 36.061, 10, 36.267, 10, 2, 36.3, 0, 1, 37.272, 0, 38.244, 0, 39.217, 0, 1, 39.428, 0, 39.639, 10, 39.85, 10, 2, 39.883, 0, 1, 39.922, 0, 39.961, 0, 40, 0]}, {"Target": "Parameter", "Id": "Param233", "Segments": [0, 0, 0, 0.983, 10, 2, 1, 0, 0, 1.983, 10, 2, 2, 0, 0, 2.983, 10, 2, 3, 0, 0, 3.983, 10, 2, 4, 0, 0, 4.983, 10, 2, 5, 0, 0, 5.983, 10, 2, 6, 0, 0, 6.983, 10, 2, 7, 0, 0, 7.983, 10, 2, 8, 0, 0, 8.983, 10, 2, 9, 0, 0, 9.983, 10, 2, 10, 0, 0, 10.983, 10, 2, 11, 0, 0, 11.983, 10, 2, 12, 0, 0, 12.983, 10, 2, 13, 0, 0, 13.983, 10, 2, 14, 0, 0, 14.983, 10, 2, 15, 0, 0, 15.983, 10, 2, 16, 0, 0, 16.983, 10, 2, 17, 0, 0, 17.983, 10, 2, 18, 0, 0, 18.983, 10, 2, 19, 0, 0, 19.983, 10, 2, 20, 0, 0, 20.983, 10, 2, 21, 0, 0, 21.983, 10, 2, 22, 0, 0, 22.983, 10, 2, 23, 0, 0, 23.983, 10, 2, 24, 0, 0, 24.983, 10, 2, 25, 0, 0, 25.983, 10, 2, 26, 0, 0, 26.983, 10, 2, 27, 0, 0, 27.983, 10, 2, 28, 0, 0, 28.983, 10, 2, 29, 0, 0, 29.983, 10, 2, 30, 0, 0, 30.983, 10, 2, 31, 0, 0, 31.983, 10, 2, 32, 0, 0, 32.983, 10, 2, 33, 0, 0, 33.983, 10, 2, 34, 0, 0, 34.983, 10, 2, 35, 0, 0, 35.983, 10, 2, 36, 0, 0, 36.983, 10, 2, 37, 0, 0, 37.983, 10, 2, 38, 0, 0, 38.983, 10, 2, 39, 0, 0, 39.983, 10, 2, 40, 0]}, {"Target": "Parameter", "Id": "Param229", "Segments": [0, 0, 1, 13.333, 0, 26.667, 0, 40, 0]}, {"Target": "Parameter", "Id": "Param230", "Segments": [0, 0, 1, 0.25, 0, 0.5, 10, 0.75, 10, 2, 0.767, 0, 1, 1.733, 0, 2.7, 0, 3.667, 0, 1, 3.917, 0, 4.167, 10, 4.417, 10, 2, 4.433, 0, 1, 5.4, 0, 6.367, 0, 7.333, 0, 1, 7.583, 0, 7.833, 10, 8.083, 10, 2, 8.1, 0, 1, 9.206, 0, 10.311, 0, 11.417, 0, 1, 11.667, 0, 11.917, 10, 12.167, 10, 2, 12.183, 0, 1, 13.206, 0, 14.228, 0, 15.25, 0, 1, 15.5, 0, 15.75, 10, 16, 10, 2, 16.017, 0, 1, 17.094, 0, 18.172, 0, 19.25, 0, 1, 19.5, 0, 19.75, 10, 20, 10, 2, 20.017, 0, 1, 21.094, 0, 22.172, 0, 23.25, 0, 1, 23.5, 0, 23.75, 10, 24, 10, 2, 24.017, 0, 1, 25.178, 0, 26.339, 0, 27.5, 0, 1, 27.75, 0, 28, 10, 28.25, 10, 2, 28.267, 0, 1, 29.378, 0, 30.489, 0, 31.6, 0, 1, 31.85, 0, 32.1, 10, 32.35, 10, 2, 32.367, 0, 1, 33.433, 0, 34.5, 0, 35.567, 0, 1, 35.817, 0, 36.067, 10, 36.317, 10, 2, 36.333, 0, 1, 37.556, 0, 38.778, 0, 40, 0]}, {"Target": "Parameter", "Id": "Param231", "Segments": [0, 0, 1, 0.389, 0, 0.778, 0, 1.167, 0, 1, 1.417, 0, 1.667, 10, 1.917, 10, 2, 1.933, 0, 1, 2.9, 0, 3.867, 0, 4.833, 0, 1, 5.083, 0, 5.333, 10, 5.583, 10, 2, 5.6, 0, 1, 6.567, 0, 7.533, 0, 8.5, 0, 1, 8.75, 0, 9, 10, 9.25, 10, 2, 9.267, 0, 1, 10.372, 0, 11.478, 0, 12.583, 0, 1, 12.833, 0, 13.083, 10, 13.333, 10, 2, 13.35, 0, 1, 14.372, 0, 15.394, 0, 16.417, 0, 1, 16.667, 0, 16.917, 10, 17.167, 10, 2, 17.183, 0, 1, 18.261, 0, 19.339, 0, 20.417, 0, 1, 20.667, 0, 20.917, 10, 21.167, 10, 2, 21.183, 0, 1, 22.261, 0, 23.339, 0, 24.417, 0, 1, 24.667, 0, 24.917, 10, 25.167, 10, 2, 25.183, 0, 1, 26.344, 0, 27.506, 0, 28.667, 0, 1, 28.917, 0, 29.167, 10, 29.417, 10, 2, 29.433, 0, 1, 30.544, 0, 31.656, 0, 32.767, 0, 1, 33.017, 0, 33.267, 10, 33.517, 10, 2, 33.533, 0, 1, 34.6, 0, 35.667, 0, 36.733, 0, 1, 36.983, 0, 37.233, 10, 37.483, 10, 2, 37.5, 0, 1, 38.333, 0, 39.167, 0, 40, 0]}, {"Target": "Parameter", "Id": "Param232", "Segments": [0, 0, 1, 0.75, 0, 1.5, 0, 2.25, 0, 1, 2.5, 0, 2.75, 10, 3, 10, 2, 3.017, 0, 1, 3.983, 0, 4.95, 0, 5.917, 0, 1, 6.167, 0, 6.417, 10, 6.667, 10, 2, 6.683, 0, 1, 7.65, 0, 8.617, 0, 9.583, 0, 1, 9.833, 0, 10.083, 10, 10.333, 10, 2, 10.35, 0, 1, 11.456, 0, 12.561, 0, 13.667, 0, 1, 13.917, 0, 14.167, 10, 14.417, 10, 2, 14.433, 0, 1, 15.456, 0, 16.478, 0, 17.5, 0, 1, 17.75, 0, 18, 10, 18.25, 10, 2, 18.267, 0, 1, 19.344, 0, 20.422, 0, 21.5, 0, 1, 21.75, 0, 22, 10, 22.25, 10, 2, 22.267, 0, 1, 23.344, 0, 24.422, 0, 25.5, 0, 1, 25.75, 0, 26, 10, 26.25, 10, 2, 26.267, 0, 1, 27.428, 0, 28.589, 0, 29.75, 0, 1, 30, 0, 30.25, 10, 30.5, 10, 2, 30.517, 0, 1, 31.628, 0, 32.739, 0, 33.85, 0, 1, 34.1, 0, 34.35, 10, 34.6, 10, 2, 34.617, 0, 1, 35.683, 0, 36.75, 0, 37.817, 0, 1, 38.067, 0, 38.317, 10, 38.567, 10, 2, 38.583, 0, 1, 39.056, 0, 39.528, 0, 40, 0]}, {"Target": "Parameter", "Id": "Param101", "Segments": [0, 0, 1, 13.333, 0, 26.667, 0, 40, 0]}, {"Target": "Parameter", "Id": "Param102", "Segments": [0, -1.5, 1, 0.5, -1.5, 1, 0, 1.5, 0, 1, 2.5, 0, 3.5, -1.513, 4.5, -1.513, 1, 5.5, -1.513, 6.5, 0, 7.5, 0, 1, 8.5, 0, 9.5, -1.513, 10.5, -1.513, 1, 11.5, -1.513, 12.5, 0, 13.5, 0, 1, 14.5, 0, 15.5, -1.513, 16.5, -1.513, 1, 17.5, -1.513, 18.5, 0, 19.5, 0, 1, 20.5, 0, 21.5, -1.513, 22.5, -1.513, 1, 23.5, -1.513, 24.5, 0, 25.5, 0, 1, 26.5, 0, 27.5, -1.513, 28.5, -1.513, 1, 29.5, -1.513, 30.5, 0, 31.5, 0, 1, 32.5, 0, 33.5, -1.513, 34.5, -1.513, 1, 35.5, -1.513, 36.5, 0, 37.5, 0, 1, 38.333, 0, 39.167, -1.5, 40, -1.5]}, {"Target": "Parameter", "Id": "Param103", "Segments": [0, -5, 1, 1, -5, 2, 5, 3, 5, 1, 4, 5, 5, -5, 6, -5, 1, 7, -5, 8, 5, 9, 5, 1, 10, 5, 11, -5, 12, -5, 1, 13, -5, 14, 5, 15, 5, 1, 16, 5, 17, -5, 18, -5, 1, 19, -5, 20, 5, 21, 5, 1, 22, 5, 23, -5, 24, -5, 1, 25, -5, 26, 5, 27, 5, 1, 28, 5, 29, -5, 30, -5, 1, 31, -5, 32, 5, 33, 5, 1, 34, 5, 35, -5, 36, -5, 1, 36.667, -5, 37.333, 0.027, 38, 0.027, 1, 38.667, 0.027, 39.333, -5, 40, -5]}, {"Target": "Parameter", "Id": "Param104", "Segments": [0, 0, 1, 13.333, 0, 26.667, 0, 40, 0]}, {"Target": "Parameter", "Id": "Param105", "Segments": [0, 0, 1, 0.5, 0, 1, 3, 1.5, 3, 1, 2.5, 3, 3.5, -3, 4.5, -3, 1, 5.5, -3, 6.5, 3, 7.5, 3, 1, 8.5, 3, 9.5, -3, 10.5, -3, 1, 11.5, -3, 12.5, 3, 13.5, 3, 1, 14.5, 3, 15.5, -3, 16.5, -3, 1, 17.5, -3, 18.5, 3, 19.5, 3, 1, 20.5, 3, 21.5, -3, 22.5, -3, 1, 23.5, -3, 24.5, 3, 25.5, 3, 1, 26.5, 3, 27.5, -3, 28.5, -3, 1, 29.5, -3, 30.5, 3, 31.5, 3, 1, 32.5, 3, 33.5, -3, 34.5, -3, 1, 35.5, -3, 36.5, 3, 37.5, 3, 1, 38.333, 3, 39.167, 0, 40, 0]}, {"Target": "Parameter", "Id": "Param106", "Segments": [0, -5, 1, 1, -5, 2, 5, 3, 5, 1, 4, 5, 5, -5, 6, -5, 1, 7, -5, 8, 5, 9, 5, 1, 10, 5, 11, -5, 12, -5, 1, 13, -5, 14, 5, 15, 5, 1, 16, 5, 17, -5, 18, -5, 1, 19, -5, 20, 5, 21, 5, 1, 22, 5, 23, -5, 24, -5, 1, 25, -5, 26, 5, 27, 5, 1, 28, 5, 29, -5, 30, -5, 1, 31, -5, 32, 5, 33, 5, 1, 34, 5, 35, -5, 36, -5, 1, 36.778, -5, 37.556, -0.797, 38.333, -0.797, 1, 38.889, -0.797, 39.444, -5, 40, -5]}, {"Target": "Parameter", "Id": "Param107", "Segments": [0, -6.036, 1, 0.267, -6.036, 0.533, -6.078, 0.8, -6.016, 1, 1.8, -5.784, 2.8, 5.989, 3.8, 5.989, 1, 4.8, 5.989, 5.8, -6.016, 6.8, -6.016, 1, 7.8, -6.016, 8.8, 5.989, 9.8, 5.989, 1, 10.8, 5.989, 11.8, -6.016, 12.8, -6.016, 1, 13.8, -6.016, 14.8, 5.989, 15.8, 5.989, 1, 16.8, 5.989, 17.8, -6.016, 18.8, -6.016, 1, 19.8, -6.016, 20.8, 5.989, 21.8, 5.989, 1, 22.8, 5.989, 23.8, -6.016, 24.8, -6.016, 1, 25.8, -6.016, 26.8, 5.989, 27.8, 5.989, 1, 28.8, 5.989, 29.8, -6.016, 30.8, -6.016, 1, 31.8, -6.016, 32.8, 5.989, 33.8, 5.989, 1, 34.8, 5.989, 35.8, -6.016, 36.8, -6.016, 1, 37.411, -6.016, 38.022, -3.267, 38.633, -3.267, 1, 39.089, -3.267, 39.544, -6.036, 40, -6.036]}, {"Target": "Parameter", "Id": "Param112", "Segments": [0, 10.97, 1, 0.167, 10.97, 0.333, 13, 0.5, 13, 1, 1.167, 13, 1.833, 0, 2.5, 0, 1, 3.167, 0, 3.833, 13, 4.5, 13, 1, 5.167, 13, 5.833, 0, 6.5, 0, 1, 7.167, 0, 7.833, 13, 8.5, 13, 1, 9.167, 13, 9.833, 0, 10.5, 0, 1, 11.167, 0, 11.833, 13, 12.5, 13, 1, 13.167, 13, 13.833, 0, 14.5, 0, 1, 15.167, 0, 15.833, 13, 16.5, 13, 1, 17.167, 13, 17.833, 0, 18.5, 0, 1, 19.167, 0, 19.833, 13, 20.5, 13, 1, 21.167, 13, 21.833, 0, 22.5, 0, 1, 23.167, 0, 23.833, 13, 24.5, 13, 1, 25.167, 13, 25.833, 0, 26.5, 0, 1, 27.167, 0, 27.833, 13, 28.5, 13, 1, 29.167, 13, 29.833, 0, 30.5, 0, 1, 31.167, 0, 31.833, 13, 32.5, 13, 1, 33.167, 13, 33.833, 0, 34.5, 0, 1, 35.167, 0, 35.833, 13, 36.5, 13, 1, 37.167, 13, 37.833, 0, 38.5, 0, 1, 39, 0, 39.5, 10.97, 40, 10.97]}, {"Target": "Parameter", "Id": "Param113", "Segments": [0, 6.5, 1, 0.333, 6.5, 0.667, 13, 1, 13, 1, 1.667, 13, 2.333, 0, 3, 0, 1, 3.667, 0, 4.333, 13, 5, 13, 1, 5.667, 13, 6.333, 0, 7, 0, 1, 7.667, 0, 8.333, 13, 9, 13, 1, 9.667, 13, 10.333, 0, 11, 0, 1, 11.667, 0, 12.333, 13, 13, 13, 1, 13.667, 13, 14.333, 0, 15, 0, 1, 15.667, 0, 16.333, 13, 17, 13, 1, 17.667, 13, 18.333, 0, 19, 0, 1, 19.667, 0, 20.333, 13, 21, 13, 1, 21.667, 13, 22.333, 0, 23, 0, 1, 23.667, 0, 24.333, 13, 25, 13, 1, 25.667, 13, 26.333, 0, 27, 0, 1, 27.667, 0, 28.333, 13, 29, 13, 1, 29.667, 13, 30.333, 0, 31, 0, 1, 31.667, 0, 32.333, 13, 33, 13, 1, 33.667, 13, 34.333, 0, 35, 0, 1, 35.667, 0, 36.333, 13, 37, 13, 1, 37.667, 13, 38.333, 0, 39, 0, 1, 39.333, 0, 39.667, 6.5, 40, 6.5]}, {"Target": "Parameter", "Id": "Param114", "Segments": [0, 2.03, 1, 0.5, 2.03, 1, 13, 1.5, 13, 1, 2.167, 13, 2.833, 0, 3.5, 0, 1, 4.167, 0, 4.833, 13, 5.5, 13, 1, 6.167, 13, 6.833, 0, 7.5, 0, 1, 8.167, 0, 8.833, 13, 9.5, 13, 1, 10.167, 13, 10.833, 0, 11.5, 0, 1, 12.167, 0, 12.833, 13, 13.5, 13, 1, 14.167, 13, 14.833, 0, 15.5, 0, 1, 16.167, 0, 16.833, 13, 17.5, 13, 1, 18.167, 13, 18.833, 0, 19.5, 0, 1, 20.167, 0, 20.833, 13, 21.5, 13, 1, 22.167, 13, 22.833, 0, 23.5, 0, 1, 24.167, 0, 24.833, 13, 25.5, 13, 1, 26.167, 13, 26.833, 0, 27.5, 0, 1, 28.167, 0, 28.833, 13, 29.5, 13, 1, 30.167, 13, 30.833, 0, 31.5, 0, 1, 32.167, 0, 32.833, 13, 33.5, 13, 1, 34.167, 13, 34.833, 0, 35.5, 0, 1, 36.167, 0, 36.833, 13, 37.5, 13, 1, 38.167, 13, 38.833, 0, 39.5, 0, 1, 39.667, 0, 39.833, 2.03, 40, 2.03]}, {"Target": "Parameter", "Id": "Param115", "Segments": [0, 0, 1, 0.667, 0, 1.333, 13, 2, 13, 1, 2.667, 13, 3.333, 0, 4, 0, 1, 4.667, 0, 5.333, 13, 6, 13, 1, 6.667, 13, 7.333, 0, 8, 0, 1, 8.667, 0, 9.333, 13, 10, 13, 1, 10.667, 13, 11.333, 0, 12, 0, 1, 12.667, 0, 13.333, 13, 14, 13, 1, 14.667, 13, 15.333, 0, 16, 0, 1, 16.667, 0, 17.333, 13, 18, 13, 1, 18.667, 13, 19.333, 0, 20, 0, 1, 20.667, 0, 21.333, 13, 22, 13, 1, 22.667, 13, 23.333, 0, 24, 0, 1, 24.667, 0, 25.333, 13, 26, 13, 1, 26.667, 13, 27.333, 0, 28, 0, 1, 28.667, 0, 29.333, 13, 30, 13, 1, 30.667, 13, 31.333, 0, 32, 0, 1, 32.667, 0, 33.333, 13, 34, 13, 1, 34.667, 13, 35.333, 0, 36, 0, 1, 36.667, 0, 37.333, 13, 38, 13, 1, 38.667, 13, 39.333, 0, 40, 0]}, {"Target": "Parameter", "Id": "Param128", "Segments": [0, -30, 1, 0.117, -30, 0.233, 30, 0.35, 30, 1, 0.467, 30, 0.583, -30, 0.7, -30, 1, 0.817, -30, 0.933, 30, 1.05, 30, 1, 1.167, 30, 1.283, -30, 1.4, -30, 1, 1.517, -30, 1.633, 30, 1.75, 30, 1, 1.867, 30, 1.983, -30, 2.1, -30, 1, 2.217, -30, 2.333, 30, 2.45, 30, 1, 2.567, 30, 2.683, -30, 2.8, -30, 1, 2.917, -30, 3.033, 30, 3.15, 30, 1, 3.267, 30, 3.383, -30, 3.5, -30, 1, 3.617, -30, 3.733, 30, 3.85, 30, 1, 3.967, 30, 4.083, -30, 4.2, -30, 1, 4.317, -30, 4.433, 30, 4.55, 30, 1, 4.667, 30, 4.783, -30, 4.9, -30, 1, 5.017, -30, 5.133, 30, 5.25, 30, 1, 5.367, 30, 5.483, -30, 5.6, -30, 1, 5.717, -30, 5.833, 30, 5.95, 30, 1, 6.067, 30, 6.183, -30, 6.3, -30, 1, 6.417, -30, 6.533, 30, 6.65, 30, 1, 6.767, 30, 6.883, -30, 7, -30, 1, 7.117, -30, 7.233, 30, 7.35, 30, 1, 7.467, 30, 7.583, -30, 7.7, -30, 1, 7.817, -30, 7.933, 30, 8.05, 30, 1, 8.167, 30, 8.283, -30, 8.4, -30, 1, 8.517, -30, 8.633, 30, 8.75, 30, 1, 8.867, 30, 8.983, -30, 9.1, -30, 1, 9.217, -30, 9.333, 30, 9.45, 30, 1, 9.567, 30, 9.683, -30, 9.8, -30, 1, 9.917, -30, 10.033, 30, 10.15, 30, 1, 10.267, 30, 10.383, -30, 10.5, -30, 1, 10.617, -30, 10.733, 30, 10.85, 30, 1, 10.967, 30, 11.083, -30, 11.2, -30, 1, 11.317, -30, 11.433, 30, 11.55, 30, 1, 11.667, 30, 11.783, -30, 11.9, -30, 1, 12.017, -30, 12.133, 30, 12.25, 30, 1, 12.367, 30, 12.483, -30, 12.6, -30, 1, 12.717, -30, 12.833, 30, 12.95, 30, 1, 13.067, 30, 13.183, -30, 13.3, -30, 1, 13.417, -30, 13.533, 30, 13.65, 30, 1, 13.767, 30, 13.883, -30, 14, -30, 1, 14.117, -30, 14.233, 30, 14.35, 30, 1, 14.467, 30, 14.583, -30, 14.7, -30, 1, 14.817, -30, 14.933, 30, 15.05, 30, 1, 15.167, 30, 15.283, -30, 15.4, -30, 1, 15.517, -30, 15.633, 30, 15.75, 30, 1, 15.867, 30, 15.983, -30, 16.1, -30, 1, 16.217, -30, 16.333, 30, 16.45, 30, 1, 16.567, 30, 16.683, -30, 16.8, -30, 1, 16.917, -30, 17.033, 30, 17.15, 30, 1, 17.267, 30, 17.383, -30, 17.5, -30, 1, 17.617, -30, 17.733, 30, 17.85, 30, 1, 17.967, 30, 18.083, -30, 18.2, -30, 1, 18.317, -30, 18.433, 30, 18.55, 30, 1, 18.667, 30, 18.783, -30, 18.9, -30, 1, 19.017, -30, 19.133, 30, 19.25, 30, 1, 19.367, 30, 19.483, -30, 19.6, -30, 1, 19.717, -30, 19.833, 30, 19.95, 30, 1, 20.067, 30, 20.183, -30, 20.3, -30, 1, 20.417, -30, 20.533, 30, 20.65, 30, 1, 20.767, 30, 20.883, -30, 21, -30, 1, 21.117, -30, 21.233, 30, 21.35, 30, 1, 21.467, 30, 21.583, -30, 21.7, -30, 1, 21.817, -30, 21.933, 30, 22.05, 30, 1, 22.167, 30, 22.283, -30, 22.4, -30, 1, 22.517, -30, 22.633, 30, 22.75, 30, 1, 22.867, 30, 22.983, -30, 23.1, -30, 1, 23.217, -30, 23.333, 30, 23.45, 30, 1, 23.567, 30, 23.683, -30, 23.8, -30, 1, 23.917, -30, 24.033, 30, 24.15, 30, 1, 24.267, 30, 24.383, -30, 24.5, -30, 1, 24.617, -30, 24.733, 30, 24.85, 30, 1, 24.967, 30, 25.083, -30, 25.2, -30, 1, 25.317, -30, 25.433, 30, 25.55, 30, 1, 25.667, 30, 25.783, -30, 25.9, -30, 1, 26.017, -30, 26.133, 30, 26.25, 30, 1, 26.367, 30, 26.483, -30, 26.6, -30, 1, 26.717, -30, 26.833, 30, 26.95, 30, 1, 27.067, 30, 27.183, -30, 27.3, -30, 1, 27.417, -30, 27.533, 30, 27.65, 30, 1, 27.767, 30, 27.883, -30, 28, -30, 1, 28.117, -30, 28.233, 30, 28.35, 30, 1, 28.467, 30, 28.583, -30, 28.7, -30, 1, 28.817, -30, 28.933, 30, 29.05, 30, 1, 29.167, 30, 29.283, -30, 29.4, -30, 1, 29.517, -30, 29.633, 30, 29.75, 30, 1, 29.867, 30, 29.983, -30, 30.1, -30, 1, 30.217, -30, 30.333, 30, 30.45, 30, 1, 30.567, 30, 30.683, -30, 30.8, -30, 1, 30.917, -30, 31.033, 30, 31.15, 30, 1, 31.267, 30, 31.383, -30, 31.5, -30, 1, 31.617, -30, 31.733, 30, 31.85, 30, 1, 31.967, 30, 32.083, -30, 32.2, -30, 1, 32.317, -30, 32.433, 30, 32.55, 30, 1, 32.667, 30, 32.783, -30, 32.9, -30, 1, 33.017, -30, 33.133, 30, 33.25, 30, 1, 33.367, 30, 33.483, -30, 33.6, -30, 1, 33.717, -30, 33.833, 30, 33.95, 30, 1, 34.067, 30, 34.183, -30, 34.3, -30, 1, 34.417, -30, 34.533, 30, 34.65, 30, 1, 34.767, 30, 34.883, -30, 35, -30, 1, 35.117, -30, 35.233, 30, 35.35, 30, 1, 35.467, 30, 35.583, -30, 35.7, -30, 1, 35.817, -30, 35.933, 30, 36.05, 30, 1, 36.167, 30, 36.283, -30, 36.4, -30, 1, 36.517, -30, 36.633, 30, 36.75, 30, 1, 36.867, 30, 36.983, -30, 37.1, -30, 1, 37.217, -30, 37.333, 30, 37.45, 30, 1, 37.567, 30, 37.683, -30, 37.8, -30, 1, 37.917, -30, 38.033, 30, 38.15, 30, 1, 38.267, 30, 38.383, -30, 38.5, -30, 1, 38.617, -30, 38.733, 30, 38.85, 30, 1, 38.967, 30, 39.083, -30, 39.2, -30, 1, 39.317, -30, 39.433, 30, 39.55, 30, 1, 39.7, 30, 39.85, -30, 40, -30]}, {"Target": "Parameter", "Id": "Param121", "Segments": [0, 0, 1, 13.333, 0, 26.667, 0, 40, 0]}, {"Target": "Parameter", "Id": "Param122", "Segments": [0, 10, 1, 0.667, 10, 1.333, -8, 2, -8, 1, 2.667, -8, 3.333, 10, 4, 10, 1, 4.667, 10, 5.333, -8, 6, -8, 1, 6.667, -8, 7.333, 10, 8, 10, 1, 8.667, 10, 9.333, -8, 10, -8, 1, 10.667, -8, 11.333, 10, 12, 10, 1, 12.667, 10, 13.333, -8, 14, -8, 1, 14.667, -8, 15.333, 10, 16, 10, 1, 16.667, 10, 17.333, -8, 18, -8, 1, 18.667, -8, 19.333, 10, 20, 10, 1, 20.667, 10, 21.333, -8, 22, -8, 1, 22.667, -8, 23.333, 10, 24, 10, 1, 24.667, 10, 25.333, -8, 26, -8, 1, 26.667, -8, 27.333, 10, 28, 10, 1, 28.667, 10, 29.333, -8, 30, -8, 1, 30.667, -8, 31.333, 10, 32, 10, 1, 32.667, 10, 33.333, -8, 34, -8, 1, 34.667, -8, 35.333, 10, 36, 10, 1, 36.667, 10, 37.333, -8, 38, -8, 1, 38.667, -8, 39.333, 10, 40, 10]}, {"Target": "Parameter", "Id": "Param123", "Segments": [0, 11.64, 1, 0.139, 11.64, 0.278, 15, 0.417, 15, 1, 1.083, 15, 1.75, -15, 2.417, -15, 1, 3.083, -15, 3.75, 15, 4.417, 15, 1, 5.083, 15, 5.75, -15, 6.417, -15, 1, 7.083, -15, 7.75, 15, 8.417, 15, 1, 9.083, 15, 9.75, -15, 10.417, -15, 1, 11.083, -15, 11.75, 15, 12.417, 15, 1, 13.083, 15, 13.75, -15, 14.417, -15, 1, 15.083, -15, 15.75, 15, 16.417, 15, 1, 17.083, 15, 17.75, -15, 18.417, -15, 1, 19.083, -15, 19.75, 15, 20.417, 15, 1, 21.083, 15, 21.75, -15, 22.417, -15, 1, 23.083, -15, 23.75, 15, 24.417, 15, 1, 25.083, 15, 25.75, -15, 26.417, -15, 1, 27.083, -15, 27.75, 15, 28.417, 15, 1, 29.083, 15, 29.75, -15, 30.417, -15, 1, 31.083, -15, 31.75, 15, 32.417, 15, 1, 33.083, 15, 33.75, -15, 34.417, -15, 1, 35.083, -15, 35.75, 15, 36.417, 15, 1, 37.083, 15, 37.75, -15, 38.417, -15, 1, 38.944, -15, 39.472, 11.64, 40, 11.64]}, {"Target": "Parameter", "Id": "Param124", "Segments": [0, 3.71, 1, 0.278, 3.71, 0.556, 15, 0.833, 15, 1, 1.5, 15, 2.167, -15, 2.833, -15, 1, 3.5, -15, 4.167, 15, 4.833, 15, 1, 5.5, 15, 6.167, -15, 6.833, -15, 1, 7.5, -15, 8.167, 15, 8.833, 15, 1, 9.5, 15, 10.167, -15, 10.833, -15, 1, 11.5, -15, 12.167, 15, 12.833, 15, 1, 13.5, 15, 14.167, -15, 14.833, -15, 1, 15.5, -15, 16.167, 15, 16.833, 15, 1, 17.5, 15, 18.167, -15, 18.833, -15, 1, 19.5, -15, 20.167, 15, 20.833, 15, 1, 21.5, 15, 22.167, -15, 22.833, -15, 1, 23.5, -15, 24.167, 15, 24.833, 15, 1, 25.5, 15, 26.167, -15, 26.833, -15, 1, 27.5, -15, 28.167, 15, 28.833, 15, 1, 29.5, 15, 30.167, -15, 30.833, -15, 1, 31.5, -15, 32.167, 15, 32.833, 15, 1, 33.5, 15, 34.167, -15, 34.833, -15, 1, 35.5, -15, 36.167, 15, 36.833, 15, 1, 37.5, 15, 38.167, -15, 38.833, -15, 1, 39.222, -15, 39.611, 3.71, 40, 3.71]}, {"Target": "Parameter", "Id": "Param125", "Segments": [0, -5.51, 1, 0.417, -5.51, 0.833, 15, 1.25, 15, 1, 1.917, 15, 2.583, -15, 3.25, -15, 1, 3.917, -15, 4.583, 15, 5.25, 15, 1, 5.917, 15, 6.583, -15, 7.25, -15, 1, 7.917, -15, 8.583, 15, 9.25, 15, 1, 9.917, 15, 10.583, -15, 11.25, -15, 1, 11.917, -15, 12.583, 15, 13.25, 15, 1, 13.917, 15, 14.583, -15, 15.25, -15, 1, 15.917, -15, 16.583, 15, 17.25, 15, 1, 17.917, 15, 18.583, -15, 19.25, -15, 1, 19.917, -15, 20.583, 15, 21.25, 15, 1, 21.917, 15, 22.583, -15, 23.25, -15, 1, 23.917, -15, 24.583, 15, 25.25, 15, 1, 25.917, 15, 26.583, -15, 27.25, -15, 1, 27.917, -15, 28.583, 15, 29.25, 15, 1, 29.917, 15, 30.583, -15, 31.25, -15, 1, 31.917, -15, 32.583, 15, 33.25, 15, 1, 33.917, 15, 34.583, -15, 35.25, -15, 1, 35.917, -15, 36.583, 15, 37.25, 15, 1, 37.917, 15, 38.583, -15, 39.25, -15, 1, 39.5, -15, 39.75, -5.328, 40, -5.328]}, {"Target": "Parameter", "Id": "Param126", "Segments": [0, -12.78, 1, 0.556, -12.78, 1.111, 15, 1.667, 15, 1, 2.333, 15, 3, -15, 3.667, -15, 1, 4.333, -15, 5, 15, 5.667, 15, 1, 6.333, 15, 7, -15, 7.667, -15, 1, 8.333, -15, 9, 15, 9.667, 15, 1, 10.333, 15, 11, -15, 11.667, -15, 1, 12.333, -15, 13, 15, 13.667, 15, 1, 14.333, 15, 15, -15, 15.667, -15, 1, 16.333, -15, 17, 15, 17.667, 15, 1, 18.333, 15, 19, -15, 19.667, -15, 1, 20.333, -15, 21, 15, 21.667, 15, 1, 22.333, 15, 23, -15, 23.667, -15, 1, 24.333, -15, 25, 15, 25.667, 15, 1, 26.333, 15, 27, -15, 27.667, -15, 1, 28.333, -15, 29, 15, 29.667, 15, 1, 30.333, 15, 31, -15, 31.667, -15, 1, 32.333, -15, 33, 15, 33.667, 15, 1, 34.333, 15, 35, -15, 35.667, -15, 1, 36.333, -15, 37, 15, 37.667, 15, 1, 38.333, 15, 39, -15, 39.667, -15, 1, 39.778, -15, 39.889, -12.78, 40, -12.78]}, {"Target": "Parameter", "Id": "Param127", "Segments": [0, -14.85, 1, 0.694, -14.85, 1.389, 15, 2.083, 15, 1, 2.75, 15, 3.417, -15, 4.083, -15, 1, 4.75, -15, 5.417, 15, 6.083, 15, 1, 6.75, 15, 7.417, -15, 8.083, -15, 1, 8.75, -15, 9.417, 15, 10.083, 15, 1, 10.75, 15, 11.417, -15, 12.083, -15, 1, 12.75, -15, 13.417, 15, 14.083, 15, 1, 14.75, 15, 15.417, -15, 16.083, -15, 1, 16.75, -15, 17.417, 15, 18.083, 15, 1, 18.75, 15, 19.417, -15, 20.083, -15, 1, 20.75, -15, 21.417, 15, 22.083, 15, 1, 22.75, 15, 23.417, -15, 24.083, -15, 1, 24.75, -15, 25.417, 15, 26.083, 15, 1, 26.75, 15, 27.417, -15, 28.083, -15, 1, 28.75, -15, 29.417, 15, 30.083, 15, 1, 30.75, 15, 31.417, -15, 32.083, -15, 1, 32.75, -15, 33.417, 15, 34.083, 15, 1, 34.75, 15, 35.417, -15, 36.083, -15, 1, 36.75, -15, 37.417, 15, 38.083, 15, 1, 38.722, 15, 39.361, -14.85, 40, -14.85]}]}