{"Version": 3, "Meta": {"Duration": 12, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 324, "TotalSegmentCount": 3089, "TotalPointCount": 9243, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.25, 0, 0.5, -25, 0.75, -25, 1, 1, -25, 1.25, -0.04, 1.5, -0.04, 1, 1.75, -0.04, 2, -24.987, 2.25, -24.987, 1, 2.5, -24.987, 2.75, 0, 3, 0, 1, 3.25, 0, 3.5, -24.987, 3.75, -24.987, 1, 4, -24.987, 4.25, -0.04, 4.5, -0.04, 1, 4.75, -0.04, 5, -24.987, 5.25, -24.987, 1, 5.5, -24.987, 5.75, -0.04, 6, -0.04, 1, 6.25, -0.04, 6.5, -24.987, 6.75, -24.987, 1, 7, -24.987, 7.25, -0.04, 7.5, -0.04, 1, 7.75, -0.04, 8, -24.987, 8.25, -24.987, 1, 8.5, -24.987, 8.75, -0.04, 9, -0.04, 1, 9.25, -0.04, 9.5, -24.987, 9.75, -24.987, 1, 10, -24.987, 10.25, -0.04, 10.5, -0.04, 1, 10.75, -0.04, 11, -24.987, 11.25, -24.987, 1, 11.5, -24.987, 11.75, -0.04, 12, -0.04]}, {"Target": "Parameter", "Id": "Param240", "Segments": [0, 1, 1, 4, 1, 8, 1, 12, 1]}, {"Target": "Parameter", "Id": "Param237", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param238", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param239", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.361, 1, 0.722, 1, 1.083, 1, 1, 1.139, 1, 1.194, 0, 1.25, 0, 1, 1.306, 0, 1.361, 1, 1.417, 1, 1, 2.361, 1, 3.306, 1, 4.25, 1, 1, 4.306, 1, 4.361, 0, 4.417, 0, 1, 4.472, 0, 4.528, 1, 4.583, 1, 1, 5.522, 1, 6.461, 1, 7.4, 1, 1, 7.456, 1, 7.511, 0, 7.567, 0, 1, 7.622, 0, 7.678, 1, 7.733, 1, 1, 8.378, 1, 9.022, 1, 9.667, 1, 1, 9.722, 1, 9.778, 0, 9.833, 0, 1, 9.889, 0, 9.944, 1, 10, 1, 1, 10.667, 1, 11.333, 1, 12, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.361, 1, 0.722, 1, 1.083, 1, 1, 1.139, 1, 1.194, 0, 1.25, 0, 1, 1.306, 0, 1.361, 1, 1.417, 1, 1, 2.361, 1, 3.306, 1, 4.25, 1, 1, 4.306, 1, 4.361, 0, 4.417, 0, 1, 4.472, 0, 4.528, 1, 4.583, 1, 1, 5.522, 1, 6.461, 1, 7.4, 1, 1, 7.456, 1, 7.511, 0, 7.567, 0, 1, 7.622, 0, 7.678, 1, 7.733, 1, 1, 8.378, 1, 9.022, 1, 9.667, 1, 1, 9.722, 1, 9.778, 0, 9.833, 0, 1, 9.889, 0, 9.944, 1, 10, 1, 1, 10.667, 1, 11.333, 1, 12, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.389, 0, 0.778, 0, 1.167, 0, 1, 1.278, 0, 1.389, -0.4, 1.5, -0.4, 1, 1.722, -0.4, 1.944, -0.4, 2.167, -0.4, 1, 2.278, -0.4, 2.389, 0, 2.5, 0, 1, 3.139, 0, 3.778, 0, 4.417, 0, 1, 4.556, 0, 4.694, -0.347, 4.833, -0.348, 1, 5.306, -0.35, 5.778, -0.35, 6.25, -0.35, 1, 6.417, -0.35, 6.583, 0, 6.75, 0, 1, 7.767, 0, 8.783, 0, 9.8, 0, 1, 9.911, 0, 10.022, -0.4, 10.133, -0.4, 1, 10.467, -0.4, 10.8, 0, 11.133, 0, 1, 11.422, 0, 11.711, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.389, 0, 0.778, 0, 1.167, 0, 1, 1.278, 0, 1.389, 0.1, 1.5, 0.1, 1, 1.722, 0.1, 1.944, 0.1, 2.167, 0.1, 1, 2.278, 0.1, 2.389, 0, 2.5, 0, 1, 3.139, 0, 3.778, 0, 4.417, 0, 1, 4.556, 0, 4.694, -0.4, 4.833, -0.4, 1, 5.306, -0.4, 5.778, 0, 6.25, 0, 1, 6.333, 0, 6.417, -0.499, 6.5, -0.499, 1, 6.583, -0.499, 6.667, -0.103, 6.75, -0.1, 1, 7.767, -0.064, 8.783, -0.037, 9.8, 0, 1, 9.911, 0.004, 10.022, 0.1, 10.133, 0.1, 1, 10.467, 0.1, 10.8, 0, 11.133, 0, 1, 11.422, 0, 11.711, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param4", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamMouthS", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param21", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0.5, 1, 4, 0.5, 8, 0.5, 12, 0.5]}, {"Target": "Parameter", "Id": "ParamEyeGaoguang", "Segments": [0, 1, 0, 0.167, -1, 0, 0.333, 1, 0, 0.5, -1, 0, 0.667, 1, 0, 0.833, -1, 0, 1, 1, 0, 1.167, -2, 0, 1.333, 6.54, 0, 1.5, -5.892, 0, 1.667, 3.757, 0, 1.833, -2.973, 0, 2, 1, 0, 2.167, -1, 0, 2.333, 1, 0, 2.5, -1, 0, 2.667, 1, 0, 2.833, -1, 0, 3, 1, 0, 3.167, -1, 0, 3.333, 1, 0, 3.5, -1, 0, 3.667, 1, 0, 3.833, -1, 0, 4, 1, 0, 4.167, -1, 0, 4.333, 1, 0, 4.5, -2, 0, 4.667, 6.541, 0, 4.833, -5.892, 0, 5, 3.757, 0, 5.167, -2.973, 0, 5.333, 1, 0, 5.5, -1, 0, 5.667, 1, 0, 5.833, -1, 0, 6, 1, 0, 6.167, -1, 0, 6.333, 1, 0, 6.5, -1, 0, 6.667, 1, 0, 6.833, -1, 0, 7, 1, 0, 7.167, -1, 0, 7.333, 1, 0, 7.5, -2, 0, 7.667, 6.541, 0, 7.833, -5.892, 0, 8, 3.757, 0, 8.167, -2.973, 0, 8.333, 1, 0, 8.5, -1, 0, 8.667, 1, 0, 8.833, -1, 0, 9, 1, 0, 9.167, -1, 0, 9.333, 1, 0, 9.5, -1, 0, 9.667, 1, 0, 9.833, -2, 0, 10, 6.541, 0, 10.167, -5.892, 0, 10.333, 3.757, 0, 10.5, -2.973, 0, 10.667, 1, 0, 10.833, -1, 0, 11, 1, 0, 11.167, -1, 0, 11.333, 1, 0, 11.5, -1, 0, 11.667, 1, 0, 11.833, -1, 0, 12, 1]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, -6, 1, 0.272, -6, 0.544, -9.433, 0.817, -9.433, 1, 1.483, -9.433, 2.15, 0, 2.817, 0, 1, 3.483, 0, 4.15, -9.433, 4.817, -9.433, 1, 5.483, -9.433, 6.15, 0, 6.817, 0, 1, 7.483, 0, 8.15, -9.433, 8.817, -9.433, 1, 9.483, -9.433, 10.15, 0, 10.817, 0, 1, 11.211, 0, 11.606, -2.311, 12, -6]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, -0.1, 1, 0.611, -2.561, 1.222, -4.8, 1.833, -4.8, 1, 2.5, -4.8, 3.167, 0, 3.833, 0, 1, 4.5, 0, 5.167, -4.8, 5.833, -4.8, 1, 6.5, -4.8, 7.167, 0, 7.833, 0, 1, 8.5, 0, 9.167, -4.8, 9.833, -4.8, 1, 10.556, -4.8, 11.278, -0.1, 12, -0.1]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.611, 0, 1.222, -1.92, 1.833, -1.92, 1, 2.5, -1.92, 3.167, 0, 3.833, 0, 1, 4.5, 0, 5.167, -1.92, 5.833, -1.92, 1, 6.5, -1.92, 7.167, 0, 7.833, 0, 1, 8.5, 0, 9.167, -1.92, 9.833, -1.92, 1, 10.556, -1.92, 11.278, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ2", "Segments": [0, -0.3, 1, 0.556, -2.331, 1.111, -4.14, 1.667, -4.14, 1, 2.333, -4.14, 3, 0, 3.667, 0, 1, 4.333, 0, 5, -4.14, 5.667, -4.14, 1, 6.333, -4.14, 7, 0, 7.667, 0, 1, 8.333, 0, 9, -4.14, 9.667, -4.14, 1, 10.444, -4.14, 11.222, -0.3, 12, -0.3]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, -0.1, 1, 0.5, -0.346, 1, -0.5, 1.5, -0.5, 1, 2.167, -0.5, 2.833, 0, 3.5, 0, 1, 4.167, 0, 4.833, -0.5, 5.5, -0.5, 1, 6.167, -0.5, 6.833, 0, 7.5, 0, 1, 8.167, 0, 8.833, -0.5, 9.5, -0.5, 1, 10.167, -0.5, 10.833, 0, 11.5, 0, 1, 11.667, 0, 11.833, -0.1, 12, -0.1]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ2", "Segments": [0, -0.2, 1, 0.461, -0.63, 0.922, -1, 1.383, -1, 1, 2.05, -1, 2.717, 0, 3.383, 0, 1, 4.05, 0, 4.717, -1, 5.383, -1, 1, 6.05, -1, 6.717, 0, 7.383, 0, 1, 8.05, 0, 8.717, -1, 9.383, -1, 1, 10.05, -1, 10.717, 0, 11.383, 0, 1, 11.589, 0, 11.794, -0.2, 12, -0.2]}, {"Target": "Parameter", "Id": "Param20", "Segments": [0, -2, 1, 0.417, -0.76, 0.833, 0, 1.25, 0, 1, 1.917, 0, 2.583, -3, 3.25, -3, 1, 3.917, -3, 4.583, 0, 5.25, 0, 1, 5.917, 0, 6.583, -3, 7.25, -3, 1, 7.917, -3, 8.583, 0, 9.25, 0, 1, 9.917, 0, 10.583, -3, 11.25, -3, 1, 11.5, -3, 11.75, -2, 12, -2]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, -0.5, 1, 0.333, -0.5, 0.667, -4, 1, -4, 1, 1.667, -4, 2.333, 1, 3, 1, 1, 3.667, 1, 4.333, -4, 5, -4, 1, 5.667, -4, 6.333, 1, 7, 1, 1, 7.667, 1, 8.333, -4, 9, -4, 1, 9.667, -4, 10.333, 1, 11, 1, 1, 11.333, 1, 11.667, -0.5, 12, -0.5]}, {"Target": "Parameter", "Id": "ParamBodyAngleUD", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamJianbangR", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.667, 0, 1.333, 1, 2, 1, 1, 2.667, 1, 3.333, 0, 4, 0, 1, 4.672, 0, 5.344, 1, 6.017, 1, 1, 6.678, 1, 7.339, 0, 8, 0, 1, 8.667, 0, 9.333, 1, 10, 1, 1, 10.667, 1, 11.333, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamJianbangL", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param42", "Segments": [0, -0.2, 1, 0.5, -0.2, 1, -0.58, 1.5, -0.58, 1, 2.167, -0.58, 2.833, -0.1, 3.5, -0.1, 1, 4.167, -0.1, 4.833, -0.58, 5.5, -0.58, 1, 6.167, -0.58, 6.833, -0.1, 7.5, -0.1, 1, 8.167, -0.1, 8.833, -0.58, 9.5, -0.58, 1, 10.167, -0.58, 10.833, -0.1, 11.5, -0.1, 1, 11.667, -0.1, 11.833, -0.121, 12, -0.2]}, {"Target": "Parameter", "Id": "Param54", "Segments": [0, -5, 0, 12, -5]}, {"Target": "Parameter", "Id": "Param43", "Segments": [0, 1, 1, 0.5, 1, 1, 1, 1.5, 1, 1, 2.167, 1, 2.833, 1, 3.5, 1, 1, 4.167, 1, 4.833, 1, 5.5, 1, 1, 6.167, 1, 6.833, 1, 7.5, 1, 1, 8.167, 1, 8.833, 1, 9.5, 1, 1, 10.167, 1, 10.833, 1, 11.5, 1, 1, 11.667, 1, 11.833, 1, 12, 1]}, {"Target": "Parameter", "Id": "Param44", "Segments": [0, -6.7, 1, 0.5, -6.7, 1, -6.3, 1.5, -6.3, 1, 2.167, -6.3, 2.833, -6.8, 3.5, -6.8, 1, 4.167, -6.8, 4.833, -6.3, 5.5, -6.3, 1, 6.167, -6.3, 6.833, -6.8, 7.5, -6.8, 1, 8.167, -6.8, 8.833, -6.3, 9.5, -6.3, 1, 10.167, -6.3, 10.833, -6.8, 11.5, -6.8, 1, 11.667, -6.8, 11.833, -6.782, 12, -6.7]}, {"Target": "Parameter", "Id": "Param45", "Segments": [0, 0.2, 1, 0.5, 0.2, 1, 1.4, 1.5, 1.4, 1, 2.167, 1.4, 2.833, 0, 3.5, 0, 1, 4.167, 0, 4.833, 1.4, 5.5, 1.4, 1, 6.167, 1.4, 6.833, 0, 7.5, 0, 1, 8.167, 0, 8.833, 1.4, 9.5, 1.4, 1, 10.167, 1.4, 10.833, 0, 11.5, 0, 1, 11.667, 0, 11.833, -0.013, 12, 0.2]}, {"Target": "Parameter", "Id": "Param46", "Segments": [0, 0.2, 1, 0.5, 0.2, 1, 1.5, 1.5, 1.5, 1, 2.167, 1.5, 2.833, 0, 3.5, 0, 1, 4.167, 0, 4.833, 1.5, 5.5, 1.5, 1, 6.167, 1.5, 6.833, 0, 7.5, 0, 1, 8.167, 0, 8.833, 1.5, 9.5, 1.5, 1, 10.167, 1.5, 10.833, 0, 11.5, 0, 1, 11.667, 0, 11.833, -0.024, 12, 0.2]}, {"Target": "Parameter", "Id": "Param47", "Segments": [0, 0, 1, 0.5, 0, 1, 0, 1.5, 0, 1, 2.167, 0, 2.833, 0, 3.5, 0, 1, 4.167, 0, 4.833, 0, 5.5, 0, 1, 6.167, 0, 6.833, 0, 7.5, 0, 1, 8.167, 0, 8.833, 0, 9.5, 0, 1, 10.167, 0, 10.833, 0, 11.5, 0, 1, 11.667, 0, 11.833, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param48", "Segments": [0, 0.664, 1, 0.5, 0.664, 1, 4.38, 1.5, 4.38, 1, 2.167, 4.38, 2.833, 0, 3.5, 0, 1, 4.167, 0, 4.833, 4.38, 5.5, 4.38, 1, 6.167, 4.38, 6.833, 0, 7.5, 0, 1, 8.167, 0, 8.833, 4.38, 9.5, 4.38, 1, 10.167, 4.38, 10.833, 0, 11.5, 0, 1, 11.667, 0, 11.833, -0.015, 12, 0.664]}, {"Target": "Parameter", "Id": "Param49", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param66", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param26", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param50", "Segments": [0, 1, 0, 12, 1]}, {"Target": "Parameter", "Id": "Param51", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param52", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param53", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param55", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param29", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param188", "Segments": [0, 1, 1, 4, 1, 8, 1, 12, 1]}, {"Target": "Parameter", "Id": "ParamShangBiR", "Segments": [0, -6, 0, 12, -6]}, {"Target": "Parameter", "Id": "ParamXiaBiR", "Segments": [0, 8.5, 0, 12, 8.5]}, {"Target": "Parameter", "Id": "ParamSouR", "Segments": [0, -9.1, 1, 0.5, -9.1, 1, -9.74, 1.5, -9.74, 1, 2.167, -9.74, 2.833, -9, 3.5, -9, 1, 4.167, -9, 4.833, -9.74, 5.5, -9.74, 1, 6.167, -9.74, 6.833, -9, 7.5, -9, 1, 8.167, -9, 8.833, -9.74, 9.5, -9.74, 1, 10.167, -9.74, 10.833, -9, 11.5, -9, 1, 11.667, -9, 11.833, -8.989, 12, -9.1]}, {"Target": "Parameter", "Id": "ParamShangBiRUD", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamXiaBiRUD", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param35", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param36", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param37", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param65", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param38", "Segments": [0, 1, 0, 12, 1]}, {"Target": "Parameter", "Id": "Param39", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param40", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param41", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamDaTuiL", "Segments": [0, -0.5, 1, 0.333, -1.167, 0.667, -1.5, 1, -1.5, 1, 1.667, -1.5, 2.333, 0.5, 3, 0.5, 1, 3.667, 0.5, 4.333, -1.5, 5, -1.5, 1, 5.667, -1.5, 6.333, 0.5, 7, 0.5, 1, 7.667, 0.5, 8.333, -1.5, 9, -1.5, 1, 9.667, -1.5, 10.333, 0.5, 11, 0.5, 1, 11.333, 0.5, 11.667, 0.167, 12, -0.5]}, {"Target": "Parameter", "Id": "ParamXiaoTuiL", "Segments": [0, 0.11, 1, 0.417, -0.136, 0.833, -0.3, 1.25, -0.3, 1, 1.917, -0.3, 2.583, 0.3, 3.25, 0.3, 1, 3.917, 0.3, 4.583, -0.3, 5.25, -0.3, 1, 5.917, -0.3, 6.583, 0.3, 7.25, 0.3, 1, 7.917, 0.3, 8.583, -0.3, 9.25, -0.3, 1, 9.917, -0.3, 10.583, 0.3, 11.25, 0.3, 1, 11.5, 0.3, 11.75, 0.258, 12, 0.11]}, {"Target": "Parameter", "Id": "ParamJiaoL", "Segments": [0, 0.59, 1, 0.5, -0.639, 1, -1.622, 1.5, -1.622, 1, 2.167, -1.622, 2.833, 1, 3.5, 1, 1, 4.167, 1, 4.833, -1.622, 5.5, -1.622, 1, 6.167, -1.622, 6.833, 1, 7.5, 1, 1, 8.167, 1, 8.833, -1.622, 9.5, -1.622, 1, 10.167, -1.622, 10.833, 1, 11.5, 1, 1, 11.667, 1, 11.833, 1, 12, 0.59]}, {"Target": "Parameter", "Id": "ParamJiaoL2", "Segments": [0, 0.84, 1, 0.583, -1.058, 1.167, -2.811, 1.75, -2.811, 1, 2.417, -2.811, 3.083, 1, 3.75, 1, 1, 4.417, 1, 5.083, -2.811, 5.75, -2.811, 1, 6.417, -2.811, 7.083, 1, 7.75, 1, 1, 8.417, 1, 9.083, -2.811, 9.75, -2.811, 1, 10.417, -2.811, 11.083, 1, 11.75, 1, 1, 11.833, 1, 11.917, 1.111, 12, 0.84]}, {"Target": "Parameter", "Id": "ParamJiaoL3", "Segments": [0, 1.5, 1, 0.667, 1.5, 1.333, -4.446, 2, -4.446, 1, 2.667, -4.446, 3.333, 1.5, 4, 1.5, 1, 4.667, 1.5, 5.333, -4.446, 6, -4.446, 1, 6.667, -4.446, 7.333, 1.5, 8, 1.5, 1, 8.667, 1.5, 9.333, -4.446, 10, -4.446, 1, 10.667, -4.446, 11.333, 1.5, 12, 1.5]}, {"Target": "Parameter", "Id": "ParamDaTuiLUD", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamXiaoTuiLUD", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param59", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamDaTuiR", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamXiaoTuiR", "Segments": [0, 0, 1, 0.417, 0, 0.833, 0.2, 1.25, 0.2, 1, 1.917, 0.2, 2.583, -0.1, 3.25, -0.1, 1, 3.917, -0.1, 4.583, 0.2, 5.25, 0.2, 1, 5.917, 0.2, 6.583, -0.1, 7.25, -0.1, 1, 7.917, -0.1, 8.583, 0.2, 9.25, 0.2, 1, 9.917, 0.2, 10.583, -0.1, 11.25, -0.1, 1, 11.5, -0.1, 11.75, -0.074, 12, 0]}, {"Target": "Parameter", "Id": "ParamJiaoR", "Segments": [0, -0.27, 1, 0.083, -0.27, 0.167, -0.139, 0.25, 0, 1, 0.667, 0.697, 1.083, 1, 1.5, 1, 1, 2.167, 1, 2.833, -0.5, 3.5, -0.5, 1, 4.167, -0.5, 4.833, 1, 5.5, 1, 1, 6.167, 1, 6.833, -0.5, 7.5, -0.5, 1, 8.167, -0.5, 8.833, 1, 9.5, 1, 1, 10.167, 1, 10.833, -0.5, 11.5, -0.5, 1, 11.667, -0.5, 11.833, -0.503, 12, -0.27]}, {"Target": "Parameter", "Id": "ParamJiaoR2", "Segments": [0, -2.762, 1, 0.167, -2.762, 0.333, -0.954, 0.5, 0, 1, 0.917, 2.384, 1.333, 3, 1.75, 3, 1, 2.417, 3, 3.083, -3, 3.75, -3, 1, 4.417, -3, 5.083, 3, 5.75, 3, 1, 6.417, 3, 7.083, -3, 7.75, -3, 1, 8.417, -3, 9.083, 3, 9.75, 3, 1, 10.417, 3, 11.083, -3, 11.75, -3, 1, 11.833, -3, 11.917, -3.182, 12, -2.762]}, {"Target": "Parameter", "Id": "ParamDaTuiRUD", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamXiaoTuiRUD", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param6", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param2", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param3", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param27", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param28", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamQianFa1_1", "Segments": [0, 0.008, 1, 0.078, 0.362, 0.156, 3.019, 0.233, 3.019, 1, 0.356, 3.019, 0.478, 0.026, 0.6, -1.363, 1, 0.706, -2.564, 0.811, -2.49, 0.917, -2.49, 1, 1.161, -2.49, 1.406, 2.499, 1.65, 2.499, 1, 1.894, 2.499, 2.139, -2.623, 2.383, -2.623, 1, 2.633, -2.623, 2.883, 2.727, 3.133, 2.727, 1, 3.383, 2.727, 3.633, -2.609, 3.883, -2.609, 1, 4.133, -2.609, 4.383, 2.61, 4.633, 2.61, 1, 4.883, 2.61, 5.133, -2.724, 5.383, -2.724, 1, 5.633, -2.724, 5.883, 2.63, 6.133, 2.63, 1, 6.383, 2.63, 6.633, -2.57, 6.883, -2.57, 1, 7.133, -2.57, 7.383, 2.712, 7.633, 2.712, 1, 7.883, 2.712, 8.133, -2.66, 8.383, -2.66, 1, 8.633, -2.66, 8.883, 2.576, 9.133, 2.576, 1, 9.383, 2.576, 9.633, -2.694, 9.883, -2.694, 1, 10.139, -2.694, 10.394, 2.761, 10.65, 2.761, 1, 10.9, 2.761, 11.15, -2.558, 11.4, -2.558, 1, 11.6, -2.558, 11.8, 0.916, 12, 2.179]}, {"Target": "Parameter", "Id": "ParamQianFa1_2", "Segments": [0, -0.418, 1, 0.05, -0.844, 0.1, -1.377, 0.15, -1.377, 1, 0.244, -1.377, 0.339, 5.258, 0.433, 5.258, 1, 0.533, 5.258, 0.633, 0.039, 0.733, 0.039, 1, 0.772, 0.039, 0.811, 0.458, 0.85, 0.458, 1, 0.95, 0.458, 1.05, -3.811, 1.15, -3.811, 1, 1.256, -3.811, 1.361, -1.206, 1.467, -1.206, 1, 1.483, -1.206, 1.5, -1.238, 1.517, -1.238, 1, 1.633, -1.238, 1.75, 3.544, 1.867, 3.544, 1, 1.961, 3.544, 2.056, 1.01, 2.15, 1.01, 1, 2.189, 1.01, 2.228, 1.163, 2.267, 1.163, 1, 2.383, 1.163, 2.5, -3.92, 2.617, -3.92, 1, 2.711, -3.92, 2.806, -1.167, 2.9, -1.167, 1, 2.939, -1.167, 2.978, -1.339, 3.017, -1.339, 1, 3.133, -1.339, 3.25, 3.982, 3.367, 3.982, 1, 3.461, 3.982, 3.556, 1.333, 3.65, 1.333, 1, 3.689, 1.333, 3.728, 1.594, 3.767, 1.594, 1, 3.883, 1.594, 4, -3.676, 4.117, -3.676, 1, 4.211, -3.676, 4.306, -0.99, 4.4, -0.99, 1, 4.439, -0.99, 4.478, -1.277, 4.517, -1.277, 1, 4.633, -1.277, 4.75, 3.882, 4.867, 3.882, 1, 4.961, 3.882, 5.056, 1.031, 5.15, 1.031, 1, 5.189, 1.031, 5.228, 1.275, 5.267, 1.275, 1, 5.383, 1.275, 5.5, -4.04, 5.617, -4.04, 1, 5.711, -4.04, 5.806, -1.324, 5.9, -1.324, 1, 5.939, -1.324, 5.978, -1.586, 6.017, -1.586, 1, 6.133, -1.586, 6.25, 3.751, 6.367, 3.751, 1, 6.461, 3.751, 6.556, 1.03, 6.65, 1.03, 1, 6.694, 1.03, 6.739, 1.331, 6.783, 1.331, 1, 6.894, 1.331, 7.006, -3.77, 7.117, -3.77, 1, 7.211, -3.77, 7.306, -0.998, 7.4, -0.998, 1, 7.439, -0.998, 7.478, -1.232, 7.517, -1.232, 1, 7.633, -1.232, 7.75, 4.048, 7.867, 4.048, 1, 7.961, 4.048, 8.056, 1.319, 8.15, 1.319, 1, 8.189, 1.319, 8.228, 1.57, 8.267, 1.57, 1, 8.383, 1.57, 8.5, -3.764, 8.617, -3.764, 1, 8.711, -3.764, 8.806, -1.127, 8.9, -1.127, 1, 8.944, -1.127, 8.989, -1.449, 9.033, -1.449, 1, 9.144, -1.449, 9.256, 3.735, 9.367, 3.735, 1, 9.461, 3.735, 9.556, 0.945, 9.65, 0.945, 1, 9.689, 0.945, 9.728, 1.201, 9.767, 1.201, 1, 9.883, 1.201, 10, -4.043, 10.117, -4.043, 1, 10.211, -4.043, 10.306, -1.281, 10.4, -1.281, 1, 10.439, -1.281, 10.478, -1.519, 10.517, -1.519, 1, 10.633, -1.519, 10.75, 3.956, 10.867, 3.956, 1, 10.961, 3.956, 11.056, 1.063, 11.15, 1.063, 1, 11.194, 1.063, 11.239, 1.387, 11.283, 1.387, 1, 11.394, 1.387, 11.506, -3.715, 11.617, -3.715, 1, 11.711, -3.715, 11.806, -0.895, 11.9, -0.895, 1, 11.933, -0.895, 11.967, -1.054, 12, -1.1]}, {"Target": "Parameter", "Id": "ParamQianFa1_3", "Segments": [0, 0.163, 1, 0.006, 0.167, 0.011, 0.169, 0.017, 0.169, 1, 0.106, 0.169, 0.194, -2.116, 0.283, -2.116, 1, 0.372, -2.116, 0.461, 3.345, 0.55, 3.345, 1, 0.639, 3.345, 0.728, -1.41, 0.817, -1.41, 1, 0.889, -1.41, 0.961, 1.251, 1.033, 1.251, 1, 1.117, 1.251, 1.2, -1.841, 1.283, -1.841, 1, 1.361, -1.841, 1.439, 0.308, 1.517, 0.308, 1, 1.583, 0.308, 1.65, -1.101, 1.717, -1.101, 1, 1.806, -1.101, 1.894, 1.844, 1.983, 1.844, 1, 2.067, 1.844, 2.15, -0.465, 2.233, -0.465, 1, 2.306, -0.465, 2.378, 1.386, 2.45, 1.386, 1, 2.539, 1.386, 2.628, -2.051, 2.717, -2.051, 1, 2.8, -2.051, 2.883, 0.563, 2.967, 0.563, 1, 3.044, 0.563, 3.122, -1.496, 3.2, -1.496, 1, 3.289, -1.496, 3.378, 2.077, 3.467, 2.077, 1, 3.55, 2.077, 3.633, -0.619, 3.717, -0.619, 1, 3.794, -0.619, 3.872, 1.533, 3.95, 1.533, 1, 4.039, 1.533, 4.128, -2.098, 4.217, -2.098, 1, 4.3, -2.098, 4.383, 0.655, 4.467, 0.655, 1, 4.544, 0.655, 4.622, -1.514, 4.7, -1.514, 1, 4.789, -1.514, 4.878, 2.135, 4.967, 2.135, 1, 5.05, 2.135, 5.133, -0.646, 5.217, -0.646, 1, 5.294, -0.646, 5.372, 1.543, 5.45, 1.543, 1, 5.539, 1.543, 5.628, -2.117, 5.717, -2.117, 1, 5.8, -2.117, 5.883, 0.635, 5.967, 0.635, 1, 6.05, 0.635, 6.133, -1.402, 6.217, -1.402, 1, 6.3, -1.402, 6.383, 2.079, 6.467, 2.079, 1, 6.55, 2.079, 6.633, -0.648, 6.717, -0.648, 1, 6.794, -0.648, 6.872, 1.493, 6.95, 1.493, 1, 7.039, 1.493, 7.128, -2.098, 7.217, -2.098, 1, 7.3, -2.098, 7.383, 0.62, 7.467, 0.62, 1, 7.544, 0.62, 7.622, -1.545, 7.7, -1.545, 1, 7.789, -1.545, 7.878, 2.121, 7.967, 2.121, 1, 8.05, 2.121, 8.133, -0.631, 8.217, -0.631, 1, 8.294, -0.631, 8.372, 1.552, 8.45, 1.552, 1, 8.539, 1.552, 8.628, -2.093, 8.717, -2.093, 1, 8.8, -2.093, 8.883, 0.664, 8.967, 0.664, 1, 9.044, 0.664, 9.122, -1.54, 9.2, -1.54, 1, 9.289, -1.54, 9.378, 2.128, 9.467, 2.128, 1, 9.55, 2.128, 9.633, -0.647, 9.717, -0.647, 1, 9.794, -0.647, 9.872, 1.525, 9.95, 1.525, 1, 10.039, 1.525, 10.128, -2.12, 10.217, -2.12, 1, 10.3, -2.12, 10.383, 0.621, 10.467, 0.621, 1, 10.544, 0.621, 10.622, -1.479, 10.7, -1.479, 1, 10.794, -1.479, 10.889, 2.135, 10.983, 2.135, 1, 11.067, 2.135, 11.15, -0.699, 11.233, -0.699, 1, 11.306, -0.699, 11.378, 1.489, 11.45, 1.489, 1, 11.544, 1.489, 11.639, -2.108, 11.733, -2.108, 1, 11.817, -2.108, 11.9, 0.609, 11.983, 0.609, 1, 11.989, 0.609, 11.994, 0.597, 12, 0.574]}, {"Target": "Parameter", "Id": "ParamQianFa1_4", "Segments": [0, -0.146, 1, 0.039, 0.051, 0.078, 0.249, 0.117, 0.249, 1, 0.2, 0.249, 0.283, -2.292, 0.367, -2.292, 1, 0.461, -2.292, 0.556, 4.295, 0.65, 4.295, 1, 0.739, 4.295, 0.828, -2.513, 0.917, -2.513, 1, 0.994, -2.513, 1.072, 1.713, 1.15, 1.713, 1, 1.233, 1.713, 1.317, -2.44, 1.4, -2.44, 1, 1.478, -2.44, 1.556, 0.725, 1.633, 0.725, 1, 1.7, 0.725, 1.767, -1.037, 1.833, -1.037, 1, 1.922, -1.037, 2.011, 2.292, 2.1, 2.292, 1, 2.183, 2.292, 2.267, -0.919, 2.35, -0.919, 1, 2.422, -0.919, 2.494, 1.555, 2.567, 1.555, 1, 2.656, 1.555, 2.744, -2.694, 2.833, -2.694, 1, 2.917, -2.694, 3, 1.151, 3.083, 1.151, 1, 3.161, 1.151, 3.239, -1.754, 3.317, -1.754, 1, 3.406, -1.754, 3.494, 2.768, 3.583, 2.768, 1, 3.667, 2.768, 3.75, -1.231, 3.833, -1.231, 1, 3.911, -1.231, 3.989, 1.818, 4.067, 1.818, 1, 4.156, 1.818, 4.244, -2.808, 4.333, -2.808, 1, 4.417, -2.808, 4.5, 1.289, 4.583, 1.289, 1, 4.661, 1.289, 4.739, -1.807, 4.817, -1.807, 1, 4.906, -1.807, 4.994, 2.851, 5.083, 2.851, 1, 5.167, 2.851, 5.25, -1.287, 5.333, -1.287, 1, 5.411, -1.287, 5.489, 1.843, 5.567, 1.843, 1, 5.656, 1.843, 5.744, -2.841, 5.833, -2.841, 1, 5.917, -2.841, 6, 1.187, 6.083, 1.187, 1, 6.161, 1.187, 6.239, -1.81, 6.317, -1.81, 1, 6.406, -1.81, 6.494, 2.793, 6.583, 2.793, 1, 6.667, 2.793, 6.75, -1.27, 6.833, -1.27, 1, 6.911, -1.27, 6.989, 1.77, 7.067, 1.77, 1, 7.156, 1.77, 7.244, -2.798, 7.333, -2.798, 1, 7.417, -2.798, 7.5, 1.241, 7.583, 1.241, 1, 7.661, 1.241, 7.739, -1.816, 7.817, -1.816, 1, 7.906, -1.816, 7.994, 2.836, 8.083, 2.836, 1, 8.167, 2.836, 8.25, -1.264, 8.333, -1.264, 1, 8.411, -1.264, 8.489, 1.851, 8.567, 1.851, 1, 8.656, 1.851, 8.744, -2.811, 8.833, -2.811, 1, 8.917, -2.811, 9, 1.298, 9.083, 1.298, 1, 9.161, 1.298, 9.239, -1.843, 9.317, -1.843, 1, 9.406, -1.843, 9.494, 2.852, 9.583, 2.852, 1, 9.667, 2.852, 9.75, -1.29, 9.833, -1.29, 1, 9.911, -1.29, 9.989, 1.82, 10.067, 1.82, 1, 10.156, 1.82, 10.244, -2.839, 10.333, -2.839, 1, 10.417, -2.839, 10.5, 1.253, 10.583, 1.253, 1, 10.661, 1.253, 10.739, -1.826, 10.817, -1.826, 1, 10.906, -1.826, 10.994, 2.869, 11.083, 2.869, 1, 11.172, 2.869, 11.261, -1.349, 11.35, -1.349, 1, 11.422, -1.349, 11.494, 1.782, 11.567, 1.782, 1, 11.656, 1.782, 11.744, -2.819, 11.833, -2.819, 1, 11.889, -2.819, 11.944, -1.006, 12, 0.202]}, {"Target": "Parameter", "Id": "ParamQianFa2_1", "Segments": [0, 1.77, 1, 0.083, 2.628, 0.167, 3.459, 0.25, 3.459, 1, 0.389, 3.459, 0.528, -2.036, 0.667, -2.036, 1, 0.678, -2.036, 0.689, -2.019, 0.7, -2.019, 1, 0.706, -2.019, 0.711, -2.019, 0.717, -2.019, 1, 0.778, -2.019, 0.839, -2.595, 0.9, -2.595, 1, 1.15, -2.595, 1.4, 2.824, 1.65, 2.824, 1, 1.9, 2.824, 2.15, -2.956, 2.4, -2.956, 1, 2.65, -2.956, 2.9, 3.06, 3.15, 3.06, 1, 3.4, 3.06, 3.65, -2.874, 3.9, -2.874, 1, 4.15, -2.874, 4.4, 2.878, 4.65, 2.878, 1, 4.9, 2.878, 5.15, -3.017, 5.4, -3.017, 1, 5.65, -3.017, 5.9, 2.909, 6.15, 2.909, 1, 6.4, 2.909, 6.65, -2.836, 6.9, -2.836, 1, 7.15, -2.836, 7.4, 3.008, 7.65, 3.008, 1, 7.9, 3.008, 8.15, -2.947, 8.4, -2.947, 1, 8.65, -2.947, 8.9, 2.844, 9.15, 2.844, 1, 9.4, 2.844, 9.65, -2.982, 9.9, -2.982, 1, 10.15, -2.982, 10.4, 3.079, 10.65, 3.079, 1, 10.9, 3.079, 11.15, -2.886, 11.4, -2.886, 1, 11.6, -2.886, 11.8, 0.83, 12, 2.316]}, {"Target": "Parameter", "Id": "ParamQianFa2_2", "Segments": [0, -0.099, 1, 0.056, -0.099, 0.111, -1.482, 0.167, -1.482, 1, 0.267, -1.482, 0.367, 5.464, 0.467, 5.464, 1, 0.583, 5.464, 0.7, -0.054, 0.817, -0.054, 1, 0.822, -0.054, 0.828, -0.054, 0.833, -0.054, 1, 0.95, -0.054, 1.067, -3.563, 1.183, -3.563, 1, 1.422, -3.563, 1.661, 3.541, 1.9, 3.541, 1, 2.017, 3.541, 2.133, 1.113, 2.25, 1.113, 1, 2.256, 1.113, 2.261, 1.114, 2.267, 1.114, 1, 2.394, 1.114, 2.522, -4.024, 2.65, -4.024, 1, 2.767, -4.024, 2.883, -1.168, 3, -1.168, 1, 3.006, -1.168, 3.011, -1.17, 3.017, -1.17, 1, 3.144, -1.17, 3.272, 4.104, 3.4, 4.104, 1, 3.511, 4.104, 3.622, 1.323, 3.733, 1.323, 1, 3.75, 1.323, 3.767, 1.346, 3.783, 1.346, 1, 3.906, 1.346, 4.028, -3.725, 4.15, -3.725, 1, 4.261, -3.725, 4.372, -1.027, 4.483, -1.027, 1, 4.5, -1.027, 4.517, -1.045, 4.533, -1.045, 1, 4.656, -1.045, 4.778, 3.924, 4.9, 3.924, 1, 5.017, 3.924, 5.133, 1.058, 5.25, 1.058, 1, 5.261, 1.058, 5.272, 1.063, 5.283, 1.063, 1, 5.406, 1.063, 5.528, -4.087, 5.65, -4.087, 1, 5.767, -4.087, 5.883, -1.372, 6, -1.372, 1, 6.011, -1.372, 6.022, -1.379, 6.033, -1.379, 1, 6.156, -1.379, 6.278, 3.754, 6.4, 3.754, 1, 6.511, 3.754, 6.622, 1.099, 6.733, 1.099, 1, 6.75, 1.099, 6.767, 1.125, 6.783, 1.125, 1, 6.906, 1.125, 7.028, -3.814, 7.15, -3.814, 1, 7.267, -3.814, 7.383, -1.026, 7.5, -1.026, 1, 7.506, -1.026, 7.511, -1.029, 7.517, -1.029, 1, 7.644, -1.029, 7.772, 4.099, 7.9, 4.099, 1, 8.017, 4.099, 8.133, 1.361, 8.25, 1.361, 1, 8.256, 1.361, 8.261, 1.366, 8.267, 1.366, 1, 8.394, 1.366, 8.522, -3.811, 8.65, -3.811, 1, 8.761, -3.811, 8.872, -1.195, 8.983, -1.195, 1, 9, -1.195, 9.017, -1.233, 9.033, -1.233, 1, 9.156, -1.233, 9.278, 3.781, 9.4, 3.781, 1, 9.511, 3.781, 9.622, 0.976, 9.733, 0.976, 1, 9.75, 0.976, 9.767, 0.986, 9.783, 0.986, 1, 9.906, 0.986, 10.028, -4.09, 10.15, -4.09, 1, 10.239, -4.09, 10.328, -1.494, 10.417, -1.494, 1, 10.45, -1.494, 10.483, -1.636, 10.517, -1.636, 1, 10.644, -1.636, 10.772, 3.908, 10.9, 3.908, 1, 11.006, 3.908, 11.111, 1.318, 11.217, 1.318, 1, 11.239, 1.318, 11.261, 1.385, 11.283, 1.385, 1, 11.406, 1.385, 11.528, -3.8, 11.65, -3.8, 1, 11.761, -3.8, 11.872, -0.93, 11.983, -0.93, 1, 11.989, -0.93, 11.994, -0.931, 12, -0.934]}, {"Target": "Parameter", "Id": "ParamQianFa2_3", "Segments": [0, -0.688, 1, 0.094, -1.283, 0.189, -1.897, 0.283, -1.897, 1, 0.383, -1.897, 0.483, 3.328, 0.583, 3.328, 1, 0.678, 3.328, 0.772, -1.226, 0.867, -1.226, 1, 0.939, -1.226, 1.011, 0.632, 1.083, 0.632, 1, 1.167, 0.632, 1.25, -1.41, 1.333, -1.41, 1, 1.411, -1.41, 1.489, -0.263, 1.567, -0.263, 1, 1.617, -0.263, 1.667, -0.623, 1.717, -0.623, 1, 1.811, -0.623, 1.906, 1.615, 2, 1.615, 1, 2.089, 1.615, 2.178, -0.166, 2.267, -0.166, 1, 2.333, -0.166, 2.4, 1.072, 2.467, 1.072, 1, 2.561, 1.072, 2.656, -1.942, 2.75, -1.942, 1, 2.839, -1.942, 2.928, 0.352, 3.017, 0.352, 1, 3.089, 0.352, 3.161, -1.109, 3.233, -1.109, 1, 3.328, -1.109, 3.422, 1.967, 3.517, 1.967, 1, 3.6, 1.967, 3.683, -0.341, 3.767, -0.341, 1, 3.839, -0.341, 3.911, 1.091, 3.983, 1.091, 1, 4.078, 1.091, 4.172, -1.902, 4.267, -1.902, 1, 4.35, -1.902, 4.433, 0.311, 4.517, 0.311, 1, 4.589, 0.311, 4.661, -1.038, 4.733, -1.038, 1, 4.828, -1.038, 4.922, 1.923, 5.017, 1.923, 1, 5.1, 1.923, 5.183, -0.3, 5.267, -0.3, 1, 5.339, -0.3, 5.411, 1.082, 5.483, 1.082, 1, 5.578, 1.082, 5.672, -1.911, 5.767, -1.911, 1, 5.85, -1.911, 5.933, 0.289, 6.017, 0.289, 1, 6.089, 0.289, 6.161, -1.087, 6.233, -1.087, 1, 6.328, -1.087, 6.422, 1.891, 6.517, 1.891, 1, 6.6, 1.891, 6.683, -0.321, 6.767, -0.321, 1, 6.839, -0.321, 6.911, 1.05, 6.983, 1.05, 1, 7.078, 1.05, 7.172, -1.9, 7.267, -1.9, 1, 7.35, -1.9, 7.433, 0.283, 7.517, 0.283, 1, 7.589, 0.283, 7.661, -1.068, 7.733, -1.068, 1, 7.828, -1.068, 7.922, 1.911, 8.017, 1.911, 1, 8.1, 1.911, 8.183, -0.285, 8.267, -0.285, 1, 8.339, -0.285, 8.411, 1.099, 8.483, 1.099, 1, 8.578, 1.099, 8.672, -1.889, 8.767, -1.889, 1, 8.85, -1.889, 8.933, 0.325, 9.017, 0.325, 1, 9.089, 0.325, 9.161, -1.087, 9.233, -1.087, 1, 9.328, -1.087, 9.422, 1.928, 9.517, 1.928, 1, 9.6, 1.928, 9.683, -0.31, 9.767, -0.31, 1, 9.839, -0.31, 9.911, 1.064, 9.983, 1.064, 1, 10.078, 1.064, 10.172, -1.917, 10.267, -1.917, 1, 10.35, -1.917, 10.433, 0.351, 10.517, 0.351, 1, 10.583, 0.351, 10.65, -1.227, 10.717, -1.227, 1, 10.806, -1.227, 10.894, 2.052, 10.983, 2.052, 1, 11.072, 2.052, 11.161, -0.415, 11.25, -0.415, 1, 11.328, -0.415, 11.406, 1.206, 11.483, 1.206, 1, 11.578, 1.206, 11.672, -2.005, 11.767, -2.005, 1, 11.844, -2.005, 11.922, -0.191, 12, 0.262]}, {"Target": "Parameter", "Id": "ParamQianFa2_4", "Segments": [0, 0.375, 1, 0.128, -0.293, 0.256, -1.53, 0.383, -1.875, 1, 0.578, -2.4, 0.772, -2.347, 0.967, -2.347, 1, 1.05, -2.347, 1.133, 1.04, 1.217, 1.04, 1, 1.3, 1.04, 1.383, -1.85, 1.467, -1.85, 1, 1.55, -1.85, 1.633, 0.01, 1.717, 0.01, 1, 1.733, 0.01, 1.75, -0.076, 1.767, -0.076, 1, 1.883, -0.076, 2, 1.822, 2.117, 1.822, 1, 2.2, 1.822, 2.283, -0.485, 2.367, -0.485, 1, 2.439, -0.485, 2.511, 1.098, 2.583, 1.098, 1, 2.678, 1.098, 2.772, -2.485, 2.867, -2.485, 1, 2.956, -2.485, 3.044, 0.834, 3.133, 0.834, 1, 3.206, 0.834, 3.278, -1.341, 3.35, -1.341, 1, 3.439, -1.341, 3.528, 2.569, 3.617, 2.569, 1, 3.706, 2.569, 3.794, -0.881, 3.883, -0.881, 1, 3.956, -0.881, 4.028, 1.168, 4.1, 1.168, 1, 4.194, 1.168, 4.289, -2.439, 4.383, -2.439, 1, 4.472, -2.439, 4.561, 0.813, 4.65, 0.813, 1, 4.717, 0.813, 4.783, -1.074, 4.85, -1.074, 1, 4.944, -1.074, 5.039, 2.436, 5.133, 2.436, 1, 5.217, 2.436, 5.3, -0.795, 5.383, -0.795, 1, 5.456, -0.795, 5.528, 1.138, 5.6, 1.138, 1, 5.694, 1.138, 5.789, -2.442, 5.883, -2.442, 1, 5.967, -2.442, 6.05, 0.784, 6.133, 0.784, 1, 6.206, 0.784, 6.278, -1.12, 6.35, -1.12, 1, 6.444, -1.12, 6.539, 2.409, 6.633, 2.409, 1, 6.717, 2.409, 6.8, -0.815, 6.883, -0.815, 1, 6.956, -0.815, 7.028, 1.105, 7.1, 1.105, 1, 7.189, 1.105, 7.278, -2.421, 7.367, -2.421, 1, 7.461, -2.421, 7.556, 0.663, 7.65, 0.663, 1, 7.717, 0.663, 7.783, -1.065, 7.85, -1.065, 1, 7.939, -1.065, 8.028, 2.424, 8.117, 2.424, 1, 8.206, 2.424, 8.294, -0.773, 8.383, -0.773, 1, 8.456, -0.773, 8.528, 1.158, 8.6, 1.158, 1, 8.689, 1.158, 8.778, -2.42, 8.867, -2.42, 1, 8.956, -2.42, 9.044, 0.82, 9.133, 0.82, 1, 9.206, 0.82, 9.278, -1.152, 9.35, -1.152, 1, 9.444, -1.152, 9.539, 2.464, 9.633, 2.464, 1, 9.717, 2.464, 9.8, -0.816, 9.883, -0.816, 1, 9.956, -0.816, 10.028, 1.12, 10.1, 1.12, 1, 10.194, 1.12, 10.289, -2.445, 10.383, -2.445, 1, 10.467, -2.445, 10.55, 0.782, 10.633, 0.782, 1, 10.706, 0.782, 10.778, -1.549, 10.85, -1.549, 1, 10.939, -1.549, 11.028, 2.698, 11.117, 2.698, 1, 11.2, 2.698, 11.283, -0.996, 11.367, -0.996, 1, 11.444, -0.996, 11.522, 1.361, 11.6, 1.361, 1, 11.689, 1.361, 11.778, -2.62, 11.867, -2.62, 1, 11.911, -2.62, 11.956, -1.808, 12, -0.948]}, {"Target": "Parameter", "Id": "ParamQianFa3_1", "Segments": [0, 1.77, 1, 0.083, 2.628, 0.167, 3.459, 0.25, 3.459, 1, 0.389, 3.459, 0.528, -2.036, 0.667, -2.036, 1, 0.678, -2.036, 0.689, -2.019, 0.7, -2.019, 1, 0.706, -2.019, 0.711, -2.019, 0.717, -2.019, 1, 0.778, -2.019, 0.839, -2.595, 0.9, -2.595, 1, 1.15, -2.595, 1.4, 2.824, 1.65, 2.824, 1, 1.9, 2.824, 2.15, -2.956, 2.4, -2.956, 1, 2.65, -2.956, 2.9, 3.06, 3.15, 3.06, 1, 3.4, 3.06, 3.65, -2.874, 3.9, -2.874, 1, 4.15, -2.874, 4.4, 2.878, 4.65, 2.878, 1, 4.9, 2.878, 5.15, -3.017, 5.4, -3.017, 1, 5.65, -3.017, 5.9, 2.909, 6.15, 2.909, 1, 6.4, 2.909, 6.65, -2.836, 6.9, -2.836, 1, 7.15, -2.836, 7.4, 3.008, 7.65, 3.008, 1, 7.9, 3.008, 8.15, -2.947, 8.4, -2.947, 1, 8.65, -2.947, 8.9, 2.844, 9.15, 2.844, 1, 9.4, 2.844, 9.65, -2.982, 9.9, -2.982, 1, 10.15, -2.982, 10.4, 3.079, 10.65, 3.079, 1, 10.9, 3.079, 11.15, -2.886, 11.4, -2.886, 1, 11.6, -2.886, 11.8, 0.83, 12, 2.316]}, {"Target": "Parameter", "Id": "ParamQianFa3_2", "Segments": [0, -0.099, 1, 0.056, -0.099, 0.111, -1.482, 0.167, -1.482, 1, 0.267, -1.482, 0.367, 5.464, 0.467, 5.464, 1, 0.583, 5.464, 0.7, -0.054, 0.817, -0.054, 1, 0.822, -0.054, 0.828, -0.054, 0.833, -0.054, 1, 0.95, -0.054, 1.067, -3.563, 1.183, -3.563, 1, 1.422, -3.563, 1.661, 3.541, 1.9, 3.541, 1, 2.017, 3.541, 2.133, 1.113, 2.25, 1.113, 1, 2.256, 1.113, 2.261, 1.114, 2.267, 1.114, 1, 2.394, 1.114, 2.522, -4.024, 2.65, -4.024, 1, 2.767, -4.024, 2.883, -1.168, 3, -1.168, 1, 3.006, -1.168, 3.011, -1.17, 3.017, -1.17, 1, 3.144, -1.17, 3.272, 4.104, 3.4, 4.104, 1, 3.511, 4.104, 3.622, 1.323, 3.733, 1.323, 1, 3.75, 1.323, 3.767, 1.346, 3.783, 1.346, 1, 3.906, 1.346, 4.028, -3.725, 4.15, -3.725, 1, 4.261, -3.725, 4.372, -1.027, 4.483, -1.027, 1, 4.5, -1.027, 4.517, -1.045, 4.533, -1.045, 1, 4.656, -1.045, 4.778, 3.924, 4.9, 3.924, 1, 5.017, 3.924, 5.133, 1.058, 5.25, 1.058, 1, 5.261, 1.058, 5.272, 1.063, 5.283, 1.063, 1, 5.406, 1.063, 5.528, -4.087, 5.65, -4.087, 1, 5.767, -4.087, 5.883, -1.372, 6, -1.372, 1, 6.011, -1.372, 6.022, -1.379, 6.033, -1.379, 1, 6.156, -1.379, 6.278, 3.754, 6.4, 3.754, 1, 6.511, 3.754, 6.622, 1.099, 6.733, 1.099, 1, 6.75, 1.099, 6.767, 1.125, 6.783, 1.125, 1, 6.906, 1.125, 7.028, -3.814, 7.15, -3.814, 1, 7.267, -3.814, 7.383, -1.026, 7.5, -1.026, 1, 7.506, -1.026, 7.511, -1.029, 7.517, -1.029, 1, 7.644, -1.029, 7.772, 4.099, 7.9, 4.099, 1, 8.017, 4.099, 8.133, 1.361, 8.25, 1.361, 1, 8.256, 1.361, 8.261, 1.366, 8.267, 1.366, 1, 8.394, 1.366, 8.522, -3.811, 8.65, -3.811, 1, 8.761, -3.811, 8.872, -1.195, 8.983, -1.195, 1, 9, -1.195, 9.017, -1.233, 9.033, -1.233, 1, 9.156, -1.233, 9.278, 3.781, 9.4, 3.781, 1, 9.511, 3.781, 9.622, 0.976, 9.733, 0.976, 1, 9.75, 0.976, 9.767, 0.986, 9.783, 0.986, 1, 9.906, 0.986, 10.028, -4.09, 10.15, -4.09, 1, 10.239, -4.09, 10.328, -1.494, 10.417, -1.494, 1, 10.45, -1.494, 10.483, -1.636, 10.517, -1.636, 1, 10.644, -1.636, 10.772, 3.908, 10.9, 3.908, 1, 11.006, 3.908, 11.111, 1.318, 11.217, 1.318, 1, 11.239, 1.318, 11.261, 1.385, 11.283, 1.385, 1, 11.406, 1.385, 11.528, -3.8, 11.65, -3.8, 1, 11.761, -3.8, 11.872, -0.93, 11.983, -0.93, 1, 11.989, -0.93, 11.994, -0.931, 12, -0.934]}, {"Target": "Parameter", "Id": "ParamQianFa3_3", "Segments": [0, -0.688, 1, 0.094, -1.283, 0.189, -1.897, 0.283, -1.897, 1, 0.383, -1.897, 0.483, 3.328, 0.583, 3.328, 1, 0.678, 3.328, 0.772, -1.226, 0.867, -1.226, 1, 0.939, -1.226, 1.011, 0.632, 1.083, 0.632, 1, 1.167, 0.632, 1.25, -1.41, 1.333, -1.41, 1, 1.411, -1.41, 1.489, -0.263, 1.567, -0.263, 1, 1.617, -0.263, 1.667, -0.623, 1.717, -0.623, 1, 1.811, -0.623, 1.906, 1.615, 2, 1.615, 1, 2.089, 1.615, 2.178, -0.166, 2.267, -0.166, 1, 2.333, -0.166, 2.4, 1.072, 2.467, 1.072, 1, 2.561, 1.072, 2.656, -1.942, 2.75, -1.942, 1, 2.839, -1.942, 2.928, 0.352, 3.017, 0.352, 1, 3.089, 0.352, 3.161, -1.109, 3.233, -1.109, 1, 3.328, -1.109, 3.422, 1.967, 3.517, 1.967, 1, 3.6, 1.967, 3.683, -0.341, 3.767, -0.341, 1, 3.839, -0.341, 3.911, 1.091, 3.983, 1.091, 1, 4.078, 1.091, 4.172, -1.902, 4.267, -1.902, 1, 4.35, -1.902, 4.433, 0.311, 4.517, 0.311, 1, 4.589, 0.311, 4.661, -1.038, 4.733, -1.038, 1, 4.828, -1.038, 4.922, 1.923, 5.017, 1.923, 1, 5.1, 1.923, 5.183, -0.3, 5.267, -0.3, 1, 5.339, -0.3, 5.411, 1.082, 5.483, 1.082, 1, 5.578, 1.082, 5.672, -1.911, 5.767, -1.911, 1, 5.85, -1.911, 5.933, 0.289, 6.017, 0.289, 1, 6.089, 0.289, 6.161, -1.087, 6.233, -1.087, 1, 6.328, -1.087, 6.422, 1.891, 6.517, 1.891, 1, 6.6, 1.891, 6.683, -0.321, 6.767, -0.321, 1, 6.839, -0.321, 6.911, 1.05, 6.983, 1.05, 1, 7.078, 1.05, 7.172, -1.9, 7.267, -1.9, 1, 7.35, -1.9, 7.433, 0.283, 7.517, 0.283, 1, 7.589, 0.283, 7.661, -1.068, 7.733, -1.068, 1, 7.828, -1.068, 7.922, 1.911, 8.017, 1.911, 1, 8.1, 1.911, 8.183, -0.285, 8.267, -0.285, 1, 8.339, -0.285, 8.411, 1.099, 8.483, 1.099, 1, 8.578, 1.099, 8.672, -1.889, 8.767, -1.889, 1, 8.85, -1.889, 8.933, 0.325, 9.017, 0.325, 1, 9.089, 0.325, 9.161, -1.087, 9.233, -1.087, 1, 9.328, -1.087, 9.422, 1.928, 9.517, 1.928, 1, 9.6, 1.928, 9.683, -0.31, 9.767, -0.31, 1, 9.839, -0.31, 9.911, 1.064, 9.983, 1.064, 1, 10.078, 1.064, 10.172, -1.917, 10.267, -1.917, 1, 10.35, -1.917, 10.433, 0.351, 10.517, 0.351, 1, 10.583, 0.351, 10.65, -1.227, 10.717, -1.227, 1, 10.806, -1.227, 10.894, 2.052, 10.983, 2.052, 1, 11.072, 2.052, 11.161, -0.415, 11.25, -0.415, 1, 11.328, -0.415, 11.406, 1.206, 11.483, 1.206, 1, 11.578, 1.206, 11.672, -2.005, 11.767, -2.005, 1, 11.844, -2.005, 11.922, -0.191, 12, 0.262]}, {"Target": "Parameter", "Id": "ParamQianFa3_4", "Segments": [0, 0.375, 1, 0.128, -0.293, 0.256, -1.53, 0.383, -1.875, 1, 0.578, -2.4, 0.772, -2.347, 0.967, -2.347, 1, 1.05, -2.347, 1.133, 1.04, 1.217, 1.04, 1, 1.3, 1.04, 1.383, -1.85, 1.467, -1.85, 1, 1.55, -1.85, 1.633, 0.01, 1.717, 0.01, 1, 1.733, 0.01, 1.75, -0.076, 1.767, -0.076, 1, 1.883, -0.076, 2, 1.822, 2.117, 1.822, 1, 2.2, 1.822, 2.283, -0.485, 2.367, -0.485, 1, 2.439, -0.485, 2.511, 1.098, 2.583, 1.098, 1, 2.678, 1.098, 2.772, -2.485, 2.867, -2.485, 1, 2.956, -2.485, 3.044, 0.834, 3.133, 0.834, 1, 3.206, 0.834, 3.278, -1.341, 3.35, -1.341, 1, 3.439, -1.341, 3.528, 2.569, 3.617, 2.569, 1, 3.706, 2.569, 3.794, -0.881, 3.883, -0.881, 1, 3.956, -0.881, 4.028, 1.168, 4.1, 1.168, 1, 4.194, 1.168, 4.289, -2.439, 4.383, -2.439, 1, 4.472, -2.439, 4.561, 0.813, 4.65, 0.813, 1, 4.717, 0.813, 4.783, -1.074, 4.85, -1.074, 1, 4.944, -1.074, 5.039, 2.436, 5.133, 2.436, 1, 5.217, 2.436, 5.3, -0.795, 5.383, -0.795, 1, 5.456, -0.795, 5.528, 1.138, 5.6, 1.138, 1, 5.694, 1.138, 5.789, -2.442, 5.883, -2.442, 1, 5.967, -2.442, 6.05, 0.784, 6.133, 0.784, 1, 6.206, 0.784, 6.278, -1.12, 6.35, -1.12, 1, 6.444, -1.12, 6.539, 2.409, 6.633, 2.409, 1, 6.717, 2.409, 6.8, -0.815, 6.883, -0.815, 1, 6.956, -0.815, 7.028, 1.105, 7.1, 1.105, 1, 7.189, 1.105, 7.278, -2.421, 7.367, -2.421, 1, 7.461, -2.421, 7.556, 0.663, 7.65, 0.663, 1, 7.717, 0.663, 7.783, -1.065, 7.85, -1.065, 1, 7.939, -1.065, 8.028, 2.424, 8.117, 2.424, 1, 8.206, 2.424, 8.294, -0.773, 8.383, -0.773, 1, 8.456, -0.773, 8.528, 1.158, 8.6, 1.158, 1, 8.689, 1.158, 8.778, -2.42, 8.867, -2.42, 1, 8.956, -2.42, 9.044, 0.82, 9.133, 0.82, 1, 9.206, 0.82, 9.278, -1.152, 9.35, -1.152, 1, 9.444, -1.152, 9.539, 2.464, 9.633, 2.464, 1, 9.717, 2.464, 9.8, -0.816, 9.883, -0.816, 1, 9.956, -0.816, 10.028, 1.12, 10.1, 1.12, 1, 10.194, 1.12, 10.289, -2.445, 10.383, -2.445, 1, 10.467, -2.445, 10.55, 0.782, 10.633, 0.782, 1, 10.706, 0.782, 10.778, -1.549, 10.85, -1.549, 1, 10.939, -1.549, 11.028, 2.698, 11.117, 2.698, 1, 11.2, 2.698, 11.283, -0.996, 11.367, -0.996, 1, 11.444, -0.996, 11.522, 1.361, 11.6, 1.361, 1, 11.689, 1.361, 11.778, -2.62, 11.867, -2.62, 1, 11.911, -2.62, 11.956, -1.808, 12, -0.948]}, {"Target": "Parameter", "Id": "ParamQianFa3_5", "Segments": [0, 0.006, 1, 0.089, 0.006, 0.178, 3.63, 0.267, 3.63, 1, 0.489, 3.63, 0.711, -3.002, 0.933, -3.002, 1, 1.178, -3.002, 1.422, 2.972, 1.667, 2.972, 1, 1.917, 2.972, 2.167, -3.143, 2.417, -3.143, 1, 2.667, -3.143, 2.917, 3.232, 3.167, 3.232, 1, 3.417, 3.232, 3.667, -3.009, 3.917, -3.009, 1, 4.167, -3.009, 4.417, 3.076, 4.667, 3.076, 1, 4.917, 3.076, 5.167, -3.234, 5.417, -3.234, 1, 5.667, -3.234, 5.917, 3.038, 6.167, 3.038, 1, 6.172, 3.038, 6.178, 3.038, 6.183, 3.038, 1, 6.428, 3.038, 6.672, -3.007, 6.917, -3.007, 1, 7.167, -3.007, 7.417, 3.231, 7.667, 3.231, 1, 7.922, 3.231, 8.178, -3.085, 8.433, -3.085, 1, 8.678, -3.085, 8.922, 2.992, 9.167, 2.992, 1, 9.417, 2.992, 9.667, -3.21, 9.917, -3.21, 1, 10.172, -3.21, 10.428, 3.08, 10.683, 3.08, 1, 10.928, 3.08, 11.172, -2.945, 11.417, -2.945, 1, 11.611, -2.945, 11.806, 0.909, 12, 2.486]}, {"Target": "Parameter", "Id": "ParamQianFa3_6", "Segments": [0, -0.006, 1, 0.056, -0.006, 0.111, -1.6, 0.167, -1.6, 1, 0.267, -1.6, 0.367, 4.261, 0.467, 4.261, 1, 0.578, 4.261, 0.689, 0.419, 0.8, 0.419, 1, 0.806, 0.419, 0.811, 0.419, 0.817, 0.419, 1, 0.939, 0.419, 1.061, -2.768, 1.183, -2.768, 1, 1.422, -2.768, 1.661, 2.768, 1.9, 2.768, 1, 2, 2.768, 2.1, 1.111, 2.2, 1.111, 1, 2.228, 1.111, 2.256, 1.17, 2.283, 1.17, 1, 2.406, 1.17, 2.528, -3.053, 2.65, -3.053, 1, 2.756, -3.053, 2.861, -1.226, 2.967, -1.226, 1, 2.989, -1.226, 3.011, -1.258, 3.033, -1.258, 1, 3.156, -1.258, 3.278, 3.063, 3.4, 3.063, 1, 3.5, 3.063, 3.6, 1.357, 3.7, 1.357, 1, 3.728, 1.357, 3.756, 1.425, 3.783, 1.425, 1, 3.906, 1.425, 4.028, -2.835, 4.15, -2.835, 1, 4.25, -2.835, 4.35, -1.101, 4.45, -1.101, 1, 4.478, -1.101, 4.506, -1.179, 4.533, -1.179, 1, 4.656, -1.179, 4.778, 2.996, 4.9, 2.996, 1, 5, 2.996, 5.1, 1.137, 5.2, 1.137, 1, 5.228, 1.137, 5.256, 1.192, 5.283, 1.192, 1, 5.406, 1.192, 5.528, -3.096, 5.65, -3.096, 1, 5.75, -3.096, 5.85, -1.359, 5.95, -1.359, 1, 5.978, -1.359, 6.006, -1.42, 6.033, -1.42, 1, 6.156, -1.42, 6.278, 2.82, 6.4, 2.82, 1, 6.5, 2.82, 6.6, 1.145, 6.7, 1.145, 1, 6.728, 1.145, 6.756, 1.208, 6.783, 1.208, 1, 6.906, 1.208, 7.028, -2.901, 7.15, -2.901, 1, 7.25, -2.901, 7.35, -1.116, 7.45, -1.116, 1, 7.478, -1.116, 7.506, -1.165, 7.533, -1.165, 1, 7.656, -1.165, 7.778, 3.106, 7.9, 3.106, 1, 8, 3.106, 8.1, 1.354, 8.2, 1.354, 1, 8.228, 1.354, 8.256, 1.412, 8.283, 1.412, 1, 8.406, 1.412, 8.528, -2.89, 8.65, -2.89, 1, 8.744, -2.89, 8.839, -1.206, 8.933, -1.206, 1, 8.967, -1.206, 9, -1.304, 9.033, -1.304, 1, 9.156, -1.304, 9.278, 2.881, 9.4, 2.881, 1, 9.5, 2.881, 9.6, 1.074, 9.7, 1.074, 1, 9.728, 1.074, 9.756, 1.133, 9.783, 1.133, 1, 9.906, 1.133, 10.028, -3.102, 10.15, -3.102, 1, 10.406, -3.102, 10.661, 2.931, 10.917, 2.931, 1, 11.022, 2.931, 11.128, 1.21, 11.233, 1.21, 1, 11.25, 1.21, 11.267, 1.218, 11.283, 1.218, 1, 11.406, 1.218, 11.528, -2.8, 11.65, -2.8, 1, 11.756, -2.8, 11.861, -1.082, 11.967, -1.082, 1, 11.978, -1.082, 11.989, -1.093, 12, -1.1]}, {"Target": "Parameter", "Id": "ParamQianFa3_7", "Segments": [0, -0.046, 1, 0.006, -0.045, 0.011, -0.045, 0.017, -0.045, 1, 0.106, -0.045, 0.194, -1.917, 0.283, -1.917, 1, 0.383, -1.917, 0.483, 3.013, 0.583, 3.013, 1, 0.672, 3.013, 0.761, -0.786, 0.85, -0.786, 1, 0.917, -0.786, 0.983, 0.454, 1.05, 0.454, 1, 1.133, 0.454, 1.217, -1.256, 1.3, -1.256, 1, 1.378, -1.256, 1.456, -0.286, 1.533, -0.286, 1, 1.589, -0.286, 1.644, -0.805, 1.7, -0.805, 1, 1.8, -0.805, 1.9, 1.571, 2, 1.571, 1, 2.083, 1.571, 2.167, -0.057, 2.25, -0.057, 1, 2.322, -0.057, 2.394, 1.021, 2.467, 1.021, 1, 2.561, 1.021, 2.656, -1.731, 2.75, -1.731, 1, 2.833, -1.731, 2.917, 0.077, 3, 0.077, 1, 3.072, 0.077, 3.144, -1.038, 3.217, -1.038, 1, 3.311, -1.038, 3.406, 1.696, 3.5, 1.696, 1, 3.583, 1.696, 3.667, -0.093, 3.75, -0.093, 1, 3.822, -0.093, 3.894, 1.048, 3.967, 1.048, 1, 4.061, 1.048, 4.156, -1.698, 4.25, -1.698, 1, 4.333, -1.698, 4.417, 0.118, 4.5, 0.118, 1, 4.572, 0.118, 4.644, -1.015, 4.717, -1.015, 1, 4.811, -1.015, 4.906, 1.731, 5, 1.731, 1, 5.083, 1.731, 5.167, -0.103, 5.25, -0.103, 1, 5.322, -0.103, 5.394, 1.042, 5.467, 1.042, 1, 5.561, 1.042, 5.656, -1.708, 5.75, -1.708, 1, 5.833, -1.708, 5.917, 0.089, 6, 0.089, 1, 6.056, 0.089, 6.111, -0.798, 6.167, -0.798, 1, 6.278, -0.798, 6.389, 1.535, 6.5, 1.535, 1, 6.589, 1.535, 6.678, -0.039, 6.767, -0.039, 1, 6.833, -0.039, 6.9, 0.966, 6.967, 0.966, 1, 7.061, 0.966, 7.156, -1.682, 7.25, -1.682, 1, 7.328, -1.682, 7.406, -0.025, 7.483, -0.025, 1, 7.556, -0.025, 7.628, -1.047, 7.7, -1.047, 1, 7.8, -1.047, 7.9, 1.71, 8, 1.71, 1, 8.083, 1.71, 8.167, -0.087, 8.25, -0.087, 1, 8.322, -0.087, 8.394, 1.059, 8.467, 1.059, 1, 8.561, 1.059, 8.656, -1.687, 8.75, -1.687, 1, 8.833, -1.687, 8.917, 0.124, 9, 0.124, 1, 9.072, 0.124, 9.144, -1.042, 9.217, -1.042, 1, 9.311, -1.042, 9.406, 1.721, 9.5, 1.721, 1, 9.583, 1.721, 9.667, -0.103, 9.75, -0.103, 1, 9.822, -0.103, 9.894, 1.023, 9.967, 1.023, 1, 10.061, 1.023, 10.156, -1.713, 10.25, -1.713, 1, 10.339, -1.713, 10.428, -0.039, 10.517, -0.039, 1, 10.589, -0.039, 10.661, -0.869, 10.733, -0.869, 1, 10.828, -0.869, 10.922, 1.591, 11.017, 1.591, 1, 11.106, 1.591, 11.194, 0.005, 11.283, 0.005, 1, 11.344, 0.005, 11.406, 0.85, 11.467, 0.85, 1, 11.561, 0.85, 11.656, -1.609, 11.75, -1.609, 1, 11.833, -1.609, 11.917, 0.018, 12, 0.018]}, {"Target": "Parameter", "Id": "ParamQianFa3_8", "Segments": [0, -0.338, 1, 0.128, -0.99, 0.256, -1.998, 0.383, -1.998, 1, 0.578, -1.998, 0.772, -1.981, 0.967, -1.785, 1, 1.044, -1.707, 1.122, 0.619, 1.2, 0.619, 1, 1.278, 0.619, 1.356, -1.544, 1.433, -1.544, 1, 1.511, -1.544, 1.589, -0.151, 1.667, -0.151, 1, 1.717, -0.151, 1.767, -0.537, 1.817, -0.537, 1, 1.911, -0.537, 2.006, 1.916, 2.1, 1.916, 1, 2.189, 1.916, 2.278, -0.398, 2.367, -0.398, 1, 2.439, -0.398, 2.511, 1.031, 2.583, 1.031, 1, 2.672, 1.031, 2.761, -2.232, 2.85, -2.232, 1, 2.939, -2.232, 3.028, 0.503, 3.117, 0.503, 1, 3.189, 0.503, 3.261, -1.059, 3.333, -1.059, 1, 3.428, -1.059, 3.522, 2.196, 3.617, 2.196, 1, 3.7, 2.196, 3.783, -0.508, 3.867, -0.508, 1, 3.939, -0.508, 4.011, 1.071, 4.083, 1.071, 1, 4.178, 1.071, 4.272, -2.198, 4.367, -2.198, 1, 4.45, -2.198, 4.533, 0.541, 4.617, 0.541, 1, 4.689, 0.541, 4.761, -1.036, 4.833, -1.036, 1, 4.928, -1.036, 5.022, 2.229, 5.117, 2.229, 1, 5.2, 2.229, 5.283, -0.528, 5.367, -0.528, 1, 5.439, -0.528, 5.511, 1.066, 5.583, 1.066, 1, 5.678, 1.066, 5.772, -2.213, 5.867, -2.213, 1, 5.95, -2.213, 6.033, 0.574, 6.117, 0.574, 1, 6.183, 0.574, 6.25, -0.949, 6.317, -0.949, 1, 6.417, -0.949, 6.517, 1.996, 6.617, 1.996, 1, 6.7, 1.996, 6.783, -0.404, 6.867, -0.404, 1, 6.939, -0.404, 7.011, 0.955, 7.083, 0.955, 1, 7.178, 0.955, 7.272, -2.152, 7.367, -2.152, 1, 7.444, -2.152, 7.522, 0.587, 7.6, 0.587, 1, 7.678, 0.587, 7.756, -1.123, 7.833, -1.123, 1, 7.922, -1.123, 8.011, 2.238, 8.1, 2.238, 1, 8.189, 2.238, 8.278, -0.516, 8.367, -0.516, 1, 8.439, -0.516, 8.511, 1.092, 8.583, 1.092, 1, 8.678, 1.092, 8.772, -2.19, 8.867, -2.19, 1, 8.95, -2.19, 9.033, 0.542, 9.117, 0.542, 1, 9.189, 0.542, 9.261, -1.07, 9.333, -1.07, 1, 9.428, -1.07, 9.522, 2.227, 9.617, 2.227, 1, 9.7, 2.227, 9.783, -0.528, 9.867, -0.528, 1, 9.939, -0.528, 10.011, 1.04, 10.083, 1.04, 1, 10.178, 1.04, 10.272, -2.212, 10.367, -2.212, 1, 10.456, -2.212, 10.544, 0.445, 10.633, 0.445, 1, 10.7, 0.445, 10.767, -0.84, 10.833, -0.84, 1, 10.933, -0.84, 11.033, 2.033, 11.133, 2.033, 1, 11.222, 2.033, 11.311, -0.367, 11.4, -0.367, 1, 11.461, -0.367, 11.522, 0.764, 11.583, 0.764, 1, 11.678, 0.764, 11.772, -2.026, 11.867, -2.026, 1, 11.911, -2.026, 11.956, -1.425, 12, -0.825]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 1.261, 1, 0.106, 1.884, 0.211, 2.477, 0.317, 2.477, 1, 0.5, 2.477, 0.683, -3.028, 0.867, -3.028, 1, 1.117, -3.028, 1.367, 1.845, 1.617, 1.845, 1, 1.883, 1.845, 2.15, -2.302, 2.417, -2.302, 1, 2.656, -2.302, 2.894, 2.207, 3.133, 2.207, 1, 3.389, 2.207, 3.644, -2.146, 3.9, -2.146, 1, 4.15, -2.146, 4.4, 2.226, 4.65, 2.226, 1, 4.9, 2.226, 5.15, -2.194, 5.4, -2.194, 1, 5.65, -2.194, 5.9, 2.19, 6.15, 2.19, 1, 6.4, 2.19, 6.65, -2.198, 6.9, -2.198, 1, 7.15, -2.198, 7.4, 2.208, 7.65, 2.208, 1, 7.9, 2.208, 8.15, -2.189, 8.4, -2.189, 1, 8.65, -2.189, 8.9, 2.194, 9.15, 2.194, 1, 9.4, 2.194, 9.65, -2.208, 9.9, -2.208, 1, 10.15, -2.208, 10.4, 2.19, 10.65, 2.19, 1, 10.9, 2.19, 11.15, -2.19, 11.4, -2.19, 1, 11.6, -2.19, 11.8, 0.594, 12, 1.708]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0.162, 1, 0.006, 0.179, 0.011, 0.189, 0.017, 0.189, 1, 0.078, 0.189, 0.139, -1.241, 0.2, -1.241, 1, 0.322, -1.241, 0.444, 6.997, 0.567, 6.997, 1, 0.75, 6.997, 0.933, -6.462, 1.117, -6.462, 1, 1.4, -6.462, 1.683, 4.582, 1.967, 4.582, 1, 2.206, 4.582, 2.444, -5.57, 2.683, -5.57, 1, 2.928, -5.57, 3.172, 5.219, 3.417, 5.219, 1, 3.672, 5.219, 3.928, -5.178, 4.183, -5.178, 1, 4.433, -5.178, 4.683, 5.302, 4.933, 5.302, 1, 5.183, 5.302, 5.433, -5.223, 5.683, -5.223, 1, 5.933, -5.223, 6.183, 5.203, 6.433, 5.203, 1, 6.683, 5.203, 6.933, -5.256, 7.183, -5.256, 1, 7.433, -5.256, 7.683, 5.264, 7.933, 5.264, 1, 8.183, 5.264, 8.433, -5.192, 8.683, -5.192, 1, 8.933, -5.192, 9.183, 5.244, 9.433, 5.244, 1, 9.683, 5.244, 9.933, -5.27, 10.183, -5.27, 1, 10.433, -5.27, 10.683, 5.197, 10.933, 5.197, 1, 11.183, 5.197, 11.433, -5.228, 11.683, -5.228, 1, 11.789, -5.228, 11.894, -3.374, 12, -1.232]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, -0.649, 1, 0.056, -0.496, 0.111, 0.343, 0.167, 0.343, 1, 0.244, 0.343, 0.322, -2.747, 0.4, -2.747, 1, 0.511, -2.747, 0.622, 4.579, 0.733, 4.579, 1, 0.894, 4.579, 1.056, -2.756, 1.217, -2.756, 1, 1.356, -2.756, 1.494, 0.069, 1.633, 0.069, 1, 1.689, 0.069, 1.744, -0.228, 1.8, -0.228, 1, 1.933, -0.228, 2.067, 1.725, 2.2, 1.725, 1, 2.422, 1.725, 2.644, -2.166, 2.867, -2.166, 1, 3.111, -2.166, 3.356, 1.849, 3.6, 1.849, 1, 3.861, 1.849, 4.122, -1.959, 4.383, -1.959, 1, 4.628, -1.959, 4.872, 1.922, 5.117, 1.922, 1, 5.367, 1.922, 5.617, -1.895, 5.867, -1.895, 1, 6.117, -1.895, 6.367, 1.921, 6.617, 1.921, 1, 6.867, 1.921, 7.117, -1.912, 7.367, -1.912, 1, 7.617, -1.912, 7.867, 1.914, 8.117, 1.914, 1, 8.367, 1.914, 8.617, -1.909, 8.867, -1.909, 1, 9.122, -1.909, 9.378, 1.902, 9.633, 1.902, 1, 9.878, 1.902, 10.122, -1.908, 10.367, -1.908, 1, 10.617, -1.908, 10.867, 1.909, 11.117, 1.909, 1, 11.367, 1.909, 11.617, -1.928, 11.867, -1.928, 1, 11.911, -1.928, 11.956, -1.813, 12, -1.623]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, -0.739, 1, 0.028, -0.845, 0.056, -0.909, 0.083, -0.909, 1, 0.144, -0.909, 0.206, 0, 0.267, 0, 1, 0.35, 0, 0.433, -2.695, 0.517, -2.695, 1, 0.628, -2.695, 0.739, 5.85, 0.85, 5.85, 1, 0.989, 5.85, 1.128, -4.01, 1.267, -4.01, 1, 1.411, -4.01, 1.556, 0.637, 1.7, 0.637, 1, 1.778, 0.637, 1.856, -0.258, 1.933, -0.258, 1, 2.056, -0.258, 2.178, 2.102, 2.3, 2.102, 1, 2.517, 2.102, 2.733, -2.524, 2.95, -2.524, 1, 3.206, -2.524, 3.461, 2.067, 3.717, 2.067, 1, 3.972, 2.067, 4.228, -2.258, 4.483, -2.258, 1, 4.728, -2.258, 4.972, 2.167, 5.217, 2.167, 1, 5.467, 2.167, 5.717, -2.153, 5.967, -2.153, 1, 6.217, -2.153, 6.467, 2.185, 6.717, 2.185, 1, 6.967, 2.185, 7.217, -2.17, 7.467, -2.17, 1, 7.717, -2.17, 7.967, 2.177, 8.217, 2.177, 1, 8.467, 2.177, 8.717, -2.169, 8.967, -2.169, 1, 9.222, -2.169, 9.478, 2.104, 9.733, 2.104, 1, 9.978, 2.104, 10.222, -2.166, 10.467, -2.166, 1, 10.717, -2.166, 10.967, 2.171, 11.217, 2.171, 1, 11.467, 2.171, 11.717, -2.187, 11.967, -2.187, 1, 11.978, -2.187, 11.989, -2.179, 12, -2.164]}, {"Target": "Parameter", "Id": "Param11", "Segments": [0, -0.894, 1, 0.011, -0.944, 0.022, -0.971, 0.033, -0.971, 1, 0.072, -0.971, 0.111, -0.914, 0.15, -0.914, 1, 0.161, -0.914, 0.172, -0.954, 0.183, -0.954, 1, 0.25, -0.954, 0.317, -0.192, 0.383, -0.192, 1, 0.461, -0.192, 0.539, -2.738, 0.617, -2.738, 1, 0.728, -2.738, 0.839, 7.16, 0.95, 7.16, 1, 1.083, 7.16, 1.217, -5.978, 1.35, -5.978, 1, 1.483, -5.978, 1.617, 1.609, 1.75, 1.609, 1, 1.85, 1.609, 1.95, -0.58, 2.05, -0.58, 1, 2.167, -0.58, 2.283, 2.598, 2.4, 2.598, 1, 2.611, 2.598, 2.822, -2.808, 3.033, -2.808, 1, 3.294, -2.808, 3.556, 2.178, 3.817, 2.178, 1, 4.072, 2.178, 4.328, -2.481, 4.583, -2.481, 1, 4.828, -2.481, 5.072, 2.292, 5.317, 2.292, 1, 5.572, 2.292, 5.828, -2.327, 6.083, -2.327, 1, 6.328, -2.327, 6.572, 2.363, 6.817, 2.363, 1, 7.067, 2.363, 7.317, -2.339, 7.567, -2.339, 1, 7.817, -2.339, 8.067, 2.353, 8.317, 2.353, 1, 8.567, 2.353, 8.817, -2.34, 9.067, -2.34, 1, 9.317, -2.34, 9.567, 2.275, 9.817, 2.275, 1, 10.067, 2.275, 10.317, -2.344, 10.567, -2.344, 1, 10.817, -2.344, 11.067, 2.347, 11.317, 2.347, 1, 11.544, 2.347, 11.772, -1.56, 12, -2.254]}, {"Target": "Parameter", "Id": "Param12", "Segments": [0, -0.172, 1, 0.05, -1.058, 0.1, -1.786, 0.15, -1.786, 1, 0.25, -1.786, 0.35, -0.041, 0.45, -0.041, 1, 0.544, -0.041, 0.639, -2.696, 0.733, -2.696, 1, 0.844, -2.696, 0.956, 8.525, 1.067, 8.525, 1, 1.189, 8.525, 1.311, -8.519, 1.433, -8.519, 1, 1.567, -8.519, 1.7, 3.337, 1.833, 3.337, 1, 1.939, 3.337, 2.044, -1.34, 2.15, -1.34, 1, 2.267, -1.34, 2.383, 3.317, 2.5, 3.317, 1, 2.7, 3.317, 2.9, -3.023, 3.1, -3.023, 1, 3.372, -3.023, 3.644, 2.152, 3.917, 2.152, 1, 4.178, 2.152, 4.439, -2.603, 4.7, -2.603, 1, 4.939, -2.603, 5.178, 2.323, 5.417, 2.323, 1, 5.667, 2.323, 5.917, -2.396, 6.167, -2.396, 1, 6.417, -2.396, 6.667, 2.432, 6.917, 2.432, 1, 7.167, 2.432, 7.417, -2.397, 7.667, -2.397, 1, 7.917, -2.397, 8.167, 2.419, 8.417, 2.419, 1, 8.667, 2.419, 8.917, -2.401, 9.167, -2.401, 1, 9.411, -2.401, 9.656, 2.349, 9.9, 2.349, 1, 10.156, 2.349, 10.411, -2.424, 10.667, -2.424, 1, 10.917, -2.424, 11.167, 2.416, 11.417, 2.416, 1, 11.611, 2.416, 11.806, -0.509, 12, -1.809]}, {"Target": "Parameter", "Id": "Param13", "Segments": [0, 1.491, 1, 0.111, 2.376, 0.222, 3.241, 0.333, 3.241, 1, 0.517, 3.241, 0.7, -4.616, 0.883, -4.616, 1, 1.122, -4.616, 1.361, 2.81, 1.6, 2.81, 1, 1.872, 2.81, 2.144, -3.37, 2.417, -3.37, 1, 2.661, -3.37, 2.906, 3.362, 3.15, 3.362, 1, 3.4, 3.362, 3.65, -3.243, 3.9, -3.243, 1, 4.15, -3.243, 4.4, 3.238, 4.65, 3.238, 1, 4.9, 3.238, 5.15, -3.371, 5.4, -3.371, 1, 5.65, -3.371, 5.9, 3.266, 6.15, 3.266, 1, 6.4, 3.266, 6.65, -3.188, 6.9, -3.188, 1, 7.15, -3.188, 7.4, 3.363, 7.65, 3.363, 1, 7.9, 3.363, 8.15, -3.304, 8.4, -3.304, 1, 8.65, -3.304, 8.9, 3.173, 9.15, 3.173, 1, 9.4, 3.173, 9.65, -3.355, 9.9, -3.355, 1, 10.15, -3.355, 10.4, 3.341, 10.65, 3.341, 1, 10.9, 3.341, 11.15, -3.21, 11.4, -3.21, 1, 11.6, -3.21, 11.8, 1.168, 12, 2.76]}, {"Target": "Parameter", "Id": "Param14", "Segments": [0, -0.669, 1, 0.067, -0.669, 0.133, -0.811, 0.2, -1.593, 1, 0.55, -5.702, 0.9, -8.091, 1.25, -8.091, 1, 1.511, -8.091, 1.772, 5.013, 2.033, 5.013, 1, 2.283, 5.013, 2.533, -6.5, 2.783, -6.5, 1, 3.033, -6.5, 3.283, 6.263, 3.533, 6.263, 1, 3.783, 6.263, 4.033, -5.905, 4.283, -5.905, 1, 4.533, -5.905, 4.783, 6.105, 5.033, 6.105, 1, 5.283, 6.105, 5.533, -6.332, 5.783, -6.332, 1, 6.033, -6.332, 6.283, 5.939, 6.533, 5.939, 1, 6.783, 5.939, 7.033, -5.955, 7.283, -5.955, 1, 7.533, -5.955, 7.783, 6.407, 8.033, 6.407, 1, 8.283, 6.407, 8.533, -6.011, 8.783, -6.011, 1, 9.033, -6.011, 9.283, 5.895, 9.533, 5.895, 1, 9.783, 5.895, 10.033, -6.36, 10.283, -6.36, 1, 10.533, -6.36, 10.783, 6.117, 11.033, 6.117, 1, 11.283, 6.117, 11.533, -5.911, 11.783, -5.911, 1, 11.856, -5.911, 11.928, -4.844, 12, -3.341]}, {"Target": "Parameter", "Id": "Param15", "Segments": [0, 0.117, 1, 0.144, -0.636, 0.289, -1.828, 0.433, -1.828, 1, 0.6, -1.828, 0.767, 3.914, 0.933, 3.914, 1, 1.111, 3.914, 1.289, -3.644, 1.467, -3.644, 1, 1.756, -3.644, 2.044, 1.873, 2.333, 1.873, 1, 2.567, 1.873, 2.8, -2.72, 3.033, -2.72, 1, 3.283, -2.72, 3.533, 2.409, 3.783, 2.409, 1, 4.033, 2.409, 4.283, -2.437, 4.533, -2.437, 1, 4.783, -2.437, 5.033, 2.481, 5.283, 2.481, 1, 5.533, 2.481, 5.783, -2.442, 6.033, -2.442, 1, 6.283, -2.442, 6.533, 2.416, 6.783, 2.416, 1, 7.033, 2.416, 7.283, -2.444, 7.533, -2.444, 1, 7.783, -2.444, 8.033, 2.492, 8.283, 2.492, 1, 8.533, 2.492, 8.783, -2.405, 9.033, -2.405, 1, 9.283, -2.405, 9.533, 2.445, 9.783, 2.445, 1, 10.039, 2.445, 10.294, -2.579, 10.55, -2.579, 1, 10.794, -2.579, 11.039, 2.42, 11.283, 2.42, 1, 11.522, 2.42, 11.761, -2.059, 12, -2.457]}, {"Target": "Parameter", "Id": "Param17", "Segments": [0, 0.888, 1, 0.189, 0.888, 0.378, -1.942, 0.567, -1.942, 1, 0.739, -1.942, 0.911, 4.293, 1.083, 4.293, 1, 1.267, 4.293, 1.45, -4.429, 1.633, -4.429, 1, 1.861, -4.429, 2.089, 2.206, 2.317, 2.206, 1, 2.611, 2.206, 2.906, -3.057, 3.2, -3.057, 1, 3.439, -3.057, 3.678, 2.811, 3.917, 2.811, 1, 4.172, 2.811, 4.428, -2.791, 4.683, -2.791, 1, 4.928, -2.791, 5.172, 2.857, 5.417, 2.857, 1, 5.672, 2.857, 5.928, -2.837, 6.183, -2.837, 1, 6.428, -2.837, 6.672, 2.778, 6.917, 2.778, 1, 7.167, 2.778, 7.417, -2.811, 7.667, -2.811, 1, 7.922, -2.811, 8.178, 2.845, 8.433, 2.845, 1, 8.678, 2.845, 8.922, -2.79, 9.167, -2.79, 1, 9.422, -2.79, 9.678, 2.796, 9.933, 2.796, 1, 10.183, 2.796, 10.433, -2.875, 10.683, -2.875, 1, 10.928, -2.875, 11.172, 2.795, 11.417, 2.795, 1, 11.611, 2.795, 11.806, -0.62, 12, -2.137]}, {"Target": "Parameter", "Id": "Param18", "Segments": [0, -0.229, 1, 0.067, 0.445, 0.133, 1.094, 0.2, 1.094, 1, 0.367, 1.094, 0.533, -2.173, 0.7, -2.173, 1, 0.883, -2.173, 1.067, 4.733, 1.25, 4.733, 1, 1.428, 4.733, 1.606, -5.256, 1.783, -5.256, 1, 1.989, -5.256, 2.194, 2.982, 2.4, 2.982, 1, 2.717, 2.982, 3.033, -3.352, 3.35, -3.352, 1, 3.578, -3.352, 3.806, 3.291, 4.033, 3.291, 1, 4.294, 3.291, 4.556, -3.168, 4.817, -3.168, 1, 5.061, -3.168, 5.306, 3.263, 5.55, 3.263, 1, 5.8, 3.263, 6.05, -3.205, 6.3, -3.205, 1, 6.55, -3.205, 6.8, 3.175, 7.05, 3.175, 1, 7.3, 3.175, 7.55, -3.211, 7.8, -3.211, 1, 8.056, -3.211, 8.311, 3.228, 8.567, 3.228, 1, 8.811, 3.228, 9.056, -3.191, 9.3, -3.191, 1, 9.556, -3.191, 9.811, 3.182, 10.067, 3.182, 1, 10.317, 3.182, 10.567, -3.268, 10.817, -3.268, 1, 11.061, -3.268, 11.306, 3.21, 11.55, 3.21, 1, 11.7, 3.21, 11.85, 0.886, 12, -0.973]}, {"Target": "Parameter", "Id": "Param22", "Segments": [0, 0.162, 1, 0.006, 0.179, 0.011, 0.189, 0.017, 0.189, 1, 0.078, 0.189, 0.139, -1.241, 0.2, -1.241, 1, 0.322, -1.241, 0.444, 6.997, 0.567, 6.997, 1, 0.75, 6.997, 0.933, -6.462, 1.117, -6.462, 1, 1.4, -6.462, 1.683, 4.582, 1.967, 4.582, 1, 2.206, 4.582, 2.444, -5.57, 2.683, -5.57, 1, 2.928, -5.57, 3.172, 5.219, 3.417, 5.219, 1, 3.672, 5.219, 3.928, -5.178, 4.183, -5.178, 1, 4.433, -5.178, 4.683, 5.302, 4.933, 5.302, 1, 5.183, 5.302, 5.433, -5.223, 5.683, -5.223, 1, 5.933, -5.223, 6.183, 5.203, 6.433, 5.203, 1, 6.683, 5.203, 6.933, -5.256, 7.183, -5.256, 1, 7.433, -5.256, 7.683, 5.264, 7.933, 5.264, 1, 8.183, 5.264, 8.433, -5.192, 8.683, -5.192, 1, 8.933, -5.192, 9.183, 5.244, 9.433, 5.244, 1, 9.683, 5.244, 9.933, -5.27, 10.183, -5.27, 1, 10.433, -5.27, 10.683, 5.197, 10.933, 5.197, 1, 11.183, 5.197, 11.433, -5.228, 11.683, -5.228, 1, 11.789, -5.228, 11.894, -3.374, 12, -1.232]}, {"Target": "Parameter", "Id": "Param23", "Segments": [0, -0.649, 1, 0.056, -0.496, 0.111, 0.343, 0.167, 0.343, 1, 0.244, 0.343, 0.322, -2.747, 0.4, -2.747, 1, 0.511, -2.747, 0.622, 4.579, 0.733, 4.579, 1, 0.894, 4.579, 1.056, -2.756, 1.217, -2.756, 1, 1.356, -2.756, 1.494, 0.069, 1.633, 0.069, 1, 1.689, 0.069, 1.744, -0.228, 1.8, -0.228, 1, 1.933, -0.228, 2.067, 1.725, 2.2, 1.725, 1, 2.422, 1.725, 2.644, -2.166, 2.867, -2.166, 1, 3.111, -2.166, 3.356, 1.849, 3.6, 1.849, 1, 3.861, 1.849, 4.122, -1.959, 4.383, -1.959, 1, 4.628, -1.959, 4.872, 1.922, 5.117, 1.922, 1, 5.367, 1.922, 5.617, -1.895, 5.867, -1.895, 1, 6.117, -1.895, 6.367, 1.921, 6.617, 1.921, 1, 6.867, 1.921, 7.117, -1.912, 7.367, -1.912, 1, 7.617, -1.912, 7.867, 1.914, 8.117, 1.914, 1, 8.367, 1.914, 8.617, -1.909, 8.867, -1.909, 1, 9.122, -1.909, 9.378, 1.902, 9.633, 1.902, 1, 9.878, 1.902, 10.122, -1.908, 10.367, -1.908, 1, 10.617, -1.908, 10.867, 1.909, 11.117, 1.909, 1, 11.367, 1.909, 11.617, -1.928, 11.867, -1.928, 1, 11.911, -1.928, 11.956, -1.813, 12, -1.623]}, {"Target": "Parameter", "Id": "Param24", "Segments": [0, -0.739, 1, 0.028, -0.845, 0.056, -0.909, 0.083, -0.909, 1, 0.144, -0.909, 0.206, 0, 0.267, 0, 1, 0.35, 0, 0.433, -2.695, 0.517, -2.695, 1, 0.628, -2.695, 0.739, 5.85, 0.85, 5.85, 1, 0.989, 5.85, 1.128, -4.01, 1.267, -4.01, 1, 1.411, -4.01, 1.556, 0.637, 1.7, 0.637, 1, 1.778, 0.637, 1.856, -0.258, 1.933, -0.258, 1, 2.056, -0.258, 2.178, 2.102, 2.3, 2.102, 1, 2.517, 2.102, 2.733, -2.524, 2.95, -2.524, 1, 3.206, -2.524, 3.461, 2.067, 3.717, 2.067, 1, 3.972, 2.067, 4.228, -2.258, 4.483, -2.258, 1, 4.728, -2.258, 4.972, 2.167, 5.217, 2.167, 1, 5.467, 2.167, 5.717, -2.153, 5.967, -2.153, 1, 6.217, -2.153, 6.467, 2.185, 6.717, 2.185, 1, 6.967, 2.185, 7.217, -2.17, 7.467, -2.17, 1, 7.717, -2.17, 7.967, 2.177, 8.217, 2.177, 1, 8.467, 2.177, 8.717, -2.169, 8.967, -2.169, 1, 9.222, -2.169, 9.478, 2.104, 9.733, 2.104, 1, 9.978, 2.104, 10.222, -2.166, 10.467, -2.166, 1, 10.717, -2.166, 10.967, 2.171, 11.217, 2.171, 1, 11.467, 2.171, 11.717, -2.187, 11.967, -2.187, 1, 11.978, -2.187, 11.989, -2.179, 12, -2.164]}, {"Target": "Parameter", "Id": "Param25", "Segments": [0, -0.894, 1, 0.011, -0.944, 0.022, -0.971, 0.033, -0.971, 1, 0.072, -0.971, 0.111, -0.914, 0.15, -0.914, 1, 0.161, -0.914, 0.172, -0.954, 0.183, -0.954, 1, 0.25, -0.954, 0.317, -0.192, 0.383, -0.192, 1, 0.461, -0.192, 0.539, -2.738, 0.617, -2.738, 1, 0.728, -2.738, 0.839, 7.16, 0.95, 7.16, 1, 1.083, 7.16, 1.217, -5.978, 1.35, -5.978, 1, 1.483, -5.978, 1.617, 1.609, 1.75, 1.609, 1, 1.85, 1.609, 1.95, -0.58, 2.05, -0.58, 1, 2.167, -0.58, 2.283, 2.598, 2.4, 2.598, 1, 2.611, 2.598, 2.822, -2.808, 3.033, -2.808, 1, 3.294, -2.808, 3.556, 2.178, 3.817, 2.178, 1, 4.072, 2.178, 4.328, -2.481, 4.583, -2.481, 1, 4.828, -2.481, 5.072, 2.292, 5.317, 2.292, 1, 5.572, 2.292, 5.828, -2.327, 6.083, -2.327, 1, 6.328, -2.327, 6.572, 2.363, 6.817, 2.363, 1, 7.067, 2.363, 7.317, -2.339, 7.567, -2.339, 1, 7.817, -2.339, 8.067, 2.353, 8.317, 2.353, 1, 8.567, 2.353, 8.817, -2.34, 9.067, -2.34, 1, 9.317, -2.34, 9.567, 2.275, 9.817, 2.275, 1, 10.067, 2.275, 10.317, -2.344, 10.567, -2.344, 1, 10.817, -2.344, 11.067, 2.347, 11.317, 2.347, 1, 11.544, 2.347, 11.772, -1.56, 12, -2.254]}, {"Target": "Parameter", "Id": "Param135", "Segments": [0, 0.263, 1, 0.128, 0.949, 0.256, 2.606, 0.383, 2.606, 1, 0.578, 2.606, 0.772, -3.395, 0.967, -3.395, 1, 1.211, -3.395, 1.456, 2.62, 1.7, 2.62, 1, 1.956, 2.62, 2.211, -2.751, 2.467, -2.751, 1, 2.717, -2.751, 2.967, 2.89, 3.217, 2.89, 1, 3.467, 2.89, 3.717, -2.676, 3.967, -2.676, 1, 4.217, -2.676, 4.467, 2.73, 4.717, 2.73, 1, 4.967, 2.73, 5.217, -2.864, 5.467, -2.864, 1, 5.717, -2.864, 5.967, 2.704, 6.217, 2.704, 1, 6.467, 2.704, 6.717, -2.676, 6.967, -2.676, 1, 7.217, -2.676, 7.467, 2.859, 7.717, 2.859, 1, 7.967, 2.859, 8.217, -2.745, 8.467, -2.745, 1, 8.717, -2.745, 8.967, 2.653, 9.217, 2.653, 1, 9.467, 2.653, 9.717, -2.849, 9.967, -2.849, 1, 10.217, -2.849, 10.467, 2.793, 10.717, 2.793, 1, 10.967, 2.793, 11.217, -2.663, 11.467, -2.663, 1, 11.644, -2.663, 11.822, 0.109, 12, 1.71]}, {"Target": "Parameter", "Id": "Param136", "Segments": [0, -0.475, 1, 0.061, -0.582, 0.122, -0.661, 0.183, -0.661, 1, 0.322, -0.661, 0.461, 5.014, 0.6, 5.014, 1, 0.8, 5.014, 1, -5.325, 1.2, -5.325, 1, 1.456, -5.325, 1.711, 4.067, 1.967, 4.067, 1, 2.217, 4.067, 2.467, -4.509, 2.717, -4.509, 1, 2.967, -4.509, 3.217, 4.771, 3.467, 4.771, 1, 3.717, 4.771, 3.967, -4.237, 4.217, -4.237, 1, 4.467, -4.237, 4.717, 4.428, 4.967, 4.428, 1, 5.217, 4.428, 5.467, -4.636, 5.717, -4.636, 1, 5.967, -4.636, 6.217, 4.307, 6.467, 4.307, 1, 6.717, 4.307, 6.967, -4.307, 7.217, -4.307, 1, 7.467, -4.307, 7.717, 4.645, 7.967, 4.645, 1, 8.217, 4.645, 8.467, -4.358, 8.717, -4.358, 1, 8.967, -4.358, 9.217, 4.208, 9.467, 4.208, 1, 9.717, 4.208, 9.967, -4.635, 10.217, -4.635, 1, 10.467, -4.635, 10.717, 4.457, 10.967, 4.457, 1, 11.217, 4.457, 11.467, -4.248, 11.717, -4.248, 1, 11.811, -4.248, 11.906, -2.991, 12, -1.426]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh217", "Segments": [0, 0.841, 1, 0.106, 1.256, 0.211, 1.651, 0.317, 1.651, 1, 0.5, 1.651, 0.683, -2.019, 0.867, -2.019, 1, 1.117, -2.019, 1.367, 1.23, 1.617, 1.23, 1, 1.883, 1.23, 2.15, -1.534, 2.417, -1.534, 1, 2.656, -1.534, 2.894, 1.471, 3.133, 1.471, 1, 3.389, 1.471, 3.644, -1.431, 3.9, -1.431, 1, 4.15, -1.431, 4.4, 1.484, 4.65, 1.484, 1, 4.9, 1.484, 5.15, -1.463, 5.4, -1.463, 1, 5.65, -1.463, 5.9, 1.46, 6.15, 1.46, 1, 6.4, 1.46, 6.65, -1.466, 6.9, -1.466, 1, 7.15, -1.466, 7.4, 1.472, 7.65, 1.472, 1, 7.9, 1.472, 8.15, -1.459, 8.4, -1.459, 1, 8.65, -1.459, 8.9, 1.462, 9.15, 1.462, 1, 9.4, 1.462, 9.65, -1.472, 9.9, -1.472, 1, 10.15, -1.472, 10.4, 1.46, 10.65, 1.46, 1, 10.9, 1.46, 11.15, -1.46, 11.4, -1.46, 1, 11.6, -1.46, 11.8, 0.396, 12, 1.139]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh217", "Segments": [0, 0.108, 1, 0.006, 0.119, 0.011, 0.126, 0.017, 0.126, 1, 0.078, 0.126, 0.139, -0.827, 0.2, -0.827, 1, 0.322, -0.827, 0.444, 4.665, 0.567, 4.665, 1, 0.75, 4.665, 0.933, -4.308, 1.117, -4.308, 1, 1.4, -4.308, 1.683, 3.054, 1.967, 3.054, 1, 2.206, 3.054, 2.444, -3.714, 2.683, -3.714, 1, 2.928, -3.714, 3.172, 3.479, 3.417, 3.479, 1, 3.672, 3.479, 3.928, -3.452, 4.183, -3.452, 1, 4.433, -3.452, 4.683, 3.535, 4.933, 3.535, 1, 5.183, 3.535, 5.433, -3.482, 5.683, -3.482, 1, 5.933, -3.482, 6.183, 3.469, 6.433, 3.469, 1, 6.683, 3.469, 6.933, -3.504, 7.183, -3.504, 1, 7.433, -3.504, 7.683, 3.51, 7.933, 3.51, 1, 8.183, 3.51, 8.433, -3.462, 8.683, -3.462, 1, 8.933, -3.462, 9.183, 3.496, 9.433, 3.496, 1, 9.683, 3.496, 9.933, -3.514, 10.183, -3.514, 1, 10.433, -3.514, 10.683, 3.465, 10.933, 3.465, 1, 11.183, 3.465, 11.433, -3.485, 11.683, -3.485, 1, 11.789, -3.485, 11.894, -2.249, 12, -0.821]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh217", "Segments": [0, -0.433, 1, 0.056, -0.331, 0.111, 0.229, 0.167, 0.229, 1, 0.244, 0.229, 0.322, -1.832, 0.4, -1.832, 1, 0.511, -1.832, 0.622, 3.053, 0.733, 3.053, 1, 0.894, 3.053, 1.056, -1.838, 1.217, -1.838, 1, 1.356, -1.838, 1.494, 0.046, 1.633, 0.046, 1, 1.689, 0.046, 1.744, -0.152, 1.8, -0.152, 1, 1.933, -0.152, 2.067, 1.15, 2.2, 1.15, 1, 2.422, 1.15, 2.644, -1.444, 2.867, -1.444, 1, 3.111, -1.444, 3.356, 1.233, 3.6, 1.233, 1, 3.861, 1.233, 4.122, -1.306, 4.383, -1.306, 1, 4.628, -1.306, 4.872, 1.281, 5.117, 1.281, 1, 5.367, 1.281, 5.617, -1.264, 5.867, -1.264, 1, 6.117, -1.264, 6.367, 1.281, 6.617, 1.281, 1, 6.867, 1.281, 7.117, -1.275, 7.367, -1.275, 1, 7.617, -1.275, 7.867, 1.276, 8.117, 1.276, 1, 8.367, 1.276, 8.617, -1.273, 8.867, -1.273, 1, 9.122, -1.273, 9.378, 1.268, 9.633, 1.268, 1, 9.878, 1.268, 10.122, -1.272, 10.367, -1.272, 1, 10.617, -1.272, 10.867, 1.273, 11.117, 1.273, 1, 11.367, 1.273, 11.617, -1.285, 11.867, -1.285, 1, 11.911, -1.285, 11.956, -1.209, 12, -1.082]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh217", "Segments": [0, -0.493, 1, 0.028, -0.563, 0.056, -0.606, 0.083, -0.606, 1, 0.144, -0.606, 0.206, 0, 0.267, 0, 1, 0.35, 0, 0.433, -1.797, 0.517, -1.797, 1, 0.628, -1.797, 0.739, 3.9, 0.85, 3.9, 1, 0.989, 3.9, 1.128, -2.674, 1.267, -2.674, 1, 1.411, -2.674, 1.556, 0.424, 1.7, 0.424, 1, 1.778, 0.424, 1.856, -0.172, 1.933, -0.172, 1, 2.056, -0.172, 2.178, 1.401, 2.3, 1.401, 1, 2.517, 1.401, 2.733, -1.682, 2.95, -1.682, 1, 3.206, -1.682, 3.461, 1.378, 3.717, 1.378, 1, 3.972, 1.378, 4.228, -1.505, 4.483, -1.505, 1, 4.728, -1.505, 4.972, 1.444, 5.217, 1.444, 1, 5.467, 1.444, 5.717, -1.435, 5.967, -1.435, 1, 6.217, -1.435, 6.467, 1.457, 6.717, 1.457, 1, 6.967, 1.457, 7.217, -1.446, 7.467, -1.446, 1, 7.717, -1.446, 7.967, 1.451, 8.217, 1.451, 1, 8.467, 1.451, 8.717, -1.446, 8.967, -1.446, 1, 9.222, -1.446, 9.478, 1.403, 9.733, 1.403, 1, 9.978, 1.403, 10.222, -1.444, 10.467, -1.444, 1, 10.717, -1.444, 10.967, 1.447, 11.217, 1.447, 1, 11.467, 1.447, 11.717, -1.458, 11.967, -1.458, 1, 11.978, -1.458, 11.989, -1.453, 12, -1.442]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh217", "Segments": [0, -0.596, 1, 0.011, -0.629, 0.022, -0.647, 0.033, -0.647, 1, 0.072, -0.647, 0.111, -0.609, 0.15, -0.609, 1, 0.161, -0.609, 0.172, -0.636, 0.183, -0.636, 1, 0.25, -0.636, 0.317, -0.128, 0.383, -0.128, 1, 0.461, -0.128, 0.539, -1.825, 0.617, -1.825, 1, 0.728, -1.825, 0.839, 4.774, 0.95, 4.774, 1, 1.083, 4.774, 1.217, -3.985, 1.35, -3.985, 1, 1.483, -3.985, 1.617, 1.073, 1.75, 1.073, 1, 1.85, 1.073, 1.95, -0.387, 2.05, -0.387, 1, 2.167, -0.387, 2.283, 1.732, 2.4, 1.732, 1, 2.611, 1.732, 2.822, -1.872, 3.033, -1.872, 1, 3.294, -1.872, 3.556, 1.452, 3.817, 1.452, 1, 4.072, 1.452, 4.328, -1.654, 4.583, -1.654, 1, 4.828, -1.654, 5.072, 1.528, 5.317, 1.528, 1, 5.572, 1.528, 5.828, -1.551, 6.083, -1.551, 1, 6.328, -1.551, 6.572, 1.575, 6.817, 1.575, 1, 7.067, 1.575, 7.317, -1.56, 7.567, -1.56, 1, 7.817, -1.56, 8.067, 1.568, 8.317, 1.568, 1, 8.567, 1.568, 8.817, -1.56, 9.067, -1.56, 1, 9.317, -1.56, 9.567, 1.516, 9.817, 1.516, 1, 10.067, 1.516, 10.317, -1.563, 10.567, -1.563, 1, 10.817, -1.563, 11.067, 1.565, 11.317, 1.565, 1, 11.544, 1.565, 11.772, -1.04, 12, -1.503]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh365", "Segments": [0, -0.841, 1, 0.106, -1.256, 0.211, -1.651, 0.317, -1.651, 1, 0.5, -1.651, 0.683, 2.019, 0.867, 2.019, 1, 1.117, 2.019, 1.367, -1.23, 1.617, -1.23, 1, 1.883, -1.23, 2.15, 1.534, 2.417, 1.534, 1, 2.656, 1.534, 2.894, -1.471, 3.133, -1.471, 1, 3.389, -1.471, 3.644, 1.431, 3.9, 1.431, 1, 4.15, 1.431, 4.4, -1.484, 4.65, -1.484, 1, 4.9, -1.484, 5.15, 1.463, 5.4, 1.463, 1, 5.65, 1.463, 5.9, -1.46, 6.15, -1.46, 1, 6.4, -1.46, 6.65, 1.466, 6.9, 1.466, 1, 7.15, 1.466, 7.4, -1.472, 7.65, -1.472, 1, 7.9, -1.472, 8.15, 1.459, 8.4, 1.459, 1, 8.65, 1.459, 8.9, -1.462, 9.15, -1.462, 1, 9.4, -1.462, 9.65, 1.472, 9.9, 1.472, 1, 10.15, 1.472, 10.4, -1.46, 10.65, -1.46, 1, 10.9, -1.46, 11.15, 1.46, 11.4, 1.46, 1, 11.6, 1.46, 11.8, -0.396, 12, -1.139]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh365", "Segments": [0, -0.108, 1, 0.006, -0.119, 0.011, -0.126, 0.017, -0.126, 1, 0.078, -0.126, 0.139, 0.827, 0.2, 0.827, 1, 0.322, 0.827, 0.444, -4.665, 0.567, -4.665, 1, 0.75, -4.665, 0.933, 4.308, 1.117, 4.308, 1, 1.4, 4.308, 1.683, -3.054, 1.967, -3.054, 1, 2.206, -3.054, 2.444, 3.714, 2.683, 3.714, 1, 2.928, 3.714, 3.172, -3.479, 3.417, -3.479, 1, 3.672, -3.479, 3.928, 3.452, 4.183, 3.452, 1, 4.433, 3.452, 4.683, -3.535, 4.933, -3.535, 1, 5.183, -3.535, 5.433, 3.482, 5.683, 3.482, 1, 5.933, 3.482, 6.183, -3.469, 6.433, -3.469, 1, 6.683, -3.469, 6.933, 3.504, 7.183, 3.504, 1, 7.433, 3.504, 7.683, -3.51, 7.933, -3.51, 1, 8.183, -3.51, 8.433, 3.462, 8.683, 3.462, 1, 8.933, 3.462, 9.183, -3.496, 9.433, -3.496, 1, 9.683, -3.496, 9.933, 3.514, 10.183, 3.514, 1, 10.433, 3.514, 10.683, -3.465, 10.933, -3.465, 1, 11.183, -3.465, 11.433, 3.485, 11.683, 3.485, 1, 11.789, 3.485, 11.894, 2.249, 12, 0.821]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh365", "Segments": [0, 0.433, 1, 0.056, 0.331, 0.111, -0.229, 0.167, -0.229, 1, 0.244, -0.229, 0.322, 1.832, 0.4, 1.832, 1, 0.511, 1.832, 0.622, -3.053, 0.733, -3.053, 1, 0.894, -3.053, 1.056, 1.838, 1.217, 1.838, 1, 1.356, 1.838, 1.494, -0.046, 1.633, -0.046, 1, 1.689, -0.046, 1.744, 0.152, 1.8, 0.152, 1, 1.933, 0.152, 2.067, -1.15, 2.2, -1.15, 1, 2.422, -1.15, 2.644, 1.444, 2.867, 1.444, 1, 3.111, 1.444, 3.356, -1.233, 3.6, -1.233, 1, 3.861, -1.233, 4.122, 1.306, 4.383, 1.306, 1, 4.628, 1.306, 4.872, -1.281, 5.117, -1.281, 1, 5.367, -1.281, 5.617, 1.264, 5.867, 1.264, 1, 6.117, 1.264, 6.367, -1.281, 6.617, -1.281, 1, 6.867, -1.281, 7.117, 1.275, 7.367, 1.275, 1, 7.617, 1.275, 7.867, -1.276, 8.117, -1.276, 1, 8.367, -1.276, 8.617, 1.273, 8.867, 1.273, 1, 9.122, 1.273, 9.378, -1.268, 9.633, -1.268, 1, 9.878, -1.268, 10.122, 1.272, 10.367, 1.272, 1, 10.617, 1.272, 10.867, -1.273, 11.117, -1.273, 1, 11.367, -1.273, 11.617, 1.285, 11.867, 1.285, 1, 11.911, 1.285, 11.956, 1.209, 12, 1.082]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh365", "Segments": [0, 0.493, 1, 0.028, 0.563, 0.056, 0.606, 0.083, 0.606, 1, 0.144, 0.606, 0.206, 0, 0.267, 0, 1, 0.35, 0, 0.433, 1.797, 0.517, 1.797, 1, 0.628, 1.797, 0.739, -3.9, 0.85, -3.9, 1, 0.989, -3.9, 1.128, 2.674, 1.267, 2.674, 1, 1.411, 2.674, 1.556, -0.424, 1.7, -0.424, 1, 1.778, -0.424, 1.856, 0.172, 1.933, 0.172, 1, 2.056, 0.172, 2.178, -1.401, 2.3, -1.401, 1, 2.517, -1.401, 2.733, 1.682, 2.95, 1.682, 1, 3.206, 1.682, 3.461, -1.378, 3.717, -1.378, 1, 3.972, -1.378, 4.228, 1.505, 4.483, 1.505, 1, 4.728, 1.505, 4.972, -1.444, 5.217, -1.444, 1, 5.467, -1.444, 5.717, 1.435, 5.967, 1.435, 1, 6.217, 1.435, 6.467, -1.457, 6.717, -1.457, 1, 6.967, -1.457, 7.217, 1.446, 7.467, 1.446, 1, 7.717, 1.446, 7.967, -1.451, 8.217, -1.451, 1, 8.467, -1.451, 8.717, 1.446, 8.967, 1.446, 1, 9.222, 1.446, 9.478, -1.403, 9.733, -1.403, 1, 9.978, -1.403, 10.222, 1.444, 10.467, 1.444, 1, 10.717, 1.444, 10.967, -1.447, 11.217, -1.447, 1, 11.467, -1.447, 11.717, 1.458, 11.967, 1.458, 1, 11.978, 1.458, 11.989, 1.453, 12, 1.442]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh365", "Segments": [0, 0.596, 1, 0.011, 0.629, 0.022, 0.647, 0.033, 0.647, 1, 0.072, 0.647, 0.111, 0.609, 0.15, 0.609, 1, 0.161, 0.609, 0.172, 0.636, 0.183, 0.636, 1, 0.25, 0.636, 0.317, 0.128, 0.383, 0.128, 1, 0.461, 0.128, 0.539, 1.825, 0.617, 1.825, 1, 0.728, 1.825, 0.839, -4.774, 0.95, -4.774, 1, 1.083, -4.774, 1.217, 3.985, 1.35, 3.985, 1, 1.483, 3.985, 1.617, -1.073, 1.75, -1.073, 1, 1.85, -1.073, 1.95, 0.387, 2.05, 0.387, 1, 2.167, 0.387, 2.283, -1.732, 2.4, -1.732, 1, 2.611, -1.732, 2.822, 1.872, 3.033, 1.872, 1, 3.294, 1.872, 3.556, -1.452, 3.817, -1.452, 1, 4.072, -1.452, 4.328, 1.654, 4.583, 1.654, 1, 4.828, 1.654, 5.072, -1.528, 5.317, -1.528, 1, 5.572, -1.528, 5.828, 1.551, 6.083, 1.551, 1, 6.328, 1.551, 6.572, -1.575, 6.817, -1.575, 1, 7.067, -1.575, 7.317, 1.56, 7.567, 1.56, 1, 7.817, 1.56, 8.067, -1.568, 8.317, -1.568, 1, 8.567, -1.568, 8.817, 1.56, 9.067, 1.56, 1, 9.317, 1.56, 9.567, -1.516, 9.817, -1.516, 1, 10.067, -1.516, 10.317, 1.563, 10.567, 1.563, 1, 10.817, 1.563, 11.067, -1.565, 11.317, -1.565, 1, 11.544, -1.565, 11.772, 1.04, 12, 1.503]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh211", "Segments": [0, 0.088, 1, 0.128, 0.316, 0.256, 0.869, 0.383, 0.869, 1, 0.578, 0.869, 0.772, -1.132, 0.967, -1.132, 1, 1.211, -1.132, 1.456, 0.873, 1.7, 0.873, 1, 1.956, 0.873, 2.211, -0.917, 2.467, -0.917, 1, 2.717, -0.917, 2.967, 0.963, 3.217, 0.963, 1, 3.467, 0.963, 3.717, -0.892, 3.967, -0.892, 1, 4.217, -0.892, 4.467, 0.91, 4.717, 0.91, 1, 4.967, 0.91, 5.217, -0.955, 5.467, -0.955, 1, 5.717, -0.955, 5.967, 0.901, 6.217, 0.901, 1, 6.467, 0.901, 6.717, -0.892, 6.967, -0.892, 1, 7.217, -0.892, 7.467, 0.953, 7.717, 0.953, 1, 7.967, 0.953, 8.217, -0.915, 8.467, -0.915, 1, 8.717, -0.915, 8.967, 0.884, 9.217, 0.884, 1, 9.467, 0.884, 9.717, -0.95, 9.967, -0.95, 1, 10.217, -0.95, 10.467, 0.931, 10.717, 0.931, 1, 10.967, 0.931, 11.217, -0.888, 11.467, -0.888, 1, 11.644, -0.888, 11.822, 0.036, 12, 0.57]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh211", "Segments": [0, -0.238, 1, 0.061, -0.291, 0.122, -0.33, 0.183, -0.331, 1, 0.322, -0.331, 0.461, 2.507, 0.6, 2.507, 1, 0.8, 2.507, 1, -2.662, 1.2, -2.662, 1, 1.456, -2.662, 1.711, 2.033, 1.967, 2.033, 1, 2.217, 2.033, 2.467, -2.254, 2.717, -2.254, 1, 2.967, -2.254, 3.217, 2.386, 3.467, 2.386, 1, 3.717, 2.386, 3.967, -2.118, 4.217, -2.118, 1, 4.467, -2.118, 4.717, 2.214, 4.967, 2.214, 1, 5.217, 2.214, 5.467, -2.318, 5.717, -2.318, 1, 5.967, -2.318, 6.217, 2.153, 6.467, 2.153, 1, 6.717, 2.153, 6.967, -2.153, 7.217, -2.153, 1, 7.467, -2.153, 7.717, 2.322, 7.967, 2.322, 1, 8.217, 2.322, 8.467, -2.179, 8.717, -2.179, 1, 8.967, -2.179, 9.217, 2.104, 9.467, 2.104, 1, 9.717, 2.104, 9.967, -2.318, 10.217, -2.318, 1, 10.467, -2.318, 10.717, 2.228, 10.967, 2.228, 1, 11.217, 2.228, 11.467, -2.124, 11.717, -2.124, 1, 11.811, -2.124, 11.906, -1.495, 12, -0.713]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh211", "Segments": [0, -0.493, 1, 0.122, -0.846, 0.244, -1.239, 0.367, -1.239, 1, 0.506, -1.239, 0.644, 2.068, 0.783, 2.068, 1, 0.961, 2.068, 1.139, -1.556, 1.317, -1.556, 1, 1.594, -1.556, 1.872, 1.087, 2.15, 1.087, 1, 2.4, 1.087, 2.65, -1.252, 2.9, -1.252, 1, 3.15, -1.252, 3.4, 1.292, 3.65, 1.292, 1, 3.9, 1.292, 4.15, -1.194, 4.4, -1.194, 1, 4.489, -1.194, 4.578, -0.623, 4.667, -0.623, 1, 4.678, -0.623, 4.689, -0.637, 4.7, -0.637, 1, 4.839, -0.637, 4.978, 1.254, 5.117, 1.254, 1, 5.372, 1.254, 5.628, -1.21, 5.883, -1.21, 1, 6.133, -1.21, 6.383, 1.219, 6.633, 1.219, 1, 6.883, 1.219, 7.133, -1.208, 7.383, -1.208, 1, 7.633, -1.208, 7.883, 1.214, 8.133, 1.214, 1, 8.383, 1.214, 8.633, -1.188, 8.883, -1.188, 1, 8.95, -1.188, 9.017, -0.805, 9.083, -0.805, 1, 9.1, -0.805, 9.117, -0.854, 9.133, -0.854, 1, 9.3, -0.854, 9.467, 1.174, 9.633, 1.174, 1, 9.883, 1.174, 10.133, -1.226, 10.383, -1.226, 1, 10.639, -1.226, 10.894, 1.202, 11.15, 1.202, 1, 11.394, 1.202, 11.639, -1.223, 11.883, -1.223, 1, 11.922, -1.223, 11.961, -1.162, 12, -1.059]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh211", "Segments": [0, -0.309, 1, 0.156, -0.734, 0.311, -1.342, 0.467, -1.342, 1, 0.6, -1.342, 0.733, 2.645, 0.867, 2.645, 1, 1.039, 2.645, 1.211, -1.936, 1.383, -1.936, 1, 1.672, -1.936, 1.961, 1.25, 2.25, 1.25, 1, 2.494, 1.25, 2.739, -1.469, 2.983, -1.469, 1, 3.239, -1.469, 3.494, 1.466, 3.75, 1.466, 1, 4, 1.466, 4.25, -1.38, 4.5, -1.38, 1, 4.739, -1.38, 4.978, 1.497, 5.217, 1.497, 1, 5.478, 1.497, 5.739, -1.441, 6, -1.441, 1, 6.244, -1.441, 6.489, 1.439, 6.733, 1.439, 1, 6.983, 1.439, 7.233, -1.404, 7.483, -1.404, 1, 7.733, -1.404, 7.983, 1.418, 8.233, 1.418, 1, 8.483, 1.418, 8.733, -1.391, 8.983, -1.391, 1, 9.228, -1.391, 9.472, 1.419, 9.717, 1.419, 1, 9.972, 1.419, 10.228, -1.441, 10.483, -1.441, 1, 10.733, -1.441, 10.983, 1.42, 11.233, 1.42, 1, 11.483, 1.42, 11.733, -1.426, 11.983, -1.426, 1, 11.989, -1.426, 11.994, -1.425, 12, -1.422]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh211", "Segments": [0, -0.118, 1, 0.189, -0.672, 0.378, -1.807, 0.567, -1.807, 1, 0.7, -1.807, 0.833, 4.118, 0.967, 4.118, 1, 1.122, 4.118, 1.278, -3.134, 1.433, -3.134, 1, 1.594, -3.134, 1.756, 0.431, 1.917, 0.431, 1, 1.944, 0.431, 1.972, 0.403, 2, 0.403, 1, 2.122, 0.403, 2.244, 1.742, 2.367, 1.742, 1, 2.606, 1.742, 2.844, -2.064, 3.083, -2.064, 1, 3.339, -2.064, 3.594, 1.993, 3.85, 1.993, 1, 4.1, 1.993, 4.35, -1.891, 4.6, -1.891, 1, 4.844, -1.891, 5.089, 2.098, 5.333, 2.098, 1, 5.589, 2.098, 5.844, -1.992, 6.1, -1.992, 1, 6.344, -1.992, 6.589, 2.006, 6.833, 2.006, 1, 7.083, 2.006, 7.333, -1.936, 7.583, -1.936, 1, 7.833, -1.936, 8.083, 1.972, 8.333, 1.972, 1, 8.583, 1.972, 8.833, -1.94, 9.083, -1.94, 1, 9.328, -1.94, 9.572, 2.027, 9.817, 2.027, 1, 10.072, 2.027, 10.328, -2.025, 10.583, -2.025, 1, 10.833, -2.025, 11.083, 2.003, 11.333, 2.003, 1, 11.556, 2.003, 11.778, -1.207, 12, -1.921]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh211", "Segments": [0, 0.165, 1, 0.222, -0.269, 0.444, -1.935, 0.667, -1.935, 1, 0.8, -1.935, 0.933, 5.015, 1.067, 5.015, 1, 1.211, 5.015, 1.356, -4.258, 1.5, -4.258, 1, 1.656, -4.258, 1.811, 0.993, 1.967, 0.993, 1, 2.033, 0.993, 2.1, 0.485, 2.167, 0.485, 1, 2.272, 0.485, 2.378, 1.927, 2.483, 1.927, 1, 2.717, 1.927, 2.95, -2.216, 3.183, -2.216, 1, 3.439, -2.216, 3.694, 2.075, 3.95, 2.075, 1, 4.194, 2.075, 4.439, -1.969, 4.683, -1.969, 1, 4.933, -1.969, 5.183, 2.241, 5.433, 2.241, 1, 5.689, 2.241, 5.944, -2.099, 6.2, -2.099, 1, 6.45, -2.099, 6.7, 2.123, 6.95, 2.123, 1, 7.194, 2.123, 7.439, -2.027, 7.683, -2.027, 1, 7.933, -2.027, 8.183, 2.095, 8.433, 2.095, 1, 8.683, 2.095, 8.933, -2.058, 9.183, -2.058, 1, 9.428, -2.058, 9.672, 2.203, 9.917, 2.203, 1, 10.167, 2.203, 10.417, -2.043, 10.667, -2.043, 1, 10.922, -2.043, 11.178, 2.123, 11.433, 2.123, 1, 11.622, 2.123, 11.811, -0.42, 12, -1.575]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh211", "Segments": [0, 0.42, 1, 0.256, 0.42, 0.511, -2.46, 0.767, -2.46, 1, 0.894, -2.46, 1.022, 7.195, 1.15, 7.195, 1, 1.294, 7.195, 1.439, -6.957, 1.583, -6.957, 1, 1.728, -6.957, 1.872, 2.271, 2.017, 2.271, 1, 2.111, 2.271, 2.206, 0.315, 2.3, 0.315, 1, 2.4, 0.315, 2.5, 2.616, 2.6, 2.616, 1, 2.822, 2.616, 3.044, -2.715, 3.267, -2.715, 1, 3.522, -2.715, 3.778, 2.474, 4.033, 2.474, 1, 4.278, 2.474, 4.522, -2.336, 4.767, -2.336, 1, 5.017, -2.336, 5.267, 2.756, 5.517, 2.756, 1, 5.778, 2.756, 6.039, -2.509, 6.3, -2.509, 1, 6.544, -2.509, 6.789, 2.555, 7.033, 2.555, 1, 7.278, 2.555, 7.522, -2.408, 7.767, -2.408, 1, 8.017, -2.408, 8.267, 2.556, 8.517, 2.556, 1, 8.767, 2.556, 9.017, -2.508, 9.267, -2.508, 1, 9.511, -2.508, 9.756, 2.748, 10, 2.748, 1, 10.25, 2.748, 10.5, -2.825, 10.75, -2.825, 1, 11.011, -2.825, 11.272, 2.573, 11.533, 2.573, 1, 11.689, 2.573, 11.844, 0.493, 12, -1.021]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh212", "Segments": [0, 0.223, 1, 0.044, 0.321, 0.089, 0.426, 0.133, 0.426, 1, 0.372, 0.426, 0.611, -2.607, 0.85, -2.607, 1, 0.983, -2.607, 1.117, 8.355, 1.25, 8.355, 1, 1.389, 8.355, 1.528, -8.971, 1.667, -8.971, 1, 1.806, -8.971, 1.944, 3.748, 2.083, 3.748, 1, 2.189, 3.748, 2.294, -0.443, 2.4, -0.443, 1, 2.5, -0.443, 2.6, 3.111, 2.7, 3.111, 1, 2.861, 3.111, 3.022, -2.725, 3.183, -2.725, 1, 3.422, -2.725, 3.661, 2.458, 3.9, 2.458, 1, 4.15, 2.458, 4.4, -2.477, 4.65, -2.477, 1, 4.967, -2.477, 5.283, 2.694, 5.6, 2.694, 1, 5.767, 2.694, 5.933, -2.252, 6.1, -2.252, 1, 6.144, -2.252, 6.189, -2.072, 6.233, -2.072, 1, 6.289, -2.072, 6.344, -2.319, 6.4, -2.319, 1, 6.567, -2.319, 6.733, 2.322, 6.9, 2.322, 1, 6.928, 2.322, 6.956, 2.288, 6.983, 2.288, 1, 7.033, 2.288, 7.083, 2.389, 7.133, 2.389, 1, 7.3, 2.389, 7.467, -2.485, 7.633, -2.485, 1, 7.944, -2.485, 8.256, 2.483, 8.567, 2.483, 1, 8.822, 2.483, 9.078, -2.409, 9.333, -2.409, 1, 9.583, -2.409, 9.833, 2.703, 10.083, 2.703, 1, 10.256, 2.703, 10.428, -2.08, 10.6, -2.08, 1, 10.622, -2.08, 10.644, -1.996, 10.667, -1.996, 1, 10.739, -1.996, 10.811, -2.685, 10.883, -2.685, 1, 11.05, -2.685, 11.217, 2.21, 11.383, 2.21, 1, 11.406, 2.21, 11.428, 2.202, 11.45, 2.202, 1, 11.511, 2.202, 11.572, 2.424, 11.633, 2.424, 1, 11.756, 2.424, 11.878, 0.105, 12, -1.345]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh371", "Segments": [0, -0.088, 1, 0.128, -0.316, 0.256, -0.869, 0.383, -0.869, 1, 0.578, -0.869, 0.772, 1.132, 0.967, 1.132, 1, 1.211, 1.132, 1.456, -0.873, 1.7, -0.873, 1, 1.956, -0.873, 2.211, 0.917, 2.467, 0.917, 1, 2.717, 0.917, 2.967, -0.963, 3.217, -0.963, 1, 3.467, -0.963, 3.717, 0.892, 3.967, 0.892, 1, 4.217, 0.892, 4.467, -0.91, 4.717, -0.91, 1, 4.967, -0.91, 5.217, 0.955, 5.467, 0.955, 1, 5.717, 0.955, 5.967, -0.901, 6.217, -0.901, 1, 6.467, -0.901, 6.717, 0.892, 6.967, 0.892, 1, 7.217, 0.892, 7.467, -0.953, 7.717, -0.953, 1, 7.967, -0.953, 8.217, 0.915, 8.467, 0.915, 1, 8.717, 0.915, 8.967, -0.884, 9.217, -0.884, 1, 9.467, -0.884, 9.717, 0.95, 9.967, 0.95, 1, 10.217, 0.95, 10.467, -0.931, 10.717, -0.931, 1, 10.967, -0.931, 11.217, 0.888, 11.467, 0.888, 1, 11.644, 0.888, 11.822, -0.036, 12, -0.57]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh371", "Segments": [0, 0.238, 1, 0.061, 0.291, 0.122, 0.33, 0.183, 0.331, 1, 0.322, 0.331, 0.461, -2.507, 0.6, -2.507, 1, 0.8, -2.507, 1, 2.662, 1.2, 2.662, 1, 1.456, 2.662, 1.711, -2.033, 1.967, -2.033, 1, 2.217, -2.033, 2.467, 2.254, 2.717, 2.254, 1, 2.967, 2.254, 3.217, -2.386, 3.467, -2.386, 1, 3.717, -2.386, 3.967, 2.118, 4.217, 2.118, 1, 4.467, 2.118, 4.717, -2.214, 4.967, -2.214, 1, 5.217, -2.214, 5.467, 2.318, 5.717, 2.318, 1, 5.967, 2.318, 6.217, -2.153, 6.467, -2.153, 1, 6.717, -2.153, 6.967, 2.153, 7.217, 2.153, 1, 7.467, 2.153, 7.717, -2.322, 7.967, -2.322, 1, 8.217, -2.322, 8.467, 2.179, 8.717, 2.179, 1, 8.967, 2.179, 9.217, -2.104, 9.467, -2.104, 1, 9.717, -2.104, 9.967, 2.318, 10.217, 2.318, 1, 10.467, 2.318, 10.717, -2.228, 10.967, -2.228, 1, 11.217, -2.228, 11.467, 2.124, 11.717, 2.124, 1, 11.811, 2.124, 11.906, 1.495, 12, 0.713]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh371", "Segments": [0, 0.493, 1, 0.122, 0.846, 0.244, 1.239, 0.367, 1.239, 1, 0.506, 1.239, 0.644, -2.068, 0.783, -2.068, 1, 0.961, -2.068, 1.139, 1.556, 1.317, 1.556, 1, 1.594, 1.556, 1.872, -1.087, 2.15, -1.087, 1, 2.4, -1.087, 2.65, 1.252, 2.9, 1.252, 1, 3.15, 1.252, 3.4, -1.292, 3.65, -1.292, 1, 3.9, -1.292, 4.15, 1.194, 4.4, 1.194, 1, 4.489, 1.194, 4.578, 0.623, 4.667, 0.623, 1, 4.678, 0.623, 4.689, 0.637, 4.7, 0.637, 1, 4.839, 0.637, 4.978, -1.254, 5.117, -1.254, 1, 5.372, -1.254, 5.628, 1.21, 5.883, 1.21, 1, 6.133, 1.21, 6.383, -1.219, 6.633, -1.219, 1, 6.883, -1.219, 7.133, 1.208, 7.383, 1.208, 1, 7.633, 1.208, 7.883, -1.214, 8.133, -1.214, 1, 8.383, -1.214, 8.633, 1.188, 8.883, 1.188, 1, 8.95, 1.188, 9.017, 0.805, 9.083, 0.805, 1, 9.1, 0.805, 9.117, 0.854, 9.133, 0.854, 1, 9.3, 0.854, 9.467, -1.174, 9.633, -1.174, 1, 9.883, -1.174, 10.133, 1.226, 10.383, 1.226, 1, 10.639, 1.226, 10.894, -1.202, 11.15, -1.202, 1, 11.394, -1.202, 11.639, 1.223, 11.883, 1.223, 1, 11.922, 1.223, 11.961, 1.162, 12, 1.059]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh371", "Segments": [0, 0.309, 1, 0.156, 0.734, 0.311, 1.342, 0.467, 1.342, 1, 0.6, 1.342, 0.733, -2.645, 0.867, -2.645, 1, 1.039, -2.645, 1.211, 1.936, 1.383, 1.936, 1, 1.672, 1.936, 1.961, -1.25, 2.25, -1.25, 1, 2.494, -1.25, 2.739, 1.469, 2.983, 1.469, 1, 3.239, 1.469, 3.494, -1.466, 3.75, -1.466, 1, 4, -1.466, 4.25, 1.38, 4.5, 1.38, 1, 4.739, 1.38, 4.978, -1.497, 5.217, -1.497, 1, 5.478, -1.497, 5.739, 1.441, 6, 1.441, 1, 6.244, 1.441, 6.489, -1.439, 6.733, -1.439, 1, 6.983, -1.439, 7.233, 1.404, 7.483, 1.404, 1, 7.733, 1.404, 7.983, -1.418, 8.233, -1.418, 1, 8.483, -1.418, 8.733, 1.391, 8.983, 1.391, 1, 9.228, 1.391, 9.472, -1.419, 9.717, -1.419, 1, 9.972, -1.419, 10.228, 1.441, 10.483, 1.441, 1, 10.733, 1.441, 10.983, -1.42, 11.233, -1.42, 1, 11.483, -1.42, 11.733, 1.426, 11.983, 1.426, 1, 11.989, 1.426, 11.994, 1.425, 12, 1.422]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh371", "Segments": [0, 0.118, 1, 0.189, 0.672, 0.378, 1.807, 0.567, 1.807, 1, 0.7, 1.807, 0.833, -4.118, 0.967, -4.118, 1, 1.122, -4.118, 1.278, 3.134, 1.433, 3.134, 1, 1.594, 3.134, 1.756, -0.431, 1.917, -0.431, 1, 1.944, -0.431, 1.972, -0.403, 2, -0.403, 1, 2.122, -0.403, 2.244, -1.742, 2.367, -1.742, 1, 2.606, -1.742, 2.844, 2.064, 3.083, 2.064, 1, 3.339, 2.064, 3.594, -1.993, 3.85, -1.993, 1, 4.1, -1.993, 4.35, 1.891, 4.6, 1.891, 1, 4.844, 1.891, 5.089, -2.098, 5.333, -2.098, 1, 5.589, -2.098, 5.844, 1.992, 6.1, 1.992, 1, 6.344, 1.992, 6.589, -2.006, 6.833, -2.006, 1, 7.083, -2.006, 7.333, 1.936, 7.583, 1.936, 1, 7.833, 1.936, 8.083, -1.972, 8.333, -1.972, 1, 8.583, -1.972, 8.833, 1.94, 9.083, 1.94, 1, 9.328, 1.94, 9.572, -2.027, 9.817, -2.027, 1, 10.072, -2.027, 10.328, 2.025, 10.583, 2.025, 1, 10.833, 2.025, 11.083, -2.003, 11.333, -2.003, 1, 11.556, -2.003, 11.778, 1.207, 12, 1.921]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh371", "Segments": [0, -0.165, 1, 0.222, 0.269, 0.444, 1.935, 0.667, 1.935, 1, 0.8, 1.935, 0.933, -5.015, 1.067, -5.015, 1, 1.211, -5.015, 1.356, 4.258, 1.5, 4.258, 1, 1.656, 4.258, 1.811, -0.993, 1.967, -0.993, 1, 2.033, -0.993, 2.1, -0.485, 2.167, -0.485, 1, 2.272, -0.485, 2.378, -1.927, 2.483, -1.927, 1, 2.717, -1.927, 2.95, 2.216, 3.183, 2.216, 1, 3.439, 2.216, 3.694, -2.075, 3.95, -2.075, 1, 4.194, -2.075, 4.439, 1.969, 4.683, 1.969, 1, 4.933, 1.969, 5.183, -2.241, 5.433, -2.241, 1, 5.689, -2.241, 5.944, 2.099, 6.2, 2.099, 1, 6.45, 2.099, 6.7, -2.123, 6.95, -2.123, 1, 7.194, -2.123, 7.439, 2.027, 7.683, 2.027, 1, 7.933, 2.027, 8.183, -2.095, 8.433, -2.095, 1, 8.683, -2.095, 8.933, 2.058, 9.183, 2.058, 1, 9.428, 2.058, 9.672, -2.203, 9.917, -2.203, 1, 10.167, -2.203, 10.417, 2.043, 10.667, 2.043, 1, 10.922, 2.043, 11.178, -2.123, 11.433, -2.123, 1, 11.622, -2.123, 11.811, 0.42, 12, 1.575]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh371", "Segments": [0, -0.42, 1, 0.256, -0.42, 0.511, 2.46, 0.767, 2.46, 1, 0.894, 2.46, 1.022, -7.195, 1.15, -7.195, 1, 1.294, -7.195, 1.439, 6.957, 1.583, 6.957, 1, 1.728, 6.957, 1.872, -2.271, 2.017, -2.271, 1, 2.111, -2.271, 2.206, -0.315, 2.3, -0.315, 1, 2.4, -0.315, 2.5, -2.616, 2.6, -2.616, 1, 2.822, -2.616, 3.044, 2.715, 3.267, 2.715, 1, 3.522, 2.715, 3.778, -2.474, 4.033, -2.474, 1, 4.278, -2.474, 4.522, 2.336, 4.767, 2.336, 1, 5.017, 2.336, 5.267, -2.756, 5.517, -2.756, 1, 5.778, -2.756, 6.039, 2.509, 6.3, 2.509, 1, 6.544, 2.509, 6.789, -2.555, 7.033, -2.555, 1, 7.278, -2.555, 7.522, 2.408, 7.767, 2.408, 1, 8.017, 2.408, 8.267, -2.556, 8.517, -2.556, 1, 8.767, -2.556, 9.017, 2.508, 9.267, 2.508, 1, 9.511, 2.508, 9.756, -2.748, 10, -2.748, 1, 10.25, -2.748, 10.5, 2.825, 10.75, 2.825, 1, 11.011, 2.825, 11.272, -2.573, 11.533, -2.573, 1, 11.689, -2.573, 11.844, -0.493, 12, 1.021]}, {"Target": "Parameter", "Id": "ParamOPRX1", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamOPRY1", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamOPRX2", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamOPRY2", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamTuiRou1", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamTuiRou2", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamTuiRou3", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param117", "Segments": [0, 0.162, 1, 0.006, 0.179, 0.011, 0.189, 0.017, 0.189, 1, 0.078, 0.189, 0.139, -1.241, 0.2, -1.241, 1, 0.322, -1.241, 0.444, 6.997, 0.567, 6.997, 1, 0.75, 6.997, 0.933, -6.462, 1.117, -6.462, 1, 1.4, -6.462, 1.683, 4.582, 1.967, 4.582, 1, 2.206, 4.582, 2.444, -5.57, 2.683, -5.57, 1, 2.928, -5.57, 3.172, 5.219, 3.417, 5.219, 1, 3.672, 5.219, 3.928, -5.178, 4.183, -5.178, 1, 4.433, -5.178, 4.683, 5.302, 4.933, 5.302, 1, 5.183, 5.302, 5.433, -5.223, 5.683, -5.223, 1, 5.933, -5.223, 6.183, 5.203, 6.433, 5.203, 1, 6.683, 5.203, 6.933, -5.256, 7.183, -5.256, 1, 7.433, -5.256, 7.683, 5.264, 7.933, 5.264, 1, 8.183, 5.264, 8.433, -5.192, 8.683, -5.192, 1, 8.933, -5.192, 9.183, 5.244, 9.433, 5.244, 1, 9.683, 5.244, 9.933, -5.27, 10.183, -5.27, 1, 10.433, -5.27, 10.683, 5.197, 10.933, 5.197, 1, 11.183, 5.197, 11.433, -5.228, 11.683, -5.228, 1, 11.789, -5.228, 11.894, -3.374, 12, -1.232]}, {"Target": "Parameter", "Id": "Param118", "Segments": [0, -0.649, 1, 0.056, -0.496, 0.111, 0.343, 0.167, 0.343, 1, 0.244, 0.343, 0.322, -2.747, 0.4, -2.747, 1, 0.511, -2.747, 0.622, 4.579, 0.733, 4.579, 1, 0.894, 4.579, 1.056, -2.756, 1.217, -2.756, 1, 1.356, -2.756, 1.494, 0.069, 1.633, 0.069, 1, 1.689, 0.069, 1.744, -0.228, 1.8, -0.228, 1, 1.933, -0.228, 2.067, 1.725, 2.2, 1.725, 1, 2.422, 1.725, 2.644, -2.166, 2.867, -2.166, 1, 3.111, -2.166, 3.356, 1.849, 3.6, 1.849, 1, 3.861, 1.849, 4.122, -1.959, 4.383, -1.959, 1, 4.628, -1.959, 4.872, 1.922, 5.117, 1.922, 1, 5.367, 1.922, 5.617, -1.895, 5.867, -1.895, 1, 6.117, -1.895, 6.367, 1.921, 6.617, 1.921, 1, 6.867, 1.921, 7.117, -1.912, 7.367, -1.912, 1, 7.617, -1.912, 7.867, 1.914, 8.117, 1.914, 1, 8.367, 1.914, 8.617, -1.909, 8.867, -1.909, 1, 9.122, -1.909, 9.378, 1.902, 9.633, 1.902, 1, 9.878, 1.902, 10.122, -1.908, 10.367, -1.908, 1, 10.617, -1.908, 10.867, 1.909, 11.117, 1.909, 1, 11.367, 1.909, 11.617, -1.928, 11.867, -1.928, 1, 11.911, -1.928, 11.956, -1.813, 12, -1.623]}, {"Target": "Parameter", "Id": "Param119", "Segments": [0, -0.739, 1, 0.028, -0.845, 0.056, -0.909, 0.083, -0.909, 1, 0.144, -0.909, 0.206, 0, 0.267, 0, 1, 0.35, 0, 0.433, -2.695, 0.517, -2.695, 1, 0.628, -2.695, 0.739, 5.85, 0.85, 5.85, 1, 0.989, 5.85, 1.128, -4.01, 1.267, -4.01, 1, 1.411, -4.01, 1.556, 0.637, 1.7, 0.637, 1, 1.778, 0.637, 1.856, -0.258, 1.933, -0.258, 1, 2.056, -0.258, 2.178, 2.102, 2.3, 2.102, 1, 2.517, 2.102, 2.733, -2.524, 2.95, -2.524, 1, 3.206, -2.524, 3.461, 2.067, 3.717, 2.067, 1, 3.972, 2.067, 4.228, -2.258, 4.483, -2.258, 1, 4.728, -2.258, 4.972, 2.167, 5.217, 2.167, 1, 5.467, 2.167, 5.717, -2.153, 5.967, -2.153, 1, 6.217, -2.153, 6.467, 2.185, 6.717, 2.185, 1, 6.967, 2.185, 7.217, -2.17, 7.467, -2.17, 1, 7.717, -2.17, 7.967, 2.177, 8.217, 2.177, 1, 8.467, 2.177, 8.717, -2.169, 8.967, -2.169, 1, 9.222, -2.169, 9.478, 2.104, 9.733, 2.104, 1, 9.978, 2.104, 10.222, -2.166, 10.467, -2.166, 1, 10.717, -2.166, 10.967, 2.171, 11.217, 2.171, 1, 11.467, 2.171, 11.717, -2.187, 11.967, -2.187, 1, 11.978, -2.187, 11.989, -2.179, 12, -2.164]}, {"Target": "Parameter", "Id": "Param129", "Segments": [0, 0, 1, 0.106, 0, 0.211, 2.43, 0.317, 2.43, 1, 0.556, 2.43, 0.794, -2.221, 1.033, -2.221, 1, 1.278, -2.221, 1.522, 1.992, 1.767, 1.992, 1, 1.772, 1.992, 1.778, 1.992, 1.783, 1.992, 1, 2.022, 1.992, 2.261, -2.206, 2.5, -2.206, 1, 2.761, -2.206, 3.022, 2.346, 3.283, 2.346, 1, 3.533, 2.346, 3.783, -2.002, 4.033, -2.002, 1, 4.272, -2.002, 4.511, 2.137, 4.75, 2.137, 1, 5.011, 2.137, 5.272, -2.287, 5.533, -2.287, 1, 5.783, -2.287, 6.033, 2.036, 6.283, 2.036, 1, 6.522, 2.036, 6.761, -2.063, 7, -2.063, 1, 7.261, -2.063, 7.522, 2.292, 7.783, 2.292, 1, 8.033, 2.292, 8.283, -2.086, 8.533, -2.086, 1, 8.778, -2.086, 9.022, 2.022, 9.267, 2.022, 1, 9.517, 2.022, 9.767, -2.279, 10.017, -2.279, 1, 10.272, -2.279, 10.528, 2.154, 10.783, 2.154, 1, 11.022, 2.154, 11.261, -2.001, 11.5, -2.001, 1, 11.506, -2.001, 11.511, -2.001, 11.517, -2.001, 1, 11.678, -2.001, 11.839, -0.162, 12, 1.092]}, {"Target": "Parameter", "Id": "Param130", "Segments": [0, 0, 1, 0.067, 0, 0.133, -1.013, 0.2, -1.013, 1, 0.306, -1.013, 0.411, 0.994, 0.517, 0.994, 1, 0.6, 0.994, 0.683, 0.432, 0.767, 0.432, 1, 0.789, 0.432, 0.811, 0.446, 0.833, 0.446, 1, 0.95, 0.446, 1.067, -0.531, 1.183, -0.531, 1, 1.244, -0.531, 1.306, -0.381, 1.367, -0.381, 1, 1.439, -0.381, 1.511, -0.612, 1.583, -0.612, 1, 1.7, -0.612, 1.817, 0.586, 1.933, 0.586, 1, 2.006, 0.586, 2.078, 0.375, 2.15, 0.375, 1, 2.211, 0.375, 2.272, 0.59, 2.333, 0.59, 1, 2.45, 0.59, 2.567, -0.604, 2.683, -0.604, 1, 2.756, -0.604, 2.828, -0.403, 2.9, -0.403, 1, 2.917, -0.403, 2.933, -0.433, 2.95, -0.433, 1, 2.967, -0.433, 2.983, -0.362, 3, -0.362, 1, 3.039, -0.362, 3.078, -0.466, 3.117, -0.466, 1, 3.122, -0.466, 3.128, -0.455, 3.133, -0.455, 1, 3.139, -0.455, 3.144, -0.476, 3.15, -0.476, 1, 3.256, -0.476, 3.361, 0.648, 3.467, 0.648, 1, 3.539, 0.648, 3.611, 0.397, 3.683, 0.397, 1, 3.733, 0.397, 3.783, 0.562, 3.833, 0.562, 1, 3.95, 0.562, 4.067, -0.569, 4.183, -0.569, 1, 4.256, -0.569, 4.328, -0.379, 4.4, -0.379, 1, 4.461, -0.379, 4.522, -0.585, 4.583, -0.585, 1, 4.7, -0.585, 4.817, 0.604, 4.933, 0.604, 1, 5.006, 0.604, 5.078, 0.388, 5.15, 0.388, 1, 5.211, 0.388, 5.272, 0.603, 5.333, 0.603, 1, 5.45, 0.603, 5.567, -0.588, 5.683, -0.588, 1, 5.756, -0.588, 5.828, -0.387, 5.9, -0.387, 1, 5.961, -0.387, 6.022, -0.607, 6.083, -0.607, 1, 6.2, -0.607, 6.317, 0.576, 6.433, 0.576, 1, 6.506, 0.576, 6.578, 0.369, 6.65, 0.369, 1, 6.711, 0.369, 6.772, 0.582, 6.833, 0.582, 1, 6.95, 0.582, 7.067, -0.59, 7.183, -0.59, 1, 7.256, -0.59, 7.328, -0.389, 7.4, -0.389, 1, 7.461, -0.389, 7.522, -0.602, 7.583, -0.602, 1, 7.7, -0.602, 7.817, 0.592, 7.933, 0.592, 1, 8.006, 0.592, 8.078, 0.39, 8.15, 0.39, 1, 8.211, 0.39, 8.272, 0.611, 8.333, 0.611, 1, 8.45, 0.611, 8.567, -0.572, 8.683, -0.572, 1, 8.756, -0.572, 8.828, -0.366, 8.9, -0.366, 1, 8.961, -0.366, 9.022, -0.593, 9.083, -0.593, 1, 9.2, -0.593, 9.317, 0.592, 9.433, 0.592, 1, 9.506, 0.592, 9.578, 0.385, 9.65, 0.385, 1, 9.711, 0.385, 9.772, 0.596, 9.833, 0.596, 1, 9.95, 0.596, 10.067, -0.595, 10.183, -0.595, 1, 10.256, -0.595, 10.328, -0.395, 10.4, -0.395, 1, 10.461, -0.395, 10.522, -0.615, 10.583, -0.615, 1, 10.7, -0.615, 10.817, 0.523, 10.933, 0.523, 1, 10.939, 0.523, 10.944, 0.522, 10.95, 0.522, 1, 10.956, 0.522, 10.961, 0.525, 10.967, 0.525, 1, 11.028, 0.525, 11.089, 0.401, 11.15, 0.401, 1, 11.211, 0.401, 11.272, 0.592, 11.333, 0.592, 1, 11.45, 0.592, 11.567, -0.592, 11.683, -0.592, 1, 11.756, -0.592, 11.828, -0.39, 11.9, -0.39, 1, 11.933, -0.39, 11.967, -0.448, 12, -0.5]}, {"Target": "Parameter", "Id": "Param131", "Segments": [0, -0.007, 1, 0.1, -0.104, 0.2, -1.021, 0.3, -1.021, 1, 0.411, -1.021, 0.522, 1.396, 0.633, 1.396, 1, 0.856, 1.396, 1.078, -0.631, 1.3, -0.631, 1, 1.378, -0.631, 1.456, -0.394, 1.533, -0.394, 1, 1.583, -0.394, 1.633, -0.525, 1.683, -0.525, 1, 1.806, -0.525, 1.928, 0.799, 2.05, 0.799, 1, 2.133, 0.799, 2.217, 0.342, 2.3, 0.342, 1, 2.35, 0.342, 2.4, 0.47, 2.45, 0.47, 1, 2.567, 0.47, 2.683, -0.812, 2.8, -0.812, 1, 3.061, -0.812, 3.322, 0.816, 3.583, 0.816, 1, 3.672, 0.816, 3.761, 0.375, 3.85, 0.375, 1, 3.878, 0.375, 3.906, 0.412, 3.933, 0.412, 1, 3.939, 0.412, 3.944, 0.412, 3.95, 0.412, 1, 4.067, 0.412, 4.183, -0.753, 4.3, -0.753, 1, 4.383, -0.753, 4.467, -0.361, 4.55, -0.361, 1, 4.6, -0.361, 4.65, -0.468, 4.7, -0.468, 1, 4.817, -0.468, 4.933, 0.809, 5.05, 0.809, 1, 5.133, 0.809, 5.217, 0.357, 5.3, 0.357, 1, 5.35, 0.357, 5.4, 0.482, 5.45, 0.482, 1, 5.567, 0.482, 5.683, -0.795, 5.8, -0.795, 1, 5.883, -0.795, 5.967, -0.364, 6.05, -0.364, 1, 6.1, -0.364, 6.15, -0.488, 6.2, -0.488, 1, 6.317, -0.488, 6.433, 0.781, 6.55, 0.781, 1, 6.633, 0.781, 6.717, 0.341, 6.8, 0.341, 1, 6.85, 0.341, 6.9, 0.462, 6.95, 0.462, 1, 7.067, 0.462, 7.183, -0.793, 7.3, -0.793, 1, 7.383, -0.793, 7.467, -0.363, 7.55, -0.363, 1, 7.6, -0.363, 7.65, -0.483, 7.7, -0.483, 1, 7.811, -0.483, 7.922, 0.824, 8.033, 0.824, 1, 8.122, 0.824, 8.211, 0.35, 8.3, 0.35, 1, 8.35, 0.35, 8.4, 0.498, 8.45, 0.498, 1, 8.567, 0.498, 8.683, -0.78, 8.8, -0.78, 1, 8.883, -0.78, 8.967, -0.341, 9.05, -0.341, 1, 9.1, -0.341, 9.15, -0.475, 9.2, -0.475, 1, 9.317, -0.475, 9.433, 0.798, 9.55, 0.798, 1, 9.633, 0.798, 9.717, 0.357, 9.8, 0.357, 1, 9.85, 0.357, 9.9, 0.474, 9.95, 0.474, 1, 10.067, 0.474, 10.183, -0.801, 10.3, -0.801, 1, 10.383, -0.801, 10.467, -0.371, 10.55, -0.371, 1, 10.6, -0.371, 10.65, -0.496, 10.7, -0.496, 1, 10.817, -0.496, 10.933, 0.731, 11.05, 0.731, 1, 11.128, 0.731, 11.206, 0.378, 11.283, 0.378, 1, 11.333, 0.378, 11.383, 0.489, 11.433, 0.489, 1, 11.556, 0.489, 11.678, -0.799, 11.8, -0.799, 1, 11.867, -0.799, 11.933, -0.519, 12, -0.407]}, {"Target": "Parameter", "Id": "Param132", "Segments": [0, 0, 1, 0.106, 0, 0.211, 2.43, 0.317, 2.43, 1, 0.556, 2.43, 0.794, -2.221, 1.033, -2.221, 1, 1.278, -2.221, 1.522, 1.992, 1.767, 1.992, 1, 1.772, 1.992, 1.778, 1.992, 1.783, 1.992, 1, 2.022, 1.992, 2.261, -2.206, 2.5, -2.206, 1, 2.761, -2.206, 3.022, 2.346, 3.283, 2.346, 1, 3.533, 2.346, 3.783, -2.002, 4.033, -2.002, 1, 4.272, -2.002, 4.511, 2.137, 4.75, 2.137, 1, 5.011, 2.137, 5.272, -2.287, 5.533, -2.287, 1, 5.783, -2.287, 6.033, 2.036, 6.283, 2.036, 1, 6.522, 2.036, 6.761, -2.063, 7, -2.063, 1, 7.261, -2.063, 7.522, 2.292, 7.783, 2.292, 1, 8.033, 2.292, 8.283, -2.086, 8.533, -2.086, 1, 8.778, -2.086, 9.022, 2.022, 9.267, 2.022, 1, 9.517, 2.022, 9.767, -2.279, 10.017, -2.279, 1, 10.272, -2.279, 10.528, 2.154, 10.783, 2.154, 1, 11.022, 2.154, 11.261, -2.001, 11.5, -2.001, 1, 11.506, -2.001, 11.511, -2.001, 11.517, -2.001, 1, 11.678, -2.001, 11.839, -0.162, 12, 1.092]}, {"Target": "Parameter", "Id": "Param133", "Segments": [0, 0, 1, 0.067, 0, 0.133, -1.013, 0.2, -1.013, 1, 0.306, -1.013, 0.411, 0.994, 0.517, 0.994, 1, 0.6, 0.994, 0.683, 0.432, 0.767, 0.432, 1, 0.789, 0.432, 0.811, 0.446, 0.833, 0.446, 1, 0.95, 0.446, 1.067, -0.531, 1.183, -0.531, 1, 1.244, -0.531, 1.306, -0.381, 1.367, -0.381, 1, 1.439, -0.381, 1.511, -0.612, 1.583, -0.612, 1, 1.7, -0.612, 1.817, 0.586, 1.933, 0.586, 1, 2.006, 0.586, 2.078, 0.375, 2.15, 0.375, 1, 2.211, 0.375, 2.272, 0.59, 2.333, 0.59, 1, 2.45, 0.59, 2.567, -0.604, 2.683, -0.604, 1, 2.756, -0.604, 2.828, -0.403, 2.9, -0.403, 1, 2.917, -0.403, 2.933, -0.433, 2.95, -0.433, 1, 2.967, -0.433, 2.983, -0.362, 3, -0.362, 1, 3.039, -0.362, 3.078, -0.466, 3.117, -0.466, 1, 3.122, -0.466, 3.128, -0.455, 3.133, -0.455, 1, 3.139, -0.455, 3.144, -0.476, 3.15, -0.476, 1, 3.256, -0.476, 3.361, 0.648, 3.467, 0.648, 1, 3.539, 0.648, 3.611, 0.397, 3.683, 0.397, 1, 3.733, 0.397, 3.783, 0.562, 3.833, 0.562, 1, 3.95, 0.562, 4.067, -0.569, 4.183, -0.569, 1, 4.256, -0.569, 4.328, -0.379, 4.4, -0.379, 1, 4.461, -0.379, 4.522, -0.585, 4.583, -0.585, 1, 4.7, -0.585, 4.817, 0.604, 4.933, 0.604, 1, 5.006, 0.604, 5.078, 0.388, 5.15, 0.388, 1, 5.211, 0.388, 5.272, 0.603, 5.333, 0.603, 1, 5.45, 0.603, 5.567, -0.588, 5.683, -0.588, 1, 5.756, -0.588, 5.828, -0.387, 5.9, -0.387, 1, 5.961, -0.387, 6.022, -0.607, 6.083, -0.607, 1, 6.2, -0.607, 6.317, 0.576, 6.433, 0.576, 1, 6.506, 0.576, 6.578, 0.369, 6.65, 0.369, 1, 6.711, 0.369, 6.772, 0.582, 6.833, 0.582, 1, 6.95, 0.582, 7.067, -0.59, 7.183, -0.59, 1, 7.256, -0.59, 7.328, -0.389, 7.4, -0.389, 1, 7.461, -0.389, 7.522, -0.602, 7.583, -0.602, 1, 7.7, -0.602, 7.817, 0.592, 7.933, 0.592, 1, 8.006, 0.592, 8.078, 0.39, 8.15, 0.39, 1, 8.211, 0.39, 8.272, 0.611, 8.333, 0.611, 1, 8.45, 0.611, 8.567, -0.572, 8.683, -0.572, 1, 8.756, -0.572, 8.828, -0.366, 8.9, -0.366, 1, 8.961, -0.366, 9.022, -0.593, 9.083, -0.593, 1, 9.2, -0.593, 9.317, 0.592, 9.433, 0.592, 1, 9.506, 0.592, 9.578, 0.385, 9.65, 0.385, 1, 9.711, 0.385, 9.772, 0.596, 9.833, 0.596, 1, 9.95, 0.596, 10.067, -0.595, 10.183, -0.595, 1, 10.256, -0.595, 10.328, -0.395, 10.4, -0.395, 1, 10.461, -0.395, 10.522, -0.615, 10.583, -0.615, 1, 10.7, -0.615, 10.817, 0.523, 10.933, 0.523, 1, 10.939, 0.523, 10.944, 0.522, 10.95, 0.522, 1, 10.956, 0.522, 10.961, 0.525, 10.967, 0.525, 1, 11.028, 0.525, 11.089, 0.401, 11.15, 0.401, 1, 11.211, 0.401, 11.272, 0.592, 11.333, 0.592, 1, 11.45, 0.592, 11.567, -0.592, 11.683, -0.592, 1, 11.756, -0.592, 11.828, -0.39, 11.9, -0.39, 1, 11.933, -0.39, 11.967, -0.448, 12, -0.5]}, {"Target": "Parameter", "Id": "Param134", "Segments": [0, -0.007, 1, 0.1, -0.104, 0.2, -1.021, 0.3, -1.021, 1, 0.411, -1.021, 0.522, 1.396, 0.633, 1.396, 1, 0.856, 1.396, 1.078, -0.631, 1.3, -0.631, 1, 1.378, -0.631, 1.456, -0.394, 1.533, -0.394, 1, 1.583, -0.394, 1.633, -0.525, 1.683, -0.525, 1, 1.806, -0.525, 1.928, 0.799, 2.05, 0.799, 1, 2.133, 0.799, 2.217, 0.342, 2.3, 0.342, 1, 2.35, 0.342, 2.4, 0.47, 2.45, 0.47, 1, 2.567, 0.47, 2.683, -0.812, 2.8, -0.812, 1, 3.061, -0.812, 3.322, 0.816, 3.583, 0.816, 1, 3.672, 0.816, 3.761, 0.375, 3.85, 0.375, 1, 3.878, 0.375, 3.906, 0.412, 3.933, 0.412, 1, 3.939, 0.412, 3.944, 0.412, 3.95, 0.412, 1, 4.067, 0.412, 4.183, -0.753, 4.3, -0.753, 1, 4.383, -0.753, 4.467, -0.361, 4.55, -0.361, 1, 4.6, -0.361, 4.65, -0.468, 4.7, -0.468, 1, 4.817, -0.468, 4.933, 0.809, 5.05, 0.809, 1, 5.133, 0.809, 5.217, 0.357, 5.3, 0.357, 1, 5.35, 0.357, 5.4, 0.482, 5.45, 0.482, 1, 5.567, 0.482, 5.683, -0.795, 5.8, -0.795, 1, 5.883, -0.795, 5.967, -0.364, 6.05, -0.364, 1, 6.1, -0.364, 6.15, -0.488, 6.2, -0.488, 1, 6.317, -0.488, 6.433, 0.781, 6.55, 0.781, 1, 6.633, 0.781, 6.717, 0.341, 6.8, 0.341, 1, 6.85, 0.341, 6.9, 0.462, 6.95, 0.462, 1, 7.067, 0.462, 7.183, -0.793, 7.3, -0.793, 1, 7.383, -0.793, 7.467, -0.363, 7.55, -0.363, 1, 7.6, -0.363, 7.65, -0.483, 7.7, -0.483, 1, 7.811, -0.483, 7.922, 0.824, 8.033, 0.824, 1, 8.122, 0.824, 8.211, 0.35, 8.3, 0.35, 1, 8.35, 0.35, 8.4, 0.498, 8.45, 0.498, 1, 8.567, 0.498, 8.683, -0.78, 8.8, -0.78, 1, 8.883, -0.78, 8.967, -0.341, 9.05, -0.341, 1, 9.1, -0.341, 9.15, -0.475, 9.2, -0.475, 1, 9.317, -0.475, 9.433, 0.798, 9.55, 0.798, 1, 9.633, 0.798, 9.717, 0.357, 9.8, 0.357, 1, 9.85, 0.357, 9.9, 0.474, 9.95, 0.474, 1, 10.067, 0.474, 10.183, -0.801, 10.3, -0.801, 1, 10.383, -0.801, 10.467, -0.371, 10.55, -0.371, 1, 10.6, -0.371, 10.65, -0.496, 10.7, -0.496, 1, 10.817, -0.496, 10.933, 0.731, 11.05, 0.731, 1, 11.128, 0.731, 11.206, 0.378, 11.283, 0.378, 1, 11.333, 0.378, 11.383, 0.489, 11.433, 0.489, 1, 11.556, 0.489, 11.678, -0.799, 11.8, -0.799, 1, 11.867, -0.799, 11.933, -0.519, 12, -0.407]}, {"Target": "Parameter", "Id": "Param30", "Segments": [0, 1.491, 1, 0.111, 2.376, 0.222, 3.241, 0.333, 3.241, 1, 0.517, 3.241, 0.7, -4.616, 0.883, -4.616, 1, 1.122, -4.616, 1.361, 2.81, 1.6, 2.81, 1, 1.872, 2.81, 2.144, -3.37, 2.417, -3.37, 1, 2.661, -3.37, 2.906, 3.362, 3.15, 3.362, 1, 3.4, 3.362, 3.65, -3.243, 3.9, -3.243, 1, 4.15, -3.243, 4.4, 3.238, 4.65, 3.238, 1, 4.9, 3.238, 5.15, -3.371, 5.4, -3.371, 1, 5.65, -3.371, 5.9, 3.266, 6.15, 3.266, 1, 6.4, 3.266, 6.65, -3.188, 6.9, -3.188, 1, 7.15, -3.188, 7.4, 3.363, 7.65, 3.363, 1, 7.9, 3.363, 8.15, -3.304, 8.4, -3.304, 1, 8.65, -3.304, 8.9, 3.173, 9.15, 3.173, 1, 9.4, 3.173, 9.65, -3.355, 9.9, -3.355, 1, 10.15, -3.355, 10.4, 3.341, 10.65, 3.341, 1, 10.9, 3.341, 11.15, -3.21, 11.4, -3.21, 1, 11.6, -3.21, 11.8, 1.168, 12, 2.76]}, {"Target": "Parameter", "Id": "Param31", "Segments": [0, -0.669, 1, 0.067, -0.669, 0.133, -0.811, 0.2, -1.593, 1, 0.55, -5.702, 0.9, -8.091, 1.25, -8.091, 1, 1.511, -8.091, 1.772, 5.013, 2.033, 5.013, 1, 2.283, 5.013, 2.533, -6.5, 2.783, -6.5, 1, 3.033, -6.5, 3.283, 6.263, 3.533, 6.263, 1, 3.783, 6.263, 4.033, -5.905, 4.283, -5.905, 1, 4.533, -5.905, 4.783, 6.105, 5.033, 6.105, 1, 5.283, 6.105, 5.533, -6.332, 5.783, -6.332, 1, 6.033, -6.332, 6.283, 5.939, 6.533, 5.939, 1, 6.783, 5.939, 7.033, -5.955, 7.283, -5.955, 1, 7.533, -5.955, 7.783, 6.407, 8.033, 6.407, 1, 8.283, 6.407, 8.533, -6.011, 8.783, -6.011, 1, 9.033, -6.011, 9.283, 5.895, 9.533, 5.895, 1, 9.783, 5.895, 10.033, -6.36, 10.283, -6.36, 1, 10.533, -6.36, 10.783, 6.117, 11.033, 6.117, 1, 11.283, 6.117, 11.533, -5.911, 11.783, -5.911, 1, 11.856, -5.911, 11.928, -4.844, 12, -3.341]}, {"Target": "Parameter", "Id": "Param34", "Segments": [0, -11, 1, 0.667, -11, 1.333, 11, 2, 11, 1, 2.667, 11, 3.333, -11, 4, -11, 1, 4.667, -11, 5.333, 11, 6, 11, 1, 6.667, 11, 7.333, -11, 8, -11, 1, 8.667, -11, 9.333, 11, 10, 11, 1, 10.667, 11, 11.333, -11, 12, -11]}, {"Target": "Parameter", "Id": "Param56", "Segments": [0, -5.3, 1, 0.222, -5.3, 0.444, -11, 0.667, -11, 1, 1.333, -11, 2, 11, 2.667, 11, 1, 3.333, 11, 4, -11, 4.667, -11, 1, 5.333, -11, 6, 11, 6.667, 11, 1, 7.333, 11, 8, -11, 8.667, -11, 1, 9.333, -11, 10, 11, 10.667, 11, 1, 11.111, 11, 11.556, -5.3, 12, -5.3]}, {"Target": "Parameter", "Id": "Param57", "Segments": [0, 4.04, 1, 0.417, 4.04, 0.833, -11, 1.25, -11, 1, 1.917, -11, 2.583, 11, 3.25, 11, 1, 3.917, 11, 4.583, -11, 5.25, -11, 1, 5.917, -11, 6.583, 11, 7.25, 11, 1, 7.917, 11, 8.583, -11, 9.25, -11, 1, 9.917, -11, 10.583, 11, 11.25, 11, 1, 11.5, 11, 11.75, 4.04, 12, 4.04]}, {"Target": "Parameter", "Id": "Param58", "Segments": [0, 10.9, 1, 0.667, 10.9, 1.333, -11, 2, -11, 1, 2.667, -11, 3.333, 11, 4, 11, 1, 4.667, 11, 5.333, -11, 6, -11, 1, 6.667, -11, 7.333, 11, 8, 11, 1, 8.667, 11, 9.333, -11, 10, -11, 1, 10.667, -11, 11.333, 11, 12, 11]}, {"Target": "Parameter", "Id": "Param61", "Segments": [0, 6.47, 1, 0.194, 6.47, 0.389, 11, 0.583, 11, 1, 1.278, 11, 1.972, -11, 2.667, -11, 1, 3.333, -11, 4, 11, 4.667, 11, 1, 5.333, 11, 6, -11, 6.667, -11, 1, 7.333, -11, 8, 11, 8.667, 11, 1, 9.333, 11, 10, -11, 10.667, -11, 1, 11.111, -11, 11.556, 6.47, 12, 6.47]}, {"Target": "Parameter", "Id": "Param60", "Segments": [0, -11, 1, 0.694, -11, 1.389, 11, 2.083, 11, 1, 2.75, 11, 3.417, -11, 4.083, -11, 1, 4.75, -11, 5.417, 11, 6.083, 11, 1, 6.75, 11, 7.417, -11, 8.083, -11, 1, 8.75, -11, 9.417, 11, 10.083, 11, 1, 10.722, 11, 11.361, -11, 12, -11]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh232", "Segments": [0, 1.261, 1, 0.106, 1.884, 0.211, 2.477, 0.317, 2.477, 1, 0.5, 2.477, 0.683, -3.028, 0.867, -3.028, 1, 1.117, -3.028, 1.367, 1.845, 1.617, 1.845, 1, 1.883, 1.845, 2.15, -2.302, 2.417, -2.302, 1, 2.656, -2.302, 2.894, 2.207, 3.133, 2.207, 1, 3.389, 2.207, 3.644, -2.146, 3.9, -2.146, 1, 4.15, -2.146, 4.4, 2.226, 4.65, 2.226, 1, 4.9, 2.226, 5.15, -2.194, 5.4, -2.194, 1, 5.65, -2.194, 5.9, 2.19, 6.15, 2.19, 1, 6.4, 2.19, 6.65, -2.198, 6.9, -2.198, 1, 7.15, -2.198, 7.4, 2.208, 7.65, 2.208, 1, 7.9, 2.208, 8.15, -2.189, 8.4, -2.189, 1, 8.65, -2.189, 8.9, 2.194, 9.15, 2.194, 1, 9.4, 2.194, 9.65, -2.208, 9.9, -2.208, 1, 10.15, -2.208, 10.4, 2.19, 10.65, 2.19, 1, 10.9, 2.19, 11.15, -2.19, 11.4, -2.19, 1, 11.6, -2.19, 11.8, 0.594, 12, 1.708]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh232", "Segments": [0, 0.162, 1, 0.006, 0.179, 0.011, 0.189, 0.017, 0.189, 1, 0.078, 0.189, 0.139, -1.241, 0.2, -1.241, 1, 0.322, -1.241, 0.444, 6.997, 0.567, 6.997, 1, 0.75, 6.997, 0.933, -6.462, 1.117, -6.462, 1, 1.4, -6.462, 1.683, 4.582, 1.967, 4.582, 1, 2.206, 4.582, 2.444, -5.57, 2.683, -5.57, 1, 2.928, -5.57, 3.172, 5.219, 3.417, 5.219, 1, 3.672, 5.219, 3.928, -5.178, 4.183, -5.178, 1, 4.433, -5.178, 4.683, 5.302, 4.933, 5.302, 1, 5.183, 5.302, 5.433, -5.223, 5.683, -5.223, 1, 5.933, -5.223, 6.183, 5.203, 6.433, 5.203, 1, 6.683, 5.203, 6.933, -5.256, 7.183, -5.256, 1, 7.433, -5.256, 7.683, 5.264, 7.933, 5.264, 1, 8.183, 5.264, 8.433, -5.192, 8.683, -5.192, 1, 8.933, -5.192, 9.183, 5.244, 9.433, 5.244, 1, 9.683, 5.244, 9.933, -5.27, 10.183, -5.27, 1, 10.433, -5.27, 10.683, 5.197, 10.933, 5.197, 1, 11.183, 5.197, 11.433, -5.228, 11.683, -5.228, 1, 11.789, -5.228, 11.894, -3.374, 12, -1.232]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh232", "Segments": [0, -0.649, 1, 0.056, -0.496, 0.111, 0.343, 0.167, 0.343, 1, 0.244, 0.343, 0.322, -2.747, 0.4, -2.747, 1, 0.511, -2.747, 0.622, 4.579, 0.733, 4.579, 1, 0.894, 4.579, 1.056, -2.756, 1.217, -2.756, 1, 1.356, -2.756, 1.494, 0.069, 1.633, 0.069, 1, 1.689, 0.069, 1.744, -0.228, 1.8, -0.228, 1, 1.933, -0.228, 2.067, 1.725, 2.2, 1.725, 1, 2.422, 1.725, 2.644, -2.166, 2.867, -2.166, 1, 3.111, -2.166, 3.356, 1.849, 3.6, 1.849, 1, 3.861, 1.849, 4.122, -1.959, 4.383, -1.959, 1, 4.628, -1.959, 4.872, 1.922, 5.117, 1.922, 1, 5.367, 1.922, 5.617, -1.895, 5.867, -1.895, 1, 6.117, -1.895, 6.367, 1.921, 6.617, 1.921, 1, 6.867, 1.921, 7.117, -1.912, 7.367, -1.912, 1, 7.617, -1.912, 7.867, 1.914, 8.117, 1.914, 1, 8.367, 1.914, 8.617, -1.909, 8.867, -1.909, 1, 9.122, -1.909, 9.378, 1.902, 9.633, 1.902, 1, 9.878, 1.902, 10.122, -1.908, 10.367, -1.908, 1, 10.617, -1.908, 10.867, 1.909, 11.117, 1.909, 1, 11.367, 1.909, 11.617, -1.928, 11.867, -1.928, 1, 11.911, -1.928, 11.956, -1.813, 12, -1.623]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh232", "Segments": [0, -0.739, 1, 0.028, -0.845, 0.056, -0.909, 0.083, -0.909, 1, 0.144, -0.909, 0.206, 0, 0.267, 0, 1, 0.35, 0, 0.433, -2.695, 0.517, -2.695, 1, 0.628, -2.695, 0.739, 5.85, 0.85, 5.85, 1, 0.989, 5.85, 1.128, -4.01, 1.267, -4.01, 1, 1.411, -4.01, 1.556, 0.637, 1.7, 0.637, 1, 1.778, 0.637, 1.856, -0.258, 1.933, -0.258, 1, 2.056, -0.258, 2.178, 2.102, 2.3, 2.102, 1, 2.517, 2.102, 2.733, -2.524, 2.95, -2.524, 1, 3.206, -2.524, 3.461, 2.067, 3.717, 2.067, 1, 3.972, 2.067, 4.228, -2.258, 4.483, -2.258, 1, 4.728, -2.258, 4.972, 2.167, 5.217, 2.167, 1, 5.467, 2.167, 5.717, -2.153, 5.967, -2.153, 1, 6.217, -2.153, 6.467, 2.185, 6.717, 2.185, 1, 6.967, 2.185, 7.217, -2.17, 7.467, -2.17, 1, 7.717, -2.17, 7.967, 2.177, 8.217, 2.177, 1, 8.467, 2.177, 8.717, -2.169, 8.967, -2.169, 1, 9.222, -2.169, 9.478, 2.104, 9.733, 2.104, 1, 9.978, 2.104, 10.222, -2.166, 10.467, -2.166, 1, 10.717, -2.166, 10.967, 2.171, 11.217, 2.171, 1, 11.467, 2.171, 11.717, -2.187, 11.967, -2.187, 1, 11.978, -2.187, 11.989, -2.179, 12, -2.164]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh232", "Segments": [0, -0.894, 1, 0.011, -0.944, 0.022, -0.971, 0.033, -0.971, 1, 0.072, -0.971, 0.111, -0.914, 0.15, -0.914, 1, 0.161, -0.914, 0.172, -0.954, 0.183, -0.954, 1, 0.25, -0.954, 0.317, -0.192, 0.383, -0.192, 1, 0.461, -0.192, 0.539, -2.738, 0.617, -2.738, 1, 0.728, -2.738, 0.839, 7.16, 0.95, 7.16, 1, 1.083, 7.16, 1.217, -5.978, 1.35, -5.978, 1, 1.483, -5.978, 1.617, 1.609, 1.75, 1.609, 1, 1.85, 1.609, 1.95, -0.58, 2.05, -0.58, 1, 2.167, -0.58, 2.283, 2.598, 2.4, 2.598, 1, 2.611, 2.598, 2.822, -2.808, 3.033, -2.808, 1, 3.294, -2.808, 3.556, 2.178, 3.817, 2.178, 1, 4.072, 2.178, 4.328, -2.481, 4.583, -2.481, 1, 4.828, -2.481, 5.072, 2.292, 5.317, 2.292, 1, 5.572, 2.292, 5.828, -2.327, 6.083, -2.327, 1, 6.328, -2.327, 6.572, 2.363, 6.817, 2.363, 1, 7.067, 2.363, 7.317, -2.339, 7.567, -2.339, 1, 7.817, -2.339, 8.067, 2.353, 8.317, 2.353, 1, 8.567, 2.353, 8.817, -2.34, 9.067, -2.34, 1, 9.317, -2.34, 9.567, 2.275, 9.817, 2.275, 1, 10.067, 2.275, 10.317, -2.344, 10.567, -2.344, 1, 10.817, -2.344, 11.067, 2.347, 11.317, 2.347, 1, 11.544, 2.347, 11.772, -1.56, 12, -2.254]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh233", "Segments": [0, -0.172, 1, 0.05, -1.058, 0.1, -1.786, 0.15, -1.786, 1, 0.25, -1.786, 0.35, -0.041, 0.45, -0.041, 1, 0.544, -0.041, 0.639, -2.696, 0.733, -2.696, 1, 0.844, -2.696, 0.956, 8.525, 1.067, 8.525, 1, 1.189, 8.525, 1.311, -8.519, 1.433, -8.519, 1, 1.567, -8.519, 1.7, 3.337, 1.833, 3.337, 1, 1.939, 3.337, 2.044, -1.34, 2.15, -1.34, 1, 2.267, -1.34, 2.383, 3.317, 2.5, 3.317, 1, 2.7, 3.317, 2.9, -3.023, 3.1, -3.023, 1, 3.372, -3.023, 3.644, 2.152, 3.917, 2.152, 1, 4.178, 2.152, 4.439, -2.603, 4.7, -2.603, 1, 4.939, -2.603, 5.178, 2.323, 5.417, 2.323, 1, 5.667, 2.323, 5.917, -2.396, 6.167, -2.396, 1, 6.417, -2.396, 6.667, 2.432, 6.917, 2.432, 1, 7.167, 2.432, 7.417, -2.397, 7.667, -2.397, 1, 7.917, -2.397, 8.167, 2.419, 8.417, 2.419, 1, 8.667, 2.419, 8.917, -2.401, 9.167, -2.401, 1, 9.411, -2.401, 9.656, 2.349, 9.9, 2.349, 1, 10.156, 2.349, 10.411, -2.424, 10.667, -2.424, 1, 10.917, -2.424, 11.167, 2.416, 11.417, 2.416, 1, 11.611, 2.416, 11.806, -0.509, 12, -1.809]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh234", "Segments": [0, 1.761, 1, 0.078, -0.324, 0.156, -2.987, 0.233, -2.987, 1, 0.339, -2.987, 0.444, 0.483, 0.55, 0.483, 1, 0.644, 0.483, 0.739, -2.851, 0.833, -2.851, 1, 0.944, -2.851, 1.056, 9.979, 1.167, 9.979, 1, 1.289, 9.979, 1.411, -11.568, 1.533, -11.568, 1, 1.661, -11.568, 1.789, 5.982, 1.917, 5.982, 1, 2.028, 5.982, 2.139, -2.768, 2.25, -2.768, 1, 2.367, -2.768, 2.483, 4.444, 2.6, 4.444, 1, 2.767, 4.444, 2.933, -3.46, 3.1, -3.46, 1, 3.3, -3.46, 3.5, 2.159, 3.7, 2.159, 1, 3.761, 2.159, 3.822, 1.801, 3.883, 1.801, 1, 3.933, 1.801, 3.983, 1.986, 4.033, 1.986, 1, 4.289, 1.986, 4.544, -2.602, 4.8, -2.602, 1, 4.95, -2.602, 5.1, 2.575, 5.25, 2.575, 1, 5.589, 2.575, 5.928, -2.351, 6.267, -2.351, 1, 6.511, -2.351, 6.756, 2.384, 7, 2.384, 1, 7.25, 2.384, 7.5, -2.331, 7.75, -2.331, 1, 8, -2.331, 8.25, 2.366, 8.5, 2.366, 1, 8.75, 2.366, 9, -2.34, 9.25, -2.34, 1, 9.489, -2.34, 9.728, 2.327, 9.967, 2.327, 1, 10.222, 2.327, 10.478, -2.41, 10.733, -2.41, 1, 10.989, -2.41, 11.244, 2.362, 11.5, 2.362, 1, 11.667, 2.362, 11.833, 0.264, 12, -1.134]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh228", "Segments": [0, 0.263, 1, 0.128, 0.949, 0.256, 2.606, 0.383, 2.606, 1, 0.578, 2.606, 0.772, -3.395, 0.967, -3.395, 1, 1.211, -3.395, 1.456, 2.62, 1.7, 2.62, 1, 1.956, 2.62, 2.211, -2.751, 2.467, -2.751, 1, 2.717, -2.751, 2.967, 2.89, 3.217, 2.89, 1, 3.467, 2.89, 3.717, -2.676, 3.967, -2.676, 1, 4.217, -2.676, 4.467, 2.73, 4.717, 2.73, 1, 4.967, 2.73, 5.217, -2.864, 5.467, -2.864, 1, 5.717, -2.864, 5.967, 2.704, 6.217, 2.704, 1, 6.467, 2.704, 6.717, -2.676, 6.967, -2.676, 1, 7.217, -2.676, 7.467, 2.859, 7.717, 2.859, 1, 7.967, 2.859, 8.217, -2.745, 8.467, -2.745, 1, 8.717, -2.745, 8.967, 2.653, 9.217, 2.653, 1, 9.467, 2.653, 9.717, -2.849, 9.967, -2.849, 1, 10.217, -2.849, 10.467, 2.793, 10.717, 2.793, 1, 10.967, 2.793, 11.217, -2.663, 11.467, -2.663, 1, 11.644, -2.663, 11.822, 0.109, 12, 1.71]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh228", "Segments": [0, -0.475, 1, 0.061, -0.582, 0.122, -0.661, 0.183, -0.661, 1, 0.322, -0.661, 0.461, 5.014, 0.6, 5.014, 1, 0.8, 5.014, 1, -5.325, 1.2, -5.325, 1, 1.456, -5.325, 1.711, 4.067, 1.967, 4.067, 1, 2.217, 4.067, 2.467, -4.509, 2.717, -4.509, 1, 2.967, -4.509, 3.217, 4.771, 3.467, 4.771, 1, 3.717, 4.771, 3.967, -4.237, 4.217, -4.237, 1, 4.467, -4.237, 4.717, 4.428, 4.967, 4.428, 1, 5.217, 4.428, 5.467, -4.636, 5.717, -4.636, 1, 5.967, -4.636, 6.217, 4.307, 6.467, 4.307, 1, 6.717, 4.307, 6.967, -4.307, 7.217, -4.307, 1, 7.467, -4.307, 7.717, 4.645, 7.967, 4.645, 1, 8.217, 4.645, 8.467, -4.358, 8.717, -4.358, 1, 8.967, -4.358, 9.217, 4.208, 9.467, 4.208, 1, 9.717, 4.208, 9.967, -4.635, 10.217, -4.635, 1, 10.467, -4.635, 10.717, 4.457, 10.967, 4.457, 1, 11.217, 4.457, 11.467, -4.248, 11.717, -4.248, 1, 11.811, -4.248, 11.906, -2.991, 12, -1.426]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh228", "Segments": [0, -0.739, 1, 0.122, -1.27, 0.244, -1.859, 0.367, -1.859, 1, 0.506, -1.859, 0.644, 3.101, 0.783, 3.101, 1, 0.961, 3.101, 1.139, -2.333, 1.317, -2.333, 1, 1.594, -2.333, 1.872, 1.631, 2.15, 1.631, 1, 2.4, 1.631, 2.65, -1.877, 2.9, -1.877, 1, 3.15, -1.877, 3.4, 1.937, 3.65, 1.937, 1, 3.9, 1.937, 4.15, -1.791, 4.4, -1.791, 1, 4.489, -1.791, 4.578, -0.934, 4.667, -0.934, 1, 4.678, -0.934, 4.689, -0.955, 4.7, -0.955, 1, 4.839, -0.955, 4.978, 1.881, 5.117, 1.881, 1, 5.372, 1.881, 5.628, -1.815, 5.883, -1.815, 1, 6.133, -1.815, 6.383, 1.829, 6.633, 1.829, 1, 6.883, 1.829, 7.133, -1.813, 7.383, -1.813, 1, 7.633, -1.813, 7.883, 1.822, 8.133, 1.822, 1, 8.383, 1.822, 8.633, -1.782, 8.883, -1.782, 1, 8.95, -1.782, 9.017, -1.208, 9.083, -1.208, 1, 9.1, -1.208, 9.117, -1.281, 9.133, -1.281, 1, 9.3, -1.281, 9.467, 1.76, 9.633, 1.76, 1, 9.883, 1.76, 10.133, -1.839, 10.383, -1.839, 1, 10.639, -1.839, 10.894, 1.803, 11.15, 1.803, 1, 11.394, 1.803, 11.639, -1.835, 11.883, -1.835, 1, 11.922, -1.835, 11.961, -1.743, 12, -1.588]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh228", "Segments": [0, -0.464, 1, 0.156, -1.1, 0.311, -2.013, 0.467, -2.013, 1, 0.6, -2.013, 0.733, 3.967, 0.867, 3.967, 1, 1.039, 3.967, 1.211, -2.904, 1.383, -2.904, 1, 1.672, -2.904, 1.961, 1.875, 2.25, 1.875, 1, 2.494, 1.875, 2.739, -2.203, 2.983, -2.203, 1, 3.239, -2.203, 3.494, 2.2, 3.75, 2.2, 1, 3.994, 2.2, 4.239, -2.071, 4.483, -2.071, 1, 4.489, -2.071, 4.494, -2.071, 4.5, -2.071, 1, 4.739, -2.071, 4.978, 2.245, 5.217, 2.245, 1, 5.478, 2.245, 5.739, -2.161, 6, -2.161, 1, 6.244, -2.161, 6.489, 2.158, 6.733, 2.158, 1, 6.983, 2.158, 7.233, -2.106, 7.483, -2.106, 1, 7.733, -2.106, 7.983, 2.126, 8.233, 2.126, 1, 8.483, 2.126, 8.733, -2.087, 8.983, -2.087, 1, 9.228, -2.087, 9.472, 2.128, 9.717, 2.128, 1, 9.972, 2.128, 10.228, -2.162, 10.483, -2.162, 1, 10.733, -2.162, 10.983, 2.13, 11.233, 2.13, 1, 11.483, 2.13, 11.733, -2.139, 11.983, -2.139, 1, 11.989, -2.139, 11.994, -2.137, 12, -2.132]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh228", "Segments": [0, -0.141, 1, 0.189, -0.807, 0.378, -2.169, 0.567, -2.169, 1, 0.7, -2.169, 0.833, 4.942, 0.967, 4.942, 1, 1.122, 4.942, 1.278, -3.76, 1.433, -3.76, 1, 1.594, -3.76, 1.756, 0.517, 1.917, 0.517, 1, 1.944, 0.517, 1.972, 0.484, 2, 0.484, 1, 2.122, 0.484, 2.244, 2.091, 2.367, 2.091, 1, 2.606, 2.091, 2.844, -2.477, 3.083, -2.477, 1, 3.339, -2.477, 3.594, 2.392, 3.85, 2.392, 1, 4.1, 2.392, 4.35, -2.27, 4.6, -2.27, 1, 4.844, -2.27, 5.089, 2.518, 5.333, 2.518, 1, 5.589, 2.518, 5.844, -2.39, 6.1, -2.39, 1, 6.344, -2.39, 6.589, 2.408, 6.833, 2.408, 1, 7.083, 2.408, 7.333, -2.323, 7.583, -2.323, 1, 7.833, -2.323, 8.083, 2.366, 8.333, 2.366, 1, 8.583, 2.366, 8.833, -2.328, 9.083, -2.328, 1, 9.328, -2.328, 9.572, 2.432, 9.817, 2.432, 1, 10.072, 2.432, 10.328, -2.43, 10.583, -2.43, 1, 10.833, -2.43, 11.083, 2.403, 11.333, 2.403, 1, 11.556, 2.403, 11.778, -1.449, 12, -2.305]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_6_ArtMesh228", "Segments": [0, 0.198, 1, 0.222, -0.323, 0.444, -2.322, 0.667, -2.322, 1, 0.8, -2.322, 0.933, 6.019, 1.067, 6.019, 1, 1.211, 6.019, 1.356, -5.109, 1.5, -5.109, 1, 1.656, -5.109, 1.811, 1.192, 1.967, 1.192, 1, 2.033, 1.192, 2.1, 0.582, 2.167, 0.582, 1, 2.272, 0.582, 2.378, 2.313, 2.483, 2.313, 1, 2.717, 2.313, 2.95, -2.659, 3.183, -2.659, 1, 3.439, -2.659, 3.694, 2.49, 3.95, 2.49, 1, 4.194, 2.49, 4.439, -2.363, 4.683, -2.363, 1, 4.933, -2.363, 5.183, 2.689, 5.433, 2.689, 1, 5.689, 2.689, 5.944, -2.519, 6.2, -2.519, 1, 6.45, -2.519, 6.7, 2.547, 6.95, 2.547, 1, 7.194, 2.547, 7.439, -2.432, 7.683, -2.432, 1, 7.933, -2.432, 8.183, 2.514, 8.433, 2.514, 1, 8.683, 2.514, 8.933, -2.47, 9.183, -2.47, 1, 9.428, -2.47, 9.672, 2.644, 9.917, 2.644, 1, 10.167, 2.644, 10.417, -2.452, 10.667, -2.452, 1, 10.922, -2.452, 11.178, 2.547, 11.433, 2.547, 1, 11.622, 2.547, 11.811, -0.504, 12, -1.89]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_7_ArtMesh228", "Segments": [0, 0.42, 1, 0.256, 0.42, 0.511, -2.46, 0.767, -2.46, 1, 0.894, -2.46, 1.022, 7.195, 1.15, 7.195, 1, 1.294, 7.195, 1.439, -6.957, 1.583, -6.957, 1, 1.728, -6.957, 1.872, 2.271, 2.017, 2.271, 1, 2.111, 2.271, 2.206, 0.315, 2.3, 0.315, 1, 2.4, 0.315, 2.5, 2.616, 2.6, 2.616, 1, 2.822, 2.616, 3.044, -2.715, 3.267, -2.715, 1, 3.522, -2.715, 3.778, 2.474, 4.033, 2.474, 1, 4.278, 2.474, 4.522, -2.336, 4.767, -2.336, 1, 5.017, -2.336, 5.267, 2.756, 5.517, 2.756, 1, 5.778, 2.756, 6.039, -2.509, 6.3, -2.509, 1, 6.544, -2.509, 6.789, 2.555, 7.033, 2.555, 1, 7.278, 2.555, 7.522, -2.408, 7.767, -2.408, 1, 8.017, -2.408, 8.267, 2.556, 8.517, 2.556, 1, 8.767, 2.556, 9.017, -2.508, 9.267, -2.508, 1, 9.511, -2.508, 9.756, 2.748, 10, 2.748, 1, 10.25, 2.748, 10.5, -2.825, 10.75, -2.825, 1, 11.011, -2.825, 11.272, 2.573, 11.533, 2.573, 1, 11.689, 2.573, 11.844, 0.493, 12, -1.021]}, {"Target": "Parameter", "Id": "ParamZhiYing1", "Segments": [0, 1, 1, 4, 1, 8, 1, 12, 1]}, {"Target": "Parameter", "Id": "ParamZhiYing1X", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamZhiYing1Y", "Segments": [0, 0, 1, 0.333, 0, 0.667, -0.1, 1, -0.1, 1, 1.333, -0.1, 1.667, 0, 2, 0, 1, 2.333, 0, 2.667, -0.1, 3, -0.1, 1, 3.333, -0.1, 3.667, 0, 4, 0, 1, 4.333, 0, 4.667, -0.1, 5, -0.1, 1, 5.333, -0.1, 5.667, 0, 6, 0, 1, 6.333, 0, 6.667, -0.1, 7, -0.1, 1, 7.333, -0.1, 7.667, 0, 8, 0, 1, 8.333, 0, 8.667, -0.1, 9, -0.1, 1, 9.333, -0.1, 9.667, 0, 10, 0, 1, 10.333, 0, 10.667, -0.1, 11, -0.1, 1, 11.333, -0.1, 11.667, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamZhiYing1Y3", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.833, 0, 1.167, -1.9, 1.5, -1.9, 1, 1.833, -1.9, 2.167, 0, 2.5, 0, 1, 2.833, 0, 3.167, -1.9, 3.5, -1.9, 1, 3.833, -1.9, 4.167, 0, 4.5, 0, 1, 4.833, 0, 5.167, -1.9, 5.5, -1.9, 1, 5.833, -1.9, 6.167, 0, 6.5, 0, 1, 6.833, 0, 7.167, -1.9, 7.5, -1.9, 1, 7.833, -1.9, 8.167, 0, 8.5, 0, 1, 8.833, 0, 9.167, -1.9, 9.5, -1.9, 1, 9.833, -1.9, 10.167, 0, 10.5, 0, 1, 10.833, 0, 11.167, -1.9, 11.5, -1.9, 1, 11.667, -1.9, 11.833, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamZhiYing1UD", "Segments": [0, 0, 1, 0.167, 0, 0.333, -3, 0.5, -3, 1, 0.667, -3, 0.833, 0, 1, 0, 1, 1.167, 0, 1.333, -3, 1.5, -3, 1, 1.667, -3, 1.833, 0, 2, 0, 1, 2.167, 0, 2.333, -3, 2.5, -3, 1, 2.667, -3, 2.833, 0, 3, 0, 1, 3.167, 0, 3.333, -3, 3.5, -3, 1, 3.667, -3, 3.833, 0, 4, 0, 1, 4.167, 0, 4.333, -3, 4.5, -3, 1, 4.667, -3, 4.833, 0, 5, 0, 1, 5.167, 0, 5.333, -3, 5.5, -3, 1, 5.667, -3, 5.833, 0, 6, 0, 1, 6.167, 0, 6.333, -3, 6.5, -3, 1, 6.667, -3, 6.833, 0, 7, 0, 1, 7.167, 0, 7.333, -3, 7.5, -3, 1, 7.667, -3, 7.833, 0, 8, 0, 1, 8.167, 0, 8.333, -3, 8.5, -3, 1, 8.667, -3, 8.833, 0, 9, 0, 1, 9.167, 0, 9.333, -3, 9.5, -3, 1, 9.667, -3, 9.833, 0, 10, 0, 1, 10.167, 0, 10.333, -3, 10.5, -3, 1, 10.667, -3, 10.833, 0, 11, 0, 1, 11.167, 0, 11.333, -3, 11.5, -3, 1, 11.667, -3, 11.833, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamZhiYing1TC", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamZhiYing1ShouR", "Segments": [0, 0, 1, 0.167, 0, 0.333, 6.2, 0.5, 6.2, 1, 0.667, 6.2, 0.833, 0, 1, 0, 1, 1.167, 0, 1.333, 6.2, 1.5, 6.2, 1, 1.667, 6.2, 1.833, 0, 2, 0, 1, 2.167, 0, 2.333, 6.2, 2.5, 6.2, 1, 2.667, 6.2, 2.833, 0, 3, 0, 1, 3.167, 0, 3.333, 6.2, 3.5, 6.2, 1, 3.667, 6.2, 3.833, 0, 4, 0, 1, 4.167, 0, 4.333, 6.2, 4.5, 6.2, 1, 4.667, 6.2, 4.833, 0, 5, 0, 1, 5.167, 0, 5.333, 6.2, 5.5, 6.2, 1, 5.667, 6.2, 5.833, 0, 6, 0, 1, 6.167, 0, 6.333, 6.2, 6.5, 6.2, 1, 6.667, 6.2, 6.833, 0, 7, 0, 1, 7.167, 0, 7.333, 6.2, 7.5, 6.2, 1, 7.667, 6.2, 7.833, 0, 8, 0, 1, 8.167, 0, 8.333, 6.2, 8.5, 6.2, 1, 8.667, 6.2, 8.833, 0, 9, 0, 1, 9.167, 0, 9.333, 6.2, 9.5, 6.2, 1, 9.667, 6.2, 9.833, 0, 10, 0, 1, 10.167, 0, 10.333, 6.2, 10.5, 6.2, 1, 10.667, 6.2, 10.833, 0, 11, 0, 1, 11.167, 0, 11.333, 6.2, 11.5, 6.2, 1, 11.667, 6.2, 11.833, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamZhiYing1ShouL", "Segments": [0, 0, 1, 0.167, 0, 0.333, 7, 0.5, 7, 1, 0.667, 7, 0.833, 0, 1, 0, 1, 1.167, 0, 1.333, 7, 1.5, 7, 1, 1.667, 7, 1.833, 0, 2, 0, 1, 2.167, 0, 2.333, 7, 2.5, 7, 1, 2.667, 7, 2.833, 0, 3, 0, 1, 3.167, 0, 3.333, 7, 3.5, 7, 1, 3.667, 7, 3.833, 0, 4, 0, 1, 4.167, 0, 4.333, 7, 4.5, 7, 1, 4.667, 7, 4.833, 0, 5, 0, 1, 5.167, 0, 5.333, 7, 5.5, 7, 1, 5.667, 7, 5.833, 0, 6, 0, 1, 6.167, 0, 6.333, 7, 6.5, 7, 1, 6.667, 7, 6.833, 0, 7, 0, 1, 7.167, 0, 7.333, 7, 7.5, 7, 1, 7.667, 7, 7.833, 0, 8, 0, 1, 8.167, 0, 8.333, 7, 8.5, 7, 1, 8.667, 7, 8.833, 0, 9, 0, 1, 9.167, 0, 9.333, 7, 9.5, 7, 1, 9.667, 7, 9.833, 0, 10, 0, 1, 10.167, 0, 10.333, 7, 10.5, 7, 1, 10.667, 7, 10.833, 0, 11, 0, 1, 11.167, 0, 11.333, 7, 11.5, 7, 1, 11.667, 7, 11.833, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param93", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamZhiYing2", "Segments": [0, 1, 1, 4, 1, 8, 1, 12, 1]}, {"Target": "Parameter", "Id": "ParamZhiYing1X2", "Segments": [0, 0, 1, 0.5, 0, 1, -0.05, 1.5, -0.05, 1, 2.167, -0.05, 2.833, 0, 3.5, 0, 1, 4.167, 0, 4.833, -0.05, 5.5, -0.05, 1, 6.167, -0.05, 6.833, 0, 7.5, 0, 1, 8.167, 0, 8.833, -0.05, 9.5, -0.05, 1, 10.167, -0.05, 10.833, 0, 11.5, 0, 1, 11.667, 0, 11.833, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamZhiYing1Y2", "Segments": [0, 0, 1, 0.5, 0, 1, -0.047, 1.5, -0.047, 1, 2.167, -0.047, 2.833, 0, 3.5, 0, 1, 4.167, 0, 4.833, -0.047, 5.5, -0.047, 1, 6.167, -0.047, 6.833, 0, 7.5, 0, 1, 8.167, 0, 8.833, -0.047, 9.5, -0.047, 1, 10.167, -0.047, 10.833, 0, 11.5, 0, 1, 11.667, 0, 11.833, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamZhiYing1Z2", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamZhiYing1S2", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamZhiYing1UD2", "Segments": [0, 0, 1, 0.5, 0, 1, -3.34, 1.5, -3.34, 1, 2.167, -3.34, 2.833, 0, 3.5, 0, 1, 4.167, 0, 4.833, -3.34, 5.5, -3.34, 1, 6.167, -3.34, 6.833, 0, 7.5, 0, 1, 8.167, 0, 8.833, -3.34, 9.5, -3.34, 1, 10.167, -3.34, 10.833, 0, 11.5, 0, 1, 11.667, 0, 11.833, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamZhiYing1UD3", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamZhiYing1ShouR2", "Segments": [0, 0, 1, 0.5, 0, 1, 2.2, 1.5, 2.2, 1, 2.167, 2.2, 2.833, 0, 3.5, 0, 1, 4.167, 0, 4.833, 2.2, 5.5, 2.2, 1, 6.167, 2.2, 6.833, 0, 7.5, 0, 1, 8.167, 0, 8.833, 2.2, 9.5, 2.2, 1, 10.167, 2.2, 10.833, 0, 11.5, 0, 1, 11.667, 0, 11.833, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamZhiYing1ShouL2", "Segments": [0, 0, 1, 0.5, 0, 1, 1.26, 1.5, 1.26, 1, 2.167, 1.26, 2.833, 0, 3.5, 0, 1, 4.167, 0, 4.833, 1.26, 5.5, 1.26, 1, 6.167, 1.26, 6.833, 0, 7.5, 0, 1, 8.167, 0, 8.833, 1.26, 9.5, 1.26, 1, 10.167, 1.26, 10.833, 0, 11.5, 0, 1, 11.667, 0, 11.833, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param97", "Segments": [0, 1, 1, 4, 1, 8, 1, 12, 1]}, {"Target": "Parameter", "Id": "Param98", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param99", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param100", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param192", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param191", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param193", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param85", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param86", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param88", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param89", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param87", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param91", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param90", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param139", "Segments": [0, 1, 1, 4, 1, 8, 1, 12, 1]}, {"Target": "Parameter", "Id": "Param138", "Segments": [0, 0, 1, 0.333, 0, 0.667, -2.42, 1, -2.42, 1, 1.333, -2.42, 1.667, 0, 2, 0, 1, 2.333, 0, 2.667, -2.42, 3, -2.42, 1, 3.333, -2.42, 3.667, 0, 4, 0, 1, 4.333, 0, 4.667, -2.42, 5, -2.42, 1, 5.333, -2.42, 5.667, 0, 6, 0, 1, 6.333, 0, 6.667, -2.42, 7, -2.42, 1, 7.333, -2.42, 7.667, 0, 8, 0, 1, 8.333, 0, 8.667, -2.42, 9, -2.42, 1, 9.333, -2.42, 9.667, 0, 10, 0, 1, 10.333, 0, 10.667, -2.42, 11, -2.42, 1, 11.333, -2.42, 11.667, 0.122, 12, 0.122]}, {"Target": "Parameter", "Id": "Param137", "Segments": [0, 0, 1, 0.333, 0, 0.667, -3.98, 1, -3.98, 1, 1.333, -3.98, 1.667, 0, 2, 0, 1, 2.333, 0, 2.667, -3.98, 3, -3.98, 1, 3.333, -3.98, 3.667, 0, 4, 0, 1, 4.333, 0, 4.667, -3.98, 5, -3.98, 1, 5.333, -3.98, 5.667, 0, 6, 0, 1, 6.333, 0, 6.667, -3.98, 7, -3.98, 1, 7.333, -3.98, 7.667, 0, 8, 0, 1, 8.333, 0, 8.667, -3.98, 9, -3.98, 1, 9.333, -3.98, 9.667, 0, 10, 0, 1, 10.333, 0, 10.667, -3.98, 11, -3.98, 1, 11.333, -3.98, 11.667, 1.777, 12, 1.777]}, {"Target": "Parameter", "Id": "Param94", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamZhiYing3", "Segments": [0, 1, 1, 4, 1, 8, 1, 12, 1]}, {"Target": "Parameter", "Id": "ParamZhiYing1X3", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamZhiYing1Y4", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamZhiYing1Z3", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param195", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param196", "Segments": [0, 0, 1, 0.167, 0, 0.333, -14, 0.5, -14, 1, 0.667, -14, 0.833, 0, 1, 0, 1, 1.167, 0, 1.333, -14, 1.5, -14, 1, 1.667, -14, 1.833, 0, 2, 0, 1, 2.167, 0, 2.333, -14, 2.5, -14, 1, 2.667, -14, 2.833, 0, 3, 0, 1, 3.167, 0, 3.333, -14, 3.5, -14, 1, 3.667, -14, 3.833, 0, 4, 0, 1, 4.167, 0, 4.333, -14, 4.5, -14, 1, 4.667, -14, 4.833, 0, 5, 0, 1, 5.167, 0, 5.333, -14, 5.5, -14, 1, 5.667, -14, 5.833, 0, 6, 0, 1, 6.167, 0, 6.333, -14, 6.5, -14, 1, 6.667, -14, 6.833, 0, 7, 0, 1, 7.167, 0, 7.333, -14, 7.5, -14, 1, 7.667, -14, 7.833, 0, 8, 0, 1, 8.167, 0, 8.333, -14, 8.5, -14, 1, 8.667, -14, 8.833, 0, 9, 0, 1, 9.167, 0, 9.333, -14, 9.5, -14, 1, 9.667, -14, 9.833, 0, 10, 0, 1, 10.167, 0, 10.333, -14, 10.5, -14, 1, 10.667, -14, 10.833, 0, 11, 0, 1, 11.167, 0, 11.333, -14, 11.5, -14, 1, 11.667, -14, 11.833, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamZhiYing1X4", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamZhiYing1Y5", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamZhiYing1Y6", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param194", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param95", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param96", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param149", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param145", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param146", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param147", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param148", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param151", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamHL2B", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param150", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamZCwater2", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "ParamZCwater3", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param74", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param73", "Segments": [0, 0, 1, 0.667, 0, 1.333, -7.689, 2, -7.689, 1, 2.667, -7.689, 3.333, 0, 4, 0, 1, 4.667, 0, 5.333, -7.689, 6, -7.689, 1, 6.667, -7.689, 7.333, 0, 8, 0, 1, 8.667, 0, 9.333, -7.689, 10, -7.689, 1, 10.667, -7.689, 11.333, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param72", "Segments": [0, 0, 1, 0.333, 0, 0.667, -3, 1, -3, 1, 1.667, -3, 2.333, 3, 3, 3, 1, 3.667, 3, 4.333, -3, 5, -3, 1, 5.667, -3, 6.333, 3, 7, 3, 1, 7.667, 3, 8.333, -3, 9, -3, 1, 9.667, -3, 10.333, 3, 11, 3, 1, 11.333, 3, 11.667, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param77", "Segments": [0, 0, 1, 0.333, 0, 0.667, 10, 1, 10, 1, 1.333, 10, 1.667, 0, 2, 0, 1, 2.333, 0, 2.667, 10, 3, 10, 1, 3.333, 10, 3.667, 0, 4, 0, 1, 4.333, 0, 4.667, 10, 5, 10, 1, 5.333, 10, 5.667, 0, 6, 0, 1, 6.333, 0, 6.667, 10, 7, 10, 1, 7.333, 10, 7.667, 0, 8, 0, 1, 8.333, 0, 8.667, 10, 9, 10, 1, 9.333, 10, 9.667, 0, 10, 0, 1, 10.333, 0, 10.667, 10, 11, 10, 1, 11.333, 10, 11.667, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param62", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param63", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param64", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param69", "Segments": [0, 10, 1, 4, 10, 8, 10, 12, 10]}, {"Target": "Parameter", "Id": "Param68", "Segments": [0, 6.369, 1, 1, 6.369, 2, 2.843, 3, 2.843, 1, 3.889, 2.843, 4.778, 6.369, 5.667, 6.369, 1, 6.689, 6.369, 7.711, 2.843, 8.733, 2.843, 1, 9.822, 2.843, 10.911, 6.369, 12, 6.369]}, {"Target": "Parameter", "Id": "Param76", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param78", "Segments": [0, 1, 1, 4, 1, 8, 1, 12, 1]}, {"Target": "Parameter", "Id": "Param109", "Segments": [0, -17, 1, 1, -17, 2, 8, 3, 8, 1, 4, 8, 5, -17, 6, -17, 1, 7, -17, 8, 8, 9, 8, 1, 10, 8, 11, -17, 12, -17]}, {"Target": "Parameter", "Id": "Param110", "Segments": [0, -14, 1, 0.167, -14, 0.333, -15.47, 0.5, -15.47, 1, 0.667, -15.47, 0.833, -8.04, 1, -8.04, 1, 1.167, -8.04, 1.333, -9.588, 1.5, -9.588, 1, 1.667, -9.588, 1.833, 4.2, 2, 4.2, 1, 2.167, 4.2, 2.333, -3.187, 2.5, -3.187, 1, 2.667, -3.187, 2.833, 9, 3, 9, 1, 3.167, 9, 3.333, 1.59, 3.5, 1.59, 1, 3.667, 1.59, 3.833, 1.833, 4, 1.833, 1, 4.167, 1.833, 4.333, -7.092, 4.5, -7.092, 1, 4.667, -7.092, 4.833, -1.992, 5, -1.992, 1, 5.167, -1.992, 5.333, -11.341, 5.5, -12.351, 1, 5.667, -13.361, 5.833, -13.296, 6, -14, 1, 6.167, -14.704, 6.333, -15.47, 6.5, -15.47, 1, 6.667, -15.47, 6.833, -8.04, 7, -8.04, 1, 7.167, -8.04, 7.333, -9.588, 7.5, -9.588, 1, 7.667, -9.588, 7.833, 4.2, 8, 4.2, 1, 8.167, 4.2, 8.333, -3.187, 8.5, -3.187, 1, 8.667, -3.187, 8.833, 9, 9, 9, 1, 9.167, 9, 9.333, 1.59, 9.5, 1.59, 1, 9.667, 1.59, 9.833, 1.833, 10, 1.833, 1, 10.167, 1.833, 10.333, -7.092, 10.5, -7.092, 1, 10.667, -7.092, 10.833, -1.992, 11, -1.992, 1, 11.167, -1.992, 11.333, -10.475, 11.5, -12.351, 1, 11.667, -14.227, 11.833, -14, 12, -14]}, {"Target": "Parameter", "Id": "Param111", "Segments": [0, 10, 1, 0.933, 10, 1.867, -17, 2.8, -17, 1, 3.744, -17, 4.689, 10, 5.633, 10, 1, 6.567, 10, 7.5, -17, 8.433, -17, 1, 9.622, -17, 10.811, 10, 12, 10]}, {"Target": "Parameter", "Id": "Param116", "Segments": [0, -0.97, 1, 0.25, -0.97, 0.5, 12, 0.75, 12, 1, 1.417, 12, 2.083, -29, 2.75, -29, 1, 3.417, -29, 4.083, 12, 4.75, 12, 1, 5.417, 12, 6.083, -29, 6.75, -29, 1, 7.417, -29, 8.083, 12, 8.75, 12, 1, 9.417, 12, 10.083, -29, 10.75, -29, 1, 11.167, -29, 11.583, -0.97, 12, -0.97]}, {"Target": "Parameter", "Id": "Param67", "Segments": [0, 0, 0, 12, 3.158]}, {"Target": "Parameter", "Id": "Param108", "Segments": [0, 0, 0, 11.983, 180, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param92", "Segments": [0, 0, 0, 11.983, 130, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param79", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 1, 0.4, 0, 0.7, 10, 1, 10, 2, 1.033, 0, 1, 1.739, 0, 2.444, 0, 3.15, 0, 1, 3.483, 0, 3.817, 10, 4.15, 10, 2, 4.183, 0, 1, 5.05, 0, 5.917, 0, 6.783, 0, 1, 7.117, 0, 7.45, 10, 7.783, 10, 2, 7.817, 0, 1, 8.683, 0, 9.55, 0, 10.417, 0, 1, 10.75, 0, 11.083, 10, 11.417, 10, 2, 11.45, 0, 1, 11.633, 0, 11.817, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param82", "Segments": [0, 0, 1, 0.2, 0, 0.4, 0, 0.6, 0, 1, 0.933, 0, 1.267, 10, 1.6, 10, 2, 1.633, 0, 1, 2.5, 0, 3.367, 0, 4.233, 0, 1, 4.567, 0, 4.9, 10, 5.233, 10, 2, 5.267, 0, 1, 6.4, 0, 7.533, 0, 8.667, 0, 1, 9, 0, 9.333, 10, 9.667, 10, 2, 9.7, 0, 1, 10.467, 0, 11.233, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param80", "Segments": [0, 0, 1, 0.444, 0, 0.889, 0, 1.333, 0, 1, 1.639, 0, 1.944, 10, 2.25, 10, 2, 2.283, 0, 1, 2.867, 0, 3.45, 0, 4.033, 0, 1, 4.339, 0, 4.644, 10, 4.95, 10, 2, 4.983, 0, 1, 5.85, 0, 6.717, 0, 7.583, 0, 1, 7.889, 0, 8.194, 10, 8.5, 10, 2, 8.533, 0, 1, 9.689, 0, 10.844, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param83", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.578, 0, 0.856, 10, 1.133, 10, 2, 1.167, 0, 1, 1.806, 0, 2.444, 0, 3.083, 0, 1, 3.361, 0, 3.639, 10, 3.917, 10, 2, 3.95, 0, 1, 4.978, 0, 6.006, 0, 7.033, 0, 1, 7.311, 0, 7.589, 10, 7.867, 10, 2, 7.9, 0, 1, 9.267, 0, 10.633, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param81", "Segments": [0, 0, 1, 0.656, 0, 1.311, 0, 1.967, 0, 1, 2.189, 0, 2.411, 10, 2.633, 10, 2, 2.667, 0, 1, 3.25, 0, 3.833, 0, 4.417, 0, 1, 4.656, 0, 4.894, 10, 5.133, 10, 2, 5.167, 0, 1, 5.811, 0, 6.456, 0, 7.1, 0, 1, 7.339, 0, 7.578, 10, 7.817, 10, 2, 7.85, 0, 1, 8.494, 0, 9.139, 0, 9.783, 0, 1, 10.022, 0, 10.261, 10, 10.5, 10, 2, 10.533, 0, 1, 11.022, 0, 11.511, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param84", "Segments": [0, 0, 1, 0.344, 0, 0.689, 0, 1.033, 0, 1, 1.244, 0, 1.456, 10, 1.667, 10, 2, 1.7, 0, 1, 2.761, 0, 3.822, 0, 4.883, 0, 1, 5.089, 0, 5.294, 10, 5.5, 10, 2, 5.533, 0, 1, 6.506, 0, 7.478, 0, 8.45, 0, 1, 8.661, 0, 8.872, 10, 9.083, 10, 2, 9.117, 0, 1, 10.078, 0, 11.039, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param233", "Segments": [0, 0, 0, 0.983, 10, 2, 1, 0, 0, 1.983, 10, 2, 2, 0, 0, 2.983, 10, 2, 3, 0, 0, 3.983, 10, 2, 4, 0, 0, 4.983, 10, 2, 5, 0, 0, 5.983, 10, 2, 6, 0, 0, 6.983, 10, 2, 7, 0, 0, 7.983, 10, 2, 8, 0, 0, 8.983, 10, 2, 9, 0, 0, 9.983, 10, 2, 10, 0, 0, 10.983, 10, 2, 11, 0, 0, 11.983, 10, 2, 12, 0]}, {"Target": "Parameter", "Id": "Param229", "Segments": [0, 0, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param230", "Segments": [0, 0, 1, 0.25, 0, 0.5, 10, 0.75, 10, 2, 0.767, 0, 1, 1.733, 0, 2.7, 0, 3.667, 0, 1, 3.917, 0, 4.167, 10, 4.417, 10, 2, 4.433, 0, 1, 5.4, 0, 6.367, 0, 7.333, 0, 1, 7.583, 0, 7.833, 10, 8.083, 10, 2, 8.1, 0, 1, 9.206, 0, 10.311, 0, 11.417, 0, 1, 11.611, 0, 11.806, 6.049, 12, 8.738]}, {"Target": "Parameter", "Id": "Param231", "Segments": [0, 0, 1, 0.389, 0, 0.778, 0, 1.167, 0, 1, 1.417, 0, 1.667, 10, 1.917, 10, 2, 1.933, 0, 1, 2.9, 0, 3.867, 0, 4.833, 0, 1, 5.083, 0, 5.333, 10, 5.583, 10, 2, 5.6, 0, 1, 6.567, 0, 7.533, 0, 8.5, 0, 1, 8.75, 0, 9, 10, 9.25, 10, 2, 9.267, 0, 1, 10.178, 0, 11.089, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param232", "Segments": [0, 0, 1, 0.75, 0, 1.5, 0, 2.25, 0, 1, 2.5, 0, 2.75, 10, 3, 10, 2, 3.017, 0, 1, 3.983, 0, 4.95, 0, 5.917, 0, 1, 6.167, 0, 6.417, 10, 6.667, 10, 2, 6.683, 0, 1, 7.65, 0, 8.617, 0, 9.583, 0, 1, 9.833, 0, 10.083, 10, 10.333, 10, 2, 10.35, 0, 1, 10.9, 0, 11.45, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param101", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param102", "Segments": [0, -1.5, 1, 0.5, -1.5, 1, 0, 1.5, 0, 1, 2.5, 0, 3.5, -1.513, 4.5, -1.513, 1, 5.5, -1.513, 6.5, 0, 7.5, 0, 1, 8.55, 0, 9.6, -2.459, 10.65, -2.459, 1, 11.1, -2.459, 11.55, -2.254, 12, -1.5]}, {"Target": "Parameter", "Id": "Param103", "Segments": [0, -5, 1, 1, -5, 2, 5, 3, 5, 1, 4, 5, 5, -5, 6, -5, 1, 7, -5, 8, 5, 9, 5, 1, 10, 5, 11, -5, 12, -5]}, {"Target": "Parameter", "Id": "Param104", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param105", "Segments": [0, 0, 1, 0.5, 0, 1, 3, 1.5, 3, 1, 2.5, 3, 3.5, -3, 4.5, -3, 1, 5.5, -3, 6.5, 3, 7.5, 3, 1, 8.5, 3, 9.5, -3, 10.5, -3, 1, 11, -3, 11.5, -1.5, 12, 0]}, {"Target": "Parameter", "Id": "Param106", "Segments": [0, -5, 1, 1, -5, 2, 5, 3, 5, 1, 4, 5, 5, -5, 6, -5, 1, 7, -5, 8, 5, 9, 5, 1, 10, 5, 11, -5, 12, -5]}, {"Target": "Parameter", "Id": "Param107", "Segments": [0, -3.91, 1, 0.267, -3.91, 0.533, -6.016, 0.8, -6.016, 1, 1.8, -6.016, 2.8, 5.989, 3.8, 5.989, 1, 4.8, 5.989, 5.8, -6.016, 6.8, -6.016, 1, 7.8, -6.016, 8.8, 5.989, 9.8, 5.989, 1, 10.533, 5.989, 11.267, 1.638, 12, -3.91]}, {"Target": "Parameter", "Id": "Param112", "Segments": [0, 10.97, 1, 0.167, 10.97, 0.333, 13, 0.5, 13, 1, 1.167, 13, 1.833, 0, 2.5, 0, 1, 3.167, 0, 3.833, 13, 4.5, 13, 1, 5.167, 13, 5.833, 0, 6.5, 0, 1, 7.167, 0, 7.833, 13, 8.5, 13, 1, 9.167, 13, 9.833, 0, 10.5, 0, 1, 11, 0, 11.5, 10.97, 12, 10.97]}, {"Target": "Parameter", "Id": "Param113", "Segments": [0, 6.5, 1, 0.333, 6.5, 0.667, 13, 1, 13, 1, 1.667, 13, 2.333, 0, 3, 0, 1, 3.667, 0, 4.333, 13, 5, 13, 1, 5.667, 13, 6.333, 0, 7, 0, 1, 7.667, 0, 8.333, 13, 9, 13, 1, 9.667, 13, 10.333, 0, 11, 0, 1, 11.333, 0, 11.667, 6.5, 12, 6.5]}, {"Target": "Parameter", "Id": "Param114", "Segments": [0, 2.03, 1, 0.5, 2.03, 1, 13, 1.5, 13, 1, 2.167, 13, 2.833, 0, 3.5, 0, 1, 4.167, 0, 4.833, 13, 5.5, 13, 1, 6.167, 13, 6.833, 0, 7.5, 0, 1, 8.167, 0, 8.833, 13, 9.5, 13, 1, 10.167, 13, 10.833, 0, 11.5, 0, 1, 11.667, 0, 11.833, 2.03, 12, 2.03]}, {"Target": "Parameter", "Id": "Param115", "Segments": [0, 0, 1, 0.667, 0, 1.333, 13, 2, 13, 1, 2.667, 13, 3.333, 0, 4, 0, 1, 4.667, 0, 5.333, 13, 6, 13, 1, 6.667, 13, 7.333, 0, 8, 0, 1, 8.667, 0, 9.333, 13, 10, 13, 1, 10.667, 13, 11.333, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param128", "Segments": [0, -30, 1, 0.117, -30, 0.233, 30, 0.35, 30, 1, 0.467, 30, 0.583, -30, 0.7, -30, 1, 0.817, -30, 0.933, 30, 1.05, 30, 1, 1.167, 30, 1.283, -30, 1.4, -30, 1, 1.517, -30, 1.633, 30, 1.75, 30, 1, 1.867, 30, 1.983, -30, 2.1, -30, 1, 2.217, -30, 2.333, 30, 2.45, 30, 1, 2.567, 30, 2.683, -30, 2.8, -30, 1, 2.917, -30, 3.033, 30, 3.15, 30, 1, 3.267, 30, 3.383, -30, 3.5, -30, 1, 3.617, -30, 3.733, 30, 3.85, 30, 1, 3.967, 30, 4.083, -30, 4.2, -30, 1, 4.317, -30, 4.433, 30, 4.55, 30, 1, 4.667, 30, 4.783, -30, 4.9, -30, 1, 5.017, -30, 5.133, 30, 5.25, 30, 1, 5.367, 30, 5.483, -30, 5.6, -30, 1, 5.717, -30, 5.833, 30, 5.95, 30, 1, 6.067, 30, 6.183, -30, 6.3, -30, 1, 6.417, -30, 6.533, 30, 6.65, 30, 1, 6.767, 30, 6.883, -30, 7, -30, 1, 7.117, -30, 7.233, 30, 7.35, 30, 1, 7.467, 30, 7.583, -30, 7.7, -30, 1, 7.817, -30, 7.933, 30, 8.05, 30, 1, 8.167, 30, 8.283, -30, 8.4, -30, 1, 8.517, -30, 8.633, 30, 8.75, 30, 1, 8.867, 30, 8.983, -30, 9.1, -30, 1, 9.217, -30, 9.333, 30, 9.45, 30, 1, 9.567, 30, 9.683, 30, 9.8, 30, 1, 9.917, 30, 10.033, -30, 10.15, -30, 1, 10.267, -30, 10.383, 30, 10.5, 30, 1, 10.617, 30, 10.733, -30, 10.85, -30, 1, 10.967, -30, 11.083, 30, 11.2, 30, 1, 11.317, 30, 11.433, -30, 11.55, -30, 1, 11.622, -30, 11.694, -4.054, 11.767, -4.054, 1, 11.844, -4.054, 11.922, -30, 12, -30]}, {"Target": "Parameter", "Id": "Param121", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param122", "Segments": [0, 10, 1, 0.667, 10, 1.333, -8, 2, -8, 1, 2.667, -8, 3.333, 10, 4, 10, 1, 4.667, 10, 5.333, -8, 6, -8, 1, 6.667, -8, 7.333, 10, 8, 10, 1, 8.667, 10, 9.333, -8, 10, -8, 1, 10.667, -8, 11.333, 10, 12, 10]}, {"Target": "Parameter", "Id": "Param123", "Segments": [0, 11.64, 1, 0.139, 11.64, 0.278, 15, 0.417, 15, 1, 1.083, 15, 1.75, -15, 2.417, -15, 1, 3.083, -15, 3.75, 15, 4.417, 15, 1, 5.083, 15, 5.75, -15, 6.417, -15, 1, 7.083, -15, 7.75, 15, 8.417, 15, 1, 9.083, 15, 9.75, -15, 10.417, -15, 1, 10.944, -15, 11.472, 3.802, 12, 11.636]}, {"Target": "Parameter", "Id": "Param124", "Segments": [0, 3.71, 1, 0.278, 3.71, 0.556, 15, 0.833, 15, 1, 1.5, 15, 2.167, -15, 2.833, -15, 1, 3.5, -15, 4.167, 15, 4.833, 15, 1, 5.5, 15, 6.167, -15, 6.833, -15, 1, 7.5, -15, 8.167, 15, 8.833, 15, 1, 9.5, 15, 10.167, -15, 10.833, -15, 1, 11.222, -15, 11.611, -4.792, 12, 3.715]}, {"Target": "Parameter", "Id": "Param125", "Segments": [0, -5.51, 1, 0.417, -5.51, 0.833, 15, 1.25, 15, 1, 1.917, 15, 2.583, -15, 3.25, -15, 1, 3.917, -15, 4.583, 15, 5.25, 15, 1, 5.917, 15, 6.583, -15, 7.25, -15, 1, 7.917, -15, 8.583, 15, 9.25, 15, 1, 9.917, 15, 10.583, -15, 11.25, -15, 1, 11.5, -15, 11.75, -10.781, 12, -5.508]}, {"Target": "Parameter", "Id": "Param126", "Segments": [0, -12.78, 1, 0.556, -12.78, 1.111, 15, 1.667, 15, 1, 2.333, 15, 3, -15, 3.667, -15, 1, 4.333, -15, 5, 15, 5.667, 15, 1, 6.333, 15, 7, -15, 7.667, -15, 1, 8.333, -15, 9, 15, 9.667, 15, 1, 10.333, 15, 11, -15, 11.667, -15, 1, 11.778, -15, 11.889, -12.78, 12, -12.78]}, {"Target": "Parameter", "Id": "Param127", "Segments": [0, -14.85, 1, 0.694, -14.85, 1.389, 15, 2.083, 15, 1, 2.75, 15, 3.417, -15, 4.083, -15, 1, 4.75, -15, 5.417, 15, 6.083, 15, 1, 6.75, 15, 7.417, -15, 8.083, -15, 1, 8.75, -15, 9.417, 15, 10.083, 15, 1, 10.722, 15, 11.361, 0.055, 12, -14.85]}, {"Target": "Parameter", "Id": "Param141", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param179", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param177", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param178", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param175", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param180", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param181", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param182", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param176", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param186", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param184", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param185", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param187", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param156", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param153", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param154", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param155", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param157", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param158", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param159", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param152", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param160", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param161", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param162", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param163", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param164", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param165", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param166", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param174", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param167", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param168", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param169", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param170", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param171", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param172", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param173", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}, {"Target": "Parameter", "Id": "Param183", "Segments": [0, 0, 1, 4, 0, 8, 0, 12, 0]}]}