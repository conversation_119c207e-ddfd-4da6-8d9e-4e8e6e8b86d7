{"Version": 3, "Meta": {"PhysicsSettingCount": 14, "TotalInputCount": 35, "TotalOutputCount": 33, "VertexCount": 74, "EffectiveForces": {"Gravity": {"X": 0, "Y": -1}, "Wind": {"X": 0, "Y": 0}}, "PhysicsDictionary": [{"Id": "PhysicsSetting1", "Name": "Dummy1"}, {"Id": "PhysicsSetting2", "Name": "Dummy2"}, {"Id": "PhysicsSetting3", "Name": "Dummy3"}, {"Id": "PhysicsSetting4", "Name": "Dummy4"}, {"Id": "PhysicsSetting5", "Name": "Dummy5"}, {"Id": "PhysicsSetting6", "Name": "Dummy6"}, {"Id": "PhysicsSetting7", "Name": "Dummy7"}, {"Id": "PhysicsSetting8", "Name": "Dummy8"}, {"Id": "PhysicsSetting9", "Name": "Dummy9"}, {"Id": "PhysicsSetting10", "Name": "Dummy10"}, {"Id": "PhysicsSetting11", "Name": "Dummy11"}, {"Id": "PhysicsSetting12", "Name": "Dummy12"}, {"Id": "PhysicsSetting13", "Name": "Dummy13"}, {"Id": "PhysicsSetting14", "Name": "Dummy14"}]}, "PhysicsSettings": [{"Id": "PhysicsSetting1", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Physics_AngleX"}, "VertexIndex": 1, "Scale": 31, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 8}, "Mobility": 0.85, "Delay": 0.7, "Acceleration": 2, "Radius": 8}, {"Position": {"X": 0, "Y": 13}, "Mobility": 0.9, "Delay": 0.7, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -20, "Default": 0, "Maximum": 20}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting2", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Physics_AngleY"}, "VertexIndex": 1, "Scale": 33, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 8}, "Mobility": 0.8, "Delay": 0.7, "Acceleration": 2, "Radius": 8}, {"Position": {"X": 0, "Y": 18}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -20, "Default": 0, "Maximum": 20}, "Angle": {"Minimum": -60, "Default": 0, "Maximum": 60}}}, {"Id": "PhysicsSetting3", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 25, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Physics_AngleZ"}, "VertexIndex": 1, "Scale": 40, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 8}, "Mobility": 0.84, "Delay": 0.7, "Acceleration": 2, "Radius": 8}, {"Position": {"X": 0, "Y": 18}, "Mobility": 0.95, "Delay": 1, "Acceleration": 1, "Radius": 10}], "Normalization": {"Position": {"Minimum": -20, "Default": 0, "Maximum": 20}, "Angle": {"Minimum": -90, "Default": 0, "Maximum": 90}}}, {"Id": "PhysicsSetting4", "Input": [{"Source": {"Target": "Parameter", "Id": "physics_Add_Y"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 15, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "Change_idle_physics12"}, "Weight": 5, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ZD_BustMotionY_1"}, "VertexIndex": 1, "Scale": 200, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ZD_BustMotionY_2"}, "VertexIndex": 2, "Scale": 200, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.98, "Delay": 0.8, "Acceleration": 1.5, "Radius": 15}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting5", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation32"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation40"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation42"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation43"}, "VertexIndex": 4, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation44"}, "VertexIndex": 5, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation45"}, "VertexIndex": 6, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation46"}, "VertexIndex": 7, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.98, "Delay": 0.8, "Acceleration": 1.5, "Radius": 15}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 55}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting6", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 50, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Jewelry_1"}, "VertexIndex": 1, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Jewelry_2"}, "VertexIndex": 2, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Jewelry_3"}, "VertexIndex": 3, "Scale": 10, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.98, "Delay": 0.8, "Acceleration": 1.5, "Radius": 15}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 55}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting7", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 25, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 25, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyStretch2"}, "Weight": 50, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Physics_Joint_5"}, "VertexIndex": 1, "Scale": 5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Physics_Joint_4"}, "VertexIndex": 2, "Scale": 5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.98, "Delay": 0.8, "Acceleration": 1.5, "Radius": 15}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 40}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 45}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 50}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 55}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting8", "Input": [{"Source": {"Target": "Parameter", "Id": "physics_Add_X"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 10, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 15, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ZD_BustMotionX_L"}, "VertexIndex": 2, "Scale": 70, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.98, "Delay": 0.9, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting9", "Input": [{"Source": {"Target": "Parameter", "Id": "physics_Add_X"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 10, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 15, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamBodyAngleZ"}, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ZD_BustMotionX_R"}, "VertexIndex": 2, "Scale": 70, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.98, "Delay": 0.9, "Acceleration": 1, "Radius": 15}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.9, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting10", "Input": [{"Source": {"Target": "Parameter", "Id": "physics_Add_X"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 10, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamNeckZ"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "L_ParamHair_2"}, "VertexIndex": 2, "Scale": 400, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "L_ParamHair_1"}, "VertexIndex": 1, "Scale": 400, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1.5, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1.5, "Radius": 10}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1.5, "Radius": 5}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1.5, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1.5, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting11", "Input": [{"Source": {"Target": "Parameter", "Id": "<PERSON><PERSON>pian<PERSON><PERSON><PERSON>"}, "Weight": 40, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh653"}, "VertexIndex": 1, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh653"}, "VertexIndex": 2, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh653"}, "VertexIndex": 3, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh653"}, "VertexIndex": 4, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh653"}, "VertexIndex": 5, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh654"}, "VertexIndex": 6, "Scale": 30, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1.5, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1.5, "Radius": 10}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1.5, "Radius": 5}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1.5, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1.5, "Radius": 5}, {"Position": {"X": 0, "Y": 30}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1.5, "Radius": 5}, {"Position": {"X": 0, "Y": 35}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1.5, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting12", "Input": [{"Source": {"Target": "Parameter", "Id": "physics_Add_X"}, "Weight": 20, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleY"}, "Weight": 10, "Type": "X", "Reflect": true}, {"Source": {"Target": "Parameter", "Id": "ParamAngleX"}, "Weight": 40, "Type": "X", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamAngleZ"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Source": {"Target": "Parameter", "Id": "ParamNeckZ"}, "Weight": 50, "Type": "<PERSON><PERSON>", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "R_ParamHair_2"}, "VertexIndex": 2, "Scale": 400, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "R_ParamHair_1"}, "VertexIndex": 1, "Scale": 400, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 10}, {"Position": {"X": 0, "Y": 15}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 20}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 25}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting13", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeLOpen"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyeLsize"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamEyeLGG"}, "VertexIndex": 2, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}, {"Id": "PhysicsSetting14", "Input": [{"Source": {"Target": "Parameter", "Id": "ParamEyeROpen"}, "Weight": 100, "Type": "X", "Reflect": false}], "Output": [{"Destination": {"Target": "Parameter", "Id": "ParamEyeRsize"}, "VertexIndex": 1, "Scale": 1, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}, {"Destination": {"Target": "Parameter", "Id": "ParamEyeRGG"}, "VertexIndex": 2, "Scale": 0.5, "Weight": 100, "Type": "<PERSON><PERSON>", "Reflect": false}], "Vertices": [{"Position": {"X": 0, "Y": 0}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 5}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}, {"Position": {"X": 0, "Y": 10}, "Mobility": 0.9, "Delay": 0.8, "Acceleration": 1, "Radius": 5}], "Normalization": {"Position": {"Minimum": -10, "Default": 0, "Maximum": 10}, "Angle": {"Minimum": -10, "Default": 0, "Maximum": 10}}}]}