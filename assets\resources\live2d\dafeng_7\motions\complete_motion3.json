{"Version": 3, "Meta": {"Duration": 10.75, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 303, "TotalSegmentCount": 1730, "TotalPointCount": 1858, "UserDataCount": 2, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.583, -0.5, 0, 1.3, 1, 1, 1.339, 1, 1.378, 0.907, 1.417, 0.7, 1, 1.428, 0.641, 1.439, 0.543, 1.45, 0.5, 1, 1.539, 0.152, 1.628, 0, 1.717, 0, 1, 1.956, 0, 2.194, 0.34, 2.433, 0.5, 1, 3.033, 0.902, 3.633, 1, 4.233, 1, 0, 4.7, -0.2, 0, 5.133, 1, 1, 5.228, 1, 5.322, 0.895, 5.417, 0.7, 1, 5.678, 0.162, 5.939, -0.1, 6.2, -0.1, 0, 7.583, 0.9, 0, 7.983, 0.3, 0, 8.483, 0.5, 0, 9.933, 0, 2, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY_SD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.367, 1, 0, 0.483, 0, 0, 0.667, 1, 0, 0.783, 0, 0, 1.017, 1, 0, 1.183, 0, 0, 1.367, 1, 0, 1.417, 0.2, 0, 1.45, 0.25, 0, 1.483, 0, 0, 1.667, 1, 0, 1.783, 0, 0, 1.967, 1, 0, 2.1, 0, 0, 2.25, 1, 0, 2.667, 0, 2, 4.717, 0, 0, 4.833, 1, 0, 4.95, 0, 0, 5.133, 0.3, 0, 5.25, 0, 0, 5.483, 1, 0, 5.65, 0, 0, 5.833, 1, 0, 5.95, 0, 0, 6.133, 1, 0, 6.25, 0, 0, 6.433, 1, 0, 6.583, 0, 2, 6.967, 0, 0, 7.083, 1, 0, 7.2, 0, 0, 7.383, 1, 0, 7.5, 0, 0, 7.733, 1, 0, 7.9, 0, 0, 8.083, 1, 0, 8.2, 0, 0, 8.383, 1, 0, 8.5, 0, 0, 8.683, 1, 0, 8.817, 0, 0, 8.967, 1, 0, 9.133, 0, 2, 9.933, 0, 2, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.083, 0, 2, 1.267, 0, 1, 1.311, 0, 1.356, 0.937, 1.4, 6, 1, 1.417, 7.899, 1.433, 30, 1.45, 30, 0, 1.65, 0, 2, 4.783, 0, 0, 5.35, 30, 0, 5.45, 0, 2, 5.717, 0, 2, 8.05, 0, 0, 8.25, 30, 1, 8.261, 30, 8.272, 3.377, 8.283, 3, 1, 8.35, 0.736, 8.416, 0, 8.483, 0, 2, 8.917, 0, 2, 9.933, 0, 2, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 0.683, 1, 0, 0.817, 0, 0, 1.017, 1, 0, 1.083, 0.9, 0, 2.667, 0.909, 0, 2.917, 0, 2, 5.5, 0, 0, 5.983, 1, 2, 6.533, 1, 0, 6.75, 0, 0, 6.933, 1, 2, 7.267, 1, 0, 7.483, 0, 0, 7.667, 1, 2, 7.967, 1, 0, 8.2, 0, 0, 8.317, 1.3, 0, 8.467, 1, 2, 9.117, 1, 0, 9.267, 0, 0, 9.517, 1, 2, 10.467, 1, 2, 10.75, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.4, 0, 0, 2.667, 1, 2, 4.333, 1, 2, 4.467, 1, 0, 4.933, 0, 2, 9.933, 0, 2, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 0.683, 1, 0, 0.817, 0, 0, 1.017, 1, 0, 1.083, 0.9, 0, 2.667, 0.909, 0, 2.917, 0, 2, 5.5, 0, 0, 5.983, 1, 2, 6.533, 1, 0, 6.75, 0, 0, 6.933, 1, 2, 7.267, 1, 0, 7.483, 0, 0, 7.667, 1, 2, 7.967, 1, 0, 8.2, 0, 0, 8.317, 1.3, 0, 8.467, 1, 2, 9.117, 1, 0, 9.267, 0, 0, 9.517, 1, 2, 10.467, 1, 2, 10.75, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.4, 0, 0, 2.667, 1, 2, 4.333, 1, 2, 4.467, 1, 0, 4.933, 0, 2, 9.933, 0, 2, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 6.533, 0, 1, 6.661, 0, 6.789, -0.377, 6.917, -0.5, 1, 7.034, -0.613, 7.15, -0.6, 7.267, -0.6, 1, 7.406, -0.6, 7.544, 0.371, 7.683, 0.5, 1, 7.816, 0.624, 7.95, 0.6, 8.083, 0.6, 0, 8.317, 0, 2, 9.933, 0, 2, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 6.533, 0, 1, 6.661, 0, 6.789, -0.209, 6.917, -0.4, 1, 7.034, -0.575, 7.15, -0.6, 7.267, -0.6, 0, 7.683, 0.2, 0, 8.083, 0, 2, 8.317, 0, 2, 9.933, 0, 2, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "EyeFrame_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.75, 1, 2, 2.483, 1, 0, 3.2, 0, 2, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.317, 1, 2, 2.817, 1, 0, 3.633, -0.8, 0, 4.85, 0, 1, 5.606, 0, 6.361, -0.384, 7.117, -0.7, 1, 7.772, -0.974, 8.428, -1, 9.083, -1, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamBrowForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.683, 0, 0, 1.083, 1, 2, 2.683, 1, 0, 3.283, 0, 2, 4.467, 0, 0, 4.933, 1, 2, 6.867, 1, 2, 8.183, 1, 0, 8.383, 0, 2, 8.55, 0, 2, 9.933, 0, 2, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamBrowAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.683, 0, 0, 1.083, -1, 2, 2.683, -1, 0, 3.283, 1, 2, 4.467, 1, 0, 4.933, 0, 2, 6.867, 0, 2, 8.183, 0, 2, 9.933, 0, 2, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamEyeLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.683, 0, 0, 0.75, 1, 2, 0.95, 1, 0, 0.967, -1, 2, 1.05, -1, 0, 1.067, 1, 2, 1.367, 1, 0, 1.6, -0.4, 0, 1.917, 0.108, 0, 2.217, -0.032, 0, 2.533, 0.008, 0, 2.667, 0.003, 0, 2.8, 1, 2, 2.883, 1, 0, 3.133, -0.377, 0, 3.433, 0.104, 0, 3.75, -0.029, 0, 4.05, 0.008, 2, 4.067, 0.008, 0, 4.367, -0.002, 2, 4.383, -0.002, 0, 4.633, 0.001, 2, 4.717, 0.001, 0, 4.733, 0, 2, 4.75, 0, 2, 4.783, 0, 2, 4.8, 0, 2, 4.833, 0, 2, 4.85, 0, 2, 4.867, 0, 2, 4.883, 0, 2, 4.9, 0, 2, 4.933, 0, 2, 4.95, 0, 2, 5.033, 0, 2, 5.05, 0, 2, 5.133, 0, 2, 5.15, 0, 2, 5.283, 0, 2, 5.3, 0, 2, 5.317, 0, 2, 5.5, 0, 0, 5.75, -0.742, 0, 6.083, 0.378, 0, 6.4, -0.104, 0, 6.65, 1, 2, 6.767, 1, 0, 6.883, -1, 2, 6.967, -1, 0, 7.233, 0.298, 0, 7.267, 0.287, 0, 7.367, 1, 2, 7.483, 1, 0, 7.6, -1, 2, 7.75, -1, 0, 8.067, 1, 2, 8.183, 1, 0, 8.267, -1, 2, 8.383, -1, 0, 8.4, 1, 2, 8.55, 1, 0, 8.783, -0.407, 0, 9.083, 0.112, 0, 9.117, 0.108, 0, 9.183, 1, 2, 9.433, 1, 0, 9.45, -1, 2, 9.533, -1, 0, 9.55, 1, 2, 9.85, 1, 0, 10.117, -0.4, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamEyeRsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.683, 0, 0, 0.75, 1, 2, 0.95, 1, 0, 0.967, -1, 2, 1.05, -1, 0, 1.067, 1, 2, 1.367, 1, 0, 1.6, -0.4, 0, 1.917, 0.108, 0, 2.217, -0.032, 0, 2.533, 0.008, 0, 2.667, 0.003, 0, 2.8, 1, 2, 2.883, 1, 0, 3.133, -0.377, 0, 3.433, 0.104, 0, 3.75, -0.029, 0, 4.05, 0.008, 2, 4.067, 0.008, 0, 4.367, -0.002, 2, 4.383, -0.002, 0, 4.633, 0.001, 2, 4.717, 0.001, 0, 4.733, 0, 2, 4.75, 0, 2, 4.783, 0, 2, 4.8, 0, 2, 4.833, 0, 2, 4.85, 0, 2, 4.867, 0, 2, 4.883, 0, 2, 4.9, 0, 2, 4.933, 0, 2, 4.95, 0, 2, 5.033, 0, 2, 5.05, 0, 2, 5.133, 0, 2, 5.15, 0, 2, 5.283, 0, 2, 5.3, 0, 2, 5.317, 0, 2, 5.5, 0, 0, 5.75, -0.742, 0, 6.083, 0.378, 0, 6.4, -0.104, 0, 6.65, 1, 2, 6.767, 1, 0, 6.883, -1, 2, 6.967, -1, 0, 7.233, 0.298, 0, 7.267, 0.287, 0, 7.367, 1, 2, 7.483, 1, 0, 7.6, -1, 2, 7.75, -1, 0, 8.067, 1, 2, 8.183, 1, 0, 8.267, -1, 2, 8.383, -1, 0, 8.4, 1, 2, 8.55, 1, 0, 8.783, -0.407, 0, 9.083, 0.112, 0, 9.117, 0.108, 0, 9.183, 1, 2, 9.433, 1, 0, 9.45, -1, 2, 9.533, -1, 0, 9.55, 1, 2, 9.85, 1, 0, 10.117, -0.4, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamEyeLGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.683, 0, 0, 0.75, -0.305, 0, 0.8, -0.2, 0, 0.917, -1, 2, 0.95, -1, 0, 0.967, 1, 2, 1.083, 1, 0, 1.1, -1, 2, 1.317, -1, 0, 1.533, 0.391, 0, 1.8, -0.217, 0, 2.083, 0.104, 0, 2.383, -0.043, 0, 2.667, 0.015, 0, 2.783, -0.26, 0, 3, 0.552, 0, 3.3, -0.291, 0, 3.6, 0.126, 0, 3.9, -0.049, 0, 4.217, 0.018, 0, 4.517, -0.006, 2, 4.533, -0.006, 0, 4.817, 0.002, 2, 4.833, 0.002, 0, 5.067, 0, 2, 5.083, 0, 0, 5.1, -0.001, 2, 5.183, -0.001, 0, 5.217, 0, 2, 5.233, 0, 2, 5.267, 0, 2, 5.283, 0, 2, 5.317, 0, 2, 5.333, 0, 2, 5.35, 0, 2, 5.367, 0, 2, 5.383, 0, 2, 5.5, 0, 0, 5.667, 0.166, 0, 5.983, -0.297, 0, 6.25, 0.239, 0, 6.617, -0.342, 0, 6.817, 1, 2, 6.967, 1, 0, 7.217, -0.313, 0, 7.283, -0.229, 0, 7.333, -0.257, 0, 7.55, 1, 2, 7.633, 1, 0, 7.95, -0.322, 0, 7.983, -0.307, 0, 8.017, -0.317, 0, 8.233, 1, 2, 8.25, 1, 0, 8.267, -1, 2, 8.367, -1, 0, 8.65, 0.345, 0, 8.933, -0.277, 0, 9.117, 0.016, 0, 9.183, -0.234, 0, 9.25, -0.162, 0, 9.417, -1, 2, 9.45, -1, 0, 9.467, 1, 2, 9.617, 1, 0, 9.633, -1, 2, 9.817, -1, 0, 10.05, 0.396, 0, 10.367, -0.213, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamEyeRGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.683, 0, 0, 0.75, -0.305, 0, 0.8, -0.2, 0, 0.917, -1, 2, 0.95, -1, 0, 0.967, 1, 2, 1.083, 1, 0, 1.1, -1, 2, 1.317, -1, 0, 1.533, 0.391, 0, 1.8, -0.217, 0, 2.083, 0.104, 0, 2.383, -0.043, 0, 2.667, 0.015, 0, 2.783, -0.26, 0, 3, 0.552, 0, 3.3, -0.291, 0, 3.6, 0.126, 0, 3.9, -0.049, 0, 4.217, 0.018, 0, 4.517, -0.006, 2, 4.533, -0.006, 0, 4.817, 0.002, 2, 4.833, 0.002, 0, 5.067, 0, 2, 5.083, 0, 0, 5.1, -0.001, 2, 5.183, -0.001, 0, 5.217, 0, 2, 5.233, 0, 2, 5.267, 0, 2, 5.283, 0, 2, 5.317, 0, 2, 5.333, 0, 2, 5.35, 0, 2, 5.367, 0, 2, 5.383, 0, 2, 5.5, 0, 0, 5.667, 0.166, 0, 5.983, -0.297, 0, 6.25, 0.239, 0, 6.617, -0.342, 0, 6.817, 1, 2, 6.967, 1, 0, 7.217, -0.313, 0, 7.283, -0.229, 0, 7.333, -0.257, 0, 7.55, 1, 2, 7.633, 1, 0, 7.95, -0.322, 0, 7.983, -0.307, 0, 8.017, -0.317, 0, 8.233, 1, 2, 8.25, 1, 0, 8.267, -1, 2, 8.367, -1, 0, 8.65, 0.345, 0, 8.933, -0.277, 0, 9.117, 0.016, 0, 9.183, -0.234, 0, 9.25, -0.162, 0, 9.417, -1, 2, 9.45, -1, 0, 9.467, 1, 2, 9.617, 1, 0, 9.633, -1, 2, 9.817, -1, 0, 10.05, 0.396, 0, 10.367, -0.213, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "jing<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.717, 0, 0, 1.033, 1, 2, 1.05, 1, 2, 1.083, 1, 2, 1.1, 1, 2, 1.117, 0, 2, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.417, 6.36, 0, 0.917, 0, 2, 2.717, 0, 0, 3, 5.107, 0, 4.083, 0.6, 2, 9.117, 0.6, 1, 9.389, 0.4, 9.661, 0.2, 9.933, 0, 2, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 4.083, 7.5, 2, 9.117, 7.5, 1, 9.389, 5, 9.661, 2.5, 9.933, 0, 2, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 30, 2, 0.25, 30, 0, 0.833, 7.5, 2, 2.683, 7.5, 0, 3.117, 8.691, 1, 3.195, 8.691, 3.272, 8.734, 3.35, 7.57, 1, 3.461, 5.907, 3.572, 0.993, 3.683, -0.716, 1, 3.816, -2.767, 3.95, -3.06, 4.083, -3.06, 2, 9.117, -3.06, 1, 9.222, -3.06, 9.328, -0.924, 9.433, 7.393, 1, 9.6, 20.525, 9.766, 30, 9.933, 30, 2, 10.467, 30, 2, 10.75, 30]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 4.083, -8.9, 2, 9.117, -8.9, 0, 9.65, -30, 0, 9.933, 0, 2, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1, 2, 0.25, -1, 2, 2.917, -1, 0, 2.933, 1, 2, 9.483, 1, 3, 9.933, -1, 2, 10.467, -1, 2, 10.75, -1]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.5, -0.322, 0, 0.883, 0.1, 0, 2.633, 0.047, 0, 2.917, 0.295, 0, 3.45, -0.218, 0, 4.083, 0, 2, 9.117, 0, 2, 9.933, 0, 2, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamHandLDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10, 2, 0.25, 10, 2, 0.667, 10, 2, 0.683, 3, 2, 3.433, 6, 2, 4.083, 6, 2, 9.533, 6, 3, 9.933, 10, 2, 10.467, 10, 2, 10.75, 10]}, {"Target": "Parameter", "Id": "XIONGJIYA2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.567, 0, 0, 2.733, 1, 2, 9.667, 1, 0, 9.933, 0, 2, 10.033, 0, 2, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.717, 0, 0, 2.95, -4.08, 0, 4.083, 15.18, 2, 9.117, 15.18, 0, 9.567, 0, 2, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.6, 0.7, 2, 0.75, 0.7, 0, 1.083, 0, 2, 2.733, 0, 0, 2.867, 1, 2, 3.083, 1, 2, 4.1, 1, 0, 4.217, 0, 2, 9.133, 0, 1, 9.211, 0, 9.289, 0.706, 9.367, 0.8, 1, 9.522, 0.988, 9.678, 1, 9.833, 1, 0, 10.05, 0, 2, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.717, 0, 0, 3.083, -2.884, 0, 4.083, 4.08, 2, 9.117, 4.08, 0, 9.733, -2.111, 0, 10.017, 0, 2, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.95, 0, 0, 4.083, -12.8, 2, 9.117, -12.8, 0, 9.567, 0, 2, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.85, 0, 2, 3.283, 0, 0, 4.083, -0.03, 2, 9.117, -0.03, 1, 9.267, -0.03, 9.417, -0.027, 9.567, 0, 1, 9.628, 0.011, 9.689, 0.136, 9.75, 0.136, 0, 10.15, 0, 2, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.567, 0, 2, 2.917, 0, 0, 2.933, 1, 2, 4.083, 1, 2, 9.883, 1, 2, 9.917, 1, 2, 9.983, 1, 1, 9.994, 0.667, 10.006, 0.333, 10.017, 0, 2, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 5.533, 0.001, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 6.25, 0, 2, 6.517, 0, 0, 6.917, -7, 2, 7.183, -7, 0, 7.617, 6, 2, 7.95, 6, 0, 8.433, 0, 2, 9.683, 0, 2, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -2.728, 0, 0.617, 5.594, 0, 1.133, -8, 2, 1.8, -8, 0, 2.433, -6, 0, 2.817, -14, 0, 3.25, 0, 0, 4.2, -5, 0, 4.6, 4.282, 0, 4.85, -14.746, 0, 5.05, 7, 0, 5.3, -15.62, 0, 5.767, 0, 2, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.533, -10, 1, 0.716, -10, 0.9, 6.373, 1.083, 7.359, 1, 1.344, 8.764, 1.606, 8.654, 1.867, 8.654, 1, 2.1, 8.654, 2.334, 8.517, 2.567, 7, 1, 2.722, 5.989, 2.878, 0.466, 3.033, 0, 1, 3.344, -0.932, 3.656, -1, 3.967, -1, 0, 5.1, 0, 0, 6.467, -0.96, 0, 8.8, 0, 2, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -7.795, 2, 0.25, -7.795, 1, 0.75, -7.795, 1.25, -5.197, 1.75, 0, 1, 2.25, 5.197, 2.75, 7.795, 3.25, 7.795, 1, 3.75, 7.795, 4.25, 5.197, 4.75, 0, 1, 5.25, -5.197, 5.75, -7.795, 6.25, -7.795, 1, 6.75, -7.795, 7.25, -5.168, 7.75, 0, 1, 8.256, 5.225, 8.761, 7.795, 9.267, 7.795, 0, 10.467, -7.795, 2, 10.75, -7.795]}, {"Target": "Parameter", "Id": "ParamBodyStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 0.294, 0, 0.339, 4.872, 0.383, 6.338, 1, 0.478, 9.454, 0.572, 10, 0.667, 10, 0, 1.083, -3, 0, 1.217, 1.044, 1, 1.284, 1.044, 1.35, 0.677, 1.417, 0, 1, 1.545, -1.298, 1.672, -1.92, 1.8, -1.92, 0, 2.4, 2.409, 0, 2.683, 0, 0, 3.033, 10, 1, 3.161, 10, 3.289, 10, 3.417, 7.281, 1, 3.578, 3.273, 3.739, -10, 3.9, -10, 0, 4.233, -3, 0, 4.5, -7, 2, 4.65, -7, 0, 4.867, -9, 0, 5.033, -4.543, 1, 5.094, -4.543, 5.156, -5.258, 5.217, -6, 1, 5.284, -6.81, 5.35, -7, 5.417, -7, 1, 5.472, -7, 5.528, -2.742, 5.583, -2, 1, 5.728, -0.072, 5.872, 0.25, 6.017, 0.25, 0, 6.633, -5.184, 0, 7.05, 2.395, 0, 7.183, 1.889, 0, 7.35, 2.493, 0, 7.683, 0.094, 0, 7.833, 0.616, 0, 8.217, -4, 0, 8.95, 0, 2, 9.117, 0, 0, 9.633, 10, 0, 10.05, -2, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.517, 4.859, 0, 0.883, -1.073, 0, 1.483, 2.445, 0, 2.017, -0.586, 0, 2.533, 0.603, 0, 4.967, -7, 0, 6.467, 6, 0, 8, -4, 0, 9.667, 10, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamLegLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 2.75, 14.515, 0, 4, -9.742, 1, 4.406, -9.742, 4.811, -5.146, 5.217, 0, 1, 6.056, 10.643, 6.894, 14.515, 7.733, 14.515, 0, 8.533, 2.532, 0, 9.583, 27.852, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamLegRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.55, 7.732, 0, 4.05, -9.756, 1, 4.439, -9.756, 4.828, -5.355, 5.217, 0, 1, 5.65, 5.967, 6.084, 7.732, 6.517, 7.732, 0, 9.033, -9.756, 0, 9.667, 30, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.75, 0, 0, 3.417, 1, 2, 10.033, 1, 1, 10.044, 1, 10.056, 0.53, 10.067, 0.5, 1, 10.2, 0.142, 10.334, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "physics_Add_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.65, 0, 0, 0.783, 8, 0, 0.983, -9, 1, 3.828, -9, 6.672, -6.102, 9.517, 0, 1, 9.567, 0.107, 9.617, 8, 9.667, 8, 0, 9.917, -9, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "Physics_AngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 6.65, 0, 0, 6.983, -4.67, 0, 7.65, 4.657, 0, 8.467, -0.366, 0, 8.767, 0.261, 0, 8.933, 0, 2, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "Physics_AngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.317, 0, 0, 0.433, -0.923, 0, 0.683, 3.649, 0, 1.183, -5.348, 0, 1.567, -4.571, 0, 1.817, -4.604, 0, 2.433, -3.381, 0, 2.85, -8.554, 0, 3.3, 0.917, 0, 4.2, -2.98, 0, 4.617, 2.972, 0, 4.883, -9.156, 0, 5.117, 4.119, 0, 5.383, -10.204, 0, 5.817, 0.955, 0, 6.167, -0.243, 0, 6.333, 0, 2, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "Physics_AngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 6.517, 0, 0, 6.783, 3.731, 0, 7.117, -0.601, 0, 7.2, -0.46, 0, 7.467, -3.736, 0, 7.8, 0.7, 0, 7.983, 0.211, 0, 8.267, 1.532, 0, 8.583, -0.504, 0, 8.783, 0, 2, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 1.819, 0, 0.617, -3.73, 0, 0.65, -3.622, 0, 0.767, -8.556, 0, 0.967, 13.378, 0, 1.35, -10.091, 0, 1.767, 6.408, 0, 2.183, -4.399, 0, 2.65, 5.028, 0, 3.067, -8.08, 0, 3.633, 8.425, 0, 4.05, -5.393, 0, 4.783, 9.541, 0, 5.033, -16.509, 0, 5.3, 12.436, 0, 5.667, -10.169, 0, 6.067, 7.313, 0, 6.467, -5.122, 0, 6.883, 3.211, 0, 7.3, -2.097, 0, 7.717, 1.278, 0, 8.133, -0.873, 0, 8.55, 0.493, 0, 8.967, -0.38, 0, 9.517, 0.84, 0, 9.65, -5.002, 0, 9.883, 11.761, 0, 10.317, -10.853, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -1.819, 0, 0.617, 3.73, 0, 0.65, 3.622, 0, 0.75, 8.235, 0, 0.95, -21.795, 0, 1.183, 30, 2, 1.25, 30, 0, 1.567, -23.443, 0, 1.967, 14.324, 0, 2.383, -9.751, 0, 2.867, 12.709, 0, 3.267, -20.431, 0, 3.533, 2.29, 0, 3.667, -2.825, 0, 3.933, 13.012, 0, 4.267, -10.873, 0, 4.6, -1.036, 0, 4.717, -3.415, 0, 4.967, 28.023, 0, 5.183, -30, 2, 5.283, -30, 0, 5.533, 29.894, 0, 5.867, -26.518, 0, 6.267, 18.273, 0, 6.667, -11.479, 0, 7.067, 6.982, 0, 7.5, -4.558, 0, 7.917, 2.566, 0, 8.333, -1.996, 0, 8.75, 0.893, 0, 9.167, -0.968, 0, 9.333, -0.666, 0, 9.367, -1.351, 0, 9.633, 3.859, 0, 9.833, -13.822, 0, 10.15, 25.586, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_L", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -0.637, 0, 0.617, 1.305, 0, 1.133, -1.867, 2, 1.8, -1.867, 0, 2.433, -1.4, 0, 2.817, -3.268, 0, 3.25, 0, 0, 4.2, -1.167, 0, 4.6, 0.999, 0, 4.85, -3.442, 0, 5.05, 1.633, 0, 5.267, -3.391, 0, 5.5, 5.978, 0, 5.85, -5.292, 0, 6.3, 2.827, 0, 6.733, -1.863, 0, 7.15, 1.856, 0, 7.65, -2.56, 0, 8.05, 1.697, 0, 8.417, -0.288, 0, 8.467, -0.279, 0, 8.65, -0.585, 0, 9.15, 0.604, 0, 9.633, -0.895, 0, 10.167, 0.719, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_R", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 0.256, 0, 0.261, 0.536, 0.267, 0.557, 1, 0.289, 0.639, 0.311, 0.657, 0.333, 0.657, 0, 0.533, -2.32, 0, 0.817, 2.834, 0, 1.183, -3.493, 0, 1.6, 2.118, 0, 2.05, -1.305, 0, 2.55, 1.204, 0, 2.933, -2.737, 0, 3.3, 4.243, 0, 3.733, -2.754, 0, 4.2, 1.444, 0, 4.4, 0.202, 0, 4.717, 2.183, 0, 4.983, -8.596, 0, 5.233, 8.725, 0, 5.517, -5.734, 0, 5.883, 4.174, 0, 6.333, -2.119, 0, 6.9, 1.754, 0, 7.283, -1.121, 0, 7.433, -0.879, 0, 7.633, -1.192, 0, 7.983, 1.098, 0, 8.267, -0.125, 0, 8.45, 0.407, 0, 8.8, -0.921, 0, 9.133, 0.601, 0, 9.15, 0.6, 0, 9.217, 0.646, 0, 9.783, -0.859, 0, 10.267, 0.721, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 3.638, 0, 0.617, -7.459, 0, 1.133, 10.668, 2, 1.8, 10.668, 0, 2.433, 8, 0, 2.733, 16.967, 0, 3.083, -11.352, 0, 3.433, 4.572, 0, 3.75, -1.483, 0, 4.067, 0.835, 0, 4.467, -5.121, 0, 4.8, 17.023, 0, 5.033, -23.294, 0, 5.267, 21.3, 0, 5.583, -13.449, 0, 5.933, 3.315, 0, 6.15, 0, 2, 6.517, 0, 0, 6.783, 21.428, 0, 7.1, -7.873, 0, 7.2, -5.846, 0, 7.467, -23.337, 0, 7.783, 10.682, 0, 8.017, 1.371, 0, 8.267, 8.875, 0, 8.6, -3.18, 0, 8.933, 2.348, 0, 9.283, -1.734, 0, 9.667, 1.821, 0, 10.05, -1.912, 0, 10.433, 2.007, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -3.638, 0, 0.617, 7.459, 0, 1.133, -10.668, 2, 1.8, -10.668, 0, 2.433, -8, 0, 2.717, -16.874, 0, 2.917, 20.179, 0, 3.2, -11.265, 0, 3.483, 5.23, 0, 3.567, 3.608, 0, 3.633, 4.599, 0, 3.867, -2.678, 0, 4.15, 1.236, 0, 4.217, 0.987, 0, 4.35, 1.611, 0, 4.7, -8.378, 0, 4.95, 22.663, 0, 5.167, -30, 2, 5.183, -30, 0, 5.4, 28.891, 0, 5.667, -14.462, 0, 5.95, 5.687, 0, 6.25, -3.942, 0, 6.483, 2.673, 0, 6.717, -20.127, 0, 6.95, 18.155, 0, 7.2, -10.033, 0, 7.383, 9.409, 0, 7.65, -16.164, 0, 7.883, 11.299, 0, 8.15, -7.77, 0, 8.433, 7.054, 0, 8.7, -3.474, 0, 8.783, -2.376, 0, 8.817, -3.574, 0, 9.033, 2.548, 0, 9.117, 1.762, 0, 9.15, 2.651, 0, 9.417, -1.895, 0, 9.483, -1.508, 0, 9.533, -2.941, 0, 9.783, 2.155, 0, 9.867, 1.541, 0, 9.917, 2.984, 0, 10.167, -2.153, 0, 10.25, -1.646, 0, 10.333, -3.816, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -3.638, 0, 0.617, 7.459, 0, 1.133, -10.668, 2, 1.8, -10.668, 0, 2.433, -8.001, 0, 2.817, -18.673, 0, 3.25, 0, 0, 4.2, -6.667, 0, 4.6, 5.709, 0, 4.85, -19.668, 0, 5.05, 9.334, 0, 5.3, -20.835, 0, 5.767, 0, 2, 6.517, 0, 0, 6.817, 27.716, 0, 7.417, -30, 2, 7.533, -30, 0, 7.867, 10.359, 0, 8.017, 7.47, 0, 8.267, 11.801, 0, 8.65, -5.703, 0, 9.083, 2.891, 0, 9.567, -2.157, 0, 10.05, 2.162, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 3.638, 0, 0.617, -7.459, 0, 1.133, 10.668, 2, 1.8, 10.668, 0, 2.433, 8.001, 0, 2.817, 18.673, 0, 3.25, 0, 0, 4.2, 6.667, 0, 4.6, -5.709, 0, 4.85, 19.668, 0, 5.05, -9.334, 0, 5.3, 20.835, 0, 5.767, 0, 2, 6.517, 0, 0, 6.767, -24.325, 0, 7.017, 27.712, 0, 7.25, -3.497, 0, 7.433, 4.717, 0, 7.683, -20.005, 0, 7.983, 10.887, 0, 8.233, -4.916, 0, 8.533, 9.82, 0, 8.833, -5.787, 0, 8.917, -4.09, 0, 8.967, -5.185, 0, 9.267, 3.075, 0, 9.367, 2.233, 0, 9.433, 3.903, 0, 9.767, -2.321, 0, 9.85, -1.855, 0, 9.917, -4.047, 0, 10.233, 2.466, 0, 10.333, 1.815, 0, 10.383, 3.953, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "Jewelry_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 0.303, 0, 0.6, -0.533, 0, 0.933, 0.876, 0, 1.317, -0.857, 0, 1.733, 0.545, 0, 2.15, -0.402, 0, 2.65, 0.608, 0, 3.05, -1.196, 2, 3.067, -1.196, 0, 3.45, 1.192, 0, 3.867, -0.717, 0, 4.233, 0.347, 0, 4.533, -0.399, 0, 4.8, 1.614, 0, 5.05, -2.386, 0, 5.3, 1.839, 0, 5.667, -1.584, 0, 6.05, 1.18, 0, 6.467, -0.75, 0, 6.833, 0.74, 0, 7.333, -0.679, 2, 7.35, -0.679, 0, 7.767, 0.839, 0, 8.15, -0.332, 0, 8.517, 0.076, 0, 8.917, -0.066, 0, 9.367, 0.044, 2, 9.383, 0.044, 0, 9.85, -0.058, 0, 10.333, 0.077, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "Jewelry_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -0.303, 0, 0.567, 0.426, 0, 0.783, -1.368, 0, 1.15, 2.369, 0, 1.533, -1.836, 0, 1.933, 1.19, 0, 2.35, -0.907, 0, 2.867, 1.637, 0, 3.267, -3.035, 0, 3.667, 2.534, 0, 4.067, -1.434, 0, 4.417, 0.847, 0, 4.717, -1.682, 0, 4.983, 4.386, 0, 5.233, -4.837, 0, 5.517, 4.19, 0, 5.867, -3.999, 0, 6.25, 2.611, 0, 6.667, -1.706, 0, 7.017, 1.811, 0, 7.583, -1.739, 0, 7.983, 1.677, 0, 8.35, -0.66, 0, 8.667, 0.293, 0, 9.1, -0.149, 0, 9.567, 0.09, 0, 9.617, 0.088, 0, 9.683, 0.133, 0, 10.217, -0.204, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "Jewelry_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.533, 0, 0, 0.7, 0.828, 0, 0.967, -1.693, 0, 1.3, 2.157, 0, 1.65, -1.817, 0, 2.05, 1.222, 0, 2.467, -0.814, 0, 3.05, 1.569, 0, 3.417, -2.527, 0, 3.8, 2.369, 0, 4.167, -1.501, 0, 4.55, 1.288, 0, 4.867, -2.623, 0, 5.117, 4.866, 0, 5.383, -5.577, 0, 5.683, 5.221, 0, 6.017, -4.337, 0, 6.367, 2.882, 0, 6.767, -1.915, 0, 7.167, 1.741, 0, 7.767, -0.997, 0, 8.117, 1.349, 0, 8.467, -0.921, 0, 8.8, 0.466, 0, 9.167, -0.195, 0, 9.217, -0.188, 0, 9.267, -0.214, 0, 9.617, 0.104, 0, 9.683, 0.076, 0, 9.883, 0.158, 0, 10.417, -0.156, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.417, -0.862, 0, 0.883, 1.672, 0, 1.233, -2.144, 0, 1.65, 1.377, 0, 2.067, -1.096, 0, 2.533, 0.987, 0, 2.933, -1.681, 0, 3.333, 1.535, 0, 4.033, -1.149, 0, 4.433, 1.418, 0, 5, -1.455, 0, 5.3, 1.513, 0, 5.65, -1.463, 0, 6.05, 1.077, 0, 6.517, -0.576, 0, 7.183, 0.547, 0, 7.517, -0.19, 0, 7.7, -0.049, 0, 7.8, -0.071, 0, 8.1, 0.455, 0, 8.483, -0.584, 0, 8.933, 0.411, 0, 9.433, -0.913, 0, 9.917, 1.881, 0, 10.35, -1.752, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.35, 0.472, 0, 0.65, -2.269, 0, 1.083, 3.869, 0, 1.45, -3.879, 0, 1.85, 2.809, 0, 2.267, -2.332, 0, 2.767, 2.3, 0, 3.133, -3.734, 0, 3.55, 2.84, 0, 4.267, -2.59, 0, 4.617, 2.823, 0, 4.883, 0.018, 0, 4.95, 0.098, 0, 5.183, -3.023, 0, 5.517, 3.367, 0, 5.85, -3.315, 0, 6.267, 2.19, 0, 6.7, -0.889, 0, 6.9, -0.523, 0, 7.1, -0.892, 0, 7.4, 0.986, 0, 7.7, -0.395, 0, 7.867, -0.151, 0, 7.967, -0.201, 0, 8.3, 1.229, 0, 8.7, -1.317, 0, 9.183, 0.867, 0, 9.683, -2.476, 0, 10.133, 4.217, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 0.91, 0, 0.6, -1.6, 0, 0.933, 2.627, 0, 1.317, -2.572, 0, 1.733, 1.635, 0, 2.15, -1.207, 0, 2.65, 1.824, 0, 3.05, -3.588, 0, 3.45, 3.577, 0, 3.867, -2.15, 0, 4.233, 1.041, 0, 4.533, -1.198, 0, 4.8, 4.842, 0, 5.05, -7.159, 0, 5.3, 5.516, 0, 5.667, -4.752, 0, 6.05, 3.541, 0, 6.467, -2.251, 0, 6.833, 2.221, 0, 7.333, -2.037, 2, 7.35, -2.037, 0, 7.767, 2.518, 0, 8.15, -0.996, 0, 8.517, 0.227, 0, 8.917, -0.198, 0, 9.367, 0.132, 0, 9.85, -0.174, 0, 10.333, 0.23, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -0.91, 0, 0.567, 1.277, 0, 0.783, -4.105, 0, 1.15, 7.109, 0, 1.533, -5.507, 0, 1.933, 3.57, 0, 2.35, -2.722, 0, 2.867, 4.91, 0, 3.267, -9.105, 0, 3.667, 7.602, 0, 4.067, -4.303, 0, 4.417, 2.54, 0, 4.717, -5.045, 0, 4.983, 13.158, 0, 5.233, -14.511, 0, 5.517, 12.571, 0, 5.867, -11.997, 0, 6.25, 7.835, 0, 6.667, -5.117, 0, 7.017, 5.433, 0, 7.583, -5.217, 0, 7.983, 5.033, 0, 8.35, -1.98, 0, 8.667, 0.879, 0, 9.1, -0.446, 0, 9.567, 0.272, 0, 9.617, 0.264, 0, 9.683, 0.399, 0, 10.217, -0.613, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.533, 0, 0, 0.7, 2.485, 0, 0.967, -5.079, 0, 1.3, 6.471, 0, 1.65, -5.452, 0, 2.05, 3.665, 0, 2.467, -2.441, 0, 3.05, 4.708, 0, 3.417, -7.58, 0, 3.8, 7.106, 0, 4.167, -4.503, 0, 4.55, 3.866, 0, 4.867, -7.868, 0, 5.117, 14.598, 0, 5.383, -16.732, 0, 5.683, 15.662, 0, 6.017, -13.011, 0, 6.367, 8.646, 0, 6.767, -5.744, 0, 7.167, 5.223, 0, 7.767, -2.99, 0, 8.117, 4.048, 0, 8.467, -2.764, 0, 8.8, 1.397, 0, 9.167, -0.586, 0, 9.217, -0.565, 0, 9.267, -0.641, 0, 9.617, 0.314, 0, 9.683, 0.227, 0, 9.883, 0.475, 0, 10.417, -0.468, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.65, 0, 0, 0.817, 2.416, 0, 1.083, -5.924, 0, 1.417, 8.501, 0, 1.767, -8.056, 0, 2.133, 5.843, 0, 2.533, -3.787, 0, 3.15, 5.134, 0, 3.517, -9.333, 0, 3.9, 9.721, 0, 4.267, -7.139, 0, 4.65, 5.535, 0, 4.967, -8.805, 0, 5.233, 14.96, 0, 5.517, -18.986, 0, 5.8, 20.255, 0, 6.117, -18.679, 0, 6.467, 13.815, 0, 6.85, -9.197, 0, 7.25, 7.446, 0, 7.65, -3.998, 0, 8.233, 4.901, 0, 8.567, -4.13, 0, 8.917, 2.548, 0, 9.267, -1.203, 0, 9.733, 0.506, 0, 9.933, 0.045, 0, 10.117, 0.169, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.467, -0.255, 0, 0.633, 0, 2, 0.75, 0, 0, 0.917, 2.476, 0, 1.2, -6.798, 0, 1.517, 10.833, 0, 1.867, -11.366, 0, 2.233, 9.016, 0, 2.617, -6.034, 0, 3.217, 5.369, 0, 3.617, -11.15, 0, 3.983, 12.936, 0, 4.367, -10.593, 0, 4.733, 8.171, 0, 5.067, -11.229, 0, 5.35, 17.749, 0, 5.633, -22.601, 0, 5.917, 25.33, 0, 6.233, -24.944, 0, 6.583, 20.292, 0, 6.933, -14.435, 2, 6.95, -14.435, 0, 7.333, 10.869, 0, 7.717, -6.608, 0, 8.333, 5.525, 0, 8.683, -5.554, 0, 9.017, 4.096, 0, 9.417, -2.387, 0, 9.85, 1.199, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 0.867, 0, 0.633, -0.517, 0, 1.017, 2.76, 0, 1.3, -7.727, 0, 1.633, 13.423, 0, 1.967, -15.384, 0, 2.333, 13.283, 0, 2.7, -9.488, 0, 3.25, 5.505, 0, 3.717, -12.862, 0, 4.083, 16.567, 0, 4.45, -14.902, 0, 4.833, 12.045, 0, 5.167, -14.416, 0, 5.45, 20.595, 0, 5.733, -26.742, 0, 6.017, 30, 2, 6.033, 30, 0, 6.317, -30, 2, 6.35, -30, 0, 6.683, 27.531, 0, 7.033, -21.183, 0, 7.417, 15.943, 0, 7.8, -10.415, 0, 8.417, 5.765, 0, 8.783, -7.496, 0, 9.133, 6.259, 0, 9.533, -4.158, 0, 9.95, 2.273, 0, 10.35, -0.703, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.517, 1.268, 0, 0.817, -0.946, 0, 1.133, 2.498, 0, 1.417, -8.569, 0, 1.733, 16.228, 0, 2.067, -20.014, 0, 2.433, 18.557, 0, 2.8, -14.287, 0, 3.2, 8.235, 0, 3.8, -14.234, 0, 4.167, 20.27, 0, 4.55, -19.855, 0, 4.917, 17.07, 0, 5.267, -18.073, 0, 5.55, 24.19, 0, 5.817, -30, 2, 5.85, -30, 0, 6.083, 30, 2, 6.167, 30, 0, 6.383, -30, 2, 6.483, -30, 0, 6.733, 30, 2, 6.817, 30, 0, 7.133, -28.631, 0, 7.5, 22.396, 0, 7.9, -15.464, 0, 8.333, 6.219, 0, 8.883, -9.126, 0, 9.25, 8.922, 0, 9.667, -6.712, 0, 10.083, 4.156, 0, 10.467, 0, 2, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.417, 1, 0, 3.25, 0, 0, 4.417, 1, 0, 6.25, 0, 0, 7.417, 1, 0, 9.25, 0, 0, 10.417, 1, 1, 10.528, 1, 10.639, 0.967, 10.75, 0.913]}, {"Target": "Parameter", "Id": "fenshangxuanzhuang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 3.75, 1.401, 7.25, 2.803, 10.75, 4.204]}, {"Target": "Parameter", "Id": "Mail_Transparency6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamSad2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 10.75, 1]}, {"Target": "Parameter", "Id": "ParamCRY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.2, 0, 10.75, -5.2]}, {"Target": "Parameter", "Id": "Position_Coordinates", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 10.75, 1]}, {"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Man_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 10.75, 1]}, {"Target": "Parameter", "Id": "Man_TMD2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "BG_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 10.75, 1]}, {"Target": "Parameter", "Id": "FG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "FG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "BG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "BG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "BG_White4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "BG_White5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "BG_White2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "BG_White3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "All_Size2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "All_Size3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "All_Size4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "tuerzuo3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "faqiehuan", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "faqiehuan2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "faqiehuan4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "faqiehuan3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "tuerzuo4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Mail_Change", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "dafengshangzhuanchang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "tongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamTongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamBrowY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamSigh", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamDarken", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamCry", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_B", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_C", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "MB_Unidirectional_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "huqi1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "huqi2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamShoulderLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamArmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 10.75, 1]}, {"Target": "Parameter", "Id": "ParamForearmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamArmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamArmL_ForearmL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamForearmL_HandL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamHandLSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "xiongjiya", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "xiongjiya5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "xiongjiya3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "xiongjiya4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "xiongjiya2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamShoulderRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamArmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 10.75, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamArmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamArmR_ForearmR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamForearm<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 10.75, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_HandR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamHandRDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamHandRSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 10.75, 0.6]}, {"Target": "Parameter", "Id": "Change_idle_physics23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Param103", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "taiqiugundong", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -22.5, 0, 10.75, -22.5]}, {"Target": "Parameter", "Id": "Change_idle_physics32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamLegL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 10.75, 1]}, {"Target": "Parameter", "Id": "ParamLegLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamCrusLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamCrusLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>oodLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamFoodLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamFoodLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamLegR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 10.75, 1]}, {"Target": "Parameter", "Id": "ParamLegRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamCrusRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamCrusRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamCrus<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>ood<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamFoodRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamFoodRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Physics_Provisional", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "physics_Add_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamBodyStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Physics_Body_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Physics_Body_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Physics_breast", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamBreath2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "touch_drag1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "touch_drag2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "touch_drag3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "touch_drag4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "touch_drag5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "touch_drag6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "touch_drag7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "touch_drag8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "touch_drag9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>pian<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh654", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh655", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh657", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh656", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh658", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh659", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh660", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.75, 0]}], "UserData": [{"Time": 0.25, "Value": ""}, {"Time": 10.25, "Value": ""}]}