{"Version": 3, "Meta": {"Duration": 12.833, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 303, "TotalSegmentCount": 2235, "TotalPointCount": 2633, "UserDataCount": 2, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.36, 0, 9.4, 0, 2, 9.617, 0, 2, 10.117, 0, 2, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.48, 0, 9.4, 0, 2, 9.617, 0, 2, 10.117, 0, 2, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.52, 0, 9.4, 0, 2, 9.617, 0, 2, 10.117, 0, 2, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.252, 0, 0.467, -0.1, 0, 1.617, 0.9, 0, 2.583, 0.241, 0, 3.367, 1, 1, 3.789, 1, 4.211, 0.736, 4.633, 0.187, 1, 4.639, 0.18, 4.644, -0.57, 4.65, -0.57, 0, 5.317, 0.7, 0, 5.75, -0.8, 0, 6.5, 0.6, 0, 6.967, 0.1, 0, 7.25, 1, 0, 7.7, 0.3, 0, 8.117, 1, 0, 8.667, -0.5, 0, 8.917, 1, 1, 9.078, 1, 9.239, 0.604, 9.4, 0.036, 1, 9.472, -0.219, 9.545, -0.435, 9.617, -0.57, 1, 9.639, -0.612, 9.661, -0.6, 9.683, -0.6, 1, 9.828, -0.6, 9.972, -0.475, 10.117, -0.098, 1, 10.378, 0.583, 10.639, 1, 10.9, 1, 0, 11.117, -0.3, 0, 11.333, 1, 1, 11.544, 1, 11.756, 0.95, 11.967, 0.7, 1, 12.028, 0.628, 12.089, -0.1, 12.15, -0.1, 0, 12.433, 0.6, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY_SD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.217, 1, 0, 1.083, 0, 2, 1.267, 0, 0, 1.383, 1, 0, 1.667, 0, 0, 1.85, 1, 0, 1.967, 0, 0, 2.2, 1, 0, 2.367, 0, 0, 2.55, 1, 0, 2.667, 0, 0, 2.85, 1, 0, 2.967, 0, 0, 3.15, 1, 0, 3.3, 0, 0, 3.45, 1, 0, 3.617, 0, 0, 3.733, 1, 0, 3.85, 0, 0, 4.033, 1, 0, 4.467, 0, 2, 5.05, 0, 0, 5.233, 1, 0, 5.35, 0, 0, 5.533, 1, 0, 5.967, 0, 2, 6.167, 0, 0, 6.283, 1, 0, 6.4, 0, 0, 6.583, 1, 0, 6.7, 0, 0, 6.933, 1, 0, 7.1, 0, 0, 7.283, 1, 0, 7.45, 0, 0, 7.633, 1, 0, 7.75, 0, 0, 7.933, 1, 0, 8.067, 0, 0, 8.217, 1, 0, 8.383, 0, 0, 8.5, 1, 0, 9.25, 0, 2, 9.567, 0, 1, 9.584, 0, 9.6, 0.111, 9.617, 0.395, 1, 9.639, 0.774, 9.661, 1, 9.683, 1, 0, 9.817, 0, 0, 9.933, 1, 0, 10.117, 0, 0, 10.25, 0.5, 0, 10.35, 0, 0, 10.633, 1, 0, 10.833, 0, 0, 10.967, 1, 0, 11.167, 0, 0, 11.3, 1, 0, 12.283, 0, 2, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 4.633, 0, 2, 4.65, 0, 2, 9.4, 0, 2, 9.617, 0, 2, 10.117, 0, 2, 10.133, 0, 0, 10.317, 30, 2, 10.467, 30, 0, 10.55, 0, 2, 10.7, 0, 2, 10.867, 0, 2, 11.283, 0, 0, 11.35, 4, 0, 11.467, 0, 2, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.133, 0, 0, 1.417, 1, 0, 1.55, 0, 0, 1.733, 1.3, 1, 1.761, 1.3, 1.789, 1.294, 1.817, 1, 1, 1.861, 0.53, 1.906, 0, 1.95, 0, 2, 2.933, 0, 2, 3.683, 0, 0, 3.967, 1.1, 1, 4.189, 1.1, 4.411, 1.08, 4.633, 1.038, 1, 4.639, 1.037, 4.644, 1, 4.65, 1, 2, 6.667, 1, 0, 6.883, 0, 0, 7.067, 1, 2, 7.4, 1, 0, 7.617, 0, 0, 7.8, 1, 2, 8.1, 1, 0, 8.333, 0, 0, 8.45, 1.3, 0, 8.6, 1, 2, 9.25, 1, 0, 9.383, 0, 0, 9.617, 1, 2, 9.783, 1, 0, 9.867, 1.3, 0, 10.05, 0, 2, 11.6, 0, 0, 11.8, 1.3, 0, 12, 0, 0, 12.15, 1, 2, 12.583, 1, 2, 12.833, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.583, 0, 0, 2.15, 1, 2, 2.933, 1, 2, 3.683, 1, 0, 3.967, 0, 2, 4.633, 0, 2, 4.65, 0, 2, 9.4, 0, 2, 9.617, 0, 0, 10.05, 1, 2, 10.333, 1, 2, 10.817, 1, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.133, 0, 0, 1.417, 1, 0, 1.55, 0, 0, 1.733, 1.3, 1, 1.761, 1.3, 1.789, 1.294, 1.817, 1, 1, 1.861, 0.53, 1.906, 0, 1.95, 0, 2, 2.933, 0, 2, 3.683, 0, 0, 3.967, 1.3, 1, 4.189, 1.3, 4.411, 1.241, 4.633, 1.114, 1, 4.639, 1.111, 4.644, 1, 4.65, 1, 2, 6.667, 1, 0, 6.883, 0, 0, 7.067, 1, 2, 7.4, 1, 0, 7.617, 0, 0, 7.8, 1, 2, 8.1, 1, 0, 8.333, 0, 0, 8.45, 1.3, 0, 8.6, 1, 2, 9.25, 1, 0, 9.383, 0, 0, 9.617, 1, 2, 9.783, 1, 0, 9.867, 1.3, 0, 10.05, 0, 2, 11.6, 0, 0, 11.8, 1.3, 0, 12, 0, 0, 12.15, 1, 2, 12.583, 1, 2, 12.833, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.583, 0, 0, 2.15, 1, 2, 2.933, 1, 2, 3.683, 1, 0, 3.967, 0, 2, 4.633, 0, 2, 4.65, 0, 2, 9.4, 0, 2, 9.617, 0, 0, 10.05, 1, 2, 10.333, 1, 2, 10.817, 1, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 4.633, 0, 2, 4.65, 0, 2, 6.667, 0, 1, 6.795, 0, 6.922, -0.377, 7.05, -0.5, 1, 7.167, -0.613, 7.283, -0.6, 7.4, -0.6, 1, 7.539, -0.6, 7.678, 0.371, 7.817, 0.5, 1, 7.95, 0.624, 8.084, 0.6, 8.217, 0.6, 0, 8.45, 0, 2, 9.4, 0, 2, 9.617, 0, 2, 10.117, 0, 2, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 4.633, 0, 2, 4.65, 0, 2, 6.667, 0, 1, 6.795, 0, 6.922, -0.209, 7.05, -0.4, 1, 7.167, -0.575, 7.283, -0.6, 7.4, -0.6, 0, 7.817, 0.2, 0, 8.217, 0, 2, 8.45, 0, 2, 9.4, 0, 2, 9.617, 0, 2, 10.117, 0, 2, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.917, 0, 2, 4.633, 0, 1, 4.639, 0, 4.644, -0.177, 4.65, -0.178, 1, 5.517, -0.353, 6.383, -0.496, 7.25, -0.7, 1, 7.906, -0.854, 8.561, -1, 9.217, -1, 1, 9.278, -1, 9.339, -0.231, 9.4, -0.213, 1, 9.472, -0.192, 9.545, -0.187, 9.617, -0.178, 1, 9.784, -0.157, 9.95, -0.137, 10.117, -0.12, 1, 10.939, -0.038, 11.761, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamBrowForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.866, 1, 0.372, -0.866, 0.745, -0.876, 1.117, -0.7, 1, 1.445, -0.545, 1.772, 0, 2.1, 0, 2, 2.917, 0, 2, 4.633, 0, 1, 4.639, 0, 4.644, 0.007, 4.65, -0.001, 1, 5.05, -0.567, 5.45, -1, 5.85, -1, 2, 8.2, -1, 1, 8.6, -1, 9, -0.774, 9.4, -0.293, 1, 9.472, -0.206, 9.545, -0.057, 9.617, -0.001, 1, 9.622, 0.003, 9.628, 0, 9.633, 0, 2, 10.117, 0, 2, 11.267, 0, 2, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamBrowAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.467, 0, 0, 2.917, 1, 2, 3.983, 1, 0, 4.633, 0.391, 0, 4.65, 1, 2, 5.85, 1, 2, 8.2, 1, 2, 9.4, 1, 2, 9.617, 1, 2, 9.633, 1, 2, 10.117, 1, 2, 11.267, 1, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.002, 0, 0.25, -0.001, 2, 0.317, -0.001, 0, 0.333, 0, 2, 0.35, 0, 2, 0.383, 0, 2, 0.4, 0, 2, 0.433, 0, 2, 0.45, 0, 2, 0.467, 0, 2, 0.483, 0, 2, 0.5, 0, 2, 0.533, 0, 2, 0.55, 0, 2, 0.633, 0, 2, 0.65, 0, 2, 0.717, 0, 2, 0.733, 0, 2, 1.133, 0, 0, 1.283, -1, 2, 1.367, -1, 0, 1.5, 1, 2, 1.617, 1, 0, 1.633, -1, 2, 1.733, -1, 0, 1.833, 1, 2, 2.183, 1, 0, 2.417, -0.384, 0, 2.717, 0.105, 2, 2.733, 0.105, 0, 3.033, -0.03, 0, 3.35, 0.008, 0, 3.633, -0.002, 2, 3.667, -0.002, 2, 3.683, -0.002, 0, 3.817, -1, 2, 3.933, -1, 0, 4.167, 0.44, 0, 4.467, -0.091, 0, 4.65, 0.131, 0, 4.95, -0.037, 0, 5.25, 0.01, 2, 5.267, 0.01, 0, 5.55, -0.003, 2, 5.583, -0.003, 0, 5.85, 0.001, 2, 5.9, 0.001, 2, 5.917, 0.001, 2, 5.933, 0.001, 0, 6.017, 0, 2, 6.033, 0, 2, 6.083, 0, 2, 6.1, 0, 2, 6.117, 0, 2, 6.267, 0, 2, 6.283, 0, 2, 6.333, 0, 2, 6.35, 0, 2, 6.433, 0, 2, 6.45, 0, 2, 6.55, 0, 2, 6.567, 0, 2, 6.667, 0, 0, 6.783, 1, 2, 6.883, 1, 0, 7.017, -1, 2, 7.117, -1, 0, 7.383, 0.301, 0, 7.4, 0.293, 0, 7.5, 1, 2, 7.617, 1, 0, 7.733, -1, 2, 7.883, -1, 0, 8.2, 1, 2, 8.317, 1, 0, 8.4, -1, 2, 8.517, -1, 0, 8.533, 1, 2, 8.683, 1, 0, 8.917, -0.407, 0, 9.217, 0.112, 0, 9.25, 0.108, 0, 9.317, 1, 2, 9.8, 1, 0, 9.867, 0.095, 0, 9.95, 1, 2, 10.167, 1, 0, 10.417, -0.346, 0, 10.717, 0.096, 0, 11.033, -0.027, 0, 11.333, 0.007, 2, 11.35, 0.007, 0, 11.683, -1, 2, 11.85, -1, 0, 11.983, 0.592, 0, 12.083, -1, 2, 12.35, -1, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeRsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.002, 0, 0.25, -0.001, 2, 0.317, -0.001, 0, 0.333, 0, 2, 0.35, 0, 2, 0.383, 0, 2, 0.4, 0, 2, 0.433, 0, 2, 0.45, 0, 2, 0.467, 0, 2, 0.483, 0, 2, 0.5, 0, 2, 0.533, 0, 2, 0.55, 0, 2, 0.633, 0, 2, 0.65, 0, 2, 0.717, 0, 2, 0.733, 0, 2, 1.133, 0, 0, 1.283, -1, 2, 1.367, -1, 0, 1.5, 1, 2, 1.617, 1, 0, 1.633, -1, 2, 1.733, -1, 0, 1.833, 1, 2, 2.183, 1, 0, 2.417, -0.384, 0, 2.717, 0.105, 2, 2.733, 0.105, 0, 3.033, -0.03, 0, 3.35, 0.008, 0, 3.633, -0.002, 2, 3.667, -0.002, 2, 3.683, -0.002, 0, 3.8, -1, 2, 3.95, -1, 0, 4.167, 0.479, 0, 4.467, -0.041, 0, 4.65, 0.387, 0, 4.917, -0.118, 0, 5.233, 0.033, 0, 5.533, -0.009, 0, 5.833, 0.003, 2, 5.85, 0.003, 0, 6.117, -0.001, 2, 6.2, -0.001, 0, 6.233, 0, 2, 6.25, 0, 2, 6.317, 0, 2, 6.333, 0, 2, 6.367, 0, 2, 6.4, 0, 2, 6.417, 0, 2, 6.533, 0, 2, 6.55, 0, 2, 6.6, 0, 2, 6.617, 0, 2, 6.667, 0, 0, 6.783, 1, 2, 6.883, 1, 0, 7.017, -1, 2, 7.117, -1, 0, 7.383, 0.301, 0, 7.4, 0.293, 0, 7.5, 1, 2, 7.617, 1, 0, 7.733, -1, 2, 7.883, -1, 0, 8.2, 1, 2, 8.317, 1, 0, 8.4, -1, 2, 8.517, -1, 0, 8.533, 1, 2, 8.683, 1, 0, 8.917, -0.407, 0, 9.217, 0.112, 0, 9.25, 0.108, 0, 9.317, 1, 2, 9.8, 1, 0, 9.867, 0.095, 0, 9.95, 1, 2, 10.167, 1, 0, 10.417, -0.346, 0, 10.717, 0.096, 0, 11.033, -0.027, 0, 11.333, 0.007, 2, 11.35, 0.007, 0, 11.683, -1, 2, 11.85, -1, 0, 11.983, 0.592, 0, 12.083, -1, 2, 12.35, -1, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeLGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.001, 0, 0.1, 0.006, 2, 0.117, 0.006, 0, 0.417, -0.002, 2, 0.433, -0.002, 0, 0.7, 0.001, 2, 0.767, 0.001, 0, 0.783, 0, 2, 0.8, 0, 2, 0.833, 0, 2, 0.85, 0, 2, 0.883, 0, 2, 0.9, 0, 2, 0.917, 0, 2, 0.933, 0, 2, 0.95, 0, 2, 0.967, 0, 2, 0.983, 0, 2, 1.1, 0, 2, 1.117, 0, 2, 1.133, 0, 0, 1.25, 0.258, 0, 1.467, -0.845, 0, 1.533, -0.489, 0, 1.6, -1, 2, 1.617, -1, 0, 1.633, 1, 2, 1.65, 1, 0, 1.8, -1, 2, 1.817, -1, 0, 1.933, -0.322, 0, 2.033, -0.451, 0, 2.3, 0.294, 0, 2.567, -0.251, 0, 2.867, 0.118, 0, 3.183, -0.047, 0, 3.483, 0.017, 0, 3.683, -0.001, 0, 3.8, 0.262, 0, 4.033, -0.658, 0, 4.333, 0.319, 0, 4.65, -0.186, 0, 4.883, 0.078, 0, 5.167, -0.033, 0, 5.45, 0.013, 0, 5.75, -0.005, 0, 6.033, 0.002, 2, 6.067, 0.002, 0, 6.3, 0, 2, 6.317, 0, 0, 6.333, -0.001, 2, 6.383, -0.001, 0, 6.4, 0, 2, 6.417, 0, 2, 6.433, 0, 2, 6.45, 0, 2, 6.483, 0, 2, 6.5, 0, 2, 6.533, 0, 2, 6.55, 0, 2, 6.567, 0, 2, 6.6, 0, 2, 6.617, 0, 2, 6.667, 0, 0, 6.767, -0.287, 0, 6.967, 1, 2, 7, 1, 0, 7.3, -0.309, 0, 7.417, -0.121, 0, 7.483, -0.178, 0, 7.683, 1, 2, 7.733, 1, 0, 8.067, -0.31, 0, 8.133, -0.254, 0, 8.15, -0.257, 0, 8.367, 1, 2, 8.4, 1, 0, 8.417, -1, 2, 8.517, -1, 0, 8.783, 0.342, 0, 9.067, -0.271, 0, 9.25, 0.008, 0, 9.317, -0.236, 0, 9.367, -0.16, 0, 9.517, -1, 2, 9.567, -1, 0, 9.583, 1, 2, 9.65, 1, 0, 9.667, -1, 2, 9.783, -1, 0, 9.883, -0.283, 0, 9.917, -0.299, 0, 10.167, 1, 2, 10.45, 1, 0, 10.733, -0.23, 0, 11.017, 0.069, 0, 11.3, -0.023, 0, 11.583, 0.008, 2, 11.6, 0.008, 0, 11.683, 0.302, 0, 11.867, -1, 2, 11.883, -1, 0, 11.9, 1, 2, 11.917, 1, 0, 12, 0.118, 0, 12.05, 0.31, 0, 12.4, -0.93, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeRGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.001, 0, 0.1, 0.006, 2, 0.117, 0.006, 0, 0.417, -0.002, 2, 0.433, -0.002, 0, 0.7, 0.001, 2, 0.767, 0.001, 0, 0.783, 0, 2, 0.8, 0, 2, 0.833, 0, 2, 0.85, 0, 2, 0.883, 0, 2, 0.9, 0, 2, 0.917, 0, 2, 0.933, 0, 2, 0.95, 0, 2, 0.967, 0, 2, 0.983, 0, 2, 1.1, 0, 2, 1.117, 0, 2, 1.133, 0, 0, 1.25, 0.258, 0, 1.467, -0.845, 0, 1.533, -0.489, 0, 1.6, -1, 2, 1.617, -1, 0, 1.633, 1, 2, 1.65, 1, 0, 1.8, -1, 2, 1.817, -1, 0, 1.933, -0.322, 0, 2.033, -0.451, 0, 2.3, 0.294, 0, 2.567, -0.251, 0, 2.867, 0.118, 0, 3.183, -0.047, 0, 3.483, 0.017, 0, 3.683, -0.001, 0, 3.783, 0.273, 0, 4.033, -0.805, 0, 4.35, 0.328, 0, 4.65, -0.268, 0, 4.85, 0.136, 2, 4.867, 0.136, 0, 5.117, -0.075, 0, 5.417, 0.035, 0, 5.7, -0.014, 2, 5.717, -0.014, 0, 6, 0.005, 2, 6.017, 0.005, 0, 6.3, -0.002, 2, 6.317, -0.002, 2, 6.333, -0.002, 2, 6.35, -0.002, 0, 6.567, 0, 2, 6.583, 0, 0, 6.6, 0.001, 2, 6.65, 0.001, 0, 6.767, -0.287, 0, 6.967, 1, 2, 7, 1, 0, 7.3, -0.309, 0, 7.417, -0.121, 0, 7.483, -0.178, 0, 7.683, 1, 2, 7.733, 1, 0, 8.067, -0.31, 0, 8.133, -0.254, 0, 8.15, -0.256, 0, 8.367, 1, 2, 8.4, 1, 0, 8.417, -1, 2, 8.517, -1, 0, 8.783, 0.342, 0, 9.067, -0.271, 0, 9.25, 0.008, 0, 9.317, -0.236, 0, 9.367, -0.16, 0, 9.517, -1, 2, 9.567, -1, 0, 9.583, 1, 2, 9.65, 1, 0, 9.667, -1, 2, 9.783, -1, 0, 9.883, -0.283, 0, 9.917, -0.299, 0, 10.167, 1, 2, 10.45, 1, 0, 10.733, -0.23, 0, 11.017, 0.069, 0, 11.3, -0.023, 0, 11.583, 0.008, 2, 11.6, 0.008, 0, 11.683, 0.302, 0, 11.867, -1, 2, 11.883, -1, 0, 11.9, 1, 2, 11.917, 1, 0, 12, 0.118, 0, 12.05, 0.31, 0, 12.4, -0.93, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5, 2, 1.183, -5, 0, 1.2, 0, 2, 4.633, 0, 2, 4.65, 0, 2, 9.4, 0, 2, 9.617, 0, 2, 10.117, 0, 2, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.233, 0, 2, 1.7, 0, 1, 1.717, 0, 1.733, 0.172, 1.75, 0.246, 1, 1.872, 0.786, 1.995, 1, 2.117, 1, 2, 4.2, 1, 1, 4.344, 1, 4.489, 0.637, 4.633, 0.015, 1, 4.639, 0, 4.644, 0, 4.65, 0, 2, 9.4, 0, 2, 9.617, 0, 2, 10.117, 0, 2, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.233, 0, 2, 1.7, 0, 1, 1.711, 0, 1.722, 0.532, 1.733, 1.071, 1, 1.772, 2.956, 1.811, 3.72, 1.85, 3.72, 0, 2.117, 0, 2, 4.2, 0, 1, 4.35, 0, 4.5, 0.496, 4.65, 1.111, 1, 4.722, 1.407, 4.795, 1.44, 4.867, 1.44, 2, 9.4, 1.44, 1, 9.528, 1.44, 9.655, 1.47, 9.783, 1.255, 1, 9.911, 1.04, 10.039, 0, 10.167, 0, 2, 11.7, 0, 0, 12.083, 2.52, 0, 12.45, 0, 2, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.233, 0, 2, 1.7, 0, 2, 2.117, 0, 2, 4.2, 0, 1, 4.35, 0, 4.5, -2.175, 4.65, -4.863, 1, 4.722, -6.157, 4.795, -6.3, 4.867, -6.3, 2, 9.4, -6.3, 1, 9.472, -6.3, 9.545, -6.093, 9.617, -4.863, 1, 9.784, -2.024, 9.95, 0, 10.117, 0, 2, 11.7, 0, 2, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.64, 2, 1.467, -1.64, 0, 1.7, 0, 2, 4.2, 0, 0, 4.65, 0.507, 1, 4.656, 0.507, 4.661, 0.274, 4.667, 0.173, 1, 4.734, -1.034, 4.8, -1.56, 4.867, -1.56, 2, 9.4, -1.56, 1, 9.461, -0.291, 9.522, 0.978, 9.583, 2.247, 1, 9.866, 6.869, 10.15, 8.4, 10.433, 8.4, 0, 11.7, 7.5, 1, 11.894, 7.5, 12.089, 24.119, 12.283, 28.486, 1, 12.383, 30.732, 12.483, 30, 12.583, 30, 2, 12.833, 30]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 4.2, 0, 0, 4.633, -29.564, 1, 4.639, -29.564, 4.644, 0, 4.65, 0, 2, 4.867, 0, 2, 9.4, 0, 2, 9.617, 0, 2, 10.117, 0, 2, 11.7, 0, 0, 12.033, -19, 0, 12.45, 0, 2, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 1.233, 1, 2, 1.7, 1, 2, 2.117, 1, 2, 4.2, 1, 2, 4.633, 1, 2, 4.65, 1, 2, 9.4, 1, 2, 9.617, 1, 2, 10, 1, 2, 10.1, 1, 0, 10.117, -1, 2, 12.583, -1, 2, 12.833, -1]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.233, 0, 2, 1.7, 0, 2, 2.117, 0, 2, 4.2, 0, 1, 4.35, 0, 4.5, 0.007, 4.65, 0.026, 1, 4.722, 0.035, 4.795, 0.061, 4.867, 0.064, 1, 5.05, 0.071, 5.234, 0.071, 5.417, 0.079, 1, 5.572, 0.086, 5.728, 0.119, 5.883, 0.119, 1, 6.328, 0.119, 6.772, 0.118, 7.217, 0.115, 1, 7.528, 0.113, 7.839, 0.112, 8.15, 0.108, 1, 8.567, 0.102, 8.983, 0.081, 9.4, 0.064, 1, 9.472, 0.061, 9.545, 0.026, 9.617, 0.026, 0, 10, 0.12, 0, 10.533, -0.089, 1, 10.922, -0.089, 11.311, -0.075, 11.7, 0, 1, 11.817, 0.023, 11.933, 0.314, 12.05, 0.314, 0, 12.35, -0.071, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamHandLDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.7, 0, 2, 1.717, 10, 2, 4.633, 10, 1, 4.639, 8.667, 4.644, 7.333, 4.65, 6, 2, 9.4, 6, 2, 9.617, 6, 2, 9.633, 3, 2, 10.117, 3, 2, 12.083, 3, 2, 12.1, 10, 2, 12.583, 10, 2, 12.833, 10]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.233, 0, 2, 1.7, 0, 2, 2.117, 0, 2, 4.2, 0, 1, 4.35, 0, 4.5, 0.345, 4.65, 0.772, 1, 4.722, 0.978, 4.795, 1, 4.867, 1, 2, 9.4, 1, 0, 9.617, -1, 0, 10.117, 0, 2, 11.7, 0, 2, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.233, 0, 2, 1.7, 0, 2, 2.117, 0, 2, 4.2, 0, 2, 4.633, 0, 1, 4.639, 0.978, 4.644, 1.955, 4.65, 2.933, 1, 4.722, 3.551, 4.795, 3.8, 4.867, 3.8, 2, 9.4, 3.8, 1, 9.472, 3.8, 9.545, 3.675, 9.617, 2.933, 1, 9.784, 1.22, 9.95, 0, 10.117, 0, 2, 11.7, 0, 2, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 4.633, 1, 2, 4.65, 1, 2, 9.4, 1, 2, 9.617, 1, 2, 10.117, 1, 2, 12.367, 1, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.233, 0, 2, 1.7, 0, 1, 1.717, 0, 1.733, 0.172, 1.75, 0.246, 1, 1.872, 0.786, 1.995, 1, 2.117, 1, 2, 4.2, 1, 1, 4.344, 1, 4.489, 0.637, 4.633, 0.015, 1, 4.639, 0, 4.644, 0, 4.65, 0, 2, 9.4, 0, 2, 9.617, 0, 2, 10.117, 0, 2, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 22.1, 2, 1.233, 22.1, 1, 1.389, 22.1, 1.544, 6.078, 1.7, 0, 1, 1.75, -1.953, 1.8, -1.14, 1.85, -1.14, 0, 2.117, 0, 2, 4.2, 0, 0, 4.633, -1.96, 0, 4.867, 0, 2, 9.4, 0, 2, 9.617, 0, 2, 10.117, 0, 2, 11.7, 0, 0, 12.083, -1.86, 0, 12.45, 0, 2, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.233, 0, 2, 1.7, 0, 2, 2.117, 0, 2, 4.2, 0, 1, 4.35, 0, 4.5, -2.458, 4.65, -5.495, 1, 4.722, -6.957, 4.795, -7.12, 4.867, -7.12, 2, 9.4, -7.12, 1, 9.472, -7.12, 9.545, -6.885, 9.617, -5.495, 1, 9.784, -2.287, 9.95, 0, 10.117, 0, 2, 11.7, 0, 2, 12.45, 0, 2, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.5, 2, 1.6, 0.5, 0, 4.633, 1, 2, 4.65, 1, 2, 9.4, 1, 2, 9.617, 1, 2, 10.117, 1, 2, 12.417, 1, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 3.8, 2, 1.467, 3.8, 1, 1.534, 3.8, 1.6, 2.98, 1.667, 0.93, 1, 1.678, 0.588, 1.689, 0, 1.7, 0, 2, 4.2, 0, 0, 4.65, -0.09, 1, 4.656, -0.09, 4.661, 0.076, 4.667, 0.186, 1, 4.734, 1.504, 4.8, 2.1, 4.867, 2.1, 2, 9.4, 2.1, 1, 9.461, 0.83, 9.522, -0.44, 9.583, -1.71, 1, 9.866, -6.912, 10.15, -8.7, 10.433, -8.7, 1, 10.855, -8.7, 11.278, -8.544, 11.7, -7.5, 1, 11.867, -7.088, 12.033, -4.089, 12.2, -2.044, 1, 12.283, -1.022, 12.367, 0, 12.45, 0, 2, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -6.2, 2, 1.233, -6.2, 0, 1.7, 0, 2, 2.117, 0, 2, 4.2, 0, 2, 4.633, 0, 2, 4.65, 0, 2, 4.867, 0, 2, 9.4, 0, 2, 9.617, 0, 2, 10.117, 0, 2, 11.7, 0, 2, 12.45, 0, 2, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.1, 2, 1.167, 0.1, 2, 1.45, 0.1, 0, 1.7, 0, 2, 2.117, 0, 2, 4.2, 0, 1, 4.35, 0, 4.5, 0.006, 4.65, 0.025, 1, 4.722, 0.034, 4.795, 0.06, 4.867, 0.062, 1, 5.05, 0.066, 5.234, 0.067, 5.417, 0.072, 1, 5.572, 0.077, 5.728, 0.11, 5.883, 0.118, 1, 6.328, 0.14, 6.772, 0.146, 7.217, 0.146, 1, 7.528, 0.146, 7.839, 0.14, 8.15, 0.125, 1, 8.567, 0.105, 8.983, 0.086, 9.4, 0.062, 1, 9.472, 0.058, 9.545, 0.025, 9.617, 0.025, 0, 10, 0.148, 0, 10.533, -0.086, 0, 11.7, 0, 2, 12.45, 0, 2, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamHandRDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.7, 0, 2, 4.633, 0, 2, 4.65, 6, 2, 9.4, 6, 2, 9.617, 6, 2, 9.633, 3, 2, 10.117, 3, 2, 12.217, 3, 2, 12.233, 0, 2, 12.367, 0, 2, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.233, 0, 2, 1.7, 0, 2, 2.117, 0, 2, 4.2, 0, 2, 4.633, 0, 2, 4.65, 0, 2, 4.867, 0, 2, 9.4, 0, 2, 9.617, 0, 2, 10.117, 0, 2, 11.7, 0, 0, 12.35, -1, 0, 12.45, 0, 2, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.233, 0, 2, 1.7, 0, 2, 2.117, 0, 2, 4.2, 0, 2, 4.633, 0, 1, 4.639, 0.257, 4.644, 0.515, 4.65, 0.772, 1, 4.722, 0.935, 4.795, 1, 4.867, 1, 2, 9.4, 1, 0, 9.617, -1, 0, 10.117, 0, 2, 11.7, 0, 2, 12.45, 0, 2, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.233, 0, 2, 1.7, 0, 2, 2.117, 0, 2, 4.2, 0, 2, 4.633, 0, 1, 4.639, -1.878, 4.644, -3.756, 4.65, -5.634, 1, 4.722, -6.818, 4.795, -7.3, 4.867, -7.3, 2, 9.4, -7.3, 1, 9.472, -7.3, 9.545, -7.059, 9.617, -5.634, 1, 9.784, -2.345, 9.95, 0, 10.117, 0, 2, 11.7, 0, 2, 12.45, 0, 2, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.217, 0, 2, 1.617, 0, 2, 1.633, 1, 2, 4.633, 1, 2, 4.65, 1, 2, 9.4, 1, 2, 9.617, 1, 2, 10.117, 1, 2, 12.367, 1, 0, 12.383, 0, 2, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.217, 0, 2, 1.617, 0, 2, 1.633, 1, 2, 4.633, 1, 2, 4.65, 1, 2, 9.4, 1, 2, 9.617, 1, 2, 10.117, 1, 2, 12.367, 1, 0, 12.383, 0, 2, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.101, 0, 1.633, -0.127, 1, 1.794, -0.085, 1.956, -0.042, 2.117, 0, 2, 4.2, 0, 1, 4.344, -0.014, 4.489, -0.027, 4.633, -0.041, 1, 4.639, -0.028, 4.644, -0.015, 4.65, -0.002, 1, 4.722, -0.001, 4.795, 0, 4.867, 0, 2, 9.4, 0, 1, 9.472, 0, 9.545, 0.001, 9.617, -0.002, 1, 9.784, -0.008, 9.95, -0.021, 10.117, -0.031, 1, 10.867, -0.075, 11.617, -0.098, 12.367, -0.098, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.047, 0, 1.633, -0.059, 1, 1.794, -0.039, 1.956, -0.02, 2.117, 0, 2, 4.2, 0, 1, 4.344, 0.004, 4.489, 0.007, 4.633, 0.011, 1, 4.639, 0.008, 4.644, 0.004, 4.65, 0.001, 1, 4.722, 0, 4.795, 0, 4.867, 0, 2, 9.4, 0, 1, 9.472, 0, 9.545, 0, 9.617, 0.001, 1, 9.784, 0.004, 9.95, 0.01, 10.117, 0.016, 1, 10.867, 0.041, 11.617, 0.054, 12.367, 0.054, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.165, 0, 1.633, 0.209, 1, 1.794, 0.139, 1.956, 0.07, 2.117, 0, 2, 4.2, 0, 1, 4.344, 0.027, 4.489, 0.053, 4.633, 0.08, 1, 4.639, 0.055, 4.644, 0.03, 4.65, 0.005, 1, 4.722, 0.001, 4.795, 0, 4.867, 0, 2, 9.4, 0, 1, 9.472, 0, 9.545, -0.001, 9.617, 0.005, 1, 9.784, 0.02, 9.95, 0.053, 10.117, 0.08, 1, 10.867, 0.2, 11.617, 0.262, 12.367, 0.262, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 4.8, 0, 2, 6.917, 0, 0, 7.317, -7, 2, 7.583, -7, 0, 8.017, 6, 2, 8.35, 6, 0, 8.65, -0.759, 1, 8.9, -0.759, 9.15, -0.613, 9.4, 0, 1, 9.472, 0.177, 9.545, 1, 9.617, 1, 0, 9.95, -2, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -14.511, 0, 0.283, 3.794, 0, 0.917, -26.203, 1, 1.15, -26.203, 1.384, -26.179, 1.617, -21, 1, 1.767, -17.671, 1.917, 18, 2.067, 18, 1, 2.2, 18, 2.334, 18.046, 2.467, 17.501, 1, 2.561, 17.115, 2.656, 10.718, 2.75, 10.718, 0, 3.083, 18.823, 0, 3.467, 11.994, 0, 3.7, 16.223, 1, 3.928, 16.223, 4.155, 16.265, 4.383, 15, 1, 4.522, 14.228, 4.661, -1.911, 4.8, -1.911, 1, 5.056, -1.911, 5.311, -1.924, 5.567, 0, 1, 5.861, 2.217, 6.156, 6, 6.45, 6, 1, 6.75, 6, 7.05, 1.18, 7.35, -6, 1, 7.6, -11.983, 7.85, -14, 8.1, -14, 1, 8.533, -14, 8.967, -10.435, 9.4, 0, 1, 9.472, 1.739, 9.545, 12, 9.617, 12, 1, 9.684, 12, 9.75, 8.819, 9.817, 0, 1, 9.861, -5.879, 9.906, -20.225, 9.95, -21, 1, 10.067, -23.035, 10.183, -23.337, 10.3, -23.337, 0, 10.833, -13.674, 0, 11.4, -15.62, 1, 11.556, -15.62, 11.711, -15.654, 11.867, -14.746, 1, 11.961, -14.195, 12.056, 4.282, 12.15, 4.282, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.3, 0, 2, 4.3, 0, 0, 5, -30, 0, 6.383, 13.662, 0, 7.6, -22, 0, 8.917, 8, 0, 10.033, 0, 2, 10.117, 0, 2, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.652, 0, 0.517, -0.96, 0, 2.85, 0, 2, 4.633, 0, 2, 4.65, 0, 2, 4.683, 0, 0, 5.617, -1, 0, 6.75, 0, 1, 7.206, 0, 7.661, 0.015, 8.117, -0.96, 1, 8.778, -2.375, 9.439, -4.28, 10.1, -4.28, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -7.54, 0, 0.3, -7.795, 1, 0.8, -7.795, 1.3, -5.197, 1.8, 0, 1, 2.3, 5.197, 2.8, 7.795, 3.3, 7.795, 0, 4.3, -7.795, 2, 4.633, -7.795, 1, 4.639, -7.795, 4.644, 7.568, 4.65, 7.578, 1, 4.733, 7.732, 4.817, 7.795, 4.9, 7.795, 1, 5.4, 7.795, 5.9, 5.197, 6.4, 0, 1, 6.9, -5.197, 7.4, -7.795, 7.9, -7.795, 0, 9.4, 7.801, 1, 9.472, 7.801, 9.545, 7.829, 9.617, 7.578, 1, 9.784, 6.998, 9.95, 6.026, 10.117, 5.098, 1, 10.4, 3.521, 10.684, 2.258, 10.967, 0, 1, 11.506, -4.295, 12.044, -7.795, 12.583, -7.795, 2, 12.833, -7.795]}, {"Target": "Parameter", "Id": "ParamBodyStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.067, 0.25, 0, 1.167, -8, 0, 2.333, 10, 0, 3.2, 9, 0, 3.6, 10, 1, 3.889, 10, 4.178, 7.345, 4.467, 2.272, 1, 4.522, 1.296, 4.578, 0.916, 4.633, 0.916, 0, 5.067, 7.281, 0, 6.15, -7, 0, 7.267, 0.25, 0, 8.45, -5.184, 1, 8.667, -5.184, 8.883, -4.061, 9.1, 0.094, 1, 9.2, 2.012, 9.3, 8.973, 9.4, 9.702, 1, 9.461, 10, 9.522, 10, 9.583, 10, 1, 9.594, 10, 9.606, 10, 9.617, 9.743, 1, 9.728, 4.675, 9.839, 0, 9.95, 0, 1, 10.006, 0, 10.061, 0.648, 10.117, 1.519, 1, 10.161, 2.216, 10.206, 2.409, 10.25, 2.409, 0, 10.917, -1.92, 1, 11.056, -1.92, 11.194, -1.266, 11.333, 0, 1, 11.411, 0.709, 11.489, 1.044, 11.567, 1.044, 0, 11.7, -3, 0, 12.167, 10, 1, 12.272, 10, 12.378, 9.311, 12.483, 6.338, 1, 12.516, 5.399, 12.55, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.435, 0, 0.517, 6, 0, 2.05, -4, 0, 3.633, 4.114, 1, 3.966, 4.114, 4.3, 3.115, 4.633, 1.026, 1, 4.639, 0.991, 4.644, -0.09, 4.65, -0.129, 1, 5.306, -4.744, 5.961, -7, 6.617, -7, 0, 8.117, 6, 0, 9.4, -1.33, 1, 9.472, -1.33, 9.545, -0.411, 9.617, -0.129, 1, 9.778, 0.5, 9.939, 0.603, 10.1, 0.603, 1, 10.106, 0.603, 10.111, 0.618, 10.117, 0.599, 1, 10.3, -0.041, 10.484, -0.586, 10.667, -0.586, 0, 11.267, 2.445, 0, 11.933, -1.073, 0, 12.333, 4.859, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamLegLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 13.287, 0, 0.683, 14.515, 0, 2.083, 2.532, 0, 3.55, 16.605, 1, 3.917, 16.605, 4.283, 16.928, 4.65, 11.993, 1, 4.983, 7.507, 5.317, -9.742, 5.65, -9.742, 1, 6.056, -9.742, 6.461, -2.274, 6.867, 0, 1, 7.784, 5.14, 8.7, 7.705, 9.617, 11.993, 1, 9.706, 12.409, 9.794, 14.515, 9.883, 14.515, 1, 9.961, 14.515, 10.039, 14.879, 10.117, 14.159, 1, 10.939, 6.548, 11.761, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamLegRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 6.492, 0, 1.8, -9.756, 0, 3.583, 17.886, 1, 3.933, 17.886, 4.283, 13.443, 4.633, 4.21, 1, 4.639, 4.063, 4.644, -3.02, 4.65, -3.092, 1, 5, -7.597, 5.35, -9.756, 5.7, -9.756, 1, 6.089, -9.756, 6.478, -5.355, 6.867, 0, 1, 7.3, 5.967, 7.734, 7.732, 8.167, 7.732, 0, 9.4, -5.723, 1, 9.472, -5.723, 9.545, -4.102, 9.617, -3.092, 1, 9.784, -0.762, 9.95, 1.177, 10.117, 2.827, 1, 10.472, 6.347, 10.828, 7.732, 11.183, 7.732, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.613, 0, 4.633, 0.015, 0, 4.65, 1, 2, 9.4, 1, 2, 9.617, 1, 2, 10.117, 1, 2, 12.35, 1, 0, 12.367, 0, 2, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "Physics_AngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 7.05, 0, 0, 7.383, -4.67, 0, 8.05, 4.657, 0, 8.717, -1.361, 0, 9.35, 0, 2, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "Physics_AngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -8.358, 0, 0.35, 3.461, 0, 0.95, -16.332, 0, 2.117, 12.626, 0, 2.8, 5.751, 0, 3.133, 11.383, 0, 3.5, 6.475, 0, 3.817, 9.724, 0, 4.867, -2.205, 0, 6.467, 3.554, 0, 8.083, -8.167, 0, 9.667, 7.776, 0, 10.1, -15.662, 0, 10.867, -7.389, 0, 11.433, -9.097, 0, 12.233, 3.969, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "Physics_AngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 6.917, 0, 0, 7.183, 3.731, 0, 7.517, -0.601, 0, 7.6, -0.46, 0, 7.867, -3.736, 0, 8.2, 0.7, 0, 8.367, 0.248, 0, 8.567, 2.624, 0, 8.85, -0.769, 0, 9.167, -0.013, 0, 9.433, -0.216, 0, 9.45, -0.173, 0, 9.617, -0.833, 0, 9.95, 1.667, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.567, 0, 0.233, -7.115, 0, 0.583, 11.194, 0, 1, -10.657, 2, 1.017, -10.657, 0, 1.417, 6.165, 0, 1.867, -12.334, 0, 2.25, 13.833, 0, 2.65, -6.404, 0, 3.267, 6.232, 0, 3.667, -7.144, 0, 4.05, 4.867, 0, 4.417, -2.358, 0, 4.7, 3.788, 0, 5.033, -4.157, 0, 5.45, 2.368, 0, 5.867, -2.512, 0, 6.267, 1.789, 0, 6.667, -0.539, 0, 7.083, 1.154, 0, 7.667, -0.571, 0, 8.083, -0.029, 0, 8.617, -0.339, 0, 8.633, -0.338, 0, 8.65, -0.339, 0, 8.667, -0.337, 0, 8.683, -0.338, 0, 8.7, -0.336, 0, 8.733, -0.337, 0, 8.767, -0.334, 0, 9.567, -3.318, 0, 9.917, 16.822, 0, 10.25, -14.757, 0, 10.7, 7.756, 0, 11.133, -4.014, 0, 11.567, 2.284, 0, 12.067, -8.101, 0, 12.417, 9.168, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.365, 0, 0.133, 5.942, 0, 0.417, -18.905, 0, 0.8, 29.202, 0, 1.217, -21.899, 0, 1.633, 12.932, 0, 2.017, -30, 2, 2.15, -30, 0, 2.467, 29.326, 0, 2.85, -11.589, 0, 3.5, 14.259, 0, 3.867, -15.714, 0, 4.25, 10.725, 0, 4.6, -5.113, 0, 4.883, 11.865, 0, 5.25, -9.128, 0, 5.65, 4.983, 0, 5.683, 4.739, 0, 5.717, 4.915, 0, 6.083, -7.533, 0, 6.467, 3.854, 0, 6.833, -1.134, 0, 7.317, 4.529, 0, 7.95, -0.138, 0, 8.217, 0.151, 0, 9.4, -2.073, 0, 9.417, -2.034, 0, 9.45, -2.166, 0, 9.483, -2.033, 0, 9.733, -12.371, 0, 10.017, 30, 2, 10.183, 30, 0, 10.433, -30, 2, 10.5, -30, 0, 10.9, 14.799, 0, 11.333, -7.889, 0, 11.767, 4.845, 0, 12.25, -21.47, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_L", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.748, 0, 0.167, 1.202, 0, 0.433, -4.917, 0, 0.833, 6.442, 0, 1.283, -4.42, 0, 1.75, 3.094, 0, 2.117, -8.302, 0, 2.517, 5.338, 0, 2.833, 0.172, 0, 2.95, 1.816, 0, 3.217, -5.207, 0, 3.583, 5.061, 0, 3.967, -3.143, 0, 4.417, 1.786, 0, 4.617, 0.664, 0, 4.85, 2.229, 0, 5.217, -1.785, 0, 5.267, -1.671, 0, 5.3, -1.75, 0, 5.667, 1.223, 0, 5.7, 1.191, 0, 5.733, 1.253, 0, 5.75, 1.241, 0, 5.783, 1.291, 0, 6.05, -0.639, 0, 6.117, -0.442, 0, 6.3, -0.768, 0, 6.683, 0.677, 0, 7.067, -0.127, 0, 7.4, 1.53, 0, 8.067, -1.863, 0, 8.417, 1.177, 0, 8.617, 0.675, 0, 8.683, 0.722, 0, 9.067, -1.261, 0, 9.517, 0.662, 0, 9.783, -4.073, 0, 10.117, 10.404, 0, 10.533, -6.014, 0, 11.067, 3.238, 0, 11.517, -2.018, 0, 12, 2.218, 0, 12.283, -5.329, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_R", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.748, 0, 0.167, -1.202, 0, 0.433, 4.917, 0, 0.833, -6.442, 0, 1.283, 4.42, 0, 1.75, -3.094, 0, 2.117, 8.302, 0, 2.517, -5.338, 0, 2.833, -0.172, 0, 2.95, -1.816, 0, 3.217, 5.207, 0, 3.583, -5.061, 0, 3.967, 3.143, 0, 4.417, -1.786, 0, 4.617, -0.664, 0, 4.85, -2.229, 0, 5.217, 1.785, 0, 5.267, 1.671, 0, 5.3, 1.75, 0, 5.667, -1.223, 0, 5.7, -1.191, 0, 5.733, -1.253, 0, 5.75, -1.241, 0, 5.783, -1.291, 0, 6.05, 0.639, 0, 6.117, 0.442, 0, 6.3, 0.768, 0, 6.683, -0.677, 0, 6.967, -0.162, 0, 7.1, -0.247, 0, 7.35, 0.565, 0, 7.617, -3.602, 0, 7.867, 0.334, 0, 8.1, -1.577, 0, 8.417, 1.061, 0, 8.583, 0.717, 0, 8.717, 1.008, 0, 9.067, -0.494, 0, 9.417, 0.8, 0, 9.45, 0.699, 0, 9.75, 3.314, 0, 10.117, -9.71, 0, 10.533, 5.728, 0, 11.1, -3.157, 0, 11.533, 1.997, 0, 12, -2.253, 0, 12.283, 5.208, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.003, 0, 0.217, -12.832, 0, 0.55, 13.569, 0, 1.017, -4.321, 0, 1.333, 0.118, 0, 1.883, -16.743, 0, 2.217, 7.536, 0, 2.5, -0.338, 0, 2.7, 3.266, 0, 2.983, -6.388, 0, 3.317, 5.98, 0, 3.65, -5.561, 0, 3.967, 1.728, 0, 4.8, -30, 2, 5.233, -30, 0, 6.35, 18.257, 0, 6.967, -3.737, 0, 7.133, -0.09, 0, 7.45, -30, 2, 7.967, -30, 0, 8.233, 2.859, 0, 8.35, 1.128, 0, 8.583, 21.405, 0, 8.883, 3.791, 0, 9.117, 6.673, 0, 9.567, -8.822, 0, 9.867, 30, 2, 9.933, 30, 0, 10.2, -12.25, 0, 10.517, -0.786, 0, 10.717, -2.221, 0, 11.033, 1.097, 0, 11.483, -0.825, 0, 11.717, -0.606, 0, 12.083, -13.218, 0, 12.383, 7.271, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.011, 0, 0.15, 5.981, 0, 0.367, -12.298, 0, 0.65, 7.655, 0, 0.9, 0.013, 0, 0.933, 0.026, 0, 1.133, -2.308, 0, 1.433, 1.427, 0, 1.617, 0.28, 0, 1.783, 4.786, 0, 2.083, -9.919, 0, 2.333, 7.435, 0, 2.617, -4.798, 0, 2.867, 5.438, 0, 3.133, -6.034, 0, 3.45, 4.475, 0, 3.767, -4.921, 0, 4.033, 2.387, 0, 4.317, -0.82, 0, 4.75, 13.309, 0, 5.117, -8.378, 0, 5.367, -2.362, 0, 5.7, -6.631, 0, 6.917, 5.415, 0, 7.083, -0.517, 0, 7.333, 14.021, 0, 7.6, -6.599, 0, 7.767, 6.503, 0, 8.033, -20.047, 0, 8.283, 7.091, 0, 8.517, -14.897, 0, 8.733, 9.414, 0, 8.967, -6.065, 0, 9.217, 5.802, 0, 9.4, -0.368, 0, 9.533, 5.413, 0, 9.75, -13.101, 0, 10.033, 25.755, 0, 10.283, -14.922, 0, 10.55, 5.94, 0, 10.85, -2.937, 0, 11.1, 1.421, 0, 11.383, -0.254, 0, 12.017, 5.743, 0, 12.217, -11.434, 0, 12.467, 7.032, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.005, 0, 0.233, 15.203, 0, 0.633, -17.504, 0, 1.117, 5.105, 0, 1.467, 1.289, 0, 1.917, 21.913, 0, 2.3, -7.473, 0, 2.533, -3.379, 0, 2.667, -4.47, 0, 3, 7.726, 0, 3.383, -6.575, 0, 3.683, 4.93, 0, 4.133, -1.224, 0, 4.317, -0.939, 0, 4.667, -30, 2, 5.25, -30, 0, 6.3, 20.393, 0, 6.967, -9.826, 0, 7.15, -4.783, 0, 7.417, -30, 2, 8.017, -30, 0, 8.567, 30, 2, 8.617, 30, 0, 8.983, 7.195, 0, 9.233, 8.825, 0, 9.417, 7.414, 0, 9.55, 9.701, 0, 9.933, -24.233, 0, 10.367, 5.809, 0, 11.067, -2.975, 0, 12.1, 15.147, 0, 12.467, -6.728, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.024, 0, 0.167, -8.17, 0, 0.433, 16.671, 0, 0.783, -9.753, 0, 1.217, 4.509, 0, 1.583, -1.935, 0, 1.617, -1.901, 0, 1.8, -7.725, 0, 2.117, 16.879, 0, 2.433, -8.625, 0, 2.683, 3.352, 0, 2.917, -5.265, 0, 3.217, 7.513, 0, 3.567, -7.482, 0, 3.85, 5.345, 0, 4.183, -1.81, 0, 4.633, 19.816, 0, 5.717, -8.951, 0, 5.75, -8.919, 0, 5.85, -12.673, 0, 6.8, 11.075, 0, 7.117, -1.202, 0, 7.383, 18.859, 0, 7.633, -4.38, 0, 7.8, 3.601, 0, 8.083, -27.247, 0, 8.367, 4.436, 0, 8.55, -12.236, 0, 8.783, 10.242, 0, 9.067, -4.822, 0, 9.4, 3.863, 0, 9.55, -0.105, 0, 9.783, 11.162, 0, 9.8, 11.136, 0, 9.883, 13.661, 0, 10.117, -20.735, 0, 10.417, 7.748, 0, 10.7, -1.041, 0, 10.917, 1.758, 0, 11.233, -1.37, 0, 11.633, 0.408, 0, 12.033, -8.064, 0, 12.267, 11.609, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "Jewelry_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.258, 0, 0.233, -1.184, 0, 0.583, 1.867, 0, 1.017, -1.765, 0, 1.417, 1.014, 0, 1.867, -2.045, 0, 2.25, 2.28, 0, 2.65, -1.041, 0, 3.267, 1.047, 0, 3.667, -1.193, 0, 4.05, 0.809, 0, 4.417, -0.389, 0, 4.7, 0.626, 0, 5.033, -0.689, 0, 5.45, 0.392, 0, 5.833, -0.272, 0, 6.283, 0.179, 0, 6.65, -0.014, 0, 7.167, 0.468, 0, 7.55, -0.427, 0, 7.6, -0.413, 2, 7.617, -0.413, 0, 7.783, -0.516, 0, 8.183, 0.664, 0, 8.8, -0.401, 0, 9.2, 0.118, 0, 9.567, -0.807, 0, 9.917, 3.055, 0, 10.267, -2.571, 0, 10.7, 1.362, 0, 11.15, -0.726, 0, 11.567, 0.413, 0, 12.067, -1.372, 0, 12.417, 1.535, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "Jewelry_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.058, 0, 0.133, 0.979, 0, 0.417, -3.119, 0, 0.8, 4.605, 0, 1.217, -3.486, 0, 1.633, 2.088, 0, 2.083, -5.419, 0, 2.467, 4.536, 0, 2.867, -1.784, 0, 3.5, 2.378, 0, 3.867, -2.587, 0, 4.25, 1.763, 0, 4.6, -0.833, 0, 4.883, 1.954, 0, 5.25, -1.526, 0, 5.65, 0.83, 0, 6.033, -0.742, 0, 6.483, 0.303, 0, 6.817, -0.014, 0, 7.383, 1.439, 0, 8.033, -1.401, 0, 8.383, 1.331, 0, 9.05, -0.83, 0, 9.5, 0.284, 0, 9.75, -2.506, 0, 10.083, 6.916, 0, 10.467, -5.082, 0, 10.917, 2.483, 0, 11.35, -1.433, 0, 11.767, 0.864, 0, 12.25, -3.566, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "Jewelry_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.455, 0, 0.283, 1.756, 0, 0.583, -3.389, 0, 0.95, 3.731, 0, 1.35, -3.048, 0, 1.783, 2.528, 0, 2.217, -4.186, 0, 2.583, 4.009, 0, 2.933, -1.865, 0, 3.2, -0.388, 0, 3.317, -0.544, 0, 3.667, 2.016, 0, 4.017, -2.392, 0, 4.383, 1.739, 0, 4.733, -1.682, 0, 5.05, 2.009, 0, 5.383, -1.588, 0, 5.767, 1.057, 0, 6.133, -0.659, 0, 6.533, 0.292, 0, 7.2, -0.332, 2, 7.217, -0.332, 0, 7.55, 0.906, 0, 7.867, -0.21, 0, 7.95, -0.186, 0, 8.2, -0.927, 0, 8.517, 1.016, 0, 8.8, -0.146, 0, 8.967, 0.036, 0, 9.217, -0.384, 0, 9.633, 0.907, 0, 9.95, -3.592, 0, 10.233, 5.974, 0, 10.6, -4.663, 0, 10.983, 2.417, 0, 11.433, -1.264, 0, 11.883, 0.834, 0, 11.967, 0.787, 0, 12.117, 1.023, 0, 12.417, -3.022, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.064, 0, 0.2, -0.245, 0, 0.55, 0.615, 0, 1, -0.534, 2, 1.017, -0.534, 0, 1.383, 0.035, 0, 1.833, -0.544, 0, 2.25, 0.705, 0, 2.717, -0.307, 0, 3.233, 0.291, 0, 3.617, -0.306, 0, 4.017, 0.345, 0, 4.4, -0.035, 0, 4.55, 0.066, 0, 4.9, -0.663, 0, 5.3, 0.835, 0, 5.733, -0.392, 0, 6.1, 0.108, 0, 6.483, -0.205, 0, 7.133, 0.15, 0, 7.55, -0.021, 0, 7.6, -0.019, 0, 7.833, -0.091, 0, 8.167, 0.147, 0, 8.833, -0.205, 0, 9.1, -0.164, 0, 9.3, -0.499, 0, 9.767, 1.458, 0, 10.167, -1.859, 0, 10.567, 1.28, 0, 11, -0.871, 0, 11.45, 0.452, 0, 11.583, 0.328, 0, 11.667, 0.603, 0, 12, -1.575, 0, 12.4, 1.817, 0, 12.517, 1.472, 0, 12.533, 1.482, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.006, 0, 0.117, 0.225, 0, 0.383, -0.732, 0, 0.783, 1.611, 0, 1.217, -0.927, 0, 1.55, -0.002, 0, 2.067, -1.818, 0, 2.483, 1.311, 0, 2.967, -0.551, 0, 3.45, 0.634, 0, 3.817, -0.725, 0, 4.233, 0.834, 0, 4.533, 0.174, 0, 4.767, 0.583, 0, 5.117, -1.736, 0, 5.517, 1.855, 0, 5.95, -0.515, 0, 6.283, 0.377, 0, 6.683, -0.553, 2, 6.7, -0.553, 0, 7.4, 0.318, 0, 8.05, -0.183, 0, 8.383, 0.397, 0, 9.1, -0.541, 0, 9.217, -0.43, 0, 9.483, -1.646, 0, 10, 3.623, 0, 10.367, -3.534, 0, 10.783, 2.592, 0, 11.217, -1.725, 0, 11.583, 0.669, 0, 11.667, 0.462, 0, 11.833, 1.508, 0, 12.2, -3.873, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.775, 0, 0.233, -3.553, 0, 0.583, 5.601, 0, 1.017, -5.294, 0, 1.417, 3.04, 0, 1.867, -6.136, 0, 2.25, 6.84, 0, 2.65, -3.123, 0, 3.267, 3.142, 0, 3.667, -3.58, 0, 4.05, 2.428, 0, 4.417, -1.167, 0, 4.7, 1.877, 0, 5.033, -2.066, 0, 5.45, 1.176, 0, 5.833, -0.816, 0, 6.283, 0.538, 0, 6.65, -0.043, 0, 7.167, 1.405, 0, 7.55, -1.282, 0, 7.6, -1.239, 0, 7.783, -1.548, 0, 8.183, 1.992, 0, 8.8, -1.204, 0, 9.2, 0.355, 0, 9.567, -2.422, 0, 9.917, 9.164, 0, 10.267, -7.713, 0, 10.7, 4.087, 0, 11.15, -2.177, 0, 11.567, 1.239, 0, 12.067, -4.117, 0, 12.417, 4.606, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.174, 0, 0.133, 2.936, 0, 0.417, -9.358, 0, 0.8, 13.815, 0, 1.217, -10.458, 0, 1.633, 6.264, 0, 2.083, -16.256, 0, 2.467, 13.608, 0, 2.867, -5.352, 0, 3.5, 7.135, 0, 3.867, -7.76, 0, 4.25, 5.288, 0, 4.6, -2.498, 0, 4.883, 5.86, 0, 5.25, -4.577, 0, 5.65, 2.488, 0, 6.033, -2.227, 0, 6.483, 0.91, 0, 6.817, -0.043, 0, 7.383, 4.316, 0, 8.033, -4.203, 0, 8.383, 3.993, 0, 9.05, -2.491, 0, 9.5, 0.851, 0, 9.75, -7.519, 0, 10.083, 20.748, 0, 10.467, -15.245, 0, 10.917, 7.448, 0, 11.35, -4.298, 0, 11.767, 2.592, 0, 12.25, -10.698, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.365, 0, 0.283, 5.268, 0, 0.583, -10.167, 0, 0.95, 11.194, 0, 1.35, -9.144, 0, 1.783, 7.585, 0, 2.217, -12.559, 0, 2.583, 12.027, 0, 2.933, -5.595, 0, 3.2, -1.165, 0, 3.317, -1.633, 0, 3.667, 6.049, 0, 4.017, -7.174, 0, 4.383, 5.216, 0, 4.733, -5.047, 0, 5.05, 6.026, 0, 5.383, -4.763, 0, 5.767, 3.17, 0, 6.133, -1.976, 0, 6.533, 0.876, 0, 7.217, -0.995, 0, 7.55, 2.718, 0, 7.867, -0.629, 0, 7.95, -0.558, 0, 8.2, -2.782, 0, 8.517, 3.049, 0, 8.8, -0.439, 0, 8.967, 0.109, 0, 9.217, -1.151, 0, 9.633, 2.72, 0, 9.95, -10.777, 0, 10.233, 17.922, 0, 10.6, -13.989, 0, 10.983, 7.251, 0, 11.433, -3.791, 0, 11.883, 2.5, 0, 11.967, 2.362, 0, 12.117, 3.069, 0, 12.417, -9.066, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.768, 0, 0.367, 5.902, 0, 0.683, -12.161, 0, 1.033, 15.182, 0, 1.433, -12.953, 0, 1.85, 10.589, 0, 2.3, -14.658, 0, 2.683, 16.018, 0, 3.033, -9.697, 0, 3.35, 0.84, 0, 3.483, 0.02, 0, 3.767, 6.505, 0, 4.117, -9.393, 0, 4.483, 7.948, 0, 4.833, -7.086, 0, 5.167, 8.155, 0, 5.5, -7.27, 0, 5.867, 5.254, 0, 6.217, -3.536, 0, 6.6, 1.672, 0, 7.033, -0.953, 0, 7.183, -0.825, 0, 7.283, -0.871, 0, 7.667, 3.151, 0, 8.017, -1.534, 0, 8.1, -1.434, 0, 8.3, -2.577, 0, 8.633, 3.826, 0, 8.95, -1.331, 0, 9.15, -0.243, 0, 9.35, -0.963, 0, 9.717, 2.905, 0, 10.033, -11.861, 0, 10.333, 21.348, 0, 10.7, -19.901, 0, 11.067, 11.991, 0, 11.5, -5.968, 0, 11.95, 3.572, 0, 12.533, -10.093, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -7.125, 0, 0.017, -7.256, 0, 0.45, 7.274, 0, 0.783, -14.496, 0, 1.133, 19.411, 0, 1.517, -17.669, 0, 1.917, 14.704, 0, 2.383, -16.784, 0, 2.767, 19.854, 0, 3.133, -14.569, 0, 3.467, 4.209, 0, 3.667, 1.318, 0, 3.883, 6.311, 0, 4.217, -11.608, 0, 4.583, 11.348, 0, 4.933, -10.252, 0, 5.267, 11.168, 0, 5.6, -10.593, 0, 5.95, 8.215, 0, 6.317, -5.713, 0, 6.683, 3.118, 0, 7.083, -1.64, 0, 7.767, 3.572, 0, 8.133, -2.657, 0, 8.3, -2.111, 0, 8.383, -2.17, 0, 8.75, 4.631, 0, 9.083, -2.553, 0, 9.333, -0.031, 0, 9.483, -0.477, 0, 9.8, 3.157, 0, 10.117, -12.96, 0, 10.433, 24.319, 0, 10.783, -25.78, 0, 11.167, 17.958, 0, 11.567, -9.811, 0, 12, 5.207, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.212, 0, 0.117, -11.426, 0, 0.517, 9.93, 0, 0.867, -17.359, 0, 1.217, 23.8, 0, 1.6, -22.88, 0, 1.983, 19.595, 0, 2.433, -19.229, 0, 2.85, 23.214, 0, 3.233, -19.743, 0, 3.583, 8.802, 0, 3.817, 1.238, 0, 4.017, 5.362, 0, 4.333, -13.696, 0, 4.683, 15.4, 0, 5.033, -14.558, 0, 5.367, 15.293, 0, 5.717, -14.982, 0, 6.067, 12.476, 0, 6.417, -9.113, 0, 6.783, 5.502, 0, 7.15, -3.002, 0, 7.867, 3.894, 0, 8.25, -3.956, 0, 8.867, 5.29, 0, 9.2, -4.046, 0, 9.5, 1.031, 0, 9.667, 0.31, 0, 9.9, 3.077, 0, 10.217, -14.316, 0, 10.5, 27.245, 0, 10.85, -30, 2, 10.883, -30, 0, 11.25, 24.159, 0, 11.65, -15.035, 0, 12.067, 8.062, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 7.138, 0, 0.217, -16.801, 0, 0.6, 14.225, 0, 0.95, -21.085, 0, 1.283, 28.398, 0, 1.667, -28.064, 0, 2.05, 24.711, 0, 2.483, -21.984, 0, 2.917, 25.536, 0, 3.317, -24.302, 0, 3.683, 14.132, 0, 3.967, -0.814, 0, 4.15, 3.938, 0, 4.433, -15.271, 0, 4.783, 19.673, 0, 5.133, -19.708, 0, 5.467, 20.559, 0, 5.817, -20.485, 0, 6.167, 17.993, 0, 6.517, -13.934, 0, 6.883, 9.169, 0, 7.25, -5.551, 0, 7.6, 1.363, 0, 7.7, 1.177, 0, 7.983, 3.998, 0, 8.35, -5.406, 0, 8.95, 5.755, 0, 9.317, -5.724, 0, 9.65, 2.268, 0, 9.817, 0.999, 0, 10, 2.597, 0, 10.3, -15.454, 0, 10.567, 30, 0, 10.883, -30, 2, 10.967, -30, 0, 11.333, 29.674, 0, 11.733, -20.783, 0, 12.133, 12.39, 0, 12.583, 0, 2, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.167, 1, 0, 3, 0, 0, 4.167, 1, 0, 6, 0, 0, 7.167, 1, 0, 9, 0, 0, 10.167, 1, 0, 12, 0, 1, 12.278, 0, 12.555, 0.51, 12.833, 0.802]}, {"Target": "Parameter", "Id": "fenshangxuanzhuang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 4.278, 1.713, 8.555, 3.425, 12.833, 5.138]}, {"Target": "Parameter", "Id": "Mail_Transparency6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamSad2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 12.833, 1]}, {"Target": "Parameter", "Id": "ParamCRY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.2, 0, 12.833, -5.2]}, {"Target": "Parameter", "Id": "Position_Coordinates", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 12.833, 1]}, {"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Man_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 12.833, 1]}, {"Target": "Parameter", "Id": "Man_TMD2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "BG_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 12.833, 1]}, {"Target": "Parameter", "Id": "FG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "FG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "BG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "BG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "BG_White4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "BG_White5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "BG_White2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "BG_White3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "All_Size2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "All_Size3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "All_Size4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "tuerzuo3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "faqiehuan", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "faqiehuan2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "faqiehuan4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "faqiehuan3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "tuerzuo4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Mail_Change", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "dafengshangzhuanchang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "tongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamTongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "EyeFrame_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamBrowY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamSigh", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamDarken", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamCry", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_B", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_C", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "MB_Unidirectional_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "jing<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "huqi1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "huqi2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamShoulderLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamArmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 12.833, 1]}, {"Target": "Parameter", "Id": "ParamForearmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamArmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamArmL_ForearmL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmL_HandL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamHandLSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "xiongjiya", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "xiongjiya5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "xiongjiya3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "xiongjiya4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "xiongjiya2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamShoulderRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamArmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 12.833, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamArmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamArmR_ForearmR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamForearm<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 12.833, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_HandR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamHandRSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 12.833, 0.6]}, {"Target": "Parameter", "Id": "Change_idle_physics23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Param103", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "taiqiugundong", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -22.5, 0, 12.833, -22.5]}, {"Target": "Parameter", "Id": "Change_idle_physics32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamLegL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 12.833, 1]}, {"Target": "Parameter", "Id": "ParamLegLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamCrusLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamCrusLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>oodLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamFoodLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamFoodLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamLegR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 12.833, 1]}, {"Target": "Parameter", "Id": "ParamLegRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamCrusRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamCrusRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamCrus<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>ood<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamFoodRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamFoodRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Physics_Provisional", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "physics_Add_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "physics_Add_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Physics_Body_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Physics_Body_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Physics_breast", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamBreath2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "touch_drag1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "touch_drag2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "touch_drag3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "touch_drag4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "touch_drag5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "touch_drag6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "touch_drag7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "touch_drag8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "touch_drag9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>pian<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh654", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh655", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh657", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh656", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh658", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh659", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh660", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.833, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 12.333, "Value": ""}]}