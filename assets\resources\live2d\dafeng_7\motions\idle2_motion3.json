{"Version": 3, "Meta": {"Duration": 14.4, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 303, "TotalSegmentCount": 1586, "TotalPointCount": 1648, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.23, 1, 0.378, -0.324, 0.755, -0.418, 1.133, -0.512, 1, 1.139, -0.741, 1.144, -0.971, 1.15, -1.2, 1, 3.322, -1.307, 5.495, -1.415, 7.667, -1.522, 1, 7.672, -2.022, 7.678, -2.522, 7.683, -3.022, 1, 9.45, -1.211, 11.216, 0.599, 12.983, 2.41, 1, 12.989, 1.607, 12.994, 0.803, 13, 0, 1, 13.467, -0.077, 13.933, -0.153, 14.4, -0.23]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.73, 1, 0.378, -0.96, 0.755, -1.19, 1.133, -1.42, 1, 3.311, -0.114, 5.489, 1.192, 7.667, 2.498, 1, 7.672, 1.838, 7.678, 1.178, 7.683, 0.518, 1, 9.45, -0.498, 11.216, -1.514, 12.983, -2.53, 1, 12.989, -1.747, 12.994, -0.963, 13, -0.18, 1, 13.467, -0.363, 13.933, -0.547, 14.4, -0.73]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.5, 2, 1.133, 1.5, 1, 1.139, 3.5, 1.144, 5.5, 1.15, 7.5, 1, 3.322, 7.12, 5.495, 6.74, 7.667, 6.36, 1, 7.672, 4.356, 7.678, 2.351, 7.683, 0.347, 1, 9.45, 0.711, 11.216, 1.076, 12.983, 1.44, 1, 12.989, 1.46, 12.994, 1.48, 13, 1.5, 2, 14.4, 1.5]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 4.43, 1, 0.378, 4.553, 0.755, 4.677, 1.133, 4.8, 1, 1.139, 6.62, 1.144, 8.44, 1.15, 10.26, 1, 3.322, 9.9, 5.495, 9.54, 7.667, 9.18, 1, 7.672, 11.976, 7.678, 14.771, 7.683, 17.567, 1, 9.45, 15.808, 11.216, 14.049, 12.983, 12.29, 1, 12.989, 9.573, 12.994, 6.857, 13, 4.14, 1, 13.467, 4.237, 13.933, 4.333, 14.4, 4.43]}, {"Target": "Parameter", "Id": "BG_White4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.989, 0, 0.267, 1, 0, 0.933, 0.989, 0, 1.2, 1, 0, 1.933, 0.989, 0, 2.2, 1, 0, 2.867, 0.989, 0, 3.133, 1, 0, 3.833, 0.989, 0, 4.117, 1, 0, 4.783, 0.989, 0, 5.067, 1, 0, 5.767, 0.989, 0, 6.033, 1, 0, 6.7, 0.989, 0, 7, 1, 0, 7.683, 0.989, 0, 7.95, 1, 0, 8.633, 0.989, 0, 8.9, 1, 0, 9.617, 0.989, 0, 9.883, 1, 0, 10.567, 0.989, 0, 10.833, 1, 0, 11.517, 0.989, 0, 11.8, 1, 0, 12.483, 0.989, 0, 12.75, 1, 0, 13.45, 0.989, 0, 13.733, 1, 0, 14.4, 0.989]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.333, 1, 0, 0.6, 0, 0, 0.833, 1, 2, 1.25, 1, 0, 1.517, 0, 0, 1.767, 1, 2, 2.867, 1, 0, 3.15, 0, 0, 3.3, 1.3, 0, 3.5, 1, 2, 4.317, 1, 0, 4.5, 0, 0, 6.95, 1, 0, 7.083, 0, 0, 7.267, 1.3, 0, 7.35, 1, 2, 9.9, 1, 0, 10.033, 0, 0, 10.217, 1.3, 0, 10.3, 1, 2, 12.817, 1, 0, 12.95, 0, 0, 13.133, 1.3, 0, 13.217, 1, 2, 14.4, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.333, 1, 0, 0.6, 0, 0, 0.833, 1, 2, 1.25, 1, 0, 1.517, 0, 0, 1.767, 1, 2, 2.867, 1, 0, 3.15, 0, 0, 3.3, 1.3, 0, 3.5, 1, 2, 4.317, 1, 0, 4.5, 0, 0, 6.95, 1, 0, 7.083, 0, 0, 7.267, 1.3, 0, 7.35, 1, 2, 9.9, 1, 0, 10.033, 0, 0, 10.217, 1.3, 0, 10.3, 1, 2, 12.817, 1, 0, 12.95, 0, 0, 13.133, 1.3, 0, 13.217, 1, 2, 14.4, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 1, 0.494, 0, 0.656, -0.374, 0.817, -0.5, 1, 0.961, -0.613, 1.106, -0.6, 1.25, -0.6, 1, 1.428, -0.6, 1.605, 0.459, 1.783, 0.5, 1, 2.194, 0.594, 2.606, 0.6, 3.017, 0.6, 0, 3.3, 0, 2, 5.35, 0, 2, 6.033, 0, 2, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 1, 0.494, 0, 0.656, -0.206, 0.817, -0.4, 1, 0.961, -0.574, 1.106, -0.6, 1.25, -0.6, 0, 1.783, 0.2, 0, 3.017, 0, 2, 3.3, 0, 2, 5.35, 0, 2, 6.033, 0, 2, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamBrowAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.284, 0, 0.733, 0.4, 0, 0.933, 0.284, 0, 1.683, 0.4, 0, 1.933, 0.284, 0, 2.65, 0.4, 0, 2.867, 0.284, 0, 3.617, 0.4, 0, 3.833, 0.284, 0, 4.567, 0.4, 0, 4.783, 0.284, 0, 5.533, 0.4, 0, 5.767, 0.284, 0, 6.5, 0.4, 0, 6.7, 0.284, 0, 7.45, 0.4, 0, 7.683, 0.284, 0, 8.417, 0.4, 0, 8.633, 0.284, 0, 9.383, 0.4, 0, 9.617, 0.284, 0, 10.35, 0.4, 0, 10.567, 0.284, 0, 11.3, 0.4, 0, 11.517, 0.284, 0, 12.267, 0.4, 0, 12.483, 0.284, 0, 13.217, 0.4, 0, 13.45, 0.284, 0, 14.2, 0.4, 0, 14.4, 0.284]}, {"Target": "Parameter", "Id": "ParamEyeLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.002, 0, 0.25, 0.001, 2, 0.317, 0.001, 0, 0.333, 0, 0, 0.467, 1, 2, 0.567, 1, 0, 0.717, -1, 2, 0.867, -1, 0, 1.117, 0.337, 0, 1.267, 0.113, 0, 1.4, 1, 2, 1.483, 1, 0, 1.633, -1, 2, 1.783, -1, 0, 2.017, 0.35, 0, 2.333, -0.097, 0, 2.65, 0.027, 0, 2.867, -0.004, 0, 3.017, 1, 2, 3.1, 1, 0, 3.217, -1, 2, 3.367, -1, 0, 3.383, 1, 2, 3.567, 1, 0, 3.783, -0.408, 0, 4.1, 0.112, 0, 4.317, -0.013, 0, 4.417, 1, 2, 4.55, 1, 0, 4.817, -0.327, 0, 5.117, 0.091, 0, 5.433, -0.025, 0, 5.733, 0.007, 2, 5.75, 0.007, 0, 6.033, -0.002, 2, 6.067, -0.002, 0, 6.317, 0, 2, 6.333, 0, 0, 6.35, 0.001, 2, 6.367, 0.001, 0, 6.383, 0, 2, 6.417, 0, 2, 6.433, 0, 2, 6.45, 0, 2, 6.483, 0, 2, 6.5, 0, 2, 6.517, 0, 2, 6.533, 0, 2, 6.55, 0, 2, 6.567, 0, 2, 6.583, 0, 2, 6.633, 0, 2, 6.65, 0, 2, 6.683, 0, 2, 6.7, 0, 2, 6.8, 0, 2, 6.817, 0, 0, 14.4, -0.002]}, {"Target": "Parameter", "Id": "ParamEyeRsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.002, 0, 0.25, 0.001, 2, 0.317, 0.001, 0, 0.333, 0, 0, 0.467, 1, 2, 0.567, 1, 0, 0.717, -1, 2, 0.867, -1, 0, 1.117, 0.337, 0, 1.267, 0.113, 0, 1.4, 1, 2, 1.483, 1, 0, 1.633, -1, 2, 1.783, -1, 0, 2.017, 0.35, 0, 2.333, -0.097, 0, 2.65, 0.027, 0, 2.867, -0.004, 0, 3.017, 1, 2, 3.1, 1, 0, 3.217, -1, 2, 3.367, -1, 0, 3.383, 1, 2, 3.567, 1, 0, 3.783, -0.408, 0, 4.1, 0.112, 0, 4.317, -0.013, 0, 4.417, 1, 2, 4.55, 1, 0, 4.817, -0.327, 0, 5.117, 0.091, 0, 5.433, -0.025, 0, 5.733, 0.007, 2, 5.75, 0.007, 0, 6.033, -0.002, 2, 6.067, -0.002, 0, 6.317, 0, 2, 6.333, 0, 0, 6.35, 0.001, 2, 6.367, 0.001, 0, 6.383, 0, 2, 6.417, 0, 2, 6.433, 0, 2, 6.45, 0, 2, 6.483, 0, 2, 6.5, 0, 2, 6.517, 0, 2, 6.533, 0, 2, 6.55, 0, 2, 6.567, 0, 2, 6.583, 0, 2, 6.633, 0, 2, 6.65, 0, 2, 6.683, 0, 2, 6.7, 0, 2, 6.8, 0, 2, 6.817, 0, 0, 14.4, -0.002]}, {"Target": "Parameter", "Id": "ParamEyeLGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.001, 0, 0.1, -0.006, 2, 0.117, -0.006, 0, 0.333, 0.001, 0, 0.45, -0.265, 0, 0.667, 0.803, 0, 0.95, -0.712, 0, 1.25, 0.289, 0, 1.4, -0.235, 0, 1.6, 0.549, 0, 1.867, -0.905, 0, 2.2, 0.31, 0, 2.5, -0.125, 0, 2.8, 0.047, 2, 2.817, 0.047, 0, 2.983, -0.261, 0, 3.2, 0.83, 0, 3.283, 0.342, 0, 3.4, 1, 2, 3.5, 1, 0, 3.517, -1, 2, 3.583, -1, 0, 3.783, 0.339, 0, 4.033, -0.144, 0, 4.3, 0.074, 2, 4.317, 0.074, 0, 4.417, -0.284, 0, 4.65, 0.526, 0, 4.95, -0.277, 0, 5.267, 0.118, 0, 5.567, -0.045, 2, 5.583, -0.045, 0, 5.883, 0.016, 0, 6.183, -0.005, 2, 6.2, -0.005, 0, 6.483, 0.002, 2, 6.517, 0.002, 0, 6.733, 0, 2, 6.75, 0, 0, 6.783, -0.001, 2, 6.833, -0.001, 0, 6.85, 0, 2, 6.867, 0, 2, 6.883, 0, 2, 6.9, 0, 2, 6.933, 0, 2, 6.95, 0, 2, 6.967, 0, 2, 6.983, 0, 2, 7, 0, 2, 7.017, 0, 2, 7.033, 0, 2, 7.05, 0, 2, 7.067, 0, 2, 7.167, 0, 2, 7.183, 0, 2, 7.25, 0, 2, 7.267, 0, 2, 7.383, 0, 2, 7.4, 0, 2, 7.467, 0, 2, 7.483, 0, 0, 14.4, -0.001]}, {"Target": "Parameter", "Id": "ParamEyeRGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.001, 0, 0.1, -0.006, 2, 0.117, -0.006, 0, 0.333, 0.001, 0, 0.45, -0.265, 0, 0.667, 0.803, 0, 0.95, -0.712, 0, 1.25, 0.289, 0, 1.4, -0.235, 0, 1.6, 0.549, 0, 1.867, -0.905, 0, 2.2, 0.31, 0, 2.5, -0.125, 0, 2.8, 0.047, 2, 2.817, 0.047, 0, 2.983, -0.261, 0, 3.2, 0.83, 0, 3.283, 0.342, 0, 3.4, 1, 2, 3.5, 1, 0, 3.517, -1, 2, 3.583, -1, 0, 3.783, 0.339, 0, 4.033, -0.144, 0, 4.3, 0.074, 2, 4.317, 0.074, 0, 4.417, -0.284, 0, 4.65, 0.526, 0, 4.95, -0.277, 0, 5.267, 0.118, 0, 5.567, -0.045, 2, 5.583, -0.045, 0, 5.883, 0.016, 0, 6.183, -0.005, 2, 6.2, -0.005, 0, 6.483, 0.002, 2, 6.517, 0.002, 0, 6.733, 0, 2, 6.75, 0, 0, 6.783, -0.001, 2, 6.833, -0.001, 0, 6.85, 0, 2, 6.867, 0, 2, 6.883, 0, 2, 6.9, 0, 2, 6.933, 0, 2, 6.95, 0, 2, 6.967, 0, 2, 6.983, 0, 2, 7, 0, 2, 7.017, 0, 2, 7.033, 0, 2, 7.05, 0, 2, 7.067, 0, 2, 7.167, 0, 2, 7.183, 0, 2, 7.25, 0, 2, 7.267, 0, 2, 7.383, 0, 2, 7.4, 0, 2, 7.467, 0, 2, 7.483, 0, 0, 14.4, -0.001]}, {"Target": "Parameter", "Id": "huqi1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.25, 1, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "huqi2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.933, 0, 0, 2.217, 1, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamShoulderLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.548, 0, 0.317, -1, 0, 0.933, -0.548, 0, 1.267, -1, 0, 1.933, -0.548, 0, 2.233, -1, 0, 2.867, -0.548, 0, 3.2, -1, 0, 3.833, -0.548, 0, 4.15, -1, 0, 4.783, -0.548, 0, 5.133, -1, 0, 5.767, -0.548, 0, 6.083, -1, 0, 6.7, -0.548, 0, 7.05, -1, 0, 7.683, -0.548, 0, 8, -1, 0, 8.633, -0.548, 0, 8.967, -1, 0, 9.617, -0.548, 0, 9.917, -1, 0, 10.567, -0.548, 0, 10.9, -1, 0, 11.517, -0.548, 0, 11.85, -1, 0, 12.483, -0.548, 0, 12.817, -1, 0, 13.45, -0.548, 0, 13.783, -1, 0, 14.4, -0.548]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -8.2, 2, 0.933, -8.2, 2, 1.933, -8.2, 2, 2.867, -8.2, 2, 3.833, -8.2, 2, 4.783, -8.2, 2, 5.767, -8.2, 2, 6.7, -8.2, 2, 7.683, -8.2, 2, 8.633, -8.2, 2, 9.617, -8.2, 2, 10.567, -8.2, 2, 11.517, -8.2, 2, 12.483, -8.2, 2, 13.45, -8.2, 2, 14.4, -8.2]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.507, 0, 0.5, -0.421, 0, 0.933, -0.507, 0, 1.45, -0.421, 0, 1.933, -0.507, 0, 2.433, -0.421, 0, 2.867, -0.507, 0, 3.4, -0.421, 0, 3.833, -0.507, 0, 4.333, -0.421, 0, 4.783, -0.507, 0, 5.317, -0.421, 0, 5.767, -0.507, 0, 6.267, -0.421, 0, 6.7, -0.507, 0, 7.25, -0.421, 0, 7.683, -0.507, 0, 8.183, -0.421, 0, 8.633, -0.507, 0, 9.15, -0.421, 0, 9.617, -0.507, 0, 10.117, -0.421, 0, 10.567, -0.507, 0, 11.083, -0.421, 0, 11.517, -0.507, 0, 12.033, -0.421, 0, 12.483, -0.507, 0, 13.017, -0.421, 0, 13.45, -0.507, 0, 13.967, -0.421, 0, 14.4, -0.507]}, {"Target": "Parameter", "Id": "ParamShoulderRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.548, 0, 0.317, -1, 0, 0.933, -0.548, 0, 1.267, -1, 0, 1.933, -0.548, 0, 2.233, -1, 0, 2.867, -0.548, 0, 3.2, -1, 0, 3.833, -0.548, 0, 4.15, -1, 0, 4.783, -0.548, 0, 5.133, -1, 0, 5.767, -0.548, 0, 6.083, -1, 0, 6.7, -0.548, 0, 7.05, -1, 0, 7.683, -0.548, 0, 8, -1, 0, 8.633, -0.548, 0, 8.967, -1, 0, 9.617, -0.548, 0, 9.917, -1, 0, 10.567, -0.548, 0, 10.9, -1, 0, 11.517, -0.548, 0, 11.85, -1, 0, 12.483, -0.548, 0, 12.817, -1, 0, 13.45, -0.548, 0, 13.783, -1, 0, 14.4, -0.548]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.505, 0, 0.5, -0.398, 0, 0.933, -0.505, 0, 1.45, -0.398, 0, 1.933, -0.505, 0, 2.433, -0.398, 0, 2.867, -0.505, 0, 3.4, -0.398, 0, 3.833, -0.505, 0, 4.333, -0.398, 0, 4.783, -0.505, 0, 5.317, -0.398, 0, 5.767, -0.505, 0, 6.267, -0.398, 0, 6.7, -0.505, 0, 7.25, -0.398, 0, 7.683, -0.505, 0, 8.183, -0.398, 0, 8.633, -0.505, 0, 9.15, -0.398, 0, 9.617, -0.505, 0, 10.117, -0.398, 0, 10.567, -0.505, 0, 11.083, -0.398, 0, 11.517, -0.505, 0, 12.033, -0.398, 0, 12.483, -0.505, 0, 13.017, -0.398, 0, 13.45, -0.505, 0, 13.967, -0.398, 0, 14.4, -0.505]}, {"Target": "Parameter", "Id": "Change_idle_physics12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.479, 0, 0.417, 0.8, 0, 0.933, -0.479, 0, 1.367, 0.8, 0, 1.933, -0.479, 0, 2.35, 0.8, 0, 2.867, -0.479, 0, 3.3, 0.8, 0, 3.833, -0.479, 0, 4.25, 0.8, 0, 4.783, -0.479, 0, 5.233, 0.8, 0, 5.767, -0.479, 0, 6.183, 0.8, 0, 6.7, -0.479, 0, 7.167, 0.8, 0, 7.683, -0.479, 0, 8.1, 0.8, 0, 8.633, -0.479, 0, 9.067, 0.8, 0, 9.617, -0.479, 0, 10.033, 0.8, 0, 10.567, -0.479, 0, 11, 0.8, 0, 11.517, -0.479, 0, 11.95, 0.8, 0, 12.483, -0.479, 0, 12.933, 0.8, 0, 13.45, -0.479, 0, 13.883, 0.8, 0, 14.4, -0.479]}, {"Target": "Parameter", "Id": "ParamAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -4.226, 0, 0.417, 2.449, 0, 0.933, -4.226, 0, 1.367, 2.449, 0, 1.933, -4.226, 0, 2.35, 2.449, 0, 2.867, -4.226, 0, 3.3, 2.449, 0, 3.833, -4.226, 0, 4.25, 2.449, 0, 4.783, -4.226, 0, 5.233, 2.449, 0, 5.767, -4.226, 0, 6.183, 2.449, 0, 6.7, -4.226, 0, 7.167, 2.449, 0, 7.683, -4.226, 0, 8.1, 2.449, 0, 8.633, -4.226, 0, 9.067, 2.449, 0, 9.617, -4.226, 0, 10.033, 2.449, 0, 10.567, -4.226, 0, 11, 2.449, 0, 11.517, -4.226, 0, 11.95, 2.449, 0, 12.483, -4.226, 0, 12.933, 2.449, 0, 13.45, -4.226, 0, 13.883, 2.449, 0, 14.4, -4.226]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.009, 0, 0.417, 2, 0, 0.933, -2.009, 0, 1.367, 2, 0, 1.933, -2.009, 0, 2.35, 2, 0, 2.867, -2.009, 0, 3.3, 2, 0, 3.833, -2.009, 0, 4.25, 2, 0, 4.783, -2.009, 0, 5.233, 2, 0, 5.767, -2.009, 0, 6.183, 2, 0, 6.7, -2.009, 0, 7.167, 2, 0, 7.683, -2.009, 0, 8.1, 2, 0, 8.633, -2.009, 0, 9.067, 2, 0, 9.617, -2.009, 0, 10.033, 2, 0, 10.567, -2.009, 0, 11, 2, 0, 11.517, -2.009, 0, 11.95, 2, 0, 12.483, -2.009, 0, 12.933, 2, 0, 13.45, -2.009, 0, 13.883, 2, 0, 14.4, -2.009]}, {"Target": "Parameter", "Id": "ParamBodyStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.483, 0, 0.317, 2.866, 0, 0.767, -5.233, 0, 1.267, 2.866, 0, 1.75, -5.233, 0, 2.233, 2.866, 0, 2.7, -5.233, 0, 3.2, 2.866, 0, 3.667, -5.233, 0, 4.15, 2.866, 0, 4.617, -5.233, 0, 5.133, 2.866, 0, 5.6, -5.233, 0, 6.083, 2.866, 0, 6.533, -5.233, 0, 7.05, 2.866, 0, 7.517, -5.233, 0, 8, 2.866, 0, 8.467, -5.233, 0, 8.967, 2.866, 0, 9.45, -5.233, 0, 9.917, 2.866, 0, 10.4, -5.233, 0, 10.9, 2.866, 0, 11.35, -5.233, 0, 11.85, 2.866, 0, 12.317, -5.233, 0, 12.817, 2.866, 0, 13.283, -5.233, 0, 13.783, 2.866, 0, 14.233, -5.233, 0, 14.4, -1.483]}, {"Target": "Parameter", "Id": "Physics_AngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.434, 0, 0.45, 1.903, 0, 0.967, -2.78, 0, 1.417, 1.76, 0, 1.95, -2.746, 0, 2.383, 1.99, 0, 2.9, -2.781, 0, 3.35, 1.756, 0, 3.85, -2.799, 0, 4.283, 1.991, 0, 4.8, -2.803, 0, 5.267, 1.941, 0, 5.783, -2.791, 0, 6.217, 1.838, 0, 6.733, -2.784, 0, 7.2, 1.744, 0, 7.717, -2.768, 0, 8.133, 1.839, 0, 8.65, -2.804, 0, 9.117, 1.739, 0, 9.65, -2.73, 0, 10.067, 1.837, 0, 10.583, -2.804, 0, 11.05, 1.735, 0, 11.55, -2.777, 0, 12, 1.757, 0, 12.5, -2.799, 0, 12.967, 1.941, 0, 13.483, -2.77, 0, 13.933, 1.758, 0, 14.4, -2.434]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.602, 0, 0.233, -5.751, 0, 0.633, 10.415, 0, 1.117, -12.716, 0, 1.55, 14.012, 0, 2.05, -12.385, 0, 2.517, 14.214, 0, 3, -13.62, 0, 3.467, 14.316, 0, 3.967, -13.184, 0, 4.417, 14.355, 0, 4.9, -13.083, 0, 5.383, 13.337, 0, 5.883, -12.255, 0, 6.35, 13.887, 0, 6.833, -13.315, 0, 7.317, 13.317, 0, 7.817, -12.781, 0, 8.267, 14.176, 0, 8.767, -13.241, 0, 9.233, 13.856, 0, 9.733, -12.227, 0, 10.2, 13.611, 0, 10.7, -12.76, 0, 11.167, 13.825, 0, 11.65, -13.373, 0, 12.117, 14.184, 0, 12.6, -12.844, 0, 13.083, 13.265, 0, 13.583, -12.715, 0, 14.05, 13.856, 0, 14.4, -1.602]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.667, 0, 0.467, -16.743, 0, 0.867, 25.188, 0, 1.333, -30, 0, 1.733, 30, 2, 1.8, 30, 0, 2.283, -28.622, 0, 2.7, 30, 2, 2.783, 30, 0, 3.217, -30, 2, 3.25, -30, 0, 3.65, 30, 2, 3.717, 30, 0, 4.183, -29.541, 0, 4.6, 30, 2, 4.683, 30, 0, 5.133, -28.815, 0, 5.6, 28.925, 0, 6.117, -27.531, 0, 6.55, 30, 2, 6.6, 30, 0, 7.067, -29.406, 0, 7.533, 29.019, 0, 8.05, -28.853, 0, 8.467, 30, 2, 8.533, 30, 0, 8.983, -29.216, 0, 9.433, 30, 2, 9.467, 30, 0, 9.967, -27.228, 0, 10.433, 29.898, 0, 10.917, -28.5, 0, 11.367, 30, 2, 11.4, 30, 0, 11.883, -29.975, 0, 12.3, 30, 2, 12.367, 30, 0, 12.833, -28.214, 0, 13.317, 28.947, 0, 13.817, -28.465, 0, 14.25, 30, 2, 14.3, 30, 0, 14.4, 2.667]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_L", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.986, 0, 0.417, 0.571, 0, 0.933, -0.986, 0, 1.367, 0.571, 0, 1.933, -0.986, 0, 2.35, 0.571, 0, 2.867, -0.986, 0, 3.3, 0.571, 0, 3.833, -0.986, 0, 4.25, 0.571, 0, 4.783, -0.986, 0, 5.233, 0.571, 0, 5.767, -0.986, 0, 6.183, 0.571, 0, 6.7, -0.986, 0, 7.167, 0.571, 0, 7.683, -0.986, 0, 8.1, 0.571, 0, 8.633, -0.986, 0, 9.067, 0.571, 0, 9.617, -0.986, 0, 10.033, 0.571, 0, 10.567, -0.986, 0, 11, 0.571, 0, 11.517, -0.986, 0, 11.95, 0.571, 0, 12.483, -0.986, 0, 12.933, 0.571, 0, 13.45, -0.986, 0, 13.883, 0.571, 0, 14.4, -0.986]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_R", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.986, 0, 0.417, -0.571, 0, 0.933, 0.986, 0, 1.367, -0.571, 0, 1.933, 0.986, 0, 2.35, -0.571, 0, 2.867, 0.986, 0, 3.3, -0.571, 0, 3.833, 0.986, 0, 4.25, -0.571, 0, 4.783, 0.986, 0, 5.233, -0.571, 0, 5.767, 0.986, 0, 6.183, -0.571, 0, 6.7, 0.986, 0, 7.167, -0.571, 0, 7.683, 0.986, 0, 8.1, -0.571, 0, 8.633, 0.986, 0, 9.067, -0.571, 0, 9.617, 0.986, 0, 10.033, -0.571, 0, 10.567, 0.986, 0, 11, -0.571, 0, 11.517, 0.986, 0, 11.95, -0.571, 0, 12.483, 0.986, 0, 12.933, -0.571, 0, 13.45, 0.986, 0, 13.883, -0.571, 0, 14.4, 0.986]}, {"Target": "Parameter", "Id": "L_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.635, 0, 0.417, -3.265, 0, 0.933, 5.635, 0, 1.367, -3.265, 0, 1.933, 5.635, 0, 2.35, -3.265, 0, 2.867, 5.635, 0, 3.3, -3.265, 0, 3.833, 5.635, 0, 4.25, -3.265, 0, 4.783, 5.635, 0, 5.233, -3.265, 0, 5.767, 5.635, 0, 6.183, -3.265, 0, 6.7, 5.635, 0, 7.167, -3.265, 0, 7.683, 5.635, 0, 8.1, -3.265, 0, 8.633, 5.635, 0, 9.067, -3.265, 0, 9.617, 5.635, 0, 10.033, -3.265, 0, 10.567, 5.635, 0, 11, -3.265, 0, 11.517, 5.635, 0, 11.95, -3.265, 0, 12.483, 5.635, 0, 12.933, -3.265, 0, 13.45, 5.635, 0, 13.883, -3.265, 0, 14.4, 5.635]}, {"Target": "Parameter", "Id": "L_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.635, 0, 0.417, 3.265, 0, 0.933, -5.635, 0, 1.367, 3.265, 0, 1.933, -5.635, 0, 2.35, 3.265, 0, 2.867, -5.635, 0, 3.3, 3.265, 0, 3.833, -5.635, 0, 4.25, 3.265, 0, 4.783, -5.635, 0, 5.233, 3.265, 0, 5.767, -5.635, 0, 6.183, 3.265, 0, 6.7, -5.635, 0, 7.167, 3.265, 0, 7.683, -5.635, 0, 8.1, 3.265, 0, 8.633, -5.635, 0, 9.067, 3.265, 0, 9.617, -5.635, 0, 10.033, 3.265, 0, 10.567, -5.635, 0, 11, 3.265, 0, 11.517, -5.635, 0, 11.95, 3.265, 0, 12.483, -5.635, 0, 12.933, 3.265, 0, 13.45, -5.635, 0, 13.883, 3.265, 0, 14.4, -5.635]}, {"Target": "Parameter", "Id": "R_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.635, 0, 0.417, 3.265, 0, 0.933, -5.635, 0, 1.367, 3.265, 0, 1.933, -5.635, 0, 2.35, 3.265, 0, 2.867, -5.635, 0, 3.3, 3.265, 0, 3.833, -5.635, 0, 4.25, 3.265, 0, 4.783, -5.635, 0, 5.233, 3.265, 0, 5.767, -5.635, 0, 6.183, 3.265, 0, 6.7, -5.635, 0, 7.167, 3.265, 0, 7.683, -5.635, 0, 8.1, 3.265, 0, 8.633, -5.635, 0, 9.067, 3.265, 0, 9.617, -5.635, 0, 10.033, 3.265, 0, 10.567, -5.635, 0, 11, 3.265, 0, 11.517, -5.635, 0, 11.95, 3.265, 0, 12.483, -5.635, 0, 12.933, 3.265, 0, 13.45, -5.635, 0, 13.883, 3.265, 0, 14.4, -5.635]}, {"Target": "Parameter", "Id": "R_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.635, 0, 0.417, -3.265, 0, 0.933, 5.635, 0, 1.367, -3.265, 0, 1.933, 5.635, 0, 2.35, -3.265, 0, 2.867, 5.635, 0, 3.3, -3.265, 0, 3.833, 5.635, 0, 4.25, -3.265, 0, 4.783, 5.635, 0, 5.233, -3.265, 0, 5.767, 5.635, 0, 6.183, -3.265, 0, 6.7, 5.635, 0, 7.167, -3.265, 0, 7.683, 5.635, 0, 8.1, -3.265, 0, 8.633, 5.635, 0, 9.067, -3.265, 0, 9.617, 5.635, 0, 10.033, -3.265, 0, 10.567, 5.635, 0, 11, -3.265, 0, 11.517, 5.635, 0, 11.95, -3.265, 0, 12.483, 5.635, 0, 12.933, -3.265, 0, 13.45, 5.635, 0, 13.883, -3.265, 0, 14.4, 5.635]}, {"Target": "Parameter", "Id": "Jewelry_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.088, 0, 0.233, -0.341, 0, 0.633, 0.603, 0, 1.117, -0.731, 0, 1.55, 0.803, 0, 2.05, -0.709, 0, 2.517, 0.791, 0, 3, -0.772, 0, 3.467, 0.818, 0, 3.95, -0.767, 0, 4.417, 0.827, 0, 4.9, -0.749, 0, 5.383, 0.774, 0, 5.883, -0.702, 0, 6.35, 0.792, 0, 6.833, -0.747, 0, 7.317, 0.756, 0, 7.817, -0.741, 0, 8.267, 0.815, 0, 8.767, -0.757, 0, 9.233, 0.791, 0, 9.733, -0.682, 0, 10.2, 0.773, 0, 10.7, -0.732, 0, 11.167, 0.773, 0, 11.65, -0.76, 0, 12.117, 0.811, 0, 12.6, -0.754, 0, 13.083, 0.764, 0, 13.567, -0.745, 0, 14.05, 0.794, 0, 14.4, -0.088]}, {"Target": "Parameter", "Id": "Jewelry_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.194, 0, 0.467, -0.98, 0, 0.867, 1.454, 0, 1.333, -1.721, 0, 1.767, 1.779, 0, 2.283, -1.575, 0, 2.75, 1.744, 0, 3.233, -1.733, 0, 3.7, 1.823, 0, 4.183, -1.718, 0, 4.633, 1.808, 0, 5.133, -1.672, 0, 5.6, 1.678, 0, 6.117, -1.57, 0, 6.567, 1.717, 0, 7.067, -1.646, 0, 7.55, 1.683, 0, 8.05, -1.671, 0, 8.5, 1.795, 0, 8.983, -1.683, 0, 9.433, 1.677, 0, 9.967, -1.52, 0, 10.433, 1.704, 0, 10.917, -1.605, 0, 11.4, 1.698, 0, 11.883, -1.709, 0, 12.35, 1.83, 0, 12.833, -1.654, 0, 13.317, 1.728, 0, 13.8, -1.662, 0, 14.267, 1.744, 0, 14.4, 0.194]}, {"Target": "Parameter", "Id": "Jewelry_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.324, 0, 0.217, 0.146, 0, 0.267, 0.151, 0, 0.65, -0.861, 0, 1.033, 1.183, 0, 1.483, -1.341, 0, 1.9, 1.339, 0, 2.433, -1.042, 0, 2.9, 1.261, 0, 3.383, -1.236, 0, 3.833, 1.316, 0, 4.333, -1.2, 0, 4.783, 1.252, 0, 5.283, -1.132, 0, 5.75, 1.176, 0, 6.283, -1.06, 0, 6.717, 1.264, 0, 7.217, -1.132, 0, 7.683, 1.16, 0, 8.2, -1.18, 0, 8.633, 1.319, 0, 9.133, -1.162, 0, 9.583, 1.217, 0, 10.133, -1.002, 0, 10.583, 1.234, 0, 11.067, -1.122, 0, 11.533, 1.208, 0, 12.033, -1.214, 0, 12.483, 1.315, 0, 12.967, -1.128, 0, 13.467, 1.179, 0, 13.95, -1.155, 0, 14.4, 0.324]}, {"Target": "Parameter", "Id": "Physics_Joint_5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.293, 0, 0.017, -0.289, 0, 0.183, -0.427, 0, 0.567, 1.068, 0, 1, -1.401, 0, 1.45, 1.542, 0, 1.917, -1.557, 0, 2.4, 1.565, 0, 2.867, -1.564, 0, 3.35, 1.519, 0, 3.833, -1.53, 0, 4.317, 1.548, 0, 4.783, -1.535, 0, 5.283, 1.432, 0, 5.767, -1.455, 0, 6.25, 1.519, 0, 6.717, -1.569, 0, 7.2, 1.48, 0, 7.683, -1.491, 0, 8.167, 1.523, 0, 8.633, -1.536, 0, 9.117, 1.486, 0, 9.6, -1.469, 0, 10.083, 1.52, 0, 10.567, -1.493, 0, 11.05, 1.477, 0, 11.517, -1.54, 0, 12, 1.52, 2, 12.017, 1.52, 0, 12.483, -1.523, 0, 12.967, 1.476, 0, 13.45, -1.485, 0, 13.933, 1.481, 0, 14.367, -1.289, 0, 14.4, -0.293]}, {"Target": "Parameter", "Id": "Physics_Joint_4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.111, 0, 0.4, -1.276, 0, 0.783, 2.611, 0, 1.217, -3.05, 0, 1.667, 3.165, 0, 2.133, -3.107, 0, 2.617, 3.141, 0, 3.083, -3.099, 0, 3.567, 3.041, 0, 4.05, -3.055, 0, 4.533, 3.115, 0, 5, -3.031, 0, 5.5, 2.877, 0, 5.983, -2.932, 0, 6.467, 3.106, 0, 6.933, -3.117, 0, 7.417, 2.96, 0, 7.9, -2.989, 0, 8.383, 3.08, 0, 8.85, -3.058, 0, 9.333, 2.965, 0, 9.833, -2.953, 0, 10.3, 3.065, 0, 10.783, -2.972, 0, 11.283, 3, 0, 11.733, -3.079, 0, 12.233, 3.057, 0, 12.7, -3.026, 0, 13.183, 2.968, 0, 13.667, -2.969, 0, 14.167, 3.017, 0, 14.4, -0.111]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.264, 0, 0.233, -1.022, 0, 0.633, 1.808, 0, 1.117, -2.193, 0, 1.55, 2.41, 0, 2.05, -2.126, 0, 2.517, 2.375, 0, 3, -2.316, 0, 3.467, 2.453, 0, 3.95, -2.302, 0, 4.417, 2.48, 0, 4.9, -2.247, 0, 5.383, 2.321, 0, 5.883, -2.106, 0, 6.35, 2.378, 0, 6.833, -2.24, 0, 7.317, 2.269, 0, 7.817, -2.224, 0, 8.267, 2.446, 0, 8.767, -2.271, 0, 9.233, 2.373, 0, 9.733, -2.046, 0, 10.2, 2.319, 0, 10.7, -2.196, 0, 11.167, 2.321, 0, 11.65, -2.281, 0, 12.117, 2.434, 0, 12.6, -2.262, 0, 13.083, 2.293, 0, 13.567, -2.236, 0, 14.05, 2.382, 0, 14.4, -0.264]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.581, 0, 0.467, -2.94, 0, 0.867, 4.361, 0, 1.333, -5.163, 0, 1.767, 5.336, 0, 2.283, -4.725, 0, 2.75, 5.234, 0, 3.233, -5.198, 0, 3.7, 5.468, 0, 4.183, -5.153, 0, 4.633, 5.423, 0, 5.133, -5.016, 0, 5.6, 5.035, 0, 6.117, -4.709, 0, 6.567, 5.152, 0, 7.067, -4.939, 0, 7.55, 5.049, 0, 8.05, -5.015, 0, 8.5, 5.386, 0, 8.983, -5.048, 0, 9.433, 5.032, 0, 9.967, -4.559, 0, 10.433, 5.112, 0, 10.917, -4.815, 0, 11.4, 5.094, 0, 11.883, -5.127, 0, 12.35, 5.49, 0, 12.833, -4.962, 0, 13.317, 5.183, 0, 13.8, -4.986, 0, 14.267, 5.234, 0, 14.4, 0.581]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.972, 0, 0.217, 0.44, 0, 0.267, 0.453, 0, 0.65, -2.583, 0, 1.033, 3.55, 0, 1.483, -4.021, 0, 1.9, 4.017, 0, 2.433, -3.126, 0, 2.9, 3.782, 0, 3.383, -3.709, 0, 3.833, 3.948, 0, 4.333, -3.599, 0, 4.783, 3.756, 0, 5.283, -3.397, 0, 5.75, 3.528, 0, 6.283, -3.181, 0, 6.717, 3.794, 0, 7.217, -3.397, 0, 7.683, 3.479, 0, 8.2, -3.541, 0, 8.633, 3.957, 0, 9.133, -3.485, 0, 9.583, 3.652, 0, 10.133, -3.007, 0, 10.583, 3.703, 0, 11.067, -3.366, 0, 11.533, 3.625, 0, 12.033, -3.643, 0, 12.483, 3.946, 0, 12.967, -3.385, 0, 13.467, 3.536, 0, 13.95, -3.466, 0, 14.4, 0.972]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.479, 0, 0.033, 1.545, 0, 0.75, -2.851, 0, 1.133, 4.635, 0, 1.567, -5.181, 0, 2, 5.365, 0, 2.45, -3.938, 0, 2.983, 4.684, 0, 3.45, -4.701, 0, 3.917, 5.053, 0, 4.4, -4.546, 0, 4.867, 4.948, 0, 5.333, -4.297, 0, 5.833, 4.478, 0, 6.333, -3.84, 0, 6.817, 4.817, 0, 7.283, -4.394, 0, 7.767, 4.382, 0, 8.267, -4.333, 0, 8.733, 5.081, 0, 9.2, -4.462, 0, 9.667, 4.701, 0, 10.183, -3.549, 0, 10.667, 4.655, 0, 11.133, -4.347, 0, 11.617, 4.563, 0, 12.1, -4.54, 0, 12.567, 5.028, 0, 13.033, -4.462, 0, 13.55, 4.397, 0, 14.017, -4.389, 0, 14.4, 1.479]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.483, 0, 0.117, 2.45, 0, 0.85, -3.013, 0, 1.233, 5.847, 0, 1.65, -6.792, 0, 2.083, 7.034, 0, 2.533, -5.727, 0, 3.067, 5.534, 0, 3.533, -6.024, 0, 4, 6.363, 0, 4.467, -5.902, 0, 4.95, 6.192, 0, 5.417, -5.864, 0, 5.917, 5.534, 0, 6.383, -4.911, 0, 6.9, 5.869, 0, 7.35, -5.79, 0, 7.85, 5.459, 0, 8.333, -5.395, 0, 8.817, 6.332, 0, 9.267, -5.891, 0, 9.75, 5.863, 0, 10.217, -4.69, 0, 10.767, 5.594, 0, 11.2, -5.695, 0, 11.7, 5.659, 0, 12.183, -5.732, 0, 12.65, 6.246, 0, 13.1, -5.892, 0, 13.617, 5.253, 0, 14.1, -5.562, 0, 14.4, 1.483]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.513, 0, 0.2, 3.899, 0, 0.583, -1.273, 0, 0.7, -1.059, 0, 0.967, -3.016, 0, 1.333, 7.157, 0, 1.733, -8.923, 0, 2.167, 9.153, 0, 2.6, -7.557, 0, 3.133, 6.425, 0, 3.6, -7.59, 0, 4.083, 7.937, 0, 4.533, -7.649, 0, 5.033, 7.586, 0, 5.483, -7.412, 0, 5.983, 6.842, 0, 6.45, -6.342, 0, 6.983, 6.964, 0, 7.433, -7.525, 0, 7.917, 6.828, 0, 8.4, -6.723, 0, 8.9, 7.719, 0, 9.333, -7.726, 0, 9.833, 7.232, 0, 10.283, -6.306, 0, 10.85, 6.49, 0, 11.283, -7.375, 0, 11.767, 7.013, 0, 12.25, -7.204, 0, 12.733, 7.878, 0, 13.183, -7.68, 0, 13.667, 6.582, 0, 14.167, -6.892, 0, 14.4, -0.513]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.752, 0, 0.283, 6.093, 0, 0.667, -2.675, 0, 0.867, -1.29, 0, 1.067, -2.774, 0, 1.433, 8.512, 0, 1.817, -11.566, 0, 2.233, 11.887, 0, 2.667, -10.161, 0, 3.183, 7.573, 0, 3.683, -9.267, 0, 4.15, 9.879, 0, 4.6, -9.723, 2, 4.617, -9.723, 0, 5.1, 9.235, 0, 5.567, -9.369, 0, 6.05, 8.498, 0, 6.533, -8.038, 0, 7.05, 8.14, 0, 7.517, -9.503, 0, 7.983, 8.627, 0, 8.467, -8.283, 0, 8.967, 9.262, 0, 9.417, -9.909, 0, 9.9, 8.951, 0, 10.367, -8.213, 0, 10.917, 7.39, 0, 11.367, -9.256, 0, 11.833, 8.793, 0, 12.317, -8.903, 0, 12.8, 9.529, 0, 13.267, -9.79, 0, 13.733, 8.335, 0, 14.25, -8.353, 0, 14.4, -5.752]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.167, 1, 0, 3, 0, 0, 4.167, 1, 0, 6, 0, 0, 7.167, 1, 0, 9, 0, 0, 10.167, 1, 0, 12, 0, 0, 13.167, 1, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.4, 1]}, {"Target": "Parameter", "Id": "ParamSad2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.4, 1]}, {"Target": "Parameter", "Id": "ParamCRY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.2, 0, 14.4, -5.2]}, {"Target": "Parameter", "Id": "Position_Coordinates", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.4, 1]}, {"Target": "Parameter", "Id": "Man_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.4, 1]}, {"Target": "Parameter", "Id": "Man_TMD2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "BG_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.4, 1]}, {"Target": "Parameter", "Id": "FG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "FG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "BG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "BG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "BG_White5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "BG_White2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "BG_White3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.3, 0, 14.4, 0.3]}, {"Target": "Parameter", "Id": "All_Size2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -12.72, 0, 14.4, -12.72]}, {"Target": "Parameter", "Id": "All_Size3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "All_Size4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "tuerzuo3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "faqiehuan", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "faqiehuan2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "faqiehuan4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "faqiehuan3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "tuerzuo4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Mail_Change", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.4, 1]}, {"Target": "Parameter", "Id": "Mail_Transparency5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "dafengshangzhuanchang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.5, 0, 14.4, 0.5]}, {"Target": "Parameter", "Id": "ParamMouthOpenY_SD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 14.4, 0.6]}, {"Target": "Parameter", "Id": "ParamMouthOpenY2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "tongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamTongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "EyeFrame_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1, 0, 14.4, -1]}, {"Target": "Parameter", "Id": "ParamBrowForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1, 0, 14.4, -1]}, {"Target": "Parameter", "Id": "ParamBrowY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5, 0, 14.4, -5]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5, 0, 14.4, 5]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamSigh", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamDarken", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamCry", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_B", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_C", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "MB_Unidirectional_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "jing<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.374, 0, 14.4, -2.374]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 6.925, 0, 14.4, 6.925]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -4.027, 0, 14.4, -4.027]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10.008, 0, 14.4, -10.008]}, {"Target": "Parameter", "Id": "ParamForearmR_RE3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamArmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.4, 1]}, {"Target": "Parameter", "Id": "ParamForearmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -30, 0, 14.4, -30]}, {"Target": "Parameter", "Id": "ParamArmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamArmL_ForearmL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.087, 0, 14.4, 0.087]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.4, 1]}, {"Target": "Parameter", "Id": "ParamForearmL_HandL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamHandLDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 8, 0, 14.4, 8]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamHandLSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "xiongjiya", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.4, 1]}, {"Target": "Parameter", "Id": "xiongjiya5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 14.4, 0.7]}, {"Target": "Parameter", "Id": "xiongjiya3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.85, 0, 14.4, 0.85]}, {"Target": "Parameter", "Id": "xiongjiya4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.85, 0, 14.4, 0.85]}, {"Target": "Parameter", "Id": "xiongjiya2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamArmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.4, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 30, 0, 14.4, 30]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -7.9, 0, 14.4, -7.9]}, {"Target": "Parameter", "Id": "ParamArmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamArmR_ForearmR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.4, 1]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.72, 0, 14.4, -0.72]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamForearm<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.4, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_HandR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamHandRDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 8, 0, 14.4, 8]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamHandRSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.4, 1]}, {"Target": "Parameter", "Id": "Change_idle_physics8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.4, 1]}, {"Target": "Parameter", "Id": "Change_idle_physics7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 14.4, 0.6]}, {"Target": "Parameter", "Id": "Change_idle_physics23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.4, 1]}, {"Target": "Parameter", "Id": "Change_idle_physics24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.095, 0, 14.4, -0.095]}, {"Target": "Parameter", "Id": "Change_idle_physics5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.3, 0, 14.4, -0.3]}, {"Target": "Parameter", "Id": "Change_idle_physics6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.615, 0, 14.4, 0.615]}, {"Target": "Parameter", "Id": "Change_idle_physics10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.4, 1]}, {"Target": "Parameter", "Id": "Change_idle_physics20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Param103", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -17.662, 0, 14.4, -17.662]}, {"Target": "Parameter", "Id": "taiqiugundong", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -22.5, 0, 14.4, -22.5]}, {"Target": "Parameter", "Id": "Change_idle_physics32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamLegL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.4, 1]}, {"Target": "Parameter", "Id": "ParamLegLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamLegLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamCrusLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamCrusLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>oodLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamFoodLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamFoodLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamLegR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.4, 1]}, {"Target": "Parameter", "Id": "ParamLegRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamLegRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamCrusRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamCrusRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamCrus<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>ood<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamFoodRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamFoodRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.187, 0, 14.4, 0.187]}, {"Target": "Parameter", "Id": "Physics_Provisional", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "physics_Add_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "physics_Add_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamBodyStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Physics_AngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Physics_AngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Physics_Body_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Physics_Body_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Physics_breast", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamBreath2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "fenshangxuanzhuang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "touch_drag1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "touch_drag2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "touch_drag3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "touch_drag4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "touch_drag5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "touch_drag6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "touch_drag7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "touch_drag8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "touch_drag9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>pian<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh654", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh655", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh657", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh656", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh658", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh659", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh660", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.4, 0]}], "UserData": []}