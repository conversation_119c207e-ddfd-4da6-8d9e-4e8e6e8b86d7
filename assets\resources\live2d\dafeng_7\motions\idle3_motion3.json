{"Version": 3, "Meta": {"Duration": 14.0, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 303, "TotalSegmentCount": 2461, "TotalPointCount": 2631, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.25, 1, 0.606, -0.337, 1.211, -0.425, 1.817, -0.512, 1, 1.822, -0.821, 1.828, -1.131, 1.833, -1.44, 1, 3.05, -1.247, 4.266, -1.055, 5.483, -0.862, 1, 5.489, -1.582, 5.494, -2.302, 5.5, -3.022, 1, 6.806, -2.195, 8.111, -1.367, 9.417, -0.54, 1, 9.422, -0.536, 9.428, -0.533, 9.433, -0.529, 1, 10.394, -0.209, 11.356, 0.111, 12.317, 0.431, 1, 12.322, 0.287, 12.328, 0.144, 12.333, 0, 1, 12.889, -0.083, 13.444, -0.167, 14, -0.25]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.27, 1, 0.606, -1.32, 1.211, -1.37, 1.817, -1.42, 1, 1.822, -2.157, 1.828, -2.893, 1.833, -3.63, 1, 3.05, -3.447, 4.266, -3.265, 5.483, -3.082, 1, 5.489, -1.882, 5.494, -0.682, 5.5, 0.518, 1, 6.806, 0.052, 8.111, -0.414, 9.417, -0.88, 1, 9.422, -0.742, 9.428, -0.604, 9.433, -0.466, 1, 10.4, -0.691, 11.366, -0.915, 12.333, -1.14, 1, 12.889, -1.183, 13.444, -1.227, 14, -1.27]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 3.75, 1, 0.606, 3.828, 1.211, 3.905, 1.817, 3.983, 1, 1.822, 4.743, 1.828, 5.504, 1.833, 6.264, 1, 3.05, 6.296, 4.266, 6.328, 5.483, 6.36, 1, 5.489, 4.356, 5.494, 2.351, 5.5, 0.347, 1, 6.806, 0.515, 8.111, 0.682, 9.417, 0.85, 1, 9.422, 2.431, 9.428, 4.011, 9.433, 5.592, 1, 10.394, 5.352, 11.356, 5.112, 12.317, 4.872, 1, 12.322, 4.428, 12.328, 3.984, 12.333, 3.54, 1, 12.889, 3.61, 13.444, 3.68, 14, 3.75]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 4.05, 1, 0.606, 4.734, 1.211, 5.418, 1.817, 6.102, 1, 1.822, 7.488, 1.828, 8.874, 1.833, 10.26, 1, 3.05, 9.2, 4.266, 8.14, 5.483, 7.08, 1, 5.489, 10.576, 5.494, 14.071, 5.5, 17.567, 1, 6.806, 16.761, 8.111, 15.956, 9.417, 15.15, 1, 9.422, 10.36, 9.428, 5.57, 9.433, 0.78, 1, 10.4, 1.24, 11.366, 1.7, 12.333, 2.16, 1, 12.889, 2.79, 13.444, 3.42, 14, 4.05]}, {"Target": "Parameter", "Id": "BG_White4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.989, 0, 0.117, 1, 0, 0.4, 0.989, 0, 0.717, 1, 0, 1, 0.989, 0, 1.117, 1, 0, 1.4, 0.989, 0, 1.717, 1, 0, 2, 0.989, 0, 2.117, 1, 0, 2.4, 0.989, 0, 2.717, 1, 0, 3, 0.989, 0, 3.383, 1, 0, 3.667, 0.989, 0, 3.783, 1, 0, 4.067, 0.989, 0, 4.383, 1, 0, 4.667, 0.989, 0, 4.783, 1, 0, 5.067, 0.989, 0, 5.383, 1, 0, 5.667, 0.989, 0, 5.783, 1, 0, 6.067, 0.989, 0, 6.383, 1, 0, 6.667, 0.989, 0, 7.05, 1, 0, 7.333, 0.989, 0, 7.45, 1, 0, 7.733, 0.989, 0, 8.05, 1, 0, 8.333, 0.989, 0, 8.45, 1, 0, 8.733, 0.989, 0, 9.05, 1, 0, 9.333, 0.989, 0, 9.45, 1, 0, 9.733, 0.989, 0, 10.05, 1, 0, 10.333, 0.989, 0, 10.717, 1, 0, 11, 0.989, 0, 11.117, 1, 0, 11.4, 0.989, 0, 11.717, 1, 0, 12, 0.989, 0, 12.117, 1, 0, 12.4, 0.989, 0, 12.717, 1, 0, 13, 0.989, 0, 13.117, 1, 0, 13.4, 0.989, 0, 13.717, 1, 0, 14, 0.989]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.467, -1, 2, 3.4, -1, 0, 3.933, 0.1, 0, 4.667, 0, 2, 5.833, 0, 0, 8.567, -0.7, 0, 12.4, 1, 0, 13.333, -0.2, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY_SD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 1.467, 0.5, 2, 3.4, 0.5, 1, 3.578, 0.5, 3.755, 0.826, 3.933, 0.9, 1, 4.178, 1, 4.422, 1, 4.667, 1, 2, 5.833, 1, 0, 8.567, 0.4, 1, 9.845, 0.4, 11.122, 0.439, 12.4, 0.6, 1, 12.711, 0.639, 13.022, 1, 13.333, 1, 2, 14, 1]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 0.267, 1.3, 0, 0.483, 0, 2, 0.933, 0, 0, 1.483, 0.538, 2, 2.267, 0.538, 0, 2.5, 0, 0, 2.617, 1.3, 0, 2.767, 1.252, 2, 3.417, 1.252, 0, 3.567, 0, 1, 3.934, 0, 4.3, 0.307, 4.667, 1, 1, 4.756, 1.168, 4.844, 1.3, 4.933, 1.3, 0, 5.15, 0, 2, 5.6, 0, 1, 5.678, 0, 5.755, 0.733, 5.833, 1, 1, 5.922, 1.3, 6.011, 1.3, 6.1, 1.3, 0, 6.317, 0, 2, 6.767, 0, 2, 11.683, 0, 0, 12.25, 1, 2, 12.75, 1, 2, 13.017, 1, 0, 13.233, 0, 0, 13.417, 1, 2, 14, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 0.267, 1.3, 0, 0.483, 0, 2, 0.933, 0, 0, 1.483, 0.538, 2, 2.267, 0.538, 0, 2.5, 0, 0, 2.617, 1.3, 0, 2.767, 1.252, 2, 3.417, 1.252, 0, 3.567, 0, 1, 3.934, 0, 4.3, 0.307, 4.667, 1, 1, 4.756, 1.168, 4.844, 1.3, 4.933, 1.3, 0, 5.15, 0, 2, 5.6, 0, 1, 5.678, 0, 5.755, 0.733, 5.833, 1, 1, 5.922, 1.3, 6.011, 1.3, 6.1, 1.3, 0, 6.317, 0, 2, 6.767, 0, 2, 11.683, 0, 0, 12.25, 1, 2, 12.75, 1, 2, 13.017, 1, 0, 13.233, 0, 0, 13.417, 1, 2, 14, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.267, 0, 1, 0.395, 0, 0.522, -0.377, 0.65, -0.5, 1, 0.767, -0.613, 0.883, -0.6, 1, -0.6, 1, 1.139, -0.6, 1.278, 0.46, 1.417, 0.5, 1, 1.739, 0.594, 2.061, 0.6, 2.383, 0.6, 0, 2.617, 0, 2, 4.233, 0, 2, 4.667, 0, 2, 4.933, 0, 1, 5.061, 0, 5.189, -0.377, 5.317, -0.5, 1, 5.434, -0.613, 5.55, -0.6, 5.667, -0.6, 0, 5.833, 0, 2, 6.1, 0, 1, 6.228, 0, 6.355, -0.377, 6.483, -0.5, 1, 6.6, -0.613, 6.716, -0.6, 6.833, -0.6, 0, 12.75, 0, 2, 13.017, 0, 0, 13.4, 0.4, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.267, 0, 1, 0.395, 0, 0.522, -0.209, 0.65, -0.4, 1, 0.767, -0.575, 0.883, -0.6, 1, -0.6, 0, 1.417, 0.2, 0, 2.383, 0, 2, 2.617, 0, 2, 4.233, 0, 2, 4.667, 0, 2, 4.933, 0, 1, 5.061, 0, 5.189, -0.209, 5.317, -0.4, 1, 5.434, -0.575, 5.55, -0.6, 5.667, -0.6, 0, 5.833, 0, 2, 6.1, 0, 1, 6.228, 0, 6.355, -0.209, 6.483, -0.4, 1, 6.6, -0.575, 6.716, -0.6, 6.833, -0.6, 0, 12.75, 0, 2, 13.017, 0, 0, 13.4, 1, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamBrowAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.284, 0, 0.45, 0.4, 0, 0.583, 0.284, 0, 1.033, 0.4, 0, 1.167, 0.284, 0, 1.617, 0.4, 0, 1.75, 0.284, 0, 2.2, 0.4, 0, 2.333, 0.284, 0, 2.783, 0.4, 0, 2.917, 0.284, 0, 3.367, 0.4, 0, 3.5, 0.284, 0, 3.95, 0.4, 0, 4.083, 0.284, 0, 4.533, 0.4, 0, 4.667, 0.284, 0, 5.117, 0.4, 0, 5.25, 0.284, 0, 5.7, 0.4, 0, 5.833, 0.284, 0, 6.283, 0.4, 0, 6.417, 0.284, 0, 6.867, 0.4, 0, 7, 0.284, 0, 7.45, 0.4, 0, 7.583, 0.284, 0, 8.033, 0.4, 0, 8.167, 0.284, 0, 8.617, 0.4, 0, 8.75, 0.284, 0, 9.2, 0.4, 0, 9.333, 0.284, 0, 9.783, 0.4, 0, 9.917, 0.284, 0, 10.367, 0.4, 0, 10.5, 0.284, 0, 10.95, 0.4, 0, 11.083, 0.284, 0, 11.533, 0.4, 0, 11.667, 0.284, 0, 12.117, 0.4, 0, 12.25, 0.284, 0, 12.7, 0.4, 0, 12.833, 0.284, 0, 13.283, 0.4, 0, 13.417, 0.284, 0, 13.867, 0.4, 0, 14, 0.284]}, {"Target": "Parameter", "Id": "ParamEyeLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.183, -0.43, 0, 0.35, 1, 2, 0.55, 1, 0, 0.8, -0.344, 0, 1, -0.048, 0, 1.25, -0.331, 0, 1.583, 0.175, 0, 1.9, -0.049, 0, 2.2, 0.014, 0, 2.267, 0.011, 0, 2.433, 0.806, 2, 2.45, 0.806, 0, 2.567, -1, 2, 2.667, -1, 0, 2.683, 1, 2, 2.833, 1, 0, 3.05, -0.418, 0, 3.367, 0.115, 0, 3.417, 0.101, 0, 3.483, 1, 2, 3.833, 1, 0, 4.083, -0.532, 0, 4.367, -0.132, 0, 4.683, -0.351, 0, 5.017, 1, 2, 5.183, 1, 0, 5.433, -0.352, 0, 5.617, -0.051, 0, 5.75, -1, 2, 5.85, -1, 0, 6.167, 1, 2, 6.317, 1, 0, 6.55, -0.396, 0, 6.867, 0.109, 0, 7.167, -0.03, 2, 7.183, -0.03, 0, 7.483, 0.009, 0, 7.783, -0.002, 2, 7.8, -0.002, 0, 8.083, 0.001, 2, 8.117, 0.001, 2, 8.133, 0.001, 2, 8.15, 0.001, 0, 8.167, 0, 2, 8.183, 0, 2, 8.217, 0, 2, 8.233, 0, 2, 8.283, 0, 2, 8.3, 0, 2, 8.317, 0, 2, 8.35, 0, 2, 8.367, 0, 2, 8.467, 0, 2, 8.483, 0, 2, 8.55, 0, 2, 8.567, 0, 2, 8.7, 0, 2, 8.717, 0, 2, 8.733, 0, 2, 8.75, 0, 2, 11.683, 0, 0, 11.967, -0.627, 0, 12.35, 0.299, 0, 12.65, -0.083, 0, 12.967, 0.023, 0, 13.017, 0.02, 0, 13.133, 1, 2, 13.233, 1, 0, 13.367, -1, 2, 13.467, -1, 0, 13.733, 0.303, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamEyeRsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.183, -0.43, 0, 0.35, 1, 2, 0.55, 1, 0, 0.8, -0.344, 0, 1, -0.048, 0, 1.25, -0.331, 0, 1.583, 0.175, 0, 1.9, -0.049, 0, 2.2, 0.014, 0, 2.267, 0.011, 0, 2.433, 0.806, 2, 2.45, 0.806, 0, 2.567, -1, 2, 2.667, -1, 0, 2.683, 1, 2, 2.833, 1, 0, 3.05, -0.418, 0, 3.367, 0.115, 0, 3.417, 0.101, 0, 3.483, 1, 2, 3.833, 1, 0, 4.083, -0.532, 0, 4.367, -0.132, 0, 4.683, -0.351, 0, 5.017, 1, 2, 5.183, 1, 0, 5.433, -0.352, 0, 5.617, -0.051, 0, 5.75, -1, 2, 5.85, -1, 0, 6.167, 1, 2, 6.317, 1, 0, 6.55, -0.396, 0, 6.867, 0.109, 0, 7.167, -0.03, 2, 7.183, -0.03, 0, 7.483, 0.009, 0, 7.783, -0.002, 2, 7.8, -0.002, 0, 8.083, 0.001, 2, 8.117, 0.001, 2, 8.133, 0.001, 2, 8.15, 0.001, 0, 8.167, 0, 2, 8.183, 0, 2, 8.217, 0, 2, 8.233, 0, 2, 8.283, 0, 2, 8.3, 0, 2, 8.317, 0, 2, 8.35, 0, 2, 8.367, 0, 2, 8.467, 0, 2, 8.483, 0, 2, 8.55, 0, 2, 8.567, 0, 2, 8.7, 0, 2, 8.717, 0, 2, 8.733, 0, 2, 8.75, 0, 2, 11.683, 0, 0, 11.967, -0.627, 0, 12.35, 0.299, 0, 12.65, -0.083, 0, 12.967, 0.023, 0, 13.017, 0.02, 0, 13.133, 1, 2, 13.233, 1, 0, 13.367, -1, 2, 13.467, -1, 0, 13.733, 0.303, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamEyeLGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.15, 0.124, 0, 0.333, -0.416, 0, 0.583, 1, 2, 0.767, 1, 0, 1.033, -0.222, 0, 1.267, 0.058, 0, 1.5, -0.115, 0, 1.767, 0.1, 0, 2.05, -0.05, 0, 2.267, 0.009, 0, 2.4, -0.202, 0, 2.55, 0.535, 0, 2.6, 0.281, 0, 2.817, 0.899, 0, 3.2, -0.349, 0, 3.417, 0.065, 0, 3.483, -0.225, 0, 3.55, -0.104, 0, 3.633, -0.204, 0, 3.9, 0.457, 0, 4.217, -0.281, 0, 4.517, 0.136, 0, 4.9, -0.135, 0, 4.933, -0.133, 0, 5, -0.291, 0, 5.233, 1, 2, 5.4, 1, 0, 5.617, -0.174, 0, 5.75, 0.16, 0, 5.983, -0.423, 0, 6.117, -0.09, 0, 6.167, -0.117, 0, 6.417, 0.995, 0, 6.75, -0.318, 0, 7.05, 0.129, 0, 7.35, -0.049, 0, 7.65, 0.018, 0, 7.95, -0.006, 2, 7.967, -0.006, 0, 8.25, 0.002, 2, 8.283, 0.002, 0, 8.5, 0, 2, 8.517, 0, 0, 8.533, -0.001, 2, 8.55, -0.001, 2, 8.567, -0.001, 2, 8.583, -0.001, 2, 8.6, -0.001, 2, 8.617, -0.001, 0, 8.633, 0, 2, 8.65, 0, 2, 8.7, 0, 2, 8.717, 0, 2, 8.75, 0, 2, 8.767, 0, 2, 8.783, 0, 2, 8.8, 0, 2, 8.817, 0, 2, 8.95, 0, 2, 8.967, 0, 2, 9.017, 0, 2, 9.033, 0, 2, 9.117, 0, 2, 9.133, 0, 2, 9.233, 0, 2, 9.25, 0, 2, 11.683, 0, 0, 11.85, 0.137, 0, 12.183, -0.193, 0, 12.5, 0.181, 0, 12.8, -0.089, 0, 13.017, 0.016, 0, 13.117, -0.265, 0, 13.317, 1, 2, 13.333, 1, 0, 13.65, -0.311, 0, 13.917, 0.191, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamEyeRGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.15, 0.124, 0, 0.333, -0.416, 0, 0.583, 1, 2, 0.767, 1, 0, 1.033, -0.222, 0, 1.267, 0.058, 0, 1.5, -0.115, 0, 1.767, 0.1, 0, 2.05, -0.05, 0, 2.267, 0.009, 0, 2.4, -0.202, 0, 2.55, 0.535, 0, 2.6, 0.281, 0, 2.817, 0.899, 0, 3.2, -0.349, 0, 3.417, 0.065, 0, 3.483, -0.225, 0, 3.55, -0.104, 0, 3.633, -0.204, 0, 3.9, 0.457, 0, 4.217, -0.281, 0, 4.517, 0.136, 0, 4.9, -0.135, 0, 4.933, -0.133, 0, 5, -0.291, 0, 5.233, 1, 2, 5.4, 1, 0, 5.617, -0.174, 0, 5.75, 0.16, 0, 5.983, -0.423, 0, 6.117, -0.09, 0, 6.167, -0.117, 0, 6.417, 0.995, 0, 6.75, -0.318, 0, 7.05, 0.129, 0, 7.35, -0.049, 0, 7.65, 0.018, 0, 7.95, -0.006, 2, 7.967, -0.006, 0, 8.25, 0.002, 2, 8.283, 0.002, 0, 8.5, 0, 2, 8.517, 0, 0, 8.533, -0.001, 2, 8.55, -0.001, 2, 8.567, -0.001, 2, 8.583, -0.001, 2, 8.6, -0.001, 2, 8.617, -0.001, 0, 8.633, 0, 2, 8.65, 0, 2, 8.7, 0, 2, 8.717, 0, 2, 8.75, 0, 2, 8.767, 0, 2, 8.783, 0, 2, 8.8, 0, 2, 8.817, 0, 2, 8.95, 0, 2, 8.967, 0, 2, 9.017, 0, 2, 9.033, 0, 2, 9.117, 0, 2, 9.133, 0, 2, 9.233, 0, 2, 9.25, 0, 2, 11.683, 0, 0, 11.85, 0.137, 0, 12.183, -0.193, 0, 12.5, 0.181, 0, 12.8, -0.089, 0, 13.017, 0.016, 0, 13.117, -0.265, 0, 13.317, 1, 2, 13.333, 1, 0, 13.65, -0.311, 0, 13.917, 0.191, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5, 2, 3.6, 5, 0, 3.617, 0, 2, 4.65, 0, 0, 4.667, 5, 2, 6.267, 5, 0, 6.283, 0, 2, 10.25, 0, 2, 11.083, 0, 0, 11.1, -5, 0, 14, 5]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.6, 0, 0, 3.617, -5, 2, 4.65, -5, 0, 4.667, 0, 2, 6.267, 0, 0, 6.283, -5, 2, 10.25, -5, 2, 11.083, -5, 1, 11.089, -5, 11.094, 5, 11.1, 5, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamShoulderLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.548, 0, 0.133, -1, 0, 0.4, -0.548, 0, 0.733, -1, 0, 1, -0.548, 0, 1.133, -1, 0, 1.4, -0.548, 0, 1.733, -1, 0, 2, -0.548, 0, 2.133, -1, 0, 2.4, -0.548, 0, 2.733, -1, 0, 3, -0.548, 0, 3.4, -1, 0, 3.667, -0.548, 0, 3.8, -1, 0, 4.067, -0.548, 0, 4.4, -1, 0, 4.667, -0.548, 0, 4.8, -1, 0, 5.067, -0.548, 0, 5.4, -1, 0, 5.667, -0.548, 0, 5.8, -1, 0, 6.067, -0.548, 0, 6.4, -1, 0, 6.667, -0.548, 0, 7.067, -1, 0, 7.333, -0.548, 0, 7.467, -1, 0, 7.733, -0.548, 0, 8.067, -1, 0, 8.333, -0.548, 0, 8.467, -1, 0, 8.733, -0.548, 0, 9.067, -1, 0, 9.333, -0.548, 0, 9.467, -1, 0, 9.733, -0.548, 0, 10.067, -1, 0, 10.333, -0.548, 0, 10.733, -1, 0, 11, -0.548, 0, 11.133, -1, 0, 11.4, -0.548, 0, 11.733, -1, 0, 12, -0.548, 0, 12.133, -1, 0, 12.4, -0.548, 0, 12.733, -1, 0, 13, -0.548, 0, 13.133, -1, 0, 13.4, -0.548, 0, 13.733, -1, 0, 14, -0.548]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -8.2, 2, 0.4, -8.2, 2, 1, -8.2, 2, 1.4, -8.2, 2, 2, -8.2, 2, 2.4, -8.2, 2, 3, -8.2, 2, 3.667, -8.2, 2, 4.067, -8.2, 2, 4.667, -8.2, 2, 5.067, -8.2, 2, 5.667, -8.2, 2, 6.067, -8.2, 2, 6.667, -8.2, 2, 7.333, -8.2, 2, 7.733, -8.2, 2, 8.333, -8.2, 2, 8.733, -8.2, 2, 9.333, -8.2, 2, 9.733, -8.2, 2, 10.333, -8.2, 2, 11, -8.2, 2, 11.4, -8.2, 2, 12, -8.2, 2, 12.4, -8.2, 2, 13, -8.2, 2, 13.4, -8.2, 2, 14, -8.2]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.582, 0, 0.217, -0.377, 0, 0.4, -0.582, 0, 0.817, -0.377, 0, 1, -0.582, 0, 1.217, -0.377, 0, 1.4, -0.582, 0, 1.817, -0.377, 0, 2, -0.582, 0, 2.217, -0.377, 0, 2.4, -0.582, 0, 2.817, -0.377, 0, 3, -0.582, 0, 3.483, -0.377, 0, 3.667, -0.582, 0, 3.883, -0.377, 0, 4.067, -0.582, 0, 4.483, -0.377, 0, 4.667, -0.582, 0, 4.883, -0.377, 0, 5.067, -0.582, 0, 5.483, -0.377, 0, 5.667, -0.582, 0, 5.883, -0.377, 0, 6.067, -0.582, 0, 6.483, -0.377, 0, 6.667, -0.582, 0, 7.15, -0.377, 0, 7.333, -0.582, 0, 7.55, -0.377, 0, 7.733, -0.582, 0, 8.15, -0.377, 0, 8.333, -0.582, 0, 8.55, -0.377, 0, 8.733, -0.582, 0, 9.15, -0.377, 0, 9.333, -0.582, 0, 9.55, -0.377, 0, 9.733, -0.582, 0, 10.15, -0.377, 0, 10.333, -0.582, 0, 10.817, -0.377, 0, 11, -0.582, 0, 11.217, -0.377, 0, 11.4, -0.582, 0, 11.817, -0.377, 0, 12, -0.582, 0, 12.217, -0.377, 0, 12.4, -0.582, 0, 12.817, -0.377, 0, 13, -0.582, 0, 13.217, -0.377, 0, 13.4, -0.582, 0, 13.817, -0.377, 0, 14, -0.582]}, {"Target": "Parameter", "Id": "ParamShoulderRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.548, 0, 0.133, -1, 0, 0.4, -0.548, 0, 0.733, -1, 0, 1, -0.548, 0, 1.133, -1, 0, 1.4, -0.548, 0, 1.733, -1, 0, 2, -0.548, 0, 2.133, -1, 0, 2.4, -0.548, 0, 2.733, -1, 0, 3, -0.548, 0, 3.4, -1, 0, 3.667, -0.548, 0, 3.8, -1, 0, 4.067, -0.548, 0, 4.4, -1, 0, 4.667, -0.548, 0, 4.8, -1, 0, 5.067, -0.548, 0, 5.4, -1, 0, 5.667, -0.548, 0, 5.8, -1, 0, 6.067, -0.548, 0, 6.4, -1, 0, 6.667, -0.548, 0, 7.067, -1, 0, 7.333, -0.548, 0, 7.467, -1, 0, 7.733, -0.548, 0, 8.067, -1, 0, 8.333, -0.548, 0, 8.467, -1, 0, 8.733, -0.548, 0, 9.067, -1, 0, 9.333, -0.548, 0, 9.467, -1, 0, 9.733, -0.548, 0, 10.067, -1, 0, 10.333, -0.548, 0, 10.733, -1, 0, 11, -0.548, 0, 11.133, -1, 0, 11.4, -0.548, 0, 11.733, -1, 0, 12, -0.548, 0, 12.133, -1, 0, 12.4, -0.548, 0, 12.733, -1, 0, 13, -0.548, 0, 13.133, -1, 0, 13.4, -0.548, 0, 13.733, -1, 0, 14, -0.548]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.578, 0, 0.217, -0.323, 0, 0.4, -0.578, 0, 0.817, -0.323, 0, 1, -0.578, 0, 1.217, -0.323, 0, 1.4, -0.578, 0, 1.817, -0.323, 0, 2, -0.578, 0, 2.217, -0.323, 0, 2.4, -0.578, 0, 2.817, -0.323, 0, 3, -0.578, 0, 3.483, -0.323, 0, 3.667, -0.578, 0, 3.883, -0.323, 0, 4.067, -0.578, 0, 4.483, -0.323, 0, 4.667, -0.578, 0, 4.883, -0.323, 0, 5.067, -0.578, 0, 5.483, -0.323, 0, 5.667, -0.578, 0, 5.883, -0.323, 0, 6.067, -0.578, 0, 6.483, -0.323, 0, 6.667, -0.578, 0, 7.15, -0.323, 0, 7.333, -0.578, 0, 7.55, -0.323, 0, 7.733, -0.578, 0, 8.15, -0.323, 0, 8.333, -0.578, 0, 8.55, -0.323, 0, 8.733, -0.578, 0, 9.15, -0.323, 0, 9.333, -0.578, 0, 9.55, -0.323, 0, 9.733, -0.578, 0, 10.15, -0.323, 0, 10.333, -0.578, 0, 10.817, -0.323, 0, 11, -0.578, 0, 11.217, -0.323, 0, 11.4, -0.578, 0, 11.817, -0.323, 0, 12, -0.578, 0, 12.217, -0.323, 0, 12.4, -0.578, 0, 12.817, -0.323, 0, 13, -0.578, 0, 13.217, -0.323, 0, 13.4, -0.578, 0, 13.817, -0.323, 0, 14, -0.578]}, {"Target": "Parameter", "Id": "Change_idle_physics12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.479, 0, 0.183, 0.8, 0, 0.4, -0.479, 0, 0.783, 0.8, 0, 1, -0.479, 0, 1.183, 0.8, 0, 1.4, -0.479, 0, 1.783, 0.8, 0, 2, -0.479, 0, 2.183, 0.8, 0, 2.4, -0.479, 0, 2.783, 0.8, 0, 3, -0.479, 0, 3.45, 0.8, 0, 3.667, -0.479, 0, 3.85, 0.8, 0, 4.067, -0.479, 0, 4.45, 0.8, 0, 4.667, -0.479, 0, 4.85, 0.8, 0, 5.067, -0.479, 0, 5.45, 0.8, 0, 5.667, -0.479, 0, 5.85, 0.8, 0, 6.067, -0.479, 0, 6.45, 0.8, 0, 6.667, -0.479, 0, 7.117, 0.8, 0, 7.333, -0.479, 0, 7.517, 0.8, 0, 7.733, -0.479, 0, 8.117, 0.8, 0, 8.333, -0.479, 0, 8.517, 0.8, 0, 8.733, -0.479, 0, 9.117, 0.8, 0, 9.333, -0.479, 0, 9.517, 0.8, 0, 9.733, -0.479, 0, 10.117, 0.8, 0, 10.333, -0.479, 0, 10.783, 0.8, 0, 11, -0.479, 0, 11.183, 0.8, 0, 11.4, -0.479, 0, 11.783, 0.8, 0, 12, -0.479, 0, 12.183, 0.8, 0, 12.4, -0.479, 0, 12.783, 0.8, 0, 13, -0.479, 0, 13.183, 0.8, 0, 13.4, -0.479, 0, 13.783, 0.8, 0, 14, -0.479]}, {"Target": "Parameter", "Id": "ParamAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -6.66, 0, 0.183, 4.883, 0, 0.4, -6.66, 0, 0.783, 4.883, 0, 1, -6.66, 0, 1.183, 4.883, 0, 1.4, -6.66, 0, 1.783, 4.883, 0, 2, -6.66, 0, 2.183, 4.883, 0, 2.4, -6.66, 0, 2.783, 4.883, 0, 3, -6.66, 0, 3.45, 4.883, 0, 3.667, -6.66, 0, 3.85, 4.883, 0, 4.067, -6.66, 0, 4.45, 4.883, 0, 4.667, -6.66, 0, 4.85, 4.883, 0, 5.067, -6.66, 0, 5.45, 4.883, 0, 5.667, -6.66, 0, 5.85, 4.883, 0, 6.067, -6.66, 0, 6.45, 4.883, 0, 6.667, -6.66, 0, 7.117, 4.883, 0, 7.333, -6.66, 0, 7.517, 4.883, 0, 7.733, -6.66, 0, 8.117, 4.883, 0, 8.333, -6.66, 0, 8.517, 4.883, 0, 8.733, -6.66, 0, 9.117, 4.883, 0, 9.333, -6.66, 0, 9.517, 4.883, 0, 9.733, -6.66, 0, 10.117, 4.883, 0, 10.333, -6.66, 0, 10.783, 4.883, 0, 11, -6.66, 0, 11.183, 4.883, 0, 11.4, -6.66, 0, 11.783, 4.883, 0, 12, -6.66, 0, 12.183, 4.883, 0, 12.4, -6.66, 0, 12.783, 4.883, 0, 13, -6.66, 0, 13.183, 4.883, 0, 13.4, -6.66, 0, 13.783, 4.883, 0, 14, -6.66]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.009, 0, 0.183, 2, 0, 0.4, -2.009, 0, 0.783, 2, 0, 1, -2.009, 0, 1.183, 2, 0, 1.4, -2.009, 0, 1.783, 2, 0, 2, -2.009, 0, 2.183, 2, 0, 2.4, -2.009, 0, 2.783, 2, 0, 3, -2.009, 0, 3.45, 2, 0, 3.667, -2.009, 0, 3.85, 2, 0, 4.067, -2.009, 0, 4.45, 2, 0, 4.667, -2.009, 0, 4.85, 2, 0, 5.067, -2.009, 0, 5.45, 2, 0, 5.667, -2.009, 0, 5.85, 2, 0, 6.067, -2.009, 0, 6.45, 2, 0, 6.667, -2.009, 0, 7.117, 2, 0, 7.333, -2.009, 0, 7.517, 2, 0, 7.733, -2.009, 0, 8.117, 2, 0, 8.333, -2.009, 0, 8.517, 2, 0, 8.733, -2.009, 0, 9.117, 2, 0, 9.333, -2.009, 0, 9.517, 2, 0, 9.733, -2.009, 0, 10.117, 2, 0, 10.333, -2.009, 0, 10.783, 2, 0, 11, -2.009, 0, 11.183, 2, 0, 11.4, -2.009, 0, 11.783, 2, 0, 12, -2.009, 0, 12.183, 2, 0, 12.4, -2.009, 0, 12.783, 2, 0, 13, -2.009, 0, 13.183, 2, 0, 13.4, -2.009, 0, 13.783, 2, 0, 14, -2.009]}, {"Target": "Parameter", "Id": "ParamBodyStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.136, 0, 0.133, 5.027, 0, 0.333, -7.331, 0, 0.733, 5.027, 0, 0.933, -7.331, 1, 0.955, -7.331, 0.978, -3.571, 1, -1.136, 1, 1.044, 3.733, 1.089, 5.027, 1.133, 5.027, 0, 1.333, -7.331, 0, 1.733, 5.027, 0, 1.933, -7.331, 1, 1.955, -7.331, 1.978, -3.571, 2, -1.136, 1, 2.044, 3.733, 2.089, 5.027, 2.133, 5.027, 0, 2.333, -7.331, 0, 2.733, 5.027, 0, 2.933, -7.331, 0, 3.4, 5.027, 0, 3.6, -7.331, 1, 3.622, -7.331, 3.645, -3.571, 3.667, -1.136, 1, 3.711, 3.733, 3.756, 5.027, 3.8, 5.027, 0, 4, -7.331, 0, 4.4, 5.027, 0, 4.6, -7.331, 1, 4.622, -7.331, 4.645, -3.571, 4.667, -1.136, 1, 4.711, 3.733, 4.756, 5.027, 4.8, 5.027, 0, 5, -7.331, 0, 5.4, 5.027, 0, 5.6, -7.331, 1, 5.622, -7.331, 5.645, -3.571, 5.667, -1.136, 1, 5.711, 3.733, 5.756, 5.027, 5.8, 5.027, 0, 6, -7.331, 0, 6.4, 5.027, 0, 6.6, -7.331, 0, 7.067, 5.027, 0, 7.267, -7.331, 1, 7.289, -7.331, 7.311, -3.571, 7.333, -1.136, 1, 7.378, 3.733, 7.422, 5.027, 7.467, 5.027, 0, 7.667, -7.331, 0, 8.067, 5.027, 0, 8.267, -7.331, 1, 8.289, -7.331, 8.311, -3.571, 8.333, -1.136, 1, 8.378, 3.733, 8.422, 5.027, 8.467, 5.027, 0, 8.667, -7.331, 0, 9.067, 5.027, 0, 9.267, -7.331, 1, 9.289, -7.331, 9.311, -3.571, 9.333, -1.136, 1, 9.378, 3.733, 9.422, 5.027, 9.467, 5.027, 0, 9.667, -7.331, 0, 10.067, 5.027, 0, 10.267, -7.331, 0, 10.733, 5.027, 0, 10.933, -7.331, 1, 10.955, -7.331, 10.978, -3.571, 11, -1.136, 1, 11.044, 3.733, 11.089, 5.027, 11.133, 5.027, 0, 11.333, -7.331, 0, 11.733, 5.027, 0, 11.933, -7.331, 1, 11.955, -7.331, 11.978, -3.571, 12, -1.136, 1, 12.044, 3.733, 12.089, 5.027, 12.133, 5.027, 0, 12.333, -7.331, 0, 12.733, 5.027, 0, 12.933, -7.331, 1, 12.955, -7.331, 12.978, -3.571, 13, -1.136, 1, 13.044, 3.733, 13.089, 5.027, 13.133, 5.027, 0, 13.333, -7.331, 0, 13.733, 5.027, 0, 13.933, -7.331, 0, 14, -1.136]}, {"Target": "Parameter", "Id": "Physics_AngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.836, 0, 0.233, 3.092, 0, 0.483, -4.158, 0, 0.817, 3.431, 0, 1.05, -3.956, 0, 1.25, 2.49, 0, 1.483, -4.112, 0, 1.817, 3.43, 0, 2.05, -3.957, 0, 2.25, 2.491, 0, 2.483, -4.112, 0, 2.817, 3.43, 0, 3.083, -4.475, 0, 3.467, 3.468, 0, 3.717, -4.022, 0, 3.917, 2.495, 0, 4.15, -4.11, 0, 4.483, 3.43, 0, 4.717, -3.957, 0, 4.917, 2.491, 0, 5.15, -4.112, 0, 5.483, 3.43, 0, 5.717, -3.957, 0, 5.917, 2.491, 0, 6.15, -4.112, 0, 6.483, 3.43, 0, 6.75, -4.475, 0, 7.133, 3.468, 0, 7.383, -4.022, 0, 7.583, 2.495, 0, 7.817, -4.11, 0, 8.15, 3.43, 0, 8.383, -3.957, 0, 8.583, 2.491, 0, 8.817, -4.112, 0, 9.15, 3.43, 0, 9.383, -3.957, 0, 9.583, 2.491, 0, 9.817, -4.112, 0, 10.15, 3.43, 0, 10.417, -4.475, 0, 10.8, 3.468, 0, 11.05, -4.022, 0, 11.25, 2.495, 0, 11.483, -4.11, 0, 11.817, 3.43, 0, 12.05, -3.957, 0, 12.25, 2.491, 0, 12.483, -4.112, 0, 12.817, 3.43, 0, 13.05, -3.957, 0, 13.25, 2.491, 0, 13.483, -4.112, 0, 13.817, 3.43, 0, 14, -3.836]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.567, 0, 0.167, -12.025, 0, 0.4, 15.028, 0, 0.717, -14.187, 0, 1, 16.599, 0, 1.2, -8.612, 0, 1.383, 7.367, 0, 1.7, -11.014, 0, 1.983, 18.305, 0, 2.2, -11.75, 0, 2.4, 4.962, 0, 2.683, -9.01, 0, 2.983, 17.989, 0, 3.333, -17.571, 0, 3.65, 22.361, 0, 3.883, -10.6, 0, 4.067, 1.896, 0, 4.333, -8.635, 0, 4.633, 19.137, 0, 4.867, -14.032, 0, 5.083, 4.446, 0, 5.367, -7.726, 0, 5.65, 17.172, 0, 5.867, -13.353, 0, 6.067, 5.66, 0, 6.367, -8.549, 0, 6.65, 17.197, 0, 7, -17.06, 0, 7.317, 22.195, 0, 7.533, -10.569, 0, 7.733, 2.513, 0, 8, -8.789, 0, 8.317, 18.966, 0, 8.533, -13.79, 0, 8.733, 4.519, 0, 9.033, -7.873, 0, 9.317, 17.253, 0, 9.533, -13.295, 0, 9.733, 5.61, 0, 10.033, -8.554, 0, 10.317, 17.23, 0, 10.667, -17.085, 0, 10.983, 22.207, 0, 11.2, -10.559, 0, 11.4, 2.506, 0, 11.667, -8.793, 0, 11.983, 18.972, 0, 12.2, -13.792, 0, 12.4, 4.515, 0, 12.7, -7.87, 0, 12.983, 17.253, 0, 13.2, -13.297, 0, 13.4, 5.61, 0, 13.7, -8.553, 0, 13.983, 17.23, 0, 14, 2.567]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 3.418, 0, 0.117, 12.585, 0, 0.333, -30, 0, 0.567, 30, 2, 0.65, 30, 0, 0.85, -30, 2, 0.967, -30, 0, 1.133, 30, 2, 1.2, 30, 0, 1.383, -15.938, 0, 1.617, 14.835, 0, 1.883, -30, 2, 1.933, -30, 0, 2.1, 30, 2, 2.217, 30, 0, 2.383, -20.084, 0, 2.633, 11.018, 0, 2.9, -28.622, 0, 3.117, 30, 2, 3.25, 30, 0, 3.45, -30, 2, 3.633, -30, 0, 3.75, 30, 2, 3.9, 30, 0, 4.067, -17.164, 0, 4.3, 4.269, 0, 4.567, -27.011, 0, 4.75, 30, 2, 4.883, 30, 0, 5.067, -24.917, 0, 5.3, 10.913, 0, 5.567, -26.014, 0, 5.767, 30, 2, 5.867, 30, 1, 5.928, 30, 5.989, -23.875, 6.05, -23.875, 0, 6.3, 13.1, 0, 6.567, -27.736, 0, 6.783, 30, 2, 6.917, 30, 0, 7.117, -30, 2, 7.283, -30, 1, 7.328, -30, 7.372, 30, 7.417, 30, 2, 7.567, 30, 0, 7.733, -18.186, 0, 7.95, 5.801, 0, 8.233, -26.607, 0, 8.433, 30, 2, 8.55, 30, 0, 8.733, -23.588, 0, 8.967, 10.694, 0, 9.233, -27.086, 0, 9.433, 30, 2, 9.533, 30, 0, 9.717, -22.899, 0, 9.967, 12.661, 0, 10.233, -27.661, 0, 10.45, 30, 2, 10.583, 30, 0, 10.783, -30, 2, 10.95, -30, 0, 11.083, 30, 2, 11.233, 30, 0, 11.4, -18.165, 0, 11.617, 5.784, 0, 11.9, -26.614, 0, 12.1, 30, 2, 12.217, 30, 0, 12.4, -23.593, 0, 12.633, 10.688, 0, 12.9, -27.124, 0, 13.1, 30, 2, 13.2, 30, 0, 13.383, -22.914, 0, 13.633, 12.664, 0, 13.9, -27.66, 0, 14, 3.418]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_L", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.554, 0, 0.183, 1.139, 0, 0.4, -1.554, 0, 0.783, 1.139, 0, 1, -1.554, 0, 1.183, 1.139, 0, 1.4, -1.554, 0, 1.783, 1.139, 0, 2, -1.554, 0, 2.183, 1.139, 0, 2.4, -1.554, 0, 2.783, 1.139, 0, 3, -1.554, 0, 3.45, 1.139, 0, 3.667, -1.554, 0, 3.85, 1.139, 0, 4.067, -1.554, 0, 4.45, 1.139, 0, 4.667, -1.554, 0, 4.85, 1.139, 0, 5.067, -1.554, 0, 5.45, 1.139, 0, 5.667, -1.554, 0, 5.85, 1.139, 0, 6.067, -1.554, 0, 6.45, 1.139, 0, 6.667, -1.554, 0, 7.117, 1.139, 0, 7.333, -1.554, 0, 7.517, 1.139, 0, 7.733, -1.554, 0, 8.117, 1.139, 0, 8.333, -1.554, 0, 8.517, 1.139, 0, 8.733, -1.554, 0, 9.117, 1.139, 0, 9.333, -1.554, 0, 9.517, 1.139, 0, 9.733, -1.554, 0, 10.117, 1.139, 0, 10.333, -1.554, 0, 10.783, 1.139, 0, 11, -1.554, 0, 11.183, 1.139, 0, 11.4, -1.554, 0, 11.783, 1.139, 0, 12, -1.554, 0, 12.183, 1.139, 0, 12.4, -1.554, 0, 12.783, 1.139, 0, 13, -1.554, 0, 13.183, 1.139, 0, 13.4, -1.554, 0, 13.783, 1.139, 0, 14, -1.554]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_R", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.554, 0, 0.183, -1.139, 0, 0.4, 1.554, 0, 0.783, -1.139, 0, 1, 1.554, 0, 1.183, -1.139, 0, 1.4, 1.554, 0, 1.783, -1.139, 0, 2, 1.554, 0, 2.183, -1.139, 0, 2.4, 1.554, 0, 2.783, -1.139, 0, 3, 1.554, 0, 3.45, -1.139, 0, 3.667, 1.554, 0, 3.85, -1.139, 0, 4.067, 1.554, 0, 4.45, -1.139, 0, 4.667, 1.554, 0, 4.85, -1.139, 0, 5.067, 1.554, 0, 5.45, -1.139, 0, 5.667, 1.554, 0, 5.85, -1.139, 0, 6.067, 1.554, 0, 6.45, -1.139, 0, 6.667, 1.554, 0, 7.117, -1.139, 0, 7.333, 1.554, 0, 7.517, -1.139, 0, 7.733, 1.554, 0, 8.117, -1.139, 0, 8.333, 1.554, 0, 8.517, -1.139, 0, 8.733, 1.554, 0, 9.117, -1.139, 0, 9.333, 1.554, 0, 9.517, -1.139, 0, 9.733, 1.554, 0, 10.117, -1.139, 0, 10.333, 1.554, 0, 10.783, -1.139, 0, 11, 1.554, 0, 11.183, -1.139, 0, 11.4, 1.554, 0, 11.783, -1.139, 0, 12, 1.554, 0, 12.183, -1.139, 0, 12.4, 1.554, 0, 12.783, -1.139, 0, 13, 1.554, 0, 13.183, -1.139, 0, 13.4, 1.554, 0, 13.783, -1.139, 0, 14, 1.554]}, {"Target": "Parameter", "Id": "L_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 8.881, 0, 0.183, -6.511, 0, 0.4, 8.881, 0, 0.783, -6.511, 0, 1, 8.881, 0, 1.183, -6.511, 0, 1.4, 8.881, 0, 1.783, -6.511, 0, 2, 8.881, 0, 2.183, -6.511, 0, 2.4, 8.881, 0, 2.783, -6.511, 0, 3, 8.881, 0, 3.45, -6.511, 0, 3.667, 8.881, 0, 3.85, -6.511, 0, 4.067, 8.881, 0, 4.45, -6.511, 0, 4.667, 8.881, 0, 4.85, -6.511, 0, 5.067, 8.881, 0, 5.45, -6.511, 0, 5.667, 8.881, 0, 5.85, -6.511, 0, 6.067, 8.881, 0, 6.45, -6.511, 0, 6.667, 8.881, 0, 7.117, -6.511, 0, 7.333, 8.881, 0, 7.517, -6.511, 0, 7.733, 8.881, 0, 8.117, -6.511, 0, 8.333, 8.881, 0, 8.517, -6.511, 0, 8.733, 8.881, 0, 9.117, -6.511, 0, 9.333, 8.881, 0, 9.517, -6.511, 0, 9.733, 8.881, 0, 10.117, -6.511, 0, 10.333, 8.881, 0, 10.783, -6.511, 0, 11, 8.881, 0, 11.183, -6.511, 0, 11.4, 8.881, 0, 11.783, -6.511, 0, 12, 8.881, 0, 12.183, -6.511, 0, 12.4, 8.881, 0, 12.783, -6.511, 0, 13, 8.881, 0, 13.183, -6.511, 0, 13.4, 8.881, 0, 13.783, -6.511, 0, 14, 8.881]}, {"Target": "Parameter", "Id": "L_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -8.881, 0, 0.183, 6.511, 0, 0.4, -8.881, 0, 0.783, 6.511, 0, 1, -8.881, 0, 1.183, 6.511, 0, 1.4, -8.881, 0, 1.783, 6.511, 0, 2, -8.881, 0, 2.183, 6.511, 0, 2.4, -8.881, 0, 2.783, 6.511, 0, 3, -8.881, 0, 3.45, 6.511, 0, 3.667, -8.881, 0, 3.85, 6.511, 0, 4.067, -8.881, 0, 4.45, 6.511, 0, 4.667, -8.881, 0, 4.85, 6.511, 0, 5.067, -8.881, 0, 5.45, 6.511, 0, 5.667, -8.881, 0, 5.85, 6.511, 0, 6.067, -8.881, 0, 6.45, 6.511, 0, 6.667, -8.881, 0, 7.117, 6.511, 0, 7.333, -8.881, 0, 7.517, 6.511, 0, 7.733, -8.881, 0, 8.117, 6.511, 0, 8.333, -8.881, 0, 8.517, 6.511, 0, 8.733, -8.881, 0, 9.117, 6.511, 0, 9.333, -8.881, 0, 9.517, 6.511, 0, 9.733, -8.881, 0, 10.117, 6.511, 0, 10.333, -8.881, 0, 10.783, 6.511, 0, 11, -8.881, 0, 11.183, 6.511, 0, 11.4, -8.881, 0, 11.783, 6.511, 0, 12, -8.881, 0, 12.183, 6.511, 0, 12.4, -8.881, 0, 12.783, 6.511, 0, 13, -8.881, 0, 13.183, 6.511, 0, 13.4, -8.881, 0, 13.783, 6.511, 0, 14, -8.881]}, {"Target": "Parameter", "Id": "R_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -8.881, 0, 0.183, 6.511, 0, 0.4, -8.881, 0, 0.783, 6.511, 0, 1, -8.881, 0, 1.183, 6.511, 0, 1.4, -8.881, 0, 1.783, 6.511, 0, 2, -8.881, 0, 2.183, 6.511, 0, 2.4, -8.881, 0, 2.783, 6.511, 0, 3, -8.881, 0, 3.45, 6.511, 0, 3.667, -8.881, 0, 3.85, 6.511, 0, 4.067, -8.881, 0, 4.45, 6.511, 0, 4.667, -8.881, 0, 4.85, 6.511, 0, 5.067, -8.881, 0, 5.45, 6.511, 0, 5.667, -8.881, 0, 5.85, 6.511, 0, 6.067, -8.881, 0, 6.45, 6.511, 0, 6.667, -8.881, 0, 7.117, 6.511, 0, 7.333, -8.881, 0, 7.517, 6.511, 0, 7.733, -8.881, 0, 8.117, 6.511, 0, 8.333, -8.881, 0, 8.517, 6.511, 0, 8.733, -8.881, 0, 9.117, 6.511, 0, 9.333, -8.881, 0, 9.517, 6.511, 0, 9.733, -8.881, 0, 10.117, 6.511, 0, 10.333, -8.881, 0, 10.783, 6.511, 0, 11, -8.881, 0, 11.183, 6.511, 0, 11.4, -8.881, 0, 11.783, 6.511, 0, 12, -8.881, 0, 12.183, 6.511, 0, 12.4, -8.881, 0, 12.783, 6.511, 0, 13, -8.881, 0, 13.183, 6.511, 0, 13.4, -8.881, 0, 13.783, 6.511, 0, 14, -8.881]}, {"Target": "Parameter", "Id": "R_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 8.881, 0, 0.183, -6.511, 0, 0.4, 8.881, 0, 0.783, -6.511, 0, 1, 8.881, 0, 1.183, -6.511, 0, 1.4, 8.881, 0, 1.783, -6.511, 0, 2, 8.881, 0, 2.183, -6.511, 0, 2.4, 8.881, 0, 2.783, -6.511, 0, 3, 8.881, 0, 3.45, -6.511, 0, 3.667, 8.881, 0, 3.85, -6.511, 0, 4.067, 8.881, 0, 4.45, -6.511, 0, 4.667, 8.881, 0, 4.85, -6.511, 0, 5.067, 8.881, 0, 5.45, -6.511, 0, 5.667, 8.881, 0, 5.85, -6.511, 0, 6.067, 8.881, 0, 6.45, -6.511, 0, 6.667, 8.881, 0, 7.117, -6.511, 0, 7.333, 8.881, 0, 7.517, -6.511, 0, 7.733, 8.881, 0, 8.117, -6.511, 0, 8.333, 8.881, 0, 8.517, -6.511, 0, 8.733, 8.881, 0, 9.117, -6.511, 0, 9.333, 8.881, 0, 9.517, -6.511, 0, 9.733, 8.881, 0, 10.117, -6.511, 0, 10.333, 8.881, 0, 10.783, -6.511, 0, 11, 8.881, 0, 11.183, -6.511, 0, 11.4, 8.881, 0, 11.783, -6.511, 0, 12, 8.881, 0, 12.183, -6.511, 0, 12.4, 8.881, 0, 12.783, -6.511, 0, 13, 8.881, 0, 13.183, -6.511, 0, 13.4, 8.881, 0, 13.783, -6.511, 0, 14, 8.881]}, {"Target": "Parameter", "Id": "Jewelry_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.205, 0, 0.167, -0.952, 0, 0.4, 1.156, 0, 0.717, -1.127, 0, 1, 1.324, 0, 1.2, -0.69, 0, 1.383, 0.483, 0, 1.683, -0.861, 0, 1.983, 1.5, 0, 2.2, -0.955, 0, 2.4, 0.358, 0, 2.683, -0.688, 0, 2.983, 1.418, 0, 3.333, -1.388, 0, 3.65, 1.766, 0, 3.867, -0.8, 0, 4.067, 0.194, 0, 4.333, -0.713, 0, 4.65, 1.517, 0, 4.867, -1.077, 0, 5.067, 0.345, 0, 5.367, -0.624, 0, 5.65, 1.371, 0, 5.867, -1.045, 0, 6.067, 0.437, 0, 6.367, -0.676, 0, 6.65, 1.365, 0, 7, -1.349, 0, 7.317, 1.748, 0, 7.533, -0.816, 0, 7.733, 0.205, 0, 8, -0.708, 0, 8.317, 1.521, 0, 8.533, -1.077, 0, 8.733, 0.342, 0, 9.033, -0.622, 0, 9.317, 1.372, 0, 9.533, -1.046, 0, 9.733, 0.436, 0, 10.033, -0.676, 0, 10.317, 1.365, 0, 10.667, -1.349, 0, 10.983, 1.748, 0, 11.2, -0.816, 0, 11.4, 0.205, 0, 11.667, -0.708, 0, 11.983, 1.52, 0, 12.2, -1.077, 0, 12.4, 0.342, 0, 12.7, -0.622, 0, 12.983, 1.371, 0, 13.2, -1.046, 0, 13.4, 0.436, 0, 13.7, -0.676, 0, 13.983, 1.365, 0, 14, 0.205]}, {"Target": "Parameter", "Id": "Jewelry_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.267, 0, 0.117, 0.993, 0, 0.333, -2.285, 0, 0.6, 2.639, 0, 0.917, -3.157, 0, 1.167, 2.857, 0, 1.383, -1.109, 0, 1.617, 0.995, 0, 1.9, -2.537, 0, 2.15, 3.324, 0, 2.4, -1.621, 0, 2.633, 0.816, 0, 2.9, -2.193, 0, 3.183, 3.208, 0, 3.55, -3.723, 0, 3.833, 3.872, 0, 4.067, -1.335, 0, 4.283, 0.451, 0, 4.567, -2.2, 0, 4.817, 3.44, 0, 5.067, -1.868, 0, 5.3, 0.835, 0, 5.567, -2.069, 0, 5.817, 3.129, 0, 6.05, -1.834, 0, 6.3, 1.004, 0, 6.567, -2.178, 0, 6.85, 3.103, 0, 7.217, -3.648, 0, 7.5, 3.846, 0, 7.733, -1.373, 0, 7.95, 0.478, 0, 8.233, -2.227, 0, 8.483, 3.45, 0, 8.733, -1.867, 0, 8.967, 0.829, 0, 9.233, -2.066, 0, 9.483, 3.13, 0, 9.717, -1.836, 0, 9.967, 1.004, 0, 10.233, -2.176, 0, 10.517, 3.102, 0, 10.883, -3.647, 0, 11.167, 3.847, 0, 11.4, -1.373, 0, 11.617, 0.478, 0, 11.9, -2.225, 0, 12.15, 3.449, 0, 12.4, -1.867, 0, 12.633, 0.829, 0, 12.9, -2.066, 0, 13.15, 3.13, 0, 13.383, -1.836, 0, 13.633, 1.004, 0, 13.9, -2.177, 0, 14, 0.267]}, {"Target": "Parameter", "Id": "Jewelry_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.176, 0, 0.233, 1.406, 0, 0.483, -2.546, 0, 0.767, 3.144, 0, 1.067, -3.754, 0, 1.317, 3.054, 0, 1.567, -1.503, 0, 1.8, 1.399, 0, 2.05, -2.793, 0, 2.3, 3.217, 0, 2.567, -2.022, 0, 2.817, 1.485, 0, 3.067, -2.546, 0, 3.35, 3.5, 0, 3.683, -4.258, 0, 3.967, 4.136, 0, 4.233, -2.08, 0, 4.483, 1.173, 0, 4.717, -2.443, 0, 4.967, 3.228, 0, 5.217, -2.227, 0, 5.483, 1.572, 0, 5.733, -2.455, 0, 5.967, 2.967, 0, 6.217, -2.111, 0, 6.483, 1.672, 0, 6.733, -2.638, 0, 7.017, 3.454, 0, 7.35, -4.235, 0, 7.633, 4.119, 0, 7.9, -2.105, 0, 8.15, 1.224, 0, 8.383, -2.479, 0, 8.633, 3.242, 0, 8.883, -2.226, 0, 9.15, 1.562, 0, 9.4, -2.451, 0, 9.633, 2.968, 0, 9.883, -2.113, 0, 10.15, 1.673, 0, 10.4, -2.645, 0, 10.683, 3.456, 0, 11.017, -4.235, 0, 11.3, 4.119, 0, 11.567, -2.105, 0, 11.817, 1.224, 0, 12.05, -2.478, 0, 12.3, 3.241, 0, 12.55, -2.225, 0, 12.817, 1.563, 0, 13.067, -2.451, 0, 13.3, 2.968, 0, 13.55, -2.113, 0, 13.817, 1.673, 0, 14, -0.176]}, {"Target": "Parameter", "Id": "Physics_Joint_5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.181, 0, 0.133, -1.112, 0, 0.333, 1.61, 0, 0.65, -1.847, 0, 0.933, 2.463, 0, 1.15, -0.773, 0, 1.317, 1.036, 0, 1.633, -1.792, 0, 1.933, 2.765, 0, 2.15, -0.846, 0, 2.317, 0.853, 0, 2.617, -1.703, 0, 2.933, 2.781, 0, 3.3, -2.369, 0, 3.6, 2.747, 0, 3.817, -0.57, 0, 3.983, 0.932, 0, 4.283, -1.853, 0, 4.6, 2.874, 0, 4.817, -0.836, 0, 4.983, 0.793, 0, 5.283, -1.695, 0, 5.6, 2.81, 0, 5.817, -0.916, 0, 5.983, 0.808, 0, 6.283, -1.66, 0, 6.6, 2.774, 0, 6.967, -2.373, 0, 7.267, 2.759, 0, 7.483, -0.57, 0, 7.65, 0.929, 0, 7.95, -1.849, 0, 8.267, 2.872, 0, 8.483, -0.837, 0, 8.65, 0.793, 0, 8.95, -1.695, 0, 9.267, 2.81, 0, 9.483, -0.916, 0, 9.65, 0.809, 0, 9.95, -1.66, 0, 10.267, 2.774, 0, 10.633, -2.372, 0, 10.933, 2.759, 0, 11.15, -0.57, 0, 11.317, 0.929, 0, 11.617, -1.849, 0, 11.933, 2.872, 0, 12.15, -0.837, 0, 12.317, 0.793, 0, 12.617, -1.695, 0, 12.933, 2.81, 0, 13.15, -0.916, 0, 13.317, 0.809, 0, 13.617, -1.66, 0, 13.933, 2.774, 0, 14, 0.181]}, {"Target": "Parameter", "Id": "Physics_Joint_4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.583, 0, 0.1, 1.255, 0, 0.283, -2.352, 0, 0.533, 3.197, 0, 0.833, -4.58, 0, 1.083, 3.54, 0, 1.317, -1.438, 0, 1.517, 2.238, 0, 1.817, -4.448, 0, 2.083, 4.13, 0, 2.317, -1.576, 0, 2.533, 1.982, 0, 2.817, -4.299, 0, 3.117, 5.05, 0, 3.483, -5.32, 0, 3.75, 4.042, 0, 3.983, -1.218, 0, 4.183, 2.117, 0, 4.483, -4.518, 0, 4.733, 4.339, 0, 4.983, -1.562, 0, 5.2, 1.901, 0, 5.483, -4.292, 0, 5.733, 4.298, 0, 5.983, -1.685, 0, 6.2, 1.927, 0, 6.483, -4.247, 0, 6.783, 5.04, 0, 7.15, -5.322, 0, 7.417, 4.063, 0, 7.65, -1.232, 0, 7.85, 2.109, 0, 8.15, -4.513, 0, 8.4, 4.337, 0, 8.65, -1.564, 0, 8.867, 1.901, 0, 9.15, -4.291, 0, 9.4, 4.297, 0, 9.65, -1.685, 0, 9.867, 1.928, 0, 10.15, -4.247, 0, 10.45, 5.04, 0, 10.817, -5.322, 0, 11.083, 4.063, 0, 11.317, -1.233, 0, 11.517, 2.109, 0, 11.817, -4.513, 0, 12.067, 4.337, 0, 12.317, -1.564, 0, 12.533, 1.902, 0, 12.817, -4.291, 0, 13.067, 4.297, 0, 13.317, -1.685, 0, 13.533, 1.928, 0, 13.817, -4.247, 0, 14, 0.583]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.615, 0, 0.167, -2.855, 0, 0.4, 3.467, 0, 0.717, -3.381, 0, 1, 3.972, 0, 1.2, -2.071, 0, 1.383, 1.45, 0, 1.683, -2.583, 0, 1.983, 4.498, 0, 2.2, -2.864, 0, 2.4, 1.075, 0, 2.683, -2.063, 0, 2.983, 4.253, 0, 3.333, -4.165, 0, 3.65, 5.3, 0, 3.867, -2.399, 0, 4.067, 0.582, 0, 4.333, -2.14, 0, 4.65, 4.55, 0, 4.867, -3.23, 0, 5.067, 1.034, 0, 5.367, -1.871, 0, 5.65, 4.114, 0, 5.867, -3.135, 0, 6.067, 1.31, 0, 6.367, -2.029, 0, 6.65, 4.095, 0, 7, -4.047, 0, 7.317, 5.244, 0, 7.533, -2.448, 0, 7.733, 0.616, 0, 8, -2.123, 0, 8.317, 4.562, 0, 8.533, -3.232, 0, 8.733, 1.026, 0, 9.033, -1.867, 0, 9.317, 4.115, 0, 9.533, -3.138, 0, 9.733, 1.309, 0, 10.033, -2.027, 0, 10.317, 4.094, 0, 10.667, -4.046, 0, 10.983, 5.244, 0, 11.2, -2.448, 0, 11.4, 0.615, 0, 11.667, -2.123, 0, 11.983, 4.561, 0, 12.2, -3.232, 0, 12.4, 1.027, 0, 12.7, -1.867, 0, 12.983, 4.115, 0, 13.2, -3.137, 0, 13.4, 1.309, 0, 13.7, -2.027, 0, 13.983, 4.094, 0, 14, 0.615]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.799, 0, 0.117, 2.978, 0, 0.333, -6.855, 0, 0.6, 7.917, 0, 0.917, -9.472, 0, 1.167, 8.569, 0, 1.383, -3.327, 0, 1.617, 2.985, 0, 1.9, -7.61, 0, 2.15, 9.972, 0, 2.4, -4.863, 0, 2.633, 2.447, 0, 2.9, -6.58, 0, 3.183, 9.624, 0, 3.55, -11.168, 0, 3.833, 11.615, 0, 4.067, -4.005, 0, 4.283, 1.353, 0, 4.567, -6.599, 0, 4.817, 10.32, 0, 5.067, -5.603, 0, 5.3, 2.505, 0, 5.567, -6.207, 0, 5.817, 9.386, 0, 6.05, -5.502, 0, 6.3, 3.012, 0, 6.567, -6.533, 0, 6.85, 9.309, 0, 7.217, -10.944, 0, 7.5, 11.539, 0, 7.733, -4.118, 0, 7.95, 1.436, 0, 8.233, -6.68, 0, 8.483, 10.351, 0, 8.733, -5.601, 0, 8.967, 2.486, 0, 9.233, -6.197, 0, 9.483, 9.389, 0, 9.717, -5.509, 0, 9.967, 3.013, 0, 10.233, -6.53, 0, 10.517, 9.306, 0, 10.883, -10.942, 0, 11.167, 11.54, 0, 11.4, -4.119, 0, 11.617, 1.435, 0, 11.9, -6.676, 0, 12.15, 10.348, 0, 12.4, -5.601, 0, 12.633, 2.488, 0, 12.9, -6.198, 0, 13.15, 9.389, 0, 13.383, -5.508, 0, 13.633, 3.013, 0, 13.9, -6.53, 0, 14, 0.799]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.528, 0, 0.233, 4.218, 0, 0.483, -7.639, 0, 0.767, 9.433, 0, 1.067, -11.261, 0, 1.317, 9.161, 0, 1.567, -4.509, 0, 1.8, 4.197, 0, 2.05, -8.378, 0, 2.3, 9.652, 0, 2.567, -6.065, 0, 2.817, 4.454, 0, 3.067, -7.639, 0, 3.35, 10.499, 0, 3.683, -12.773, 0, 3.967, 12.406, 0, 4.233, -6.241, 0, 4.483, 3.518, 0, 4.717, -7.33, 0, 4.967, 9.686, 0, 5.217, -6.68, 0, 5.483, 4.715, 0, 5.733, -7.365, 0, 5.967, 8.901, 0, 6.217, -6.333, 0, 6.483, 5.017, 0, 6.733, -7.914, 0, 7.017, 10.363, 0, 7.35, -12.705, 0, 7.633, 12.357, 0, 7.9, -6.315, 0, 8.15, 3.673, 0, 8.383, -7.436, 0, 8.633, 9.727, 0, 8.883, -6.677, 0, 9.15, 4.684, 0, 9.4, -7.352, 0, 9.633, 8.904, 0, 9.883, -6.34, 0, 10.15, 5.02, 0, 10.4, -7.934, 0, 10.683, 10.367, 0, 11.017, -12.706, 0, 11.3, 12.357, 0, 11.567, -6.316, 0, 11.817, 3.672, 0, 12.05, -7.433, 0, 12.3, 9.724, 0, 12.55, -6.676, 0, 12.817, 4.689, 0, 13.067, -7.353, 0, 13.3, 8.903, 0, 13.55, -6.339, 0, 13.817, 5.019, 0, 14, -0.528]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.325, 0, 0.317, 4.488, 0, 0.6, -8.357, 0, 0.883, 11.488, 0, 1.183, -13.667, 0, 1.45, 11.644, 0, 1.717, -5.973, 0, 1.95, 3.737, 0, 2.183, -7.935, 0, 2.433, 10.625, 0, 2.7, -7.754, 0, 2.95, 4.961, 0, 3.183, -7.74, 0, 3.467, 12.618, 0, 3.783, -16.301, 0, 4.1, 15.997, 0, 4.383, -9.52, 0, 4.633, 4.094, 0, 4.85, -6.504, 0, 5.1, 10.114, 0, 5.367, -8.35, 0, 5.617, 5.668, 0, 5.85, -7.229, 0, 6.1, 9.38, 0, 6.367, -7.605, 0, 6.617, 5.829, 0, 6.867, -8.248, 0, 7.133, 12.55, 0, 7.45, -16.148, 0, 7.767, 15.888, 0, 8.05, -9.548, 0, 8.3, 4.254, 0, 8.517, -6.651, 0, 8.767, 10.181, 0, 9.033, -8.361, 0, 9.283, 5.665, 0, 9.517, -7.214, 0, 9.767, 9.38, 0, 10.033, -7.613, 0, 10.283, 5.867, 0, 10.533, -8.266, 0, 10.8, 12.556, 0, 11.117, -16.15, 0, 11.433, 15.889, 0, 11.717, -9.549, 0, 11.967, 4.254, 0, 12.183, -6.648, 0, 12.433, 10.177, 0, 12.7, -8.359, 0, 12.95, 5.659, 0, 13.183, -7.212, 0, 13.433, 9.378, 0, 13.7, -7.612, 0, 13.95, 5.862, 0, 14, -2.325]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -6.361, 0, 0.4, 5.563, 0, 0.717, -9.699, 0, 1, 14.02, 0, 1.3, -17.012, 0, 1.583, 15.276, 0, 1.867, -8.583, 0, 2.083, 3.26, 0, 2.3, -6.96, 0, 2.567, 11.58, 0, 2.833, -9.874, 0, 3.083, 5.68, 0, 3.317, -7.744, 0, 3.583, 14.467, 0, 3.9, -20.139, 0, 4.2, 20.79, 0, 4.517, -14.141, 0, 4.767, 5.62, 0, 4.983, -5.314, 0, 5.233, 10.26, 0, 5.5, -10.249, 0, 5.75, 6.698, 0, 5.983, -7.013, 0, 6.233, 9.918, 0, 6.5, -9.121, 0, 6.75, 6.432, 0, 6.983, -8.549, 0, 7.267, 14.546, 0, 7.567, -20.038, 0, 7.883, 20.633, 0, 8.183, -14.107, 0, 8.433, 5.773, 0, 8.65, -5.521, 0, 8.9, 10.376, 0, 9.167, -10.291, 0, 9.417, 6.675, 0, 9.65, -6.973, 0, 9.9, 9.905, 0, 10.167, -9.128, 0, 10.417, 6.457, 0, 10.65, -8.564, 0, 10.933, 14.548, 0, 11.233, -20.04, 0, 11.55, 20.634, 0, 11.85, -14.109, 0, 12.1, 5.774, 0, 12.317, -5.517, 0, 12.567, 10.37, 0, 12.833, -10.284, 0, 13.083, 6.67, 0, 13.317, -6.971, 0, 13.567, 9.903, 0, 13.833, -9.126, 0, 14, -6.361]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -9.818, 0, 0.05, -11.141, 0, 0.45, 7.986, 0, 0.817, -11.696, 0, 1.117, 16.973, 0, 1.417, -20.81, 0, 1.717, 19.995, 0, 2, -12.904, 0, 2.25, 3.691, 0, 2.45, -4.888, 0, 2.683, 11.954, 0, 2.967, -12.358, 0, 3.233, 6.931, 0, 3.45, -7.201, 0, 3.7, 15.999, 0, 4, -24.186, 0, 4.317, 26.398, 0, 4.633, -20.127, 0, 4.917, 9.113, 0, 5.133, -4.132, 0, 5.367, 9.637, 0, 5.633, -11.968, 0, 5.9, 8.218, 0, 6.133, -6.809, 0, 6.367, 10.302, 0, 6.633, -10.811, 0, 6.883, 7.296, 0, 7.117, -8.386, 0, 7.383, 16.45, 0, 7.667, -24.136, 0, 7.983, 26.223, 0, 8.3, -20.047, 0, 8.583, 9.199, 0, 8.8, -4.389, 0, 9.033, 9.823, 0, 9.3, -12.048, 0, 9.567, 8.207, 0, 9.8, -6.741, 0, 10.033, 10.261, 0, 10.3, -10.811, 0, 10.55, 7.402, 0, 10.783, -8.444, 0, 11.05, 16.346, 0, 11.333, -24.105, 0, 11.65, 26.219, 0, 11.967, -20.047, 0, 12.25, 9.201, 0, 12.467, -4.386, 0, 12.7, 9.816, 0, 12.967, -12.039, 0, 13.233, 8.202, 0, 13.467, -6.739, 0, 13.7, 10.259, 0, 13.967, -10.808, 0, 14, -9.818]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.076, 0, 0.15, -16.916, 0, 0.533, 12.253, 0, 0.9, -14.582, 0, 1.217, 20.502, 0, 1.517, -25.441, 0, 1.817, 25.555, 0, 2.133, -18.282, 0, 2.4, 5.713, 0, 2.583, -1.99, 0, 2.817, 11.193, 0, 3.083, -14.78, 0, 3.367, 9.129, 0, 3.583, -6.529, 0, 3.817, 16.205, 0, 4.1, -27.432, 0, 4.383, 30, 2, 4.433, 30, 0, 4.75, -27.125, 0, 5.05, 14.002, 0, 5.283, -3.79, 0, 5.5, 8.051, 0, 5.75, -13.287, 0, 6.033, 10.017, 0, 6.267, -6.807, 0, 6.5, 10.333, 0, 6.767, -12.52, 0, 7.017, 8.697, 0, 7.25, -7.941, 0, 7.483, 17.05, 0, 7.767, -27.607, 0, 8.05, 30, 2, 8.1, 30, 0, 8.417, -27.014, 0, 8.717, 14.017, 0, 8.95, -4.049, 0, 9.167, 8.32, 0, 9.417, -13.394, 0, 9.7, 10.017, 0, 9.933, -6.698, 0, 10.167, 10.246, 0, 10.433, -12.712, 0, 10.683, 8.845, 0, 10.917, -7.832, 0, 11.15, 16.985, 0, 11.433, -27.565, 0, 11.717, 30, 2, 11.767, 30, 0, 12.083, -27.013, 0, 12.383, 14.02, 0, 12.617, -4.049, 0, 12.833, 8.312, 0, 13.083, -13.384, 0, 13.367, 10.011, 0, 13.6, -6.696, 0, 13.833, 10.245, 0, 14, -2.076]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.167, 1, 0, 3, 0, 0, 4.167, 1, 0, 6, 0, 0, 7.167, 1, 0, 9, 0, 0, 10.167, 1, 0, 12, 0, 0, 13.167, 1, 0, 14, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14, 1]}, {"Target": "Parameter", "Id": "ParamSad2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14, 1]}, {"Target": "Parameter", "Id": "ParamCRY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.2, 0, 14, -5.2]}, {"Target": "Parameter", "Id": "Position_Coordinates", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14, 1]}, {"Target": "Parameter", "Id": "Man_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14, 1]}, {"Target": "Parameter", "Id": "Man_TMD2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "BG_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14, 1]}, {"Target": "Parameter", "Id": "FG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "FG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "BG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "BG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "BG_White5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "BG_White2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "BG_White3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.3, 0, 14, 0.3]}, {"Target": "Parameter", "Id": "All_Size2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -12.72, 0, 14, -12.72]}, {"Target": "Parameter", "Id": "All_Size3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "All_Size4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "tuerzuo3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "faqiehuan", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "faqiehuan2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "faqiehuan4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "faqiehuan3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "tuerzuo4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Mail_Change", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14, 1]}, {"Target": "Parameter", "Id": "Mail_Transparency5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.5, 0, 14, 0.5]}, {"Target": "Parameter", "Id": "Mail_Transparency2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "dafengshangzhuanchang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14, 1]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "tongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamTongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "EyeFrame_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1, 0, 14, -1]}, {"Target": "Parameter", "Id": "ParamBrowForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1, 0, 14, -1]}, {"Target": "Parameter", "Id": "ParamBrowY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14, 1]}, {"Target": "Parameter", "Id": "ParamSigh", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamDarken", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamCry", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_B", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_C", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "MB_Unidirectional_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "jing<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.374, 0, 14, -2.374]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 6.925, 0, 14, 6.925]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -4.027, 0, 14, -4.027]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10.008, 0, 14, -10.008]}, {"Target": "Parameter", "Id": "huqi1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "huqi2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamArmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14, 1]}, {"Target": "Parameter", "Id": "ParamForearmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -30, 0, 14, -30]}, {"Target": "Parameter", "Id": "ParamArmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamArmL_ForearmL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.087, 0, 14, 0.087]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14, 1]}, {"Target": "Parameter", "Id": "ParamForearmL_HandL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamHandLDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 8, 0, 14, 8]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamHandLSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "xiongjiya", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14, 1]}, {"Target": "Parameter", "Id": "xiongjiya5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.4, 0, 14, 0.4]}, {"Target": "Parameter", "Id": "xiongjiya3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.85, 0, 14, 0.85]}, {"Target": "Parameter", "Id": "xiongjiya4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.85, 0, 14, 0.85]}, {"Target": "Parameter", "Id": "xiongjiya2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamArmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 30, 0, 14, 30]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -7.9, 0, 14, -7.9]}, {"Target": "Parameter", "Id": "ParamArmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamArmR_ForearmR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14, 1]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.72, 0, 14, -0.72]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamForearm<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_HandR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamHandRDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 8, 0, 14, 8]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamHandRSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14, 1]}, {"Target": "Parameter", "Id": "Change_idle_physics8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14, 1]}, {"Target": "Parameter", "Id": "Change_idle_physics7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 14, 0.6]}, {"Target": "Parameter", "Id": "Change_idle_physics23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14, 1]}, {"Target": "Parameter", "Id": "Change_idle_physics24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.1, 0, 14, -0.1]}, {"Target": "Parameter", "Id": "Change_idle_physics5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.3, 0, 14, -0.3]}, {"Target": "Parameter", "Id": "Change_idle_physics6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.615, 0, 14, 0.615]}, {"Target": "Parameter", "Id": "Change_idle_physics10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.321, 0, 14, 0.321]}, {"Target": "Parameter", "Id": "Change_idle_physics11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14, 1]}, {"Target": "Parameter", "Id": "Change_idle_physics20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param103", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -17.662, 0, 14, -17.662]}, {"Target": "Parameter", "Id": "taiqiugundong", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -22.5, 0, 14, -22.5]}, {"Target": "Parameter", "Id": "Change_idle_physics32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamLegL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14, 1]}, {"Target": "Parameter", "Id": "ParamLegLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamLegLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamCrusLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamCrusLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>oodLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamFoodLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamFoodLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamLegR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14, 1]}, {"Target": "Parameter", "Id": "ParamLegRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamLegRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamCrusRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamCrusRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamCrus<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>ood<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamFoodRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamFoodRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.187, 0, 14, 0.187]}, {"Target": "Parameter", "Id": "Physics_Provisional", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "physics_Add_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "physics_Add_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamBodyStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Physics_AngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Physics_AngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Physics_Body_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Physics_Body_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Physics_breast", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamBreath2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "fenshangxuanzhuang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "touch_drag1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "touch_drag2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "touch_drag3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "touch_drag4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "touch_drag5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "touch_drag6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "touch_drag7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "touch_drag8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "touch_drag9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>pian<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh654", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh655", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh657", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh656", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh658", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh659", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh660", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14, 0]}], "UserData": []}