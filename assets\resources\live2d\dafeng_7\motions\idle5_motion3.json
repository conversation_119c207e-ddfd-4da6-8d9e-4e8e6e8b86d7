{"Version": 3, "Meta": {"Duration": 14.817, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 267, "TotalSegmentCount": 2388, "TotalPointCount": 2536, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 1.406, -0.171, 2.811, -0.341, 4.217, -0.512, 1, 4.222, -0.741, 4.228, -0.971, 4.233, -1.2, 1, 6.433, -1.307, 8.633, -1.415, 10.833, -1.522, 1, 10.839, -2.022, 10.844, -2.522, 10.85, -3.022, 1, 12.167, -1.211, 13.483, 0.599, 14.8, 2.41, 1, 14.806, 1.607, 14.811, 0.803, 14.817, 0]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.18, 1, 1.406, -0.593, 2.811, -1.007, 4.217, -1.42, 1, 6.422, -0.114, 8.628, 1.192, 10.833, 2.498, 1, 10.839, 1.838, 10.844, 1.178, 10.85, 0.518, 1, 12.167, -0.498, 13.483, -1.514, 14.8, -2.53, 1, 14.806, -1.747, 14.811, -0.963, 14.817, -0.18]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.5, 2, 4.217, 1.5, 1, 4.222, 3.5, 4.228, 5.5, 4.233, 7.5, 1, 6.433, 7.12, 8.633, 6.74, 10.833, 6.36, 1, 10.839, 4.356, 10.844, 2.351, 10.85, 0.347, 1, 12.167, 0.711, 13.483, 1.076, 14.8, 1.44, 1, 14.806, 1.46, 14.811, 1.48, 14.817, 1.5]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 4.14, 1, 1.406, 4.36, 2.811, 4.58, 4.217, 4.8, 1, 4.222, 6.62, 4.228, 8.44, 4.233, 10.26, 1, 6.433, 9.9, 8.633, 9.54, 10.833, 9.18, 1, 10.839, 11.976, 10.844, 14.771, 10.85, 17.567, 1, 12.167, 15.808, 13.483, 14.049, 14.8, 12.29, 1, 14.806, 9.573, 14.811, 6.857, 14.817, 4.14]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.317, 1, 0, 0.6, 0, 0, 0.85, 1, 2, 1.267, 1, 0, 1.55, 0, 0, 1.8, 1, 2, 2.9, 1, 0, 3.2, 0, 0, 3.333, 1.3, 0, 3.533, 1, 2, 4.383, 1, 0, 4.567, 0, 0, 14.817, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.317, 1, 0, 0.6, 0, 0, 0.85, 1, 2, 1.267, 1, 0, 1.55, 0, 0, 1.8, 1, 2, 2.9, 1, 0, 3.2, 0, 0, 3.333, 1.3, 0, 3.533, 1, 2, 4.383, 1, 0, 4.567, 0, 0, 14.817, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.317, 0, 1, 0.484, 0, 0.65, -0.375, 0.817, -0.5, 1, 0.967, -0.613, 1.117, -0.6, 1.267, -0.6, 1, 1.45, -0.6, 1.634, 0.458, 1.817, 0.5, 1, 2.228, 0.595, 2.639, 0.6, 3.05, 0.6, 0, 3.333, 0, 2, 5.433, 0, 2, 6.117, 0, 2, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.317, 0, 1, 0.484, 0, 0.65, -0.207, 0.817, -0.4, 1, 0.967, -0.574, 1.117, -0.6, 1.267, -0.6, 0, 1.817, 0.2, 0, 3.05, 0, 2, 3.333, 0, 2, 5.433, 0, 2, 6.117, 0, 2, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamBrowAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.284, 0, 0.733, 0.4, 0, 0.95, 0.284, 0, 1.7, 0.4, 0, 1.933, 0.284, 0, 2.683, 0.4, 0, 2.9, 0.284, 0, 3.65, 0.4, 0, 3.867, 0.284, 0, 4.633, 0.4, 0, 4.833, 0.284, 0, 5.583, 0.4, 0, 5.833, 0.284, 0, 6.583, 0.4, 0, 6.817, 0.284, 0, 7.55, 0.4, 0, 7.783, 0.284, 0, 8.533, 0.4, 0, 8.75, 0.284, 0, 9.5, 0.4, 0, 9.733, 0.284, 0, 10.483, 0.4, 0, 10.7, 0.284, 0, 11.45, 0.4, 0, 11.667, 0.284, 0, 12.417, 0.4, 0, 12.633, 0.284, 0, 13.383, 0.4, 0, 13.633, 0.284, 0, 14.367, 0.4, 0, 14.583, 0.284, 2, 14.817, 0.284]}, {"Target": "Parameter", "Id": "ParamEyeLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.317, 0, 0, 0.467, 1, 2, 0.55, 1, 0, 0.717, -1, 2, 0.867, -1, 0, 1.1, 0.354, 0, 1.283, 0.056, 0, 1.433, 1, 2, 1.5, 1, 0, 1.667, -1, 2, 1.817, -1, 0, 2.05, 0.355, 0, 2.367, -0.098, 0, 2.667, 0.027, 0, 2.9, -0.004, 0, 3.067, 1, 2, 3.133, 1, 0, 3.267, -1, 2, 3.383, -1, 0, 3.4, 1, 2, 3.517, 1, 0, 3.733, -0.487, 0, 4.05, 0.132, 0, 4.35, -0.037, 0, 4.483, 1, 2, 4.633, 1, 0, 4.883, -0.332, 0, 5.183, 0.085, 0, 5.5, -0.034, 0, 5.8, -0.004, 0, 6.15, -0.015, 2, 6.183, -0.015, 2, 6.2, -0.015, 0, 6.317, -0.014, 2, 6.333, -0.014, 2, 6.35, -0.014, 2, 6.367, -0.014, 2, 6.383, -0.014, 2, 6.417, -0.014, 0, 6.75, -0.016, 2, 6.767, -0.016, 0, 6.817, -0.017, 2, 6.833, -0.017, 2, 6.867, -0.017, 2, 6.883, -0.017, 2, 6.917, -0.017, 2, 6.933, -0.017, 2, 6.95, -0.017, 2, 6.967, -0.017, 2, 6.983, -0.017, 2, 7.017, -0.017, 0, 7.033, -0.018, 2, 7.05, -0.018, 2, 7.1, -0.018, 2, 7.117, -0.018, 2, 7.167, -0.018, 2, 7.183, -0.018, 2, 7.233, -0.018, 2, 7.25, -0.018, 0, 7.267, -0.019, 2, 7.3, -0.019, 2, 7.35, -0.019, 2, 7.367, -0.019, 2, 7.383, -0.019, 2, 7.433, -0.019, 2, 7.45, -0.019, 2, 7.467, -0.019, 0, 7.5, -0.02, 2, 7.533, -0.02, 2, 7.567, -0.02, 2, 7.583, -0.02, 2, 7.6, -0.02, 2, 7.617, -0.02, 2, 7.65, -0.02, 2, 7.667, -0.02, 2, 7.7, -0.02, 2, 7.733, -0.02, 0, 7.75, -0.021, 2, 7.783, -0.021, 2, 7.817, -0.021, 2, 7.85, -0.021, 2, 7.883, -0.021, 2, 7.9, -0.021, 2, 7.917, -0.021, 2, 7.967, -0.021, 2, 7.983, -0.021, 2, 8.033, -0.021, 2, 8.05, -0.021, 2, 8.083, -0.021, 2, 8.1, -0.021, 2, 8.133, -0.021, 0, 8.15, -0.022, 2, 8.2, -0.022, 2, 8.217, -0.022, 2, 8.267, -0.022, 2, 8.283, -0.022, 2, 8.317, -0.022, 2, 8.333, -0.022, 2, 8.383, -0.022, 2, 8.4, -0.022, 2, 8.433, -0.022, 2, 8.45, -0.022, 2, 8.467, -0.022, 2, 8.483, -0.022, 2, 8.5, -0.022, 2, 8.517, -0.022, 2, 8.55, -0.022, 0, 8.567, -0.023, 2, 8.617, -0.023, 2, 8.633, -0.023, 2, 8.683, -0.023, 2, 8.7, -0.023, 2, 8.733, -0.023, 2, 8.75, -0.023, 2, 8.8, -0.023, 2, 8.817, -0.023, 2, 8.833, -0.023, 2, 8.85, -0.023, 2, 8.867, -0.023, 2, 8.883, -0.023, 2, 8.917, -0.023, 2, 8.933, -0.023, 2, 8.967, -0.023, 2, 8.983, -0.023, 2, 9.017, -0.023, 2, 9.033, -0.023, 2, 9.05, -0.023, 2, 9.067, -0.023, 2, 9.1, -0.023, 2, 9.117, -0.023, 2, 9.15, -0.023, 2, 9.167, -0.023, 2, 9.183, -0.023, 2, 9.2, -0.023, 2, 9.217, -0.023, 2, 9.25, -0.023, 2, 9.267, -0.023, 2, 9.283, -0.023, 2, 9.317, -0.023, 2, 9.333, -0.023, 0, 9.35, -0.024, 0, 9.367, -0.023, 2, 9.383, -0.023, 0, 9.4, -0.024, 2, 9.417, -0.024, 0, 9.433, -0.023, 2, 9.45, -0.023, 0, 9.467, -0.024, 2, 9.483, -0.024, 0, 9.5, -0.023, 0, 9.517, -0.024, 2, 9.55, -0.024, 0, 9.567, -0.023, 0, 9.583, -0.024, 2, 9.6, -0.024, 0, 9.617, -0.023, 0, 9.633, -0.024, 2, 9.667, -0.024, 0, 9.683, -0.023, 0, 9.7, -0.024, 2, 9.717, -0.024, 0, 9.733, -0.023, 2, 9.75, -0.023, 0, 9.767, -0.024, 0, 9.783, -0.023, 2, 9.8, -0.023, 0, 9.817, -0.024, 2, 9.833, -0.024, 0, 9.85, -0.023, 2, 9.867, -0.023, 0, 9.883, -0.024, 0, 9.9, -0.023, 2, 9.917, -0.023, 0, 9.933, -0.024, 0, 9.967, -0.023, 2, 9.983, -0.023, 2, 10, -0.023, 2, 10.017, -0.023, 2, 10.033, -0.023, 2, 10.05, -0.023, 2, 10.067, -0.023, 2, 10.083, -0.023, 2, 10.1, -0.023, 2, 10.117, -0.023, 2, 10.133, -0.023, 2, 10.15, -0.023, 2, 10.167, -0.023, 2, 10.183, -0.023, 2, 10.2, -0.023, 2, 10.217, -0.023, 2, 10.233, -0.023, 2, 10.25, -0.023, 2, 10.3, -0.023, 2, 10.317, -0.023, 2, 10.367, -0.023, 2, 10.4, -0.023, 2, 10.417, -0.023, 2, 10.433, -0.023, 2, 10.45, -0.023, 2, 10.467, -0.023, 2, 10.5, -0.023, 2, 10.55, -0.023, 2, 10.567, -0.023, 2, 10.583, -0.023, 0, 10.617, -0.022, 2, 10.667, -0.022, 2, 10.683, -0.022, 2, 10.7, -0.022, 2, 10.733, -0.022, 2, 10.767, -0.022, 0, 10.85, -0.023, 2, 10.867, -0.023, 2, 10.883, -0.023, 0, 11.05, -0.022, 2, 11.067, -0.022, 0, 11.1, -0.021, 2, 11.117, -0.021, 2, 11.167, -0.021, 2, 11.183, -0.021, 2, 11.2, -0.021, 2, 11.217, -0.021, 2, 11.233, -0.021, 2, 11.25, -0.021, 2, 11.267, -0.021, 2, 11.283, -0.021, 2, 11.3, -0.021, 2, 11.317, -0.021, 2, 11.367, -0.021, 2, 11.383, -0.021, 2, 11.433, -0.021, 0, 11.467, -0.02, 0, 11.483, -0.021, 0, 11.517, -0.02, 2, 11.533, -0.02, 2, 11.583, -0.02, 2, 11.6, -0.02, 2, 11.633, -0.02, 2, 11.65, -0.02, 0, 11.7, -0.019, 2, 11.717, -0.019, 2, 11.75, -0.019, 2, 11.767, -0.019, 2, 11.817, -0.019, 2, 11.833, -0.019, 2, 11.85, -0.019, 2, 11.867, -0.019, 2, 11.883, -0.019, 2, 11.9, -0.019, 2, 11.933, -0.019, 2, 11.95, -0.019, 0, 12, -0.018, 2, 12.017, -0.018, 2, 12.05, -0.018, 2, 12.067, -0.018, 2, 12.117, -0.018, 2, 12.133, -0.018, 2, 12.167, -0.018, 2, 12.183, -0.018, 0, 12.233, -0.017, 2, 12.25, -0.017, 2, 12.3, -0.017, 2, 12.317, -0.017, 2, 12.35, -0.017, 2, 12.367, -0.017, 2, 12.4, -0.017, 2, 12.417, -0.017, 0, 12.467, -0.016, 2, 12.483, -0.016, 0, 12.583, -0.015, 2, 12.6, -0.015, 2, 12.65, -0.015, 2, 12.667, -0.015, 2, 12.7, -0.015, 2, 12.717, -0.015, 2, 12.767, -0.015, 2, 12.783, -0.015, 0, 12.817, -0.014, 2, 12.833, -0.014, 2, 12.883, -0.014, 2, 12.9, -0.014, 0, 12.95, -0.013, 2, 12.967, -0.013, 2, 13, -0.013, 2, 13.017, -0.013, 2, 13.067, -0.013, 2, 13.083, -0.013, 0, 13.117, -0.012, 2, 13.133, -0.012, 2, 13.183, -0.012, 2, 13.2, -0.012, 0, 13.3, -0.011, 2, 13.317, -0.011, 0, 13.467, -0.01, 2, 13.483, -0.01, 0, 13.65, -0.009, 2, 13.667, -0.009, 0, 13.717, -0.008, 2, 13.733, -0.008, 1, 14.094, -0.008, 14.456, 0, 14.817, 0.001]}, {"Target": "Parameter", "Id": "ParamEyeRsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.317, 0, 0, 0.467, 1, 2, 0.55, 1, 0, 0.717, -1, 2, 0.867, -1, 0, 1.1, 0.354, 0, 1.283, 0.056, 0, 1.433, 1, 2, 1.5, 1, 0, 1.667, -1, 2, 1.817, -1, 0, 2.05, 0.355, 0, 2.367, -0.098, 0, 2.667, 0.027, 0, 2.9, -0.004, 0, 3.067, 1, 2, 3.133, 1, 0, 3.267, -1, 2, 3.383, -1, 0, 3.4, 1, 2, 3.517, 1, 0, 3.733, -0.487, 0, 4.05, 0.132, 0, 4.35, -0.037, 0, 4.483, 1, 2, 4.633, 1, 0, 4.883, -0.332, 0, 5.183, 0.085, 0, 5.5, -0.034, 0, 5.8, -0.004, 0, 6.15, -0.015, 2, 6.183, -0.015, 2, 6.2, -0.015, 0, 6.317, -0.014, 2, 6.333, -0.014, 2, 6.35, -0.014, 2, 6.367, -0.014, 2, 6.383, -0.014, 2, 6.417, -0.014, 0, 6.75, -0.016, 2, 6.767, -0.016, 0, 6.817, -0.017, 2, 6.833, -0.017, 2, 6.867, -0.017, 2, 6.883, -0.017, 2, 6.917, -0.017, 2, 6.933, -0.017, 2, 6.95, -0.017, 2, 6.967, -0.017, 2, 6.983, -0.017, 2, 7.017, -0.017, 0, 7.033, -0.018, 2, 7.05, -0.018, 2, 7.1, -0.018, 2, 7.117, -0.018, 2, 7.167, -0.018, 2, 7.183, -0.018, 2, 7.233, -0.018, 2, 7.25, -0.018, 0, 7.267, -0.019, 2, 7.3, -0.019, 2, 7.35, -0.019, 2, 7.367, -0.019, 2, 7.383, -0.019, 2, 7.433, -0.019, 2, 7.45, -0.019, 2, 7.467, -0.019, 0, 7.5, -0.02, 2, 7.533, -0.02, 2, 7.567, -0.02, 2, 7.583, -0.02, 2, 7.6, -0.02, 2, 7.617, -0.02, 2, 7.65, -0.02, 2, 7.667, -0.02, 2, 7.7, -0.02, 2, 7.733, -0.02, 0, 7.75, -0.021, 2, 7.783, -0.021, 2, 7.817, -0.021, 2, 7.85, -0.021, 2, 7.883, -0.021, 2, 7.9, -0.021, 2, 7.917, -0.021, 2, 7.967, -0.021, 2, 7.983, -0.021, 2, 8.033, -0.021, 2, 8.05, -0.021, 2, 8.083, -0.021, 2, 8.1, -0.021, 2, 8.133, -0.021, 0, 8.15, -0.022, 2, 8.2, -0.022, 2, 8.217, -0.022, 2, 8.267, -0.022, 2, 8.283, -0.022, 2, 8.317, -0.022, 2, 8.333, -0.022, 2, 8.383, -0.022, 2, 8.4, -0.022, 2, 8.433, -0.022, 2, 8.45, -0.022, 2, 8.467, -0.022, 2, 8.483, -0.022, 2, 8.5, -0.022, 2, 8.517, -0.022, 2, 8.55, -0.022, 0, 8.567, -0.023, 2, 8.617, -0.023, 2, 8.633, -0.023, 2, 8.683, -0.023, 2, 8.7, -0.023, 2, 8.733, -0.023, 2, 8.75, -0.023, 2, 8.8, -0.023, 2, 8.817, -0.023, 2, 8.833, -0.023, 2, 8.85, -0.023, 2, 8.867, -0.023, 2, 8.883, -0.023, 2, 8.917, -0.023, 2, 8.933, -0.023, 2, 8.967, -0.023, 2, 8.983, -0.023, 2, 9.017, -0.023, 2, 9.033, -0.023, 2, 9.05, -0.023, 2, 9.067, -0.023, 2, 9.1, -0.023, 2, 9.117, -0.023, 2, 9.15, -0.023, 2, 9.167, -0.023, 2, 9.183, -0.023, 2, 9.2, -0.023, 2, 9.217, -0.023, 2, 9.25, -0.023, 2, 9.267, -0.023, 2, 9.283, -0.023, 2, 9.317, -0.023, 2, 9.333, -0.023, 0, 9.35, -0.024, 0, 9.367, -0.023, 2, 9.383, -0.023, 0, 9.4, -0.024, 2, 9.417, -0.024, 0, 9.433, -0.023, 2, 9.45, -0.023, 0, 9.467, -0.024, 2, 9.483, -0.024, 0, 9.5, -0.023, 0, 9.517, -0.024, 2, 9.55, -0.024, 0, 9.567, -0.023, 0, 9.583, -0.024, 2, 9.6, -0.024, 0, 9.617, -0.023, 0, 9.633, -0.024, 2, 9.667, -0.024, 0, 9.683, -0.023, 0, 9.7, -0.024, 2, 9.717, -0.024, 0, 9.733, -0.023, 2, 9.75, -0.023, 0, 9.767, -0.024, 0, 9.783, -0.023, 2, 9.8, -0.023, 0, 9.817, -0.024, 2, 9.833, -0.024, 0, 9.85, -0.023, 2, 9.867, -0.023, 0, 9.883, -0.024, 0, 9.9, -0.023, 2, 9.917, -0.023, 0, 9.933, -0.024, 0, 9.967, -0.023, 2, 9.983, -0.023, 2, 10, -0.023, 2, 10.017, -0.023, 2, 10.033, -0.023, 2, 10.05, -0.023, 2, 10.067, -0.023, 2, 10.083, -0.023, 2, 10.1, -0.023, 2, 10.117, -0.023, 2, 10.133, -0.023, 2, 10.15, -0.023, 2, 10.167, -0.023, 2, 10.183, -0.023, 2, 10.2, -0.023, 2, 10.217, -0.023, 2, 10.233, -0.023, 2, 10.25, -0.023, 2, 10.3, -0.023, 2, 10.317, -0.023, 2, 10.367, -0.023, 2, 10.4, -0.023, 2, 10.417, -0.023, 2, 10.433, -0.023, 2, 10.45, -0.023, 2, 10.467, -0.023, 2, 10.5, -0.023, 2, 10.55, -0.023, 2, 10.567, -0.023, 2, 10.583, -0.023, 0, 10.617, -0.022, 2, 10.667, -0.022, 2, 10.683, -0.022, 2, 10.7, -0.022, 2, 10.733, -0.022, 2, 10.767, -0.022, 0, 10.85, -0.023, 2, 10.867, -0.023, 2, 10.883, -0.023, 0, 11.05, -0.022, 2, 11.067, -0.022, 0, 11.1, -0.021, 2, 11.117, -0.021, 2, 11.167, -0.021, 2, 11.183, -0.021, 2, 11.2, -0.021, 2, 11.217, -0.021, 2, 11.233, -0.021, 2, 11.25, -0.021, 2, 11.267, -0.021, 2, 11.283, -0.021, 2, 11.3, -0.021, 2, 11.317, -0.021, 2, 11.367, -0.021, 2, 11.383, -0.021, 2, 11.433, -0.021, 0, 11.467, -0.02, 0, 11.483, -0.021, 0, 11.517, -0.02, 2, 11.533, -0.02, 2, 11.583, -0.02, 2, 11.6, -0.02, 2, 11.633, -0.02, 2, 11.65, -0.02, 0, 11.7, -0.019, 2, 11.717, -0.019, 2, 11.75, -0.019, 2, 11.767, -0.019, 2, 11.817, -0.019, 2, 11.833, -0.019, 2, 11.85, -0.019, 2, 11.867, -0.019, 2, 11.883, -0.019, 2, 11.9, -0.019, 2, 11.933, -0.019, 2, 11.95, -0.019, 0, 12, -0.018, 2, 12.017, -0.018, 2, 12.05, -0.018, 2, 12.067, -0.018, 2, 12.117, -0.018, 2, 12.133, -0.018, 2, 12.167, -0.018, 2, 12.183, -0.018, 0, 12.233, -0.017, 2, 12.25, -0.017, 2, 12.3, -0.017, 2, 12.317, -0.017, 2, 12.35, -0.017, 2, 12.367, -0.017, 2, 12.4, -0.017, 2, 12.417, -0.017, 0, 12.467, -0.016, 2, 12.483, -0.016, 0, 12.583, -0.015, 2, 12.6, -0.015, 2, 12.65, -0.015, 2, 12.667, -0.015, 2, 12.7, -0.015, 2, 12.717, -0.015, 2, 12.767, -0.015, 2, 12.783, -0.015, 0, 12.817, -0.014, 2, 12.833, -0.014, 2, 12.883, -0.014, 2, 12.9, -0.014, 0, 12.95, -0.013, 2, 12.967, -0.013, 2, 13, -0.013, 2, 13.017, -0.013, 2, 13.067, -0.013, 2, 13.083, -0.013, 0, 13.117, -0.012, 2, 13.133, -0.012, 2, 13.183, -0.012, 2, 13.2, -0.012, 0, 13.3, -0.011, 2, 13.317, -0.011, 0, 13.467, -0.01, 2, 13.483, -0.01, 0, 13.65, -0.009, 2, 13.667, -0.009, 0, 13.717, -0.008, 2, 13.733, -0.008, 1, 14.094, -0.008, 14.456, 0, 14.817, 0.001]}, {"Target": "Parameter", "Id": "ParamEyeLGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.317, 0, 0, 0.433, -0.258, 0, 0.667, 0.75, 0, 0.95, -0.857, 0, 1.267, 0.306, 0, 1.433, -0.246, 0, 1.617, 0.553, 0, 1.9, -0.93, 0, 2.233, 0.31, 0, 2.533, -0.125, 0, 2.833, 0.047, 2, 2.85, 0.047, 0, 3.033, -0.254, 0, 3.233, 0.773, 0, 3.317, 0.346, 0, 3.533, 0.987, 0, 3.883, -0.38, 0, 4.2, 0.16, 0, 4.467, -0.34, 0, 4.717, 0.591, 0, 5.033, -0.286, 0, 5.333, 0.12, 0, 5.65, -0.045, 0, 5.95, 0.016, 2, 5.967, 0.016, 0, 6.25, -0.005, 2, 6.267, -0.005, 0, 6.567, 0.002, 2, 6.583, 0.002, 2, 6.6, 0.002, 0, 6.833, 0, 2, 6.867, 0, 2, 6.883, 0, 2, 6.9, 0, 2, 6.917, 0, 2, 6.95, 0, 2, 7, 0, 2, 7.017, 0, 2, 7.05, 0, 2, 7.067, 0, 2, 7.083, 0, 2, 7.133, 0, 2, 7.15, 0, 2, 7.233, 0, 2, 7.25, 0, 2, 7.267, 0, 2, 7.283, 0, 2, 7.333, 0, 2, 7.35, 0, 2, 7.417, 0, 2, 7.433, 0, 2, 7.45, 0, 2, 7.467, 0, 2, 7.483, 0, 2, 7.5, 0, 2, 7.517, 0, 2, 7.55, 0, 2, 7.567, 0, 2, 7.583, 0, 2, 7.617, 0, 2, 7.633, 0, 2, 7.95, 0, 2, 7.967, 0, 2, 7.983, 0, 2, 8, 0, 2, 8.017, 0, 2, 8.033, 0, 2, 8.05, 0, 2, 8.067, 0, 2, 8.083, 0, 2, 8.1, 0, 2, 8.183, 0, 2, 8.2, 0, 2, 8.217, 0, 2, 8.233, 0, 2, 8.25, 0, 2, 8.267, 0, 2, 8.283, 0, 2, 8.3, 0, 2, 8.317, 0, 2, 8.333, 0, 2, 8.35, 0, 2, 8.367, 0, 2, 8.383, 0, 2, 8.4, 0, 2, 8.417, 0, 2, 8.433, 0, 2, 8.45, 0, 2, 8.467, 0, 2, 8.483, 0, 2, 8.5, 0, 2, 8.517, 0, 2, 8.533, 0, 2, 8.55, 0, 2, 8.567, 0, 2, 8.583, 0, 2, 8.6, 0, 2, 8.617, 0, 2, 8.633, 0, 2, 8.65, 0, 2, 8.683, 0, 2, 8.7, 0, 2, 8.717, 0, 2, 8.733, 0, 2, 8.75, 0, 2, 8.767, 0, 2, 8.8, 0, 2, 8.817, 0, 2, 8.833, 0, 2, 8.85, 0, 2, 8.867, 0, 2, 8.883, 0, 2, 8.967, 0, 2, 8.983, 0, 2, 9, 0, 2, 9.2, 0, 2, 9.217, 0, 2, 9.233, 0, 2, 10.083, 0, 2, 10.1, 0, 2, 10.117, 0, 2, 10.133, 0, 2, 10.15, 0, 2, 10.167, 0, 2, 10.183, 0, 2, 10.2, 0, 2, 10.217, 0, 2, 10.233, 0, 2, 10.317, 0, 2, 10.333, 0, 2, 10.35, 0, 2, 10.367, 0, 2, 10.383, 0, 2, 10.4, 0, 2, 10.417, 0, 2, 10.433, 0, 2, 10.45, 0, 2, 10.467, 0, 2, 10.483, 0, 2, 10.5, 0, 2, 10.517, 0, 2, 10.533, 0, 2, 10.55, 0, 2, 10.567, 0, 2, 10.583, 0, 2, 10.6, 0, 2, 10.617, 0, 2, 10.633, 0, 2, 10.65, 0, 2, 10.667, 0, 2, 10.683, 0, 2, 10.7, 0, 2, 10.717, 0, 2, 10.733, 0, 2, 10.75, 0, 0, 10.883, 0.002, 2, 10.9, 0.002, 0, 11.133, -0.001, 2, 11.183, -0.001, 2, 11.2, -0.001, 2, 11.217, -0.001, 0, 11.267, 0, 2, 11.283, 0, 2, 11.3, 0, 2, 11.317, 0, 2, 11.35, 0, 2, 11.4, 0, 2, 11.417, 0, 2, 11.433, 0, 2, 11.45, 0, 2, 11.5, 0, 2, 11.517, 0, 2, 11.533, 0, 2, 11.567, 0, 2, 11.617, 0, 2, 11.633, 0, 2, 11.65, 0, 2, 11.667, 0, 2, 11.817, 0, 2, 11.833, 0, 2, 12.217, 0, 2, 12.233, 0, 2, 12.25, 0, 2, 12.267, 0, 2, 12.283, 0, 2, 12.3, 0, 2, 12.317, 0, 2, 12.333, 0, 2, 12.35, 0, 2, 12.367, 0, 2, 12.45, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.5, 0, 2, 12.517, 0, 2, 12.533, 0, 2, 12.55, 0, 2, 12.567, 0, 2, 12.583, 0, 2, 12.6, 0, 2, 12.633, 0, 2, 12.65, 0, 2, 12.667, 0, 2, 12.683, 0, 2, 12.7, 0, 2, 12.717, 0, 2, 12.733, 0, 2, 12.75, 0, 2, 12.767, 0, 2, 12.783, 0, 2, 12.8, 0, 2, 12.817, 0, 2, 12.833, 0, 2, 12.85, 0, 2, 12.883, 0, 2, 12.9, 0, 2, 12.917, 0, 2, 13, 0, 2, 13.017, 0, 2, 13.033, 0, 2, 13.117, 0, 2, 13.133, 0, 2, 13.15, 0, 2, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamEyeRGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.317, 0, 0, 0.433, -0.258, 0, 0.667, 0.75, 0, 0.95, -0.857, 0, 1.267, 0.306, 0, 1.433, -0.246, 0, 1.617, 0.553, 0, 1.9, -0.93, 0, 2.233, 0.31, 0, 2.533, -0.125, 0, 2.833, 0.047, 2, 2.85, 0.047, 0, 3.033, -0.254, 0, 3.233, 0.773, 0, 3.317, 0.346, 0, 3.533, 0.987, 0, 3.883, -0.38, 0, 4.2, 0.16, 0, 4.467, -0.34, 0, 4.717, 0.591, 0, 5.033, -0.286, 0, 5.333, 0.12, 0, 5.65, -0.045, 0, 5.95, 0.016, 2, 5.967, 0.016, 0, 6.25, -0.005, 2, 6.267, -0.005, 0, 6.567, 0.002, 2, 6.583, 0.002, 2, 6.6, 0.002, 0, 6.833, 0, 2, 6.867, 0, 2, 6.883, 0, 2, 6.9, 0, 2, 6.917, 0, 2, 6.95, 0, 2, 7, 0, 2, 7.017, 0, 2, 7.05, 0, 2, 7.067, 0, 2, 7.083, 0, 2, 7.133, 0, 2, 7.15, 0, 2, 7.233, 0, 2, 7.25, 0, 2, 7.267, 0, 2, 7.283, 0, 2, 7.333, 0, 2, 7.35, 0, 2, 7.417, 0, 2, 7.433, 0, 2, 7.45, 0, 2, 7.467, 0, 2, 7.483, 0, 2, 7.5, 0, 2, 7.517, 0, 2, 7.55, 0, 2, 7.567, 0, 2, 7.583, 0, 2, 7.617, 0, 2, 7.633, 0, 2, 7.95, 0, 2, 7.967, 0, 2, 7.983, 0, 2, 8, 0, 2, 8.017, 0, 2, 8.033, 0, 2, 8.05, 0, 2, 8.067, 0, 2, 8.083, 0, 2, 8.1, 0, 2, 8.183, 0, 2, 8.2, 0, 2, 8.217, 0, 2, 8.233, 0, 2, 8.25, 0, 2, 8.267, 0, 2, 8.283, 0, 2, 8.3, 0, 2, 8.317, 0, 2, 8.333, 0, 2, 8.35, 0, 2, 8.367, 0, 2, 8.383, 0, 2, 8.4, 0, 2, 8.417, 0, 2, 8.433, 0, 2, 8.45, 0, 2, 8.467, 0, 2, 8.483, 0, 2, 8.5, 0, 2, 8.517, 0, 2, 8.533, 0, 2, 8.55, 0, 2, 8.567, 0, 2, 8.583, 0, 2, 8.6, 0, 2, 8.617, 0, 2, 8.633, 0, 2, 8.65, 0, 2, 8.683, 0, 2, 8.7, 0, 2, 8.717, 0, 2, 8.733, 0, 2, 8.75, 0, 2, 8.767, 0, 2, 8.8, 0, 2, 8.817, 0, 2, 8.833, 0, 2, 8.85, 0, 2, 8.867, 0, 2, 8.883, 0, 2, 8.967, 0, 2, 8.983, 0, 2, 9, 0, 2, 9.2, 0, 2, 9.217, 0, 2, 9.233, 0, 2, 10.083, 0, 2, 10.1, 0, 2, 10.117, 0, 2, 10.133, 0, 2, 10.15, 0, 2, 10.167, 0, 2, 10.183, 0, 2, 10.2, 0, 2, 10.217, 0, 2, 10.233, 0, 2, 10.317, 0, 2, 10.333, 0, 2, 10.35, 0, 2, 10.367, 0, 2, 10.383, 0, 2, 10.4, 0, 2, 10.417, 0, 2, 10.433, 0, 2, 10.45, 0, 2, 10.467, 0, 2, 10.483, 0, 2, 10.5, 0, 2, 10.517, 0, 2, 10.533, 0, 2, 10.55, 0, 2, 10.567, 0, 2, 10.583, 0, 2, 10.6, 0, 2, 10.617, 0, 2, 10.633, 0, 2, 10.65, 0, 2, 10.667, 0, 2, 10.683, 0, 2, 10.7, 0, 2, 10.717, 0, 2, 10.733, 0, 2, 10.75, 0, 0, 10.883, 0.002, 2, 10.9, 0.002, 0, 11.133, -0.001, 2, 11.183, -0.001, 2, 11.2, -0.001, 2, 11.217, -0.001, 0, 11.267, 0, 2, 11.283, 0, 2, 11.3, 0, 2, 11.317, 0, 2, 11.35, 0, 2, 11.4, 0, 2, 11.417, 0, 2, 11.433, 0, 2, 11.45, 0, 2, 11.5, 0, 2, 11.517, 0, 2, 11.533, 0, 2, 11.567, 0, 2, 11.617, 0, 2, 11.633, 0, 2, 11.65, 0, 2, 11.667, 0, 2, 11.817, 0, 2, 11.833, 0, 2, 12.217, 0, 2, 12.233, 0, 2, 12.25, 0, 2, 12.267, 0, 2, 12.283, 0, 2, 12.3, 0, 2, 12.317, 0, 2, 12.333, 0, 2, 12.35, 0, 2, 12.367, 0, 2, 12.45, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.5, 0, 2, 12.517, 0, 2, 12.533, 0, 2, 12.55, 0, 2, 12.567, 0, 2, 12.583, 0, 2, 12.6, 0, 2, 12.633, 0, 2, 12.65, 0, 2, 12.667, 0, 2, 12.683, 0, 2, 12.7, 0, 2, 12.717, 0, 2, 12.733, 0, 2, 12.75, 0, 2, 12.767, 0, 2, 12.783, 0, 2, 12.8, 0, 2, 12.817, 0, 2, 12.833, 0, 2, 12.85, 0, 2, 12.883, 0, 2, 12.9, 0, 2, 12.917, 0, 2, 13, 0, 2, 13.017, 0, 2, 13.033, 0, 2, 13.117, 0, 2, 13.133, 0, 2, 13.15, 0, 2, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -8, 0, 2.083, 0, 2, 4.183, 0, 2, 6.317, 0, 2, 8.433, 0, 2, 10.567, 0, 2, 12.65, 0, 0, 14.817, -8]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.216, 0, 2.433, 0.26, 0, 4.9, 0.216, 0, 7.4, 0.26, 0, 9.883, 0.216, 0, 12.367, 0.26, 0, 14.817, 0.216]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.05, -0.1, 0, 2.117, 0, 0, 3.167, -0.1, 0, 4.233, 0, 0, 5.283, -0.1, 0, 6.35, 0, 0, 7.4, -0.1, 0, 8.467, 0, 0, 9.517, -0.1, 0, 10.583, 0, 0, 11.633, -0.1, 0, 12.7, 0, 0, 13.75, -0.1, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.5, 0, 0.717, -5.8, 0, 1.85, -5.5, 0, 2.567, -5.8, 0, 3.7, -5.5, 0, 4.383, -5.8, 0, 5.55, -5.5, 0, 6.233, -5.8, 0, 7.383, -5.5, 0, 8.117, -5.8, 0, 9.25, -5.5, 0, 9.967, -5.8, 0, 11.1, -5.5, 0, 11.817, -5.8, 0, 12.967, -5.5, 0, 13.667, -5.8, 0, 14.817, -5.5]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.13, 0, 0.417, 0.172, 0, 1.233, 0.002, 1, 1.439, 0.002, 1.644, 0.07, 1.85, 0.13, 1, 1.989, 0.17, 2.128, 0.172, 2.267, 0.172, 0, 3.117, 0.002, 1, 3.311, 0.002, 3.506, 0.073, 3.7, 0.13, 1, 3.839, 0.171, 3.978, 0.172, 4.117, 0.172, 0, 4.95, 0.002, 1, 5.15, 0.002, 5.35, 0.071, 5.55, 0.13, 1, 5.689, 0.171, 5.828, 0.172, 5.967, 0.172, 0, 6.8, 0.002, 1, 6.994, 0.002, 7.189, 0.075, 7.383, 0.13, 1, 7.528, 0.171, 7.672, 0.172, 7.817, 0.172, 0, 8.65, 0.002, 1, 8.85, 0.002, 9.05, 0.071, 9.25, 0.13, 1, 9.389, 0.171, 9.528, 0.172, 9.667, 0.172, 0, 10.483, 0.002, 1, 10.689, 0.002, 10.894, 0.068, 11.1, 0.13, 1, 11.233, 0.17, 11.367, 0.172, 11.5, 0.172, 0, 12.35, 0.002, 1, 12.556, 0.002, 12.761, 0.068, 12.967, 0.13, 1, 13.1, 0.17, 13.234, 0.172, 13.367, 0.172, 0, 14.2, 0.002, 0, 14.817, 0.13]}, {"Target": "Parameter", "Id": "Change_idle_physics12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.4, -0.479, 0, 0.85, -0.002, 0, 1.367, -0.555, 0, 2.083, 0, 0, 2.517, -0.479, 0, 2.967, -0.002, 0, 3.483, -0.555, 0, 4.183, 0, 0, 4.633, -0.479, 0, 5.05, -0.002, 0, 5.583, -0.555, 0, 6.317, 0, 0, 6.717, -0.479, 0, 7.183, -0.002, 0, 7.717, -0.555, 0, 8.433, 0, 0, 8.85, -0.479, 0, 9.3, -0.002, 0, 9.833, -0.555, 0, 10.567, 0, 0, 10.967, -0.479, 0, 11.433, -0.002, 0, 11.967, -0.555, 0, 12.65, 0, 0, 13.083, -0.479, 0, 13.55, -0.002, 0, 14.05, -0.555, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2, 1, 0.178, 2, 0.355, 2.019, 0.533, 3.313, 1, 0.666, 4.283, 0.8, 7.399, 0.933, 7.399, 0, 1.3, 3.329, 0, 2.083, 6.876, 0, 2.65, 3.313, 0, 3.05, 7.399, 0, 3.433, 3.329, 0, 4.183, 6.876, 0, 4.767, 3.313, 0, 5.15, 7.399, 0, 5.55, 3.329, 0, 6.317, 6.876, 0, 6.883, 3.313, 0, 7.267, 7.399, 0, 7.667, 3.329, 0, 8.433, 6.876, 0, 9, 3.313, 0, 9.4, 7.399, 0, 9.767, 3.329, 0, 10.567, 6.876, 0, 11.133, 3.313, 0, 11.517, 7.399, 0, 11.9, 3.329, 0, 12.65, 6.876, 0, 13.233, 3.313, 0, 13.633, 7.399, 1, 13.761, 7.399, 13.889, 3.912, 14.017, 3.329, 1, 14.284, 2.112, 14.55, 2, 14.817, 2]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 6.738, 0, 0.6, 0.9, 0, 2.083, 6.738, 0, 2.733, 0.9, 0, 4.183, 6.738, 0, 4.833, 0.9, 0, 6.317, 6.738, 0, 6.967, 0.9, 0, 8.433, 6.738, 0, 9.083, 0.9, 0, 10.567, 6.738, 0, 11.2, 0.9, 0, 12.65, 6.738, 0, 13.3, 0.9, 0, 14.817, 6.738]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -9.45, 0, 0.7, -8.9, 0, 2.083, -9.45, 0, 2.8, -8.9, 0, 4.183, -9.45, 0, 4.917, -8.9, 0, 6.317, -9.45, 0, 7.05, -8.9, 0, 8.433, -9.45, 0, 9.167, -8.9, 0, 10.567, -9.45, 0, 11.267, -8.9, 0, 12.65, -9.45, 0, 13.383, -8.9, 0, 14.817, -9.45]}, {"Target": "Parameter", "Id": "ParamBodyStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.562, 0, 0.583, -2.837, 0, 1.1, 0.769, 0, 1.517, -3.285, 0, 2.083, 1.562, 0, 2.7, -2.837, 0, 3.2, 0.769, 0, 3.65, -3.285, 0, 4.183, 1.562, 0, 4.817, -2.837, 0, 5.317, 0.769, 0, 5.767, -3.285, 0, 6.317, 1.562, 0, 6.95, -2.837, 0, 7.433, 0.769, 0, 7.9, -3.285, 0, 8.433, 1.562, 0, 9.05, -2.837, 0, 9.55, 0.769, 0, 10.017, -3.285, 0, 10.567, 1.562, 0, 11.167, -2.837, 0, 11.667, 0.769, 0, 12.1, -3.285, 0, 12.65, 1.562, 0, 13.283, -2.837, 0, 13.783, 0.769, 0, 14.217, -3.285, 0, 14.817, 1.562]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.316, 0, 0.817, 0.4, 0, 2.083, 0.316, 0, 2.917, 0.4, 0, 4.183, 0.316, 0, 5.033, 0.4, 0, 6.317, 0.316, 0, 7.167, 0.4, 0, 8.433, 0.316, 0, 9.283, 0.4, 0, 10.567, 0.316, 0, 11.417, 0.4, 0, 12.65, 0.316, 0, 13.483, 0.4, 0, 14.817, 0.316]}, {"Target": "Parameter", "Id": "Physics_AngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.152, 2, 0.017, 1.152, 1, 0.334, 1.153, 0.65, 4.496, 0.967, 4.496, 0, 1.367, 1.621, 0, 2.1, 4.061, 0, 2.667, 1.745, 0, 3.083, 4.52, 0, 3.483, 1.627, 0, 4.2, 4.069, 0, 4.783, 1.751, 0, 5.183, 4.525, 0, 5.6, 1.633, 0, 6.333, 4.065, 0, 6.9, 1.746, 0, 7.3, 4.525, 0, 7.717, 1.633, 0, 8.45, 4.065, 0, 9.017, 1.745, 0, 9.433, 4.519, 0, 9.833, 1.619, 0, 10.583, 4.058, 0, 11.15, 1.746, 0, 11.55, 4.524, 0, 11.95, 1.627, 0, 12.667, 4.069, 0, 13.25, 1.751, 0, 13.667, 4.522, 0, 14.767, 1.14, 1, 14.784, 1.14, 14.8, 1.14, 14.817, 1.141]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.333, 1, 0.128, -1.332, 0.255, 1.513, 0.383, 1.513, 0, 0.883, -4.785, 0, 1.35, 1.442, 0, 2.083, -4.584, 0, 2.55, 0.738, 0, 3.017, -4.77, 0, 3.467, 1.456, 0, 4.183, -4.584, 0, 4.667, 0.741, 0, 5.117, -4.703, 0, 5.583, 1.468, 0, 6.317, -4.584, 0, 6.767, 0.622, 0, 7.233, -4.771, 0, 7.7, 1.457, 0, 8.433, -4.584, 0, 8.9, 0.679, 0, 9.35, -4.712, 0, 9.817, 1.444, 0, 10.567, -4.584, 0, 11.017, 0.622, 0, 11.483, -4.771, 0, 11.95, 1.44, 0, 12.65, -4.584, 0, 13.133, 0.697, 0, 13.6, -4.763, 0, 14.117, 1.673, 0, 14.817, -1.333]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.333, 1, 0.128, 1.332, 0.255, -1.513, 0.383, -1.513, 0, 0.883, 4.785, 0, 1.35, -1.442, 0, 2.083, 4.584, 0, 2.55, -0.738, 0, 3.017, 4.77, 0, 3.467, -1.456, 0, 4.183, 4.584, 0, 4.667, -0.741, 0, 5.117, 4.703, 0, 5.583, -1.468, 0, 6.317, 4.584, 0, 6.767, -0.622, 0, 7.233, 4.771, 0, 7.7, -1.457, 0, 8.433, 4.584, 0, 8.9, -0.679, 0, 9.35, 4.712, 0, 9.817, -1.444, 0, 10.567, 4.584, 0, 11.017, -0.622, 0, 11.483, 4.771, 0, 11.95, -1.44, 0, 12.65, 4.584, 0, 13.133, -0.697, 0, 13.6, 4.763, 0, 14.117, -1.673, 0, 14.817, 1.333]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_L", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.004, 2, 0.017, -0.004, 0, 0.317, 0.017, 0, 0.967, -0.999, 0, 1.4, 2.017, 0, 1.783, -1.378, 0, 2.333, 0.884, 0, 3.1, -1.071, 0, 3.5, 2.234, 0, 3.9, -1.581, 0, 4.4, 0.866, 0, 4.55, 0.038, 0, 4.783, 0.416, 0, 5.133, -0.97, 0, 5.7, 0.808, 0, 6.1, -1.08, 0, 6.6, 0.662, 0, 7.35, -1.422, 0, 7.717, 2.307, 0, 8.133, -1.563, 0, 8.633, 0.793, 0, 8.833, 0.296, 0, 8.933, 0.315, 0, 9.367, -0.871, 0, 9.767, 0.685, 2, 9.783, 0.685, 0, 9.917, 0.805, 0, 10.317, -1.071, 0, 10.817, 0.517, 2, 10.833, 0.517, 0, 10.95, 0.294, 0, 11.083, 0.362, 0, 11.6, -1.54, 0, 11.967, 1.887, 0, 12.417, -1.438, 0, 12.883, 0.805, 0, 13.1, 0.306, 0, 13.133, 0.307, 0, 13.583, -0.868, 0, 14.067, 1.367, 2, 14.083, 1.367, 0, 14.517, -0.862, 1, 14.617, -0.862, 14.717, -0.24, 14.817, 0.175]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_R", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.004, 2, 0.017, 0.004, 0, 0.317, -0.017, 0, 1, 0.744, 0, 1.383, -1.463, 0, 1.817, 1.181, 0, 2.367, -0.648, 0, 3.117, 1.085, 0, 3.517, -1.671, 0, 3.933, 1.33, 0, 4.483, -0.796, 0, 5.217, 1.1, 0, 5.617, -1.659, 0, 6.05, 1.313, 0, 6.583, -0.724, 0, 7.333, 1.115, 0, 7.733, -1.658, 0, 8.167, 1.31, 0, 8.7, -0.728, 0, 9.467, 1.065, 0, 9.85, -1.701, 0, 10.283, 1.312, 0, 10.8, -0.687, 0, 11.583, 1.1, 0, 11.983, -1.673, 0, 12.417, 1.324, 0, 12.95, -0.801, 0, 13.683, 1.042, 0, 14.1, -1.544, 0, 14.517, 0.878, 1, 14.617, 0.878, 14.717, 0.292, 14.817, -0.127]}, {"Target": "Parameter", "Id": "L_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 30, 2, 0.367, 30, 0, 0.633, 26.078, 0, 0.933, 30, 2, 2.533, 30, 0, 2.817, 25.916, 0, 3.083, 30, 2, 4.633, 30, 0, 4.933, 26, 0, 5.183, 30, 2, 6.767, 30, 0, 7.05, 25.813, 0, 7.3, 30, 2, 8.883, 30, 0, 9.167, 25.915, 0, 9.433, 30, 2, 11, 30, 0, 11.3, 25.905, 0, 11.55, 30, 2, 13.1, 30, 0, 13.4, 26.1, 0, 13.667, 30, 2, 14.817, 30]}, {"Target": "Parameter", "Id": "L_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -6.244, 0, 0.567, 17.768, 0, 1.1, -10.026, 0, 1.433, 0.502, 0, 1.883, -17.144, 0, 2.133, 5.357, 0, 2.35, 0.635, 0, 2.467, 1.674, 0, 3.033, -1.911, 0, 3.45, 0.892, 0, 3.633, -2.754, 0, 3.817, 0.192, 0, 4.033, -0.495, 0, 4.55, 2.611, 0, 4.583, 2.528, 0, 4.65, 3.656, 0, 5.133, -1.722, 0, 5.25, 0.352, 0, 5.4, -0.178, 0, 5.583, 0.568, 0, 5.733, -2.631, 0, 6, -0.095, 0, 6.167, -0.315, 0, 6.683, 2.665, 0, 6.717, 2.586, 0, 6.8, 4.111, 0, 7.35, -2.873, 0, 7.633, 1.269, 0, 7.85, -2.913, 0, 8.033, 0.541, 0, 8.25, -0.638, 0, 8.8, 2.637, 0, 8.833, 2.567, 0, 8.917, 4.168, 0, 9.283, -1.707, 0, 9.783, 1.219, 0, 10, -2.297, 0, 10.183, -0.1, 0, 10.367, -0.391, 0, 10.933, 2.738, 0, 11.133, 0.264, 0, 11.2, 0.354, 0, 11.6, -3.748, 0, 11.883, 1.663, 0, 12.1, -3.592, 0, 12.35, 0.28, 0, 12.55, -0.406, 0, 13, 2.625, 0, 13.05, 2.529, 0, 13.117, 3.654, 0, 13.7, -2.283, 0, 14.017, 0.911, 0, 14.117, -0.378, 0, 14.167, 0.056, 0, 14.683, -7.261, 1, 14.728, -7.261, 14.772, -6.344, 14.817, -6.244]}, {"Target": "Parameter", "Id": "R_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 30, 2, 0.4, 30, 0, 0.567, 27.898, 0, 0.767, 30, 2, 1.017, 30, 0, 1.167, 28.535, 0, 1.3, 30, 2, 2.4, 30, 0, 2.65, 26.7, 0, 2.85, 30, 2, 3.15, 30, 0, 3.3, 28.479, 0, 3.433, 30, 2, 4.5, 30, 0, 4.75, 26.599, 0, 4.95, 30, 2, 5.267, 30, 0, 5.4, 28.496, 0, 5.533, 30, 2, 6.633, 30, 0, 6.883, 26.703, 0, 7.083, 30, 2, 7.367, 30, 0, 7.517, 28.434, 0, 7.65, 30, 2, 8.75, 30, 0, 9, 26.702, 0, 9.2, 30, 2, 9.5, 30, 0, 9.65, 28.25, 0, 9.783, 30, 2, 10.883, 30, 0, 11.117, 26.511, 0, 11.317, 30, 2, 11.633, 30, 0, 11.767, 28.491, 0, 11.9, 30, 2, 12.967, 30, 0, 13.217, 26.599, 0, 13.433, 30, 2, 13.75, 30, 0, 13.883, 28.585, 0, 14.033, 30, 2, 14.817, 30]}, {"Target": "Parameter", "Id": "R_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.367, 3.901, 0, 0.767, -2.52, 0, 1.067, 1.565, 0, 1.383, -4.047, 0, 1.75, 0.474, 0, 1.983, -0.141, 0, 2.417, 3.575, 0, 2.867, -2.736, 0, 3.167, 2.08, 0, 3.517, -4.229, 0, 3.883, 0.531, 0, 4.117, -0.017, 0, 4.533, 3.543, 0, 4.967, -2.902, 0, 5.267, 2.219, 0, 5.617, -4.131, 0, 5.983, 0.461, 0, 6.217, -0.096, 0, 6.65, 3.554, 0, 7.083, -2.782, 0, 7.4, 2.152, 0, 7.733, -4.126, 0, 8.1, 0.442, 0, 8.333, -0.102, 0, 8.767, 3.556, 0, 9.217, -2.725, 0, 9.517, 2.19, 0, 9.85, -4.337, 0, 10.217, 0.508, 0, 10.467, -0.214, 0, 10.9, 3.723, 0, 11.333, -3.021, 0, 11.65, 2.27, 0, 11.983, -4.299, 0, 12.35, 0.548, 0, 12.583, -0.017, 0, 13, 3.543, 0, 13.433, -2.833, 0, 13.75, 2.095, 0, 14.083, -3.613, 0, 14.45, 0.133, 0, 14.7, -0.453, 1, 14.739, -0.453, 14.778, -0.146, 14.817, 0]}, {"Target": "Parameter", "Id": "Jewelry_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.017, 0, 0, 0.733, -0.123, 0, 1.15, 0.379, 0, 1.533, -0.417, 0, 1.967, 0.261, 0, 2.883, -0.214, 0, 3.283, 0.443, 0, 3.667, -0.456, 0, 4.117, 0.294, 0, 4.617, -0.087, 0, 4.767, -0.079, 0, 4.983, -0.196, 0, 5.383, 0.427, 0, 5.767, -0.442, 0, 6.217, 0.278, 0, 6.75, -0.061, 0, 6.8, -0.06, 0, 7.117, -0.215, 0, 7.5, 0.439, 0, 7.9, -0.448, 0, 8.333, 0.283, 0, 8.85, -0.064, 0, 8.917, -0.063, 2, 8.933, -0.063, 0, 9.233, -0.203, 0, 9.617, 0.447, 0, 10.017, -0.452, 0, 10.45, 0.283, 0, 10.917, -0.053, 0, 11.067, -0.042, 0, 11.367, -0.22, 0, 11.75, 0.45, 0, 12.133, -0.457, 0, 12.583, 0.297, 0, 13.083, -0.091, 2, 13.1, -0.091, 0, 13.233, -0.083, 0, 13.45, -0.184, 0, 13.85, 0.408, 2, 13.867, 0.408, 0, 14.25, -0.337, 0, 14.667, 0.203, 1, 14.717, 0.203, 14.767, 0.063, 14.817, 0]}, {"Target": "Parameter", "Id": "Jewelry_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.001, 2, 0.05, 0.001, 2, 0.083, 0.001, 0, 0.233, 0.003, 2, 0.25, 0.003, 0, 0.967, -0.426, 0, 1.367, 0.98, 0, 1.75, -0.937, 0, 2.167, 0.505, 0, 2.583, 0.028, 0, 2.75, 0.058, 0, 3.117, -0.645, 0, 3.5, 1.102, 0, 3.883, -1.03, 0, 4.3, 0.536, 0, 4.783, -0.046, 0, 4.883, -0.022, 0, 5.217, -0.614, 0, 5.6, 1.059, 0, 5.983, -0.99, 0, 6.433, 0.545, 0, 6.85, -0.007, 0, 7, 0.033, 0, 7.333, -0.655, 0, 7.717, 1.111, 0, 8.1, -1.004, 0, 8.517, 0.502, 0, 9, 0.003, 0, 9.1, 0.024, 0, 9.467, -0.624, 0, 9.833, 1.092, 0, 10.233, -1.014, 0, 10.65, 0.545, 0, 11.05, -0.012, 0, 11.25, 0.071, 0, 11.583, -0.672, 0, 11.967, 1.139, 0, 12.35, -1.037, 0, 12.783, 0.56, 0, 13.25, -0.058, 0, 13.35, -0.037, 0, 13.683, -0.577, 0, 14.067, 1.052, 0, 14.467, -0.696, 1, 14.584, -0.696, 14.7, 0.177, 14.817, 0.395]}, {"Target": "Parameter", "Id": "Jewelry_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.002, 2, 0.033, 0.002, 2, 0.067, 0.002, 2, 0.083, 0.002, 2, 0.1, 0.002, 2, 0.117, 0.002, 0, 0.767, 0.073, 0, 1.167, -0.453, 0, 1.183, -0.448, 0, 1.217, -0.454, 0, 1.517, 0.891, 0, 1.883, -0.798, 0, 2.25, 0.451, 0, 2.6, -0.149, 0, 2.933, 0.248, 0, 3.25, -0.632, 0, 3.65, 0.952, 0, 4.017, -0.88, 0, 4.383, 0.527, 0, 4.75, -0.137, 0, 5.067, 0.204, 0, 5.367, -0.612, 0, 5.75, 0.927, 0, 6.117, -0.843, 0, 6.533, 0.436, 0, 6.883, -0.155, 0, 7.183, 0.247, 0, 7.517, -0.688, 0, 7.867, 1, 0, 8.233, -0.866, 0, 8.617, 0.507, 0, 8.967, -0.147, 0, 9.283, 0.227, 2, 9.3, 0.227, 0, 9.633, -0.669, 0, 9.983, 0.963, 0, 10.35, -0.87, 0, 10.767, 0.493, 0, 11.1, -0.196, 0, 11.433, 0.285, 0, 11.733, -0.683, 0, 12.117, 1.02, 0, 12.483, -0.9, 0, 12.867, 0.484, 0, 13.25, -0.134, 0, 13.533, 0.186, 0, 13.883, -0.644, 0, 14.217, 0.881, 0, 14.583, -0.704, 1, 14.661, -0.704, 14.739, -0.313, 14.817, 0.013]}, {"Target": "Parameter", "Id": "Physics_Joint_5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.002, 0, 0.3, 0.189, 0, 0.767, -0.411, 0, 1.25, 0.594, 0, 1.7, -0.736, 0, 2.183, 0.652, 0, 2.7, -0.417, 0, 3.333, 0.478, 0, 3.817, -0.63, 0, 4.3, 0.609, 0, 4.817, -0.405, 0, 5.45, 0.478, 0, 5.933, -0.621, 0, 6.417, 0.59, 0, 6.95, -0.384, 0, 7.567, 0.49, 0, 8.05, -0.615, 0, 8.55, 0.608, 0, 9.083, -0.426, 0, 9.683, 0.494, 0, 10.167, -0.596, 0, 10.667, 0.566, 0, 11.233, -0.421, 0, 11.8, 0.532, 0, 12.267, -0.68, 0, 12.75, 0.629, 0, 13.267, -0.394, 0, 13.933, 0.476, 0, 14.4, -0.614, 1, 14.539, -0.614, 14.678, -0.45, 14.817, 0.002]}, {"Target": "Parameter", "Id": "Physics_Joint_4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.002, 0, 0.15, -0.05, 0, 0.517, 0.59, 0, 1, -1.017, 0, 1.483, 1.414, 0, 1.917, -1.637, 0, 2.4, 1.334, 0, 2.983, -0.802, 0, 3.567, 1.13, 0, 4.033, -1.423, 0, 4.533, 1.265, 0, 5.117, -0.802, 0, 5.683, 1.128, 0, 6.167, -1.395, 0, 6.65, 1.219, 0, 7.267, -0.777, 0, 7.8, 1.169, 0, 8.283, -1.387, 0, 8.783, 1.267, 0, 9.35, -0.862, 0, 9.917, 1.137, 0, 10.4, -1.329, 0, 10.9, 1.177, 0, 11.5, -0.893, 0, 12.033, 1.255, 0, 12.5, -1.52, 0, 12.983, 1.294, 0, 13.567, -0.741, 0, 14.167, 1.155, 0, 14.617, -1.356, 1, 14.684, -1.356, 14.75, -0.628, 14.817, 0.002]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.068, 1, 0.244, -0.175, 0.489, -0.368, 0.733, -0.368, 0, 1.15, 1.136, 0, 1.533, -1.251, 0, 1.967, 0.783, 0, 2.883, -0.641, 0, 3.283, 1.328, 0, 3.667, -1.369, 0, 4.117, 0.883, 0, 4.617, -0.262, 0, 4.767, -0.237, 0, 4.983, -0.589, 0, 5.383, 1.282, 0, 5.767, -1.325, 0, 6.217, 0.835, 0, 6.75, -0.182, 0, 6.8, -0.181, 0, 7.117, -0.644, 0, 7.5, 1.315, 0, 7.9, -1.344, 0, 8.333, 0.849, 0, 8.85, -0.191, 0, 8.917, -0.188, 0, 9.233, -0.609, 0, 9.617, 1.341, 0, 10.017, -1.357, 0, 10.45, 0.85, 0, 10.917, -0.159, 0, 11.067, -0.127, 0, 11.367, -0.66, 0, 11.75, 1.349, 0, 12.133, -1.372, 0, 12.583, 0.891, 0, 13.1, -0.275, 0, 13.233, -0.249, 0, 13.45, -0.553, 0, 13.867, 1.224, 0, 14.25, -1.012, 0, 14.667, 0.609, 1, 14.717, 0.609, 14.767, 0.479, 14.817, 0.313]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.004, 1, 0.022, 0.003, 0.045, 0.003, 0.067, 0.003, 0, 0.233, 0.01, 2, 0.25, 0.01, 0, 0.967, -1.279, 0, 1.367, 2.94, 0, 1.75, -2.812, 0, 2.167, 1.515, 0, 2.583, 0.084, 0, 2.75, 0.174, 0, 3.117, -1.935, 0, 3.5, 3.305, 0, 3.883, -3.091, 0, 4.3, 1.607, 0, 4.783, -0.137, 0, 4.883, -0.066, 0, 5.217, -1.841, 0, 5.6, 3.178, 0, 5.983, -2.97, 0, 6.433, 1.636, 0, 6.85, -0.021, 0, 7, 0.099, 0, 7.333, -1.966, 0, 7.717, 3.333, 0, 8.1, -3.012, 0, 8.517, 1.506, 0, 9, 0.008, 0, 9.1, 0.071, 0, 9.467, -1.872, 0, 9.833, 3.276, 0, 10.233, -3.042, 0, 10.65, 1.634, 0, 11.05, -0.036, 0, 11.25, 0.214, 0, 11.583, -2.015, 0, 11.967, 3.418, 0, 12.35, -3.11, 0, 12.783, 1.679, 0, 13.25, -0.175, 0, 13.35, -0.11, 0, 13.683, -1.732, 0, 14.067, 3.155, 0, 14.467, -2.087, 1, 14.584, -2.087, 14.7, 0.53, 14.817, 1.184]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.005, 2, 0.017, 0.005, 2, 0.1, 0.005, 2, 0.117, 0.005, 0, 0.767, 0.22, 0, 1.167, -1.358, 0, 1.183, -1.343, 0, 1.217, -1.362, 0, 1.517, 2.672, 0, 1.883, -2.393, 0, 2.25, 1.354, 0, 2.6, -0.446, 0, 2.933, 0.745, 0, 3.25, -1.897, 0, 3.65, 2.857, 0, 4.017, -2.641, 0, 4.383, 1.582, 0, 4.75, -0.412, 0, 5.067, 0.613, 0, 5.367, -1.836, 0, 5.75, 2.78, 0, 6.117, -2.528, 0, 6.533, 1.307, 0, 6.883, -0.465, 0, 7.183, 0.742, 0, 7.517, -2.063, 0, 7.867, 3.001, 0, 8.233, -2.596, 0, 8.617, 1.522, 0, 8.967, -0.442, 0, 9.3, 0.68, 0, 9.633, -2.008, 0, 9.983, 2.89, 0, 10.35, -2.609, 0, 10.767, 1.478, 0, 11.1, -0.587, 0, 11.433, 0.854, 0, 11.733, -2.048, 0, 12.117, 3.06, 0, 12.483, -2.699, 0, 12.867, 1.452, 0, 13.25, -0.401, 0, 13.533, 0.56, 0, 13.883, -1.933, 0, 14.217, 2.643, 0, 14.583, -2.113, 1, 14.661, -2.113, 14.739, -1.331, 14.817, 0.005]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.006, 1, 0.028, 0.007, 0.055, 0.008, 0.083, 0.008, 0, 0.2, 0.007, 2, 0.217, 0.007, 0, 0.833, 0.222, 0, 1.183, -1.238, 0, 1.217, -1.143, 0, 1.317, -1.876, 0, 1.633, 3.48, 0, 1.983, -3.488, 0, 2.367, 2.36, 0, 2.7, -1.048, 0, 3.033, 1.053, 0, 3.367, -2.675, 0, 3.75, 3.718, 0, 4.117, -3.772, 0, 4.5, 2.511, 0, 4.85, -1.028, 0, 5.167, 0.882, 0, 5.5, -2.306, 0, 5.85, 3.587, 0, 6.217, -3.629, 0, 6.583, 2.022, 0, 6.967, -0.898, 0, 7.283, 0.985, 0, 7.617, -2.457, 0, 7.967, 3.878, 0, 8.333, -3.789, 0, 8.717, 2.342, 0, 9.067, -1.023, 0, 9.4, 0.97, 0, 9.733, -2.786, 0, 10.083, 3.971, 0, 10.45, -3.79, 0, 10.833, 2.044, 0, 11.2, -1.054, 0, 11.533, 1.159, 0, 11.867, -2.747, 0, 12.217, 3.994, 0, 12.583, -3.926, 0, 12.95, 2.468, 0, 13.317, -0.963, 0, 13.65, 0.806, 0, 14, -2.482, 0, 14.333, 3.599, 0, 14.683, -3.194, 1, 14.728, -3.194, 14.772, -2.544, 14.817, -1.695]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.003, 1, 0.05, 0.008, 0.1, 0.012, 0.15, 0.012, 0, 0.317, 0.009, 0, 0.9, 0.236, 0, 1.233, -1.074, 0, 1.3, -0.982, 0, 1.433, -1.431, 0, 1.75, 4.142, 0, 2.083, -4.908, 0, 2.467, 3.547, 0, 2.8, -1.972, 0, 3.133, 1.621, 0, 3.483, -3.205, 0, 3.85, 4.857, 0, 4.217, -5.257, 0, 4.583, 3.924, 0, 4.95, -2.021, 0, 5.267, 1.42, 0, 5.6, -2.839, 0, 5.967, 4.622, 0, 6.333, -5.053, 0, 6.683, 3.606, 0, 7.05, -1.724, 0, 7.383, 1.438, 0, 7.733, -3.02, 0, 8.083, 4.977, 0, 8.433, -5.425, 0, 8.8, 3.782, 0, 9.167, -1.97, 0, 9.5, 1.518, 0, 9.85, -3.163, 0, 10.2, 5.11, 0, 10.55, -5.38, 0, 10.917, 3.768, 0, 11.283, -1.911, 0, 11.633, 1.68, 0, 11.967, -3.198, 0, 12.333, 5.134, 0, 12.683, -5.524, 0, 13.05, 3.964, 0, 13.417, -1.965, 0, 13.75, 1.322, 0, 14.1, -2.572, 0, 14.45, 4.549, 0, 14.8, -4.629, 1, 14.806, -4.629, 14.811, -4.61, 14.817, -4.575]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.008, 1, 0.072, 0.005, 0.145, 0.019, 0.217, 0.019, 2, 0.233, 0.019, 0, 0.433, 0.01, 0, 0.967, 0.258, 0, 1.433, -1.531, 0, 1.85, 4.849, 0, 2.2, -6.502, 0, 2.55, 5.399, 0, 2.9, -3.397, 0, 3.25, 2.576, 0, 3.583, -4.054, 0, 3.95, 6.296, 0, 4.317, -7.201, 0, 4.683, 5.925, 0, 5.05, -3.563, 0, 5.383, 2.375, 0, 5.7, -3.495, 0, 6.067, 5.894, 0, 6.433, -6.919, 0, 6.783, 5.58, 0, 7.15, -3.157, 0, 7.483, 2.273, 0, 7.833, -3.911, 0, 8.183, 6.374, 0, 8.55, -7.382, 0, 8.9, 5.878, 0, 9.267, -3.494, 0, 9.6, 2.481, 0, 9.95, -3.923, 0, 10.3, 6.628, 0, 10.667, -7.453, 0, 11.017, 5.901, 0, 11.383, -3.43, 0, 11.733, 2.6, 0, 12.067, -4.301, 0, 12.433, 6.491, 0, 12.8, -7.87, 0, 13.15, 5.919, 0, 13.517, -3.46, 0, 13.85, 2.239, 0, 14.2, -3.304, 0, 14.55, 5.691, 1, 14.639, 5.691, 14.728, -1.354, 14.817, -4.708]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.031, 1, 0.1, -0.01, 0.2, 0.028, 0.3, 0.028, 0, 0.533, 0.01, 2, 0.55, 0.01, 0, 1.05, 0.286, 0, 1.533, -1.843, 0, 1.95, 5.667, 0, 2.3, -8.412, 0, 2.667, 7.874, 0, 3.017, -5.537, 0, 3.35, 4.133, 0, 3.7, -5.402, 0, 4.05, 8.149, 0, 4.417, -9.71, 0, 4.783, 8.647, 0, 5.15, -5.852, 0, 5.483, 3.959, 0, 5.8, -5.101, 0, 6.167, 7.636, 0, 6.533, -9.298, 0, 6.883, 8.219, 0, 7.25, -5.35, 0, 7.583, 3.707, 0, 7.933, -5.003, 0, 8.283, 8.131, 0, 8.65, -9.851, 0, 9, 8.684, 0, 9.367, -5.813, 0, 9.7, 3.922, 0, 10.05, -5.2, 0, 10.4, 8.334, 0, 10.767, -10.085, 0, 11.117, 8.698, 0, 11.483, -5.748, 0, 11.833, 4.231, 0, 12.183, -5.632, 0, 12.533, 8.48, 0, 12.9, -10.259, 0, 13.25, 9.063, 0, 13.617, -5.814, 0, 13.95, 3.786, 0, 14.3, -4.743, 0, 14.65, 7.427, 1, 14.706, 7.427, 14.761, 3.758, 14.817, -0.085]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.167, 1, 0, 3, 0, 0, 4.167, 1, 0, 6, 0, 0, 7.167, 1, 0, 9, 0, 0, 10.167, 1, 0, 12, 0, 0, 13.167, 1, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.817, 1]}, {"Target": "Parameter", "Id": "ParamSad2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.817, 1]}, {"Target": "Parameter", "Id": "ParamCRY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.2, 0, 14.817, -5.2]}, {"Target": "Parameter", "Id": "Position_Coordinates", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.817, 1]}, {"Target": "Parameter", "Id": "Man_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.817, 1]}, {"Target": "Parameter", "Id": "BG_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.817, 1]}, {"Target": "Parameter", "Id": "FG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "FG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "BG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "BG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "BG_White4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "BG_White5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "BG_White2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "BG_White3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.3, 0, 14.817, 0.3]}, {"Target": "Parameter", "Id": "All_Size2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -12.72, 0, 14.817, -12.72]}, {"Target": "Parameter", "Id": "All_Size3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "All_Size4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "tuerzuo3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "faqiehuan", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "tuerzuo4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Mail_Change", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.817, 1]}, {"Target": "Parameter", "Id": "Mail_Transparency5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 14.817, 0.7]}, {"Target": "Parameter", "Id": "ParamMouthOpenY_SD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "tongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamTongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "EyeFrame_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1, 0, 14.817, -1]}, {"Target": "Parameter", "Id": "ParamBrowForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1, 0, 14.817, -1]}, {"Target": "Parameter", "Id": "ParamBrowY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5, 0, 14.817, -5]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5, 0, 14.817, 5]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamSigh", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamDarken", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamCry", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_B", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_C", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "MB_Unidirectional_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "jing<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -21.288, 0, 14.817, -21.288]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 12.875, 0, 14.817, 12.875]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -6.222, 0, 14.817, -6.222]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -15.466, 0, 14.817, -15.466]}, {"Target": "Parameter", "Id": "ParamForearmR_RE3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamShoulderLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 14.817, -10]}, {"Target": "Parameter", "Id": "ParamArmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.817, 1]}, {"Target": "Parameter", "Id": "ParamForearmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -30, 0, 14.817, -30]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -6, 0, 14.817, -6]}, {"Target": "Parameter", "Id": "ParamArmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamArmL_ForearmL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -12.5, 0, 14.817, -12.5]}, {"Target": "Parameter", "Id": "ParamForearmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.817, 1]}, {"Target": "Parameter", "Id": "ParamForearmL_HandL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamHandLDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 6, 0, 14.817, 6]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamHandLSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.817, 1]}, {"Target": "Parameter", "Id": "Change_idle_physics9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "xiongjiya", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "xiongjiya5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "xiongjiya3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "xiongjiya4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "xiongjiya2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.98, 0, 14.817, -0.98]}, {"Target": "Parameter", "Id": "ParamForearmR_RE2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamShoulderRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5, 0, 14.817, -5]}, {"Target": "Parameter", "Id": "ParamArmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.817, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.76, 0, 14.817, -2.76]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamArmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamArmR_ForearmR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.951, 0, 14.817, 0.951]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 7.4, 0, 14.817, 7.4]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamForearm<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.817, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_HandR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamHandRDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2, 0, 14.817, 2]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamHandRSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.817, 1]}, {"Target": "Parameter", "Id": "Change_idle_physics8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 14.817, 0.6]}, {"Target": "Parameter", "Id": "Change_idle_physics23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.817, 1]}, {"Target": "Parameter", "Id": "Change_idle_physics24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.187, 0, 14.817, 0.187]}, {"Target": "Parameter", "Id": "Change_idle_physics5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.311, 0, 14.817, -0.311]}, {"Target": "Parameter", "Id": "Change_idle_physics6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.933, 0, 14.817, -0.933]}, {"Target": "Parameter", "Id": "Change_idle_physics10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.57, 0, 14.817, 0.57]}, {"Target": "Parameter", "Id": "Change_idle_physics11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.817, 1]}, {"Target": "Parameter", "Id": "Change_idle_physics13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.817, 1]}, {"Target": "Parameter", "Id": "Change_idle_physics14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Param103", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -30, 0, 14.817, -30]}, {"Target": "Parameter", "Id": "Change_idle_physics32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.01, 0, 14.817, 0.01]}, {"Target": "Parameter", "Id": "Change_idle_physics15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 14.817, -10]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 24, 0, 14.817, 24]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamLegL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.817, 1]}, {"Target": "Parameter", "Id": "ParamLegLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamLegLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamCrusLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamCrusLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>oodLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamFoodLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamFoodLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamLegR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.817, 1]}, {"Target": "Parameter", "Id": "ParamLegRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamLegRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamCrusRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamCrusRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamCrus<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>ood<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamFoodRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamFoodRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 14.817, 1]}, {"Target": "Parameter", "Id": "physics_Add_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "physics_Add_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamBodyStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Physics_AngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.411, 0, 14.817, -5.411]}, {"Target": "Parameter", "Id": "Physics_AngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Physics_Body_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Physics_Body_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Physics_breast", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamBreath2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "fenshangxuanzhuang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "touch_drag1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "touch_drag2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "touch_drag3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "touch_drag4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "touch_drag5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "touch_drag9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh654", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 14.817, 0]}], "UserData": []}