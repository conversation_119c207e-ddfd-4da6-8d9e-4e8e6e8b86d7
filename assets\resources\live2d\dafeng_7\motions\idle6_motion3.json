{"Version": 3, "Meta": {"Duration": 15.2, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 260, "TotalSegmentCount": 1986, "TotalPointCount": 2108, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.25, 1, 0.711, -0.337, 1.422, -0.425, 2.133, -0.512, 1, 2.139, -0.741, 2.144, -0.971, 2.15, -1.2, 1, 4.35, -1.307, 6.55, -1.415, 8.75, -1.522, 1, 8.756, -2.022, 8.761, -2.522, 8.767, -3.022, 1, 10.211, -1.211, 11.656, 0.599, 13.1, 2.41, 1, 13.106, 1.607, 13.111, 0.803, 13.117, 0, 1, 13.811, -0.083, 14.506, -0.167, 15.2, -0.25]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.79, 1, 0.711, -1, 1.422, -1.21, 2.133, -1.42, 1, 4.339, -0.114, 6.544, 1.192, 8.75, 2.498, 1, 8.756, 1.838, 8.761, 1.178, 8.767, 0.518, 1, 10.211, -0.498, 11.656, -1.514, 13.1, -2.53, 1, 13.106, -1.747, 13.111, -0.963, 13.117, -0.18, 1, 13.811, -0.383, 14.506, -0.587, 15.2, -0.79]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.5, 2, 2.133, 1.5, 1, 2.139, 3.5, 2.144, 5.5, 2.15, 7.5, 1, 4.35, 7.12, 6.55, 6.74, 8.75, 6.36, 1, 8.756, 4.356, 8.761, 2.351, 8.767, 0.347, 1, 10.211, 0.711, 11.656, 1.076, 13.1, 1.44, 1, 13.106, 1.46, 13.111, 1.48, 13.117, 1.5, 2, 15.2, 1.5]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 4.47, 1, 0.711, 4.58, 1.422, 4.69, 2.133, 4.8, 1, 2.139, 6.62, 2.144, 8.44, 2.15, 10.26, 1, 4.35, 9.9, 6.55, 9.54, 8.75, 9.18, 1, 8.756, 11.976, 8.761, 14.771, 8.767, 17.567, 1, 10.211, 15.808, 11.656, 14.049, 13.1, 12.29, 1, 13.106, 9.573, 13.111, 6.857, 13.117, 4.14, 1, 13.811, 4.25, 14.506, 4.36, 15.2, 4.47]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.467, -1, 2, 3.4, -1, 0, 3.933, 0.1, 2, 6.783, 0.1, 0, 8.567, -0.7, 0, 12.4, 1, 0, 13.333, -0.2, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY_SD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 1.467, 0.5, 2, 3.4, 0.5, 1, 3.578, 0.5, 3.755, 0.885, 3.933, 0.9, 1, 4.883, 0.978, 5.833, 1, 6.783, 1, 0, 8.567, 0.4, 1, 9.845, 0.4, 11.122, 0.439, 12.4, 0.6, 1, 12.711, 0.639, 13.022, 1, 13.333, 1, 2, 15.2, 1]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 0.267, 1.3, 0, 0.483, 0, 2, 0.933, 0, 0, 1.483, 0.538, 2, 2.267, 0.538, 0, 2.5, 0, 0, 2.617, 1.3, 0, 2.767, 1.252, 2, 3.417, 1.252, 0, 3.567, 0, 2, 11.683, 0, 0, 12.25, 1, 2, 12.75, 1, 2, 13.017, 1, 0, 13.233, 0, 0, 13.417, 1, 2, 15.2, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 0.267, 1.3, 0, 0.483, 0, 2, 0.933, 0, 0, 1.483, 0.538, 2, 2.267, 0.538, 0, 2.5, 0, 0, 2.617, 1.3, 0, 2.767, 1.252, 2, 3.417, 1.252, 0, 3.567, 0, 2, 11.683, 0, 0, 12.25, 1, 2, 12.75, 1, 2, 13.017, 1, 0, 13.233, 0, 0, 13.417, 1, 2, 15.2, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.267, 0, 1, 0.395, 0, 0.522, -0.377, 0.65, -0.5, 1, 0.767, -0.613, 0.883, -0.6, 1, -0.6, 1, 1.139, -0.6, 1.278, 0.46, 1.417, 0.5, 1, 1.739, 0.594, 2.061, 0.6, 2.383, 0.6, 0, 2.617, 0, 2, 4.233, 0, 2, 4.767, 0, 2, 12.75, 0, 2, 13.017, 0, 0, 13.4, 0.4, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.267, 0, 1, 0.395, 0, 0.522, -0.209, 0.65, -0.4, 1, 0.767, -0.575, 0.883, -0.6, 1, -0.6, 0, 1.417, 0.2, 0, 2.383, 0, 2, 2.617, 0, 2, 4.233, 0, 2, 4.767, 0, 2, 12.75, 0, 2, 13.017, 0, 0, 13.4, 1, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamBrowAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.284, 0, 0.45, 0.4, 0, 0.583, 0.284, 0, 1.033, 0.4, 0, 1.167, 0.284, 0, 1.617, 0.4, 0, 1.75, 0.284, 0, 2.2, 0.4, 0, 2.333, 0.284, 0, 2.783, 0.4, 0, 2.917, 0.284, 0, 3.367, 0.4, 0, 3.5, 0.284, 0, 3.95, 0.4, 0, 4.083, 0.284, 0, 4.533, 0.4, 0, 4.667, 0.284, 0, 5.117, 0.4, 0, 5.25, 0.284, 0, 5.7, 0.4, 0, 5.833, 0.284, 0, 6.283, 0.4, 0, 6.417, 0.284, 0, 6.867, 0.4, 0, 7, 0.284, 0, 7.45, 0.4, 0, 7.583, 0.284, 0, 8.033, 0.4, 0, 8.167, 0.284, 0, 8.617, 0.4, 0, 8.75, 0.284, 0, 9.2, 0.4, 0, 9.333, 0.284, 0, 9.783, 0.4, 0, 9.917, 0.284, 0, 10.367, 0.4, 0, 10.5, 0.284, 0, 10.95, 0.4, 0, 11.083, 0.284, 0, 11.533, 0.4, 0, 11.667, 0.284, 0, 12.117, 0.4, 0, 12.25, 0.284, 0, 12.7, 0.4, 0, 12.833, 0.284, 0, 13.283, 0.4, 0, 13.417, 0.284, 0, 13.867, 0.4, 2, 14, 0.4, 0, 15.2, 0.284]}, {"Target": "Parameter", "Id": "ParamEyeLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.583, 0, 2, 1.167, 0, 2, 1.75, 0, 2, 2.333, 0, 2, 2.917, 0, 2, 3.5, 0, 2, 4.083, 0, 2, 4.667, 0, 2, 5.25, 0, 2, 5.833, 0, 2, 6.417, 0, 2, 7, 0, 2, 7.583, 0, 2, 8.167, 0, 2, 8.75, 0, 2, 9.333, 0, 2, 9.917, 0, 2, 10.5, 0, 2, 11.083, 0, 2, 11.667, 0, 2, 12.25, 0, 2, 12.833, 0, 2, 13.417, 0, 2, 14, 0, 0, 14.333, 0.015, 2, 14.367, 0.015, 0, 14.4, 0.014, 2, 14.417, 0.014, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamEyeRsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.583, 0, 2, 1.167, 0, 2, 1.75, 0, 2, 2.333, 0, 2, 2.917, 0, 2, 3.5, 0, 2, 4.083, 0, 2, 4.667, 0, 2, 5.25, 0, 2, 5.833, 0, 2, 6.417, 0, 2, 7, 0, 2, 7.583, 0, 2, 8.167, 0, 2, 8.75, 0, 2, 9.333, 0, 2, 9.917, 0, 2, 10.5, 0, 2, 11.083, 0, 2, 11.667, 0, 2, 12.25, 0, 2, 12.833, 0, 2, 13.417, 0, 2, 14, 0, 0, 14.283, 0.005, 2, 14.3, 0.005, 2, 14.317, 0.005, 2, 14.383, 0.005, 2, 14.4, 0.005, 2, 14.433, 0.005, 2, 14.45, 0.005, 2, 14.467, 0.005, 0, 14.483, 0.004, 2, 14.517, 0.004, 2, 14.533, 0.004, 2, 14.567, 0.004, 2, 14.583, 0.004, 2, 14.6, 0.004, 2, 14.617, 0.004, 2, 14.633, 0.004, 2, 14.65, 0.004, 2, 14.667, 0.004, 2, 14.683, 0.004, 2, 14.7, 0.004, 2, 14.733, 0.004, 2, 14.75, 0.004, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamEyeLGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.583, 0, 2, 1.167, 0, 2, 1.75, 0, 2, 2.333, 0, 2, 2.917, 0, 2, 3.5, 0, 2, 4.083, 0, 2, 4.667, 0, 2, 5.25, 0, 2, 5.833, 0, 2, 6.417, 0, 2, 7, 0, 2, 7.583, 0, 2, 8.167, 0, 2, 8.75, 0, 2, 9.333, 0, 2, 9.917, 0, 2, 10.5, 0, 2, 11.083, 0, 2, 11.667, 0, 2, 12.25, 0, 2, 12.833, 0, 2, 13.417, 0, 2, 14, 0, 0, 14.133, -0.003, 2, 14.15, -0.003, 0, 14.433, 0.001, 2, 14.483, 0.001, 0, 14.667, 0, 2, 14.683, 0, 2, 14.7, 0, 2, 14.75, 0, 2, 14.767, 0, 2, 14.783, 0, 2, 14.85, 0, 2, 14.867, 0, 0, 14.967, 0.001, 2, 14.983, 0.001, 2, 15, 0.001, 2, 15.017, 0.001, 2, 15.033, 0.001, 2, 15.067, 0.001, 2, 15.083, 0.001, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamEyeRGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.583, 0, 2, 1.167, 0, 2, 1.75, 0, 2, 2.333, 0, 2, 2.917, 0, 2, 3.5, 0, 2, 4.083, 0, 2, 4.667, 0, 2, 5.25, 0, 2, 5.833, 0, 2, 6.417, 0, 2, 7, 0, 2, 7.583, 0, 2, 8.167, 0, 2, 8.75, 0, 2, 9.333, 0, 2, 9.917, 0, 2, 10.5, 0, 2, 11.083, 0, 2, 11.667, 0, 2, 12.25, 0, 2, 12.833, 0, 2, 13.417, 0, 2, 14, 0, 0, 14.133, -0.001, 2, 14.15, -0.001, 2, 14.167, -0.001, 2, 14.183, -0.001, 2, 14.217, -0.001, 2, 14.233, -0.001, 0, 14.367, 0, 2, 14.383, 0, 2, 14.4, 0, 2, 14.417, 0, 2, 14.433, 0, 2, 14.483, 0, 2, 14.5, 0, 2, 14.533, 0, 2, 14.55, 0, 2, 14.567, 0, 2, 14.583, 0, 2, 14.6, 0, 2, 14.617, 0, 2, 14.65, 0, 2, 14.667, 0, 2, 14.783, 0, 2, 14.8, 0, 2, 14.85, 0, 2, 14.867, 0, 2, 14.9, 0, 2, 14.917, 0, 2, 14.967, 0, 2, 14.983, 0, 2, 15.1, 0, 2, 15.117, 0, 2, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5, 2, 3.6, 5, 0, 3.617, 0, 2, 10.25, 0, 2, 11.083, 0, 0, 11.1, -5, 0, 15.2, 5]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.6, 0, 0, 3.617, -5, 2, 10.25, -5, 2, 11.083, -5, 1, 11.089, -5, 11.094, 5, 11.1, 5, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.374, 2, 0.583, -2.374, 2, 1.167, -2.374, 2, 1.75, -2.374, 2, 2.333, -2.374, 2, 2.917, -2.374, 2, 3.5, -2.374, 2, 4.083, -2.374, 2, 4.667, -2.374, 2, 5.25, -2.374, 2, 5.833, -2.374, 2, 6.417, -2.374, 2, 7, -2.374, 2, 7.583, -2.374, 2, 8.167, -2.374, 2, 8.75, -2.374, 2, 9.333, -2.374, 2, 9.917, -2.374, 2, 10.5, -2.374, 2, 11.083, -2.374, 2, 11.667, -2.374, 2, 12.25, -2.374, 2, 12.833, -2.374, 2, 13.417, -2.374, 0, 14, -3.7, 0, 15.2, -2.374]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 6.925, 2, 0.583, 6.925, 2, 1.167, 6.925, 2, 1.75, 6.925, 2, 2.333, 6.925, 2, 2.917, 6.925, 2, 3.5, 6.925, 2, 4.083, 6.925, 2, 4.667, 6.925, 2, 5.25, 6.925, 2, 5.833, 6.925, 2, 6.417, 6.925, 2, 7, 6.925, 2, 7.583, 6.925, 2, 8.167, 6.925, 2, 8.75, 6.925, 2, 9.333, 6.925, 2, 9.917, 6.925, 2, 10.5, 6.925, 2, 11.083, 6.925, 2, 11.667, 6.925, 2, 12.25, 6.925, 2, 12.833, 6.925, 2, 13.417, 6.925, 0, 14, 10.794, 0, 15.2, 6.925]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -4.027, 2, 0.583, -4.027, 2, 1.167, -4.027, 2, 1.75, -4.027, 2, 2.333, -4.027, 2, 2.917, -4.027, 2, 3.5, -4.027, 2, 4.083, -4.027, 2, 4.667, -4.027, 2, 5.25, -4.027, 2, 5.833, -4.027, 2, 6.417, -4.027, 2, 7, -4.027, 2, 7.583, -4.027, 2, 8.167, -4.027, 2, 8.75, -4.027, 2, 9.333, -4.027, 2, 9.917, -4.027, 2, 10.5, -4.027, 2, 11.083, -4.027, 2, 11.667, -4.027, 2, 12.25, -4.027, 2, 12.833, -4.027, 2, 13.417, -4.027, 0, 14, -5.284, 0, 15.2, -4.027]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10.008, 2, 0.583, -10.008, 2, 1.167, -10.008, 2, 1.75, -10.008, 2, 2.333, -10.008, 2, 2.917, -10.008, 2, 3.5, -10.008, 2, 4.083, -10.008, 2, 4.667, -10.008, 2, 5.25, -10.008, 2, 5.833, -10.008, 2, 6.417, -10.008, 2, 7, -10.008, 2, 7.583, -10.008, 2, 8.167, -10.008, 2, 8.75, -10.008, 2, 9.333, -10.008, 2, 9.917, -10.008, 2, 10.5, -10.008, 2, 11.083, -10.008, 2, 11.667, -10.008, 2, 12.25, -10.008, 2, 12.833, -10.008, 2, 13.417, -10.008, 0, 14, -13.132, 0, 15.2, -10.008]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.85, -0.1, 0, 1.683, 0, 0, 2.533, -0.1, 0, 3.383, 0, 0, 4.217, -0.1, 0, 5.067, 0, 0, 5.917, -0.1, 0, 6.75, 0, 0, 7.6, -0.1, 0, 8.45, 0, 0, 9.283, -0.1, 0, 10.133, 0, 0, 10.983, -0.1, 0, 11.817, 0, 0, 12.667, -0.1, 0, 13.5, 0, 0, 14.35, -0.1, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.5, 0, 0.483, -5.8, 0, 1.267, -5.5, 0, 1.75, -5.8, 0, 2.533, -5.5, 0, 3.017, -5.8, 0, 3.8, -5.5, 0, 4.283, -5.8, 0, 5.067, -5.5, 0, 5.55, -5.8, 0, 6.333, -5.5, 0, 6.817, -5.8, 0, 7.6, -5.5, 0, 8.083, -5.8, 0, 8.867, -5.5, 0, 9.35, -5.8, 0, 10.133, -5.5, 0, 10.617, -5.8, 0, 11.4, -5.5, 0, 11.883, -5.8, 0, 12.667, -5.5, 0, 13.15, -5.8, 0, 13.933, -5.5, 0, 14.417, -5.8, 0, 15.2, -5.5]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.13, 0, 0.283, 0.172, 0, 0.85, 0.002, 1, 0.989, 0.002, 1.128, 0.07, 1.267, 0.13, 1, 1.361, 0.171, 1.456, 0.172, 1.55, 0.172, 0, 2.117, 0.002, 1, 2.256, 0.002, 2.394, 0.07, 2.533, 0.13, 1, 2.628, 0.171, 2.722, 0.172, 2.817, 0.172, 0, 3.383, 0.002, 1, 3.522, 0.002, 3.661, 0.07, 3.8, 0.13, 1, 3.894, 0.17, 3.989, 0.172, 4.083, 0.172, 0, 4.65, 0.002, 1, 4.789, 0.002, 4.928, 0.07, 5.067, 0.13, 1, 5.161, 0.17, 5.256, 0.172, 5.35, 0.172, 0, 5.917, 0.002, 1, 6.056, 0.002, 6.194, 0.07, 6.333, 0.13, 1, 6.428, 0.17, 6.522, 0.172, 6.617, 0.172, 0, 7.183, 0.002, 1, 7.322, 0.002, 7.461, 0.07, 7.6, 0.13, 1, 7.694, 0.17, 7.789, 0.172, 7.883, 0.172, 0, 8.45, 0.002, 1, 8.589, 0.002, 8.728, 0.07, 8.867, 0.13, 1, 8.961, 0.17, 9.056, 0.172, 9.15, 0.172, 0, 9.717, 0.002, 1, 9.856, 0.002, 9.994, 0.07, 10.133, 0.13, 1, 10.228, 0.17, 10.322, 0.172, 10.417, 0.172, 0, 10.983, 0.002, 1, 11.122, 0.002, 11.261, 0.07, 11.4, 0.13, 1, 11.494, 0.17, 11.589, 0.172, 11.683, 0.172, 0, 12.25, 0.002, 1, 12.389, 0.002, 12.528, 0.07, 12.667, 0.13, 1, 12.761, 0.17, 12.856, 0.172, 12.95, 0.172, 0, 13.517, 0.002, 1, 13.656, 0.002, 13.794, 0.07, 13.933, 0.13, 1, 14.028, 0.17, 14.122, 0.172, 14.217, 0.172, 0, 14.783, 0.002, 0, 15.2, 0.13]}, {"Target": "Parameter", "Id": "Change_idle_physics12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.25, -0.479, 0, 0.517, 0, 0, 0.833, -0.691, 0, 1.267, 0, 0, 1.517, -0.479, 0, 1.783, -0.002, 0, 2.1, -0.691, 0, 2.533, 0, 0, 2.783, -0.479, 0, 3.05, -0.002, 0, 3.367, -0.691, 0, 3.8, 0, 0, 4.05, -0.479, 0, 4.317, -0.002, 0, 4.633, -0.691, 0, 5.067, 0, 0, 5.317, -0.479, 0, 5.583, -0.002, 0, 5.9, -0.691, 0, 6.333, 0, 0, 6.583, -0.479, 0, 6.85, -0.002, 0, 7.167, -0.691, 0, 7.6, 0, 0, 7.85, -0.479, 0, 8.117, -0.002, 0, 8.433, -0.691, 0, 8.867, 0, 0, 9.117, -0.479, 0, 9.383, -0.002, 0, 9.7, -0.691, 0, 10.133, 0, 0, 10.383, -0.479, 0, 10.65, -0.002, 0, 10.967, -0.691, 0, 11.4, 0, 0, 11.65, -0.479, 0, 11.917, -0.002, 0, 12.233, -0.691, 0, 12.667, 0, 0, 12.917, -0.479, 0, 13.183, -0.002, 0, 13.5, -0.691, 0, 13.933, 0, 0, 14.183, -0.479, 0, 14.45, -0.002, 0, 14.767, -0.691, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 6.876, 0, 0.333, 3.313, 0, 0.567, 7.399, 0, 0.8, 3.329, 0, 1.267, 6.876, 0, 1.6, 3.313, 0, 1.833, 7.399, 0, 2.067, 3.329, 0, 2.533, 6.876, 0, 2.867, 3.313, 0, 3.1, 7.399, 0, 3.333, 3.329, 0, 3.8, 6.876, 0, 4.133, 3.313, 0, 4.367, 7.399, 0, 4.6, 3.329, 0, 5.067, 6.876, 0, 5.4, 3.313, 0, 5.633, 7.399, 0, 5.867, 3.329, 0, 6.333, 6.876, 0, 6.667, 3.313, 0, 6.9, 7.399, 0, 7.133, 3.329, 0, 7.6, 6.876, 0, 7.933, 3.313, 0, 8.167, 7.399, 0, 8.4, 3.329, 0, 8.867, 6.876, 0, 9.2, 3.313, 0, 9.433, 7.399, 0, 9.667, 3.329, 0, 10.133, 6.876, 0, 10.467, 3.313, 0, 10.7, 7.399, 0, 10.933, 3.329, 0, 11.4, 6.876, 0, 11.733, 3.313, 0, 11.967, 7.399, 0, 12.2, 3.329, 0, 12.667, 6.876, 0, 13, 3.313, 0, 13.233, 7.399, 0, 13.467, 3.329, 0, 13.933, 6.876, 0, 14.267, 3.313, 0, 14.5, 7.399, 0, 14.733, 3.329, 0, 15.2, 6.876]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.938, 0, 0.383, 0.9, 0, 1.267, 5.938, 0, 1.65, 0.9, 0, 2.533, 5.938, 0, 2.917, 0.9, 0, 3.8, 5.938, 0, 4.183, 0.9, 0, 5.067, 5.938, 0, 5.45, 0.9, 0, 6.333, 5.938, 0, 6.717, 0.9, 0, 7.6, 5.938, 0, 7.983, 0.9, 0, 8.867, 5.938, 0, 9.25, 0.9, 0, 10.133, 5.938, 0, 10.517, 0.9, 0, 11.4, 5.938, 0, 11.783, 0.9, 0, 12.667, 5.938, 0, 13.05, 0.9, 0, 13.933, 5.938, 0, 14.317, 0.9, 0, 15.2, 5.938]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -9.45, 0, 0.433, -8.9, 0, 1.267, -9.45, 0, 1.7, -8.9, 0, 2.533, -9.45, 0, 2.967, -8.9, 0, 3.8, -9.45, 0, 4.233, -8.9, 0, 5.067, -9.45, 0, 5.5, -8.9, 0, 6.333, -9.45, 0, 6.767, -8.9, 0, 7.6, -9.45, 0, 8.033, -8.9, 0, 8.867, -9.45, 0, 9.3, -8.9, 0, 10.133, -9.45, 0, 10.567, -8.9, 0, 11.4, -9.45, 0, 11.833, -8.9, 0, 12.667, -9.45, 0, 13.1, -8.9, 0, 13.933, -9.45, 0, 14.367, -8.9, 0, 15.2, -9.45]}, {"Target": "Parameter", "Id": "ParamBodyStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.562, 0, 0.367, -2.837, 0, 0.667, 0.769, 0, 0.933, -6.03, 0, 1.267, 1.562, 0, 1.633, -2.837, 0, 1.933, 0.769, 0, 2.2, -6.03, 0, 2.533, 1.562, 0, 2.9, -2.837, 0, 3.2, 0.769, 0, 3.467, -6.03, 0, 3.8, 1.562, 0, 4.167, -2.837, 0, 4.467, 0.769, 0, 4.733, -6.03, 0, 5.067, 1.562, 0, 5.433, -2.837, 0, 5.733, 0.769, 0, 6, -6.03, 0, 6.333, 1.562, 0, 6.7, -2.837, 0, 7, 0.769, 0, 7.267, -6.03, 0, 7.6, 1.562, 0, 7.967, -2.837, 0, 8.267, 0.769, 0, 8.533, -6.03, 0, 8.867, 1.562, 0, 9.233, -2.837, 0, 9.533, 0.769, 0, 9.8, -6.03, 0, 10.133, 1.562, 0, 10.5, -2.837, 0, 10.8, 0.769, 0, 11.067, -6.03, 0, 11.4, 1.562, 0, 11.767, -2.837, 0, 12.067, 0.769, 0, 12.333, -6.03, 0, 12.667, 1.562, 0, 13.033, -2.837, 0, 13.333, 0.769, 0, 13.6, -6.03, 0, 13.933, 1.562, 0, 14.3, -2.837, 0, 14.6, 0.769, 0, 14.867, -6.03, 0, 15.2, 1.562]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.316, 0, 0.5, 0.4, 0, 1.267, 0.316, 0, 1.767, 0.4, 0, 2.533, 0.316, 0, 3.033, 0.4, 0, 3.8, 0.316, 0, 4.3, 0.4, 0, 5.067, 0.316, 0, 5.567, 0.4, 0, 6.333, 0.316, 0, 6.833, 0.4, 0, 7.6, 0.316, 0, 8.1, 0.4, 0, 8.867, 0.316, 0, 9.367, 0.4, 0, 10.133, 0.316, 0, 10.633, 0.4, 0, 11.4, 0.316, 0, 11.9, 0.4, 0, 12.667, 0.316, 0, 13.167, 0.4, 0, 13.933, 0.316, 0, 14.433, 0.4, 0, 15.2, 0.316]}, {"Target": "Parameter", "Id": "Physics_AngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 3.96, 0, 0.367, 1.708, 0, 0.617, 4.393, 0, 0.883, 1.71, 0, 1.3, 4.168, 0, 1.633, 1.702, 0, 1.883, 4.389, 0, 2.15, 1.71, 0, 2.567, 4.168, 0, 2.9, 1.702, 0, 3.15, 4.389, 0, 3.417, 1.71, 0, 3.833, 4.168, 0, 4.167, 1.702, 0, 4.417, 4.389, 0, 4.683, 1.71, 0, 5.1, 4.168, 0, 5.433, 1.702, 0, 5.683, 4.389, 0, 5.95, 1.71, 0, 6.367, 4.168, 0, 6.7, 1.702, 0, 6.95, 4.389, 0, 7.217, 1.71, 0, 7.633, 4.168, 0, 7.967, 1.702, 0, 8.217, 4.389, 0, 8.483, 1.71, 0, 8.9, 4.168, 0, 9.233, 1.702, 0, 9.483, 4.389, 0, 9.75, 1.71, 0, 10.167, 4.168, 0, 10.5, 1.702, 0, 10.75, 4.389, 0, 11.017, 1.71, 0, 11.433, 4.168, 0, 11.767, 1.702, 0, 12.017, 4.389, 0, 12.283, 1.71, 0, 12.7, 4.168, 0, 13.033, 1.702, 0, 13.283, 4.389, 0, 13.55, 1.71, 0, 13.967, 4.168, 0, 14.3, 1.702, 0, 14.55, 4.389, 0, 14.817, 1.73, 0, 15.2, 3.96]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.345, 0, 0.217, 3.714, 0, 0.5, -6.341, 0, 0.8, 5.994, 0, 1.15, -5.78, 0, 1.5, 7.838, 0, 1.8, -7.192, 0, 2.083, 4.888, 0, 2.433, -4.654, 0, 2.767, 5.976, 0, 3.067, -6.12, 0, 3.35, 5.114, 0, 3.7, -5.585, 0, 4.033, 7.298, 0, 4.333, -7.504, 0, 4.633, 5.06, 0, 4.967, -4.229, 0, 5.3, 5.739, 0, 5.6, -6.411, 0, 5.883, 5.732, 0, 6.233, -4.733, 0, 6.567, 5.996, 0, 6.867, -6.136, 0, 7.15, 5.117, 0, 7.5, -5.654, 0, 7.833, 7.642, 0, 8.133, -7.305, 0, 8.417, 5.708, 0, 8.783, -5.052, 0, 9.1, 6.323, 0, 9.4, -6.208, 0, 9.683, 5.223, 0, 10.033, -5.09, 0, 10.367, 6.77, 0, 10.667, -6.681, 0, 10.95, 4.574, 0, 11.3, -4.093, 0, 11.633, 6.246, 0, 11.933, -7.411, 0, 12.217, 5.545, 0, 12.567, -5.004, 0, 12.9, 6.507, 0, 13.2, -6.415, 0, 13.483, 5.071, 0, 13.833, -4.206, 0, 14.15, 6.72, 0, 14.467, -7.15, 0, 14.75, 5.163, 0, 15.1, -5.024, 0, 15.2, -0.345]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.962, 0, 0.117, -2.873, 0, 0.433, 10.607, 0, 0.7, -13.626, 0, 1, 14.491, 0, 1.367, -17.979, 0, 1.7, 19.937, 0, 2, -17.096, 0, 2.3, 13.085, 0, 2.633, -11.69, 0, 2.967, 15.161, 0, 3.25, -15.018, 0, 3.567, 14.133, 0, 3.9, -16.385, 0, 4.233, 19.433, 0, 4.533, -17.701, 0, 4.833, 12.938, 0, 5.183, -12.323, 0, 5.517, 15.543, 0, 5.8, -16.895, 0, 6.083, 13.792, 0, 6.433, -11.822, 0, 6.767, 15.247, 0, 7.05, -15.059, 0, 7.367, 14.296, 0, 7.683, -17.015, 0, 8.033, 19.581, 0, 8.333, -19.145, 0, 8.633, 14.655, 0, 8.983, -14.357, 0, 9.3, 16.41, 0, 9.583, -16.604, 0, 9.883, 13.703, 0, 10.25, -15.108, 0, 10.567, 17.592, 0, 10.867, -14.9, 0, 11.15, 11.447, 0, 11.483, -11.498, 0, 11.833, 17.145, 0, 12.133, -17.463, 0, 12.433, 14.196, 0, 12.783, -14.483, 0, 13.1, 16.856, 0, 13.4, -15.173, 0, 13.683, 12.026, 0, 14.033, -13.865, 0, 14.35, 17.62, 0, 14.65, -16.322, 0, 14.967, 13.148, 0, 15.2, -1.962]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_L", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.03, 0, 0.167, -0.302, 0, 0.467, 1.185, 0, 0.733, -1.724, 0, 1.017, 1.306, 0, 1.4, -1.166, 0, 1.733, 1.566, 0, 2, -1.527, 0, 2.283, 1.014, 0, 2.65, -1.012, 0, 3, 1.553, 0, 3.267, -1.63, 0, 3.55, 1.07, 0, 3.917, -1.02, 0, 4.267, 1.531, 0, 4.533, -1.601, 0, 4.817, 1.068, 0, 5.183, -1.028, 0, 5.533, 1.542, 0, 5.8, -1.606, 0, 6.083, 1.065, 0, 6.45, -1.024, 0, 6.8, 1.539, 0, 7.067, -1.606, 0, 7.35, 1.067, 0, 7.717, -1.025, 0, 8.067, 1.539, 0, 8.333, -1.605, 0, 8.617, 1.066, 0, 8.983, -1.025, 0, 9.333, 1.539, 0, 9.6, -1.606, 0, 9.883, 1.066, 0, 10.25, -1.025, 0, 10.6, 1.539, 0, 10.867, -1.606, 0, 11.15, 1.066, 0, 11.517, -1.025, 0, 11.867, 1.539, 0, 12.133, -1.606, 0, 12.417, 1.066, 0, 12.783, -1.025, 0, 13.133, 1.539, 0, 13.4, -1.606, 0, 13.683, 1.066, 0, 14.05, -1.025, 0, 14.4, 1.539, 0, 14.667, -1.606, 0, 14.95, 1.088, 0, 15.2, -0.03]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_R", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.03, 0, 0.167, 0.302, 0, 0.467, -1.185, 0, 0.733, 1.724, 0, 1.017, -1.306, 0, 1.4, 1.166, 0, 1.733, -1.566, 0, 2, 1.527, 0, 2.283, -1.014, 0, 2.65, 1.012, 0, 3, -1.553, 0, 3.267, 1.63, 0, 3.55, -1.07, 0, 3.917, 1.02, 0, 4.267, -1.531, 0, 4.533, 1.601, 0, 4.817, -1.068, 0, 5.183, 1.028, 0, 5.533, -1.542, 0, 5.8, 1.606, 0, 6.083, -1.065, 0, 6.45, 1.024, 0, 6.8, -1.539, 0, 7.067, 1.606, 0, 7.35, -1.067, 0, 7.717, 1.025, 0, 8.067, -1.539, 0, 8.333, 1.605, 0, 8.617, -1.066, 0, 8.983, 1.025, 0, 9.333, -1.539, 0, 9.6, 1.606, 0, 9.883, -1.066, 0, 10.25, 1.025, 0, 10.6, -1.539, 0, 10.867, 1.606, 0, 11.15, -1.066, 0, 11.517, 1.025, 0, 11.867, -1.539, 0, 12.133, 1.606, 0, 12.417, -1.066, 0, 12.783, 1.025, 0, 13.133, -1.539, 0, 13.4, 1.606, 0, 13.683, -1.066, 0, 14.05, 1.025, 0, 14.4, -1.539, 0, 14.667, 1.606, 0, 14.95, -1.088, 0, 15.2, 0.03]}, {"Target": "Parameter", "Id": "L_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 6.9, 0, 0.1, 7.269, 0, 0.5, -4.84, 0, 0.783, 8.78, 0, 1.067, 4.143, 0, 1.383, 8.409, 0, 1.767, -5.128, 0, 2.05, 8.829, 0, 2.333, 4.15, 0, 2.65, 8.404, 0, 3.033, -5.127, 0, 3.317, 8.828, 0, 3.6, 4.15, 0, 3.917, 8.404, 0, 4.3, -5.127, 0, 4.583, 8.828, 0, 4.867, 4.15, 0, 5.183, 8.404, 0, 5.567, -5.127, 0, 5.85, 8.828, 0, 6.133, 4.15, 0, 6.45, 8.404, 0, 6.833, -5.127, 0, 7.117, 8.828, 0, 7.4, 4.15, 0, 7.717, 8.404, 0, 8.1, -5.127, 0, 8.383, 8.828, 0, 8.667, 4.15, 0, 8.983, 8.404, 0, 9.367, -5.127, 0, 9.65, 8.828, 0, 9.933, 4.15, 0, 10.25, 8.404, 0, 10.633, -5.127, 0, 10.917, 8.828, 0, 11.2, 4.15, 0, 11.517, 8.404, 0, 11.9, -5.127, 0, 12.183, 8.828, 0, 12.467, 4.15, 0, 12.783, 8.404, 0, 13.167, -5.127, 0, 13.45, 8.828, 0, 13.733, 4.15, 0, 14.05, 8.404, 0, 14.433, -4.96, 0, 14.733, 11.982, 1, 14.811, 11.982, 14.889, 11.378, 14.967, 9.703, 1, 15.045, 8.028, 15.122, 6.9, 15.2, 6.9]}, {"Target": "Parameter", "Id": "L_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.001, 0, 0.05, -0.092, 0, 0.317, 4.703, 0, 0.65, -7.279, 0, 0.9, 4.548, 0, 1.167, -3.265, 0, 1.567, 5.419, 0, 1.917, -7.38, 0, 2.167, 4.612, 0, 2.433, -3.277, 0, 2.833, 5.422, 0, 3.183, -7.381, 0, 3.433, 4.612, 0, 3.7, -3.277, 0, 4.1, 5.422, 0, 4.45, -7.381, 0, 4.7, 4.612, 0, 4.967, -3.277, 0, 5.367, 5.422, 0, 5.717, -7.381, 0, 5.967, 4.612, 0, 6.233, -3.277, 0, 6.633, 5.422, 0, 6.983, -7.381, 0, 7.233, 4.612, 0, 7.5, -3.277, 0, 7.9, 5.422, 0, 8.25, -7.381, 0, 8.5, 4.612, 0, 8.767, -3.277, 0, 9.167, 5.422, 0, 9.517, -7.381, 0, 9.767, 4.612, 0, 10.033, -3.277, 0, 10.433, 5.422, 0, 10.783, -7.38, 0, 11.033, 4.612, 0, 11.3, -3.277, 0, 11.7, 5.422, 0, 12.05, -7.381, 0, 12.3, 4.612, 0, 12.567, -3.277, 0, 12.967, 5.422, 0, 13.317, -7.38, 0, 13.567, 4.612, 0, 13.833, -3.277, 0, 14.233, 5.422, 0, 14.583, -8.544, 0, 14.833, 3.568, 0, 15.1, -3.787, 0, 15.2, -0.001]}, {"Target": "Parameter", "Id": "R_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 6.939, 0, 0.35, -2.171, 0, 0.567, 3.478, 0, 0.75, 0.181, 0, 1.167, 10.738, 0, 1.617, -2.99, 0, 1.833, 3.329, 0, 2.017, 0.334, 0, 2.433, 10.713, 0, 2.883, -2.986, 0, 3.1, 3.333, 0, 3.283, 0.334, 0, 3.7, 10.713, 0, 4.15, -2.986, 0, 4.367, 3.333, 0, 4.55, 0.334, 0, 4.967, 10.713, 0, 5.417, -2.986, 0, 5.633, 3.333, 0, 5.817, 0.334, 0, 6.233, 10.713, 0, 6.683, -2.986, 0, 6.9, 3.333, 0, 7.083, 0.334, 0, 7.5, 10.713, 0, 7.95, -2.986, 0, 8.167, 3.333, 0, 8.35, 0.334, 0, 8.767, 10.713, 0, 9.217, -2.986, 0, 9.433, 3.333, 0, 9.617, 0.334, 0, 10.033, 10.713, 0, 10.483, -2.986, 0, 10.7, 3.333, 0, 10.883, 0.334, 0, 11.3, 10.713, 0, 11.75, -2.986, 0, 11.967, 3.333, 0, 12.15, 0.334, 0, 12.567, 10.713, 0, 13.017, -2.986, 0, 13.233, 3.333, 0, 13.417, 0.334, 0, 13.833, 10.713, 0, 14.283, -2.986, 0, 14.517, 3.947, 0, 14.65, 2.689, 0, 15.1, 18.28, 0, 15.2, 6.939]}, {"Target": "Parameter", "Id": "R_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.005, 0, 0.267, 5.791, 0, 0.533, -5.023, 0, 0.733, 2.09, 0, 0.983, -5.25, 0, 1.483, 6.095, 0, 1.783, -5.645, 0, 2, 2.393, 0, 2.25, -5.225, 0, 2.75, 6.096, 0, 3.05, -5.642, 0, 3.267, 2.394, 0, 3.517, -5.226, 0, 4.017, 6.096, 0, 4.317, -5.642, 0, 4.533, 2.394, 0, 4.783, -5.226, 0, 5.283, 6.096, 0, 5.583, -5.641, 0, 5.8, 2.394, 0, 6.05, -5.226, 0, 6.55, 6.096, 0, 6.85, -5.641, 0, 7.067, 2.394, 0, 7.317, -5.226, 0, 7.817, 6.096, 0, 8.117, -5.641, 0, 8.333, 2.394, 0, 8.583, -5.226, 0, 9.083, 6.096, 0, 9.383, -5.641, 0, 9.6, 2.394, 0, 9.85, -5.226, 0, 10.35, 6.096, 0, 10.65, -5.641, 0, 10.867, 2.394, 0, 11.117, -5.226, 0, 11.617, 6.096, 0, 11.917, -5.641, 0, 12.133, 2.394, 0, 12.383, -5.226, 0, 12.883, 6.096, 0, 13.183, -5.641, 0, 13.4, 2.394, 0, 13.65, -5.226, 0, 14.15, 6.096, 0, 14.467, -6.088, 0, 14.667, 0.38, 0, 14.9, -7.189, 0, 15.2, 0.005]}, {"Target": "Parameter", "Id": "Jewelry_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.014, 0, 0.233, 0.24, 0, 0.533, -0.513, 0, 0.8, 0.476, 0, 1.167, -0.391, 0, 1.533, 0.472, 0, 1.817, -0.541, 0, 2.083, 0.353, 0, 2.45, -0.295, 0, 2.8, 0.409, 0, 3.083, -0.528, 0, 3.35, 0.381, 0, 3.7, -0.318, 2, 3.717, -0.318, 0, 4.067, 0.425, 0, 4.35, -0.532, 0, 4.617, 0.374, 0, 4.983, -0.312, 0, 5.333, 0.421, 0, 5.617, -0.531, 0, 5.883, 0.376, 0, 6.25, -0.314, 0, 6.6, 0.422, 0, 6.883, -0.531, 0, 7.15, 0.376, 0, 7.517, -0.313, 0, 7.867, 0.422, 0, 8.15, -0.531, 0, 8.417, 0.376, 0, 8.783, -0.314, 0, 9.133, 0.422, 0, 9.417, -0.531, 0, 9.683, 0.376, 0, 10.05, -0.314, 0, 10.4, 0.422, 0, 10.683, -0.531, 0, 10.95, 0.376, 0, 11.317, -0.314, 0, 11.667, 0.422, 0, 11.95, -0.531, 0, 12.217, 0.376, 0, 12.583, -0.314, 0, 12.933, 0.422, 0, 13.217, -0.531, 0, 13.483, 0.376, 0, 13.85, -0.314, 0, 14.2, 0.422, 0, 14.483, -0.531, 0, 14.75, 0.375, 0, 15.1, -0.341, 0, 15.2, -0.014]}, {"Target": "Parameter", "Id": "Jewelry_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.134, 0, 0.1, -0.163, 0, 0.45, 0.762, 0, 0.717, -1.243, 0, 1.017, 1.121, 0, 1.383, -1.066, 0, 1.717, 1.289, 0, 2.017, -1.279, 0, 2.3, 0.899, 0, 2.65, -0.869, 0, 2.983, 1.159, 0, 3.267, -1.256, 0, 3.567, 0.95, 0, 3.917, -0.918, 0, 4.25, 1.193, 0, 4.533, -1.262, 0, 4.833, 0.938, 0, 5.183, -0.906, 0, 5.517, 1.184, 0, 5.8, -1.26, 0, 6.1, 0.941, 0, 6.45, -0.909, 0, 6.783, 1.186, 0, 7.067, -1.261, 0, 7.367, 0.94, 0, 7.717, -0.908, 0, 8.05, 1.186, 0, 8.333, -1.261, 0, 8.633, 0.94, 0, 8.983, -0.908, 0, 9.317, 1.186, 0, 9.6, -1.261, 0, 9.9, 0.94, 0, 10.25, -0.908, 0, 10.583, 1.186, 0, 10.867, -1.261, 0, 11.167, 0.94, 0, 11.517, -0.908, 0, 11.85, 1.186, 0, 12.133, -1.261, 0, 12.433, 0.94, 0, 12.783, -0.908, 0, 13.117, 1.186, 0, 13.4, -1.261, 0, 13.7, 0.94, 0, 14.05, -0.908, 0, 14.383, 1.186, 0, 14.667, -1.261, 0, 14.967, 0.939, 0, 15.2, -0.134]}, {"Target": "Parameter", "Id": "Jewelry_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.078, 0, 0.267, -0.256, 0, 0.6, 0.855, 0, 0.883, -1.322, 0, 1.183, 1.349, 0, 1.517, -1.293, 0, 1.867, 1.5, 0, 2.167, -1.513, 0, 2.467, 1.248, 0, 2.8, -1.143, 0, 3.133, 1.377, 0, 3.417, -1.464, 0, 3.733, 1.267, 0, 4.067, -1.179, 0, 4.4, 1.409, 0, 4.7, -1.478, 0, 5, 1.264, 0, 5.333, -1.171, 0, 5.667, 1.401, 0, 5.95, -1.473, 0, 6.267, 1.264, 0, 6.6, -1.172, 0, 6.933, 1.403, 0, 7.217, -1.474, 2, 7.233, -1.474, 0, 7.533, 1.264, 0, 7.867, -1.172, 0, 8.2, 1.403, 0, 8.483, -1.474, 2, 8.5, -1.474, 0, 8.8, 1.264, 0, 9.133, -1.172, 0, 9.467, 1.403, 0, 9.75, -1.474, 2, 9.767, -1.474, 0, 10.067, 1.264, 0, 10.4, -1.172, 0, 10.733, 1.403, 0, 11.017, -1.474, 2, 11.033, -1.474, 0, 11.333, 1.264, 0, 11.667, -1.172, 0, 12, 1.403, 0, 12.283, -1.474, 2, 12.3, -1.474, 0, 12.6, 1.264, 0, 12.933, -1.172, 0, 13.267, 1.403, 0, 13.55, -1.474, 2, 13.567, -1.474, 0, 13.867, 1.264, 0, 14.2, -1.172, 0, 14.533, 1.403, 0, 14.817, -1.473, 0, 15.133, 1.293, 0, 15.2, -0.078]}, {"Target": "Parameter", "Id": "Physics_Joint_5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.112, 0, 0.267, 0.453, 0, 0.583, -0.818, 0, 0.9, 0.912, 0, 1.217, -0.884, 0, 1.567, 0.916, 0, 1.883, -0.895, 0, 2.183, 0.736, 0, 2.5, -0.685, 0, 2.817, 0.791, 0, 3.15, -0.874, 0, 3.45, 0.786, 0, 3.767, -0.739, 0, 4.1, 0.822, 0, 4.417, -0.88, 0, 4.717, 0.774, 0, 5.033, -0.726, 0, 5.367, 0.814, 0, 5.683, -0.878, 0, 5.983, 0.777, 0, 6.3, -0.729, 0, 6.633, 0.816, 0, 6.95, -0.879, 0, 7.25, 0.776, 0, 7.567, -0.728, 0, 7.9, 0.816, 0, 8.217, -0.879, 0, 8.517, 0.776, 0, 8.833, -0.728, 0, 9.167, 0.816, 0, 9.483, -0.879, 0, 9.783, 0.776, 0, 10.1, -0.728, 0, 10.433, 0.816, 0, 10.75, -0.879, 0, 11.05, 0.776, 0, 11.367, -0.728, 0, 11.7, 0.816, 0, 12.017, -0.879, 0, 12.317, 0.776, 0, 12.633, -0.728, 0, 12.967, 0.816, 0, 13.283, -0.879, 0, 13.583, 0.776, 0, 13.9, -0.728, 0, 14.233, 0.816, 0, 14.55, -0.879, 0, 14.85, 0.773, 0, 15.167, -0.689, 0, 15.2, -0.112]}, {"Target": "Parameter", "Id": "Physics_Joint_4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.147, 0, 0.133, -0.397, 0, 0.467, 1.354, 0, 0.783, -2.033, 0, 1.1, 2.162, 0, 1.417, -2.209, 0, 1.767, 2.29, 0, 2.083, -2.158, 0, 2.383, 1.804, 0, 2.7, -1.815, 0, 3.017, 2.096, 0, 3.333, -2.14, 0, 3.65, 1.906, 0, 3.95, -1.923, 0, 4.283, 2.152, 0, 4.6, -2.144, 0, 4.917, 1.883, 0, 5.217, -1.896, 0, 5.55, 2.138, 0, 5.867, -2.142, 0, 6.183, 1.888, 0, 6.483, -1.903, 0, 6.817, 2.142, 0, 7.133, -2.143, 0, 7.45, 1.887, 0, 7.75, -1.901, 0, 8.083, 2.141, 0, 8.4, -2.143, 0, 8.717, 1.887, 0, 9.017, -1.901, 0, 9.35, 2.141, 0, 9.667, -2.143, 0, 9.983, 1.887, 0, 10.283, -1.901, 0, 10.617, 2.141, 0, 10.933, -2.143, 0, 11.25, 1.887, 0, 11.55, -1.901, 0, 11.883, 2.141, 0, 12.2, -2.143, 0, 12.517, 1.887, 0, 12.817, -1.901, 0, 13.15, 2.141, 0, 13.467, -2.143, 0, 13.783, 1.887, 0, 14.083, -1.901, 0, 14.417, 2.141, 0, 14.733, -2.143, 0, 15.05, 1.868, 0, 15.2, -0.147]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.042, 0, 0.233, 0.721, 0, 0.533, -1.539, 0, 0.8, 1.428, 0, 1.167, -1.174, 0, 1.533, 1.417, 0, 1.817, -1.624, 0, 2.083, 1.06, 0, 2.45, -0.885, 0, 2.8, 1.227, 0, 3.083, -1.583, 0, 3.35, 1.142, 0, 3.717, -0.954, 0, 4.067, 1.276, 0, 4.35, -1.597, 0, 4.617, 1.123, 0, 4.983, -0.937, 0, 5.333, 1.264, 0, 5.617, -1.593, 0, 5.883, 1.127, 0, 6.25, -0.941, 0, 6.6, 1.267, 0, 6.883, -1.594, 0, 7.15, 1.126, 0, 7.517, -0.94, 0, 7.867, 1.266, 0, 8.15, -1.594, 0, 8.417, 1.127, 0, 8.783, -0.941, 0, 9.133, 1.266, 0, 9.417, -1.594, 0, 9.683, 1.127, 0, 10.05, -0.94, 0, 10.4, 1.266, 0, 10.683, -1.594, 0, 10.95, 1.127, 0, 11.317, -0.94, 0, 11.667, 1.266, 0, 11.95, -1.594, 0, 12.217, 1.127, 0, 12.583, -0.94, 0, 12.933, 1.266, 0, 13.217, -1.594, 0, 13.483, 1.127, 0, 13.85, -0.94, 0, 14.2, 1.266, 0, 14.483, -1.594, 0, 14.75, 1.125, 0, 15.1, -1.022, 0, 15.2, -0.042]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.401, 0, 0.1, -0.488, 0, 0.45, 2.287, 0, 0.717, -3.73, 0, 1.017, 3.364, 0, 1.383, -3.199, 0, 1.717, 3.866, 0, 2.017, -3.836, 0, 2.3, 2.697, 0, 2.65, -2.607, 0, 2.983, 3.476, 0, 3.267, -3.767, 0, 3.567, 2.849, 0, 3.917, -2.753, 0, 4.25, 3.579, 0, 4.533, -3.787, 0, 4.833, 2.815, 0, 5.183, -2.718, 0, 5.517, 3.552, 0, 5.8, -3.78, 0, 6.1, 2.822, 0, 6.45, -2.726, 0, 6.783, 3.559, 0, 7.067, -3.782, 0, 7.367, 2.821, 0, 7.717, -2.724, 0, 8.05, 3.558, 0, 8.333, -3.782, 0, 8.633, 2.821, 0, 8.983, -2.725, 0, 9.317, 3.558, 0, 9.6, -3.782, 0, 9.9, 2.821, 0, 10.25, -2.725, 0, 10.583, 3.558, 0, 10.867, -3.782, 0, 11.167, 2.821, 0, 11.517, -2.725, 0, 11.85, 3.558, 0, 12.133, -3.782, 0, 12.433, 2.821, 0, 12.783, -2.725, 0, 13.117, 3.558, 0, 13.4, -3.782, 0, 13.7, 2.821, 0, 14.05, -2.725, 0, 14.383, 3.558, 0, 14.667, -3.782, 0, 14.967, 2.817, 0, 15.2, -0.401]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.235, 0, 0.267, -0.768, 0, 0.6, 2.564, 0, 0.883, -3.965, 0, 1.183, 4.045, 0, 1.517, -3.879, 0, 1.867, 4.501, 0, 2.167, -4.54, 0, 2.467, 3.745, 0, 2.8, -3.428, 0, 3.133, 4.133, 0, 3.417, -4.393, 0, 3.733, 3.801, 0, 4.067, -3.537, 0, 4.4, 4.228, 0, 4.7, -4.433, 0, 5, 3.792, 0, 5.333, -3.511, 0, 5.667, 4.204, 0, 5.95, -4.42, 0, 6.267, 3.793, 0, 6.6, -3.517, 0, 6.933, 4.21, 0, 7.233, -4.423, 0, 7.533, 3.793, 0, 7.867, -3.516, 0, 8.2, 4.208, 0, 8.483, -4.422, 2, 8.5, -4.422, 0, 8.8, 3.793, 0, 9.133, -3.516, 0, 9.467, 4.209, 0, 9.767, -4.423, 0, 10.067, 3.793, 0, 10.4, -3.516, 0, 10.733, 4.209, 0, 11.033, -4.423, 0, 11.333, 3.793, 0, 11.667, -3.516, 0, 12, 4.209, 0, 12.3, -4.423, 0, 12.6, 3.793, 0, 12.933, -3.516, 0, 13.267, 4.209, 0, 13.55, -4.423, 2, 13.567, -4.423, 0, 13.867, 3.793, 0, 14.2, -3.516, 0, 14.533, 4.209, 0, 14.817, -4.419, 0, 15.133, 3.879, 0, 15.2, -0.235]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.115, 0, 0.333, -0.764, 0, 0.7, 2.77, 0, 1, -4.71, 0, 1.3, 5.489, 0, 1.633, -5.636, 0, 1.967, 6.108, 0, 2.283, -6.21, 0, 2.583, 5.539, 0, 2.917, -5.139, 0, 3.233, 5.588, 0, 3.55, -5.911, 0, 3.85, 5.535, 0, 4.167, -5.253, 0, 4.5, 5.725, 0, 4.817, -5.995, 0, 5.117, 5.544, 0, 5.45, -5.227, 0, 5.767, 5.691, 0, 6.083, -5.973, 0, 6.383, 5.54, 0, 6.717, -5.232, 0, 7.033, 5.699, 0, 7.35, -5.979, 0, 7.65, 5.542, 0, 7.983, -5.231, 0, 8.3, 5.697, 0, 8.617, -5.977, 0, 8.917, 5.541, 0, 9.25, -5.231, 0, 9.567, 5.698, 0, 9.883, -5.978, 0, 10.183, 5.541, 0, 10.517, -5.231, 0, 10.833, 5.698, 0, 11.15, -5.978, 0, 11.45, 5.541, 0, 11.783, -5.231, 0, 12.1, 5.698, 0, 12.417, -5.978, 0, 12.717, 5.541, 0, 13.05, -5.231, 0, 13.367, 5.698, 0, 13.683, -5.978, 0, 13.983, 5.541, 0, 14.317, -5.231, 0, 14.633, 5.698, 0, 14.95, -5.959, 0, 15.2, -0.115]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.487, 0, 0.233, -1.003, 0, 0.8, 3.004, 0, 1.117, -5.596, 0, 1.417, 7.203, 0, 1.75, -7.882, 0, 2.083, 8.437, 0, 2.4, -8.592, 0, 2.717, 7.98, 0, 3.033, -7.529, 0, 3.35, 7.817, 0, 3.667, -8.109, 0, 3.967, 7.859, 0, 4.283, -7.636, 0, 4.617, 7.989, 0, 4.933, -8.245, 0, 5.233, 7.902, 0, 5.55, -7.613, 0, 5.883, 7.949, 0, 6.2, -8.21, 0, 6.5, 7.889, 0, 6.817, -7.617, 0, 7.15, 7.959, 0, 7.467, -8.218, 0, 7.767, 7.893, 0, 8.083, -7.616, 0, 8.417, 7.956, 0, 8.733, -8.216, 0, 9.033, 7.891, 0, 9.35, -7.616, 0, 9.683, 7.957, 0, 10, -8.217, 0, 10.3, 7.892, 0, 10.617, -7.616, 0, 10.95, 7.957, 0, 11.267, -8.217, 0, 11.567, 7.892, 0, 11.883, -7.616, 0, 12.217, 7.957, 0, 12.533, -8.217, 0, 12.833, 7.892, 0, 13.15, -7.616, 0, 13.483, 7.957, 0, 13.8, -8.217, 0, 14.1, 7.892, 0, 14.417, -7.616, 0, 14.75, 7.957, 0, 15.067, -8.186, 0, 15.2, 0.487]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 3.02, 0, 0.283, -1.961, 0, 0.9, 3.187, 0, 1.217, -6.622, 0, 1.533, 9.239, 0, 1.85, -10.721, 0, 2.183, 11.618, 0, 2.517, -11.842, 0, 2.833, 11.313, 0, 3.15, -10.825, 0, 3.467, 10.949, 0, 3.783, -11.178, 0, 4.083, 11.017, 0, 4.4, -10.89, 0, 4.733, 11.141, 0, 5.05, -11.373, 0, 5.35, 11.112, 0, 5.667, -10.887, 0, 6, 11.099, 0, 6.317, -11.325, 0, 6.617, 11.086, 0, 6.933, -10.885, 0, 7.267, 11.108, 0, 7.583, -11.336, 0, 7.883, 11.093, 0, 8.2, -10.886, 0, 8.533, 11.107, 0, 8.85, -11.334, 0, 9.15, 11.091, 0, 9.467, -10.886, 0, 9.8, 11.107, 0, 10.117, -11.334, 0, 10.417, 11.092, 0, 10.733, -10.886, 0, 11.067, 11.107, 0, 11.383, -11.334, 0, 11.683, 11.091, 0, 12, -10.886, 0, 12.333, 11.107, 0, 12.65, -11.334, 0, 12.95, 11.091, 0, 13.267, -10.886, 0, 13.6, 11.107, 0, 13.917, -11.334, 0, 14.217, 11.091, 0, 14.533, -10.886, 0, 14.867, 11.107, 0, 15.183, -11.289, 1, 15.189, -11.289, 15.194, 3.02, 15.2, 3.02]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 6.709, 0, 0.017, 6.874, 0, 0.383, -3.821, 0, 0.983, 3.221, 0, 1.317, -7.673, 0, 1.65, 11.558, 0, 1.967, -14.168, 0, 2.3, 15.689, 0, 2.617, -16.186, 0, 2.933, 15.726, 0, 3.25, -15.166, 0, 3.567, 15.148, 0, 3.883, -15.31, 0, 4.2, 15.212, 0, 4.517, -15.122, 0, 4.833, 15.276, 0, 5.15, -15.557, 0, 5.467, 15.4, 0, 5.783, -15.162, 0, 6.1, 15.335, 0, 6.417, -15.517, 0, 6.733, 15.328, 0, 7.05, -15.143, 0, 7.367, 15.343, 0, 7.683, -15.533, 0, 8, 15.34, 0, 8.317, -15.147, 0, 8.633, 15.341, 0, 8.95, -15.529, 0, 9.267, 15.337, 0, 9.583, -15.146, 0, 9.9, 15.342, 0, 10.217, -15.53, 0, 10.533, 15.337, 0, 10.85, -15.146, 0, 11.167, 15.342, 0, 11.483, -15.529, 0, 11.8, 15.337, 0, 12.117, -15.146, 0, 12.433, 15.342, 0, 12.75, -15.53, 0, 13.067, 15.337, 0, 13.383, -15.146, 0, 13.7, 15.342, 0, 14.017, -15.53, 0, 14.333, 15.337, 0, 14.65, -15.146, 0, 14.967, 15.342, 0, 15.2, 6.709]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.167, 1, 0, 3, 0, 0, 4.167, 1, 0, 6, 0, 0, 7.167, 1, 0, 9, 0, 0, 10.167, 1, 0, 12, 0, 0, 13.167, 1, 0, 15, 0, 2, 15.2, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.2, 1]}, {"Target": "Parameter", "Id": "ParamSad2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.2, 1]}, {"Target": "Parameter", "Id": "ParamCRY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.2, 0, 15.2, -5.2]}, {"Target": "Parameter", "Id": "Position_Coordinates", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.2, 1]}, {"Target": "Parameter", "Id": "Man_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.2, 1]}, {"Target": "Parameter", "Id": "BG_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.2, 1]}, {"Target": "Parameter", "Id": "FG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "FG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "BG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "BG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "BG_White4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "BG_White5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "BG_White2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "BG_White3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.3, 0, 15.2, 0.3]}, {"Target": "Parameter", "Id": "All_Size2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -12.72, 0, 15.2, -12.72]}, {"Target": "Parameter", "Id": "All_Size3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "All_Size4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "tuerzuo3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "faqiehuan", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "tuerzuo4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Mail_Change", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.2, 1]}, {"Target": "Parameter", "Id": "Mail_Transparency5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.5, 0, 15.2, 0.5]}, {"Target": "Parameter", "Id": "Mail_Transparency2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "tongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamTongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "EyeFrame_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1, 0, 15.2, -1]}, {"Target": "Parameter", "Id": "ParamBrowForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1, 0, 15.2, -1]}, {"Target": "Parameter", "Id": "ParamBrowY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.2, 1]}, {"Target": "Parameter", "Id": "ParamSigh", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamDarken", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamCry", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_B", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_C", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "MB_Unidirectional_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "jing<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamShoulderLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamArmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.2, 1]}, {"Target": "Parameter", "Id": "ParamForearmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -30, 0, 15.2, -30]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.48, 0, 15.2, -3.48]}, {"Target": "Parameter", "Id": "ParamArmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamArmL_ForearmL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -13.64, 0, 15.2, -13.64]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.2, 1]}, {"Target": "Parameter", "Id": "ParamForearmL_HandL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.26, 0, 15.2, 0.26]}, {"Target": "Parameter", "Id": "ParamHandLDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 6, 0, 15.2, 6]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamHandLSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.2, 1]}, {"Target": "Parameter", "Id": "Change_idle_physics9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "xiongjiya", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "xiongjiya5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "xiongjiya3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "xiongjiya4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "xiongjiya2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.98, 0, 15.2, -0.98]}, {"Target": "Parameter", "Id": "ParamForearmR_RE2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamShoulderRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamArmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.2, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.76, 0, 15.2, -2.76]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamArmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamArmR_ForearmR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.951, 0, 15.2, 0.951]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 7.4, 0, 15.2, 7.4]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamForearm<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.2, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_HandR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamHandRDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2, 0, 15.2, 2]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamHandRSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.2, 1]}, {"Target": "Parameter", "Id": "Change_idle_physics8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 15.2, 0.6]}, {"Target": "Parameter", "Id": "Change_idle_physics23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.2, 1]}, {"Target": "Parameter", "Id": "Change_idle_physics24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.187, 0, 15.2, 0.187]}, {"Target": "Parameter", "Id": "Change_idle_physics5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.311, 0, 15.2, -0.311]}, {"Target": "Parameter", "Id": "Change_idle_physics6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.933, 0, 15.2, -0.933]}, {"Target": "Parameter", "Id": "Change_idle_physics10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.57, 0, 15.2, 0.57]}, {"Target": "Parameter", "Id": "Change_idle_physics11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.2, 1]}, {"Target": "Parameter", "Id": "Change_idle_physics13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.2, 1]}, {"Target": "Parameter", "Id": "Change_idle_physics14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Param103", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -30, 0, 15.2, -30]}, {"Target": "Parameter", "Id": "Change_idle_physics15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -21.5, 0, 15.2, -21.5]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 24, 0, 15.2, 24]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamLegL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.2, 1]}, {"Target": "Parameter", "Id": "ParamLegLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamLegLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamCrusLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamCrusLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>oodLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamFoodLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamFoodLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamLegR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.2, 1]}, {"Target": "Parameter", "Id": "ParamLegRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamLegRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamCrusRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamCrusRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamCrus<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>ood<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamFoodRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamFoodRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.2, 1]}, {"Target": "Parameter", "Id": "physics_Add_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "physics_Add_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamBodyStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Physics_AngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -11.633, 0, 15.2, -11.633]}, {"Target": "Parameter", "Id": "Physics_AngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Physics_Body_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Physics_Body_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Physics_breast", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamBreath2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "fenshangxuanzhuang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "touch_drag1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "touch_drag2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "touch_drag3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "touch_drag4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "touch_drag5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "touch_drag9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.2, 0]}], "UserData": []}