{"Version": 3, "Meta": {"Duration": 6.317, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 306, "TotalSegmentCount": 1341, "TotalPointCount": 1805, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "MB_Lightning_Splinter_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.483, 30, 2, 0.5, 30, 0, 0.583, 100, 2, 0.917, 100, 2, 0.933, 0, 2, 1.167, 0, 0, 1.45, 30, 2, 1.467, 30, 1, 1.506, 30, 1.544, 100, 1.583, 100, 2, 1.75, 100, 2, 1.767, 0, 2, 6.317, 0]}, {"Target": "Parameter", "Id": "MB_Lightning_Splinter_B", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.617, 0, 0, 0.983, 30, 2, 1, 30, 0, 1.167, 100, 2, 1.25, 100, 2, 1.267, 0, 2, 6.317, 0]}, {"Target": "Parameter", "Id": "MB_Lightning_Splinter_C", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.733, 0, 0, 1.383, 30, 2, 1.4, 30, 1, 1.433, 30, 1.467, 100, 1.5, 100, 2, 1.583, 100, 2, 1.6, 0, 2, 6.317, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 1, 0.445, 0.133, 0.472, 0.267, 0.5, 0.4, 1, 0.639, 0.517, 0.778, 0.633, 0.917, 0.75, 1, 0.972, 0.833, 1.028, 0.917, 1.083, 1, 2, 3.617, 1, 1, 3.661, 0.917, 3.706, 0.833, 3.75, 0.75, 1, 3.861, 0.633, 3.972, 0.517, 4.083, 0.4, 1, 4.139, 0.267, 4.194, 0.133, 4.25, 0, 2, 6.317, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 2, 4.05, 0, 0, 6.317, 1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 2, 1.517, 0, 0, 1.983, -0.1, 0, 2.45, 0.1, 0, 2.917, -0.1, 0, 3.383, 0, 0, 3.817, -0.3, 0, 4.05, 0, 2, 5.033, 0, 0, 5.15, -1, 0, 5.3, 1, 0, 5.45, -0.4, 0, 5.55, 0.3, 0, 5.633, 0, 2, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY_SD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 2, 1.517, 0, 0, 1.983, 0.6, 0, 2.45, 0.3, 0, 2.917, 0.5, 0, 3.383, 0, 0, 3.817, 1, 0, 4.05, 0, 2, 4.067, 0, 2, 4.417, 0, 2, 5.067, 0, 0, 5.45, 0.5, 2, 5.7, 0.5, 0, 6.083, 1, 2, 6.1, 0, 2, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 2, 4.05, 0, 0, 4.067, 2, 2, 5.683, 2, 2, 6.317, 2]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.417, 1, 2, 1.517, 1, 2, 1.983, 1, 2, 2.45, 1, 2, 2.917, 1, 2, 3.383, 1, 0, 4.05, 0, 1, 4.172, 0, 4.295, 0.412, 4.417, 1, 1, 4.445, 1.134, 4.472, 1.132, 4.5, 1.132, 0, 4.883, 0.782, 0, 5.767, 0.848, 0, 5.983, 0, 0, 6.317, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.417, 1, 2, 1.517, 1, 2, 1.983, 1, 2, 2.45, 1, 2, 2.917, 1, 2, 3.383, 1, 0, 4.05, 0, 1, 4.172, 0, 4.295, 0.412, 4.417, 1, 1, 4.445, 1.134, 4.472, 1.132, 4.5, 1.132, 0, 4.883, 0.782, 0, 5.767, 0.848, 0, 5.983, 0, 0, 6.317, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 2, 1.517, 0, 2, 1.983, 0, 2, 2.45, 0, 2, 2.917, 0, 2, 3.383, 0, 2, 4.05, 0, 2, 4.3, 0, 0, 4.667, 0.1, 0, 5.067, 0, 2, 5.1, 0, 2, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 2, 1.517, 0, 2, 1.983, 0, 2, 2.45, 0, 2, 2.917, 0, 2, 3.383, 0, 2, 4.05, 0, 0, 4.3, -1, 0, 4.667, 0.4, 0, 5.067, 0, 2, 5.1, 0, 2, 6.317, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 2, 1.517, 0, 2, 1.983, 0, 2, 2.45, 0, 2, 2.917, 0, 2, 3.383, 0, 2, 4.05, 0, 0, 4.867, 0.6, 1, 5.15, 0.6, 5.434, 0.605, 5.717, 0.546, 1, 5.917, 0.505, 6.117, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 2, 0.867, 0, 1, 1.084, 0, 1.3, -0.295, 1.517, -0.711, 1, 1.639, -0.946, 1.761, -1, 1.883, -1, 0, 1.983, -0.711, 0, 2.35, -1, 0, 2.45, -0.711, 0, 2.817, -1, 0, 2.917, -0.711, 0, 3.283, -1, 1, 3.316, -1, 3.35, -0.788, 3.383, -0.711, 1, 3.605, -0.195, 3.828, 0, 4.05, 0, 2, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamBrowAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 2, 0.867, 0, 1, 1.084, 0, 1.3, 0.118, 1.517, 0.284, 1, 1.639, 0.378, 1.761, 0.4, 1.883, 0.4, 0, 1.983, 0.284, 0, 2.35, 0.4, 0, 2.45, 0.284, 0, 2.817, 0.4, 0, 2.917, 0.284, 0, 3.283, 0.4, 1, 3.316, 0.4, 3.35, 0.315, 3.383, 0.284, 1, 3.605, 0.078, 3.828, 0, 4.05, 0, 2, 4.283, 0, 0, 5.067, 1, 2, 5.683, 1, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamEyeLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 2, 0.483, 0, 2, 0.5, 0, 2, 0.567, 0, 2, 0.583, 0, 2, 0.6, 0, 2, 0.617, 0, 2, 0.633, 0, 2, 0.667, 0, 2, 0.683, 0, 2, 0.7, 0, 2, 0.717, 0, 2, 0.867, 0, 2, 0.883, 0, 2, 1.517, 0, 2, 1.983, 0, 2, 2.45, 0, 2, 2.917, 0, 2, 3.383, 0, 0, 4.05, 1, 0, 4.067, -1, 2, 4.217, -1, 0, 4.6, 0.368, 0, 4.917, -0.099, 0, 5.233, 0.033, 0, 5.517, 0, 2, 5.533, 0, 0, 5.867, 1, 2, 6, 1, 0, 6.183, -1, 2, 6.267, -1, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamEyeRsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 2, 0.483, 0, 2, 0.5, 0, 2, 0.567, 0, 2, 0.583, 0, 2, 0.6, 0, 2, 0.617, 0, 2, 0.633, 0, 2, 0.667, 0, 2, 0.683, 0, 2, 0.7, 0, 2, 0.717, 0, 2, 0.867, 0, 2, 0.883, 0, 2, 1.517, 0, 2, 1.983, 0, 2, 2.45, 0, 2, 2.917, 0, 2, 3.383, 0, 0, 4.05, 1, 0, 4.067, -1, 2, 4.217, -1, 0, 4.6, 0.368, 0, 4.917, -0.099, 0, 5.233, 0.033, 0, 5.517, 0, 2, 5.533, 0, 0, 5.867, 1, 2, 6, 1, 0, 6.183, -1, 2, 6.267, -1, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamEyeLGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.004, 2, 0.417, 0.004, 0, 0.617, -0.001, 2, 0.633, -0.001, 2, 0.65, -0.001, 2, 0.683, -0.001, 0, 0.85, 0, 2, 0.867, 0, 2, 0.883, 0, 2, 0.933, 0, 2, 0.95, 0, 2, 0.967, 0, 2, 0.983, 0, 2, 1, 0, 2, 1.017, 0, 2, 1.033, 0, 2, 1.067, 0, 2, 1.083, 0, 2, 1.233, 0, 2, 1.25, 0, 2, 1.517, 0, 2, 1.983, 0, 2, 2.45, 0, 2, 2.917, 0, 2, 3.383, 0, 0, 4.067, -1, 0, 4.167, -0.074, 0, 4.25, -0.095, 0, 4.433, 0.044, 0, 4.567, -0.08, 0, 4.783, 0.146, 0, 5.083, -0.087, 0, 5.383, 0.038, 0, 5.683, -0.015, 0, 5.767, -0.01, 0, 5.867, -0.286, 0, 6.1, 1, 2, 6.117, 1, 0, 6.317, 0.004]}, {"Target": "Parameter", "Id": "ParamEyeRGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.004, 2, 0.417, 0.004, 0, 0.617, -0.001, 2, 0.633, -0.001, 2, 0.65, -0.001, 2, 0.683, -0.001, 0, 0.85, 0, 2, 0.867, 0, 2, 0.883, 0, 2, 0.933, 0, 2, 0.95, 0, 2, 0.967, 0, 2, 0.983, 0, 2, 1, 0, 2, 1.017, 0, 2, 1.033, 0, 2, 1.067, 0, 2, 1.083, 0, 2, 1.233, 0, 2, 1.25, 0, 2, 1.517, 0, 2, 1.983, 0, 2, 2.45, 0, 2, 2.917, 0, 2, 3.383, 0, 0, 4.067, -1, 0, 4.167, -0.074, 0, 4.25, -0.095, 0, 4.433, 0.044, 0, 4.567, -0.08, 0, 4.783, 0.146, 0, 5.083, -0.087, 0, 5.383, 0.038, 0, 5.683, -0.015, 0, 5.767, -0.01, 0, 5.867, -0.286, 0, 6.1, 1, 2, 6.117, 1, 0, 6.317, 0.004]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 2, 2, 0, 2, 2.017, 0, 2, 4, 0, 2, 4.017, 0, 2, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 2, 2, 0, 2, 2.017, -5, 2, 4, -5, 2, 4.017, 0, 2, 6.317, 0]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.364, 2, 0.417, -1.364, 0, 1.517, -2.374, 2, 1.983, -2.374, 2, 2.45, -2.374, 2, 2.917, -2.374, 2, 3.383, -2.374, 0, 4.05, 0, 0, 6.317, -1.364]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 3.978, 2, 0.417, 3.978, 0, 1.517, 6.925, 2, 1.983, 6.925, 2, 2.45, 6.925, 2, 2.917, 6.925, 2, 3.383, 6.925, 0, 4.05, 0, 0, 6.317, 3.978]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.562, 2, 0.417, -2.562, 0, 1.517, -4.027, 2, 1.983, -4.027, 2, 2.45, -4.027, 2, 2.917, -4.027, 2, 3.383, -4.027, 0, 4.05, 0, 0, 6.317, -2.562]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -6.366, 2, 0.417, -6.366, 0, 1.517, -10.008, 2, 1.983, -10.008, 2, 2.45, -10.008, 2, 2.917, -10.008, 2, 3.383, -10.008, 0, 4.05, 0, 0, 6.317, -6.366]}, {"Target": "Parameter", "Id": "ParamShoulderLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.985, 2, 0.417, -0.985, 0, 1.017, 2, 1, 1.184, 2, 1.35, 0.844, 1.517, -0.548, 1, 1.572, -1.012, 1.628, -1, 1.683, -1, 0, 1.983, -0.548, 0, 2.15, -1, 0, 2.45, -0.548, 0, 2.617, -1, 0, 2.917, -0.548, 0, 3.083, -1, 1, 3.183, -1, 3.283, -1.084, 3.383, -0.548, 1, 3.522, 0.196, 3.661, 2, 3.8, 2, 0, 4.05, -0.985, 2, 6.317, -0.985]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -4.72, 2, 0.417, -4.72, 0, 1.217, -8.2, 2, 1.517, -8.2, 2, 1.983, -8.2, 2, 2.45, -8.2, 2, 2.917, -8.2, 2, 3.383, -8.2, 0, 4.05, -4.72, 2, 6.317, -4.72]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.099, 2, 0.417, 0.099, 0, 1.517, 0.087, 2, 1.983, 0.087, 2, 2.45, 0.087, 2, 2.917, 0.087, 2, 3.383, 0.087, 0, 4.05, 0.699, 0, 5.55, -0.289, 0, 6.317, 0.099]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.6, 2, 0.417, -0.6, 2, 1.467, -0.6, 1, 1.484, -0.6, 1.5, -0.605, 1.517, -0.582, 1, 1.6, -0.469, 1.684, -0.377, 1.767, -0.377, 0, 1.983, -0.582, 0, 2.233, -0.377, 0, 2.45, -0.582, 0, 2.7, -0.377, 0, 2.917, -0.582, 0, 3.167, -0.377, 1, 3.239, -0.377, 3.311, -0.524, 3.383, -0.582, 1, 3.416, -0.609, 3.45, -0.6, 3.483, -0.6, 0, 3.817, -0.242, 1, 3.895, -0.242, 3.972, -0.586, 4.05, -0.6, 1, 4.239, -0.635, 4.428, -0.638, 4.617, -0.638, 0, 6, -0.569, 0, 6.317, -0.6]}, {"Target": "Parameter", "Id": "xiongjiya3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.417, 1, 0, 1.517, 0.85, 2, 1.983, 0.85, 2, 2.45, 0.85, 2, 2.917, 0.85, 2, 3.383, 0.85, 0, 4.05, 1, 2, 6.317, 1]}, {"Target": "Parameter", "Id": "xiongjiya4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.417, 1, 0, 1.517, 0.85, 2, 1.983, 0.85, 2, 2.45, 0.85, 2, 2.917, 0.85, 2, 3.383, 0.85, 0, 4.05, 1, 2, 6.317, 1]}, {"Target": "Parameter", "Id": "ParamShoulderRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.985, 2, 0.417, -0.985, 0, 1.017, 2, 1, 1.184, 2, 1.35, 0.844, 1.517, -0.548, 1, 1.572, -1.012, 1.628, -1, 1.683, -1, 0, 1.983, -0.548, 0, 2.15, -1, 0, 2.45, -0.548, 0, 2.617, -1, 0, 2.917, -0.548, 0, 3.083, -1, 1, 3.183, -1, 3.283, -1.084, 3.383, -0.548, 1, 3.522, 0.196, 3.661, 2, 3.8, 2, 0, 4.05, -0.985, 2, 6.317, -0.985]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.92, 2, 0.417, -5.92, 1, 0.684, -6.58, 0.95, -7.24, 1.217, -7.9, 2, 1.517, -7.9, 2, 1.983, -7.9, 2, 2.45, -7.9, 2, 2.917, -7.9, 2, 3.383, -7.9, 1, 3.605, -7.24, 3.828, -6.58, 4.05, -5.92, 2, 6.317, -5.92]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.72, 2, 0.417, -0.72, 2, 1.517, -0.72, 2, 1.983, -0.72, 2, 2.45, -0.72, 2, 2.917, -0.72, 2, 3.383, -0.72, 0, 4.05, -1.44, 0, 5.55, -0.283, 0, 6.317, -0.72]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.6, 2, 0.417, -0.6, 2, 1.467, -0.6, 1, 1.484, -0.6, 1.5, -0.606, 1.517, -0.578, 1, 1.6, -0.439, 1.684, -0.323, 1.767, -0.323, 0, 1.983, -0.578, 0, 2.233, -0.323, 0, 2.45, -0.578, 0, 2.7, -0.323, 0, 2.917, -0.578, 0, 3.167, -0.323, 1, 3.239, -0.323, 3.311, -0.506, 3.383, -0.578, 1, 3.416, -0.611, 3.45, -0.6, 3.483, -0.6, 0, 3.817, -0.157, 1, 3.895, -0.157, 3.972, -0.586, 4.05, -0.6, 1, 4.239, -0.633, 4.428, -0.636, 4.617, -0.636, 0, 6, -0.57, 0, 6.317, -0.6]}, {"Target": "Parameter", "Id": "Change_idle_physics4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.103, 2, 0.417, -0.103, 0, 1.517, -0.137, 2, 1.983, -0.137, 2, 2.45, -0.137, 2, 2.917, -0.137, 2, 3.383, -0.137, 0, 4.05, -0.103, 2, 6.317, -0.103]}, {"Target": "Parameter", "Id": "Change_idle_physics6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.61, 2, 0.417, 0.61, 0, 1.517, 0.615, 2, 1.983, 0.615, 2, 2.45, 0.615, 2, 2.917, 0.615, 2, 3.383, 0.615, 0, 4.05, 0.61, 2, 6.317, 0.61]}, {"Target": "Parameter", "Id": "Change_idle_physics10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 2, 0.95, 0, 1, 1.111, 0, 1.272, 0.073, 1.433, 0.249, 1, 1.461, 0.279, 1.489, 0.321, 1.517, 0.321, 2, 1.983, 0.321, 2, 2.45, 0.321, 2, 2.917, 0.321, 2, 3.383, 0.321, 0, 3.783, 0.679, 0, 4.05, 0, 2, 6.317, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 2, 1.317, 0, 0, 1.517, -0.479, 0, 1.733, 0.8, 0, 1.983, -0.479, 0, 2.2, 0.8, 0, 2.45, -0.479, 0, 2.667, 0.8, 0, 2.917, -0.479, 0, 3.133, 0.8, 0, 3.383, -0.479, 0, 3.733, 0.293, 1, 3.839, 0.293, 3.944, 0.017, 4.05, 0, 1, 4.55, -0.08, 5.05, -0.1, 5.55, -0.1, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.417, 1, 2, 4.933, 1, 0, 5.817, 0.748, 2, 6.317, 0.748]}, {"Target": "Parameter", "Id": "ParamAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 2, 1.517, 0, 2, 1.983, 0, 2, 2.45, 0, 2, 2.917, 0, 2, 3.383, 0, 2, 4.4, 0, 0, 5.117, -1.68, 0, 5.767, 0, 2, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 2, 0.533, 0, 2, 1.317, 0, 0, 1.517, -6.66, 0, 1.733, 4.883, 0, 1.983, -6.66, 0, 2.2, 4.883, 0, 2.45, -6.66, 0, 2.667, 4.883, 0, 2.917, -6.66, 0, 3.133, 4.883, 0, 3.383, -6.66, 0, 3.733, 8.24, 0, 4.4, -17.7, 0, 5.117, 2.64, 0, 5.767, 0, 2, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 2, 1.517, 0, 2, 1.983, 0, 2, 2.45, 0, 2, 2.917, 0, 2, 3.383, 0, 2, 4.05, 0, 2, 4.733, 0, 0, 5.55, 7, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 2, 1.517, 0, 2, 1.983, 0, 2, 2.45, 0, 2, 2.917, 0, 2, 3.383, 0, 2, 4.05, 0, 0, 5.55, 4.783, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -7.795, 2, 0.417, -7.795, 0, 1.317, 0, 0, 1.517, -2.009, 0, 1.733, 2, 0, 1.983, -2.009, 0, 2.2, 2, 0, 2.45, -2.009, 0, 2.667, 2, 0, 2.917, -2.009, 0, 3.133, 2, 0, 3.383, -2.009, 0, 3.733, 4, 0, 4.05, -7.795, 0, 5.55, 0, 0, 6.317, -7.795]}, {"Target": "Parameter", "Id": "ParamBodyStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 2, 1.267, 0, 0, 1.467, -2.288, 1, 1.484, -2.288, 1.5, -2.24, 1.517, -1.483, 1, 1.572, 1.041, 1.628, 2.866, 1.683, 2.866, 0, 1.9, -5.233, 1, 1.928, -5.233, 1.955, -3.155, 1.983, -1.483, 1, 2.039, 1.862, 2.094, 2.866, 2.15, 2.866, 0, 2.367, -5.233, 1, 2.395, -5.233, 2.422, -3.155, 2.45, -1.483, 1, 2.506, 1.862, 2.561, 2.866, 2.617, 2.866, 0, 2.833, -5.233, 1, 2.861, -5.233, 2.889, -3.155, 2.917, -1.483, 1, 2.972, 1.862, 3.028, 2.866, 3.083, 2.866, 0, 3.3, -5.233, 1, 3.328, -5.233, 3.355, -4.455, 3.383, -1.483, 1, 3.439, 4.461, 3.494, 8.411, 3.55, 8.411, 0, 4.133, -10, 0, 4.967, 0.95, 0, 5.35, -3, 0, 5.817, 0.795, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 2, 1.517, 0, 2, 1.983, 0, 2, 2.45, 0, 2, 2.917, 0, 2, 3.383, 0, 2, 4.05, 0, 0, 5.917, 3.269, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 0, 1.517, 0.187, 2, 1.983, 0.187, 2, 2.45, 0.187, 2, 2.917, 0.187, 2, 3.383, 0.187, 0, 4.05, 0, 2, 6.317, 0]}, {"Target": "Parameter", "Id": "Physics_AngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.465, 2, 0.417, -0.465, 0, 0.55, 0.366, 0, 0.7, 0, 2, 1.367, 0, 1, 1.417, 0, 1.467, -1.408, 1.517, -3.467, 1, 1.528, -3.925, 1.539, -3.934, 1.55, -3.934, 0, 1.767, 3.345, 1, 1.839, 3.345, 1.911, 0.383, 1.983, -3.467, 1, 1.994, -4.059, 2.006, -3.934, 2.017, -3.934, 0, 2.233, 3.345, 1, 2.305, 3.345, 2.378, 0.383, 2.45, -3.467, 1, 2.461, -4.059, 2.472, -3.934, 2.483, -3.934, 0, 2.7, 3.345, 1, 2.772, 3.345, 2.845, 0.383, 2.917, -3.467, 1, 2.928, -4.059, 2.939, -3.934, 2.95, -3.934, 0, 3.167, 3.345, 1, 3.239, 3.345, 3.311, 1.458, 3.383, -3.467, 1, 3.394, -4.225, 3.406, -6.559, 3.417, -6.559, 0, 3.75, 5.861, 0, 4.05, -0.465, 2, 6.317, -0.465]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.169, 2, 0.417, 1.169, 0, 0.55, 2.978, 0, 0.883, -1.641, 0, 1.217, 1.096, 0, 1.333, 0.657, 0, 1.483, 4.354, 1, 1.494, 4.354, 1.506, 4.546, 1.517, 2.68, 1, 1.572, -6.65, 1.628, -13.784, 1.683, -13.784, 0, 1.95, 19.949, 1, 1.961, 19.949, 1.972, 5.18, 1.983, 2.68, 1, 2.039, -9.818, 2.094, -13.784, 2.15, -13.784, 0, 2.417, 19.949, 1, 2.428, 19.949, 2.439, 5.18, 2.45, 2.68, 1, 2.506, -9.818, 2.561, -13.784, 2.617, -13.784, 0, 2.883, 19.949, 1, 2.894, 19.949, 2.906, 5.18, 2.917, 2.68, 1, 2.972, -9.818, 3.028, -13.784, 3.083, -13.784, 0, 3.35, 19.949, 1, 3.361, 19.949, 3.372, 5.312, 3.383, 2.68, 1, 3.461, -15.743, 3.539, -22.473, 3.617, -22.473, 0, 3.95, 27.524, 0, 4.05, 1.169, 2, 6.317, 1.169]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -7.12, 2, 0.417, -7.12, 0, 0.717, 5.854, 0, 1.033, -3.462, 0, 1.317, 2.188, 0, 1.433, -0.623, 1, 1.461, -0.623, 1.489, 0.878, 1.517, 5.531, 1, 1.55, 11.115, 1.584, 15.004, 1.617, 15.004, 0, 1.817, -30, 2, 1.883, -30, 1, 1.916, -30, 1.95, -4.114, 1.983, 5.531, 1, 2.016, 15.176, 2.05, 15.004, 2.083, 15.004, 0, 2.283, -30, 2, 2.35, -30, 1, 2.383, -30, 2.417, -4.114, 2.45, 5.531, 1, 2.483, 15.176, 2.517, 15.004, 2.55, 15.004, 0, 2.75, -30, 2, 2.817, -30, 1, 2.85, -30, 2.884, -4.114, 2.917, 5.531, 1, 2.95, 15.176, 2.984, 15.004, 3.017, 15.004, 0, 3.217, -30, 2, 3.283, -30, 1, 3.316, -30, 3.35, -21.587, 3.383, 5.531, 1, 3.394, 14.57, 3.406, 30, 3.417, 30, 2, 3.567, 30, 0, 3.7, -30, 2, 3.883, -30, 0, 4, 30, 0, 4.05, -7.12, 2, 6.317, -7.12]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_L", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.578, 2, 0.417, -1.578, 0, 0.467, -1.778, 0, 0.5, -1.625, 0, 0.533, -1.692, 0, 0.867, 1.447, 0, 1.25, -1.073, 1, 1.339, -1.073, 1.428, 0.415, 1.517, 2.498, 1, 1.55, 3.279, 1.584, 3.38, 1.617, 3.38, 0, 1.867, -5.103, 1, 1.906, -5.103, 1.944, 1.286, 1.983, 2.498, 1, 2.016, 3.537, 2.05, 3.38, 2.083, 3.38, 0, 2.333, -5.103, 1, 2.372, -5.103, 2.411, 1.286, 2.45, 2.498, 1, 2.483, 3.537, 2.517, 3.38, 2.55, 3.38, 0, 2.8, -5.103, 1, 2.839, -5.103, 2.878, 1.286, 2.917, 2.498, 1, 2.95, 3.537, 2.984, 3.38, 3.017, 3.38, 0, 3.267, -5.103, 1, 3.306, -5.103, 3.344, 0.278, 3.383, 2.498, 1, 3.428, 5.035, 3.472, 5.171, 3.517, 5.171, 0, 3.817, -5.99, 0, 4.05, -1.578, 2, 6.317, -1.578]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_R", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.578, 2, 0.417, 1.578, 0, 0.467, 1.778, 0, 0.5, 1.625, 0, 0.533, 1.692, 0, 0.867, -1.447, 0, 1.25, 1.073, 1, 1.339, 1.073, 1.428, -0.415, 1.517, -2.498, 1, 1.55, -3.279, 1.584, -3.38, 1.617, -3.38, 0, 1.867, 5.103, 1, 1.906, 5.103, 1.944, -1.286, 1.983, -2.498, 1, 2.016, -3.537, 2.05, -3.38, 2.083, -3.38, 0, 2.333, 5.103, 1, 2.372, 5.103, 2.411, -1.286, 2.45, -2.498, 1, 2.483, -3.537, 2.517, -3.38, 2.55, -3.38, 0, 2.8, 5.103, 1, 2.839, 5.103, 2.878, -1.286, 2.917, -2.498, 1, 2.95, -3.537, 2.984, -3.38, 3.017, -3.38, 0, 3.267, 5.103, 1, 3.306, 5.103, 3.344, -0.278, 3.383, -2.498, 1, 3.428, -5.035, 3.472, -5.171, 3.517, -5.171, 0, 3.817, 5.99, 0, 4.05, 1.578, 2, 6.317, 1.578]}, {"Target": "Parameter", "Id": "L_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.475, 2, 0.417, 0.475, 0, 0.6, 1.879, 0, 0.85, -1.972, 0, 1.017, 0, 2, 1.317, 0, 0, 1.517, 8.88, 0, 1.733, -6.511, 0, 1.983, 8.88, 0, 2.2, -6.511, 0, 2.45, 8.88, 0, 2.667, -6.511, 0, 2.917, 8.88, 0, 3.133, -6.511, 0, 3.383, 8.88, 0, 3.733, -10.988, 0, 4.05, 0.475, 2, 6.317, 0.475]}, {"Target": "Parameter", "Id": "L_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.527, 2, 0.417, -0.527, 0, 0.55, -1.974, 0, 0.717, 1.554, 0, 0.733, 1.549, 0, 0.767, 3.899, 0, 0.95, -2.324, 0, 1, -1.985, 0, 1.017, -2.457, 0, 1.133, 2.584, 0, 1.3, -2.726, 0, 1.333, -2.562, 0, 1.383, -3.381, 0, 1.417, -3.079, 1, 1.45, -3.079, 1.484, -5.151, 1.517, -6.646, 1, 1.522, -6.895, 1.528, -6.773, 1.533, -6.773, 0, 1.55, -6.329, 0, 1.567, -6.475, 0, 1.767, 7.051, 1, 1.839, 7.051, 1.911, -1.18, 1.983, -6.646, 1, 1.989, -7.066, 1.994, -6.773, 2, -6.773, 0, 2.017, -6.329, 0, 2.033, -6.475, 0, 2.233, 7.051, 1, 2.305, 7.051, 2.378, -1.18, 2.45, -6.646, 1, 2.456, -7.066, 2.461, -6.773, 2.467, -6.773, 0, 2.483, -6.329, 0, 2.5, -6.475, 0, 2.7, 7.051, 1, 2.772, 7.051, 2.845, -1.18, 2.917, -6.646, 1, 2.922, -7.066, 2.928, -6.773, 2.933, -6.773, 0, 2.95, -6.329, 0, 2.967, -6.475, 0, 3.167, 7.051, 0, 3.383, -6.646, 0, 3.733, 10.988, 0, 4.05, -0.527, 2, 6.317, -0.527]}, {"Target": "Parameter", "Id": "R_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.261, 2, 0.417, 0.261, 0, 0.633, -2.415, 0, 0.85, 0, 2, 1.317, 0, 0, 1.517, -8.88, 0, 1.733, 6.511, 0, 1.983, -8.88, 0, 2.2, 6.511, 0, 2.45, -8.88, 0, 2.667, 6.511, 0, 2.917, -8.88, 0, 3.133, 6.511, 0, 3.383, -8.88, 0, 3.733, 10.988, 0, 4.05, 0.261, 2, 6.317, 0.261]}, {"Target": "Parameter", "Id": "R_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.813, 2, 0.417, 0.813, 0, 0.517, 1.685, 0, 0.75, -1.827, 0, 0.833, -1.178, 0, 0.85, -1.882, 0, 0.967, 4.094, 0, 1.2, -3.213, 0, 1.483, 10.559, 1, 1.494, 10.559, 1.506, 11.068, 1.517, 9.971, 1, 1.589, 2.842, 1.661, -6.511, 1.733, -6.511, 0, 1.983, 9.971, 0, 2.2, -6.511, 0, 2.45, 9.971, 0, 2.667, -6.511, 0, 2.917, 9.971, 0, 3.133, -6.511, 0, 3.383, 9.971, 0, 3.733, -10.988, 0, 4.05, 0.814, 2, 6.317, 0.814]}, {"Target": "Parameter", "Id": "Jewelry_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.157, 2, 0.417, 0.157, 0, 0.55, 0.265, 0, 0.883, -0.169, 0, 1.217, 0.107, 0, 1.333, 0.06, 0, 1.483, 0.445, 1, 1.494, 0.445, 1.506, 0.464, 1.517, 0.297, 1, 1.572, -0.537, 1.628, -1.157, 1.683, -1.157, 0, 1.95, 1.612, 1, 1.961, 1.612, 1.972, 0.518, 1.983, 0.297, 1, 2.039, -0.807, 2.094, -1.157, 2.15, -1.157, 0, 2.417, 1.612, 1, 2.428, 1.612, 2.439, 0.518, 2.45, 0.297, 1, 2.506, -0.807, 2.561, -1.157, 2.617, -1.157, 0, 2.883, 1.612, 1, 2.894, 1.612, 2.906, 0.518, 2.917, 0.297, 1, 2.972, -0.807, 3.028, -1.157, 3.083, -1.157, 0, 3.35, 1.612, 1, 3.361, 1.612, 3.372, 0.529, 3.383, 0.297, 1, 3.461, -1.329, 3.539, -1.925, 3.617, -1.925, 0, 3.95, 2.429, 0, 4.05, 0.157, 2, 6.317, 0.157]}, {"Target": "Parameter", "Id": "Jewelry_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.525, 2, 0.417, -0.525, 0, 0.717, 0.466, 0, 1.033, -0.349, 2, 1.05, -0.349, 0, 1.333, 0.209, 0, 1.433, -0.1, 1, 1.461, -0.1, 1.489, 0.043, 1.517, 0.492, 1, 1.55, 1.03, 1.584, 1.404, 1.617, 1.404, 0, 1.85, -2.939, 1, 1.894, -2.939, 1.939, -0.773, 1.983, 0.492, 1, 2.016, 1.44, 2.05, 1.404, 2.083, 1.404, 0, 2.317, -2.939, 1, 2.361, -2.939, 2.406, -0.773, 2.45, 0.492, 1, 2.483, 1.44, 2.517, 1.404, 2.55, 1.404, 0, 2.783, -2.939, 1, 2.828, -2.939, 2.872, -0.773, 2.917, 0.492, 1, 2.95, 1.44, 2.984, 1.404, 3.017, 1.404, 0, 3.25, -2.939, 1, 3.294, -2.939, 3.339, -1.956, 3.383, 0.492, 1, 3.422, 2.634, 3.461, 3.993, 3.5, 3.993, 0, 3.783, -4.844, 0, 4.05, -0.525, 2, 6.317, -0.525]}, {"Target": "Parameter", "Id": "Jewelry_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.43, 2, 0.417, -0.43, 0, 0.45, -0.457, 0, 0.817, 0.343, 0, 1.15, -0.373, 0, 1.4, 0.187, 1, 1.439, 0.187, 1.478, -0.009, 1.517, -0.295, 1, 1.528, -0.377, 1.539, -0.384, 1.55, -0.384, 0, 1.75, 1.686, 1, 1.828, 1.686, 1.905, 0.74, 1.983, -0.295, 1, 1.994, -0.443, 2.006, -0.384, 2.017, -0.384, 0, 2.217, 1.686, 1, 2.295, 1.686, 2.372, 0.74, 2.45, -0.295, 1, 2.461, -0.443, 2.472, -0.384, 2.483, -0.384, 0, 2.683, 1.686, 1, 2.761, 1.686, 2.839, 0.74, 2.917, -0.295, 1, 2.928, -0.443, 2.939, -0.384, 2.95, -0.384, 0, 3.15, 1.686, 0, 3.383, -0.295, 0, 3.617, 4.475, 0, 3.9, -5.037, 0, 4.05, -0.43, 2, 6.317, -0.43]}, {"Target": "Parameter", "Id": "Physics_Joint_5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.042, 2, 0.417, 0.042, 0, 0.55, 0.096, 0, 0.883, -0.061, 0, 1.067, 0, 2, 1.267, 0, 0, 1.433, 0.385, 1, 1.461, 0.385, 1.489, 0.299, 1.517, -0.032, 1, 1.561, -0.561, 1.606, -0.933, 1.65, -0.933, 0, 1.883, 1.46, 1, 1.916, 1.46, 1.95, 0.554, 1.983, -0.032, 1, 2.028, -0.813, 2.072, -0.933, 2.117, -0.933, 0, 2.35, 1.46, 1, 2.383, 1.46, 2.417, 0.554, 2.45, -0.032, 1, 2.494, -0.813, 2.539, -0.933, 2.583, -0.933, 0, 2.817, 1.46, 1, 2.85, 1.46, 2.884, 0.554, 2.917, -0.032, 1, 2.961, -0.813, 3.006, -0.933, 3.05, -0.933, 0, 3.283, 1.46, 1, 3.316, 1.46, 3.35, 0.905, 3.383, -0.032, 1, 3.433, -1.437, 3.483, -2.062, 3.533, -2.062, 0, 3.833, 2.414, 0, 4.05, 0.042, 2, 6.317, 0.042]}, {"Target": "Parameter", "Id": "Physics_Joint_4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.23, 2, 0.417, -0.23, 0, 0.717, 0.181, 0, 1.05, -0.13, 0, 1.217, 0.063, 0, 1.4, -0.285, 1, 1.439, -0.285, 1.478, 0.134, 1.517, 0.723, 1, 1.539, 1.059, 1.561, 1.138, 1.583, 1.138, 0, 1.8, -2.363, 1, 1.861, -2.363, 1.922, -0.679, 1.983, 0.723, 1, 2.005, 1.233, 2.028, 1.138, 2.05, 1.138, 0, 2.267, -2.363, 1, 2.328, -2.363, 2.389, -0.679, 2.45, 0.723, 1, 2.472, 1.233, 2.495, 1.138, 2.517, 1.138, 0, 2.733, -2.363, 1, 2.794, -2.363, 2.856, -0.679, 2.917, 0.723, 1, 2.939, 1.233, 2.961, 1.138, 2.983, 1.138, 0, 3.2, -2.363, 1, 3.261, -2.363, 3.322, -1.583, 3.383, 0.723, 1, 3.394, 1.142, 3.406, 3.448, 3.417, 3.448, 0, 3.683, -4.485, 0, 3.983, 4.816, 0, 4.05, -0.23, 2, 6.317, -0.23]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.471, 2, 0.417, 0.471, 0, 0.55, 0.796, 0, 0.883, -0.506, 0, 1.217, 0.322, 0, 1.333, 0.18, 0, 1.483, 1.334, 1, 1.494, 1.334, 1.506, 1.388, 1.517, 0.891, 1, 1.572, -1.593, 1.628, -3.47, 1.683, -3.47, 0, 1.95, 4.835, 1, 1.961, 4.835, 1.972, 1.546, 1.983, 0.891, 1, 2.039, -2.383, 2.094, -3.47, 2.15, -3.47, 0, 2.417, 4.835, 1, 2.428, 4.835, 2.439, 1.546, 2.45, 0.891, 1, 2.506, -2.383, 2.561, -3.47, 2.617, -3.47, 0, 2.883, 4.835, 1, 2.894, 4.835, 2.906, 1.546, 2.917, 0.891, 1, 2.972, -2.383, 3.028, -3.47, 3.083, -3.47, 0, 3.35, 4.835, 1, 3.361, 4.835, 3.372, 1.582, 3.383, 0.891, 1, 3.461, -3.948, 3.539, -5.774, 3.617, -5.774, 0, 3.95, 7.288, 0, 4.05, 0.471, 2, 6.317, 0.471]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.576, 2, 0.417, -1.576, 0, 0.717, 1.397, 0, 1.05, -1.049, 0, 1.333, 0.627, 0, 1.433, -0.3, 1, 1.461, -0.3, 1.489, 0.132, 1.517, 1.476, 1, 1.55, 3.088, 1.584, 4.211, 1.617, 4.211, 0, 1.85, -8.818, 1, 1.894, -8.818, 1.939, -2.23, 1.983, 1.476, 1, 2.016, 4.256, 2.05, 4.211, 2.083, 4.211, 0, 2.317, -8.818, 1, 2.361, -8.818, 2.406, -2.23, 2.45, 1.476, 1, 2.483, 4.256, 2.517, 4.211, 2.55, 4.211, 0, 2.783, -8.818, 1, 2.828, -8.818, 2.872, -2.23, 2.917, 1.476, 1, 2.95, 4.256, 2.984, 4.211, 3.017, 4.211, 0, 3.25, -8.818, 1, 3.294, -8.818, 3.339, -5.869, 3.383, 1.476, 1, 3.422, 7.903, 3.461, 11.979, 3.5, 11.979, 0, 3.783, -14.533, 0, 4.05, -1.576, 2, 6.317, -1.576]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.291, 2, 0.417, -1.291, 0, 0.45, -1.371, 0, 0.817, 1.03, 0, 1.15, -1.119, 0, 1.4, 0.561, 1, 1.439, 0.561, 1.478, -0.025, 1.517, -0.884, 1, 1.528, -1.129, 1.539, -1.152, 1.55, -1.152, 0, 1.75, 5.058, 1, 1.828, 5.058, 1.905, 2.181, 1.983, -0.884, 1, 1.994, -1.322, 2.006, -1.152, 2.017, -1.152, 0, 2.217, 5.058, 1, 2.295, 5.058, 2.372, 2.181, 2.45, -0.884, 1, 2.461, -1.322, 2.472, -1.152, 2.483, -1.152, 0, 2.683, 5.058, 1, 2.761, 5.058, 2.839, 2.181, 2.917, -0.884, 1, 2.928, -1.322, 2.939, -1.152, 2.95, -1.152, 0, 3.15, 5.058, 0, 3.383, -0.884, 0, 3.617, 13.426, 0, 3.9, -15.11, 0, 4.05, -1.291, 2, 6.317, -1.291]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.131, 2, 0.417, -1.131, 0, 0.483, -1.518, 2, 0.5, -1.518, 1, 0.622, -1.518, 0.745, 0.294, 0.867, 1.377, 1, 0.872, 1.426, 0.878, 1.384, 0.883, 1.384, 0, 1.233, -1.306, 0, 1.483, 0.743, 1, 1.494, 0.743, 1.506, 0.725, 1.517, 0.527, 1, 1.556, -0.167, 1.594, -0.64, 1.633, -0.64, 0, 1.833, 4.914, 1, 1.883, 4.914, 1.933, 2.085, 1.983, 0.527, 1, 2.022, -0.685, 2.061, -0.64, 2.1, -0.64, 0, 2.3, 4.914, 1, 2.35, 4.914, 2.4, 2.085, 2.45, 0.527, 1, 2.489, -0.685, 2.528, -0.64, 2.567, -0.64, 0, 2.767, 4.914, 1, 2.817, 4.914, 2.867, 2.085, 2.917, 0.527, 1, 2.956, -0.685, 2.994, -0.64, 3.033, -0.64, 0, 3.233, 4.914, 1, 3.283, 4.914, 3.333, 4.416, 3.383, 0.527, 1, 3.411, -1.634, 3.439, -11.222, 3.467, -11.222, 0, 3.717, 16.749, 0, 3.983, -20.062, 0, 4.05, -1.131, 2, 6.317, -1.131]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.918, 2, 0.417, -0.918, 0, 0.55, -1.983, 0, 0.883, 1.658, 0, 1.3, -1.696, 1, 1.372, -1.696, 1.445, -0.61, 1.517, 0.874, 1, 1.528, 1.102, 1.539, 1.059, 1.55, 1.059, 0, 1.717, -0.411, 0, 1.917, 4.847, 0, 1.983, 0.874, 0, 2.017, 1.059, 0, 2.183, -0.411, 0, 2.383, 4.847, 0, 2.45, 0.874, 0, 2.483, 1.059, 0, 2.65, -0.411, 0, 2.85, 4.847, 0, 2.917, 0.874, 0, 2.95, 1.059, 0, 3.117, -0.411, 0, 3.317, 4.847, 1, 3.339, 4.847, 3.361, 4.115, 3.383, 0.874, 1, 3.439, -7.229, 3.494, -12.54, 3.55, -12.54, 0, 3.8, 20.18, 0, 4.05, -0.918, 2, 6.317, -0.918]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.094, 2, 0.417, 0.094, 0, 0.617, -2.35, 0, 0.983, 2.393, 0, 1.35, -2.017, 1, 1.406, -2.017, 1.461, -1.211, 1.517, 0.236, 1, 1.55, 1.104, 1.584, 1.463, 1.617, 1.463, 0, 1.817, -0.277, 1, 1.872, -0.277, 1.928, -0.238, 1.983, 0.236, 1, 2.016, 0.52, 2.05, 1.463, 2.083, 1.463, 0, 2.283, -0.277, 1, 2.339, -0.277, 2.394, -0.238, 2.45, 0.236, 1, 2.483, 0.52, 2.517, 1.463, 2.55, 1.463, 0, 2.75, -0.277, 1, 2.806, -0.277, 2.861, -0.238, 2.917, 0.236, 1, 2.95, 0.52, 2.984, 1.463, 3.017, 1.463, 0, 3.217, -0.277, 1, 3.272, -0.277, 3.328, -0.138, 3.383, 0.236, 1, 3.389, 0.273, 3.394, 4.729, 3.4, 4.729, 0, 3.633, -13.965, 0, 3.867, 23.828, 0, 4.05, 0.094, 2, 6.317, 0.094]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.727, 2, 0.417, 2.727, 0, 0.667, -2.59, 0, 1.067, 3.043, 0, 1.417, -2.758, 1, 1.45, -2.758, 1.484, -2.649, 1.517, -1.67, 1, 1.589, 0.451, 1.661, 2.144, 1.733, 2.144, 1, 1.789, 2.144, 1.844, 1.506, 1.9, -0.16, 1, 1.928, -0.993, 1.955, -1.67, 1.983, -1.67, 0, 2.2, 2.144, 1, 2.256, 2.144, 2.311, 1.506, 2.367, -0.16, 1, 2.395, -0.993, 2.422, -1.67, 2.45, -1.67, 0, 2.667, 2.144, 1, 2.722, 2.144, 2.778, 1.506, 2.833, -0.16, 1, 2.861, -0.993, 2.889, -1.67, 2.917, -1.67, 0, 3.133, 2.144, 1, 3.189, 2.144, 3.244, 1.506, 3.3, -0.16, 1, 3.328, -0.993, 3.355, -1.67, 3.383, -1.67, 0, 3.483, 4.451, 0, 3.717, -15.122, 0, 3.933, 27.354, 0, 4.05, 2.727, 2, 6.317, 2.727]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 0, 1.583, 1, 0, 3.417, 0, 0, 4.583, 1, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "touch_drag6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 1, 1.334, 0.3, 2.25, 0.6, 3.167, 0.9, 1, 3.339, 0.933, 3.511, 0.967, 3.683, 1, 2, 3.75, 1, 0, 3.767, 0, 2, 6.317, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.317, 1]}, {"Target": "Parameter", "Id": "ParamSad2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.317, 1]}, {"Target": "Parameter", "Id": "ParamCRY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.2, 0, 6.317, -5.2]}, {"Target": "Parameter", "Id": "Position_Coordinates", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.317, 1]}, {"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Man_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.317, 1]}, {"Target": "Parameter", "Id": "Man_TMD2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "BG_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.317, 1]}, {"Target": "Parameter", "Id": "FG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "FG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "BG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "BG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "BG_White4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "BG_White5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "BG_White2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "BG_White3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.3, 0, 6.317, 0.3]}, {"Target": "Parameter", "Id": "All_Size2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -12.72, 0, 6.317, -12.72]}, {"Target": "Parameter", "Id": "All_Size3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "All_Size4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "tuerzuo3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "faqiehuan", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "faqiehuan2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "faqiehuan4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "faqiehuan3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "tuerzuo4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Mail_Change", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "dafengshangzhuanchang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "tongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamTongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "EyeFrame_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamBrowForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamBrowY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamSigh", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamDarken", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamCry", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_B", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_C", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "MB_Unidirectional_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "jing<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "huqi1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "huqi2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamArmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.317, 1]}, {"Target": "Parameter", "Id": "ParamForearmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -30, 0, 6.317, -30]}, {"Target": "Parameter", "Id": "ParamArmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamArmL_ForearmL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.317, 1]}, {"Target": "Parameter", "Id": "ParamForearmL_HandL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamHandLDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 8, 0, 6.317, 8]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamHandLSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "xiongjiya", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.317, 1]}, {"Target": "Parameter", "Id": "xiongjiya5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "xiongjiya2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamArmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.317, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 30, 0, 6.317, 30]}, {"Target": "Parameter", "Id": "ParamArmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamArmR_ForearmR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.317, 1]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamForearm<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.317, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_HandR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamHandRDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 8, 0, 6.317, 8]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamHandRSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.317, 1]}, {"Target": "Parameter", "Id": "Change_idle_physics8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.317, 1]}, {"Target": "Parameter", "Id": "Change_idle_physics7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 6.317, 0.6]}, {"Target": "Parameter", "Id": "Change_idle_physics23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.317, 1]}, {"Target": "Parameter", "Id": "Change_idle_physics24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.3, 0, 6.317, -0.3]}, {"Target": "Parameter", "Id": "Change_idle_physics11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Param103", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -11.235, 0, 6.317, -11.235]}, {"Target": "Parameter", "Id": "taiqiugundong", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -22.5, 0, 6.317, -22.5]}, {"Target": "Parameter", "Id": "Change_idle_physics32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamLegL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.317, 1]}, {"Target": "Parameter", "Id": "ParamLegLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamLegLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamCrusLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamCrusLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>oodLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamFoodLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamFoodLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamLegR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.317, 1]}, {"Target": "Parameter", "Id": "ParamLegRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamLegRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamCrusRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamCrusRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamCrus<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>ood<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamFoodRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamFoodRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Physics_Provisional", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "physics_Add_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "physics_Add_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamBodyStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Physics_AngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Physics_AngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Physics_Body_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Physics_Body_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Physics_breast", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamBreath2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "fenshangxuanzhuang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "touch_drag1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "touch_drag2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "touch_drag3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "touch_drag4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "touch_drag5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "touch_drag7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "touch_drag8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "touch_drag9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>pian<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh654", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh655", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh657", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh656", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh658", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh659", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh660", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.317, 0]}], "UserData": []}