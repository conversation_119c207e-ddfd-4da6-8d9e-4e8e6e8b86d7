{"Version": 3, "Meta": {"Duration": 6.0, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 303, "TotalSegmentCount": 348, "TotalPointCount": 390, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 2, 0.883, 1, 0, 1.017, 0, 0, 1.217, 1, 2, 3.75, 1, 0, 3.883, 0, 0, 4.083, 1, 2, 6, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 2, 0.883, 1, 0, 1.017, 0, 0, 1.217, 1, 2, 3.75, 1, 0, 3.883, 0, 0, 4.083, 1, 2, 6, 1]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "Segments": [0, 0, 1, 0.228, 0.025, 0.455, 0.042, 0.683, 0.042, 0, 2.367, -0.073, 0, 3.95, 0.052, 0, 5.267, -0.037, 1, 5.511, -0.037, 5.756, -0.027, 6, 0]}, {"Target": "Parameter", "Id": "ParamHandLChange", "Segments": [0, 0, 0, 1.5, 0.8, 0, 3, 0, 0, 4.5, 0.4, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "Segments": [0, 0, 0, 1.5, 0.4, 0, 3, 0, 0, 4.5, 0.5, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 0, 1.233, 4.138, 1, 2.45, 4.138, 3.666, 3.589, 4.883, 1.738, 1, 5.255, 1.172, 5.628, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 0, 1.533, -7.13, 0, 3, -0.006, 0, 4.567, -7.13, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X", "Segments": [0, 0, 1, 0.5, 3.189, 1, 4.783, 1.5, 4.783, 1, 2, 4.783, 2.5, 3.189, 3, 0, 1, 3.5, -3.189, 4, -4.783, 4.5, -4.783, 1, 5, -4.783, 5.5, -3.189, 6, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y", "Segments": [0, -7.795, 1, 0.5, -7.795, 1, -5.197, 1.5, 0, 1, 2, 5.197, 2.5, 7.795, 3, 7.795, 1, 3.5, 7.795, 4, 5.197, 4.5, 0, 1, 5, -5.197, 5.5, -7.795, 6, -7.795]}, {"Target": "Parameter", "Id": "ParamBodyStretch2", "Segments": [0, 0, 1, 0.5, 2.852, 1, 4.293, 1.5, 4.293, 1, 2, 4.293, 2.5, 2.852, 3, 0, 1, 3.5, -2.852, 4, -4.264, 4.5, -4.264, 1, 5, -4.264, 5.5, -2.852, 6, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ", "Segments": [0, 0, 1, 0.75, 2.265, 1.5, 3.269, 2.25, 3.269, 0, 4.233, -2.807, 1, 4.822, -2.807, 5.411, -1.778, 6, 0]}, {"Target": "Parameter", "Id": "ParamLegLAngle", "Segments": [0, 0, 0, 3.017, 14.515, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamLegRAngle", "Segments": [0, 0, 1, 0.522, 5.954, 1.045, 7.732, 1.567, 7.732, 0, 4.583, -9.756, 1, 5.055, -9.756, 5.528, -5.384, 6, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 1.167, 1, 0, 3, 0, 0, 4.167, 1, 0, 6, 0]}, {"Target": "Parameter", "Id": "fenshangxuanzhuang", "Segments": [0, 0, 1, 2, 0.801, 4, 1.601, 6, 2.402]}, {"Target": "Parameter", "Id": "Mail_Transparency6", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamSad2", "Segments": [0, 1, 0, 6, 1]}, {"Target": "Parameter", "Id": "ParamCRY", "Segments": [0, -5.2, 0, 6, -5.2]}, {"Target": "Parameter", "Id": "Position_Coordinates", "Segments": [0, 1, 0, 6, 1]}, {"Target": "Parameter", "Id": "All_X", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "All_Y", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "All_Angle", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "All_Size", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Man_TMD", "Segments": [0, 1, 0, 6, 1]}, {"Target": "Parameter", "Id": "Man_TMD2", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "BG_TMD", "Segments": [0, 1, 0, 6, 1]}, {"Target": "Parameter", "Id": "FG_Black", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "FG_White", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "BG_Black", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "BG_White", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "BG_White4", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "BG_White5", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "BG_White2", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "BG_White3", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "All_Size2", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "All_Size3", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "All_Size4", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "tuerzuo3", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "faqiehuan", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "faqiehuan2", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "faqiehuan4", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "faqiehuan3", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "tuerzuo4", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param10", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param7", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param9", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param8", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Mail_Change", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency4", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency5", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency2", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency7", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency11", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency12", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency10", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency8", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency9", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "dafengshangzhuanchang", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY_SD", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY2", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "tongue", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamTongue", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "EyeFrame_X", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Y", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Angle", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamBrowForm", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamBrowAngle", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamBrowY", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLsize", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamEyeLsize", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamEyeRsize", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamEyeLGG", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamEyeRGG", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_X", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_Y", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON>", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamSigh", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamDarken", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamCry", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_A", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_B", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_C", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "MB_Unidirectional_A", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "jing<PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param90", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param91", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param92", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param93", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "huqi1", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "huqi2", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE3", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE7", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE5", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamShoulderLStretch", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamArmL_RE", "Segments": [0, 1, 0, 6, 1]}, {"Target": "Parameter", "Id": "ParamForearmL_RE", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamArmLChange", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamArmL_ForearmL", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "Segments": [0, 30, 0, 6, 30]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, -1, 0, 6, -1]}, {"Target": "Parameter", "Id": "ParamForearmL_HandL", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamHandLDisplay", "Segments": [0, 10, 0, 6, 10]}, {"Target": "Parameter", "Id": "ParamHandLAngleX", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleY", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleX", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleY", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamHandLSize", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA2", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics9", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "xiongjiya", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "xiongjiya5", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "xiongjiya3", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "xiongjiya4", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "xiongjiya2", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE2", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE6", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE4", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamShoulderRStretch", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamArmR_RE", "Segments": [0, 1, 0, 6, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_RE", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamArmRChange", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamArmR_ForearmR", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA3", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamForearm<PERSON>ayer", "Segments": [0, 1, 0, 6, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_HandR", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamHandRDisplay", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleX", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleY", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleX", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleY", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamHandRSize", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics2", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics8", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics7", "Segments": [0, 0.6, 0, 6, 0.6]}, {"Target": "Parameter", "Id": "Change_idle_physics23", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics25", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics3", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics24", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics4", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics5", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics6", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics10", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics11", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics12", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics13", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics20", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics14", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics26", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics27", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics28", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics29", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics30", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics31", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param103", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "taiqiugundong", "Segments": [0, -22.5, 0, 6, -22.5]}, {"Target": "Parameter", "Id": "Change_idle_physics32", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics15", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics16", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics17", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics21", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics22", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyY", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamLegL_RE", "Segments": [0, 1, 0, 6, 1]}, {"Target": "Parameter", "Id": "ParamLegLStretch", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamCrusLAngle", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamCrusLStretch", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>oodLAngle", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamFoodLAnimation", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamFoodLChange", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamLegR_RE", "Segments": [0, 1, 0, 6, 1]}, {"Target": "Parameter", "Id": "ParamLegRStretch", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamCrusRAngle", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamCrusRStretch", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamCrus<PERSON>ayer", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>ood<PERSON>ngle", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamFoodRAnimation", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamFoodRChange", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Physics_Provisional", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "physics_Add_X", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "physics_Add_Y", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamBodyStretch", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Physics_AngleX", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Physics_AngleY", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Physics_AngleZ", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Physics_Body_X", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Physics_Body_Y", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_X", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_Y", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_1", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_2", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_3", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Physics_breast", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_1", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_2", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_L", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_R", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_1", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_2", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_1", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_2", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Jewelry_1", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Jewelry_2", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Jewelry_3", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_5", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_4", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation32", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation40", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation44", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation45", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation46", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_1", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_2", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_1", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_2", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_3", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamBreath2", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X2", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y2", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX2", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ2", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveX", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveY", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "touch_drag1", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "touch_drag2", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "touch_drag3", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "touch_drag4", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "touch_drag5", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "touch_drag6", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "touch_drag7", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "touch_drag8", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "touch_drag9", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle1", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle2", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle3", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle4", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle5", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle6", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle7", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle8", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle9", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle10", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle11", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle12", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle13", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle14", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle15", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle16", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle17", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle18", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle19", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>pian<PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang3", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang5", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh653", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh653", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh653", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh653", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh653", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh654", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh655", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh657", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh656", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh658", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh659", "Segments": [0, 0, 0, 6, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh660", "Segments": [0, 0, 0, 6, 0]}], "UserData": []}