{"Version": 3, "Meta": {"Duration": 15.667, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 303, "TotalSegmentCount": 2510, "TotalPointCount": 2762, "UserDataCount": 2, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Mail_Transparency6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 9.15, 1, 0, 9.383, 0, 2, 11.517, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 1, -0.171, 2, -0.341, 3, -0.512, 1, 3.006, -1.349, 3.011, -2.185, 3.017, -3.022, 1, 4.245, -1.211, 5.472, 0.599, 6.7, 2.41, 1, 6.706, 1.607, 6.711, 0.803, 6.717, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.18, 1, 1, -0.593, 2, -1.007, 3, -1.42, 1, 3.006, -0.774, 3.011, -0.128, 3.017, 0.518, 1, 4.245, -0.498, 5.472, -1.514, 6.7, -2.53, 1, 6.706, -1.747, 6.711, -0.963, 6.717, -0.18, 1, 9.611, -0.12, 12.506, -0.06, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.5, 2, 3, 1.5, 1, 3.006, 1.116, 3.011, 0.731, 3.017, 0.347, 1, 4.245, 0.711, 5.472, 1.076, 6.7, 1.44, 1, 6.706, 1.46, 6.711, 1.48, 6.717, 1.5, 1, 9.611, 1, 12.506, 0.5, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 4.14, 1, 1, 4.36, 2, 4.58, 3, 4.8, 1, 3.006, 9.056, 3.011, 13.311, 3.017, 17.567, 1, 4.245, 15.808, 5.472, 14.049, 6.7, 12.29, 1, 6.706, 9.573, 6.711, 6.857, 6.717, 4.14, 1, 9.611, 2.76, 12.506, 1.38, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "BG_White4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.989, 0, 0.267, 1, 0, 0.933, 0.989, 0, 1.2, 1, 0, 1.933, 0.989, 0, 2.2, 1, 0, 2.867, 0.989, 0, 3.133, 1, 0, 3.833, 0.989, 0, 4.117, 1, 0, 4.783, 0.989, 0, 5.067, 1, 0, 5.767, 0.989, 0, 6.033, 1, 0, 6.7, 0.989, 0, 7, 1, 0, 7.683, 0.989, 0, 7.95, 1, 0, 8.633, 0.989, 0, 8.9, 1, 1, 8.983, 1, 9.067, 1, 9.15, 0.997, 1, 9.228, 0.993, 9.305, 0, 9.383, 0, 2, 11.517, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "BG_White3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.3, 2, 0.933, 0.3, 2, 1.933, 0.3, 2, 2.867, 0.3, 2, 3.833, 0.3, 2, 4.783, 0.3, 2, 5.767, 0.3, 2, 6.7, 0.3, 2, 7.683, 0.3, 2, 8.633, 0.3, 2, 9.15, 0.3, 0, 9.383, 0, 2, 11.517, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "All_Size2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -12.72, 2, 0.933, -12.72, 2, 1.933, -12.72, 2, 2.867, -12.72, 2, 3.833, -12.72, 2, 4.783, -12.72, 2, 5.767, -12.72, 2, 6.7, -12.72, 2, 7.683, -12.72, 2, 8.633, -12.72, 2, 9.15, -12.72, 0, 9.383, 0, 2, 11.517, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.5, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY_SD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 0.433, 1, 0, 1.783, 0, 0, 1.9, 1, 0, 2.017, 0, 0, 2.2, 1, 0, 2.317, 0, 0, 2.55, 1, 0, 2.717, 0, 0, 2.9, 1, 0, 3.017, 0, 0, 3.2, 1, 0, 3.317, 0, 0, 3.5, 1, 0, 3.633, 0, 0, 3.783, 1, 0, 3.95, 0, 0, 4.067, 1, 0, 4.183, 0, 0, 4.4, 1, 0, 4.617, 0, 0, 4.867, 1, 0, 5.15, 0, 2, 5.883, 0, 0, 6, 1, 0, 6.117, 0, 0, 6.3, 1, 0, 6.533, 0, 2, 6.8, 0, 0, 6.917, 1, 0, 7.033, 0, 0, 7.217, 1, 0, 7.567, 0, 2, 7.733, 0, 0, 7.917, 1, 0, 8.033, 0, 0, 8.217, 1, 0, 8.333, 0, 0, 8.517, 1, 0, 8.65, 0, 0, 8.8, 1, 0, 8.967, 0, 2, 9.517, 0, 0, 9.633, 1, 0, 9.75, 0, 0, 9.933, 1, 0, 10.05, 0, 0, 10.283, 1, 0, 10.45, 0, 2, 10.75, 0, 0, 10.933, 1, 0, 11.05, 0, 0, 11.233, 1, 0, 11.367, 0, 0, 11.517, 1, 0, 11.683, 0, 0, 11.8, 1, 0, 11.917, 0, 0, 12.1, 1, 0, 12.217, 0, 0, 12.45, 1, 0, 12.617, 0, 0, 12.8, 1, 0, 12.917, 0, 0, 13.1, 1, 0, 13.217, 0, 0, 13.4, 1, 0, 13.533, 0, 0, 13.683, 1, 0, 13.85, 0, 0, 14.05, 0.7, 0, 14.233, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.333, 1, 0, 0.6, 0, 0, 0.833, 1, 2, 1.25, 1, 0, 1.517, 0, 0, 1.767, 1, 2, 2.867, 1, 0, 3.15, 0, 0, 3.3, 1.3, 0, 3.5, 1, 2, 4.317, 1, 0, 4.5, 0, 2, 5.683, 0, 0, 5.95, 1, 2, 8.083, 1, 0, 8.217, 0, 0, 8.4, 1.3, 0, 8.483, 1, 2, 9.15, 1, 2, 9.383, 1, 2, 11.417, 1, 0, 11.517, 0, 0, 11.667, 1.3, 0, 11.733, 1, 2, 14.3, 1, 0, 14.433, 0, 0, 14.617, 1.3, 0, 14.7, 1, 2, 15.4, 1, 2, 15.667, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.333, 1, 0, 0.6, 0, 0, 0.833, 1, 2, 1.25, 1, 0, 1.517, 0, 0, 1.767, 1, 2, 2.867, 1, 0, 3.15, 0, 0, 3.3, 1.3, 0, 3.5, 1, 2, 4.317, 1, 0, 4.5, 0, 2, 5.683, 0, 0, 5.95, 1, 2, 8.083, 1, 0, 8.217, 0, 0, 8.4, 1.3, 0, 8.483, 1, 2, 9.15, 1, 2, 9.383, 1, 2, 11.417, 1, 0, 11.517, 0, 0, 11.667, 1.3, 0, 11.733, 1, 2, 14.3, 1, 0, 14.433, 0, 0, 14.617, 1.3, 0, 14.7, 1, 2, 15.4, 1, 2, 15.667, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 1, 0.494, 0, 0.656, -0.374, 0.817, -0.5, 1, 0.961, -0.613, 1.106, -0.6, 1.25, -0.6, 1, 1.428, -0.6, 1.605, 0.459, 1.783, 0.5, 1, 2.194, 0.594, 2.606, 0.6, 3.017, 0.6, 0, 3.3, 0, 2, 5.35, 0, 2, 6.033, 0, 2, 9.15, 0, 2, 9.383, 0, 2, 11.517, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 1, 0.494, 0, 0.656, -0.206, 0.817, -0.4, 1, 0.961, -0.574, 1.106, -0.6, 1.25, -0.6, 0, 1.783, 0.2, 0, 3.017, 0, 2, 3.3, 0, 2, 5.35, 0, 2, 6.033, 0, 2, 9.15, 0, 2, 9.383, 0, 2, 11.517, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1, 2, 9.15, -1, 0, 9.383, -0.741, 0, 9.783, -1, 2, 11.517, -1, 2, 11.767, -1, 2, 12.183, -1, 2, 14.3, -1, 0, 14.783, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamBrowForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1, 2, 9.15, -1, 0, 9.383, 0, 2, 11.517, 0, 2, 11.767, 0, 0, 12.183, -1, 2, 14.3, -1, 0, 14.783, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamBrowAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.284, 0, 0.733, 0.4, 0, 0.933, 0.284, 0, 1.683, 0.4, 0, 1.933, 0.284, 0, 2.65, 0.4, 0, 2.867, 0.284, 0, 3.617, 0.4, 0, 3.833, 0.284, 0, 4.567, 0.4, 0, 4.783, 0.284, 0, 5.533, 0.4, 0, 5.767, 0.284, 0, 6.5, 0.4, 0, 6.7, 0.284, 0, 7.45, 0.4, 0, 7.683, 0.284, 0, 8.417, 0.4, 0, 8.633, 0.284, 0, 9.15, 0.373, 0, 9.383, 0, 2, 11.517, 0, 2, 12.183, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, 1, 2, 0.567, 1, 0, 0.717, -1, 2, 0.867, -1, 0, 1.117, 0.337, 0, 1.267, 0.113, 0, 1.4, 1, 2, 1.483, 1, 0, 1.633, -1, 2, 1.783, -1, 0, 2.017, 0.35, 0, 2.333, -0.097, 0, 2.65, 0.027, 0, 2.867, -0.004, 0, 3.017, 1, 2, 3.1, 1, 0, 3.217, -1, 2, 3.367, -1, 0, 3.383, 1, 2, 3.567, 1, 0, 3.783, -0.408, 0, 4.1, 0.112, 0, 4.317, -0.013, 0, 4.417, 1, 2, 4.55, 1, 0, 4.817, -0.327, 0, 5.117, 0.091, 0, 5.433, -0.025, 0, 5.683, 0.006, 0, 5.817, -1, 2, 5.917, -1, 0, 6.15, 0.402, 0, 6.467, -0.11, 0, 6.767, 0.031, 0, 7.083, -0.009, 0, 7.383, 0.002, 2, 7.417, 0.002, 0, 7.65, -0.001, 2, 7.667, -0.001, 2, 7.683, -0.001, 2, 7.717, -0.001, 2, 7.733, -0.001, 2, 7.75, -0.001, 0, 7.767, 0, 2, 7.783, 0, 2, 7.817, 0, 2, 7.833, 0, 2, 7.883, 0, 2, 7.9, 0, 2, 7.917, 0, 2, 7.95, 0, 2, 7.967, 0, 2, 8.067, 0, 2, 8.083, 0, 0, 8.15, 1, 2, 8.3, 1, 0, 8.317, -1, 2, 8.45, -1, 0, 8.75, 0.244, 0, 9.067, -0.068, 0, 9.367, 0.019, 0, 9.667, -0.005, 2, 9.683, -0.005, 0, 9.983, 0.002, 2, 10, 0.002, 0, 10.017, 0.001, 2, 10.033, 0.001, 0, 10.25, 0, 2, 10.35, 0, 2, 10.367, 0, 2, 10.383, 0, 2, 10.4, 0, 2, 10.417, 0, 2, 10.433, 0, 2, 10.467, 0, 2, 10.483, 0, 2, 10.517, 0, 2, 10.533, 0, 2, 10.717, 0, 2, 10.733, 0, 2, 11.417, 0, 0, 11.467, 1, 2, 11.55, 1, 0, 11.567, -1, 2, 11.633, -1, 0, 11.733, 1, 2, 11.883, 1, 0, 12.133, -0.317, 0, 12.45, 0.088, 0, 12.767, -0.025, 0, 13.067, 0.007, 0, 13.367, -0.002, 2, 13.4, -0.002, 0, 13.65, 0, 2, 13.733, 0, 2, 13.75, 0, 2, 13.767, 0, 2, 13.783, 0, 2, 13.8, 0, 2, 13.817, 0, 2, 13.833, 0, 2, 13.867, 0, 2, 13.9, 0, 2, 13.917, 0, 2, 13.967, 0, 2, 13.983, 0, 2, 14, 0, 2, 14.017, 0, 2, 14.133, 0, 2, 14.15, 0, 2, 14.3, 0, 0, 14.367, 1, 2, 14.517, 1, 0, 14.533, -1, 2, 14.667, -1, 0, 14.967, 0.244, 0, 15.283, -0.068, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeRsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 0, 0.467, 1, 2, 0.567, 1, 0, 0.717, -1, 2, 0.867, -1, 0, 1.117, 0.337, 0, 1.267, 0.113, 0, 1.4, 1, 2, 1.483, 1, 0, 1.633, -1, 2, 1.783, -1, 0, 2.017, 0.35, 0, 2.333, -0.097, 0, 2.65, 0.027, 0, 2.867, -0.004, 0, 3.017, 1, 2, 3.1, 1, 0, 3.217, -1, 2, 3.367, -1, 0, 3.383, 1, 2, 3.567, 1, 0, 3.783, -0.408, 0, 4.1, 0.112, 0, 4.317, -0.013, 0, 4.417, 1, 2, 4.55, 1, 0, 4.817, -0.327, 0, 5.117, 0.091, 0, 5.433, -0.025, 0, 5.683, 0.006, 0, 5.817, -1, 2, 5.917, -1, 0, 6.15, 0.402, 0, 6.467, -0.11, 0, 6.767, 0.031, 0, 7.083, -0.009, 0, 7.383, 0.002, 2, 7.417, 0.002, 0, 7.65, -0.001, 2, 7.667, -0.001, 2, 7.683, -0.001, 2, 7.717, -0.001, 2, 7.733, -0.001, 2, 7.75, -0.001, 0, 7.767, 0, 2, 7.783, 0, 2, 7.817, 0, 2, 7.833, 0, 2, 7.883, 0, 2, 7.9, 0, 2, 7.917, 0, 2, 7.95, 0, 2, 7.967, 0, 2, 8.067, 0, 2, 8.083, 0, 0, 8.15, 1, 2, 8.3, 1, 0, 8.317, -1, 2, 8.45, -1, 0, 8.75, 0.244, 0, 9.067, -0.068, 0, 9.367, 0.019, 0, 9.667, -0.005, 2, 9.683, -0.005, 0, 9.983, 0.002, 2, 10, 0.002, 0, 10.017, 0.001, 2, 10.033, 0.001, 0, 10.25, 0, 2, 10.35, 0, 2, 10.367, 0, 2, 10.383, 0, 2, 10.4, 0, 2, 10.417, 0, 2, 10.433, 0, 2, 10.467, 0, 2, 10.483, 0, 2, 10.517, 0, 2, 10.533, 0, 2, 10.717, 0, 2, 10.733, 0, 2, 11.417, 0, 0, 11.467, 1, 2, 11.55, 1, 0, 11.567, -1, 2, 11.633, -1, 0, 11.733, 1, 2, 11.883, 1, 0, 12.133, -0.317, 0, 12.45, 0.088, 0, 12.767, -0.025, 0, 13.067, 0.007, 0, 13.367, -0.002, 2, 13.4, -0.002, 0, 13.65, 0, 2, 13.733, 0, 2, 13.75, 0, 2, 13.767, 0, 2, 13.783, 0, 2, 13.8, 0, 2, 13.817, 0, 2, 13.833, 0, 2, 13.867, 0, 2, 13.9, 0, 2, 13.917, 0, 2, 13.967, 0, 2, 13.983, 0, 2, 14, 0, 2, 14.017, 0, 2, 14.133, 0, 2, 14.15, 0, 2, 14.3, 0, 0, 14.367, 1, 2, 14.517, 1, 0, 14.533, -1, 2, 14.667, -1, 0, 14.967, 0.244, 0, 15.283, -0.068, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeLGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 0, 0.45, -0.266, 0, 0.667, 0.804, 0, 0.95, -0.71, 0, 1.25, 0.289, 0, 1.4, -0.235, 0, 1.6, 0.55, 0, 1.867, -0.905, 0, 2.2, 0.31, 0, 2.5, -0.125, 0, 2.8, 0.047, 2, 2.817, 0.047, 0, 2.983, -0.261, 0, 3.2, 0.83, 0, 3.283, 0.342, 0, 3.4, 1, 2, 3.5, 1, 0, 3.517, -1, 2, 3.583, -1, 0, 3.783, 0.339, 0, 4.033, -0.144, 0, 4.3, 0.074, 0, 4.417, -0.284, 0, 4.65, 0.526, 0, 4.95, -0.277, 0, 5.267, 0.118, 0, 5.567, -0.045, 2, 5.583, -0.045, 0, 5.8, 0.275, 0, 6.017, -0.6, 0, 6.317, 0.306, 0, 6.617, -0.132, 0, 6.933, 0.051, 0, 7.233, -0.018, 0, 7.533, 0.006, 2, 7.55, 0.006, 0, 7.833, -0.002, 2, 7.867, -0.002, 0, 8.083, 0, 0, 8.15, -0.305, 0, 8.2, -0.199, 0, 8.3, -1, 2, 8.317, -1, 0, 8.333, 1, 2, 8.367, 1, 0, 8.4, 0.831, 0, 8.45, 0.864, 0, 8.7, -0.314, 0, 8.967, 0.153, 0, 9.25, -0.069, 0, 9.55, 0.028, 0, 9.85, -0.01, 0, 10.133, 0.004, 2, 10.167, 0.004, 0, 10.417, -0.001, 2, 10.433, -0.001, 2, 10.45, -0.001, 2, 10.483, -0.001, 0, 10.7, 0, 2, 10.717, 0, 2, 10.733, 0, 2, 10.8, 0, 2, 10.817, 0, 2, 10.85, 0, 2, 10.867, 0, 2, 10.883, 0, 2, 10.9, 0, 2, 10.917, 0, 2, 10.933, 0, 2, 10.967, 0, 2, 10.983, 0, 2, 11.183, 0, 2, 11.2, 0, 2, 11.417, 0, 0, 11.467, -0.3, 0, 11.5, -0.242, 0, 11.567, -1, 2, 11.617, -1, 0, 11.633, 1, 2, 11.667, 1, 0, 11.85, -0.755, 0, 12.1, 0.262, 0, 12.35, -0.155, 0, 12.633, 0.078, 0, 12.933, -0.033, 0, 13.233, 0.012, 0, 13.533, -0.004, 0, 13.833, 0.002, 2, 13.85, 0.002, 0, 13.867, 0.001, 2, 13.883, 0.001, 0, 14.067, 0, 2, 14.083, 0, 2, 14.117, 0, 2, 14.183, 0, 2, 14.2, 0, 2, 14.217, 0, 2, 14.233, 0, 2, 14.25, 0, 2, 14.267, 0, 2, 14.283, 0, 2, 14.3, 0, 0, 14.367, -0.305, 0, 14.417, -0.199, 0, 14.517, -1, 2, 14.533, -1, 0, 14.55, 1, 2, 14.583, 1, 0, 14.617, 0.832, 2, 14.633, 0.832, 0, 14.667, 0.864, 0, 14.917, -0.314, 0, 15.183, 0.153, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeRGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 0, 0.45, -0.266, 0, 0.667, 0.804, 0, 0.95, -0.71, 0, 1.25, 0.289, 0, 1.4, -0.235, 0, 1.6, 0.55, 0, 1.867, -0.905, 0, 2.2, 0.31, 0, 2.5, -0.125, 0, 2.8, 0.047, 2, 2.817, 0.047, 0, 2.983, -0.261, 0, 3.2, 0.83, 0, 3.283, 0.342, 0, 3.4, 1, 2, 3.5, 1, 0, 3.517, -1, 2, 3.583, -1, 0, 3.783, 0.339, 0, 4.033, -0.144, 0, 4.3, 0.074, 0, 4.417, -0.284, 0, 4.65, 0.526, 0, 4.95, -0.277, 0, 5.267, 0.118, 0, 5.567, -0.045, 2, 5.583, -0.045, 0, 5.8, 0.275, 0, 6.017, -0.6, 0, 6.317, 0.306, 0, 6.617, -0.132, 0, 6.933, 0.051, 0, 7.233, -0.018, 0, 7.533, 0.006, 2, 7.55, 0.006, 0, 7.833, -0.002, 2, 7.867, -0.002, 0, 8.083, 0, 0, 8.15, -0.305, 0, 8.2, -0.199, 0, 8.3, -1, 2, 8.317, -1, 0, 8.333, 1, 2, 8.367, 1, 0, 8.4, 0.831, 0, 8.45, 0.864, 0, 8.7, -0.314, 0, 8.967, 0.153, 0, 9.25, -0.069, 0, 9.55, 0.028, 0, 9.85, -0.01, 0, 10.133, 0.004, 2, 10.167, 0.004, 0, 10.417, -0.001, 2, 10.433, -0.001, 2, 10.45, -0.001, 2, 10.483, -0.001, 0, 10.7, 0, 2, 10.717, 0, 2, 10.733, 0, 2, 10.8, 0, 2, 10.817, 0, 2, 10.85, 0, 2, 10.867, 0, 2, 10.883, 0, 2, 10.9, 0, 2, 10.917, 0, 2, 10.933, 0, 2, 10.967, 0, 2, 10.983, 0, 2, 11.183, 0, 2, 11.2, 0, 2, 11.417, 0, 0, 11.467, -0.3, 0, 11.5, -0.242, 0, 11.567, -1, 2, 11.617, -1, 0, 11.633, 1, 2, 11.667, 1, 0, 11.85, -0.755, 0, 12.1, 0.262, 0, 12.35, -0.155, 0, 12.633, 0.078, 0, 12.933, -0.033, 0, 13.233, 0.012, 0, 13.533, -0.004, 0, 13.833, 0.002, 2, 13.85, 0.002, 0, 13.867, 0.001, 2, 13.883, 0.001, 0, 14.067, 0, 2, 14.083, 0, 2, 14.117, 0, 2, 14.183, 0, 2, 14.2, 0, 2, 14.217, 0, 2, 14.233, 0, 2, 14.25, 0, 2, 14.267, 0, 2, 14.283, 0, 2, 14.3, 0, 0, 14.367, -0.305, 0, 14.417, -0.199, 0, 14.517, -1, 2, 14.533, -1, 0, 14.55, 1, 2, 14.583, 1, 0, 14.617, 0.832, 2, 14.633, 0.832, 0, 14.667, 0.864, 0, 14.917, -0.314, 0, 15.183, 0.153, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.374, 2, 0.933, -2.374, 2, 1.933, -2.374, 2, 2.867, -2.374, 2, 3.833, -2.374, 2, 4.783, -2.374, 2, 5.767, -2.374, 2, 6.7, -2.374, 2, 7.683, -2.374, 2, 8.633, -2.374, 2, 9.15, -2.374, 0, 9.383, 0, 2, 11.517, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 6.925, 2, 0.933, 6.925, 2, 1.933, 6.925, 2, 2.867, 6.925, 2, 3.833, 6.925, 2, 4.783, 6.925, 2, 5.767, 6.925, 2, 6.7, 6.925, 2, 7.683, 6.925, 2, 8.633, 6.925, 2, 9.15, 6.925, 0, 9.383, 0, 2, 11.517, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -4.027, 2, 0.933, -4.027, 2, 1.933, -4.027, 2, 2.867, -4.027, 2, 3.833, -4.027, 2, 4.783, -4.027, 2, 5.767, -4.027, 2, 6.7, -4.027, 2, 7.683, -4.027, 2, 8.633, -4.027, 2, 9.15, -4.027, 0, 9.383, 0, 2, 11.517, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10.008, 2, 0.933, -10.008, 2, 1.933, -10.008, 2, 2.867, -10.008, 2, 3.833, -10.008, 2, 4.783, -10.008, 2, 5.767, -10.008, 2, 6.7, -10.008, 2, 7.683, -10.008, 2, 8.633, -10.008, 2, 9.15, -10.008, 0, 9.383, 0, 2, 11.517, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "huqi1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.25, 1, 2, 9.15, 1, 0, 9.383, 0, 2, 11.517, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "huqi2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.933, 0, 0, 2.217, 1, 2, 9.15, 1, 0, 9.383, 0, 2, 11.517, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamShoulderLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.548, 0, 0.317, -1, 0, 0.933, -0.548, 0, 1.267, -1, 0, 1.933, -0.548, 0, 2.233, -1, 0, 2.867, -0.548, 0, 3.2, -1, 0, 3.833, -0.548, 0, 4.15, -1, 0, 4.783, -0.548, 0, 5.133, -1, 0, 5.767, -0.548, 0, 6.083, -1, 0, 6.7, -0.548, 0, 7.05, -1, 0, 7.683, -0.548, 0, 8, -1, 0, 8.633, -0.548, 0, 8.967, -1, 1, 9.028, -1, 9.089, -1.027, 9.15, -0.912, 1, 9.228, -0.765, 9.305, 0, 9.383, 0, 2, 9.85, 0, 2, 11.317, 0, 2, 11.517, 0, 2, 12.167, 0, 2, 14.35, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.1, 2, 9.15, 2.1, 1, 9.228, 2.1, 9.305, 2.059, 9.383, 2.217, 1, 9.539, 2.534, 9.694, 3.2, 9.85, 3.2, 2, 11.317, 3.2, 1, 11.384, 3.2, 11.45, 3.203, 11.517, 3.13, 1, 11.734, 2.893, 11.95, 2.7, 12.167, 2.7, 2, 14.35, 2.7, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -8.2, 2, 0.933, -8.2, 2, 1.933, -8.2, 2, 2.867, -8.2, 2, 3.833, -8.2, 2, 4.783, -8.2, 2, 5.767, -8.2, 2, 6.7, -8.2, 2, 7.683, -8.2, 2, 8.633, -8.2, 2, 9.15, -8.2, 2, 9.383, 0, 2, 9.85, 0, 2, 11.317, 0, 2, 11.517, 0, 2, 12.167, 0, 2, 14.35, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 3.507, 2, 9.15, 3.507, 0, 9.383, 6.834, 1, 9.428, 6.834, 9.472, 2.632, 9.517, 2.029, 1, 9.628, 0.522, 9.739, 0.3, 9.85, 0.3, 2, 10.133, 0.3, 1, 10.283, 0.3, 10.433, 0.547, 10.583, 0.6, 1, 10.828, 0.687, 11.072, 0.704, 11.317, 0.78, 1, 11.384, 0.801, 11.45, 2.332, 11.517, 3.512, 1, 11.578, 4.593, 11.639, 5.927, 11.7, 5.927, 0, 12.167, -1.3, 1, 12.895, -1.3, 13.622, -1.191, 14.35, -0.88, 1, 14.433, -0.844, 14.517, 0.859, 14.6, 3.163, 1, 14.7, 5.928, 14.8, 23.801, 14.9, 27.055, 1, 14.994, 30.128, 15.089, 30, 15.183, 30, 2, 15.4, 30, 2, 15.667, 30]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.933, 0, 2, 1.933, 0, 2, 2.867, 0, 2, 3.833, 0, 2, 4.783, 0, 2, 5.767, 0, 2, 6.7, 0, 2, 7.683, 0, 2, 8.633, 0, 2, 9.15, 0, 2, 9.383, 0, 2, 9.85, 0, 2, 11.317, 0, 1, 11.384, 0, 11.45, 0.024, 11.517, -0.526, 1, 11.734, -2.314, 11.95, -3.78, 12.167, -3.78, 2, 14.35, -3.78, 0, 14.733, -30, 0, 15.083, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.933, 1, 2, 1.933, 1, 2, 2.867, 1, 2, 3.833, 1, 2, 4.783, 1, 2, 5.767, 1, 2, 6.7, 1, 2, 7.683, 1, 2, 8.633, 1, 2, 9.15, 1, 1, 9.228, 0.795, 9.305, 0.591, 9.383, 0.386, 0, 9.85, 1, 2, 11.317, 1, 2, 11.517, 1, 2, 12.167, 1, 2, 14.35, 1, 2, 14.733, 1, 2, 14.75, -1, 2, 15.4, -1, 2, 15.667, -1]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.507, 0, 0.5, -0.421, 0, 0.933, -0.507, 0, 1.45, -0.421, 0, 1.933, -0.507, 0, 2.433, -0.421, 0, 2.867, -0.507, 0, 3.4, -0.421, 0, 3.833, -0.507, 0, 4.333, -0.421, 0, 4.783, -0.507, 0, 5.317, -0.421, 0, 5.767, -0.507, 0, 6.267, -0.421, 0, 6.7, -0.507, 0, 7.25, -0.421, 0, 7.683, -0.507, 0, 8.183, -0.421, 0, 8.633, -0.507, 1, 8.805, -0.507, 8.978, -0.495, 9.15, -0.421, 1, 9.228, -0.388, 9.305, -0.262, 9.383, -0.14, 1, 9.539, 0.104, 9.694, 0.29, 9.85, 0.29, 2, 11.317, 0.29, 1, 11.384, 0.29, 11.45, 0.293, 11.517, 0.22, 1, 11.734, -0.017, 11.95, -0.21, 12.167, -0.21, 0, 12.717, -0.194, 1, 13, -0.194, 13.284, -0.201, 13.567, -0.202, 1, 13.772, -0.203, 13.978, -0.203, 14.183, -0.203, 0, 14.35, -0.21, 1, 14.5, -0.21, 14.65, -0.113, 14.8, 0.1, 1, 14.894, 0.234, 14.989, 0.312, 15.083, 0.312, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamHandLDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 8, 2, 9.25, 8, 2, 9.267, 6, 2, 11.7, 6, 3, 12.167, 5, 2, 14.35, 5, 2, 14.9, 5, 3, 15.4, 10, 2, 15.667, 10]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.933, 0, 2, 1.933, 0, 2, 2.867, 0, 2, 3.833, 0, 2, 4.783, 0, 2, 5.767, 0, 2, 6.7, 0, 2, 7.683, 0, 2, 8.633, 0, 2, 9.15, 0, 2, 9.383, 0, 2, 9.85, 0, 2, 11.317, 0, 2, 11.517, 0, 2, 11.7, 0, 0, 11.717, -1, 0, 12.033, 0.6, 0, 12.167, 0, 2, 14.25, 0, 2, 14.35, 0, 0, 14.9, -1, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.933, 0, 2, 1.933, 0, 2, 2.867, 0, 2, 3.833, 0, 2, 4.783, 0, 2, 5.767, 0, 2, 6.7, 0, 2, 7.683, 0, 2, 8.633, 0, 2, 9.15, 0, 1, 9.228, 0.16, 9.305, 0.32, 9.383, 0.48, 1, 9.411, 0.619, 9.439, 1, 9.467, 1, 0, 9.85, 0, 2, 11.317, 0, 1, 11.384, 0, 11.45, -0.04, 11.517, -0.25, 1, 11.656, -0.688, 11.794, -1, 11.933, -1, 2, 12.167, -1, 2, 14.35, -1, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.933, 0, 2, 1.933, 0, 2, 2.867, 0, 2, 3.833, 0, 2, 4.783, 0, 2, 5.767, 0, 2, 6.7, 0, 2, 7.683, 0, 2, 8.633, 0, 2, 9.15, 0, 0, 9.383, 1, 2, 11.517, 1, 2, 15.067, 1, 0, 15.233, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 5.767, 0, 2, 9.15, 0, 2, 9.383, 0, 2, 11.517, 0, 2, 14.567, 0, 0, 14.817, 1, 2, 15.083, 1, 0, 15.217, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "xiongjiya4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.85, 2, 0.933, 0.85, 2, 1.933, 0.85, 2, 2.867, 0.85, 2, 3.833, 0.85, 2, 4.783, 0.85, 2, 5.767, 0.85, 2, 6.7, 0.85, 2, 7.683, 0.85, 2, 8.633, 0.85, 2, 9.15, 0.85, 0, 9.383, 0, 2, 11.517, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamShoulderRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.548, 0, 0.317, -1, 0, 0.933, -0.548, 0, 1.267, -1, 0, 1.933, -0.548, 0, 2.233, -1, 0, 2.867, -0.548, 0, 3.2, -1, 0, 3.833, -0.548, 0, 4.15, -1, 0, 4.783, -0.548, 0, 5.133, -1, 0, 5.767, -0.548, 0, 6.083, -1, 0, 6.7, -0.548, 0, 7.05, -1, 0, 7.683, -0.548, 0, 8, -1, 0, 8.633, -0.548, 0, 8.967, -1, 1, 9.028, -1, 9.089, -1.027, 9.15, -0.912, 1, 9.228, -0.765, 9.305, 0, 9.383, 0, 2, 9.85, 0, 2, 11.317, 0, 2, 11.517, 0, 2, 12.167, 0, 2, 14.35, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.02, 2, 9.15, -1.02, 1, 9.228, -1.02, 9.305, -1.006, 9.383, -1.039, 1, 9.539, -1.104, 9.694, -1.5, 9.85, -1.5, 2, 11.317, -1.5, 2, 11.517, -1.5, 2, 12.167, -1.5, 2, 14.35, -1.5, 0, 14.817, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -7.9, 2, 0.933, -7.9, 2, 1.933, -7.9, 2, 2.867, -7.9, 2, 3.833, -7.9, 2, 4.783, -7.9, 2, 5.767, -7.9, 2, 6.7, -7.9, 2, 7.683, -7.9, 2, 8.633, -7.9, 2, 9.15, -7.9, 1, 9.228, -5.267, 9.305, -2.633, 9.383, 0, 2, 9.85, 0, 2, 11.317, 0, 2, 11.517, 0, 2, 12.167, 0, 2, 14.35, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.933, 1, 2, 1.933, 1, 2, 2.867, 1, 2, 3.833, 1, 2, 4.783, 1, 2, 5.767, 1, 2, 6.7, 1, 2, 7.683, 1, 2, 8.633, 1, 2, 9.15, 1, 2, 9.383, 1, 2, 11.517, 1, 2, 14.467, 1, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.66, 2, 9.15, -3.66, 1, 9.228, -3.66, 9.305, -0.913, 9.383, -0.405, 1, 9.539, 0.61, 9.694, 0.7, 9.85, 0.7, 2, 10.133, 0.7, 1, 10.283, 0.7, 10.433, 0.453, 10.583, 0.4, 1, 10.828, 0.313, 11.072, 0.296, 11.317, 0.22, 1, 11.384, 0.199, 11.45, -0.842, 11.517, -1.648, 1, 11.578, -2.386, 11.639, -3.285, 11.7, -3.285, 0, 12.167, 2.1, 1, 12.895, 2.1, 13.622, 2.016, 14.35, 1.68, 1, 14.506, 1.608, 14.661, 0, 14.817, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.505, 0, 0.5, -0.398, 0, 0.933, -0.505, 0, 1.45, -0.398, 0, 1.933, -0.505, 0, 2.433, -0.398, 0, 2.867, -0.505, 0, 3.4, -0.398, 0, 3.833, -0.505, 0, 4.333, -0.398, 0, 4.783, -0.505, 0, 5.317, -0.398, 0, 5.767, -0.505, 0, 6.267, -0.398, 0, 6.7, -0.505, 0, 7.25, -0.398, 0, 7.683, -0.505, 0, 8.183, -0.398, 0, 8.633, -0.505, 1, 8.805, -0.505, 8.978, -0.49, 9.15, -0.398, 1, 9.228, -0.357, 9.305, -0.153, 9.383, -0.057, 1, 9.539, 0.135, 9.694, 0.21, 9.85, 0.21, 2, 11.317, 0.21, 1, 11.384, 0.21, 11.45, 0.213, 11.517, 0.146, 1, 11.734, -0.072, 11.95, -0.25, 12.167, -0.25, 2, 14.35, -0.25, 1, 14.356, -0.25, 14.361, -0.173, 14.367, -0.169, 1, 14.517, -0.052, 14.667, 0, 14.817, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamHandRDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 8, 2, 9.25, 8, 2, 9.267, 6, 2, 11.7, 6, 3, 12.167, 5, 2, 14.35, 5, 3, 14.367, 0, 3, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.933, 0, 2, 1.933, 0, 2, 2.867, 0, 2, 3.833, 0, 2, 4.783, 0, 2, 5.767, 0, 2, 6.7, 0, 2, 7.683, 0, 2, 8.633, 0, 2, 9.15, 0, 2, 9.383, 0, 2, 9.85, 0, 2, 11.317, 0, 2, 11.517, 0, 2, 11.7, 0, 0, 11.717, -1, 0, 12.033, 0.5, 0, 12.167, 0, 2, 14.25, 0, 0, 14.417, -1, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.933, 0, 2, 1.933, 0, 2, 2.867, 0, 2, 3.833, 0, 2, 4.783, 0, 2, 5.767, 0, 2, 6.7, 0, 2, 7.683, 0, 2, 8.633, 0, 2, 9.15, 0, 1, 9.228, 0.333, 9.305, 0.667, 9.383, 1, 0, 9.85, 0, 2, 11.317, 0, 1, 11.384, 0, 11.45, -0.04, 11.517, -0.25, 1, 11.656, -0.688, 11.794, -1, 11.933, -1, 0, 12.167, 0, 2, 14.35, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 14.35, 1, 2, 14.367, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.933, 1, 2, 1.933, 1, 2, 2.867, 1, 2, 3.833, 1, 2, 4.783, 1, 2, 5.767, 1, 2, 6.7, 1, 2, 7.683, 1, 2, 8.633, 1, 2, 9.15, 1, 0, 9.383, 0, 2, 9.85, 0, 2, 11.317, 0, 2, 11.517, 0, 2, 12.167, 0, 2, 14.35, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 9.15, 1, 0, 9.383, 0, 2, 11.517, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 14.35, 1, 2, 14.367, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.015, 2, 9.15, 0.015, 0, 11.233, 0.008, 0, 14.367, 0.172, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.3, 2, 9.15, -0.3, 0, 11.233, -0.304, 1, 12.278, -0.304, 13.322, -0.23, 14.367, -0.078, 1, 14.711, -0.028, 15.056, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.615, 0, 9, 0.62, 1, 9.744, 0.62, 10.489, 0.565, 11.233, 0.382, 1, 12.278, 0.125, 13.322, -0.052, 14.367, -0.052, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.321, 2, 0.933, 0.321, 2, 1.933, 0.321, 2, 2.867, 0.321, 2, 3.833, 0.321, 2, 4.783, 0.321, 2, 5.767, 0.321, 2, 6.7, 0.321, 2, 7.683, 0.321, 2, 8.633, 0.321, 2, 9.15, 0.321, 0, 9.383, 0, 2, 11.517, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.479, 0, 0.417, 0.8, 0, 0.933, -0.479, 0, 1.367, 0.8, 0, 1.933, -0.479, 0, 2.35, 0.8, 0, 2.867, -0.479, 0, 3.3, 0.8, 0, 3.833, -0.479, 0, 4.25, 0.8, 0, 4.783, -0.479, 0, 5.233, 0.8, 0, 5.767, -0.479, 0, 6.183, 0.8, 0, 6.7, -0.479, 0, 7.167, 0.8, 0, 7.683, -0.479, 0, 8.1, 0.8, 0, 8.633, -0.479, 0, 9.067, 0.8, 1, 9.095, 0.8, 9.122, 0.829, 9.15, 0.721, 1, 9.228, 0.42, 9.305, 0, 9.383, 0, 2, 11.517, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.933, 1, 2, 1.933, 1, 2, 2.867, 1, 2, 3.833, 1, 2, 4.783, 1, 2, 5.767, 1, 2, 6.7, 1, 2, 7.683, 1, 2, 8.633, 1, 2, 9.15, 1, 0, 9.383, 0, 2, 11.517, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "Param103", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -17.662, 2, 0.933, -17.662, 2, 1.933, -17.662, 2, 2.867, -17.662, 2, 3.833, -17.662, 2, 4.783, -17.662, 2, 5.767, -17.662, 2, 6.7, -17.662, 2, 7.683, -17.662, 2, 8.633, -17.662, 2, 9.15, -17.662, 0, 9.383, 0, 2, 11.517, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -4.226, 0, 0.417, 2.449, 0, 0.933, -4.226, 0, 1.367, 2.449, 0, 1.933, -4.226, 0, 2.35, 2.449, 0, 2.867, -4.226, 0, 3.3, 2.449, 0, 3.833, -4.226, 0, 4.25, 2.449, 0, 4.783, -4.226, 0, 5.233, 2.449, 0, 5.767, -4.226, 0, 6.183, 2.449, 0, 6.7, -4.226, 0, 7.167, 2.449, 0, 7.683, -4.226, 0, 8.1, 2.449, 0, 8.633, -4.226, 0, 9.067, 2.449, 1, 9.095, 2.449, 9.122, 2.638, 9.15, 2.036, 1, 9.228, 0.352, 9.305, -4.517, 9.383, -7.055, 1, 9.411, -7.961, 9.439, -8, 9.467, -8, 2, 10.133, -8, 0, 10.767, -6, 0, 11.15, -20.733, 1, 11.272, -20.733, 11.395, -8.709, 11.517, 7.588, 1, 11.561, 13.514, 11.606, 14.174, 11.65, 14.174, 1, 11.75, 14.174, 11.85, -9.592, 11.95, -19.801, 1, 12.022, -27.174, 12.095, -26.593, 12.167, -26.593, 0, 12.533, 1.177, 0, 13.017, -12.086, 0, 13.5, -0.95, 0, 14.05, -15, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.333, -5.52, 0, 3.55, 4.58, 0, 6.483, -2.315, 1, 7.2, -2.315, 7.916, -2.037, 8.633, 0, 1, 8.883, 0.711, 9.133, 14.119, 9.383, 14.119, 0, 11.05, -17, 1, 11.206, -17, 11.361, -16.655, 11.517, -11.848, 1, 11.889, -0.347, 12.261, 9, 12.633, 9, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.009, 0, 0.417, 2, 0, 0.933, -2.009, 0, 1.367, 2, 0, 1.933, -2.009, 0, 2.35, 2, 0, 2.867, -2.009, 0, 3.3, 2, 0, 3.833, -2.009, 0, 4.25, 2, 0, 4.783, -2.009, 0, 5.233, 2, 0, 5.767, -2.009, 0, 6.183, 2, 0, 6.7, -2.009, 0, 7.167, 2, 0, 7.683, -2.009, 0, 8.1, 2, 0, 8.633, -2.009, 0, 9.067, 2, 1, 9.095, 2, 9.122, 2.231, 9.15, 1.752, 1, 9.228, 0.411, 9.305, -5.578, 9.383, -5.578, 1, 9.616, -5.578, 9.85, -2.832, 10.083, 0, 1, 10.561, 5.799, 11.039, 7.795, 11.517, 7.795, 1, 11.945, 7.795, 12.372, 3.079, 12.8, 0, 1, 13.667, -6.238, 14.533, -7.795, 15.4, -7.795, 2, 15.667, -7.795]}, {"Target": "Parameter", "Id": "ParamBodyStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.483, 0, 0.317, 2.866, 0, 0.767, -5.233, 0, 1.267, 2.866, 0, 1.75, -5.233, 0, 2.233, 2.866, 0, 2.7, -5.233, 0, 3.2, 2.866, 0, 3.667, -5.233, 0, 4.15, 2.866, 0, 4.617, -5.233, 0, 5.133, 2.866, 0, 5.6, -5.233, 0, 6.083, 2.866, 0, 6.533, -5.233, 0, 7.05, 2.866, 0, 7.517, -5.233, 0, 8, 2.866, 0, 8.467, -5.233, 0, 8.967, 2.866, 1, 9.028, 2.866, 9.089, 1.289, 9.15, 0.253, 1, 9.228, -1.066, 9.305, -1.746, 9.383, -2.763, 1, 9.394, -2.908, 9.406, -3, 9.417, -3, 0, 9.667, 0, 0, 9.967, -1.92, 0, 10.517, 2.409, 0, 11.017, 0, 0, 11.35, 10, 1, 11.406, 10, 11.461, 9.967, 11.517, 9.508, 1, 11.561, 9.14, 11.606, 9.053, 11.65, 7.281, 1, 11.778, 2.186, 11.905, -10, 12.033, -10, 0, 12.3, -3, 0, 12.717, -9, 0, 13.083, -4.543, 0, 13.467, -7, 0, 14.067, 0.25, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.8, 0, 2, 9, 0, 0, 9.383, -4.129, 0, 9.817, 10, 0, 13.017, -7, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamLegLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.933, 0, 2, 1.933, 0, 2, 2.867, 0, 2, 3.833, 0, 2, 4.783, 0, 2, 5.767, 0, 2, 6.7, 0, 2, 7.683, 0, 2, 8.633, 0, 2, 9.15, 0, 1, 9.228, 0, 9.305, 3.321, 9.383, 4.593, 1, 9.828, 11.864, 10.272, 14.515, 10.717, 14.515, 1, 10.984, 14.515, 11.25, 4.699, 11.517, -7.783, 1, 11.567, -10.123, 11.617, -9.742, 11.667, -9.742, 0, 12.533, 0, 2, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamLegRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.933, 0, 2, 1.933, 0, 2, 2.867, 0, 2, 3.833, 0, 2, 4.783, 0, 2, 5.767, 0, 2, 6.7, 0, 2, 7.683, 0, 2, 8.633, 0, 2, 9.15, 0, 1, 9.228, 0, 9.305, 5.192, 9.383, 6.321, 1, 9.483, 7.773, 9.583, 7.732, 9.683, 7.732, 1, 10.294, 7.732, 10.906, 0.412, 11.517, -9.174, 1, 11.578, -10.133, 11.639, -9.756, 11.7, -9.756, 1, 11.978, -9.756, 12.255, -4.653, 12.533, 0, 1, 12.905, 6.235, 13.278, 7.732, 13.65, 7.732, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.187, 2, 0.933, 0.187, 2, 1.933, 0.187, 2, 2.867, 0.187, 2, 3.833, 0.187, 2, 4.783, 0.187, 2, 5.767, 0.187, 2, 6.7, 0.187, 2, 7.683, 0.187, 2, 8.633, 0.187, 2, 9.15, 0.187, 0, 9.383, 1, 2, 11.517, 1, 2, 14.367, 1, 2, 14.8, 1, 1, 14.806, 1, 14.811, 0.51, 14.817, 0.5, 1, 15.011, 0.157, 15.206, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "Physics_AngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.434, 0, 0.45, 1.903, 0, 0.967, -2.78, 0, 1.417, 1.76, 0, 1.95, -2.746, 0, 2.383, 1.99, 0, 2.9, -2.781, 0, 3.35, 1.756, 0, 3.85, -2.799, 0, 4.283, 1.991, 0, 4.8, -2.803, 0, 5.267, 1.941, 0, 5.783, -2.791, 0, 6.217, 1.838, 0, 6.733, -2.784, 0, 7.2, 1.744, 0, 7.717, -2.768, 0, 8.133, 1.839, 0, 8.65, -2.804, 0, 9.117, 1.772, 0, 9.533, -5.439, 0, 9.917, -4.566, 0, 10.15, -4.602, 0, 10.767, -3.381, 0, 11.183, -12.863, 0, 11.683, 10.321, 0, 12.133, -17.83, 0, 12.583, 2.666, 0, 13.05, -7.78, 0, 13.5, 0, 2, 13.6, 0, 0, 14.083, -9.371, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.994, 0, 0.283, -5.773, 0, 0.667, 10.187, 0, 1.117, -12.869, 0, 1.55, 14.168, 0, 2.05, -12.626, 0, 2.517, 14.024, 0, 3, -13.917, 0, 3.467, 14.481, 0, 3.95, -13.669, 0, 4.417, 14.536, 0, 4.9, -13.08, 0, 5.383, 13.27, 0, 5.883, -12.182, 0, 6.35, 13.839, 0, 6.833, -13.289, 0, 7.317, 13.307, 0, 7.817, -12.781, 0, 8.267, 14.178, 0, 8.767, -13.243, 0, 9.25, 15.929, 0, 9.633, -13.987, 0, 10.05, 8.894, 0, 10.467, -6.216, 0, 11, 9.114, 0, 11.433, -14.735, 0, 11.867, 25.772, 0, 12.3, -26.131, 0, 12.717, 24.949, 0, 13.15, -19.19, 0, 13.6, 14.841, 0, 14.067, -9.971, 0, 14.483, 5.53, 0, 14.9, -3.925, 0, 15.333, 2.792, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.55, 0, 0.15, 3.863, 0, 0.483, -17.329, 0, 0.883, 25.22, 0, 1.317, -30, 2, 1.367, -30, 0, 1.733, 30, 2, 1.817, 30, 0, 2.283, -28.151, 0, 2.717, 30, 2, 2.783, 30, 0, 3.2, -30, 2, 3.267, -30, 0, 3.65, 30, 2, 3.75, 30, 0, 4.15, -30, 2, 4.2, -30, 0, 4.6, 30, 2, 4.683, 30, 0, 5.133, -28.685, 0, 5.6, 28.758, 0, 6.117, -27.372, 0, 6.55, 30, 2, 6.6, 30, 0, 7.067, -29.361, 0, 7.533, 29.001, 0, 8.05, -28.858, 0, 8.467, 30, 2, 8.533, 30, 0, 8.983, -29.202, 0, 9.383, 30, 2, 9.533, 30, 0, 9.85, -29.609, 0, 10.267, 19.978, 0, 10.667, -13.678, 0, 10.917, -2.093, 0, 10.933, -2.098, 0, 11.233, 21.54, 0, 11.567, -30, 2, 11.75, -30, 0, 11.933, 30, 2, 12.2, 30, 0, 12.367, -30, 2, 12.65, -30, 0, 12.817, 30, 2, 13.067, 30, 0, 13.267, -30, 2, 13.467, -30, 0, 13.783, 30, 2, 13.85, 30, 0, 14.267, -19.901, 0, 14.667, 10.555, 0, 15.1, -9.285, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_L", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.986, 0, 0.417, 0.571, 0, 0.933, -0.986, 0, 1.367, 0.571, 0, 1.933, -0.986, 0, 2.35, 0.571, 0, 2.867, -0.986, 0, 3.3, 0.571, 0, 3.833, -0.986, 0, 4.25, 0.571, 0, 4.783, -0.986, 0, 5.233, 0.571, 0, 5.767, -0.986, 0, 6.183, 0.571, 0, 6.7, -0.986, 0, 7.167, 0.571, 0, 7.683, -0.986, 0, 8.1, 0.571, 0, 8.633, -0.986, 0, 9.1, 0.578, 0, 9.467, -1.867, 2, 10.133, -1.867, 0, 10.767, -1.4, 0, 11, -3.475, 0, 11.267, 6.904, 0, 11.717, -10.563, 0, 12.117, 14.815, 0, 12.583, -14.772, 0, 13.017, 11.947, 0, 13.483, -9.364, 0, 13.95, 7.994, 0, 14.417, -5.52, 0, 14.867, 2.893, 0, 15.367, -2.689, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_R", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.986, 0, 0.417, -0.571, 0, 0.933, 0.986, 0, 1.367, -0.571, 0, 1.933, 0.986, 0, 2.35, -0.571, 0, 2.867, 0.986, 0, 3.3, -0.571, 0, 3.833, 0.986, 0, 4.25, -0.571, 0, 4.783, 0.986, 0, 5.233, -0.571, 0, 5.767, 0.986, 0, 6.183, -0.571, 0, 6.7, 0.986, 0, 7.167, -0.571, 0, 7.683, 0.986, 0, 8.1, -0.571, 0, 8.633, 0.986, 0, 9.1, -0.578, 0, 9.467, 1.867, 2, 10.133, 1.867, 0, 10.767, 1.4, 0, 11, 3.475, 0, 11.267, -6.904, 0, 11.717, 10.563, 0, 12.117, -14.815, 0, 12.583, 14.772, 0, 13.017, -11.947, 0, 13.483, 9.364, 0, 13.95, -7.994, 0, 14.417, 5.52, 0, 14.867, -2.893, 0, 15.367, 2.689, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.635, 0, 0.417, -3.265, 0, 0.933, 5.634, 0, 1.367, -3.265, 0, 1.933, 5.635, 0, 2.35, -3.265, 0, 2.867, 5.635, 0, 3.3, -3.265, 0, 3.833, 5.634, 0, 4.25, -3.265, 0, 4.783, 5.635, 0, 5.233, -3.265, 0, 5.767, 5.635, 0, 6.183, -3.265, 0, 6.7, 5.635, 0, 7.167, -3.265, 0, 7.683, 5.635, 0, 8.1, -3.265, 0, 8.633, 5.635, 0, 9.033, -3.114, 0, 9.383, 28.433, 0, 10.783, -19.037, 0, 11.017, -12.857, 0, 11.35, -30, 2, 11.517, -30, 0, 11.9, 28.171, 0, 12.35, -8.806, 0, 12.733, 23.151, 0, 13.233, 2.133, 0, 13.717, 12.752, 0, 14.25, 0.909, 0, 14.417, 1.228, 0, 14.983, -1.042, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.635, 0, 0.417, 3.265, 0, 0.933, -5.634, 0, 1.367, 3.265, 0, 1.933, -5.635, 0, 2.35, 3.265, 0, 2.867, -5.635, 0, 3.3, 3.265, 0, 3.833, -5.634, 0, 4.25, 3.265, 0, 4.783, -5.635, 0, 5.233, 3.265, 0, 5.767, -5.635, 0, 6.183, 3.265, 0, 6.7, -5.635, 0, 7.167, 3.265, 0, 7.683, -5.635, 0, 8.1, 3.265, 0, 8.633, -5.635, 0, 9.033, 3.114, 0, 9.217, -15.157, 0, 9.5, 12.102, 0, 9.783, -2.079, 0, 10.083, 5.067, 0, 10.933, -2.287, 0, 11.217, 5.684, 0, 11.683, -15.569, 0, 12.033, 13.389, 0, 12.267, -6.065, 0, 12.4, -4.37, 0, 12.567, -8.946, 0, 12.833, 7.652, 0, 13.317, -1.565, 0, 13.35, -1.515, 0, 13.367, -1.531, 0, 13.867, 3.653, 0, 14.017, 0.242, 0, 14.117, 0.737, 0, 14.317, -0.354, 0, 14.6, 0.995, 0, 14.933, 0.069, 2, 14.95, 0.069, 0, 14.967, 0.066, 0, 15.033, 0.084, 2, 15.05, 0.084, 0, 15.067, 0.087, 0, 15.083, 0.083, 2, 15.1, 0.083, 0, 15.383, -1.579, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.635, 0, 0.417, 3.265, 0, 0.933, -5.634, 0, 1.367, 3.265, 0, 1.933, -5.635, 0, 2.35, 3.265, 0, 2.867, -5.635, 0, 3.3, 3.265, 0, 3.833, -5.634, 0, 4.25, 3.265, 0, 4.783, -5.635, 0, 5.233, 3.265, 0, 5.767, -5.635, 0, 6.183, 3.265, 0, 6.7, -5.635, 0, 7.167, 3.265, 0, 7.683, -5.635, 0, 8.1, 3.265, 0, 8.633, -5.635, 0, 9.1, 3.301, 0, 9.367, -7.474, 0, 9.717, 16.668, 0, 10.967, -30, 2, 11.1, -30, 0, 11.55, 12.036, 0, 11.917, -29.725, 0, 12.317, 30, 2, 12.517, 30, 0, 12.85, -4.124, 0, 13.3, 18.232, 0, 13.833, -3.385, 0, 14.283, 7.001, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.635, 0, 0.417, -3.265, 0, 0.933, 5.634, 0, 1.367, -3.265, 0, 1.933, 5.635, 0, 2.35, -3.265, 0, 2.867, 5.635, 0, 3.3, -3.265, 0, 3.833, 5.634, 0, 4.25, -3.265, 0, 4.783, 5.635, 0, 5.233, -3.265, 0, 5.767, 5.635, 0, 6.183, -3.265, 0, 6.7, 5.635, 0, 7.167, -3.265, 0, 7.683, 5.635, 0, 8.1, -3.265, 0, 8.633, 5.635, 0, 9.1, -3.301, 0, 9.333, 6.863, 0, 9.533, -17.393, 0, 9.883, 12.764, 0, 10.233, 2.987, 0, 10.45, 6.979, 0, 10.767, 2.389, 0, 10.95, 5.846, 0, 11.283, -13.548, 0, 11.783, 12.818, 0, 12.1, -26.453, 0, 12.6, 18.08, 0, 12.983, -10.311, 0, 13.483, 6.276, 0, 14.05, -3.437, 0, 14.383, 2.972, 0, 14.75, 0.062, 0, 15.083, 0.711, 1, 15.161, 0.711, 15.239, 0.683, 15.317, 0.509, 1, 15.345, 0.447, 15.372, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "Jewelry_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.079, 0, 0.283, -0.338, 0, 0.667, 0.583, 0, 1.117, -0.738, 0, 1.55, 0.811, 0, 2.05, -0.724, 0, 2.517, 0.803, 0, 3, -0.779, 0, 3.467, 0.821, 0, 3.95, -0.764, 0, 4.417, 0.825, 0, 4.9, -0.749, 0, 5.383, 0.78, 0, 5.883, -0.704, 0, 6.35, 0.793, 0, 6.833, -0.739, 0, 7.317, 0.754, 0, 7.817, -0.732, 0, 8.267, 0.813, 0, 8.767, -0.76, 0, 9.283, 1.012, 0, 9.667, -1.01, 0, 10.083, 0.642, 0, 10.483, -0.463, 0, 11, 0.969, 0, 11.433, -2.1, 0, 11.867, 4.024, 0, 12.3, -4.149, 0, 12.717, 3.947, 0, 13.167, -3.034, 0, 13.617, 2.307, 0, 14.067, -1.585, 0, 14.5, 0.871, 0, 14.9, -0.621, 0, 15.333, 0.438, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "Jewelry_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.034, 0, 0.15, 0.266, 0, 0.483, -0.998, 0, 0.883, 1.443, 0, 1.35, -1.753, 0, 1.783, 1.804, 0, 2.283, -1.612, 0, 2.75, 1.769, 0, 3.233, -1.746, 0, 3.683, 1.817, 0, 4.183, -1.71, 0, 4.633, 1.806, 0, 5.15, -1.675, 0, 5.6, 1.694, 0, 6.117, -1.572, 0, 6.567, 1.698, 0, 7.067, -1.629, 0, 7.533, 1.646, 0, 8.05, -1.655, 0, 8.5, 1.795, 0, 8.983, -1.667, 0, 9.5, 2.551, 0, 9.867, -2.115, 0, 10.283, 1.397, 0, 10.7, -1.039, 0, 11.217, 2.685, 0, 11.65, -5.626, 0, 12.05, 8.584, 0, 12.5, -8.101, 0, 12.933, 7.32, 0, 13.367, -5.857, 0, 13.817, 4.555, 0, 14.283, -3.027, 0, 14.7, 1.679, 0, 15.1, -1.491, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "Jewelry_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.126, 0, 0.317, 0.474, 0, 0.667, -1, 0, 1.05, 1.241, 0, 1.5, -1.402, 0, 1.917, 1.378, 0, 2.433, -1.078, 0, 2.9, 1.282, 0, 3.383, -1.246, 0, 3.833, 1.315, 0, 4.333, -1.189, 0, 4.783, 1.294, 0, 5.283, -1.124, 0, 5.75, 1.193, 0, 6.283, -1.059, 0, 6.717, 1.254, 0, 7.217, -1.113, 0, 7.683, 1.148, 0, 8.217, -1.166, 0, 8.65, 1.324, 0, 9.133, -1.162, 0, 9.65, 1.923, 0, 10.017, -1.96, 0, 10.4, 1.413, 0, 10.817, -0.954, 0, 11.4, 2.609, 0, 11.8, -4.702, 0, 12.183, 6.65, 0, 12.617, -5.817, 0, 13.05, 5.552, 0, 13.483, -4.422, 0, 13.933, 3.231, 0, 14.4, -2.314, 0, 14.817, 1.749, 0, 15.217, -1.249, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.054, 0, 0.25, -0.463, 0, 0.6, 1.022, 0, 1.017, -1.376, 0, 1.467, 1.544, 0, 1.917, -1.569, 0, 2.4, 1.582, 0, 2.867, -1.576, 0, 3.35, 1.522, 0, 3.833, -1.532, 0, 4.317, 1.55, 0, 4.783, -1.536, 0, 5.283, 1.433, 0, 5.767, -1.455, 0, 6.25, 1.519, 0, 6.717, -1.569, 0, 7.2, 1.479, 0, 7.683, -1.491, 0, 8.167, 1.523, 0, 8.633, -1.536, 0, 9.117, 1.498, 0, 9.583, -1.445, 0, 9.95, 1.154, 0, 10.333, -0.922, 2, 10.35, -0.922, 0, 10.75, 0.814, 0, 11.25, -1.521, 0, 11.767, 2.014, 0, 12.217, -2.966, 0, 12.617, 2.524, 0, 13.017, -1.942, 0, 13.417, 1.315, 0, 13.833, -1.016, 0, 14.233, 0.763, 0, 14.65, -0.51, 0, 15.067, 0.318, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.011, 0, 0.15, 0.273, 0, 0.433, -1.387, 0, 0.8, 2.614, 0, 1.217, -3.05, 0, 1.667, 3.183, 0, 2.133, -3.137, 0, 2.617, 3.171, 0, 3.083, -3.116, 0, 3.567, 3.044, 0, 4.05, -3.059, 0, 4.533, 3.117, 0, 5, -3.033, 0, 5.5, 2.878, 0, 5.983, -2.932, 0, 6.467, 3.106, 0, 6.933, -3.117, 0, 7.417, 2.96, 0, 7.9, -2.989, 0, 8.383, 3.08, 0, 8.85, -3.058, 0, 9.317, 2.947, 0, 9.767, -2.965, 0, 10.15, 2.427, 0, 10.55, -2.135, 0, 10.967, 1.745, 0, 11.433, -3.406, 0, 11.983, 4.245, 2, 12, 4.245, 0, 12.4, -5.221, 0, 12.817, 4.545, 0, 13.217, -3.678, 0, 13.617, 2.631, 0, 14.033, -2.252, 0, 14.433, 1.556, 0, 14.85, -1.108, 0, 15.267, 0.634, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.236, 0, 0.283, -1.015, 0, 0.667, 1.748, 0, 1.117, -2.213, 0, 1.55, 2.434, 0, 2.05, -2.172, 0, 2.517, 2.409, 0, 3, -2.338, 0, 3.467, 2.464, 0, 3.95, -2.293, 0, 4.417, 2.476, 0, 4.9, -2.247, 0, 5.383, 2.339, 0, 5.883, -2.112, 0, 6.35, 2.379, 0, 6.833, -2.216, 0, 7.317, 2.263, 0, 7.817, -2.197, 0, 8.267, 2.438, 0, 8.767, -2.279, 0, 9.283, 3.036, 0, 9.667, -3.031, 0, 10.083, 1.928, 0, 10.483, -1.39, 0, 11, 2.907, 0, 11.433, -6.3, 0, 11.867, 12.072, 0, 12.3, -12.448, 0, 12.717, 11.842, 0, 13.167, -9.101, 0, 13.617, 6.921, 0, 14.067, -4.756, 0, 14.5, 2.613, 0, 14.9, -1.861, 0, 15.333, 1.313, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.103, 0, 0.15, 0.798, 0, 0.483, -2.993, 0, 0.883, 4.328, 0, 1.35, -5.259, 0, 1.783, 5.413, 0, 2.283, -4.835, 0, 2.75, 5.307, 0, 3.233, -5.238, 0, 3.683, 5.45, 0, 4.183, -5.129, 0, 4.633, 5.417, 0, 5.15, -5.025, 0, 5.6, 5.081, 0, 6.117, -4.715, 0, 6.567, 5.094, 0, 7.067, -4.887, 0, 7.533, 4.938, 0, 8.05, -4.965, 0, 8.5, 5.386, 0, 8.983, -5.002, 0, 9.5, 7.653, 0, 9.867, -6.346, 0, 10.283, 4.192, 0, 10.7, -3.116, 0, 11.217, 8.056, 0, 11.65, -16.878, 0, 12.05, 25.752, 0, 12.5, -24.302, 0, 12.933, 21.959, 0, 13.367, -17.571, 0, 13.817, 13.665, 0, 14.283, -9.081, 0, 14.7, 5.037, 0, 15.1, -4.473, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.377, 0, 0.317, 1.421, 0, 0.667, -2.999, 0, 1.05, 3.723, 0, 1.5, -4.205, 0, 1.917, 4.135, 0, 2.433, -3.234, 0, 2.9, 3.845, 0, 3.383, -3.737, 0, 3.833, 3.945, 0, 4.333, -3.567, 0, 4.783, 3.883, 0, 5.283, -3.372, 0, 5.75, 3.578, 0, 6.283, -3.176, 0, 6.717, 3.763, 0, 7.217, -3.34, 0, 7.683, 3.444, 0, 8.217, -3.498, 0, 8.65, 3.972, 0, 9.133, -3.487, 0, 9.65, 5.77, 0, 10.017, -5.88, 0, 10.4, 4.238, 0, 10.817, -2.862, 0, 11.4, 7.826, 0, 11.8, -14.106, 0, 12.183, 19.949, 0, 12.617, -17.45, 0, 13.05, 16.655, 0, 13.483, -13.265, 0, 13.933, 9.693, 0, 14.4, -6.944, 0, 14.817, 5.248, 0, 15.217, -3.748, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.154, 0, 0.4, 1.649, 0, 0.767, -3.65, 0, 1.15, 4.995, 0, 1.567, -5.456, 0, 2, 5.564, 0, 2.467, -4.109, 0, 2.983, 4.8, 0, 3.45, -4.742, 0, 3.917, 5.066, 0, 4.4, -4.484, 0, 4.867, 4.996, 0, 5.35, -4.322, 0, 5.833, 4.559, 0, 6.333, -3.851, 0, 6.817, 4.787, 0, 7.283, -4.284, 0, 7.767, 4.373, 0, 8.283, -4.365, 0, 8.733, 5.137, 0, 9.2, -4.524, 0, 9.75, 6.526, 0, 10.117, -7.938, 0, 10.483, 6.448, 0, 10.883, -4.439, 0, 11.483, 8.578, 0, 11.883, -16.441, 0, 12.267, 23.755, 0, 12.683, -21.234, 0, 13.133, 20.151, 0, 13.567, -17.003, 0, 14.017, 12.751, 0, 14.467, -9.202, 0, 14.9, 7.342, 0, 15.3, -5.514, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.311, 0, 0.017, -2.343, 0, 0.467, 2.082, 0, 0.867, -4.387, 0, 1.25, 6.524, 0, 1.65, -7.227, 0, 2.083, 7.347, 0, 2.517, -5.662, 0, 3.067, 5.743, 0, 3.517, -6.085, 0, 4, 6.38, 0, 4.467, -5.822, 0, 4.95, 6.194, 0, 5.417, -5.683, 0, 5.917, 5.639, 0, 6.383, -4.969, 0, 6.9, 5.82, 0, 7.35, -5.637, 0, 7.85, 5.442, 0, 8.35, -4.952, 0, 8.817, 6.417, 0, 9.267, -6.003, 0, 9.833, 7.268, 0, 10.217, -10.156, 0, 10.583, 9.342, 0, 10.967, -6.862, 0, 11.567, 8.993, 0, 11.95, -18.726, 0, 12.317, 26.878, 0, 12.733, -24.985, 0, 13.183, 22.923, 0, 13.633, -20.485, 0, 14.083, 16.06, 0, 14.533, -11.892, 0, 14.983, 9.847, 0, 15.383, -7.889, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.587, 0, 0.117, -3.734, 0, 0.517, 2.998, 0, 0.967, -5.255, 0, 1.333, 8.334, 0, 1.733, -9.591, 0, 2.167, 9.629, 0, 2.6, -7.847, 0, 3.133, 6.681, 0, 3.6, -7.712, 0, 4.067, 7.952, 0, 4.533, -7.569, 0, 5.033, 7.542, 0, 5.483, -7.445, 0, 5.983, 6.895, 0, 6.45, -6.467, 0, 6.983, 6.948, 0, 7.433, -7.34, 0, 7.917, 6.755, 0, 8.4, -6.455, 0, 8.9, 7.745, 0, 9.333, -7.905, 0, 9.9, 8.031, 0, 10.317, -12.43, 0, 10.683, 12.844, 0, 11.067, -10.314, 0, 11.633, 9.037, 0, 12, -20.678, 0, 12.367, 29.189, 0, 12.783, -27.796, 0, 13.233, 24.536, 0, 13.683, -23.004, 0, 14.133, 18.998, 0, 14.6, -14.724, 0, 15.067, 12.635, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 3.148, 0, 0.233, -5.658, 0, 0.6, 4.604, 0, 1.05, -6.394, 0, 1.433, 10.438, 0, 1.817, -12.568, 0, 2.25, 12.537, 0, 2.667, -10.612, 0, 3.183, 7.801, 0, 3.683, -9.461, 0, 4.15, 9.897, 0, 4.6, -9.652, 0, 5.1, 9.117, 0, 5.567, -9.41, 0, 6.05, 8.458, 0, 6.517, -8.172, 0, 7.05, 8.12, 0, 7.5, -9.304, 0, 7.983, 8.457, 0, 8.467, -8.123, 0, 8.983, 9.144, 0, 9.417, -10.167, 0, 9.95, 9.022, 0, 10.4, -14.68, 0, 10.767, 16.744, 0, 11.15, -14.701, 0, 11.617, 9.684, 0, 12.05, -21.921, 0, 12.4, 30, 2, 12.417, 30, 0, 12.817, -29.043, 0, 13.283, 24.98, 0, 13.733, -24.138, 0, 14.2, 21.072, 0, 14.667, -17.238, 0, 15.133, 15.509, 0, 15.4, 0, 2, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.167, 1, 0, 3, 0, 0, 4.167, 1, 0, 6, 0, 0, 7.167, 1, 0, 9, 0, 0, 10.167, 1, 0, 12, 0, 0, 13.167, 1, 0, 15, 0, 1, 15.222, 0, 15.445, 0.327, 15.667, 0.606]}, {"Target": "Parameter", "Id": "fenshangxuanzhuang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 5.222, 2.091, 10.445, 4.181, 15.667, 6.272]}, {"Target": "Parameter", "Id": "ParamSad2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.667, 1]}, {"Target": "Parameter", "Id": "ParamCRY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.2, 0, 15.667, -5.2]}, {"Target": "Parameter", "Id": "Position_Coordinates", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.667, 1]}, {"Target": "Parameter", "Id": "Man_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.667, 1]}, {"Target": "Parameter", "Id": "Man_TMD2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "BG_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.667, 1]}, {"Target": "Parameter", "Id": "FG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "FG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "BG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "BG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "BG_White5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "BG_White2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "All_Size3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "All_Size4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "tuerzuo3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "faqiehuan", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "faqiehuan2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "faqiehuan4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "faqiehuan3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "tuerzuo4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Mail_Change", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "dafengshangzhuanchang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "tongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamTongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "EyeFrame_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamBrowY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamSigh", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamDarken", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamCry", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_B", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_C", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "MB_Unidirectional_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "jing<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamArmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.667, 1]}, {"Target": "Parameter", "Id": "ParamForearmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamArmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamArmL_ForearmL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmL_HandL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamHandLSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "xiongjiya", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "xiongjiya5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "xiongjiya3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "xiongjiya2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamArmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.667, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamArmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamArmR_ForearmR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamForearm<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.667, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_HandR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamHandRSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 15.667, 0.6]}, {"Target": "Parameter", "Id": "Change_idle_physics25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "taiqiugundong", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -22.5, 0, 15.667, -22.5]}, {"Target": "Parameter", "Id": "Change_idle_physics32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamLegL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.667, 1]}, {"Target": "Parameter", "Id": "ParamLegLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamCrusLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamCrusLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>oodLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamFoodLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamFoodLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamLegR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.667, 1]}, {"Target": "Parameter", "Id": "ParamLegRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamCrusRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamCrusRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamCrus<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>ood<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamFoodRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamFoodRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Physics_Provisional", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "physics_Add_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "physics_Add_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Physics_AngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Physics_AngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Physics_Body_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Physics_Body_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Physics_breast", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamBreath2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "touch_drag1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "touch_drag2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "touch_drag3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "touch_drag4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "touch_drag5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "touch_drag6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "touch_drag7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "touch_drag8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "touch_drag9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>pian<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh654", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh655", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh657", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh656", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh658", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh659", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh660", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.667, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 15.167, "Value": ""}]}