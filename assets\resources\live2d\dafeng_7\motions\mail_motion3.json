{"Version": 3, "Meta": {"Duration": 13.0, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 303, "TotalSegmentCount": 2063, "TotalPointCount": 2183, "UserDataCount": 2, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Mail_Transparency", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.633, 0, 0, 0.833, 1, 2, 1.067, 1, 2, 5.167, 1, 2, 8.383, 1, 2, 9.233, 1, 2, 10.65, 1, 2, 11, 1, 0, 11.283, 0, 2, 11.833, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Mail_Change", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.717, 1, 0, 1.133, -0.7, 0, 1.35, 0.212, 0, 1.483, 0, 2, 10.8, 0, 0, 10.983, -1, 0, 11.55, 1, 0, 11.8, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.867, 1, 0, 1.167, -0.7, 1, 1.4, -0.7, 1.634, 0.593, 1.867, 0.6, 1, 4.234, 0.673, 6.6, 0.7, 8.967, 0.7, 2, 9.017, 0.7, 0, 9.717, -0.8, 2, 9.817, -0.8, 0, 10.267, 1, 2, 10.383, 1, 0, 11, 0, 0, 11.3, 1, 0, 11.65, -0.5, 0, 12, 0.9, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY_SD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.75, 0, 0, 0.867, 1, 0, 0.983, 0, 0, 1.167, 0.4, 0, 1.283, 0, 0, 1.517, 1, 0, 1.683, 0, 0, 1.867, 0.6, 0, 1.983, 0, 0, 2.167, 1, 0, 2.333, 0, 2, 2.75, 0, 0, 2.867, 1, 0, 2.983, 0, 0, 3.167, 1, 0, 3.283, 0, 0, 3.517, 1, 0, 3.683, 0, 0, 3.867, 1, 0, 3.983, 0, 0, 4.167, 1, 0, 4.283, 0, 0, 4.467, 1, 0, 4.6, 0, 0, 4.75, 1, 0, 5.033, 0, 2, 5.633, 0, 0, 5.75, 1, 0, 5.867, 0, 0, 6.05, 1, 0, 6.167, 0, 0, 6.4, 1, 0, 6.567, 0, 0, 6.75, 1, 0, 6.867, 0, 0, 7.05, 1, 0, 7.383, 0, 2, 7.633, 0, 0, 7.8, 1, 0, 8.1, 0, 0, 8.317, 1, 0, 8.583, 0, 2, 8.833, 0, 0, 8.967, 0.4, 2, 9.017, 0.4, 0, 9.217, 0, 2, 9.617, 0, 0, 9.717, 0.6, 2, 9.817, 0.6, 0, 10.167, 0, 0, 10.267, 0.8, 2, 10.383, 0.8, 0, 10.883, 0, 0, 11, 1, 0, 11.117, 0, 0, 11.3, 0.7, 0, 11.417, 0, 0, 11.65, 0.6, 0, 11.817, 0, 0, 12, 0.5, 0, 12.117, 0, 0, 12.3, 1, 0, 12.583, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 0.683, 1, 0, 0.817, 0, 0, 1, 1.3, 1, 1.028, 1.3, 1.055, 1.007, 1.083, 1, 1, 1.355, 0.927, 1.628, 0.9, 1.9, 0.9, 0, 2.467, 1, 0, 2.6, 0, 0, 2.783, 1.3, 0, 2.867, 1, 2, 5.167, 1, 0, 6, 0, 2, 8.25, 0, 0, 8.5, 1, 2, 9.433, 1, 0, 9.7, 0, 2, 10.467, 0, 0, 10.883, 1.127, 0, 11.067, 1, 2, 11.583, 1, 0, 11.717, 0, 0, 11.9, 1.3, 0, 11.983, 1, 2, 12.75, 1, 2, 13, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 7.317, 0, 2, 8.667, 0, 0, 9.567, 1, 2, 10.467, 1, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 0.683, 1, 0, 0.817, 0, 0, 1, 1.3, 1, 1.028, 1.3, 1.055, 1.007, 1.083, 1, 1, 1.355, 0.927, 1.628, 0.9, 1.9, 0.9, 0, 2.467, 1, 0, 2.6, 0, 0, 2.783, 1.3, 0, 2.867, 1, 2, 5.167, 1, 0, 6, 0, 2, 8.25, 0, 0, 8.5, 1, 2, 9.433, 1, 0, 9.7, 0, 2, 10.467, 0, 0, 10.883, 1.127, 0, 11.067, 1, 2, 11.583, 1, 0, 11.717, 0, 0, 11.9, 1.3, 0, 11.983, 1, 2, 12.75, 1, 2, 13, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 7.317, 0, 2, 8.667, 0, 0, 9.567, 1, 2, 10.467, 1, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.9, -1, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamBrowForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.583, -0.8, 2, 2.667, -0.8, 0, 3.283, 0, 2, 5.1, 0, 0, 6.533, -0.6, 2, 8.517, -0.6, 0, 9.75, 0, 2, 10.233, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamBrowAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 5.1, 0, 0, 6.533, 0.6, 2, 8.517, 0.6, 0, 9.75, 1, 2, 10.233, 1, 0, 11.317, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamEyeLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.683, 0, 0, 0.75, 1, 2, 0.9, 1, 0, 0.917, -1, 2, 1.05, -1, 0, 1.35, 0.278, 0, 1.667, -0.066, 0, 1.917, 0.011, 0, 2.2, -0.069, 0, 2.533, 1, 2, 2.683, 1, 0, 2.7, -1, 2, 2.817, -1, 0, 3.1, 0.13, 0, 3.417, -0.036, 0, 3.717, 0.01, 2, 3.733, 0.01, 0, 4.033, -0.003, 0, 4.317, 0.001, 2, 4.367, 0.001, 2, 4.383, 0.001, 2, 4.4, 0.001, 0, 4.483, 0, 2, 4.5, 0, 2, 4.55, 0, 2, 4.567, 0, 2, 4.583, 0, 2, 4.733, 0, 2, 4.75, 0, 2, 4.8, 0, 2, 4.817, 0, 2, 4.9, 0, 2, 4.917, 0, 2, 5.017, 0, 2, 5.033, 0, 2, 5.167, 0, 0, 5.5, 0.39, 0, 6.083, -0.134, 0, 6.383, 0.037, 0, 6.7, -0.011, 0, 7, 0.003, 2, 7.033, 0.003, 0, 7.283, -0.001, 2, 7.35, -0.001, 2, 7.367, -0.001, 2, 7.383, -0.001, 0, 7.483, 0, 2, 7.5, 0, 2, 7.533, 0, 2, 7.55, 0, 2, 7.567, 0, 2, 7.717, 0, 2, 7.733, 0, 2, 7.767, 0, 2, 7.783, 0, 2, 7.867, 0, 2, 7.883, 0, 2, 8, 0, 2, 8.017, 0, 2, 8.25, 0, 0, 8.383, -1, 2, 8.483, -1, 0, 8.717, 0.384, 0, 9.033, -0.105, 0, 9.333, 0.029, 0, 9.433, 0.018, 0, 9.567, 1, 2, 9.667, 1, 0, 9.9, -0.404, 0, 10.217, 0.111, 0, 10.7, -0.935, 0, 11.017, 0.683, 0, 11.317, -0.199, 0, 11.65, 1, 2, 11.8, 1, 0, 11.817, -1, 2, 11.933, -1, 0, 12.233, 0.167, 0, 12.55, -0.046, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamEyeRsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.683, 0, 0, 0.75, 1, 2, 0.9, 1, 0, 0.917, -1, 2, 1.05, -1, 0, 1.35, 0.278, 0, 1.667, -0.066, 0, 1.917, 0.011, 0, 2.2, -0.069, 0, 2.533, 1, 2, 2.683, 1, 0, 2.7, -1, 2, 2.817, -1, 0, 3.1, 0.13, 0, 3.417, -0.036, 0, 3.717, 0.01, 2, 3.733, 0.01, 0, 4.033, -0.003, 0, 4.317, 0.001, 2, 4.367, 0.001, 2, 4.383, 0.001, 2, 4.4, 0.001, 0, 4.483, 0, 2, 4.5, 0, 2, 4.55, 0, 2, 4.567, 0, 2, 4.583, 0, 2, 4.733, 0, 2, 4.75, 0, 2, 4.8, 0, 2, 4.817, 0, 2, 4.9, 0, 2, 4.917, 0, 2, 5.017, 0, 2, 5.033, 0, 2, 5.167, 0, 0, 5.5, 0.39, 0, 6.083, -0.134, 0, 6.383, 0.037, 0, 6.7, -0.011, 0, 7, 0.003, 2, 7.033, 0.003, 0, 7.283, -0.001, 2, 7.35, -0.001, 2, 7.367, -0.001, 2, 7.383, -0.001, 0, 7.483, 0, 2, 7.5, 0, 2, 7.533, 0, 2, 7.55, 0, 2, 7.567, 0, 2, 7.717, 0, 2, 7.733, 0, 2, 7.767, 0, 2, 7.783, 0, 2, 7.867, 0, 2, 7.883, 0, 2, 8, 0, 2, 8.017, 0, 2, 8.25, 0, 0, 8.383, -1, 2, 8.483, -1, 0, 8.717, 0.384, 0, 9.033, -0.105, 0, 9.333, 0.029, 0, 9.433, 0.018, 0, 9.567, 1, 2, 9.667, 1, 0, 9.9, -0.404, 0, 10.217, 0.111, 0, 10.7, -0.935, 0, 11.017, 0.683, 0, 11.317, -0.199, 0, 11.65, 1, 2, 11.8, 1, 0, 11.817, -1, 2, 11.933, -1, 0, 12.233, 0.167, 0, 12.55, -0.046, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamEyeLGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.683, 0, 0, 0.75, -0.305, 0, 0.8, -0.2, 0, 0.9, -1, 2, 0.917, -1, 0, 0.933, 1, 2, 0.967, 1, 0, 1.017, 0.83, 0, 1.05, 0.859, 0, 1.3, -0.31, 0, 1.567, 0.156, 0, 1.85, -0.07, 0, 2.117, 0.04, 0, 2.433, -0.031, 0, 2.467, -0.03, 0, 2.533, -0.305, 0, 2.583, -0.186, 0, 2.683, -1, 2, 2.7, -1, 0, 2.717, 1, 2, 2.767, 1, 0, 3.067, -0.242, 0, 3.35, 0.095, 0, 3.617, -0.038, 2, 3.633, -0.038, 0, 3.917, 0.015, 0, 4.217, -0.005, 0, 4.517, 0.002, 2, 4.533, 0.002, 2, 4.55, 0.002, 0, 4.783, -0.001, 2, 4.867, -0.001, 0, 4.9, 0, 2, 4.917, 0, 2, 4.95, 0, 2, 4.967, 0, 2, 5, 0, 2, 5.017, 0, 2, 5.033, 0, 2, 5.05, 0, 2, 5.067, 0, 2, 5.167, 0, 0, 5.367, -0.079, 0, 5.667, 0.067, 0, 5.917, 0.024, 0, 6.017, 0.034, 0, 6.25, -0.062, 0, 6.55, 0.035, 0, 6.85, -0.015, 0, 7.15, 0.006, 2, 7.167, 0.006, 0, 7.45, -0.002, 2, 7.467, -0.002, 0, 7.733, 0.001, 2, 7.75, 0.001, 2, 7.767, 0.001, 2, 7.783, 0.001, 2, 7.8, 0.001, 2, 7.817, 0.001, 0, 7.833, 0, 2, 7.85, 0, 2, 7.883, 0, 2, 7.9, 0, 2, 7.95, 0, 2, 7.967, 0, 2, 7.983, 0, 2, 8, 0, 2, 8.017, 0, 2, 8.15, 0, 2, 8.167, 0, 2, 8.217, 0, 2, 8.233, 0, 2, 8.25, 0, 0, 8.367, 0.271, 0, 8.583, -0.625, 0, 8.883, 0.304, 0, 9.183, -0.129, 0, 9.433, 0.039, 0, 9.55, -0.241, 0, 9.767, 0.581, 0, 10.067, -0.305, 0, 10.367, 0.132, 0, 10.483, 0.072, 0, 10.6, 0.172, 0, 10.9, -0.43, 0, 11.183, 0.394, 0, 11.483, -0.19, 0, 11.583, -0.097, 0, 11.633, -0.29, 0, 11.717, -0.147, 0, 11.817, -1, 2, 11.85, -1, 0, 11.867, 1, 2, 11.967, 1, 0, 12.217, -0.288, 0, 12.483, 0.107, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamEyeRGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.683, 0, 0, 0.75, -0.305, 0, 0.8, -0.2, 0, 0.9, -1, 2, 0.917, -1, 0, 0.933, 1, 2, 0.967, 1, 0, 1.017, 0.83, 0, 1.05, 0.859, 0, 1.3, -0.31, 0, 1.567, 0.156, 0, 1.85, -0.07, 0, 2.117, 0.04, 0, 2.433, -0.031, 0, 2.467, -0.03, 0, 2.533, -0.305, 0, 2.583, -0.186, 0, 2.683, -1, 2, 2.7, -1, 0, 2.717, 1, 2, 2.767, 1, 0, 3.067, -0.242, 0, 3.35, 0.095, 0, 3.617, -0.038, 2, 3.633, -0.038, 0, 3.917, 0.015, 0, 4.217, -0.005, 0, 4.517, 0.002, 2, 4.533, 0.002, 2, 4.55, 0.002, 0, 4.783, -0.001, 2, 4.867, -0.001, 0, 4.9, 0, 2, 4.917, 0, 2, 4.95, 0, 2, 4.967, 0, 2, 5, 0, 2, 5.017, 0, 2, 5.033, 0, 2, 5.05, 0, 2, 5.067, 0, 2, 5.167, 0, 0, 5.367, -0.079, 0, 5.667, 0.067, 0, 5.917, 0.024, 0, 6.017, 0.034, 0, 6.25, -0.062, 0, 6.55, 0.035, 0, 6.85, -0.015, 0, 7.15, 0.006, 2, 7.167, 0.006, 0, 7.45, -0.002, 2, 7.467, -0.002, 0, 7.733, 0.001, 2, 7.75, 0.001, 2, 7.767, 0.001, 2, 7.783, 0.001, 2, 7.8, 0.001, 2, 7.817, 0.001, 0, 7.833, 0, 2, 7.85, 0, 2, 7.883, 0, 2, 7.9, 0, 2, 7.95, 0, 2, 7.967, 0, 2, 7.983, 0, 2, 8, 0, 2, 8.017, 0, 2, 8.15, 0, 2, 8.167, 0, 2, 8.217, 0, 2, 8.233, 0, 2, 8.25, 0, 0, 8.367, 0.271, 0, 8.583, -0.625, 0, 8.883, 0.304, 0, 9.183, -0.129, 0, 9.433, 0.039, 0, 9.55, -0.241, 0, 9.767, 0.581, 0, 10.067, -0.305, 0, 10.367, 0.132, 0, 10.483, 0.072, 0, 10.6, 0.172, 0, 10.9, -0.43, 0, 11.183, 0.394, 0, 11.483, -0.19, 0, 11.583, -0.097, 0, 11.633, -0.29, 0, 11.717, -0.147, 0, 11.817, -1, 2, 11.85, -1, 0, 11.867, 1, 2, 11.967, 1, 0, 12.217, -0.288, 0, 12.483, 0.107, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamShoulderLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.067, 0, 2, 5.067, 0, 2, 5.167, 0, 0, 6.167, 1, 2, 8.217, 1, 0, 8.617, 0, 2, 9.233, 0, 2, 10.65, 0, 2, 11.833, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 5.167, 0, 0, 6.017, 2.28, 2, 8.383, 2.28, 0, 9.233, 0, 2, 10.65, 0, 0, 11, 4.62, 0, 11.55, 0, 0, 11.95, 0.6, 0, 12.283, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.067, 0, 2, 5.167, 0, 0, 6.017, -30, 2, 8.133, -30, 0, 8.85, 0, 2, 10.65, 0, 2, 11.833, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 30, 2, 0.25, 30, 0, 1.067, 0, 2, 5.167, 0, 0, 6.017, 0.96, 0, 7.45, 0.66, 2, 8.383, 0.66, 0, 8.767, -0.06, 0, 9.233, 0, 2, 10.65, 0, 1, 10.856, 0, 11.061, 20.855, 11.267, 25.904, 1, 11.456, 30.544, 11.644, 30, 11.833, 30, 2, 12.75, 30, 2, 13, 30]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.067, 0, 2, 5.167, 0, 0, 6.017, 6, 2, 8.383, 6, 0, 9.233, 0, 2, 10.65, 0, 0, 11, -17.143, 0, 11.533, 0, 2, 11.833, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1, 2, 0.25, -1, 2, 0.667, -1, 0, 0.7, 1, 2, 1.067, 1, 2, 5.167, 1, 2, 8.383, 1, 2, 9.233, 1, 2, 10.65, 1, 2, 11.183, 1, 0, 11.2, -1, 2, 11.833, -1, 2, 12.75, -1, 2, 13, -1]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.45, -0.336, 0, 1.067, 0, 2, 5.167, 0, 0, 6.017, -0.038, 2, 8.383, -0.038, 0, 9.233, 0, 2, 10.65, 0, 1, 10.783, 0, 10.917, 0.028, 11.05, 0.128, 1, 11.139, 0.194, 11.228, 0.263, 11.317, 0.263, 0, 11.75, -0.218, 0, 12.133, 0.1, 0, 12.35, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamHandLDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10, 2, 0.25, 10, 2, 0.65, 10, 2, 0.667, 4, 2, 11.233, 4, 3, 11.833, 10, 2, 12.75, 10, 2, 13, 10]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.067, -1, 2, 5.167, -1, 2, 8.383, -1, 2, 9.233, -1, 2, 10.65, -1, 0, 11.45, 0, 0, 11.817, -0.5, 0, 12.417, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.067, 0, 2, 5.167, 0, 2, 8.383, 0, 2, 9.233, 0, 2, 10.65, 0, 2, 11.45, 0, 0, 11.817, -0.9, 0, 12.417, 0.2, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.983, 0, 0, 2.533, 1, 2, 2.8, 1, 2, 11.067, 1, 2, 11.583, 1, 0, 11.833, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.183, 0, 2, 2.8, 0, 0, 11.7, 0.5, 0, 11.9, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamShoulderRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 5.067, 0, 2, 5.167, 0, 0, 6.167, 5, 2, 8.217, 5, 0, 8.617, 0, 2, 9.233, 0, 2, 10.65, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.633, 0, 0, 3, -0.54, 0, 3.333, 0, 2, 5.167, 0, 0, 6.017, -2.1, 2, 8.383, -2.1, 0, 9.233, 0, 2, 10.65, 0, 0, 10.85, -1.02, 0, 11.583, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 5.167, 0, 0, 6.017, -30, 2, 8.133, -30, 0, 8.85, 0, 2, 10.65, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 0.361, 0, 0.472, 0.552, 0.583, 0.6, 1, 1.283, 0.904, 1.983, 1, 2.683, 1, 2, 10.7, 1, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.633, 0, 0, 3, -4.14, 0, 3.333, 0, 2, 5.167, 0, 0, 6.017, -1.38, 2, 8.383, -1.38, 0, 9.233, 0, 2, 10.65, 0, 0, 10.85, -1.32, 0, 11.583, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 5.167, 0, 0, 6.017, 6.18, 2, 8.383, 6.18, 0, 9.233, 0, 2, 10.65, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.633, 0, 0, 3, 0.212, 0, 3.333, 0, 2, 5.167, 0, 0, 6.017, 0.084, 2, 8.383, 0.084, 0, 9.233, 0, 2, 10.65, 0, 0, 10.85, 0.006, 0, 11.583, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamHandRDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 3, 0, 2, 3.017, 4, 2, 10.65, 4, 2, 10.683, 4, 2, 10.7, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 3.083, 0, 0, 3.333, -1, 2, 5.167, -1, 2, 8.383, -1, 2, 9.233, -1, 2, 10.65, -1, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.9, 0, 0, 2.917, 1, 2, 10.683, 1, 2, 10.7, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.9, 0, 0, 2.917, 1, 2, 10.683, 1, 2, 10.7, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 2.917, -0.21, 0, 3.583, -0.068, 2, 8.383, -0.068, 0, 9.233, -0.008, 0, 10.7, -0.024, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 2.917, 0.174, 0, 3.583, 0.056, 2, 8.383, 0.056, 1, 8.666, 0.056, 8.95, 0.031, 9.233, 0.002, 1, 9.722, -0.047, 10.211, -0.064, 10.7, -0.064, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 2.917, -0.506, 0, 3.583, -0.174, 2, 8.383, -0.174, 0, 9.233, -0.008, 0, 10.7, -0.182, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 6.25, 0, 0, 6.767, -8.906, 0, 7.067, 7.906, 0, 7.433, -5.704, 0, 7.667, 4.704, 0, 7.983, -2.515, 0, 8.433, 0, 2, 9.6, 0, 2, 10.25, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -2.728, 0, 0.617, 5.594, 0, 1.133, -8, 2, 1.8, -8, 0, 2.433, -6, 0, 2.817, -20.733, 0, 3.25, 5.998, 0, 3.783, -5, 0, 4.2, 1, 0, 4.6, -1, 0, 5.067, 0, 0, 6.433, -23.4, 1, 7.044, -23.4, 7.656, -22.59, 8.267, -20, 1, 8.361, -19.6, 8.456, -1.609, 8.55, 1, 1, 8.6, 2.381, 8.65, 2, 8.7, 2, 0, 8.933, -12, 1, 9, -12, 9.066, -12.092, 9.133, -9, 1, 9.2, -5.908, 9.266, 3.817, 9.333, 3.817, 1, 9.366, 3.817, 9.4, 4.14, 9.433, 3, 1, 9.489, 1.099, 9.544, -10, 9.6, -10, 1, 9.633, -10, 9.667, -10.322, 9.7, -9.032, 1, 9.767, -6.451, 9.833, 0, 9.9, 0, 0, 10.133, -16.809, 2, 10.667, -16.809, 0, 11.133, 2, 0, 11.467, 0, 2, 11.917, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.533, -10, 1, 0.716, -10, 0.9, 6.373, 1.083, 7.359, 1, 1.344, 8.764, 1.606, 8.654, 1.867, 8.654, 1, 2.1, 8.654, 2.334, 8.517, 2.567, 7, 1, 2.722, 5.989, 2.878, 0.466, 3.033, 0, 1, 3.344, -0.932, 3.656, -1, 3.967, -1, 0, 5.1, 0, 0, 6.467, -0.96, 0, 8.8, 0, 0, 11.017, -0.519, 0, 11.333, 4.903, 0, 11.833, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -7.795, 2, 0.25, -7.795, 1, 0.75, -7.795, 1.25, -5.197, 1.75, 0, 1, 2.25, 5.197, 2.75, 7.795, 3.25, 7.795, 1, 3.75, 7.795, 4.25, 5.197, 4.75, 0, 1, 5.25, -5.197, 5.75, -7.795, 6.25, -7.795, 1, 6.75, -7.795, 7.25, -5.197, 7.75, 0, 1, 8.25, 5.197, 8.75, 7.795, 9.25, 7.795, 0, 10.25, -7.795, 0, 11.6, 1, 0, 12.233, -10, 0, 12.75, -7.795, 2, 13, -7.795]}, {"Target": "Parameter", "Id": "ParamBodyStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 0.294, 0, 0.339, 4.872, 0.383, 6.338, 1, 0.478, 9.454, 0.572, 10, 0.667, 10, 0, 1.083, -3, 0, 1.333, 0, 0, 1.633, -1.92, 0, 2.183, 2.409, 0, 2.683, 0, 0, 3.033, 10, 1, 3.161, 10, 3.289, 10, 3.417, 7.281, 1, 3.578, 3.273, 3.739, -10, 3.9, -10, 0, 4.233, -3, 0, 4.667, -9, 0, 5.033, -4.543, 0, 5.3, -7, 1, 5.394, -7, 5.489, -3.351, 5.583, -2, 1, 5.728, 0.067, 5.872, 0.25, 6.017, 0.25, 1, 6.222, 0.25, 6.428, -4.017, 6.633, -5.184, 1, 7.266, -8.778, 7.9, -9.608, 8.533, -9.608, 0, 8.7, -2.353, 0, 8.933, -10, 0, 9.333, -0.551, 0, 9.6, -10, 0, 9.9, -3.203, 0, 10.133, -10, 2, 10.667, -10, 0, 10.95, 2, 0, 11.333, -2, 0, 11.8, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.517, 4.859, 0, 0.883, -1.073, 0, 1.483, 2.445, 0, 2.017, -0.586, 0, 2.533, 0.603, 0, 4.967, -7, 0, 6.467, 6, 0, 8, -4, 0, 9.583, 10, 0, 10.833, -5, 0, 11.933, 1.668, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamLegLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 2.75, 14.515, 0, 4, -9.742, 1, 4.406, -9.742, 4.811, -5.146, 5.217, 0, 1, 6.056, 10.644, 6.894, 14.515, 7.733, 14.515, 0, 8.533, 2.532, 0, 9.517, 27.852, 1, 9.761, 27.852, 10.006, 13.831, 10.25, 0, 1, 10.6, -19.804, 10.95, -25.433, 11.3, -25.433, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamLegRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.55, 7.732, 0, 4.05, -9.756, 1, 4.439, -9.756, 4.828, -5.355, 5.217, 0, 1, 5.65, 5.967, 6.084, 7.732, 6.517, 7.732, 0, 9.033, -9.756, 0, 9.583, 30, 1, 9.805, 30, 10.028, 9.018, 10.25, 0, 1, 10.7, -18.261, 11.15, -22, 11.6, -22, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.483, 0, 1, 2.533, 0, 2.583, 0.438, 2.633, 0.5, 1, 2.939, 0.878, 3.244, 1, 3.55, 1, 2, 10.767, 1, 0, 11.75, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Physics_AngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 6.417, 0, 0, 6.783, -5.738, 0, 7.117, 6.626, 0, 7.467, -5.038, 0, 7.75, 4.038, 0, 8.05, -2.518, 0, 8.45, 0.333, 0, 8.633, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Physics_AngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.317, 0, 0, 0.433, -0.923, 0, 0.683, 3.649, 0, 1.183, -5.348, 0, 1.567, -4.571, 0, 1.817, -4.604, 0, 2.433, -3.381, 0, 2.85, -12.838, 0, 3.3, 5.268, 0, 3.817, -3.476, 0, 4.233, 1.154, 0, 4.65, -0.745, 0, 4.967, 0, 2, 5.367, 0, 0, 6.45, -13.737, 0, 8.667, 2.852, 0, 9.033, -8.193, 0, 9.417, 3.479, 0, 9.7, -6.797, 0, 9.95, 0.117, 0, 10.25, -11.111, 0, 10.65, -9.609, 0, 10.667, -9.611, 0, 11.183, 2.249, 0, 11.55, -0.28, 0, 11.733, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Physics_AngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 6.25, 0, 0, 6.55, 3.675, 0, 6.983, -7.167, 0, 7.3, 5.851, 0, 7.617, -5.973, 0, 7.9, 3.688, 0, 8.25, -1.176, 0, 8.483, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 1.819, 0, 0.617, -3.729, 0, 1.133, 5.334, 2, 1.8, 5.334, 0, 2.433, 4, 0, 2.667, 9.114, 0, 3.067, -14.946, 0, 3.467, 15.534, 0, 3.9, -11.824, 0, 4.333, 8.94, 0, 4.75, -6.4, 0, 5.183, 4.837, 0, 5.617, -1.957, 0, 6.017, 1.659, 0, 6.467, -1.811, 0, 6.883, 1.113, 0, 7.3, -0.774, 0, 7.717, 0.398, 0, 8.133, -0.381, 0, 8.267, -0.257, 0, 8.483, -7.178, 0, 8.867, 13.638, 0, 9.25, -16.157, 0, 9.583, 18.494, 0, 9.917, -12.121, 0, 10.217, 4.048, 0, 10.633, -2.576, 0, 10.683, -2.457, 0, 10.9, -3.713, 0, 11.3, 6.069, 0, 11.7, -3.549, 0, 12.117, 2.372, 0, 12.533, -1.584, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -1.819, 0, 0.617, 3.729, 0, 1.133, -5.334, 2, 1.8, -5.334, 0, 2.433, -4, 0, 2.617, -8.055, 0, 2.9, 25.516, 0, 3.2, -30, 2, 3.35, -30, 0, 3.617, 30, 2, 3.733, 30, 0, 4.117, -25.265, 0, 4.55, 18.605, 0, 4.983, -13.582, 0, 5.4, 10.374, 0, 5.817, -2.76, 0, 6.2, 5.149, 0, 6.667, -3.515, 0, 7.083, 2.327, 0, 7.5, -1.729, 0, 7.9, 0.71, 2, 7.917, 0.71, 0, 8.267, -0.918, 0, 8.4, 1.818, 0, 8.683, -20.942, 0, 9.017, 30, 2, 9.1, 30, 0, 9.383, -30, 2, 9.533, -30, 0, 9.733, 30, 2, 9.883, 30, 0, 10.1, -26.583, 0, 10.417, 13.429, 0, 10.8, -5.038, 0, 10.85, -4.969, 0, 11.15, -11.641, 0, 11.5, 13.147, 0, 11.9, -7.749, 0, 12.317, 5.03, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_L", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.35, -1.037, 0, 0.517, 1.282, 0, 0.783, -2.337, 0, 1.15, 3.425, 0, 1.567, -2.211, 0, 2.017, 1.367, 0, 2.55, -1.404, 0, 2.933, 4.568, 0, 3.317, -8.023, 0, 3.733, 6.621, 0, 4.217, -5.386, 0, 4.683, 4.327, 0, 5.1, -3.125, 0, 5.583, 2.502, 0, 6.033, -0.583, 0, 6.467, 0.829, 0, 6.783, 0.477, 0, 6.883, 0.688, 0, 7.183, -3.667, 0, 7.55, 4.601, 0, 7.883, -3.658, 0, 8.25, 1.625, 0, 8.267, 1.612, 0, 8.383, 1.998, 0, 8.733, -6.147, 0, 9.1, 7.861, 0, 9.517, -9.09, 0, 9.833, 7.481, 0, 10.1, -2.883, 0, 10.317, 1.642, 0, 10.617, -0.931, 0, 10.867, 0.622, 0, 11.183, -3.388, 0, 11.583, 2.745, 0, 12.017, -1.592, 0, 12.533, 0.919, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_R", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.35, 0.941, 0, 0.533, -1.454, 0, 0.767, 2.529, 0, 1.15, -3.115, 0, 1.583, 2.11, 0, 2.033, -1.325, 0, 2.55, 1.394, 0, 2.933, -4.546, 0, 3.317, 8.003, 0, 3.733, -6.606, 0, 4.217, 5.378, 0, 4.683, -4.33, 0, 5.1, 3.13, 0, 5.583, -2.503, 0, 6.033, 0.584, 0, 6.433, -1.139, 0, 6.867, 2.003, 0, 7.2, -4.139, 0, 7.567, 4.673, 0, 7.883, -3.163, 0, 8.25, 1.487, 0, 8.45, -0.469, 0, 8.7, 4.904, 0, 9.083, -6.492, 0, 9.5, 8.528, 0, 9.817, -7.564, 0, 10.1, 3.296, 0, 10.333, -1.723, 0, 10.633, 0.759, 0, 10.867, -0.501, 0, 11.183, 3.088, 0, 11.583, -2.57, 0, 12.017, 1.525, 0, 12.517, -0.582, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 3.637, 0, 0.617, -7.459, 0, 1.133, 10.668, 2, 1.8, 10.668, 0, 2.433, 8, 0, 2.65, 17.446, 0, 3.067, -17.587, 0, 3.45, 9.547, 0, 3.95, -4.492, 0, 4.383, 2.35, 0, 4.7, -2.009, 0, 4.967, 0.158, 0, 5.067, 0, 0, 5.8, 16.91, 0, 6.183, -2.19, 0, 6.55, 14.624, 0, 6.883, -30, 2, 7.067, -30, 0, 7.2, 30, 2, 7.4, 30, 1, 7.45, 30, 7.5, -30, 7.55, -30, 2, 7.7, -30, 1, 7.761, -30, 7.822, 30, 7.883, 30, 2, 7.95, 30, 0, 8.25, -14.991, 0, 8.3, -14.293, 0, 8.4, -14.935, 0, 8.867, 14.71, 0, 9.25, -14.404, 0, 9.567, 17.778, 0, 9.85, -12.871, 0, 10.1, 15.803, 0, 10.417, -4.67, 0, 10.683, 0.561, 0, 10.967, -8.279, 0, 11.317, 4.692, 0, 11.65, -2.636, 0, 11.983, 1.947, 0, 12.183, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -3.637, 0, 0.617, 7.459, 0, 1.133, -10.668, 2, 1.8, -10.668, 0, 2.433, -8, 0, 2.617, -15.983, 0, 2.833, 17.019, 0, 3.167, -9.868, 0, 3.517, 5.443, 0, 3.617, 3.521, 0, 3.633, 3.547, 0, 3.983, -1.104, 0, 4.017, -1.103, 0, 4.15, -1.198, 0, 4.283, 1.766, 0, 4.5, 0.458, 0, 4.55, 2.267, 0, 4.8, -1.618, 0, 5.067, 3.665, 0, 5.783, -16.746, 0, 5.983, 17.37, 0, 6.25, -7.178, 0, 6.75, 6.585, 0, 6.767, 6.542, 0, 6.9, 18.819, 0, 7.083, -30, 2, 7.2, -30, 0, 7.367, 30, 2, 7.533, 30, 1, 7.583, 30, 7.633, -30, 7.683, -30, 2, 7.833, -30, 0, 7.983, 30, 2, 8.067, 30, 0, 8.283, -16.11, 0, 8.5, 1.722, 0, 8.65, -4.941, 0, 9, 8.695, 0, 9.417, -9.727, 0, 9.45, -9.65, 0, 9.467, -9.656, 0, 9.7, 15.437, 0, 10, -18.288, 0, 10.233, 15.003, 0, 10.483, -7.339, 0, 10.817, 5.074, 0, 11.133, -5.52, 0, 11.15, -5.481, 0, 11.183, -5.896, 0, 11.433, 4.265, 0, 11.5, 3.4, 0, 11.533, 3.982, 0, 11.767, -2.839, 0, 11.833, -1.993, 0, 11.867, -3, 0, 12.083, 2.152, 0, 12.167, 1.442, 0, 12.183, 1.884, 0, 12.317, -3.05, 0, 12.533, 3.219, 0, 12.667, 0, 2, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -2.749, 0, 0.583, 6.847, 0, 0.95, -9.125, 0, 1.367, 2.762, 0, 1.817, -0.517, 0, 2.2, 0.951, 0, 2.717, -10.31, 0, 3.117, 19.541, 0, 3.55, -10.864, 0, 4.033, 6.668, 0, 4.467, -3.514, 0, 5.067, 0, 0, 6.067, -25.279, 0, 6.583, 23.591, 0, 6.883, -30, 2, 7.1, -30, 1, 7.15, -30, 7.2, 30, 7.25, 30, 2, 7.45, 30, 0, 7.583, -30, 2, 7.717, -30, 0, 7.95, 25.825, 0, 8.283, -8.814, 0, 8.533, 13.516, 0, 8.9, -16.948, 0, 9.3, 16.459, 0, 9.583, -15.78, 0, 9.883, 10.012, 0, 10.117, -14.004, 0, 10.517, 3.033, 0, 10.683, 1.481, 0, 10.983, 10.388, 0, 11.383, -4.818, 0, 11.783, 2.523, 0, 12.2, -1.882, 0, 12.6, 1.887, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.35, 4.268, 0, 0.55, -6.926, 0, 0.783, 7.839, 0, 1.133, -7.214, 0, 1.467, 3.493, 0, 1.833, -1, 0, 2.417, 0.454, 2, 2.433, 0.454, 0, 2.617, 4.556, 0, 2.95, -11.979, 0, 3.317, 14.909, 0, 3.667, -7.715, 0, 3.7, -7.486, 0, 3.733, -7.717, 0, 4.217, 3.903, 0, 4.233, 3.839, 0, 4.267, 4.116, 0, 4.633, -3.358, 0, 4.817, 3.84, 0, 5.1, -3.52, 0, 6.05, 25.227, 0, 6.317, -26.976, 0, 6.75, 15.033, 0, 6.767, 15.009, 0, 6.917, 30, 1, 6.978, 30, 7.039, -30, 7.1, -30, 2, 7.3, -30, 0, 7.433, 30, 2, 7.633, 30, 1, 7.666, 30, 7.7, -30, 7.733, -30, 2, 7.917, -30, 0, 8.083, 30, 2, 8.167, 30, 0, 8.45, -23.672, 0, 8.783, 18.754, 0, 9.1, -14.712, 0, 9.5, 18.084, 0, 9.783, -19.806, 0, 10.05, 21.066, 0, 10.317, -13.515, 0, 10.617, 5.452, 0, 10.9, -5.54, 0, 11.2, 7.843, 0, 11.517, -4.979, 0, 11.617, -3.053, 0, 11.7, -4.415, 0, 12, 2.448, 0, 12.033, 2.261, 0, 12.1, 4.088, 0, 12.383, -2.134, 0, 12.433, -1.931, 0, 12.5, -4.131, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Jewelry_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 0.303, 0, 0.6, -0.533, 0, 0.933, 0.876, 0, 1.317, -0.857, 0, 1.733, 0.545, 0, 2.15, -0.402, 0, 2.667, 0.927, 0, 3.067, -2.112, 0, 3.467, 2.34, 0, 3.9, -1.843, 0, 4.333, 1.404, 0, 4.75, -0.998, 0, 5.2, 0.712, 0, 5.617, -0.267, 0, 6.017, 0.237, 0, 6.333, -0.112, 0, 6.633, 0.146, 0, 6.983, -1.282, 0, 7.317, 1.878, 0, 7.65, -2.028, 0, 8, 1.296, 0, 8.467, -1.94, 0, 8.867, 2.705, 0, 9.25, -2.937, 0, 9.6, 3.105, 0, 9.917, -1.909, 0, 10.217, 0.528, 0, 10.65, -0.336, 0, 10.683, -0.329, 0, 10.917, -0.652, 0, 11.3, 1.026, 0, 11.717, -0.715, 0, 12.133, 0.455, 0, 12.55, -0.29, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Jewelry_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -0.303, 0, 0.567, 0.426, 0, 0.783, -1.368, 0, 1.15, 2.369, 0, 1.533, -1.836, 0, 1.933, 1.19, 0, 2.35, -0.907, 0, 2.883, 2.65, 0, 3.267, -5.265, 0, 3.667, 4.939, 0, 4.117, -3.798, 0, 4.55, 2.896, 0, 4.967, -2.1, 0, 5.4, 1.505, 0, 5.817, -0.324, 0, 6.217, 0.772, 0, 6.533, -0.073, 0, 6.867, 0.889, 0, 7.183, -3.468, 0, 7.533, 4.676, 0, 7.867, -4.464, 0, 8.217, 3.007, 0, 8.65, -4.848, 0, 9.05, 5.951, 0, 9.45, -6.521, 0, 9.8, 6.49, 0, 10.1, -3.806, 0, 10.417, 1.812, 0, 10.783, -0.607, 0, 10.833, -0.604, 0, 11.15, -1.971, 0, 11.517, 2.206, 0, 11.917, -1.547, 0, 12.333, 0.975, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Jewelry_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.533, 0, 0, 0.7, 0.828, 0, 0.967, -1.693, 0, 1.3, 2.157, 0, 1.65, -1.817, 0, 2.05, 1.222, 0, 2.467, -0.815, 0, 3.067, 2.712, 0, 3.417, -4.445, 0, 3.8, 4.203, 0, 4.233, -3.074, 0, 4.667, 2.41, 0, 5.083, -1.862, 0, 5.5, 1.108, 0, 5.917, -0.665, 0, 6.333, 0.513, 0, 6.667, -0.461, 0, 7.033, 1.588, 0, 7.35, -3.5, 0, 7.683, 4.889, 0, 8, -4.852, 0, 8.35, 3.611, 0, 8.8, -4.073, 0, 9.2, 5.35, 0, 9.583, -6.258, 0, 9.933, 6.653, 0, 10.25, -4.987, 0, 10.567, 2.785, 0, 10.867, -0.835, 0, 11.083, 0.194, 0, 11.333, -1.48, 0, 11.667, 1.964, 0, 12.033, -1.54, 0, 12.433, 0.97, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.417, -0.836, 0, 0.883, 1.649, 0, 1.267, -2.005, 0, 1.65, 1.361, 0, 2.05, -1, 0, 2.467, 0.849, 0, 2.933, -1.6, 0, 3.35, 1.595, 0, 4.017, -1.303, 0, 4.433, 1.62, 0, 4.867, -1.596, 0, 5.267, 1.462, 0, 5.617, -1.17, 0, 6.033, 0.87, 0, 6.467, -0.358, 0, 6.8, 0.058, 0, 7.017, -0.132, 0, 7.317, 0.348, 0, 7.65, -0.469, 0, 7.983, 0.332, 0, 8.467, -0.48, 0, 8.55, -0.354, 0, 8.667, -0.742, 0, 8.9, 1.877, 0, 9.25, -1.997, 0, 9.583, 2.395, 0, 9.9, -1.758, 0, 10.167, 0.893, 0, 10.583, -0.566, 0, 10.683, -0.464, 0, 10.867, -1.129, 0, 11.2, 1.627, 0, 11.617, -1.27, 0, 12.017, 0.87, 0, 12.433, -0.551, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.35, 0.363, 0, 0.633, -2.213, 0, 1.083, 3.831, 0, 1.467, -3.826, 0, 1.85, 2.749, 0, 2.25, -2.262, 0, 2.683, 1.875, 0, 3.117, -3.607, 0, 3.567, 2.916, 0, 4.25, -2.88, 0, 4.65, 3.37, 0, 5.067, -3.334, 0, 5.45, 3.082, 0, 5.817, -2.595, 0, 6.233, 1.802, 0, 6.667, -0.462, 0, 6.967, 0.242, 0, 7.2, -0.421, 0, 7.533, 0.998, 0, 7.85, -1.045, 0, 8.2, 0.804, 0, 8.567, -0.856, 0, 8.633, -0.706, 0, 8.817, -2.301, 0, 9.1, 3.753, 0, 9.433, -4.582, 0, 9.75, 4.734, 0, 10.067, -3.556, 0, 10.4, 2.285, 0, 10.717, -0.943, 0, 10.817, -0.703, 0, 11.05, -2.934, 0, 11.417, 3.34, 0, 11.817, -2.644, 0, 12.217, 1.78, 0, 12.633, -1.15, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 0.909, 0, 0.6, -1.6, 0, 0.933, 2.627, 0, 1.317, -2.572, 0, 1.733, 1.635, 0, 2.15, -1.207, 0, 2.667, 2.78, 0, 3.067, -6.337, 0, 3.467, 7.02, 0, 3.9, -5.529, 0, 4.333, 4.211, 0, 4.75, -2.995, 0, 5.2, 2.137, 0, 5.617, -0.8, 0, 6.017, 0.712, 0, 6.333, -0.336, 0, 6.633, 0.439, 0, 6.983, -3.845, 0, 7.317, 5.633, 0, 7.65, -6.085, 0, 8, 3.888, 0, 8.467, -5.821, 0, 8.867, 8.114, 0, 9.25, -8.812, 0, 9.6, 9.316, 0, 9.917, -5.728, 0, 10.217, 1.584, 0, 10.65, -1.008, 0, 10.683, -0.986, 0, 10.917, -1.957, 0, 11.3, 3.078, 0, 11.717, -2.146, 0, 12.133, 1.365, 0, 12.55, -0.869, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -0.909, 0, 0.567, 1.277, 0, 0.783, -4.105, 0, 1.15, 7.109, 0, 1.533, -5.507, 0, 1.933, 3.57, 0, 2.35, -2.722, 0, 2.883, 7.951, 0, 3.267, -15.794, 0, 3.667, 14.816, 0, 4.117, -11.393, 0, 4.55, 8.688, 0, 4.967, -6.301, 0, 5.4, 4.514, 0, 5.817, -0.971, 0, 6.217, 2.315, 0, 6.533, -0.219, 0, 6.867, 2.667, 0, 7.183, -10.404, 0, 7.533, 14.027, 0, 7.867, -13.393, 0, 8.217, 9.021, 0, 8.65, -14.543, 0, 9.05, 17.854, 0, 9.45, -19.562, 0, 9.8, 19.469, 0, 10.1, -11.417, 0, 10.417, 5.437, 0, 10.783, -1.822, 0, 10.833, -1.812, 0, 11.15, -5.913, 0, 11.517, 6.618, 0, 11.917, -4.642, 0, 12.333, 2.926, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.533, 0, 0, 0.7, 2.485, 0, 0.967, -5.079, 0, 1.3, 6.471, 0, 1.65, -5.452, 0, 2.05, 3.665, 0, 2.467, -2.444, 0, 3.067, 8.135, 0, 3.417, -13.336, 0, 3.8, 12.61, 0, 4.233, -9.22, 0, 4.667, 7.231, 0, 5.083, -5.585, 0, 5.5, 3.324, 0, 5.917, -1.995, 0, 6.333, 1.538, 0, 6.667, -1.382, 0, 7.033, 4.763, 0, 7.35, -10.5, 0, 7.683, 14.666, 0, 8, -14.557, 0, 8.35, 10.832, 0, 8.8, -12.218, 0, 9.2, 16.049, 0, 9.583, -18.775, 0, 9.933, 19.959, 0, 10.25, -14.96, 0, 10.567, 8.356, 0, 10.867, -2.504, 0, 11.083, 0.581, 0, 11.333, -4.439, 0, 11.667, 5.893, 0, 12.033, -4.62, 0, 12.433, 2.909, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.65, 0, 0, 0.817, 2.416, 0, 1.083, -5.924, 0, 1.417, 8.501, 0, 1.767, -8.056, 0, 2.133, 5.843, 0, 2.533, -3.793, 0, 3.15, 8.915, 0, 3.517, -16.227, 0, 3.883, 17.056, 0, 4.3, -12.991, 0, 4.75, 9.819, 0, 5.167, -7.838, 0, 5.583, 5.088, 0, 5.983, -3.057, 0, 6.417, 2.201, 0, 6.767, -1.961, 0, 7.133, 5.368, 0, 7.45, -12.311, 0, 7.783, 18.142, 0, 8.117, -19.459, 0, 8.45, 16.418, 0, 8.883, -15.509, 0, 9.283, 19.906, 0, 9.667, -23.658, 0, 10.033, 25.624, 0, 10.367, -20.984, 0, 10.683, 13.924, 0, 11, -5.955, 0, 11.233, 0.778, 0, 11.467, -4.35, 0, 11.783, 7.54, 0, 12.15, -6.916, 0, 12.517, 4.727, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.75, 0, 0, 0.917, 2.476, 0, 1.2, -6.798, 0, 1.517, 10.833, 0, 1.867, -11.366, 0, 2.233, 9.016, 0, 2.617, -6.043, 0, 3.25, 9.392, 0, 3.6, -18.858, 0, 3.967, 21.654, 0, 4.383, -17.646, 0, 4.817, 13.145, 0, 5.25, -10.616, 0, 5.65, 7.521, 0, 6.067, -4.723, 0, 6.483, 3.225, 0, 6.867, -2.765, 0, 7.217, 6.27, 0, 7.55, -14.293, 0, 7.867, 21.784, 0, 8.2, -24.726, 0, 8.55, 22.972, 0, 8.95, -20.413, 0, 9.35, 23.425, 0, 9.733, -28.108, 0, 10.1, 30, 2, 10.133, 30, 0, 10.467, -27.541, 0, 10.8, 20.611, 0, 11.133, -11.058, 0, 11.4, 2.337, 0, 11.617, -3.976, 0, 11.9, 9.055, 0, 12.25, -9.811, 0, 12.617, 7.459, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.85, 0, 0, 1.017, 2.57, 0, 1.3, -7.736, 0, 1.633, 13.411, 0, 1.967, -15.378, 0, 2.333, 13.281, 0, 2.7, -9.504, 0, 3.317, 9.421, 0, 3.667, -21.122, 0, 4.05, 25.981, 0, 4.45, -22.483, 0, 4.883, 17.037, 0, 5.317, -13.828, 0, 5.733, 10.586, 0, 6.15, -7.139, 0, 6.567, 4.791, 0, 6.95, -3.893, 0, 7.317, 7.582, 0, 7.633, -16.745, 0, 7.95, 25.872, 0, 8.283, -30, 2, 8.3, -30, 0, 8.633, 29.657, 0, 9, -26.435, 0, 9.4, 27.006, 0, 9.783, -30, 2, 9.817, -30, 0, 10.15, 30, 2, 10.233, 30, 0, 10.517, -30, 2, 10.6, -30, 0, 10.9, 27.772, 0, 11.25, -17.692, 0, 11.533, 5.739, 0, 11.767, -3.692, 0, 12.033, 10.26, 0, 12.35, -13.112, 0, 12.717, 11.216, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.45, 0.234, 0, 0.717, -0.263, 0, 0.883, 0, 2, 0.95, 0, 0, 1.117, 2.715, 0, 1.417, -8.704, 0, 1.733, 16.244, 0, 2.067, -20.004, 0, 2.433, 18.548, 0, 2.8, -14.315, 0, 3.367, 8.813, 0, 3.733, -22.879, 0, 4.1, 29.44, 0, 4.517, -26.651, 0, 4.95, 20.918, 0, 5.4, -17.239, 0, 5.817, 14.111, 0, 6.233, -10.35, 0, 6.633, 7.146, 0, 7.033, -5.551, 0, 7.4, 9.253, 0, 7.733, -19.584, 0, 8.033, 30, 0, 8.333, -30, 2, 8.417, -30, 0, 8.667, 30, 2, 8.767, 30, 0, 9.05, -30, 2, 9.1, -30, 0, 9.45, 30, 0, 9.817, -30, 2, 9.867, -30, 0, 10.2, 30, 2, 10.3, 30, 0, 10.583, -30, 2, 10.7, -30, 0, 10.95, 30, 2, 11.05, 30, 0, 11.35, -25.128, 0, 11.667, 11.032, 0, 11.9, -4.237, 0, 12.15, 11.017, 0, 12.467, -16.677, 0, 12.75, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.417, 1, 0, 3.25, 0, 0, 4.417, 1, 0, 6.25, 0, 0, 7.417, 1, 0, 9.25, 0, 0, 10.417, 1, 0, 12.25, 0, 1, 12.5, 0, 12.75, 0.413, 13, 0.708]}, {"Target": "Parameter", "Id": "fenshangxuanzhuang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 4.5, 1.702, 8.75, 3.403, 13, 5.105]}, {"Target": "Parameter", "Id": "Mail_Transparency6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamSad2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 13, 1]}, {"Target": "Parameter", "Id": "ParamCRY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.2, 0, 13, -5.2]}, {"Target": "Parameter", "Id": "Position_Coordinates", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 13, 1]}, {"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Man_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 13, 1]}, {"Target": "Parameter", "Id": "Man_TMD2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "BG_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 13, 1]}, {"Target": "Parameter", "Id": "FG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "FG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "BG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "BG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "BG_White4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "BG_White5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "BG_White2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "BG_White3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "All_Size2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "All_Size3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "All_Size4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "tuerzuo3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "faqiehuan", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "faqiehuan2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "faqiehuan4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "faqiehuan3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "tuerzuo4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "dafengshangzhuanchang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "tongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamTongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "EyeFrame_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamBrowY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamSigh", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamDarken", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamCry", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_B", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_C", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "MB_Unidirectional_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "jing<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "huqi1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "huqi2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamArmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 13, 1]}, {"Target": "Parameter", "Id": "ParamForearmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamArmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamArmL_ForearmL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamForearmL_HandL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamHandLSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "xiongjiya", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "xiongjiya5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "xiongjiya3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "xiongjiya4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "xiongjiya2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamArmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 13, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamArmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamArmR_ForearmR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamForearm<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 13, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_HandR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamHandRSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 13, 0.6]}, {"Target": "Parameter", "Id": "Change_idle_physics23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param103", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "taiqiugundong", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -22.5, 0, 13, -22.5]}, {"Target": "Parameter", "Id": "Change_idle_physics32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamLegL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 13, 1]}, {"Target": "Parameter", "Id": "ParamLegLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamCrusLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamCrusLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>oodLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamFoodLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamFoodLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamLegR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 13, 1]}, {"Target": "Parameter", "Id": "ParamLegRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamCrusRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamCrusRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamCrus<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>ood<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamFoodRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamFoodRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Physics_Provisional", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "physics_Add_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "physics_Add_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamBodyStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Physics_Body_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Physics_Body_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Physics_breast", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamBreath2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "touch_drag1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "touch_drag2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "touch_drag3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "touch_drag4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "touch_drag5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "touch_drag6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "touch_drag7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "touch_drag8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "touch_drag9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>pian<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh654", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh655", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh657", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh656", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh658", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh659", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh660", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}], "UserData": [{"Time": 0.25, "Value": ""}, {"Time": 12.5, "Value": ""}]}