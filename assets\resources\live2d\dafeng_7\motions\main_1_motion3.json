{"Version": 3, "Meta": {"Duration": 11.333, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 303, "TotalSegmentCount": 1648, "TotalPointCount": 1804, "UserDataCount": 2, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.45, 0.8, 0, 0.733, -0.2, 0, 0.933, 1, 0, 1.25, -0.3, 0, 1.717, 0.5, 0, 2, -0.2, 0, 3.083, 0.7, 0, 4.417, -0.1, 0, 5.25, 0.9, 0, 6.417, -0.1, 0, 7.567, 0.9, 0, 8.533, 0.241, 0, 9.317, 1, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY_SD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.367, 1, 0, 0.483, 0, 0, 0.667, 1, 0, 1.117, 0, 2, 1.333, 0, 0, 1.433, 1, 0, 1.633, 0, 0, 1.85, 1, 0, 1.983, 0, 0, 2.217, 1, 0, 2.35, 0, 0, 2.567, 1, 0, 2.733, 0, 0, 2.917, 1, 0, 3.117, 0, 2, 3.783, 0, 0, 3.9, 1, 0, 4.017, 0, 0, 4.2, 1, 0, 4.317, 0, 0, 4.55, 1, 0, 4.717, 0, 0, 4.9, 1, 0, 5.017, 0, 0, 5.2, 1, 0, 5.317, 0, 0, 5.5, 1, 0, 5.633, 0, 0, 5.783, 1, 0, 5.95, 0, 0, 6.067, 1, 0, 6.183, 0, 0, 6.367, 1, 0, 6.483, 0, 0, 6.717, 1, 0, 6.883, 0, 2, 7.317, 0, 0, 7.433, 1, 0, 7.717, 0, 0, 7.9, 1, 0, 8.017, 0, 0, 8.25, 1, 0, 8.417, 0, 0, 8.6, 1, 0, 8.717, 0, 0, 8.9, 1, 0, 9.017, 0, 0, 9.2, 1, 0, 9.333, 0, 0, 9.483, 1, 0, 9.65, 0, 0, 9.767, 1, 0, 9.883, 0, 0, 10.067, 1, 0, 10.417, 0, 2, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 0.65, 1, 0, 0.783, 0, 0, 0.967, 1.3, 0, 1.05, 1, 2, 1.333, 1, 0, 1.5, 0, 2, 7.083, 0, 0, 7.367, 1, 0, 7.5, 0, 0, 7.683, 1.3, 1, 7.711, 1.3, 7.739, 1.294, 7.767, 1, 1, 7.811, 0.53, 7.856, 0, 7.9, 0, 2, 8.883, 0, 2, 9.633, 0, 0, 9.917, 1.1, 0, 11.067, 1, 2, 11.333, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 7.533, 0, 0, 8.1, 1, 2, 8.883, 1, 2, 9.633, 1, 0, 9.917, 0, 2, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 0.65, 1, 0, 0.783, 0, 0, 0.967, 1.3, 0, 1.05, 1, 2, 1.333, 1, 0, 1.5, 0, 2, 7.083, 0, 0, 7.367, 1, 0, 7.5, 0, 0, 7.683, 1.3, 1, 7.711, 1.3, 7.739, 1.294, 7.767, 1, 1, 7.811, 0.53, 7.856, 0, 7.9, 0, 2, 8.883, 0, 2, 9.633, 0, 0, 9.917, 1.3, 0, 11.067, 1, 2, 11.333, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 7.533, 0, 0, 8.1, 1, 2, 8.883, 1, 2, 9.633, 1, 0, 9.917, 0, 2, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.5, -1, 1, 3.356, -1, 5.211, -0.932, 7.067, -0.7, 1, 7.395, -0.659, 7.722, 0, 8.05, 0, 2, 8.867, 0, 2, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 8.417, 0, 0, 8.867, 1, 2, 9.933, 1, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamEyeLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.65, 0, 0, 0.717, 1, 2, 0.867, 1, 0, 0.883, -1, 2, 1.017, -1, 0, 1.317, 0.244, 0, 1.333, 0.24, 0, 1.417, 1, 2, 1.567, 1, 0, 1.817, -0.33, 0, 2.133, 0.092, 0, 2.45, -0.026, 0, 2.75, 0.007, 0, 3.05, -0.002, 2, 3.083, -0.002, 0, 3.3, 0, 2, 3.317, 0, 2, 3.333, 0, 2, 3.35, 0, 0, 3.367, 0.001, 2, 3.383, 0.001, 0, 3.4, 0, 2, 3.433, 0, 2, 3.45, 0, 2, 3.467, 0, 2, 3.5, 0, 2, 3.517, 0, 2, 3.55, 0, 2, 3.583, 0, 2, 3.6, 0, 2, 3.65, 0, 2, 3.667, 0, 2, 3.7, 0, 2, 3.717, 0, 2, 3.817, 0, 2, 3.833, 0, 2, 7.083, 0, 0, 7.233, -1, 2, 7.317, -1, 0, 7.45, 1, 2, 7.567, 1, 0, 7.583, -1, 2, 7.683, -1, 0, 7.783, 1, 2, 8.133, 1, 0, 8.367, -0.384, 0, 8.667, 0.105, 2, 8.683, 0.105, 0, 8.983, -0.03, 0, 9.3, 0.008, 0, 9.583, -0.002, 2, 9.617, -0.002, 2, 9.633, -0.002, 0, 9.767, -1, 2, 9.883, -1, 0, 10.117, 0.445, 0, 10.417, -0.094, 0, 10.717, 0.049, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamEyeRsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.65, 0, 0, 0.717, 1, 2, 0.867, 1, 0, 0.883, -1, 2, 1.017, -1, 0, 1.317, 0.244, 0, 1.333, 0.24, 0, 1.417, 1, 2, 1.567, 1, 0, 1.817, -0.33, 0, 2.133, 0.092, 0, 2.45, -0.026, 0, 2.75, 0.007, 0, 3.05, -0.002, 2, 3.083, -0.002, 0, 3.3, 0, 2, 3.317, 0, 2, 3.333, 0, 2, 3.35, 0, 0, 3.367, 0.001, 2, 3.383, 0.001, 0, 3.4, 0, 2, 3.433, 0, 2, 3.45, 0, 2, 3.467, 0, 2, 3.5, 0, 2, 3.517, 0, 2, 3.55, 0, 2, 3.583, 0, 2, 3.6, 0, 2, 3.65, 0, 2, 3.667, 0, 2, 3.7, 0, 2, 3.717, 0, 2, 3.817, 0, 2, 3.833, 0, 2, 7.083, 0, 0, 7.233, -1, 2, 7.317, -1, 0, 7.45, 1, 2, 7.567, 1, 0, 7.583, -1, 2, 7.683, -1, 0, 7.783, 1, 2, 8.133, 1, 0, 8.367, -0.384, 0, 8.667, 0.105, 2, 8.683, 0.105, 0, 8.983, -0.03, 0, 9.3, 0.008, 0, 9.583, -0.002, 2, 9.617, -0.002, 2, 9.633, -0.002, 0, 9.75, -1, 2, 9.9, -1, 0, 10.117, 0.492, 0, 10.433, -0.051, 0, 10.7, 0.084, 2, 10.717, 0.084, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamEyeLGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.65, 0, 0, 0.717, -0.305, 0, 0.767, -0.2, 0, 0.867, -1, 2, 0.883, -1, 0, 0.9, 1, 2, 0.933, 1, 0, 0.967, 0.831, 2, 0.983, 0.831, 0, 1.017, 0.863, 0, 1.267, -0.314, 0, 1.35, -0.211, 0, 1.4, -0.273, 0, 1.65, 0.814, 0, 1.983, -0.307, 0, 2.3, 0.124, 0, 2.6, -0.047, 0, 2.9, 0.017, 0, 3.2, -0.006, 2, 3.217, -0.006, 0, 3.5, 0.002, 2, 3.55, 0.002, 0, 3.8, -0.001, 2, 3.867, -0.001, 0, 3.883, 0, 2, 3.9, 0, 2, 3.933, 0, 2, 3.95, 0, 2, 3.983, 0, 2, 4, 0, 2, 4.017, 0, 2, 4.033, 0, 2, 4.05, 0, 2, 4.067, 0, 2, 4.083, 0, 2, 4.2, 0, 2, 4.217, 0, 2, 4.267, 0, 2, 4.283, 0, 2, 4.383, 0, 2, 4.4, 0, 2, 4.5, 0, 2, 4.517, 0, 2, 7.083, 0, 0, 7.2, 0.258, 0, 7.417, -0.845, 0, 7.483, -0.489, 0, 7.55, -1, 2, 7.567, -1, 0, 7.583, 1, 2, 7.6, 1, 0, 7.75, -1, 2, 7.767, -1, 0, 7.883, -0.322, 0, 7.983, -0.451, 0, 8.25, 0.294, 0, 8.517, -0.251, 0, 8.817, 0.117, 2, 8.833, 0.117, 0, 9.133, -0.047, 0, 9.433, 0.017, 0, 9.633, -0.001, 0, 9.75, 0.262, 0, 9.983, -0.658, 0, 10.283, 0.32, 0, 10.583, -0.137, 0, 10.883, 0.056, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamEyeRGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.65, 0, 0, 0.717, -0.305, 0, 0.767, -0.2, 0, 0.867, -1, 2, 0.883, -1, 0, 0.9, 1, 2, 0.933, 1, 0, 0.983, 0.831, 0, 1.017, 0.863, 0, 1.267, -0.314, 0, 1.35, -0.211, 0, 1.4, -0.273, 0, 1.65, 0.814, 0, 1.983, -0.307, 0, 2.3, 0.124, 0, 2.6, -0.047, 0, 2.9, 0.017, 0, 3.2, -0.006, 2, 3.217, -0.006, 0, 3.5, 0.002, 2, 3.55, 0.002, 0, 3.8, -0.001, 2, 3.867, -0.001, 0, 3.883, 0, 2, 3.9, 0, 2, 3.933, 0, 2, 3.95, 0, 2, 3.983, 0, 2, 4, 0, 2, 4.017, 0, 2, 4.033, 0, 2, 4.05, 0, 2, 4.067, 0, 2, 4.083, 0, 2, 4.2, 0, 2, 4.217, 0, 2, 4.267, 0, 2, 4.283, 0, 2, 4.383, 0, 2, 4.4, 0, 2, 4.5, 0, 2, 4.517, 0, 2, 7.083, 0, 0, 7.2, 0.258, 0, 7.417, -0.845, 0, 7.483, -0.489, 0, 7.55, -1, 2, 7.567, -1, 0, 7.583, 1, 2, 7.6, 1, 0, 7.75, -1, 2, 7.767, -1, 0, 7.883, -0.322, 0, 7.983, -0.451, 0, 8.25, 0.294, 0, 8.517, -0.251, 0, 8.817, 0.117, 2, 8.833, 0.117, 0, 9.133, -0.047, 0, 9.433, 0.017, 0, 9.633, -0.001, 0, 9.733, 0.273, 0, 9.983, -0.806, 0, 10.3, 0.332, 0, 10.6, -0.138, 0, 10.9, 0.061, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.333, 0, 2, 1.417, 0, 2, 1.433, 0, 0, 1.45, -5, 2, 3.55, -5, 1, 3.556, -5, 3.561, 0, 3.567, 0, 2, 7.133, 0, 2, 7.15, 0, 2, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.333, 0, 2, 1.417, 0, 2, 1.433, 0, 0, 1.45, -5, 2, 3.55, -5, 2, 3.567, -5, 2, 7.133, -5, 0, 7.15, 0, 2, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 7.733, 0, 1, 8.511, 0.667, 9.289, 1.333, 10.067, 2, 1, 10.4, 1.333, 10.734, 0.667, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_B", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 7.983, 0, 0, 10.317, 2, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_C", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 7.867, 0, 0, 10.2, 2, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "MB_Unidirectional_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 7.733, 0, 0, 8.233, 1, 2, 9.567, 1, 1, 9.761, 1, 9.956, 1.315, 10.15, 2, 1, 10.156, 2.02, 10.161, 10, 10.167, 10, 2, 10.3, 10, 2, 10.317, 0, 2, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.3, 0, 2, 3.367, 0, 2, 7.183, 0, 2, 7.65, 0, 1, 7.667, 0, 7.683, 0.172, 7.7, 0.246, 1, 7.822, 0.786, 7.945, 1, 8.067, 1, 2, 10.15, 1, 0, 10.617, 0, 2, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.917, 3.96, 0, 1.2, 0, 2, 3.367, 0, 2, 7.183, 0, 2, 7.65, 0, 1, 7.661, 0, 7.672, 0.532, 7.683, 1.071, 1, 7.722, 2.956, 7.761, 3.72, 7.8, 3.72, 0, 8.067, 0, 2, 10.15, 0, 2, 10.617, 0, 2, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 30, 2, 0.25, 30, 2, 0.417, 30, 1, 0.545, 30, 0.672, 29.986, 0.8, 26.59, 1, 0.906, 23.785, 1.011, 8.505, 1.117, 2.292, 1, 1.178, -1.305, 1.239, -1.64, 1.3, -1.64, 2, 7.417, -1.64, 0, 7.65, 0, 2, 10.15, 0, 1, 10.306, 0, 10.461, 14.108, 10.617, 22, 1, 10.767, 29.61, 10.917, 30, 11.067, 30, 2, 11.333, 30]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.617, 0, 0, 0.9, -30, 1, 0.944, -30, 0.989, -29.557, 1.033, -22, 1, 1.072, -15.388, 1.111, 0, 1.15, 0, 2, 10.15, 0, 0, 10.617, -30, 0, 10.833, 0, 2, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1, 2, 0.25, -1, 2, 0.983, -1, 0, 1, 1, 2, 1.3, 1, 2, 3.367, 1, 2, 7.183, 1, 2, 7.65, 1, 2, 8.067, 1, 2, 10.15, 1, 2, 10.6, 1, 0, 10.617, -1, 2, 11.067, -1, 2, 11.333, -1]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.617, 0, 1, 0.678, 0, 0.739, -0.252, 0.8, -0.3, 1, 0.878, -0.361, 0.955, -0.357, 1.033, -0.357, 0, 1.4, 0, 2, 3.367, 0, 2, 7.183, 0, 2, 7.65, 0, 2, 8.067, 0, 2, 10.15, 0, 2, 10.617, 0, 2, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamHandLDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10, 2, 0.25, 10, 2, 0.917, 10, 2, 0.933, 0, 2, 7.65, 0, 2, 7.667, 10, 2, 11.067, 10, 2, 11.333, 10]}, {"Target": "Parameter", "Id": "XIONGJIYA2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 0.306, 0, 0.361, 0.335, 0.417, 0.4, 1, 0.795, 0.844, 1.172, 1, 1.55, 1, 2, 10.583, 1, 1, 10.611, 1, 10.639, 0.643, 10.667, 0.5, 1, 10.745, 0.099, 10.822, 0, 10.9, 0, 2, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.417, 0, 0, 0.583, 1, 0, 0.7, 0, 2, 10.8, 0, 2, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.3, 0, 2, 3.367, 0, 2, 7.183, 0, 2, 7.65, 0, 1, 7.667, 0, 7.683, 0.172, 7.7, 0.246, 1, 7.822, 0.786, 7.945, 1, 8.067, 1, 2, 10.15, 1, 0, 10.617, 0, 2, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.433, 0, 2, 0.583, 0, 0, 1.3, 22.1, 2, 3.367, 22.1, 2, 7.183, 22.1, 1, 7.339, 22.1, 7.494, 6.078, 7.65, 0, 1, 7.7, -1.953, 7.75, -1.14, 7.8, -1.14, 0, 8.067, 0, 2, 10.15, 0, 1, 10.289, -0.98, 10.428, -1.96, 10.567, -2.94, 1, 10.584, -1.96, 10.6, -0.98, 10.617, 0, 2, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.867, 0.5, 2, 7.55, 0.5, 0, 10.617, 1, 0, 10.85, 0, 2, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.583, 0, 0, 1.3, 3.8, 2, 7.417, 3.8, 1, 7.484, 3.8, 7.55, 2.98, 7.617, 0.93, 1, 7.628, 0.588, 7.639, 0, 7.65, 0, 2, 10.15, 0, 1, 10.289, -1.16, 10.428, -2.32, 10.567, -3.48, 1, 10.584, -2.32, 10.6, -1.16, 10.617, 0, 2, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.3, -6.2, 2, 3.367, -6.2, 2, 7.183, -6.2, 1, 7.339, -4.133, 7.494, -2.067, 7.65, 0, 2, 8.067, 0, 2, 10.15, 0, 2, 10.617, 0, 2, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.5, 0, 0, 0.833, -0.016, 0, 1.3, 0.1, 2, 3.367, 0.1, 2, 7.117, 0.1, 2, 7.4, 0.1, 0, 7.65, 0, 2, 8.067, 0, 2, 10.15, 0, 2, 10.617, 0, 2, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamHandRDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 7.65, 0, 2, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 7.167, 0, 2, 7.567, 0, 2, 7.583, 1, 2, 10.6, 1, 2, 10.617, 0, 2, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 7.167, 0, 2, 7.567, 0, 2, 7.583, 1, 2, 10.6, 1, 2, 10.617, 0, 2, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 7.583, -0.127, 1, 7.744, -0.085, 7.906, -0.042, 8.067, 0, 2, 10.15, 0, 1, 10.306, -0.015, 10.461, -0.029, 10.617, -0.044, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 7.583, -0.059, 1, 7.744, -0.039, 7.906, -0.02, 8.067, 0, 2, 10.15, 0, 1, 10.306, 0.004, 10.461, 0.008, 10.617, 0.012, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 7.583, 0.209, 1, 7.744, 0.139, 7.906, 0.07, 8.067, 0, 2, 10.15, 0, 1, 10.306, 0.029, 10.461, 0.057, 10.617, 0.086, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -2.728, 0, 0.617, 5.594, 0, 1.133, -8, 2, 1.8, -8, 0, 2.433, -6, 0, 2.817, -20.733, 1, 2.961, -20.733, 3.106, -10.603, 3.25, 5.998, 1, 3.306, 12.383, 3.361, 14.174, 3.417, 14.174, 1, 3.539, 14.174, 3.661, -10.305, 3.783, -19.801, 1, 3.878, -27.138, 3.972, -26.593, 4.067, -26.593, 0, 4.483, 1.177, 1, 4.544, 1.177, 4.606, 1.473, 4.667, -0.87, 1, 4.767, -4.704, 4.867, -12.086, 4.967, -12.086, 0, 5.45, 6.052, 0, 6, -15, 0, 6.333, 3.794, 1, 6.511, 3.794, 6.689, -7.752, 6.867, -13.183, 1, 7.1, -20.311, 7.334, -21, 7.567, -21, 0, 8.017, 18, 1, 8.15, 18, 8.284, 18.046, 8.417, 17.501, 1, 8.511, 17.115, 8.606, 10.718, 8.7, 10.718, 0, 9.033, 18.823, 0, 9.417, 11.994, 0, 9.65, 16.223, 1, 9.811, 16.223, 9.972, 16.316, 10.133, 15, 1, 10.261, 13.957, 10.389, 4.363, 10.517, 0, 1, 10.611, -3.225, 10.706, -4.14, 10.8, -4.14, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.9, 0, 0, 2.283, -2, 0, 2.6, 0, 0, 3.017, -1.404, 0, 3.45, 0, 2, 5.1, 0, 0, 6.467, -0.96, 0, 8.8, 0, 2, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -7.795, 2, 0.25, -7.795, 1, 0.75, -7.795, 1.25, -5.197, 1.75, 0, 1, 2.25, 5.197, 2.75, 7.795, 3.25, 7.795, 1, 3.75, 7.795, 4.25, 5.197, 4.75, 0, 1, 5.25, -5.197, 5.75, -7.795, 6.25, -7.795, 1, 6.75, -7.795, 7.25, -5.197, 7.75, 0, 1, 8.25, 5.197, 8.75, 7.795, 9.25, 7.795, 0, 10.25, -7.795, 2, 11.067, -7.795, 2, 11.333, -7.795]}, {"Target": "Parameter", "Id": "ParamBodyStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 0.294, 0, 0.339, 4.872, 0.383, 6.338, 1, 0.478, 9.454, 0.572, 10, 0.667, 10, 0, 1.083, -3, 0, 1.333, 0, 0, 1.633, -1.92, 0, 2.183, 2.409, 0, 2.683, 0, 0, 3.033, 10, 1, 3.161, 10, 3.289, 10, 3.417, 7.281, 1, 3.578, 3.273, 3.739, -10, 3.9, -10, 0, 4.233, -3, 0, 4.667, -9, 0, 5.033, -4.543, 0, 5.417, -7, 0, 6.017, 0.25, 0, 7.117, -8, 0, 8.283, 10, 0, 9.15, 9, 0, 9.55, 10, 1, 9.839, 10, 10.128, 5.36, 10.417, 2.272, 1, 10.634, -0.044, 10.85, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.517, 4.859, 0, 0.883, -1.073, 0, 1.483, 2.445, 0, 2.017, -0.586, 0, 2.533, 0.603, 0, 4.967, -7, 0, 6.467, 6, 0, 8, -4, 0, 9.583, 4.114, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamLegLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 2.383, 14.515, 0, 3.433, -9.742, 1, 3.783, -9.742, 4.133, -5.187, 4.483, 0, 1, 5.2, 10.622, 5.916, 14.515, 6.633, 14.515, 0, 8.033, 2.532, 0, 9.5, 16.605, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamLegRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.35, 7.732, 0, 3.483, -9.756, 1, 3.816, -9.756, 4.15, -5.346, 4.483, 0, 1, 4.855, 5.97, 5.228, 7.732, 5.6, 7.732, 0, 7.75, -9.756, 0, 9.533, 17.886, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.417, 0.5, 2, 0.583, 0.5, 0, 0.6, 1, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "Physics_AngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.317, 0, 0, 0.433, -0.923, 0, 0.683, 3.649, 0, 1.183, -5.348, 0, 1.567, -4.571, 0, 1.817, -4.604, 0, 2.433, -3.381, 0, 2.867, -12.9, 0, 3.433, 10.034, 0, 3.983, -17.125, 0, 4.55, 2.536, 0, 5, -7.853, 0, 5.483, 4.473, 0, 6.017, -9.645, 0, 6.4, 3.573, 0, 7.5, -12.195, 0, 8.067, 12.756, 0, 8.75, 5.75, 0, 9.083, 11.383, 0, 9.45, 6.475, 0, 9.767, 9.724, 0, 10.767, -2.957, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 1.451, 0, 0.583, -3.116, 0, 0.917, 5.762, 0, 1.3, -5.589, 0, 1.717, 3.556, 0, 2.133, -2.602, 0, 2.667, 5.563, 0, 3.083, -10.174, 0, 3.617, 17.877, 0, 4.033, -17.153, 0, 4.583, 12.642, 0, 5.1, -11.214, 0, 5.6, 11.43, 0, 6.15, -11.915, 0, 6.55, 14.027, 0, 6.983, -9.041, 0, 7.4, 5.135, 0, 7.833, -13.182, 0, 8.217, 14.284, 0, 8.633, -6.662, 0, 9.217, 6.781, 0, 9.617, -7.499, 0, 10, 5.223, 0, 10.783, -2.133, 0, 10.8, -2.129, 2, 10.817, -2.129, 0, 10.917, -2.326, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.35, -3.534, 0, 0.55, 4.679, 0, 0.783, -8.882, 0, 1.133, 15.461, 0, 1.517, -12.062, 0, 1.9, 8.428, 0, 2.333, -5.812, 0, 2.883, 15.548, 0, 3.35, -26.538, 0, 3.717, 30, 2, 3.933, 30, 0, 4.2, -30, 2, 4.333, -30, 0, 4.833, 24.442, 0, 5.333, -24.689, 0, 5.817, 25.069, 0, 6.367, -29.317, 0, 6.75, 30, 2, 6.8, 30, 0, 7.2, -17.238, 0, 7.65, 12.318, 0, 7.983, -30, 2, 8.117, -30, 0, 8.417, 30, 2, 8.433, 30, 0, 8.9, -13.038, 0, 9.433, 15.362, 0, 9.817, -16.451, 0, 10.2, 11.128, 0, 10.533, 2.786, 0, 10.65, 3.039, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_L", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.3, -0.672, 0, 0.583, 1.342, 0, 0.6, 1.277, 0, 0.617, 1.305, 0, 1.133, -1.867, 2, 1.8, -1.867, 0, 2.433, -1.4, 0, 2.667, -3.475, 0, 2.933, 6.705, 0, 3.417, -8.341, 0, 3.883, 12.052, 0, 4.4, -8.964, 0, 4.9, 7.702, 0, 5.4, -7.769, 0, 5.883, 7.543, 0, 6.4, -9.283, 0, 6.833, 7.838, 0, 7.3, -4.298, 0, 7.733, 3.996, 0, 8.083, -9.17, 0, 8.483, 5.493, 0, 9.1, -4.206, 0, 9.5, 4.754, 0, 9.867, -3.861, 0, 10.533, 2.593, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_R", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.3, 0.672, 0, 0.583, -1.342, 0, 0.6, -1.277, 0, 0.617, -1.305, 0, 1.133, 1.867, 2, 1.8, 1.867, 0, 2.433, 1.4, 0, 2.667, 3.475, 0, 2.933, -6.705, 0, 3.417, 8.341, 0, 3.883, -12.052, 0, 4.4, 8.964, 0, 4.9, -7.702, 0, 5.4, 7.769, 0, 5.883, -7.543, 0, 6.4, 9.283, 0, 6.833, -7.838, 0, 7.3, 4.298, 0, 7.733, -3.996, 0, 8.083, 9.17, 0, 8.483, -5.493, 0, 9.1, 4.206, 0, 9.5, -4.754, 0, 9.867, 3.861, 0, 10.533, -2.593, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 3.637, 0, 0.617, -7.459, 0, 1.133, 10.668, 2, 1.8, 10.668, 0, 2.433, 8, 0, 2.65, 17.446, 0, 3.117, -13.163, 0, 3.667, 20.614, 0, 4.05, -7.257, 0, 4.083, -7.125, 0, 4.317, -12.986, 0, 4.767, 8.95, 0, 5.217, -10.149, 0, 5.7, 9.785, 0, 6.217, -14.186, 0, 6.583, 9.227, 0, 7.083, 0.06, 0, 7.283, 0.323, 0, 7.85, -18.318, 0, 8.183, 8.085, 0, 8.45, -0.29, 0, 8.65, 3.121, 0, 8.933, -6.35, 0, 9.267, 5.972, 0, 9.6, -5.56, 0, 9.933, 1.857, 0, 10.133, 0.947, 0, 10.417, 6.48, 0, 10.967, -3.864, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -3.637, 0, 0.617, 7.459, 0, 1.133, -10.668, 2, 1.8, -10.668, 0, 2.433, -8, 0, 2.617, -15.983, 0, 2.833, 16.933, 0, 3.1, -6.415, 0, 3.283, 0.206, 0, 3.467, -8.043, 0, 3.817, 9.287, 0, 4.1, -4.941, 0, 4.283, 2.809, 0, 4.483, -7.147, 0, 5, 3.66, 0, 5.367, -4.115, 0, 5.75, 2.424, 0, 5.883, 0.281, 0, 6.117, 4.693, 0, 6.35, -9.049, 0, 6.65, 5.046, 0, 6.933, -0.874, 0, 7.417, 0.203, 0, 7.567, 0.055, 0, 7.733, 6.555, 0, 8.033, -11.58, 0, 8.283, 8.268, 0, 8.567, -5.064, 0, 8.817, 5.466, 0, 9.083, -6.024, 0, 9.4, 4.471, 0, 9.717, -4.918, 0, 9.983, 2.286, 0, 10.283, -2.672, 0, 10.583, 3.164, 0, 10.617, 2.983, 0, 10.65, 3.233, 0, 10.867, -0.801, 1, 10.9, -0.801, 10.934, -0.712, 10.967, -0.449, 1, 11, -0.186, 11.034, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -3.637, 0, 0.617, 7.459, 0, 1.133, -10.668, 2, 1.8, -10.668, 0, 2.433, -8.001, 0, 2.75, -25.676, 0, 3.267, 22.831, 0, 3.717, -27.964, 0, 4.333, 20.479, 0, 4.817, -13.562, 0, 5.283, 13.647, 0, 5.767, -13.671, 0, 6.25, 18.006, 0, 6.683, -11.72, 0, 7.233, 0.291, 0, 7.483, 0.068, 0, 7.883, 23.442, 0, 8.283, -7.074, 0, 8.483, -3.999, 0, 8.617, -4.827, 0, 8.95, 7.842, 0, 9.333, -6.596, 0, 9.633, 4.926, 0, 10.45, -8.725, 0, 11, 5.509, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 3.637, 0, 0.617, -7.459, 0, 1.133, 10.668, 2, 1.8, 10.668, 0, 2.433, 8.001, 0, 2.733, 25.27, 0, 2.983, -30, 0, 3.383, 13.564, 0, 3.9, -15.927, 0, 4.183, 1.119, 0, 4.283, 0.272, 0, 4.55, 11.182, 0, 4.933, -7.546, 0, 5.5, 8.477, 0, 5.983, -6.679, 0, 6, -6.663, 0, 6.117, -8.08, 0, 6.417, 14.082, 0, 6.8, -10.063, 0, 7.15, 2.713, 0, 7.483, -0.668, 0, 7.567, -0.493, 0, 7.767, -9.619, 0, 8.083, 14.991, 0, 8.4, -7.81, 0, 8.633, 2.536, 0, 8.867, -5.055, 0, 9.15, 7.515, 0, 9.517, -7.486, 0, 9.8, 5.359, 0, 10.133, -1.55, 0, 10.35, 2.917, 0, 10.667, -4.401, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "Jewelry_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 0.232, 0, 0.583, -0.65, 0, 0.933, 0.986, 0, 1.317, -0.931, 0, 1.733, 0.592, 0, 2.15, -0.432, 0, 2.667, 0.94, 0, 3.083, -1.699, 0, 3.617, 2.962, 0, 4.033, -2.819, 0, 4.583, 2.099, 0, 5.1, -1.869, 0, 5.6, 1.903, 0, 6.15, -1.981, 0, 6.55, 2.321, 0, 6.983, -1.49, 0, 7.4, 0.843, 0, 7.833, -2.184, 0, 8.217, 2.35, 0, 8.633, -1.083, 0, 9.217, 1.136, 0, 9.617, -1.25, 0, 10.017, 0.867, 0, 10.783, -0.359, 0, 10.817, -0.357, 0, 10.917, -0.386, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "Jewelry_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.35, -0.276, 0, 0.533, 0.667, 0, 0.783, -1.594, 0, 1.15, 2.594, 0, 1.533, -1.99, 0, 1.933, 1.277, 0, 2.35, -0.97, 0, 2.867, 2.589, 0, 3.35, -4.235, 0, 3.833, 6.56, 0, 4.267, -4.935, 0, 4.833, 3.92, 0, 5.333, -3.971, 0, 5.817, 4.012, 0, 6.367, -4.668, 0, 6.767, 4.843, 0, 7.2, -2.736, 0, 7.65, 2.014, 0, 8.05, -5.737, 0, 8.417, 4.697, 0, 8.867, -1.916, 0, 9.433, 2.545, 0, 9.817, -2.702, 0, 10.2, 1.822, 0, 10.55, 0.473, 0, 10.633, 0.493, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "Jewelry_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.45, -0.341, 0, 0.683, 0.911, 0, 0.967, -1.854, 0, 1.3, 2.345, 0, 1.65, -1.968, 0, 2.05, 1.304, 0, 2.467, -0.869, 0, 3.05, 2.331, 0, 3.517, -2.959, 0, 3.967, 4.5, 0, 4.367, -3.132, 0, 4.983, 2.407, 0, 5.483, -2.628, 0, 5.95, 2.58, 0, 6.517, -3.394, 0, 6.9, 3.698, 0, 7.3, -2.582, 0, 7.8, 2.438, 0, 8.183, -4.567, 0, 8.55, 4.204, 0, 8.9, -1.841, 0, 9.167, -0.625, 0, 9.233, -0.64, 0, 9.617, 2.122, 0, 9.967, -2.495, 0, 10.317, 1.571, 0, 10.617, -0.418, 0, 10.9, 0.441, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.417, -0.862, 0, 0.883, 1.672, 0, 1.267, -2.015, 0, 1.65, 1.364, 0, 2.05, -1.001, 0, 2.467, 0.849, 0, 2.933, -1.538, 0, 3.35, 1.218, 0, 4.05, -1.73, 0, 4.5, 1.745, 0, 4.917, -1.516, 0, 5.317, 1.087, 0, 5.733, -0.828, 0, 6.117, 0.512, 0, 7.383, -0.284, 0, 7.6, -0.191, 0, 7.817, -0.403, 0, 8.217, 0.626, 0, 8.733, -0.286, 0, 9.2, 0.297, 0, 9.583, -0.302, 0, 9.967, 0.374, 0, 10.4, -0.026, 0, 10.417, -0.025, 0, 10.433, -0.026, 2, 10.45, -0.026, 0, 10.917, -0.136, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.35, 0.472, 0, 0.65, -2.269, 0, 1.083, 3.869, 0, 1.467, -3.846, 0, 1.85, 2.754, 0, 2.25, -2.264, 0, 2.683, 1.877, 0, 3.117, -3.451, 0, 3.8, 2.412, 0, 4.267, -3.569, 0, 4.7, 3.466, 0, 5.117, -3.07, 0, 5.517, 2.197, 0, 5.933, -1.836, 0, 6.317, 0.894, 0, 6.617, 0.252, 0, 6.9, 0.483, 0, 7.6, -0.722, 0, 7.733, -0.632, 0, 8.05, -1.56, 0, 8.45, 1.189, 0, 8.983, -0.547, 0, 9.417, 0.653, 0, 9.767, -0.721, 0, 10.167, 0.933, 0, 10.517, 0.253, 0, 10.583, 0.266, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 0.695, 0, 0.583, -1.951, 0, 0.933, 2.959, 0, 1.317, -2.794, 0, 1.733, 1.777, 0, 2.15, -1.297, 0, 2.667, 2.82, 0, 3.083, -5.098, 0, 3.617, 8.886, 0, 4.033, -8.458, 0, 4.583, 6.297, 0, 5.1, -5.606, 0, 5.6, 5.71, 0, 6.15, -5.943, 0, 6.55, 6.962, 0, 6.983, -4.47, 0, 7.4, 2.529, 0, 7.833, -6.551, 0, 8.217, 7.051, 0, 8.633, -3.251, 0, 9.217, 3.407, 0, 9.617, -3.75, 0, 10.017, 2.601, 0, 10.783, -1.077, 0, 10.817, -1.072, 0, 10.917, -1.159, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.35, -0.827, 0, 0.533, 2, 0, 0.783, -4.782, 0, 1.15, 7.78, 0, 1.533, -5.97, 0, 1.933, 3.832, 0, 2.35, -2.91, 0, 2.867, 7.766, 0, 3.35, -12.705, 0, 3.833, 19.681, 0, 4.267, -14.804, 0, 4.833, 11.76, 0, 5.333, -11.912, 0, 5.817, 12.035, 0, 6.367, -14.004, 0, 6.767, 14.528, 0, 7.2, -8.206, 0, 7.65, 6.043, 0, 8.05, -17.211, 0, 8.417, 14.092, 0, 8.867, -5.748, 0, 9.433, 7.636, 0, 9.817, -8.106, 0, 10.2, 5.465, 0, 10.55, 1.421, 0, 10.633, 1.478, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.45, -1.023, 0, 0.683, 2.733, 0, 0.967, -5.563, 0, 1.3, 7.033, 0, 1.65, -5.905, 0, 2.05, 3.911, 0, 2.467, -2.606, 0, 3.05, 6.994, 0, 3.517, -8.878, 0, 3.967, 13.5, 0, 4.367, -9.395, 0, 4.983, 7.22, 0, 5.483, -7.883, 0, 5.95, 7.739, 0, 6.517, -10.181, 0, 6.9, 11.095, 0, 7.3, -7.747, 0, 7.8, 7.313, 0, 8.183, -13.701, 0, 8.55, 12.613, 0, 8.9, -5.522, 0, 9.167, -1.875, 0, 9.233, -1.919, 0, 9.617, 6.365, 0, 9.967, -7.484, 0, 10.317, 4.714, 0, 10.617, -1.253, 0, 10.9, 1.323, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.267, 0.898, 0, 0.55, -1.072, 0, 0.8, 2.628, 0, 1.083, -6.432, 0, 1.4, 9.303, 0, 1.767, -8.704, 0, 2.133, 6.282, 0, 2.533, -4.032, 0, 3.15, 7.788, 0, 3.567, -10.877, 0, 4.05, 15.766, 0, 4.45, -13.289, 0, 5.067, 7.932, 0, 5.567, -9.571, 0, 6.033, 9.733, 0, 6.6, -10.921, 0, 7, 14.29, 0, 7.383, -11.209, 0, 7.833, 9.154, 0, 8.283, -15.981, 0, 8.65, 17, 0, 9, -9.862, 0, 9.35, 0.31, 0, 9.4, 0.269, 0, 9.717, 6.863, 0, 10.067, -9.795, 0, 10.417, 7.431, 0, 10.733, -3.255, 0, 11.033, 2.311, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.367, 1.035, 0, 0.65, -1.189, 0, 0.9, 2.636, 0, 1.2, -7.313, 0, 1.517, 11.803, 0, 1.867, -12.288, 0, 2.233, 9.678, 0, 2.617, -6.419, 0, 3.233, 8.285, 0, 3.633, -13.361, 0, 4.117, 17.591, 0, 4.533, -17.02, 0, 5.117, 7.986, 0, 5.633, -11.317, 0, 6.1, 11.857, 0, 6.65, -11.519, 0, 7.083, 17.155, 0, 7.483, -15.306, 0, 7.9, 12.443, 0, 8.35, -17.93, 0, 8.733, 21.261, 0, 9.1, -15.032, 0, 9.45, 3.814, 0, 9.6, 2.453, 0, 9.817, 6.527, 0, 10.167, -12.08, 0, 10.517, 10.827, 0, 10.85, -6.048, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.467, 1.189, 0, 0.767, -1.351, 0, 1.017, 2.695, 0, 1.3, -8.27, 0, 1.617, 14.565, 0, 1.967, -16.569, 0, 2.317, 14.187, 0, 2.7, -10.087, 0, 3.317, 8.317, 0, 3.7, -15.771, 0, 4.167, 19.07, 0, 4.6, -20.061, 0, 5.033, 9.73, 0, 5.7, -12.827, 0, 6.167, 14.079, 0, 6.7, -12.341, 0, 7.15, 19.349, 0, 7.55, -19.461, 0, 7.967, 16.671, 0, 8.417, -19.741, 0, 8.817, 25.096, 0, 9.2, -20.534, 0, 9.55, 8.531, 0, 9.783, 2.963, 0, 9.95, 5.174, 0, 10.267, -14.04, 0, 10.617, 14.762, 0, 10.967, -9.858, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.55, 1.387, 0, 0.867, -1.599, 0, 1.117, 2.769, 0, 1.417, -9.156, 0, 1.717, 17.487, 0, 2.05, -21.34, 0, 2.417, 19.73, 0, 2.8, -15.07, 0, 3.317, 7.915, 0, 3.767, -17.776, 0, 4.2, 20.223, 0, 4.667, -21.864, 0, 5.1, 12.969, 0, 5.783, -13.886, 0, 6.233, 16.241, 0, 6.717, -13.675, 0, 7.217, 20.654, 0, 7.633, -23.134, 0, 8.033, 21.138, 0, 8.467, -21.488, 0, 8.883, 27.494, 0, 9.283, -25.346, 0, 9.65, 13.969, 0, 9.933, 0.987, 0, 10.083, 2.987, 0, 10.367, -15.467, 0, 10.717, 19.027, 0, 11.067, 0, 2, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.417, 1, 0, 3.25, 0, 0, 4.417, 1, 0, 6.25, 0, 0, 7.417, 1, 0, 9.25, 0, 0, 10.417, 1, 1, 10.722, 1, 11.028, 0.75, 11.333, 0.5]}, {"Target": "Parameter", "Id": "fenshangxuanzhuang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 3.944, 1.479, 7.639, 2.958, 11.333, 4.437]}, {"Target": "Parameter", "Id": "Mail_Transparency6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamSad2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "Parameter", "Id": "ParamCRY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.2, 0, 11.333, -5.2]}, {"Target": "Parameter", "Id": "Position_Coordinates", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Man_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "Parameter", "Id": "Man_TMD2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "BG_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "Parameter", "Id": "FG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "FG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "BG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "BG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "BG_White4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "BG_White5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "BG_White2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "BG_White3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "All_Size2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "All_Size3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "All_Size4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "tuerzuo3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "faqiehuan", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "faqiehuan2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "faqiehuan4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "faqiehuan3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "tuerzuo4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Mail_Change", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "dafengshangzhuanchang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "tongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamTongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "EyeFrame_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamSigh", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamDarken", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamCry", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "jing<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "huqi1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "huqi2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamShoulderLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamArmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "Parameter", "Id": "ParamForearmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamArmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamArmL_ForearmL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamForearmL_HandL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamHandLSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "xiongjiya", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "xiongjiya5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "xiongjiya3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "xiongjiya4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "xiongjiya2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamShoulderRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamArmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamArmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamArmR_ForearmR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamForearm<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_HandR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamHandRSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 11.333, 0.6]}, {"Target": "Parameter", "Id": "Change_idle_physics23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Param103", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "taiqiugundong", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -22.5, 0, 11.333, -22.5]}, {"Target": "Parameter", "Id": "Change_idle_physics32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamLegL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "Parameter", "Id": "ParamLegLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamCrusLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamCrusLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>oodLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamFoodLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamFoodLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamLegR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 11.333, 1]}, {"Target": "Parameter", "Id": "ParamLegRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamCrusRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamCrusRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamCrus<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>ood<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamFoodRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamFoodRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Physics_Provisional", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "physics_Add_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "physics_Add_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamBodyStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Physics_AngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Physics_AngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Physics_Body_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Physics_Body_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Physics_breast", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamBreath2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "touch_drag1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "touch_drag2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "touch_drag3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "touch_drag4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "touch_drag5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "touch_drag6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "touch_drag7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "touch_drag8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "touch_drag9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>pian<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh654", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh655", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh657", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh656", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh658", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh659", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh660", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.333, 0]}], "UserData": [{"Time": 0.25, "Value": ""}, {"Time": 10.833, "Value": ""}]}