{"Version": 3, "Meta": {"Duration": 15.5, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 303, "TotalSegmentCount": 2815, "TotalPointCount": 3323, "UserDataCount": 2, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 10.2, 0, 2, 10.65, 0, 0, 10.983, 0.24, 0, 11.267, 0, 0, 11.517, 0.24, 0, 11.817, 0, 0, 12.183, 0.24, 0, 12.45, 0, 0, 12.667, 0.24, 0, 12.9, 0, 0, 13.217, 0.24, 0, 13.4, 0, 0, 13.783, 0.24, 0, 14.083, 0, 2, 14.4, 0, 2, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 10.2, 0, 2, 10.65, 0, 0, 10.983, -0.12, 0, 11.267, 0, 0, 11.517, -0.12, 0, 11.817, 0, 0, 12.183, -0.12, 0, 12.45, 0, 0, 12.667, -0.12, 0, 12.9, 0, 0, 13.217, -0.12, 0, 13.4, 0, 0, 13.783, -0.12, 0, 14.083, 0, 2, 14.4, 0, 2, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 5.85, 0.84, 0, 8.35, 0.69, 0, 10.167, 0.764, 2, 14.133, 0.764, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 5.85, 0.54, 0, 8.35, 0.45, 1, 8.956, 0.45, 9.561, 0.541, 10.167, 0.567, 1, 10.328, 0.574, 10.489, 0.57, 10.65, 0.57, 2, 10.983, 0.57, 0, 11.267, 0.728, 0, 11.517, 0.57, 0, 11.817, 0.671, 0, 12.183, 0.57, 0, 12.45, 0.664, 0, 12.667, 0.57, 0, 12.9, 0.728, 0, 13.217, 0.57, 0, 13.4, 0.807, 0, 13.933, 0.044, 0, 14.083, 1.347, 0, 15, 0, 2, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.75, 0.6, 0, 1.583, -0.1, 0, 2.283, 0.7, 0, 2.883, -0.1, 0, 3.783, 0.8, 0, 4.317, 0.2, 0, 5.15, 1, 0, 6.45, 0, 0, 6.75, 1, 0, 8.367, 0, 0, 9.167, 0.3, 0, 9.85, -1, 0, 10.2, -0.1, 0, 11.25, -1, 0, 12.35, -0.4, 0, 14.35, -1, 2, 14.75, -1, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY_SD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.35, 0, 0, 0.467, 1, 0, 0.583, 0, 0, 0.767, 1, 0, 0.883, 0, 0, 1.117, 1, 0, 1.283, 0, 0, 1.467, 1, 0, 1.583, 0, 0, 1.767, 1, 0, 1.883, 0, 0, 2.067, 1, 0, 2.2, 0, 2, 2.483, 0, 0, 2.6, 1, 0, 2.717, 0, 0, 2.9, 1, 0, 3.017, 0, 0, 3.25, 1, 0, 3.417, 0, 0, 3.6, 1, 0, 3.717, 0, 0, 3.9, 1, 0, 4.017, 0, 0, 4.2, 1, 0, 4.333, 0, 0, 4.483, 1, 0, 4.65, 0, 0, 4.767, 1, 0, 4.883, 0, 0, 5.067, 1, 0, 5.183, 0, 0, 5.417, 1, 0, 5.583, 0, 0, 5.767, 1, 0, 5.883, 0, 0, 6.067, 1, 0, 6.183, 0, 0, 6.367, 1, 0, 6.5, 0, 0, 6.65, 1, 0, 6.817, 0, 0, 6.933, 1, 0, 7.05, 0, 0, 7.233, 1, 0, 7.35, 0, 0, 7.583, 1, 0, 7.75, 0, 0, 7.933, 1, 0, 8.05, 0, 0, 8.233, 1, 0, 8.35, 0, 0, 8.533, 1, 0, 8.667, 0, 0, 8.817, 1, 0, 8.983, 0, 0, 9.1, 1, 0, 9.217, 0, 0, 9.4, 1, 0, 9.517, 0, 0, 9.75, 1, 0, 9.917, 0, 0, 10.1, 1, 1, 10.133, 1, 10.167, 0.877, 10.2, 0.5, 1, 10.206, 0.437, 10.211, 0, 10.217, 0, 0, 10.4, 1, 0, 10.517, 0, 0, 10.7, 1, 0, 10.833, 0, 0, 10.983, 1, 0, 11.15, 0, 0, 11.267, 1, 0, 11.383, 0, 0, 11.567, 1, 0, 11.683, 0, 0, 11.917, 1, 0, 12.083, 0, 0, 12.267, 1, 0, 12.383, 0, 0, 12.567, 1, 0, 12.683, 0, 0, 12.867, 1, 0, 13, 0, 0, 13.15, 1, 0, 13.317, 0, 0, 13.433, 1, 0, 13.55, 0, 0, 13.733, 1, 0, 13.85, 0, 0, 14.083, 1, 0, 14.25, 0, 0, 14.433, 1, 0, 14.55, 0, 0, 14.733, 1, 0, 14.917, 0, 2, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 0.5, 1, 0, 0.633, 0, 0, 0.817, 1.3, 0, 0.9, 1, 2, 2.3, 1, 0, 2.433, 0, 0, 2.617, 1.3, 0, 2.7, 1, 2, 6.7, 1, 0, 6.833, 0, 0, 7.017, 1.3, 0, 7.1, 1, 2, 9.5, 1, 0, 9.633, 0, 0, 9.817, 1.3, 0, 9.9, 1, 2, 14.133, 1, 0, 14.267, 0, 0, 14.45, 1.3, 0, 14.533, 1, 2, 15.25, 1, 2, 15.5, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 0.5, 1, 0, 0.633, 0, 0, 0.817, 1.3, 0, 0.9, 1, 2, 2.3, 1, 0, 2.433, 0, 0, 2.617, 1.3, 0, 2.7, 1, 2, 6.7, 1, 0, 6.833, 0, 0, 7.017, 1.3, 0, 7.1, 1, 2, 9.5, 1, 0, 9.633, 0, 0, 9.817, 1.3, 0, 9.9, 1, 2, 14.133, 1, 0, 14.267, 0, 0, 14.45, 1.3, 0, 14.533, 1, 2, 15.25, 1, 2, 15.5, 1]}, {"Target": "Parameter", "Id": "EyeFrame_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 5.417, 0, 0, 6.117, -1, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamBrowForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.233, 1, 2, 5.067, 1, 0, 6.3, 0, 2, 10.25, 0, 0, 11.417, -1, 2, 14.717, -1, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamBrowAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.233, 0, 2, 5.467, 0, 0, 6.3, 1, 2, 8.883, 1, 2, 9.833, 1, 2, 10.25, 1, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.5, 0, 0, 0.567, 1, 2, 0.717, 1, 0, 0.733, -1, 2, 0.867, -1, 0, 1.167, 0.244, 0, 1.483, -0.068, 0, 1.783, 0.019, 0, 2.083, -0.005, 2, 2.1, -0.005, 0, 2.367, 1, 2, 2.517, 1, 0, 2.533, -1, 2, 2.667, -1, 0, 2.967, 0.237, 0, 3.283, -0.066, 0, 3.583, 0.018, 0, 3.9, -0.005, 0, 4.2, 0.002, 0, 4.217, 0.001, 2, 4.233, 0.001, 0, 4.45, 0, 2, 4.467, 0, 2, 4.483, 0, 2, 4.567, 0, 2, 4.583, 0, 2, 4.6, 0, 2, 4.617, 0, 2, 4.633, 0, 2, 4.65, 0, 2, 4.683, 0, 2, 4.7, 0, 2, 4.733, 0, 2, 4.75, 0, 2, 4.933, 0, 2, 4.95, 0, 2, 6.7, 0, 0, 6.767, 1, 2, 6.917, 1, 0, 6.933, -1, 2, 7.067, -1, 0, 7.367, 0.244, 0, 7.683, -0.068, 0, 7.983, 0.019, 0, 8.283, -0.005, 2, 8.3, -0.005, 0, 8.6, 0.002, 2, 8.617, 0.002, 0, 8.633, 0.001, 2, 8.65, 0.001, 0, 8.85, 0, 2, 8.867, 0, 2, 8.883, 0, 2, 8.967, 0, 2, 8.983, 0, 2, 9, 0, 2, 9.017, 0, 2, 9.033, 0, 2, 9.05, 0, 2, 9.083, 0, 2, 9.1, 0, 2, 9.133, 0, 2, 9.15, 0, 2, 9.333, 0, 2, 9.35, 0, 2, 9.5, 0, 0, 9.567, 1, 2, 9.717, 1, 0, 9.733, -1, 2, 9.867, -1, 0, 10.167, 0.244, 0, 10.483, -0.068, 0, 10.783, 0.019, 0, 11.083, -0.005, 2, 11.1, -0.005, 0, 11.4, 0.002, 2, 11.417, 0.002, 0, 11.433, 0.001, 2, 11.45, 0.001, 0, 11.65, 0, 2, 11.667, 0, 2, 11.683, 0, 2, 11.767, 0, 2, 11.783, 0, 2, 11.8, 0, 2, 11.817, 0, 2, 11.833, 0, 2, 11.85, 0, 2, 11.883, 0, 2, 11.9, 0, 2, 11.933, 0, 2, 11.95, 0, 2, 12.133, 0, 2, 12.15, 0, 2, 14.133, 0, 0, 14.2, 1, 2, 14.35, 1, 0, 14.367, -1, 2, 14.5, -1, 0, 14.8, 0.244, 0, 15.117, -0.068, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeRsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.5, 0, 0, 0.567, 1, 2, 0.717, 1, 0, 0.733, -1, 2, 0.867, -1, 0, 1.167, 0.244, 0, 1.483, -0.068, 0, 1.783, 0.019, 0, 2.083, -0.005, 2, 2.1, -0.005, 0, 2.367, 1, 2, 2.517, 1, 0, 2.533, -1, 2, 2.667, -1, 0, 2.967, 0.237, 0, 3.283, -0.066, 0, 3.583, 0.018, 0, 3.9, -0.005, 0, 4.2, 0.002, 0, 4.217, 0.001, 2, 4.233, 0.001, 0, 4.45, 0, 2, 4.467, 0, 2, 4.483, 0, 2, 4.567, 0, 2, 4.583, 0, 2, 4.6, 0, 2, 4.617, 0, 2, 4.633, 0, 2, 4.65, 0, 2, 4.683, 0, 2, 4.7, 0, 2, 4.733, 0, 2, 4.75, 0, 2, 4.933, 0, 2, 4.95, 0, 2, 6.7, 0, 0, 6.767, 1, 2, 6.917, 1, 0, 6.933, -1, 2, 7.067, -1, 0, 7.367, 0.244, 0, 7.683, -0.068, 0, 7.983, 0.019, 0, 8.283, -0.005, 2, 8.3, -0.005, 0, 8.6, 0.002, 2, 8.617, 0.002, 0, 8.633, 0.001, 2, 8.65, 0.001, 0, 8.85, 0, 2, 8.867, 0, 2, 8.883, 0, 2, 8.967, 0, 2, 8.983, 0, 2, 9, 0, 2, 9.017, 0, 2, 9.033, 0, 2, 9.05, 0, 2, 9.083, 0, 2, 9.1, 0, 2, 9.133, 0, 2, 9.15, 0, 2, 9.333, 0, 2, 9.35, 0, 2, 9.5, 0, 0, 9.567, 1, 2, 9.717, 1, 0, 9.733, -1, 2, 9.867, -1, 0, 10.167, 0.244, 0, 10.483, -0.068, 0, 10.783, 0.019, 0, 11.083, -0.005, 2, 11.1, -0.005, 0, 11.4, 0.002, 2, 11.417, 0.002, 0, 11.433, 0.001, 2, 11.45, 0.001, 0, 11.65, 0, 2, 11.667, 0, 2, 11.683, 0, 2, 11.767, 0, 2, 11.783, 0, 2, 11.8, 0, 2, 11.817, 0, 2, 11.833, 0, 2, 11.85, 0, 2, 11.883, 0, 2, 11.9, 0, 2, 11.933, 0, 2, 11.95, 0, 2, 12.133, 0, 2, 12.15, 0, 2, 14.133, 0, 0, 14.2, 1, 2, 14.35, 1, 0, 14.367, -1, 2, 14.5, -1, 0, 14.8, 0.244, 0, 15.117, -0.068, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeLGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.5, 0, 0, 0.567, -0.305, 0, 0.617, -0.2, 0, 0.717, -1, 2, 0.733, -1, 0, 0.75, 1, 2, 0.783, 1, 0, 0.817, 0.831, 2, 0.833, 0.831, 0, 0.867, 0.863, 0, 1.117, -0.314, 0, 1.383, 0.153, 0, 1.667, -0.069, 0, 1.967, 0.028, 0, 2.267, -0.01, 0, 2.3, -0.009, 0, 2.367, -0.307, 0, 2.417, -0.198, 0, 2.517, -1, 2, 2.533, -1, 0, 2.55, 1, 2, 2.583, 1, 0, 2.633, 0.858, 0, 2.65, 0.881, 0, 2.917, -0.312, 0, 3.183, 0.149, 0, 3.467, -0.067, 0, 3.767, 0.027, 0, 4.067, -0.01, 0, 4.35, 0.003, 2, 4.383, 0.003, 0, 4.65, -0.001, 2, 4.7, -0.001, 2, 4.717, -0.001, 2, 4.733, -0.001, 0, 4.917, 0, 2, 4.933, 0, 2, 4.95, 0, 2, 5.017, 0, 2, 5.033, 0, 2, 5.067, 0, 2, 5.083, 0, 2, 5.1, 0, 2, 5.117, 0, 2, 5.133, 0, 2, 5.15, 0, 2, 5.183, 0, 2, 5.2, 0, 2, 5.4, 0, 2, 5.417, 0, 2, 6.7, 0, 0, 6.767, -0.305, 0, 6.817, -0.2, 0, 6.917, -1, 2, 6.933, -1, 0, 6.95, 1, 2, 6.983, 1, 0, 7.017, 0.831, 2, 7.033, 0.831, 0, 7.067, 0.863, 0, 7.317, -0.314, 0, 7.583, 0.153, 0, 7.867, -0.069, 0, 8.167, 0.028, 0, 8.467, -0.01, 0, 8.75, 0.004, 2, 8.783, 0.004, 0, 9.033, -0.001, 2, 9.05, -0.001, 2, 9.067, -0.001, 2, 9.083, -0.001, 2, 9.1, -0.001, 2, 9.117, -0.001, 0, 9.317, 0, 2, 9.333, 0, 2, 9.35, 0, 2, 9.417, 0, 2, 9.433, 0, 2, 9.467, 0, 2, 9.483, 0, 2, 9.5, 0, 0, 9.567, -0.305, 0, 9.617, -0.2, 0, 9.717, -1, 2, 9.733, -1, 0, 9.75, 1, 2, 9.783, 1, 0, 9.833, 0.831, 0, 9.867, 0.863, 0, 10.117, -0.314, 0, 10.383, 0.153, 0, 10.667, -0.069, 0, 10.967, 0.028, 0, 11.267, -0.01, 0, 11.55, 0.004, 2, 11.583, 0.004, 0, 11.833, -0.001, 2, 11.85, -0.001, 2, 11.867, -0.001, 2, 11.9, -0.001, 0, 12.117, 0, 2, 12.133, 0, 2, 12.15, 0, 2, 12.217, 0, 2, 12.233, 0, 2, 12.267, 0, 2, 12.283, 0, 2, 12.3, 0, 2, 12.317, 0, 2, 12.333, 0, 2, 12.35, 0, 2, 12.383, 0, 2, 12.4, 0, 2, 12.6, 0, 2, 12.617, 0, 2, 14.133, 0, 0, 14.2, -0.305, 0, 14.25, -0.2, 0, 14.35, -1, 2, 14.367, -1, 0, 14.383, 1, 2, 14.417, 1, 0, 14.45, 0.831, 2, 14.467, 0.831, 0, 14.5, 0.863, 0, 14.75, -0.314, 0, 15.017, 0.153, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeRGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.5, 0, 0, 0.567, -0.305, 0, 0.617, -0.2, 0, 0.717, -1, 2, 0.733, -1, 0, 0.75, 1, 2, 0.783, 1, 0, 0.817, 0.831, 2, 0.833, 0.831, 0, 0.867, 0.863, 0, 1.117, -0.314, 0, 1.383, 0.153, 0, 1.667, -0.069, 0, 1.967, 0.028, 0, 2.267, -0.01, 0, 2.3, -0.009, 0, 2.367, -0.307, 0, 2.417, -0.198, 0, 2.517, -1, 2, 2.533, -1, 0, 2.55, 1, 2, 2.583, 1, 0, 2.633, 0.858, 0, 2.65, 0.881, 0, 2.917, -0.312, 0, 3.183, 0.149, 0, 3.467, -0.067, 0, 3.767, 0.027, 0, 4.067, -0.01, 0, 4.35, 0.003, 2, 4.383, 0.003, 0, 4.65, -0.001, 2, 4.7, -0.001, 2, 4.717, -0.001, 2, 4.733, -0.001, 0, 4.917, 0, 2, 4.933, 0, 2, 4.95, 0, 2, 5.017, 0, 2, 5.033, 0, 2, 5.067, 0, 2, 5.083, 0, 2, 5.1, 0, 2, 5.117, 0, 2, 5.133, 0, 2, 5.15, 0, 2, 5.183, 0, 2, 5.2, 0, 2, 5.4, 0, 2, 5.417, 0, 2, 6.7, 0, 0, 6.767, -0.305, 0, 6.817, -0.2, 0, 6.917, -1, 2, 6.933, -1, 0, 6.95, 1, 2, 6.983, 1, 0, 7.017, 0.831, 2, 7.033, 0.831, 0, 7.067, 0.863, 0, 7.317, -0.314, 0, 7.583, 0.153, 0, 7.867, -0.069, 0, 8.167, 0.028, 0, 8.467, -0.01, 0, 8.75, 0.004, 2, 8.783, 0.004, 0, 9.033, -0.001, 2, 9.05, -0.001, 2, 9.067, -0.001, 2, 9.083, -0.001, 2, 9.1, -0.001, 2, 9.117, -0.001, 0, 9.317, 0, 2, 9.333, 0, 2, 9.35, 0, 2, 9.417, 0, 2, 9.433, 0, 2, 9.467, 0, 2, 9.483, 0, 2, 9.5, 0, 0, 9.567, -0.305, 0, 9.617, -0.2, 0, 9.717, -1, 2, 9.733, -1, 0, 9.75, 1, 2, 9.783, 1, 0, 9.833, 0.831, 0, 9.867, 0.863, 0, 10.117, -0.314, 0, 10.383, 0.153, 0, 10.667, -0.069, 0, 10.967, 0.028, 0, 11.267, -0.01, 0, 11.55, 0.004, 2, 11.583, 0.004, 0, 11.833, -0.001, 2, 11.85, -0.001, 2, 11.867, -0.001, 2, 11.9, -0.001, 0, 12.117, 0, 2, 12.133, 0, 2, 12.15, 0, 2, 12.217, 0, 2, 12.233, 0, 2, 12.267, 0, 2, 12.283, 0, 2, 12.3, 0, 2, 12.317, 0, 2, 12.333, 0, 2, 12.35, 0, 2, 12.383, 0, 2, 12.4, 0, 2, 12.6, 0, 2, 12.617, 0, 2, 14.133, 0, 0, 14.2, -0.305, 0, 14.25, -0.2, 0, 14.35, -1, 2, 14.367, -1, 0, 14.383, 1, 2, 14.417, 1, 0, 14.45, 0.831, 2, 14.467, 0.831, 0, 14.5, 0.863, 0, 14.75, -0.314, 0, 15.017, 0.153, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 9.1, 0, 0, 9.117, -5, 2, 11.183, -5, 1, 11.189, -5, 11.194, 5, 11.2, 5, 2, 14.75, 5, 0, 14.767, 0, 2, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 9.1, 0, 0, 9.117, -5, 2, 11.183, -5, 2, 11.2, -5, 2, 14.75, -5, 0, 14.767, 0, 2, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.95, 0, 2, 2.333, 0, 1, 2.394, 0, 2.456, 0.023, 2.517, 0.2, 1, 2.678, 0.667, 2.839, 1, 3, 1, 2, 5.133, 1, 1, 5.322, 1, 5.511, 0.665, 5.7, 0.2, 1, 5.772, 0.022, 5.845, 0, 5.917, 0, 2, 6.667, 0, 2, 7.167, 0, 2, 8.767, 0, 2, 10, 0, 2, 14.167, 0, 2, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 6.667, 0, 0, 7.167, 1.92, 0, 9.267, 1.5, 0, 9.6, 5.52, 0, 10, 1.5, 2, 14.5, 1.5, 0, 14.833, 4.137, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 6.667, 0, 2, 7.167, 0, 2, 9.267, 0, 0, 9.6, -13.5, 0, 10, 0, 2, 14.167, 0, 2, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 30, 2, 0.25, 30, 1, 0.311, 30, 0.372, 30.442, 0.433, 28.091, 1, 0.561, 23.175, 0.689, 6.507, 0.817, 1.308, 1, 0.861, -0.501, 0.906, 0, 0.95, 0, 2, 2.333, 0, 2, 5.917, 0, 2, 6.667, 0, 0, 7.167, 7.5, 0, 7.767, 6.72, 0, 8.483, 10.348, 2, 8.833, 10.348, 1, 9.166, 10.348, 9.5, 9.88, 9.833, 8.478, 1, 9.889, 8.244, 9.944, 7.08, 10, 7.08, 2, 14.5, 7.08, 0, 14.95, 30, 2, 15.15, 30, 2, 15.25, 30, 2, 15.5, 30]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.6, -30, 0, 0.867, 0, 2, 1, 0, 2, 6.667, 0, 0, 7.167, 3.4, 2, 8.767, 3.4, 1, 9.122, 3.16, 9.478, 2.92, 9.833, 2.68, 1, 9.889, 2.92, 9.944, 3.16, 10, 3.4, 2, 14.5, 3.4, 0, 14.75, -30, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1, 2, 0.25, -1, 2, 0.5, -1, 0, 0.517, 1, 2, 6.717, 1, 2, 14.567, 1, 1, 14.572, 0.333, 14.578, -0.333, 14.583, -1, 2, 15.25, -1, 2, 15.5, -1]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.517, -0.574, 0, 0.983, 0, 2, 1.233, 0, 2, 6.667, 0, 0, 7.167, -0.492, 1, 7.445, -0.368, 7.722, -0.244, 8, -0.12, 1, 8.228, -0.275, 8.455, -0.43, 8.683, -0.585, 1, 9.122, -0.39, 9.561, -0.195, 10, 0, 2, 14.167, 0, 0, 14.717, 0.338, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamHandLDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10, 2, 0.25, 10, 2, 0.7, 10, 2, 0.717, 0, 2, 0.95, 0, 2, 2.333, 0, 2, 2.517, 0, 0, 2.533, 1, 2, 5.683, 1, 2, 5.7, 0, 2, 6.733, 0, 2, 6.75, 5.5, 2, 9.4, 5.5, 2, 9.417, 7, 2, 10, 7, 2, 14.167, 7, 2, 14.65, 7, 2, 14.667, 10, 2, 15.25, 10, 2, 15.5, 10]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 6.667, 0, 0, 6.75, -1, 1, 6.889, -1, 7.028, -0.781, 7.167, 0, 1, 7.2, 0.187, 7.234, 1, 7.267, 1, 0, 7.983, -0.9, 0, 8.533, 1, 1, 8.683, 1, 8.833, 0.845, 8.983, 0.6, 1, 9.116, 0.382, 9.25, 0.208, 9.383, 0, 1, 9.389, -0.009, 9.394, -1, 9.4, -1, 0, 9.417, 0, 2, 10, 0, 2, 14.167, 0, 2, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 6.667, 0, 0, 6.75, -1, 0, 7.167, 0, 2, 8.767, 0, 0, 9.4, -0.8, 0, 10, 0, 2, 14.167, 0, 2, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 6.667, 0, 0, 7.167, -5.22, 2, 9, -5.22, 1, 9.133, -3.48, 9.267, -1.74, 9.4, 0, 2, 10, 0, 2, 14.167, 0, 2, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 6.667, 0, 0, 7.167, 20.34, 2, 9, 20.34, 1, 9.133, 13.56, 9.267, 6.78, 9.4, 0, 2, 9.417, 0, 2, 10, 0, 2, 14.167, 0, 2, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.033, 0, 0, 2.417, 1, 2, 5.817, 1, 2, 15, 1, 1, 15.05, 1, 15.1, 0.865, 15.15, 0.5, 1, 15.156, 0.459, 15.161, 0, 15.167, 0, 2, 15.217, 0, 2, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 6.667, 0, 2, 7.167, 0, 2, 8.767, 0, 2, 10, 0, 2, 14.283, 0, 2, 15.067, 0, 1, 15.095, 0.333, 15.122, 0.667, 15.15, 1, 1, 15.167, 0.667, 15.183, 0.333, 15.2, 0, 2, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.95, 0, 2, 2.333, 0, 1, 2.394, 0, 2.456, 0.023, 2.517, 0.2, 1, 2.678, 0.667, 2.839, 1, 3, 1, 2, 5.133, 1, 1, 5.322, 1, 5.511, 0.665, 5.7, 0.2, 1, 5.772, 0.022, 5.845, 0, 5.917, 0, 2, 6.667, 0, 2, 7.167, 0, 2, 8.767, 0, 2, 10, 0, 2, 14.283, 0, 2, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 6.667, 0, 0, 7.167, -2, 0, 9.267, -0.5, 0, 9.6, -9, 0, 10, -0.5, 2, 13, -0.5, 0, 14.4, -4.333, 0, 15.15, 0, 2, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 6.667, 0, 2, 7.167, 0, 2, 9.267, 0, 0, 9.6, -11.1, 0, 10, 0, 2, 14.283, 0, 2, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 2.317, 0.5, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 6.667, 0, 0, 7.167, -7.16, 0, 7.767, -6.066, 0, 8.483, -9.937, 2, 8.833, -9.937, 1, 9.166, -9.937, 9.5, -9.439, 9.833, -7.88, 1, 9.889, -7.62, 9.944, -5.6, 10, -5.6, 2, 14.283, -5.6, 0, 14.9, 0, 2, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 6.667, 0, 0, 7.167, 17.4, 2, 8.767, 17.4, 1, 9.122, 14.1, 9.478, 10.8, 9.833, 7.5, 1, 9.889, 10.8, 9.944, 14.1, 10, 17.4, 2, 14.283, 17.4, 1, 14.494, 17.4, 14.706, 14.428, 14.917, 6.59, 1, 14.945, 5.559, 14.972, 0, 15, 0, 2, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 6.667, 0, 0, 7.167, -0.522, 1, 7.445, -0.346, 7.722, -0.17, 8, 0.006, 1, 8.228, -0.151, 8.455, -0.308, 8.683, -0.465, 1, 9.122, -0.31, 9.561, -0.155, 10, 0, 2, 13, 0, 0, 13.367, 0.192, 1, 13.672, 0.192, 13.978, 0.157, 14.283, 0, 1, 14.372, -0.046, 14.461, -0.314, 14.55, -0.314, 1, 14.622, -0.314, 14.695, -0.321, 14.767, -0.285, 1, 14.845, -0.246, 14.922, 0, 15, 0, 2, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamHandRDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.517, 0, 0, 2.533, 1, 2, 5.683, 1, 2, 5.7, 0, 2, 6.733, 0, 2, 6.75, 5.5, 2, 9.4, 5.5, 2, 9.417, 7, 2, 10, 7, 2, 14.283, 7, 2, 14.733, 7, 2, 14.75, 0, 2, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 6.667, 0, 0, 7.167, 21.06, 2, 9.217, 21.06, 0, 9.55, 0, 2, 10, 0, 2, 14.283, 0, 2, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.317, 0, 0, 2.333, 1, 2, 6.667, 1, 2, 7.167, 1, 2, 8.767, 1, 2, 10, 1, 2, 14.983, 1, 2, 15, 0, 2, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 6.667, 0, 0, 7.167, 1, 2, 8.767, 1, 2, 10, 1, 2, 14.283, 1, 1, 14.289, 0.667, 14.294, 0.333, 14.3, 0, 2, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 6.667, 0, 2, 9.883, 0, 2, 9.9, 1, 2, 10.05, 1, 2, 14.283, 1, 0, 14.3, 0, 2, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 10.283, 0, 0, 10.767, 1, 2, 14.267, 1, 0, 15.1, 0, 2, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.317, 0, 0, 2.333, 1, 2, 9.65, 1, 2, 10, 1, 2, 14.983, 1, 2, 15, 0, 2, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 8.767, 0, 0, 9.367, 0.7, 0, 10, 0, 2, 14.283, 0, 0, 14.517, 0.295, 0, 14.817, 0, 2, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 2.333, 0.006, 0, 8.767, 0, 2, 10, 0, 2, 14.283, 0, 0, 15, 0.016, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 2.333, 0.04, 1, 4.478, 0.04, 6.622, 0.032, 8.767, 0, 1, 9.178, -0.006, 9.589, -0.59, 10, -0.59, 2, 14.283, -0.59, 0, 15, 0.06, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 2.333, -0.166, 1, 4.478, -0.166, 6.622, -0.127, 8.767, 0, 1, 9.095, 0.019, 9.422, 0.872, 9.75, 0.872, 1, 9.833, 0.872, 9.917, 0.826, 10, 0.798, 1, 10.344, 0.683, 10.689, 0.642, 11.033, 0.642, 0, 13.183, 0.668, 0, 14.283, 0.656, 1, 14.305, 0.656, 14.328, 0.719, 14.35, 0.756, 1, 14.456, 0.934, 14.561, 1, 14.667, 1, 1, 14.778, 1, 14.889, 0.066, 15, 0.022, 1, 15.083, -0.011, 15.167, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 6.667, 0, 2, 7.167, 0, 2, 8.35, 0, 1, 8.472, 0.333, 8.595, 0.667, 8.717, 1, 2, 14.417, 1, 1, 14.611, 0.667, 14.806, 0.333, 15, 0, 2, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 6.667, 0, 2, 7.167, 0, 2, 8.767, 0, 2, 10, 0, 0, 10.3, -0.742, 0, 10.617, 0.72, 0, 10.933, -0.864, 0, 11.233, 0.964, 0, 11.517, -0.8, 0, 11.8, 1, 0, 12.167, -0.864, 0, 12.367, 1, 0, 12.65, -1, 0, 12.933, 1, 0, 13.217, -1, 0, 13.35, 1, 0, 13.767, -1, 0, 14, 0.7, 0, 14.283, 0, 2, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -2.728, 0, 0.617, 5.594, 0, 1.133, -8, 2, 1.8, -8, 0, 1.917, -6, 0, 2.3, -14, 0, 2.733, 13.86, 0, 3.133, -10.031, 1, 3.316, -10.031, 3.5, -8.569, 3.683, -5, 1, 3.989, 0.949, 4.294, 4.282, 4.6, 4.282, 0, 4.85, -14.746, 0, 5.05, 7, 0, 5.3, -15.62, 0, 5.767, 0, 0, 6.667, -21, 1, 6.911, -21, 7.156, 6.754, 7.4, 9, 1, 7.989, 14.41, 8.578, 15, 9.167, 15, 0, 10.05, -30, 1, 10.117, -30, 10.183, -8.1, 10.25, 0, 1, 10.372, 14.851, 10.495, 17.42, 10.617, 17.42, 0, 10.95, -17, 0, 11.267, 14, 0, 11.567, -12, 0, 11.867, 12, 0, 12.2, -11, 0, 12.367, 11, 0, 12.633, -10, 0, 12.883, 11, 0, 13.15, -11, 0, 13.483, 17.782, 0, 13.933, -24.09, 0, 14.183, 15.578, 0, 14.483, -11, 0, 14.95, 6.436, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 6.25, 0, 2, 6.667, 0, 2, 10.25, 0, 0, 12.167, 12, 0, 12.983, 0.9, 0, 14.033, 11.821, 1, 14.278, 11.821, 14.522, 8.227, 14.767, 3.65, 1, 14.928, 0.633, 15.089, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.533, -10, 1, 0.716, -10, 0.9, 6.373, 1.083, 7.359, 1, 1.344, 8.764, 1.606, 8.654, 1.867, 8.654, 1, 2.1, 8.654, 2.334, 8.517, 2.567, 7, 1, 2.722, 5.989, 2.878, 0.466, 3.033, 0, 1, 3.344, -0.932, 3.656, -1, 3.967, -1, 0, 5.1, 0, 0, 6.467, -0.96, 0, 8.8, 0, 2, 10.25, 0, 0, 12.167, -10, 0, 13.033, -8.9, 0, 14.083, -10, 1, 14.366, -10, 14.65, -9.901, 14.933, -9.02, 1, 15.039, -8.692, 15.144, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -7.795, 2, 0.25, -7.795, 1, 0.75, -7.795, 1.25, -5.197, 1.75, 0, 1, 2.25, 5.197, 2.75, 7.795, 3.25, 7.795, 1, 3.75, 7.795, 4.25, 5.197, 4.75, 0, 1, 5.25, -5.197, 5.75, -7.795, 6.25, -7.795, 1, 6.75, -7.795, 7.25, -5.197, 7.75, 0, 1, 8.25, 5.197, 8.75, 7.795, 9.25, 7.795, 0, 10.25, -7.795, 0, 12.167, 0, 1, 13.195, -2.598, 14.222, -5.197, 15.25, -7.795, 2, 15.5, -7.795]}, {"Target": "Parameter", "Id": "ParamBodyStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 0.294, 0, 0.339, 4.872, 0.383, 6.338, 1, 0.478, 9.454, 0.572, 10, 0.667, 10, 0, 1.083, -3, 0, 1.45, 1.044, 0, 1.8, -1.92, 0, 2.4, 2.409, 0, 2.683, 0, 0, 3.033, 10, 1, 3.161, 10, 3.289, 10, 3.417, 7.281, 1, 3.578, 3.273, 3.739, -10, 3.9, -10, 0, 4.233, -3, 0, 4.5, -7, 2, 4.65, -7, 0, 4.867, -9, 0, 5.033, -4.543, 1, 5.094, -4.543, 5.156, -5.258, 5.217, -6, 1, 5.284, -6.81, 5.35, -7, 5.417, -7, 1, 5.472, -7, 5.528, -2.742, 5.583, -2, 1, 5.728, -0.072, 5.872, 0.25, 6.017, 0.25, 0, 6.633, -5.184, 0, 7.05, 2.395, 0, 7.183, 1.889, 0, 7.35, 2.493, 0, 7.683, 0.094, 0, 7.833, 0.616, 0, 8.217, -4, 0, 8.95, 0, 2, 9.117, 0, 0, 9.55, 10, 0, 9.85, -2, 0, 10, 0, 0, 10.3, -5.52, 0, 10.617, 5, 0, 10.933, -5, 0, 11.233, 6, 0, 11.517, -5, 0, 11.8, 6, 0, 12.117, -5, 0, 12.283, 5, 0, 12.533, -6, 0, 12.767, 6, 0, 13.017, -5, 0, 13.333, 4, 0, 13.767, -5, 0, 14, 10, 0, 14.283, -10, 0, 14.717, 6, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.517, 4.859, 0, 0.883, -1.073, 0, 1.483, 2.445, 0, 2.017, -0.586, 0, 2.533, 0.603, 0, 4.967, -7, 0, 6.467, 6, 0, 8, -4, 0, 9.583, 10, 0, 10.867, 0, 2, 11.067, 0, 0, 11.1, -1.7, 0, 11.117, 1, 0, 11.167, -1, 0, 11.217, 0.523, 0, 11.283, -0.637, 0, 11.333, 0, 1, 11.428, 0, 11.522, -0.045, 11.617, -0.176, 1, 11.628, -0.192, 11.639, -1.02, 11.65, -1.02, 0, 11.667, 0.32, 0, 11.717, -0.672, 0, 11.767, 0.083, 0, 11.833, -0.492, 1, 11.85, -0.492, 11.866, -0.185, 11.883, -0.176, 1, 11.994, -0.117, 12.106, -0.098, 12.217, -0.098, 0, 12.3, -1.322, 0, 12.333, 0.622, 0, 12.433, -0.818, 0, 12.533, 0.279, 0, 12.7, -0.557, 0, 12.8, -0.098, 1, 12.956, -0.098, 13.111, -0.124, 13.267, -0.206, 1, 13.289, -0.217, 13.311, -0.906, 13.333, -0.906, 0, 13.367, 0.206, 0, 13.45, -0.618, 0, 13.55, 0.01, 0, 13.667, -0.468, 1, 13.7, -0.468, 13.734, -0.331, 13.767, -0.206, 1, 13.811, -0.039, 13.856, 0, 13.9, 0, 0, 13.933, -1.7, 0, 13.95, 1, 0, 14, -1, 0, 14.05, 0.523, 0, 14.117, -0.637, 0, 14.167, 0, 2, 14.25, 0, 0, 14.483, 3.334, 0, 14.8, -10, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamLegLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 2.75, 14.515, 0, 4, -9.742, 1, 4.406, -9.742, 4.811, -5.169, 5.217, 0, 1, 6.228, 12.886, 7.239, 17.873, 8.25, 17.873, 1, 8.628, 17.873, 9.005, 14.454, 9.383, 0, 1, 9.505, -4.676, 9.628, -30, 9.75, -30, 0, 9.85, -28.585, 0, 9.95, -30, 0, 10.1, -29.869, 1, 10.122, -29.852, 10.145, -29.833, 10.167, -29.815, 1, 10.178, -29.759, 10.189, -29.933, 10.2, -29.471, 1, 10.211, -29.124, 10.222, -28.583, 10.233, -28.585, 2, 10.4, -28.585, 1, 10.417, -28.601, 10.433, -29.58, 10.45, -29.596, 1, 10.461, -29.597, 10.472, -29.323, 10.483, -29.148, 1, 10.494, -28.917, 10.506, -29.012, 10.517, -28.989, 2, 10.533, -28.989, 2, 10.55, -28.989, 2, 10.567, -28.989, 2, 10.583, -28.989, 2, 10.6, -28.989, 2, 10.617, -28.989, 2, 10.633, -28.989, 2, 10.65, -28.989, 2, 10.667, -28.989, 1, 10.684, -28.996, 10.7, -29.264, 10.717, -29.596, 1, 10.734, -29.924, 10.75, -29.996, 10.767, -30, 1, 10.784, -29.991, 10.8, -29.403, 10.817, -29.394, 1, 10.839, -29.389, 10.861, -30, 10.883, -30, 1, 10.9, -29.978, 10.916, -28.607, 10.933, -28.585, 1, 10.95, -28.589, 10.966, -28.6, 10.983, -28.989, 1, 11, -29.388, 11.016, -29.987, 11.033, -30, 1, 11.05, -29.991, 11.066, -29.403, 11.083, -29.394, 1, 11.1, -29.403, 11.116, -29.991, 11.133, -30, 1, 11.15, -29.991, 11.166, -29.403, 11.183, -29.394, 1, 11.2, -29.403, 11.216, -29.991, 11.233, -30, 2, 11.283, -30, 1, 11.3, -29.994, 11.316, -29.602, 11.333, -29.596, 1, 11.35, -29.602, 11.366, -29.994, 11.383, -30, 2, 11.6, -30, 1, 11.617, -29.984, 11.633, -29.353, 11.65, -28.601, 1, 11.667, -27.857, 11.683, -27.697, 11.7, -27.688, 1, 11.717, -27.725, 11.733, -29.964, 11.75, -30, 2, 12.017, -30, 1, 12.034, -29.969, 12.05, -28.138, 12.067, -27.979, 1, 12.084, -27.849, 12.1, -27.846, 12.117, -27.777, 1, 12.134, -27.707, 12.15, -27.692, 12.167, -27.575, 1, 12.184, -27.45, 12.2, -26.977, 12.217, -26.968, 2, 12.233, -26.968, 2, 12.25, -26.968, 3, 12.267, -26.968, 2, 12.333, -26.968, 1, 12.35, -26.97, 12.366, -26.966, 12.383, -27.171, 1, 12.4, -27.381, 12.416, -27.769, 12.433, -27.777, 2, 12.483, -27.777, 1, 12.5, -27.793, 12.516, -28.772, 12.533, -28.787, 1, 12.55, -28.771, 12.566, -28.177, 12.583, -27.373, 1, 12.6, -26.574, 12.616, -26.372, 12.633, -26.362, 2, 12.733, -26.362, 1, 12.75, -26.378, 12.766, -27.357, 12.783, -27.373, 1, 12.8, -27.368, 12.816, -27.405, 12.833, -26.766, 1, 12.85, -26.102, 12.866, -24.573, 12.883, -24.543, 1, 12.894, -24.537, 12.906, -26.091, 12.917, -27.076, 1, 12.928, -28.387, 12.939, -27.849, 12.95, -27.992, 1, 12.967, -27.934, 12.983, -28.002, 13, -26.611, 1, 13.017, -25.319, 13.033, -23.331, 13.05, -23.331, 1, 13.067, -23.351, 13.083, -24.489, 13.1, -24.745, 1, 13.117, -24.983, 13.133, -24.946, 13.15, -24.947, 1, 13.167, -24.932, 13.183, -23.953, 13.2, -23.937, 1, 13.217, -23.956, 13.233, -25.13, 13.25, -25.149, 1, 13.267, -25.131, 13.283, -24.63, 13.3, -23.331, 1, 13.317, -22.028, 13.333, -21.331, 13.35, -21.309, 1, 13.367, -21.351, 13.383, -23.896, 13.4, -23.937, 1, 13.417, -23.912, 13.433, -22.345, 13.45, -22.32, 1, 13.467, -22.322, 13.483, -22.34, 13.5, -22.522, 1, 13.517, -22.707, 13.533, -22.921, 13.55, -22.926, 1, 13.567, -22.888, 13.583, -20.539, 13.6, -20.501, 1, 13.617, -20.507, 13.633, -20.39, 13.65, -21.309, 1, 13.667, -22.295, 13.683, -26.29, 13.7, -26.362, 1, 13.717, -26.32, 13.733, -24.043, 13.75, -23.331, 1, 13.767, -22.656, 13.783, -22.728, 13.8, -22.724, 2, 13.817, -22.724, 2, 13.833, -22.724, 3, 13.85, -22.724, 2, 13.867, -22.724, 2, 13.883, -22.724, 2, 13.9, -22.724, 3, 13.917, -22.724, 1, 13.934, -22.8, 13.95, -27.499, 13.967, -27.575, 2, 14.017, -27.575, 1, 14.05, -27.576, 14.084, -27.371, 14.117, -27.373, 2, 14.317, -27.373, 1, 14.334, -27.365, 14.35, -27.173, 14.367, -26.564, 1, 14.384, -25.952, 14.4, -25.565, 14.417, -25.554, 1, 14.439, -25.554, 14.461, -26.715, 14.483, -28.238, 1, 14.494, -28.943, 14.506, -29.646, 14.517, -29.798, 1, 14.534, -30, 14.55, -29.998, 14.567, -30, 2, 14.583, -30, 2, 14.783, -30, 0, 14.867, -29.798, 2, 14.9, -29.798, 2, 14.917, -29.798, 2, 14.933, -29.798, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamLegRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.55, 7.732, 0, 4.05, -9.756, 1, 4.439, -9.756, 4.828, -5.355, 5.217, 0, 1, 5.65, 5.967, 6.084, 7.732, 6.517, 7.732, 0, 9.033, -9.756, 1, 9.15, -9.756, 9.266, -9.444, 9.383, 0, 1, 9.505, 9.893, 9.628, 30, 9.75, 30, 2, 9.85, 30, 1, 9.867, 29.999, 9.883, 29.911, 9.9, 29.91, 1, 9.917, 29.911, 9.933, 29.999, 9.95, 30, 1, 9.967, 29.999, 9.983, 30, 10, 29.91, 1, 10.022, 29.756, 10.045, 28.91, 10.067, 28.917, 2, 10.167, 28.917, 1, 10.178, 28.91, 10.189, 28.706, 10.2, 28.342, 1, 10.217, 27.821, 10.233, 26.916, 10.25, 26.107, 1, 10.261, 25.601, 10.272, 25.13, 10.283, 24.854, 1, 10.3, 24.431, 10.316, 24.406, 10.333, 24.403, 1, 10.355, 24.389, 10.378, 26.131, 10.4, 26.118, 2, 10.5, 26.118, 1, 10.517, 26.109, 10.533, 25.585, 10.55, 25.576, 1, 10.567, 25.597, 10.583, 26.909, 10.6, 26.93, 1, 10.617, 26.918, 10.633, 26.131, 10.65, 26.118, 1, 10.667, 26.121, 10.683, 26.295, 10.7, 26.389, 1, 10.717, 26.48, 10.733, 26.478, 10.75, 26.479, 1, 10.767, 26.463, 10.783, 25.501, 10.8, 25.486, 1, 10.817, 25.503, 10.833, 26.172, 10.85, 26.93, 1, 10.872, 27.908, 10.895, 28.2, 10.917, 28.194, 1, 10.934, 28.187, 10.95, 27.75, 10.967, 27.743, 1, 10.984, 27.754, 11, 28.454, 11.017, 28.465, 1, 11.028, 28.466, 11.039, 28.331, 11.05, 28.202, 1, 11.067, 27.953, 11.083, 27.9, 11.1, 27.924, 1, 11.117, 27.943, 11.133, 29.168, 11.15, 29.188, 1, 11.167, 29.184, 11.183, 29.098, 11.2, 28.826, 1, 11.217, 28.553, 11.233, 28.38, 11.25, 28.375, 1, 11.267, 28.389, 11.283, 29.264, 11.3, 29.278, 1, 11.317, 29.269, 11.333, 29.212, 11.35, 28.194, 1, 11.367, 27.153, 11.383, 25.609, 11.4, 25.576, 1, 11.417, 25.597, 11.433, 26.909, 11.45, 26.93, 1, 11.467, 26.93, 11.483, 26.937, 11.5, 26.84, 1, 11.517, 26.739, 11.533, 26.484, 11.55, 26.479, 1, 11.567, 26.485, 11.583, 26.835, 11.6, 26.84, 1, 11.617, 26.83, 11.633, 26.304, 11.65, 26.118, 1, 11.672, 25.889, 11.695, 25.935, 11.717, 25.897, 1, 11.728, 25.885, 11.739, 25.873, 11.75, 25.847, 1, 11.761, 25.825, 11.772, 24.982, 11.783, 24.368, 1, 11.794, 23.603, 11.806, 23.606, 11.817, 23.482, 1, 11.828, 23.403, 11.839, 23.409, 11.85, 23.41, 1, 11.867, 23.426, 11.883, 23.677, 11.9, 25.215, 1, 11.917, 26.775, 11.933, 28.337, 11.95, 28.375, 1, 11.961, 28.379, 11.972, 27.358, 11.983, 26.711, 1, 11.994, 25.85, 12.006, 26.202, 12.017, 26.118, 1, 12.034, 26.189, 12.05, 25.985, 12.067, 26.568, 1, 12.078, 26.925, 12.089, 27.495, 12.1, 27.833, 1, 12.111, 28.172, 12.122, 28.683, 12.133, 28.987, 1, 12.15, 29.483, 12.166, 29.245, 12.183, 29.332, 1, 12.194, 29.361, 12.206, 29.327, 12.217, 29.559, 1, 12.228, 29.74, 12.239, 30, 12.25, 30, 2, 12.417, 30, 1, 12.434, 29.997, 12.45, 29.822, 12.467, 29.819, 1, 12.484, 29.822, 12.5, 29.997, 12.517, 30, 2, 12.567, 30, 1, 12.584, 29.998, 12.6, 30, 12.617, 29.729, 1, 12.634, 29.421, 12.65, 28.572, 12.667, 28.555, 1, 12.684, 28.568, 12.7, 29.355, 12.717, 29.368, 1, 12.734, 29.363, 12.75, 29.066, 12.767, 29.007, 1, 12.784, 28.953, 12.8, 28.963, 12.817, 28.917, 1, 12.85, 28.828, 12.884, 28.735, 12.917, 28.736, 1, 12.956, 28.735, 12.994, 28.827, 13.033, 28.826, 2, 13.083, 28.826, 1, 13.1, 28.83, 13.116, 28.958, 13.133, 29.097, 1, 13.155, 29.295, 13.178, 29.417, 13.2, 29.368, 3, 13.217, 29.368, 2, 13.233, 29.368, 1, 13.272, 29.372, 13.311, 29.03, 13.35, 28.972, 1, 13.372, 28.981, 13.395, 28.891, 13.417, 28.716, 1, 13.439, 28.556, 13.461, 28.375, 13.483, 28.375, 2, 13.683, 28.375, 1, 13.7, 28.379, 13.716, 28.642, 13.733, 28.646, 1, 13.75, 28.626, 13.766, 27.402, 13.783, 27.382, 1, 13.8, 27.39, 13.816, 27.572, 13.833, 28.285, 1, 13.85, 29.003, 13.866, 29.534, 13.883, 29.549, 1, 13.9, 29.514, 13.916, 28.144, 13.933, 26.569, 1, 13.95, 25.012, 13.966, 24.691, 13.983, 24.673, 1, 13.994, 24.669, 14.006, 25.443, 14.017, 26.195, 1, 14.034, 27.46, 14.05, 28.171, 14.067, 28.765, 1, 14.095, 29.713, 14.122, 30, 14.15, 30, 2, 14.2, 30, 1, 14.233, 30, 14.267, 28.544, 14.3, 28.555, 2, 14.35, 28.555, 1, 14.383, 28.565, 14.417, 27.373, 14.45, 27.382, 2, 14.5, 27.382, 1, 14.567, 27.392, 14.633, 29.99, 14.7, 30, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "Physics_AngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.317, 0, 0, 0.433, -0.923, 0, 0.683, 3.649, 0, 1.183, -5.348, 0, 1.567, -4.571, 0, 1.8, -4.603, 0, 1.967, -3.523, 0, 2.333, -8.505, 0, 2.767, 9.644, 0, 3.2, -7.489, 0, 4.6, 2.558, 0, 4.883, -9.25, 0, 5.117, 4.143, 0, 5.383, -10.205, 0, 5.817, 0.919, 0, 6.683, -12.572, 0, 9.133, 8.647, 2, 9.15, 8.647, 0, 10.05, -18.317, 0, 10.55, 11.763, 0, 11, -11.952, 0, 11.317, 9.91, 0, 11.617, -8.329, 0, 11.917, 8.254, 0, 12.233, -7.399, 0, 12.433, 6.211, 0, 12.683, -6.2, 0, 12.933, 6.951, 0, 13.2, -7.202, 0, 13.533, 11.993, 0, 13.95, -16.183, 0, 14.25, 11.101, 0, 14.567, -8.012, 0, 14.983, 4.793, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 2.179, 0, 0.6, -4.027, 0, 0.933, 5.662, 0, 1.317, -5.338, 0, 1.733, 3.396, 0, 1.983, -0.592, 0, 2.217, 1.027, 0, 2.567, -8.853, 0, 2.967, 16.174, 0, 3.35, -14.668, 0, 3.767, 8.395, 0, 4.183, -5.17, 0, 4.783, 8.894, 0, 5.033, -18.448, 0, 5.317, 14.325, 0, 5.683, -11.036, 0, 6.083, 10.542, 0, 6.517, -6.567, 0, 6.9, 0.057, 0, 7.167, -1.436, 0, 7.533, 2.998, 0, 7.95, -1.906, 0, 8.367, 1.242, 0, 8.783, -0.752, 0, 9.517, 4.617, 0, 10.033, -4.325, 0, 10.05, -4.274, 0, 10.233, -13.084, 0, 10.817, 24.301, 0, 11.083, -30, 2, 11.25, -30, 0, 11.433, 30, 2, 11.583, 30, 0, 11.783, -30, 2, 11.883, -30, 0, 12.167, 27.219, 0, 12.383, -17.056, 0, 12.633, 12.984, 0, 12.867, -19.276, 0, 13.15, 24.645, 0, 13.417, -24.051, 0, 13.75, 28.052, 0, 14.05, -30, 2, 14.167, -30, 0, 14.4, 30, 2, 14.5, 30, 0, 14.833, -25.543, 0, 15.217, 19.239, 1, 15.228, 19.239, 15.239, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.333, -3.792, 0, 0.55, 7.581, 0, 0.8, -10.803, 0, 1.15, 15.566, 0, 1.533, -11.665, 0, 1.917, 7.812, 0, 2.183, -1.403, 0, 2.433, 5.995, 0, 2.783, -26.06, 0, 3.083, 30, 2, 3.25, 30, 0, 3.533, -30, 2, 3.6, -30, 0, 3.967, 16.992, 0, 4.383, -11.746, 0, 4.967, 28.6, 0, 5.167, -30, 2, 5.3, -30, 0, 5.5, 30, 2, 5.583, 30, 0, 5.9, -29.088, 0, 6.283, 24.882, 0, 6.717, -11.759, 0, 7.05, -1.186, 0, 7.383, -6.86, 0, 7.767, 5.568, 0, 8.15, -4.498, 0, 8.567, 2.479, 0, 8.983, -1.669, 0, 9.233, -0.194, 0, 9.3, -0.267, 0, 9.75, 14.76, 0, 10.1, -0.07, 0, 10.15, 0.245, 0, 10.433, -30, 2, 10.483, -30, 0, 10.883, 30, 2, 11.133, 30, 1, 11.166, 30, 11.2, -30, 11.233, -30, 2, 11.5, -30, 0, 11.583, 30, 2, 11.833, 30, 0, 11.917, -30, 2, 12.15, -30, 0, 12.25, 30, 2, 12.417, 30, 0, 12.55, -30, 2, 12.633, -30, 0, 12.8, 30, 2, 12.85, 30, 0, 13, -30, 2, 13.133, -30, 1, 13.178, -30, 13.222, 30, 13.267, 30, 2, 13.417, 30, 1, 13.456, 30, 13.494, -30, 13.533, -30, 2, 13.717, -30, 1, 13.756, -30, 13.794, 30, 13.833, 30, 2, 14.083, 30, 1, 14.116, 30, 14.15, -30, 14.183, -30, 2, 14.433, -30, 0, 14.533, 30, 2, 14.783, 30, 1, 14.828, 30, 14.872, -30, 14.917, -30, 2, 15.167, -30, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_L", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.267, 0.295, 0, 0.317, 0.125, 0, 0.5, 2.2, 0, 0.783, -3.345, 0, 1.167, 4.056, 0, 1.6, -2.47, 0, 1.917, 1.327, 0, 2.133, 0.329, 0, 2.417, 2.05, 0, 2.8, -6.996, 0, 3.2, 9.576, 0, 3.633, -6.213, 0, 4.1, 3.115, 0, 4.55, -2.418, 0, 4.633, -2.152, 0, 4.7, -2.295, 0, 4.983, 8.278, 0, 5.233, -8.152, 0, 5.517, 5.289, 0, 5.883, -4.155, 0, 6.35, 4.031, 2, 6.367, 4.031, 0, 7.133, -1.886, 0, 7.733, 1.131, 0, 8.167, -0.887, 0, 8.633, 0.46, 0, 9.083, -0.317, 0, 9.183, -0.252, 0, 9.333, -0.467, 0, 9.8, 3.562, 0, 10.067, 1.306, 0, 10.167, 1.838, 0, 10.467, -10.498, 0, 11.017, 12.263, 0, 11.4, -17.271, 0, 11.75, 14.878, 0, 12.083, -9.154, 0, 12.333, 5.857, 0, 12.55, -4.29, 0, 12.8, 6.955, 0, 13.05, -7.637, 0, 13.333, 7.264, 0, 13.633, -8.805, 0, 14.033, 13.518, 0, 14.35, -17.365, 0, 14.7, 11.826, 0, 15.1, -7.712, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_R", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.267, -0.295, 0, 0.317, -0.125, 0, 0.5, -2.2, 0, 0.783, 3.345, 0, 1.167, -4.056, 0, 1.6, 2.47, 0, 1.917, -1.327, 0, 2.133, -0.329, 0, 2.417, -2.05, 0, 2.8, 6.996, 0, 3.2, -9.576, 0, 3.633, 6.213, 0, 4.1, -3.115, 0, 4.55, 2.418, 0, 4.633, 2.152, 0, 4.7, 2.295, 0, 4.983, -8.278, 0, 5.233, 8.152, 0, 5.517, -5.289, 0, 5.883, 4.155, 0, 6.35, -4.031, 2, 6.367, -4.031, 0, 7.133, 1.886, 0, 7.733, -1.131, 0, 8.167, 0.887, 0, 8.633, -0.46, 0, 9.083, 0.317, 0, 9.183, 0.252, 0, 9.333, 0.467, 0, 9.8, -3.562, 0, 10.067, -1.306, 0, 10.167, -1.838, 0, 10.467, 10.498, 0, 11.017, -12.263, 0, 11.4, 17.271, 0, 11.75, -14.878, 0, 12.083, 9.154, 0, 12.333, -5.857, 0, 12.55, 4.29, 0, 12.8, -6.955, 0, 13.05, 7.637, 0, 13.333, -7.264, 0, 13.633, 8.805, 0, 14.033, -13.518, 0, 14.35, 17.365, 0, 14.7, -11.826, 0, 15.1, 7.712, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 3.637, 0, 0.617, -7.459, 0, 1.133, 10.668, 2, 1.8, 10.668, 0, 1.917, 8, 0, 2.217, 16.967, 0, 2.567, -18.079, 0, 2.967, 18.075, 0, 3.317, -7.471, 0, 3.65, -0.277, 0, 3.933, -1.601, 0, 4.217, 0.077, 0, 4.45, -0.256, 0, 4.8, 14.665, 0, 5.017, -23.164, 0, 5.267, 21.615, 0, 5.583, -13.263, 0, 5.983, 6.839, 0, 6.033, 6.505, 0, 6.05, 6.583, 0, 7.017, -7.591, 0, 7.517, 1.676, 0, 7.867, -1.022, 0, 8.267, -0.086, 2, 8.283, -0.086, 0, 8.517, -0.146, 0, 9.567, 9.522, 0, 10.233, -27.541, 0, 10.833, 25.009, 0, 11.167, -21.659, 0, 11.417, 30, 2, 11.567, 30, 0, 11.8, -11.943, 0, 12.033, 30, 2, 12.183, 30, 0, 12.35, -14.251, 0, 12.6, 24.336, 0, 12.85, -19.325, 0, 13.117, 21.132, 0, 13.383, -17.856, 0, 13.65, 30, 2, 13.867, 30, 0, 14.117, -26.1, 0, 14.333, 30, 2, 14.483, 30, 0, 14.767, -14.046, 0, 15.133, 8.694, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -3.637, 0, 0.617, 7.459, 0, 1.133, -10.668, 2, 1.8, -10.668, 0, 1.917, -8, 0, 2.2, -16.874, 0, 2.417, 22.048, 0, 2.683, -12.494, 0, 3.133, 9.889, 0, 3.4, -6.805, 0, 3.683, 2.701, 0, 3.967, -0.978, 0, 4.033, -0.75, 0, 4.067, -1.582, 0, 4.267, 1.026, 0, 4.55, -0.437, 0, 4.6, -0.386, 0, 4.733, -7.151, 0, 4.967, 21.366, 0, 5.167, -29.719, 0, 5.417, 26.091, 0, 5.683, -13.753, 0, 5.983, 4.859, 0, 6.183, -1.656, 0, 6.4, 0.813, 0, 6.583, 0.24, 0, 6.817, 1.698, 0, 7.133, -1.562, 0, 7.15, -1.561, 0, 7.167, -1.562, 0, 7.367, -1.066, 0, 7.4, -1.105, 0, 7.633, 1.648, 0, 7.917, -0.788, 0, 8.217, 0.213, 0, 8.533, -0.055, 2, 8.55, -0.055, 0, 8.983, 0.002, 2, 9, 0.002, 2, 9.017, 0.002, 2, 9.05, 0.002, 0, 9.367, -2.573, 0, 9.683, 4.455, 0, 9.917, -0.401, 0, 10.183, 12.584, 0, 10.4, -17.323, 0, 10.633, 6.107, 0, 10.8, -5.478, 0, 11.033, 15.06, 0, 11.333, -26.232, 0, 11.633, 26.356, 0, 11.933, -25.459, 0, 12.283, 27.518, 0, 12.483, -30, 2, 12.517, -30, 0, 12.717, 30, 2, 12.767, 30, 0, 13, -29.785, 0, 13.25, 27.387, 0, 13.533, -26.561, 0, 13.867, 15.579, 0, 13.95, 13.171, 0, 14.017, 14.854, 0, 14.233, -30, 2, 14.3, -30, 0, 14.5, 30, 2, 14.567, 30, 0, 14.817, -13.75, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -3.637, 0, 0.617, 7.459, 0, 1.133, -10.668, 2, 1.8, -10.668, 0, 1.917, -8.001, 0, 2.3, -18.673, 0, 2.733, 18.486, 0, 3.133, -13.377, 0, 4.6, 5.71, 0, 4.85, -19.669, 0, 5.05, 9.334, 0, 5.3, -20.835, 0, 5.767, 0, 0, 6.5, -25.093, 0, 7.017, 14.48, 0, 7.55, -2.8, 0, 7.983, 1.304, 0, 8.55, 0.073, 0, 8.75, 0.088, 0, 9.633, -13.613, 0, 10.217, 30, 2, 10.317, 30, 0, 10.85, -26.589, 0, 11.133, 30, 2, 11.283, 30, 0, 11.517, -16.672, 0, 11.767, 30, 2, 11.9, 30, 0, 12.15, -5.778, 0, 12.333, 30, 2, 12.383, 30, 0, 12.633, -11.027, 0, 12.85, 16.566, 0, 13.117, -19.464, 0, 13.4, 30, 2, 13.467, 30, 0, 13.8, -16.416, 0, 14.033, 30, 2, 14.25, 30, 0, 14.467, -16.392, 0, 14.817, 15.509, 0, 15.2, -9.781, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 3.637, 0, 0.617, -7.459, 0, 1.133, 10.668, 2, 1.8, 10.668, 0, 1.917, 8.001, 0, 2.3, 18.673, 0, 2.733, -18.486, 0, 3.133, 13.377, 0, 4.6, -5.71, 0, 4.85, 19.669, 0, 5.05, -9.334, 0, 5.3, 20.835, 0, 5.767, 0, 0, 6.483, 25.005, 0, 6.733, -26.873, 0, 7.083, 9.545, 0, 7.25, 3.645, 0, 7.283, 3.739, 0, 7.683, -2.932, 0, 8.05, 1.066, 0, 8.417, -0.196, 0, 8.817, 0.044, 0, 9.167, 0.007, 0, 9.4, 4.024, 0, 9.817, -4.246, 0, 10.05, -2.914, 0, 10.2, -16.674, 0, 10.467, 19.74, 0, 10.7, 0.618, 0, 10.783, 2.035, 0, 11.083, -29.147, 0, 11.35, 30, 2, 11.45, 30, 0, 11.633, -30, 2, 11.767, -30, 0, 11.983, 30, 2, 12.05, 30, 0, 12.283, -30, 2, 12.35, -30, 0, 12.517, 30, 2, 12.6, 30, 0, 12.817, -24.364, 0, 13.05, 30, 2, 13.117, 30, 0, 13.283, -30, 2, 13.4, -30, 1, 13.478, -30, 13.555, 29.296, 13.633, 29.296, 0, 13.967, -30, 2, 14.1, -30, 0, 14.25, 30, 2, 14.4, 30, 0, 14.617, -28.649, 0, 14.967, 18.443, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "Jewelry_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.367, 0.253, 0, 0.583, -0.653, 0, 0.933, 0.982, 0, 1.317, -0.927, 0, 1.733, 0.589, 0, 1.983, -0.124, 0, 2.217, 0.167, 0, 2.567, -1.464, 0, 2.967, 2.674, 0, 3.35, -2.4, 0, 3.767, 1.362, 0, 4.183, -0.853, 0, 4.783, 1.489, 0, 5.033, -2.916, 0, 5.3, 2.115, 0, 5.683, -1.68, 0, 6.067, 1.544, 0, 6.517, -0.954, 0, 6.883, -0.073, 0, 7.133, -0.248, 0, 7.517, 0.52, 0, 7.933, -0.331, 0, 8.35, 0.215, 0, 8.767, -0.13, 0, 9.517, 0.764, 0, 10.233, -2.619, 0, 10.717, 2.658, 0, 11.15, -4.709, 0, 11.517, 5.021, 0, 11.85, -3.9, 0, 12.183, 2.712, 0, 12.383, -0.981, 0, 12.617, 0.803, 0, 12.85, -1.906, 0, 13.133, 2.401, 0, 13.433, -2.72, 0, 13.783, 3.592, 0, 14.15, -5.651, 0, 14.483, 4.41, 0, 14.867, -3.156, 0, 15.233, 2.342, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "Jewelry_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.35, -0.272, 0, 0.533, 0.708, 0, 0.783, -1.597, 0, 1.15, 2.588, 0, 1.533, -1.982, 0, 1.9, 1.337, 0, 2.183, -0.282, 0, 2.433, 0.989, 0, 2.783, -4.191, 0, 3.167, 6.25, 0, 3.567, -4.826, 0, 3.967, 2.639, 0, 4.383, -1.914, 0, 4.95, 4.378, 0, 5.217, -5.835, 0, 5.533, 4.765, 0, 5.883, -4.205, 0, 6.283, 3.542, 0, 6.717, -1.615, 0, 7.033, -0.337, 0, 7.35, -1.216, 0, 7.75, 0.964, 0, 8.133, -0.775, 0, 8.55, 0.429, 2, 8.567, 0.429, 0, 8.967, -0.289, 0, 9.233, -0.013, 0, 9.3, -0.028, 0, 9.75, 2.247, 0, 10.083, 0.179, 0, 10.15, 0.249, 0, 10.433, -6.141, 0, 10.967, 6.209, 0, 11.35, -9.682, 0, 11.7, 9.851, 0, 12.033, -7.886, 0, 12.35, 5.892, 0, 12.6, -2.136, 0, 12.8, 2.145, 0, 13.033, -4.312, 0, 13.317, 5.407, 0, 13.633, -6.55, 0, 13.983, 8.7, 0, 14.317, -10.98, 0, 14.667, 8.512, 0, 15.05, -6.673, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "Jewelry_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.45, -0.327, 0, 0.683, 0.943, 0, 0.967, -1.871, 0, 1.3, 2.359, 0, 1.65, -1.966, 0, 2.017, 1.407, 0, 2.317, -0.911, 0, 2.617, 1.978, 0, 2.95, -4.075, 0, 3.3, 5.334, 0, 3.683, -4.318, 0, 4.083, 2.717, 0, 4.5, -1.723, 0, 4.767, -0.23, 0, 4.85, -0.367, 0, 5.1, 4.39, 0, 5.367, -6.307, 0, 5.683, 5.794, 0, 6.017, -4.748, 0, 6.383, 3.102, 0, 6.783, -1.461, 0, 7.15, 0.808, 0, 7.517, -0.935, 0, 7.883, 0.963, 0, 8.267, -0.72, 0, 8.667, 0.442, 0, 9.083, -0.27, 0, 9.283, -0.095, 0, 9.55, -0.606, 0, 9.933, 1.067, 0, 10.133, 0.529, 0, 10.3, 1.69, 0, 10.6, -4.547, 0, 11.1, 4.341, 0, 11.467, -8.127, 0, 11.817, 9.243, 0, 12.167, -8.45, 0, 12.483, 6.832, 0, 12.75, -3.502, 0, 12.967, 2.726, 0, 13.2, -4.583, 0, 13.483, 6.413, 0, 13.783, -7.584, 0, 14.1, 8.661, 0, 14.433, -10.282, 0, 14.8, 8.505, 0, 15.183, -6.476, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.417, -0.817, 0, 0.883, 1.648, 0, 1.283, -1.964, 0, 1.683, 1.63, 0, 2.083, -1.212, 0, 2.517, 0.693, 0, 2.917, -0.958, 0, 3.283, 0.869, 0, 4.067, -1.19, 0, 4.45, 1.517, 0, 5, -1.587, 0, 5.3, 1.58, 0, 5.65, -1.479, 0, 6.067, 1.169, 0, 6.533, -0.645, 0, 7.2, 0.629, 0, 7.8, -0.309, 0, 8.117, 0.505, 0, 8.5, -0.561, 0, 8.95, 0.406, 0, 9.367, -0.78, 0, 9.767, 2.054, 0, 10.117, -1.691, 0, 10.817, 2.059, 0, 11.183, -3.286, 0, 11.5, 3.119, 0, 11.817, -2.151, 0, 12.117, 1.64, 0, 12.317, -1.113, 0, 12.533, 1.095, 0, 12.767, -1.639, 0, 13.033, 1.712, 0, 13.35, -1.659, 0, 13.7, 1.947, 0, 14, -2.596, 0, 14.3, 2.286, 0, 14.65, -2.287, 0, 15.05, 1.996, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.367, 0.43, 0, 0.65, -2.19, 0, 1.083, 3.842, 0, 1.483, -3.829, 0, 1.883, 3.324, 0, 2.283, -2.498, 0, 2.75, 1.414, 0, 3.1, -2.381, 0, 3.483, 1.772, 0, 3.767, 1.184, 0, 3.8, 1.187, 0, 4.283, -2.873, 0, 4.633, 3.041, 0, 5.183, -3.269, 0, 5.517, 3.494, 0, 5.85, -3.35, 0, 6.267, 2.406, 0, 6.733, -0.888, 0, 6.833, -0.831, 0, 7.083, -1.367, 0, 7.417, 0.973, 0, 8, -0.702, 0, 8.317, 1.306, 0, 8.7, -1.313, 0, 9.2, 0.874, 0, 9.6, -2.145, 0, 9.933, 4.683, 0, 10.317, -3.19, 0, 10.6, -0.813, 0, 10.7, -0.998, 0, 11, 4.726, 0, 11.333, -6.474, 0, 11.667, 5.968, 0, 11.983, -4.397, 0, 12.267, 3.586, 0, 12.5, -2.115, 0, 12.717, 2.242, 0, 12.95, -3.222, 0, 13.233, 3.574, 0, 13.533, -3.787, 0, 13.883, 4.818, 0, 14.167, -5.138, 0, 14.483, 4.74, 0, 14.833, -4.772, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.367, 0.759, 0, 0.583, -1.958, 0, 0.933, 2.946, 0, 1.317, -2.781, 0, 1.733, 1.768, 0, 1.983, -0.372, 0, 2.217, 0.501, 0, 2.567, -4.393, 0, 2.967, 8.021, 0, 3.35, -7.201, 0, 3.767, 4.087, 0, 4.183, -2.558, 0, 4.783, 4.468, 0, 5.033, -8.749, 0, 5.3, 6.344, 0, 5.683, -5.039, 0, 6.067, 4.631, 0, 6.517, -2.861, 0, 6.883, -0.22, 0, 7.133, -0.743, 0, 7.517, 1.559, 0, 7.933, -0.992, 0, 8.35, 0.645, 0, 8.767, -0.391, 0, 9.517, 2.292, 0, 10.233, -7.856, 0, 10.717, 7.973, 0, 11.15, -14.126, 0, 11.517, 15.063, 0, 11.85, -11.699, 0, 12.183, 8.135, 0, 12.383, -2.943, 0, 12.617, 2.41, 0, 12.85, -5.717, 0, 13.133, 7.202, 0, 13.433, -8.159, 0, 13.783, 10.775, 0, 14.15, -16.954, 0, 14.483, 13.229, 0, 14.867, -9.467, 0, 15.233, 7.026, 1, 15.239, 7.026, 15.244, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.35, -0.815, 0, 0.533, 2.123, 0, 0.783, -4.791, 0, 1.15, 7.763, 0, 1.533, -5.948, 0, 1.9, 4.011, 0, 2.183, -0.846, 0, 2.433, 2.966, 0, 2.783, -12.574, 0, 3.167, 18.751, 0, 3.567, -14.478, 0, 3.967, 7.916, 0, 4.383, -5.743, 0, 4.95, 13.134, 0, 5.217, -17.505, 0, 5.533, 14.294, 0, 5.883, -12.616, 0, 6.283, 10.628, 0, 6.717, -4.844, 0, 7.033, -1.011, 0, 7.35, -3.649, 0, 7.75, 2.893, 0, 8.133, -2.325, 0, 8.567, 1.286, 0, 8.967, -0.868, 0, 9.233, -0.038, 0, 9.3, -0.082, 0, 9.75, 6.741, 0, 10.083, 0.537, 0, 10.15, 0.749, 0, 10.433, -18.421, 0, 10.967, 18.627, 0, 11.35, -29.047, 0, 11.7, 29.552, 0, 12.033, -23.658, 0, 12.35, 17.675, 0, 12.6, -6.407, 0, 12.8, 6.435, 0, 13.033, -12.937, 0, 13.317, 16.221, 0, 13.633, -19.65, 0, 13.983, 26.099, 0, 14.283, -30, 2, 14.35, -30, 0, 14.667, 25.537, 0, 15.05, -20.02, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.45, -0.981, 0, 0.683, 2.828, 0, 0.967, -5.612, 0, 1.3, 7.077, 0, 1.65, -5.898, 0, 2.017, 4.22, 0, 2.317, -2.733, 0, 2.617, 5.935, 0, 2.95, -12.225, 0, 3.3, 16.002, 0, 3.683, -12.954, 0, 4.083, 8.15, 0, 4.5, -5.169, 0, 4.767, -0.69, 0, 4.85, -1.1, 0, 5.1, 13.169, 0, 5.367, -18.92, 0, 5.683, 17.383, 0, 6.017, -14.244, 0, 6.383, 9.306, 0, 6.783, -4.382, 0, 7.15, 2.423, 0, 7.517, -2.805, 0, 7.883, 2.888, 0, 8.267, -2.159, 0, 8.667, 1.326, 0, 9.083, -0.81, 0, 9.283, -0.286, 0, 9.55, -1.818, 0, 9.933, 3.2, 0, 10.133, 1.586, 0, 10.3, 5.071, 0, 10.6, -13.641, 0, 11.1, 13.024, 0, 11.467, -24.381, 0, 11.817, 27.73, 0, 12.167, -25.35, 0, 12.483, 20.496, 0, 12.75, -10.507, 0, 12.967, 8.177, 0, 13.2, -13.749, 0, 13.483, 19.24, 0, 13.783, -22.751, 0, 14.1, 25.981, 0, 14.417, -30, 2, 14.45, -30, 0, 14.8, 25.516, 0, 15.183, -19.427, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.533, -1.043, 0, 0.783, 2.774, 0, 1.083, -6.531, 0, 1.4, 9.303, 0, 1.767, -8.711, 0, 2.117, 6.478, 0, 2.433, -4.256, 0, 2.717, 7.239, 0, 3.05, -14.778, 0, 3.4, 20.316, 0, 3.767, -18.159, 0, 4.167, 12.441, 0, 4.567, -7.904, 0, 5.2, 11.94, 0, 5.483, -20.983, 0, 5.8, 22.701, 0, 6.133, -20.444, 0, 6.483, 14.818, 0, 6.867, -7.707, 0, 7.233, 4.312, 0, 7.6, -4.189, 0, 7.983, 4.059, 0, 8.35, -3.29, 0, 8.733, 2.145, 0, 9.15, -1.282, 0, 9.4, -0.228, 0, 9.667, -1.87, 0, 10.033, 4.09, 0, 10.233, 1.766, 0, 10.4, 3.884, 0, 10.7, -15.103, 0, 11.117, 14.437, 0, 11.533, -26.997, 0, 11.867, 30, 2, 11.95, 30, 0, 12.233, -30, 2, 12.3, -30, 0, 12.6, 26.427, 0, 12.883, -14.847, 0, 13.117, 7.541, 0, 13.333, -12.011, 0, 13.583, 20.608, 0, 13.867, -27.762, 0, 14.15, 30, 2, 14.217, 30, 0, 14.467, -30, 2, 14.583, -30, 0, 14.85, 30, 2, 14.933, 30, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.317, 0.579, 0, 0.633, -1.104, 0, 0.9, 2.99, 0, 1.183, -7.564, 0, 1.517, 11.862, 0, 1.867, -12.299, 0, 2.217, 9.786, 0, 2.55, -6.618, 0, 2.833, 9.155, 0, 3.133, -17.712, 0, 3.483, 24.599, 0, 3.85, -23.636, 0, 4.25, 17.746, 0, 4.65, -11.856, 0, 5.283, 11.068, 0, 5.583, -23.238, 0, 5.9, 27.924, 0, 6.233, -27.032, 0, 6.583, 21.561, 0, 6.95, -12.735, 0, 7.333, 7.455, 0, 7.683, -6.569, 0, 8.067, 5.819, 0, 8.45, -4.893, 0, 8.833, 3.425, 0, 9.217, -2.086, 0, 9.517, 0.059, 0, 9.767, -1.971, 0, 10.133, 5.047, 0, 10.367, 1.643, 0, 10.5, 2.671, 0, 10.783, -16.087, 0, 11.15, 18.259, 0, 11.6, -28.51, 0, 11.917, 30, 2, 12.033, 30, 0, 12.3, -30, 2, 12.417, -30, 0, 12.667, 30, 2, 12.75, 30, 0, 13.017, -19.832, 0, 13.25, 7.504, 0, 13.467, -10.382, 0, 13.7, 21.369, 0, 13.95, -30, 2, 13.983, -30, 0, 14.2, 30, 2, 14.317, 30, 0, 14.533, -30, 2, 14.683, -30, 0, 14.9, 30, 2, 15.033, 30, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.4, 0.945, 0, 0.733, -1.275, 0, 1.017, 3.247, 0, 1.3, -8.71, 0, 1.617, 14.74, 0, 1.967, -16.613, 0, 2.317, 14.236, 0, 2.65, -10.191, 0, 2.933, 11.909, 0, 3.217, -21.247, 0, 3.55, 29.032, 0, 3.933, -28.899, 0, 4.333, 23.302, 0, 4.733, -16.724, 0, 5.117, 7.899, 0, 5.2, 7.51, 0, 5.367, 9.324, 0, 5.667, -25.083, 0, 5.95, 30, 2, 6.017, 30, 0, 6.283, -30, 2, 6.35, -30, 0, 6.667, 28.666, 0, 7.05, -19.138, 0, 7.417, 12.044, 0, 7.783, -9.86, 0, 8.167, 8.543, 0, 8.55, -7.176, 0, 8.917, 5.322, 0, 9.3, -3.41, 0, 9.633, 0.696, 0, 9.883, -2.198, 0, 10.233, 6.189, 0, 10.5, 0.837, 0, 10.583, 1.084, 0, 10.85, -16.527, 0, 11.217, 22.277, 0, 11.65, -28.718, 0, 11.983, 30, 2, 12.1, 30, 0, 12.367, -30, 2, 12.5, -30, 0, 12.75, 30, 2, 12.867, 30, 0, 13.133, -27.148, 0, 13.4, 10.14, 0, 13.6, -6.374, 0, 13.8, 20.51, 0, 14.017, -30, 2, 14.067, -30, 0, 14.267, 30, 2, 14.4, 30, 0, 14.6, -30, 2, 14.783, -30, 0, 14.983, 30, 2, 15.117, 30, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.267, 0.516, 0, 0.3, 0.505, 0, 0.5, 0.882, 0, 0.8, -1.413, 0, 1.117, 3.148, 0, 1.417, -9.707, 0, 1.717, 17.814, 0, 2.067, -21.491, 0, 2.417, 19.687, 0, 2.75, -14.967, 0, 3.05, 15.605, 0, 3.3, -25.551, 0, 3.583, 30, 2, 3.633, 30, 0, 3.967, -30, 2, 4.033, -30, 0, 4.4, 28.237, 0, 4.8, -21.889, 0, 5.2, 12.308, 0, 5.75, -25.929, 0, 6.017, 30, 2, 6.117, 30, 0, 6.333, -30, 2, 6.45, -30, 0, 6.717, 30, 2, 6.8, 30, 0, 7.133, -25.944, 0, 7.517, 17.978, 0, 7.883, -14.496, 0, 8.25, 12.452, 0, 8.633, -10.314, 0, 9.017, 8.01, 0, 9.4, -5.474, 0, 9.733, 1.891, 0, 10, -2.689, 0, 10.333, 7.665, 0, 10.917, -16.297, 0, 11.267, 25.36, 0, 11.683, -28.033, 0, 12.033, 30, 2, 12.15, 30, 0, 12.433, -30, 2, 12.583, -30, 0, 12.817, 30, 2, 12.967, 30, 0, 13.2, -30, 2, 13.283, -30, 0, 13.533, 15.247, 0, 13.717, -2.962, 0, 13.9, 17.265, 0, 14.067, -30, 2, 14.117, -30, 0, 14.333, 30, 2, 14.483, 30, 0, 14.683, -30, 2, 14.75, -30, 1, 14.756, -30, 14.761, 30, 14.767, 30, 2, 14.817, 30, 1, 14.85, 30, 14.884, -30, 14.917, -30, 2, 15, -30, 1, 15.039, -30, 15.078, 28.972, 15.117, 28.972, 0, 15.25, 0, 2, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.417, 1, 0, 3.25, 0, 0, 4.417, 1, 0, 6.25, 0, 0, 7.417, 1, 0, 9.25, 0, 0, 10.417, 1, 0, 12.25, 0, 0, 13.417, 1, 0, 15.25, 0, 1, 15.333, 0, 15.417, 0.046, 15.5, 0.118]}, {"Target": "Parameter", "Id": "fenshangxuanzhuang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 5.333, 2.035, 10.417, 4.071, 15.5, 6.106]}, {"Target": "Parameter", "Id": "Mail_Transparency6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamSad2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.5, 1]}, {"Target": "Parameter", "Id": "ParamCRY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.2, 0, 15.5, -5.2]}, {"Target": "Parameter", "Id": "Position_Coordinates", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.5, 1]}, {"Target": "Parameter", "Id": "Man_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.5, 1]}, {"Target": "Parameter", "Id": "Man_TMD2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "BG_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.5, 1]}, {"Target": "Parameter", "Id": "FG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "FG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "BG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "BG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "BG_White4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "BG_White5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "BG_White2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "BG_White3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "All_Size2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "All_Size3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "All_Size4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "tuerzuo3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "faqiehuan", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "faqiehuan2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "faqiehuan4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "faqiehuan3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "tuerzuo4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Mail_Change", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "dafengshangzhuanchang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "tongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamTongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "EyeFrame_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamBrowY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamSigh", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamDarken", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamCry", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_B", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_C", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "MB_Unidirectional_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "jing<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "huqi1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "huqi2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamShoulderLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamArmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.5, 1]}, {"Target": "Parameter", "Id": "ParamForearmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamArmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamArmL_ForearmL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamForearmL_HandL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamHandLSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "xiongjiya", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "xiongjiya5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "xiongjiya3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "xiongjiya4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "xiongjiya2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamShoulderRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamArmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.5, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamArmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamArmR_ForearmR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamForearm<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.5, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_HandR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamHandRSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 15.5, 0.6]}, {"Target": "Parameter", "Id": "Change_idle_physics11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Param103", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "taiqiugundong", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -22.5, 0, 15.5, -22.5]}, {"Target": "Parameter", "Id": "Change_idle_physics32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamLegL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.5, 1]}, {"Target": "Parameter", "Id": "ParamLegLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamCrusLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamCrusLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>oodLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamFoodLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamFoodLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamLegR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 15.5, 1]}, {"Target": "Parameter", "Id": "ParamLegRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamCrusRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamCrusRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamCrus<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>ood<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamFoodRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamFoodRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Physics_Provisional", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "physics_Add_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "physics_Add_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamBodyStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Physics_AngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Physics_AngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Physics_Body_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Physics_Body_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Physics_breast", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamBreath2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "touch_drag1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "touch_drag2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "touch_drag3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "touch_drag4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "touch_drag5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "touch_drag6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "touch_drag7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "touch_drag8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "touch_drag9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>pian<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh654", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh655", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh657", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh656", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh658", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh659", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh660", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 15.5, 0]}], "UserData": [{"Time": 0.25, "Value": ""}, {"Time": 15.0, "Value": ""}]}