{"Version": 3, "Meta": {"Duration": 18.167, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 303, "TotalSegmentCount": 2801, "TotalPointCount": 3051, "UserDataCount": 2, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 2.167, 0, 4.083, 0.055, 6, 0.209, 1, 7.578, 0.336, 9.155, 0.457, 10.733, 0.643, 1, 11.211, 0.699, 11.689, 2.34, 12.167, 2.34, 2, 13.917, 2.34, 2, 14.7, 2.34, 1, 14.772, 2.34, 14.845, 6.14, 14.917, 7.904, 1, 15.095, 12.246, 15.272, 13.32, 15.45, 13.32, 2, 15.867, 13.32, 0, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 3.744, 0, 7.239, -0.063, 10.733, -0.257, 1, 11.211, -0.283, 11.689, -1.08, 12.167, -1.08, 2, 13.917, -1.08, 2, 14.7, -1.08, 1, 14.772, -1.08, 14.845, -4.509, 14.917, -5.571, 1, 15.095, -8.186, 15.272, -8.7, 15.45, -8.7, 2, 15.867, -8.7, 1, 15.872, -8.7, 15.878, 0, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 4.433, 0, 0, 5.567, 0.01, 0, 6.333, -0.199, 0, 8.167, 0.334, 0, 9.583, 0, 1, 9.966, 0, 10.35, -0.09, 10.733, 0.275, 1, 11.211, 0.73, 11.689, 3.6, 12.167, 3.6, 2, 13.917, 3.6, 2, 14.7, 3.6, 2, 15.867, 3.6, 0, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 12.167, 2.28, 2, 13.917, 2.28, 2, 14.7, 2.28, 2, 15.867, 2.28, 0, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "FG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 10.683, 0, 2, 12.167, 0, 2, 13.917, 0, 2, 15.45, 0, 2, 15.467, 0, 0, 15.867, 1, 0, 16.35, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "BG_White3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 4.222, 0, 8.195, 0.282, 12.167, 0.377, 1, 12.75, 0.391, 13.334, 0.381, 13.917, 0.381, 1, 14.567, 0.381, 15.217, 0.381, 15.867, 0.38, 0, 15.933, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "All_Size2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 12.167, -5.94, 2, 13.917, -5.94, 2, 15.867, -5.94, 0, 15.933, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.75, -0.5, 0, 1.25, 0.5, 0, 1.683, -0.3, 0, 2.25, 0.7, 0, 3.083, -0.5, 0, 4.117, 0.7, 0, 5.417, -0.3, 0, 6.333, 0.8, 0, 7.917, -0.8, 0, 9.65, 0.7, 0, 10.783, -0.3, 0, 11.75, 0.7, 1, 12.372, 0.7, 12.995, 0.684, 13.617, 0.5, 1, 13.895, 0.418, 14.172, -0.3, 14.45, -0.3, 0, 15.583, 0.7, 0, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY_SD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.35, 0, 0, 0.467, 1, 0, 0.583, 0, 0, 0.767, 1, 0, 0.883, 0, 0, 1.117, 1, 0, 1.283, 0, 0, 1.467, 1, 0, 1.65, 0, 2, 2.183, 0, 0, 2.3, 1, 0, 2.417, 0, 0, 2.6, 1, 0, 2.717, 0, 0, 2.95, 1, 0, 3.117, 0, 0, 3.3, 1, 0, 3.417, 0, 0, 3.6, 1, 0, 3.717, 0, 0, 3.9, 1, 0, 4.35, 0, 2, 4.983, 0, 0, 5.1, 1, 0, 5.217, 0, 0, 5.4, 1, 0, 5.517, 0, 0, 5.75, 1, 0, 5.917, 0, 0, 6.1, 1, 0, 6.217, 0, 0, 6.4, 1, 0, 6.517, 0, 0, 6.7, 1, 0, 6.833, 0, 0, 6.983, 1, 0, 7.15, 0, 0, 7.267, 1, 0, 7.383, 0, 0, 7.567, 1, 0, 7.683, 0, 0, 7.917, 1, 0, 8.083, 0, 0, 8.267, 1, 0, 8.383, 0, 0, 8.567, 1, 0, 8.683, 0, 0, 8.867, 1, 0, 9, 0, 0, 9.15, 1, 0, 9.317, 0, 0, 9.433, 1, 0, 9.55, 0, 0, 9.733, 1, 0, 9.9, 0, 0, 10.117, 1, 0, 10.4, 0, 2, 11.167, 0, 0, 11.283, 1, 0, 11.4, 0, 0, 11.583, 1, 0, 11.917, 0, 2, 12.4, 0, 0, 12.517, 1, 0, 12.633, 0, 0, 12.817, 1, 0, 12.933, 0, 0, 13.167, 1, 0, 13.333, 0, 0, 13.517, 1, 0, 13.633, 0, 0, 13.817, 1, 0, 14.35, 0, 2, 14.95, 0, 0, 15.067, 1, 0, 15.183, 0, 0, 15.367, 1, 0, 15.483, 0, 0, 15.717, 1, 1, 15.767, 1, 15.817, 0.531, 15.867, 0.028, 1, 15.872, 0, 15.878, 0, 15.883, 0, 0, 16.067, 1, 0, 16.183, 0, 0, 16.367, 1, 0, 16.483, 0, 0, 16.667, 1, 0, 16.8, 0, 0, 16.95, 1, 0, 17.25, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 0.6, 1, 0, 0.733, 0, 0, 0.917, 1.3, 0, 1, 1, 2, 2.65, 1, 0, 2.783, 0, 0, 2.967, 1.3, 0, 3.05, 1, 2, 4.383, 1, 0, 4.517, 0, 0, 4.7, 1.3, 0, 4.783, 1, 2, 10.85, 1, 0, 10.983, 0, 0, 11.167, 1.3, 0, 11.25, 1, 2, 12.167, 1, 2, 15.55, 1, 0, 15.8, 0, 0, 16.417, 1, 2, 17.917, 1, 2, 18.167, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 0.6, 1, 0, 0.733, 0, 0, 0.917, 1.3, 0, 1, 1, 2, 2.65, 1, 0, 2.783, 0, 0, 2.967, 1.3, 0, 3.05, 1, 2, 4.383, 1, 0, 4.517, 0, 0, 4.7, 1.3, 0, 4.783, 1, 2, 10.85, 1, 0, 10.983, 0, 0, 11.167, 1.3, 1, 11.195, 1.3, 11.222, 1.007, 11.25, 1, 1, 11.556, 0.927, 11.861, 0.9, 12.167, 0.9, 0, 15.55, 1, 0, 15.8, 0, 0, 16.417, 1, 2, 17.917, 1, 2, 18.167, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 10.683, 0, 0, 11.117, 0.1, 1, 11.467, 0.1, 11.817, -0.551, 12.167, -0.6, 1, 13.261, -0.66, 14.356, -0.72, 15.45, -0.78, 2, 15.867, -0.78, 0, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 10.683, 0, 0, 11.117, -1, 1, 11.467, -1, 11.817, 0.617, 12.167, 0.7, 1, 13.261, 0.8, 14.356, 0.9, 15.45, 1, 2, 15.867, 1, 0, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "EyeFrame_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 7.383, -0.2, 0, 12.533, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.983, 0, 0, 1.567, -1, 2, 3.617, -1, 0, 11.683, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamBrowForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.833, -1, 0, 10.683, 0, 2, 12.167, 0, 2, 15.45, 0, 2, 15.867, 0, 2, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamBrowAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.833, 1, 0, 10.683, 0, 2, 12.167, 0, 2, 15.45, 0, 2, 15.867, 0, 2, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamEyeLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.6, 0, 0, 0.667, 1, 2, 0.817, 1, 0, 0.833, -1, 2, 0.967, -1, 0, 1.267, 0.244, 0, 1.583, -0.068, 0, 1.883, 0.019, 0, 2.183, -0.005, 2, 2.2, -0.005, 0, 2.5, 0.002, 2, 2.517, 0.002, 0, 2.65, 0, 0, 2.717, 1, 2, 2.867, 1, 0, 2.883, -1, 2, 3.017, -1, 0, 3.317, 0.247, 0, 3.633, -0.069, 0, 3.933, 0.019, 0, 4.25, -0.005, 0, 4.45, 1, 2, 4.6, 1, 0, 4.617, -1, 2, 4.75, -1, 0, 5.05, 0.235, 0, 5.367, -0.065, 0, 5.667, 0.018, 0, 5.967, -0.005, 2, 5.983, -0.005, 0, 6.267, 0.001, 2, 6.317, 0.001, 0, 6.533, 0, 2, 6.55, 0, 2, 6.567, 0, 2, 6.633, 0, 2, 6.65, 0, 2, 6.683, 0, 2, 6.7, 0, 2, 6.717, 0, 2, 6.733, 0, 2, 6.767, 0, 2, 6.783, 0, 2, 6.817, 0, 2, 6.833, 0, 2, 7.017, 0, 2, 7.033, 0, 2, 10.85, 0, 0, 10.917, 1, 2, 11.067, 1, 0, 11.083, -1, 2, 11.217, -1, 0, 11.5, 0.155, 0, 11.8, -0.146, 0, 12.217, 0.046, 0, 12.517, -0.009, 0, 12.85, 0.009, 2, 12.867, 0.009, 0, 13.083, 0.007, 2, 13.1, 0.007, 2, 13.117, 0.007, 0, 15.683, 1, 2, 15.783, 1, 0, 16.05, -0.837, 0, 16.483, 0.262, 0, 16.8, -0.073, 0, 17.1, 0.02, 0, 17.417, -0.006, 0, 17.717, 0.002, 2, 17.75, 0.002, 0, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamEyeRsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.6, 0, 0, 0.667, 1, 2, 0.817, 1, 0, 0.833, -1, 2, 0.967, -1, 0, 1.267, 0.244, 0, 1.583, -0.068, 0, 1.883, 0.019, 0, 2.183, -0.005, 2, 2.2, -0.005, 0, 2.5, 0.002, 2, 2.517, 0.002, 0, 2.65, 0, 0, 2.717, 1, 2, 2.867, 1, 0, 2.883, -1, 2, 3.017, -1, 0, 3.317, 0.247, 0, 3.633, -0.069, 0, 3.933, 0.019, 0, 4.25, -0.005, 0, 4.45, 1, 2, 4.6, 1, 0, 4.617, -1, 2, 4.75, -1, 0, 5.05, 0.235, 0, 5.367, -0.065, 0, 5.667, 0.018, 0, 5.967, -0.005, 2, 5.983, -0.005, 0, 6.267, 0.001, 2, 6.317, 0.001, 0, 6.533, 0, 2, 6.55, 0, 2, 6.567, 0, 2, 6.633, 0, 2, 6.65, 0, 2, 6.683, 0, 2, 6.7, 0, 2, 6.717, 0, 2, 6.733, 0, 2, 6.767, 0, 2, 6.783, 0, 2, 6.817, 0, 2, 6.833, 0, 2, 7.017, 0, 2, 7.033, 0, 2, 10.85, 0, 0, 10.917, 1, 2, 11.067, 1, 0, 11.083, -1, 2, 11.217, -1, 0, 11.517, 0.214, 0, 11.817, -0.093, 0, 12.183, 0.025, 0, 12.483, -0.006, 2, 12.5, -0.006, 0, 12.8, 0.004, 2, 12.833, 0.004, 0, 13.05, 0.002, 2, 13.117, 0.002, 2, 13.133, 0.002, 2, 13.15, 0.002, 0, 13.317, 0.003, 2, 13.333, 0.003, 0, 13.383, 0.004, 2, 13.4, 0.004, 2, 13.417, 0.004, 2, 13.433, 0.004, 2, 13.45, 0.004, 2, 13.483, 0.004, 2, 13.5, 0.004, 2, 13.533, 0.004, 2, 13.55, 0.004, 2, 13.583, 0.004, 2, 13.6, 0.004, 2, 13.633, 0.004, 2, 13.65, 0.004, 2, 13.667, 0.004, 2, 13.683, 0.004, 2, 13.7, 0.004, 2, 13.717, 0.004, 2, 13.733, 0.004, 2, 13.75, 0.004, 2, 13.767, 0.004, 0, 13.783, 0.005, 2, 13.8, 0.005, 2, 13.817, 0.005, 2, 13.833, 0.005, 2, 13.85, 0.005, 2, 13.867, 0.005, 2, 13.9, 0.005, 2, 13.917, 0.005, 2, 13.933, 0.005, 2, 13.95, 0.005, 2, 13.967, 0.005, 2, 13.983, 0.005, 2, 14, 0.005, 2, 14.017, 0.005, 2, 14.033, 0.005, 2, 14.05, 0.005, 2, 14.067, 0.005, 2, 14.083, 0.005, 0, 14.1, 0.006, 2, 14.117, 0.006, 2, 14.133, 0.006, 2, 14.15, 0.006, 2, 14.167, 0.006, 2, 14.183, 0.006, 2, 14.217, 0.006, 2, 14.25, 0.006, 2, 14.267, 0.006, 2, 14.283, 0.006, 2, 14.317, 0.006, 2, 14.333, 0.006, 2, 14.35, 0.006, 2, 14.367, 0.006, 2, 14.383, 0.006, 2, 14.4, 0.006, 0, 14.417, 0.007, 2, 14.433, 0.007, 2, 14.45, 0.007, 2, 14.467, 0.007, 2, 14.5, 0.007, 2, 14.533, 0.007, 2, 14.567, 0.007, 2, 14.583, 0.007, 2, 14.6, 0.007, 2, 14.617, 0.007, 2, 14.65, 0.007, 2, 14.667, 0.007, 2, 14.683, 0.007, 2, 14.7, 0.007, 0, 14.717, 0.008, 2, 14.733, 0.008, 2, 14.75, 0.008, 2, 14.767, 0.008, 2, 14.783, 0.008, 2, 14.8, 0.008, 2, 14.833, 0.008, 2, 14.85, 0.008, 2, 14.867, 0.008, 2, 14.883, 0.008, 2, 14.9, 0.008, 2, 14.917, 0.008, 2, 14.933, 0.008, 2, 14.95, 0.008, 0, 14.983, 0.009, 2, 15, 0.009, 2, 15.017, 0.009, 2, 15.033, 0.009, 2, 15.067, 0.009, 2, 15.083, 0.009, 2, 15.117, 0.009, 2, 15.133, 0.009, 2, 15.15, 0.009, 2, 15.167, 0.009, 2, 15.183, 0.009, 2, 15.2, 0.009, 2, 15.217, 0.009, 2, 15.233, 0.009, 2, 15.267, 0.009, 2, 15.283, 0.009, 0, 15.3, 0.01, 2, 15.317, 0.01, 2, 15.35, 0.01, 2, 15.367, 0.01, 2, 15.4, 0.01, 2, 15.417, 0.01, 2, 15.433, 0.01, 2, 15.45, 0.01, 2, 15.467, 0.01, 2, 15.483, 0.01, 2, 15.5, 0.01, 2, 15.517, 0.01, 0, 15.533, 0.011, 2, 15.55, 0.011, 0, 15.683, 1, 2, 15.783, 1, 0, 16.05, -0.838, 0, 16.483, 0.264, 0, 16.8, -0.073, 0, 17.1, 0.021, 0, 17.417, -0.006, 0, 17.717, 0.002, 2, 17.75, 0.002, 0, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamEyeLGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.6, 0, 0, 0.667, -0.305, 0, 0.717, -0.2, 0, 0.817, -1, 2, 0.833, -1, 0, 0.85, 1, 2, 0.883, 1, 0, 0.917, 0.831, 2, 0.933, 0.831, 0, 0.967, 0.863, 0, 1.217, -0.314, 0, 1.483, 0.153, 0, 1.767, -0.069, 0, 2.067, 0.028, 0, 2.367, -0.01, 0, 2.65, 0.004, 0, 2.717, -0.304, 0, 2.767, -0.2, 0, 2.867, -1, 2, 2.883, -1, 0, 2.9, 1, 2, 2.933, 1, 0, 2.967, 0.824, 0, 3.017, 0.86, 0, 3.267, -0.315, 0, 3.533, 0.154, 0, 3.817, -0.07, 0, 4.117, 0.028, 0, 4.45, -0.31, 0, 4.5, -0.199, 0, 4.6, -1, 2, 4.617, -1, 0, 4.633, 1, 2, 4.667, 1, 0, 4.717, 0.846, 0, 4.733, 0.868, 0, 5, -0.31, 0, 5.267, 0.149, 0, 5.55, -0.066, 0, 5.85, 0.026, 0, 6.15, -0.01, 0, 6.45, 0.003, 2, 6.467, 0.003, 0, 6.733, -0.001, 2, 6.783, -0.001, 0, 7, 0, 2, 7.017, 0, 2, 7.033, 0, 2, 7.083, 0, 2, 7.1, 0, 2, 7.15, 0, 2, 7.167, 0, 2, 7.183, 0, 2, 7.2, 0, 2, 7.217, 0, 2, 7.233, 0, 2, 7.267, 0, 2, 7.283, 0, 2, 7.483, 0, 2, 7.5, 0, 2, 10.85, 0, 0, 10.917, -0.305, 0, 10.967, -0.2, 0, 11.067, -1, 2, 11.083, -1, 0, 11.1, 1, 2, 11.133, 1, 0, 11.167, 0.831, 2, 11.183, 0.831, 0, 11.217, 0.863, 0, 11.467, -0.299, 0, 11.733, 0.141, 0, 12.017, -0.075, 0, 12.35, 0.037, 0, 12.667, -0.017, 0, 12.967, 0.006, 2, 12.983, 0.006, 0, 13.283, -0.003, 2, 13.3, -0.003, 0, 13.533, 0, 2, 13.55, 0, 2, 13.567, 0, 2, 13.633, 0, 2, 13.667, 0, 2, 13.683, 0, 2, 13.767, 0, 2, 13.783, 0, 2, 13.817, 0, 2, 13.833, 0, 2, 13.85, 0, 2, 13.95, 0, 2, 13.967, 0, 2, 14.033, 0, 2, 14.05, 0, 2, 14.083, 0, 2, 14.1, 0, 2, 14.383, 0, 2, 14.4, 0, 2, 14.417, 0, 2, 14.433, 0, 2, 14.45, 0, 2, 14.65, 0, 2, 14.667, 0, 2, 14.683, 0, 2, 14.7, 0, 2, 14.717, 0, 2, 14.733, 0, 2, 14.75, 0, 2, 14.767, 0, 2, 14.783, 0, 2, 14.8, 0, 2, 14.817, 0, 2, 14.833, 0, 2, 14.883, 0, 2, 14.9, 0, 2, 14.917, 0, 2, 14.933, 0, 2, 14.95, 0, 2, 14.967, 0, 2, 14.983, 0, 2, 15, 0, 2, 15.017, 0, 2, 15.033, 0, 2, 15.05, 0, 2, 15.067, 0, 2, 15.217, 0, 2, 15.233, 0, 2, 15.25, 0, 2, 15.267, 0, 2, 15.283, 0, 2, 15.3, 0, 2, 15.317, 0, 2, 15.333, 0, 2, 15.35, 0, 2, 15.55, 0, 0, 15.667, -0.263, 0, 15.883, 0.662, 0, 16.2, -0.356, 0, 16.617, 0.156, 0, 16.933, -0.078, 0, 17.25, 0.032, 0, 17.55, -0.012, 0, 17.867, 0.004, 0, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamEyeRGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.6, 0, 0, 0.667, -0.305, 0, 0.717, -0.2, 0, 0.817, -1, 2, 0.833, -1, 0, 0.85, 1, 2, 0.883, 1, 0, 0.917, 0.831, 2, 0.933, 0.831, 0, 0.967, 0.863, 0, 1.217, -0.314, 0, 1.483, 0.153, 0, 1.767, -0.069, 0, 2.067, 0.028, 0, 2.367, -0.01, 0, 2.65, 0.004, 0, 2.717, -0.304, 0, 2.767, -0.2, 0, 2.867, -1, 2, 2.883, -1, 0, 2.9, 1, 2, 2.933, 1, 0, 2.967, 0.824, 0, 3.017, 0.86, 0, 3.267, -0.315, 0, 3.533, 0.154, 0, 3.817, -0.07, 0, 4.117, 0.028, 0, 4.45, -0.31, 0, 4.5, -0.199, 0, 4.6, -1, 2, 4.617, -1, 0, 4.633, 1, 2, 4.667, 1, 0, 4.717, 0.846, 0, 4.733, 0.868, 0, 5, -0.31, 0, 5.267, 0.149, 0, 5.55, -0.066, 0, 5.85, 0.026, 0, 6.15, -0.01, 0, 6.45, 0.003, 2, 6.467, 0.003, 0, 6.733, -0.001, 2, 6.783, -0.001, 0, 7, 0, 2, 7.017, 0, 2, 7.033, 0, 2, 7.083, 0, 2, 7.1, 0, 2, 7.15, 0, 2, 7.167, 0, 2, 7.183, 0, 2, 7.2, 0, 2, 7.217, 0, 2, 7.233, 0, 2, 7.267, 0, 2, 7.283, 0, 2, 7.483, 0, 2, 7.5, 0, 2, 10.85, 0, 0, 10.917, -0.305, 0, 10.967, -0.2, 0, 11.067, -1, 2, 11.083, -1, 0, 11.1, 1, 2, 11.133, 1, 0, 11.167, 0.831, 2, 11.183, 0.831, 0, 11.217, 0.863, 0, 11.467, -0.309, 0, 11.733, 0.149, 0, 12.017, -0.071, 0, 12.333, 0.03, 0, 12.633, -0.012, 0, 12.933, 0.004, 2, 12.95, 0.004, 0, 13.233, -0.002, 2, 13.283, -0.002, 0, 13.5, 0, 2, 13.517, 0, 2, 13.533, 0, 2, 13.6, 0, 2, 13.617, 0, 2, 13.65, 0, 2, 13.667, 0, 2, 13.683, 0, 2, 13.717, 0, 2, 13.733, 0, 2, 13.75, 0, 2, 13.783, 0, 2, 13.8, 0, 2, 13.983, 0, 2, 14, 0, 2, 14.1, 0, 2, 14.117, 0, 2, 14.25, 0, 2, 14.267, 0, 2, 15.55, 0, 0, 15.667, -0.269, 0, 15.883, 0.675, 0, 16.2, -0.358, 0, 16.617, 0.158, 0, 16.933, -0.079, 0, 17.25, 0.032, 0, 17.55, -0.012, 0, 17.867, 0.004, 0, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 5, 0, 0, 5.033, -5, 2, 10.6, -5, 0, 10.617, 0, 2, 10.683, 0, 2, 12.167, 0, 2, 15.45, 0, 2, 15.867, 0, 2, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "jing<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 14.933, 0, 0, 15, 1, 2, 15.017, 1, 2, 15.05, 1, 2, 15.067, 1, 2, 15.083, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 14.967, -12.9, 1, 14.972, -12.9, 14.978, -8.121, 14.983, -8.102, 1, 15.016, -7.985, 15.05, -8.008, 15.083, -7.8, 1, 15.122, -7.557, 15.161, 0, 15.2, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 14.967, -7.98, 0, 14.983, 23.466, 1, 15.016, 23.466, 15.05, 23.756, 15.083, 21.78, 1, 15.122, 19.474, 15.161, 0, 15.2, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 14.967, -15.72, 1, 14.972, -15.72, 14.978, -6.217, 14.983, -5.88, 1, 15.016, -3.856, 15.05, -0.924, 15.083, -0.48, 1, 15.122, 0.038, 15.161, 0, 15.2, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 5.156, 0, 10.061, -4.267, 14.967, -13, 1, 15.006, -13.069, 15.044, -17, 15.083, -17, 0, 15.2, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamForearmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.167, 0, 0, 1.717, 1, 2, 1.867, 1, 0, 4.333, 0, 2, 5.6, 0, 2, 10.683, 0, 2, 12.167, 0, 2, 13.917, 0, 2, 15.45, 0, 2, 15.867, 0, 2, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 0.278, 0, 0.305, -0.138, 0.333, 0.037, 1, 0.533, 1.294, 0.733, 20.848, 0.933, 27.603, 1, 1.011, 30, 1.089, 30, 1.167, 30, 2, 4.333, 30, 0, 5.6, 0.6, 2, 10.683, 0.6, 0, 12.167, -2.8, 2, 13.917, -2.8, 2, 15.45, -2.8, 2, 15.867, -2.8, 0, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 30, 2, 0.25, 30, 1, 0.367, 30, 0.483, 30.438, 0.6, 29.04, 1, 0.789, 26.777, 0.978, 14.8, 1.167, 14.8, 2, 4.333, 14.8, 0, 5.6, -4.92, 2, 10.683, -4.92, 1, 10.739, -4.92, 10.794, -4.785, 10.85, -5.466, 1, 11.017, -7.51, 11.183, -12.429, 11.35, -14.152, 1, 11.622, -16.966, 11.895, -17.54, 12.167, -17.54, 2, 13.917, -17.54, 2, 15.45, -17.54, 2, 15.867, -17.54, 1, 15.872, -17.54, 15.878, 30, 15.883, 30, 2, 17.917, 30, 2, 18.167, 30]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.167, 0, 2, 4.333, 0, 0, 5.6, -30, 2, 10.683, -30, 0, 12.167, 0, 2, 13.917, 0, 2, 15.45, 0, 2, 15.867, 0, 2, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1, 2, 0.25, -1, 0, 1.167, 1, 2, 4.333, 1, 2, 5.6, 1, 2, 10.683, 1, 2, 12.167, 1, 2, 13.917, 1, 2, 15.45, 1, 2, 15.867, 1, 0, 15.883, -1, 2, 17.917, -1, 2, 18.167, -1]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.167, 0, 2, 4.333, 0, 0, 5.6, -0.14, 2, 10.6, -0.14, 0, 11.267, -0.33, 0, 12.167, 0.26, 2, 13.917, 0.26, 2, 15.45, 0.26, 2, 15.867, 0.26, 0, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamHandLDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10, 2, 0.25, 10, 2, 0.833, 10, 2, 0.85, 7, 2, 4.7, 7, 2, 4.717, 0, 2, 5.6, 0, 2, 10.683, 0, 2, 11.25, 0, 2, 11.267, 6, 2, 12.167, 6, 2, 13.917, 6, 2, 15.45, 6, 2, 15.867, 6, 0, 15.883, 10, 2, 17.917, 10, 2, 18.167, 10]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 10.683, 0, 0, 11.267, -1, 0, 12.167, 0, 2, 13.917, 0, 2, 15.45, 0, 2, 15.867, 0, 2, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.167, 1.14, 2, 4.333, 1.14, 0, 5.6, 0, 2, 10.683, 0, 2, 12.167, 0, 2, 13.917, 0, 2, 15.45, 0, 2, 15.867, 0, 2, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.167, -8.64, 2, 4.333, -8.64, 0, 5.6, 0, 2, 10.683, 0, 2, 12.167, 0, 2, 13.917, 0, 2, 15.45, 0, 2, 15.867, 0, 2, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 4.417, 0, 0, 4.583, 1, 2, 4.717, 1, 2, 4.983, 1, 2, 13.917, 1, 2, 15.867, 1, 0, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 4.033, 0, 2, 4.483, 0, 0, 4.583, 1, 0, 4.667, 0, 2, 4.983, 0, 2, 5.6, 0, 2, 10.683, 0, 2, 12.167, 0, 2, 13.917, 0, 2, 15.45, 0, 2, 15.867, 0, 2, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 4.717, 0, 0, 4.967, -4.7, 0, 5.6, 20.256, 1, 7.294, 20.256, 8.989, 19.216, 10.683, 14, 1, 11.178, 12.478, 11.672, -2.76, 12.167, -2.76, 2, 13.917, -2.76, 2, 15.45, -2.76, 2, 15.867, -2.76, 0, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 4.667, 0, 0, 7.033, 0.616, 2, 9.867, 0.616, 0, 11.35, 1, 1, 12.206, 1, 13.061, 0.918, 13.917, 0.661, 1, 15.25, 0.26, 16.584, 0, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 4.717, 0, 0, 5.117, -2.884, 0, 5.6, 1.1, 2, 10.683, 1.1, 0, 12.167, -5.5, 2, 13.917, -5.5, 2, 15.45, -5.5, 2, 15.867, -5.5, 1, 15.872, -5.5, 15.878, 0, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 4.717, 0, 2, 5.15, 0, 2, 5.6, 0, 2, 10.683, 0, 0, 12.167, 7.4, 2, 13.917, 7.4, 2, 15.45, 7.4, 2, 15.867, 7.4, 0, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 4.717, 0, 2, 4.967, 0, 1, 5.028, 0.11, 5.089, 0.22, 5.15, 0.33, 0, 5.6, -0.03, 2, 10.683, -0.03, 0, 11.133, 0.226, 0, 12.167, 0.13, 2, 13.917, 0.13, 2, 15.45, 0.13, 2, 15.867, 0.13, 0, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamHandRDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 11.4, 0, 2, 11.417, 2, 2, 12.167, 2, 2, 13.917, 2, 2, 15.45, 2, 2, 15.867, 2, 0, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 4.717, 0, 2, 5.6, 0, 2, 11.133, 0, 2, 11.15, 1, 2, 12.167, 1, 2, 13.917, 1, 2, 15.45, 1, 2, 15.867, 1, 0, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 4.717, 0, 2, 4.867, 0, 2, 5.117, 0, 0, 5.133, 1, 2, 5.6, 1, 2, 10.683, 1, 0, 12.167, 0, 2, 13.917, 0, 2, 15.45, 0, 2, 15.867, 0, 2, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 4.717, 0, 2, 5.6, 0, 2, 11.133, 0, 2, 11.15, 1, 2, 12.167, 1, 2, 13.917, 1, 2, 15.45, 1, 2, 15.867, 1, 0, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 4.717, 0, 0, 5.6, -0.1, 2, 10.683, -0.1, 0, 11.15, -0.5, 1, 11.489, -0.5, 11.828, 0.168, 12.167, 0.184, 1, 12.75, 0.211, 13.334, 0.229, 13.917, 0.236, 1, 14.178, 0.239, 14.439, 0.238, 14.7, 0.238, 2, 15.867, 0.238, 0, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 4.717, 0, 0, 5.6, -0.3, 2, 10.683, -0.3, 0, 11.15, -0.034, 1, 11.489, -0.034, 11.828, -0.307, 12.167, -0.31, 1, 12.75, -0.316, 13.334, -0.318, 13.917, -0.321, 1, 14.178, -0.322, 14.439, -0.322, 14.7, -0.322, 2, 15.867, -0.322, 0, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 4.717, 0, 0, 5.6, 0.6, 2, 10.683, 0.6, 1, 10.839, 0.6, 10.994, 0.612, 11.15, 0.46, 1, 11.489, 0.129, 11.828, -0.928, 12.167, -0.932, 1, 12.75, -0.939, 13.334, -0.941, 13.917, -0.946, 1, 14.178, -0.948, 14.439, -0.949, 14.7, -0.952, 1, 15.089, -0.956, 15.478, -0.962, 15.867, -0.962, 0, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 11.083, 0, 0, 11.917, 0.57, 2, 13.917, 0.57, 2, 14.833, 0.57, 0, 15.45, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 4.717, 0, 2, 5.6, 0, 2, 10.683, 0, 0, 12.167, 1, 2, 13.917, 1, 2, 15.45, 1, 2, 15.867, 1, 0, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 4.717, 0, 2, 5.6, 0, 2, 10.683, 0, 2, 12.167, 0, 2, 12.6, 0, 0, 12.85, -0.479, 0, 13.117, -0.002, 0, 13.433, -0.555, 0, 13.867, -0.028, 1, 13.884, -0.028, 13.9, -0.001, 13.917, -0.062, 1, 14.05, -0.552, 14.184, -1, 14.317, -1, 1, 14.411, -1, 14.506, -0.989, 14.6, -0.9, 1, 14.633, -0.868, 14.667, 0.656, 14.7, 0.656, 0, 14.733, 0.586, 0, 14.817, 1, 1, 15.167, 1, 15.517, 0.861, 15.867, 0.57, 1, 15.872, 0.565, 15.878, 0, 15.883, 0, 2, 17.133, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 9.9, 0, 0, 12.6, 1, 2, 13.917, 1, 2, 15.867, 1, 0, 15.9, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 4.717, 0, 2, 5.6, 0, 2, 10.683, 0, 2, 12.167, 0, 2, 13.917, 0, 2, 14.683, 0, 1, 14.783, 0, 14.883, 0.462, 14.983, 0.52, 1, 15.611, 0.882, 16.239, 0.999, 16.867, 0.999, 0, 16.883, 0, 2, 17.133, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Param103", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 4.717, 0, 2, 5.6, 0, 2, 10.683, 0, 0, 11.267, -30, 1, 12.15, -30, 13.034, -29.44, 13.917, -28.024, 1, 14.272, -27.454, 14.628, -26.822, 14.983, -25.92, 1, 15.089, -25.652, 15.194, 6.558, 15.3, 17.477, 1, 15.4, 27.821, 15.5, 30, 15.6, 30, 2, 15.867, 30, 1, 15.872, 30, 15.878, 0, 15.883, 0, 2, 17.133, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 4.717, 0, 2, 5.6, 0, 2, 10.683, 0, 2, 12.167, 0, 2, 13.917, 0, 2, 14.967, 0, 1, 14.972, 0, 14.978, 0, 14.983, 0.007, 1, 15.2, 0.628, 15.416, 1, 15.633, 1, 2, 15.867, 1, 2, 15.883, 0, 2, 17.133, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 13.917, 0, 2, 15.333, 0, 1, 15.355, 0, 15.378, -0.019, 15.4, 0.1, 1, 15.422, 0.219, 15.445, 1, 15.467, 1, 0, 15.633, -0.7, 0, 15.8, 0.648, 1, 15.822, 0.648, 15.845, 0.326, 15.867, 0.161, 1, 15.9, -0.087, 15.934, -0.119, 15.967, -0.119, 0, 16.05, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 13.917, 0, 2, 15.333, 0, 0, 15.4, -1, 0, 15.55, 1, 0, 15.717, -0.7, 0, 15.867, 0.609, 0, 16.05, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 10.25, 0, 0, 12.167, -21.5, 2, 13.917, -21.5, 2, 15.45, -21.5, 2, 15.867, -21.5, 1, 15.872, -21.5, 15.878, 0, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -2.728, 0, 0.617, 5.594, 0, 1.133, -8, 2, 1.8, -8, 0, 2.433, -6, 0, 2.817, -14, 0, 3.25, 13.86, 0, 3.65, -10.031, 1, 3.833, -10.031, 4.017, -9.461, 4.2, -5, 1, 4.333, -1.756, 4.467, 4.282, 4.6, 4.282, 0, 4.85, -14.746, 0, 5.05, 7, 0, 5.3, -15.62, 0, 5.767, 0, 2, 10.25, 0, 0, 12.167, 6.9, 2, 12.583, 6.9, 0, 12.933, 3.313, 0, 13.167, 7.399, 0, 13.4, 3.329, 0, 13.867, 7.761, 1, 13.884, 7.761, 13.9, 7.895, 13.917, 7.608, 1, 14.056, 5.217, 14.194, 2.993, 14.333, 2.993, 0, 15, 20.146, 0, 15.45, 6.9, 2, 15.867, 6.9, 0, 15.883, -21.262, 0, 17.267, 6, 0, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 6.25, 0, 2, 10.25, 0, 0, 12.167, 12, 0, 12.983, 0.9, 1, 13.294, 0.9, 13.606, 5.982, 13.917, 11.447, 1, 13.956, 12.13, 13.994, 11.821, 14.033, 11.821, 0, 14.767, 3.65, 0, 15.45, 12, 2, 15.867, 12, 1, 15.872, 12, 15.878, 0, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.533, -10, 1, 0.716, -10, 0.9, 6.373, 1.083, 7.359, 1, 1.344, 8.764, 1.606, 8.654, 1.867, 8.654, 1, 2.1, 8.654, 2.334, 8.655, 2.567, 7, 1, 2.722, 5.896, 2.878, 0, 3.033, 0, 0, 3.967, 10, 0, 4.7, 0, 2, 5.1, 0, 0, 6.467, -0.96, 0, 8.8, 0, 2, 10.25, 0, 0, 12.167, -10, 0, 13.033, -8.9, 1, 13.328, -8.9, 13.622, -9.382, 13.917, -9.926, 1, 13.972, -10, 14.028, -10, 14.083, -10, 0, 14.933, -9.02, 0, 15.867, -10, 0, 16.883, 1.934, 0, 17.5, -2.398, 0, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -7.795, 2, 0.25, -7.795, 1, 0.75, -7.795, 1.25, -5.197, 1.75, 0, 1, 2.25, 5.197, 2.75, 7.795, 3.25, 7.795, 1, 3.75, 7.795, 4.25, 5.197, 4.75, 0, 1, 5.25, -5.197, 5.75, -7.795, 6.25, -7.795, 1, 6.75, -7.795, 7.25, -5.197, 7.75, 0, 1, 8.25, 5.197, 8.75, 7.795, 9.25, 7.795, 0, 10.25, -7.795, 0, 12.167, 0, 2, 13.917, 0, 2, 15.45, 0, 2, 15.867, 0, 1, 15.872, 0, 15.878, -7.714, 15.883, -7.795, 1, 15.989, -9.333, 16.094, -10, 16.2, -10, 0, 17.133, 4.681, 0, 17.917, -7.795, 2, 18.167, -7.795]}, {"Target": "Parameter", "Id": "ParamBodyStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 0.294, 0, 0.339, 4.872, 0.383, 6.338, 1, 0.478, 9.454, 0.572, 10, 0.667, 10, 0, 1.083, -3, 0, 1.217, 1.044, 1, 1.284, 1.044, 1.35, 0.677, 1.417, 0, 1, 1.545, -1.298, 1.672, -1.92, 1.8, -1.92, 0, 2.4, 2.409, 0, 2.683, 0, 0, 3.033, 10, 1, 3.161, 10, 3.289, 10, 3.417, 7.281, 1, 3.578, 3.273, 3.739, -10, 3.9, -10, 0, 4.233, -3, 0, 4.5, -7, 2, 4.65, -7, 0, 4.867, -9, 0, 5.033, -4.543, 1, 5.094, -4.543, 5.156, -5.258, 5.217, -6, 1, 5.284, -6.81, 5.35, -7, 5.417, -7, 1, 5.472, -7, 5.528, -2.742, 5.583, -2, 1, 5.728, -0.072, 5.872, 0.25, 6.017, 0.25, 0, 6.633, -5.184, 0, 7.05, 2.395, 0, 7.183, 1.889, 0, 7.35, 2.493, 0, 7.683, 0.094, 0, 7.833, 0.616, 0, 8.217, -4, 0, 8.95, 0, 2, 9.117, 0, 0, 9.55, 10, 0, 9.9, -2, 0, 10.25, 0, 2, 11.5, 0, 0, 12.517, 2, 0, 12.967, -2.837, 0, 13.267, 0.769, 0, 13.533, -3.285, 0, 13.867, 0.94, 1, 13.884, 0.94, 13.9, 1.083, 13.917, 0.81, 1, 14.078, -1.826, 14.239, -4.306, 14.4, -4.306, 0, 15.05, 2, 0, 16.133, -10, 0, 16.917, 2, 0, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.517, 4.859, 0, 0.883, -1.073, 0, 1.483, 2.445, 0, 2.017, -0.586, 0, 2.533, 0.603, 0, 4.967, -7, 0, 6.467, 6, 0, 8, -4, 0, 9.583, 10, 0, 10.867, 0, 0, 13.1, 0.4, 1, 13.372, 0.4, 13.645, 0.391, 13.917, 0.316, 1, 14.567, 0.136, 15.217, 0, 15.867, 0, 0, 16.933, 2.964, 0, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamLegLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 2.75, 14.515, 0, 4, -9.742, 1, 4.406, -9.742, 4.811, -5.169, 5.217, 0, 1, 6.228, 12.886, 7.239, 17.873, 8.25, 17.873, 0, 10.25, 0, 0, 11.267, 30, 0, 12.167, 0, 2, 15.45, 0, 2, 15.867, 0, 2, 15.883, 0, 0, 17, 17.67, 0, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamLegRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.55, 7.732, 0, 4.05, -9.756, 1, 4.439, -9.756, 4.828, -5.355, 5.217, 0, 1, 5.65, 5.967, 6.084, 7.732, 6.517, 7.732, 0, 9.033, -9.756, 0, 10.25, 0, 2, 12.167, 0, 2, 15.45, 0, 2, 15.867, 0, 2, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 3.333, 0, 2, 4.417, 0, 0, 4.833, 1, 2, 10.683, 1, 2, 12.167, 1, 2, 15.45, 1, 2, 15.867, 1, 0, 15.883, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Physics_AngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 10.733, 0, 0, 12.15, -11.812, 0, 12.483, -11.608, 2, 12.5, -11.608, 0, 12.833, -11.636, 0, 13.117, -11.632, 2, 13.133, -11.632, 2, 13.15, -11.632, 2, 13.2, -11.632, 2, 13.217, -11.632, 2, 13.25, -11.632, 2, 13.267, -11.632, 2, 13.283, -11.632, 2, 13.3, -11.632, 2, 13.317, -11.632, 0, 13.333, -11.633, 2, 13.367, -11.633, 2, 13.383, -11.633, 2, 13.45, -11.633, 2, 13.467, -11.633, 2, 13.617, -11.633, 2, 13.633, -11.633, 2, 15.867, -11.633, 0, 16.067, 3.829, 0, 16.417, -0.691, 0, 16.733, 0.272, 0, 16.9, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Physics_AngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.317, 0, 0, 0.433, -0.923, 0, 0.683, 3.649, 0, 1.183, -5.348, 0, 1.567, -4.571, 0, 1.817, -4.604, 0, 2.433, -3.381, 0, 2.85, -8.518, 0, 3.283, 9.644, 0, 3.717, -7.497, 0, 4.617, 2.944, 0, 4.883, -9.334, 0, 5.117, 4.129, 0, 5.383, -10.202, 0, 5.817, 0.955, 0, 6.167, -0.243, 0, 6.333, 0, 2, 11.35, 0, 0, 12.183, 4.01, 0, 12.567, 3.972, 2, 12.583, 3.972, 0, 12.967, 1.705, 0, 13.217, 4.398, 0, 13.483, 1.728, 0, 13.9, 4.735, 0, 14.367, 1.452, 0, 15.017, 12.236, 0, 15.5, 3.13, 0, 15.867, 4.014, 0, 16.083, -0.521, 0, 16.4, 0.241, 0, 16.567, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Physics_AngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 10.25, 0, 0, 10.8, 3.297, 0, 11.133, 0.964, 0, 11.4, 1.208, 0, 12.3, -0.098, 0, 12.633, 0.011, 2, 12.65, 0.011, 0, 12.967, -0.001, 2, 13.017, -0.001, 0, 13.2, 0, 2, 13.217, 0, 2, 13.25, 0, 2, 13.3, 0, 2, 13.317, 0, 2, 13.367, 0, 2, 13.383, 0, 2, 13.483, 0, 2, 13.5, 0, 2, 15.867, 0, 0, 15.883, -16.399, 0, 16.2, 1.89, 0, 16.533, -0.472, 0, 16.733, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.367, 2.717, 0, 0.6, -4.428, 0, 0.95, 5.626, 0, 1.333, -5.004, 0, 1.75, 3.184, 0, 2.167, -2.349, 0, 2.65, 3.723, 0, 3.067, -10.823, 0, 3.467, 17.5, 0, 3.867, -15.418, 0, 4.283, 8.247, 0, 4.617, -3.255, 0, 4.817, 5.763, 0, 5.033, -13.224, 0, 5.283, 13.195, 0, 5.65, -11.215, 0, 6.05, 8.262, 0, 6.467, -5.259, 0, 6.883, 3.348, 0, 7.283, -2.818, 0, 7.7, 1.883, 0, 8.117, -1.258, 0, 8.517, 1.657, 0, 8.933, -1.107, 0, 9.333, 1.459, 0, 9.75, -0.975, 0, 10.15, 1.284, 0, 10.583, -1.102, 0, 10.933, 1.101, 0, 11.35, -0.835, 0, 11.783, 0.506, 0, 12.167, -0.193, 0, 12.8, 3.128, 0, 13.1, -7.112, 0, 13.4, 7.269, 0, 13.767, -6.966, 0, 14.133, 9.282, 0, 14.55, -10.262, 0, 14.6, -9.851, 0, 14.683, -15.135, 0, 15.067, 14.822, 0, 15.517, -10.438, 0, 15.883, 14.625, 0, 16.3, -9.3, 0, 16.717, 5.385, 0, 17.133, -3.601, 0, 17.55, 2.406, 0, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.533, 7.415, 0, 0.817, -10.314, 0, 1.167, 14.474, 0, 1.55, -10.881, 0, 1.95, 6.984, 0, 2.367, -5.333, 0, 2.9, 10.624, 0, 3.3, -29.754, 0, 3.6, 30, 2, 3.783, 30, 0, 4.033, -30, 2, 4.117, -30, 0, 4.467, 16.267, 0, 4.767, -8.961, 0, 4.983, 19.064, 0, 5.233, -29.317, 0, 5.517, 30, 0, 5.85, -28.468, 0, 6.25, 18.578, 0, 6.65, -11.327, 0, 7.133, 7.603, 0, 7.517, -6.11, 0, 7.933, 4.25, 0, 8.417, -4.416, 0, 8.75, 3.806, 0, 9.25, -3.936, 0, 9.583, 3.417, 0, 9.917, -2.234, 0, 9.95, -2.201, 0, 10.017, -3.139, 0, 10.383, 2.662, 0, 10.767, -4.017, 0, 11.133, 2.007, 0, 11.55, -2.103, 0, 11.983, 0.801, 0, 12.367, -0.471, 0, 12.6, 0, 0, 12.733, -1.008, 0, 13.017, 11.645, 0, 13.3, -17.556, 0, 13.617, 18.066, 0, 13.983, -20.798, 0, 14.35, 24.217, 0, 14.8, -30, 2, 14.933, -30, 0, 15.25, 30, 2, 15.317, 30, 0, 15.717, -21.143, 0, 15.867, -10.033, 0, 15.883, -15.487, 0, 16.133, 29.296, 0, 16.517, -20.59, 0, 16.917, 11.653, 0, 17.35, -7.592, 0, 17.767, 5.125, 0, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_L", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.267, 0.544, 0, 0.3, 0.432, 0, 0.35, 0.715, 0, 0.367, 0.692, 0, 0.517, 1.544, 0, 0.783, -3.298, 0, 1.167, 3.998, 0, 1.6, -2.482, 0, 2.033, 1.409, 0, 2.55, -1.338, 0, 2.95, 3.24, 0, 3.333, -7.035, 0, 3.733, 9.6, 0, 4.15, -6.099, 0, 4.6, 2.048, 0, 4.767, 0.178, 0, 4.967, 6.537, 0, 5.233, -9.347, 0, 5.517, 6.329, 0, 5.9, -4.475, 0, 6.35, 2.403, 0, 6.917, -1.463, 0, 7.317, 1.242, 0, 7.8, -0.918, 0, 8.217, 0.786, 0, 8.7, -0.583, 0, 9.1, 0.861, 0, 9.55, -0.689, 0, 10, 0.553, 0, 10.45, -0.848, 0, 10.833, 1.12, 0, 11.25, -0.436, 0, 11.717, 0.472, 0, 12.2, -0.193, 0, 12.6, 0.101, 0, 12.75, -0.061, 0, 13.05, 1.07, 0, 13.333, -1.819, 0, 13.617, 1.442, 0, 13.983, -1.319, 0, 14.417, 1.648, 0, 14.917, -3.121, 0, 15.45, 3.896, 0, 15.867, -2.887, 0, 15.883, -1.203, 0, 16, -1.981, 0, 16.467, 2.216, 0, 16.917, -1.818, 0, 17.367, 1.428, 0, 17.817, -1.166, 0, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_R", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.367, 0.651, 0, 0.533, -0.78, 0, 0.8, 2.209, 0, 1.167, -3.371, 0, 1.583, 2.206, 0, 2.033, -1.372, 0, 2.533, 1.21, 0, 2.95, -3.17, 0, 3.333, 7.263, 0, 3.717, -9.488, 0, 4.15, 6.019, 0, 4.6, -2.284, 0, 4.767, -0.208, 0, 4.983, -6.244, 0, 5.233, 9.249, 0, 5.517, -6.29, 0, 5.9, 4.473, 0, 6.35, -2.404, 0, 6.917, 1.463, 0, 7.317, -1.242, 0, 7.8, 0.919, 0, 8.217, -0.786, 0, 8.7, 0.583, 0, 9.1, -0.862, 0, 9.55, 0.689, 0, 10, -0.553, 0, 10.433, 0.609, 0, 10.783, -0.079, 0, 11.267, 0.646, 0, 11.783, 0.184, 0, 12.033, 0.244, 0, 12.5, -0.204, 0, 12.767, 0.189, 0, 13.067, -1.046, 0, 13.333, 1.732, 0, 13.617, -1.407, 0, 13.983, 1.321, 0, 14.417, -1.654, 0, 14.883, 2.862, 0, 15.45, -3.912, 0, 15.883, 7.487, 0, 16.167, -7.024, 0, 16.583, 3.647, 0, 17.05, -2.072, 0, 17.517, 1.274, 0, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 3.637, 0, 0.617, -7.459, 0, 1.133, 10.668, 2, 1.8, 10.668, 0, 2.433, 8, 0, 2.733, 16.967, 0, 3.083, -18.079, 0, 3.483, 18.075, 0, 3.833, -7.279, 0, 4.15, -0.592, 0, 4.417, -3.495, 0, 4.45, -3.392, 0, 4.467, -3.466, 0, 4.8, 15.976, 0, 5.033, -23.174, 0, 5.267, 21.429, 0, 5.583, -13.198, 0, 5.933, 4.508, 0, 6.283, -2.016, 0, 6.483, 0, 2, 10.25, 0, 0, 10.833, 21.113, 0, 11.15, 10.125, 0, 11.6, 17.069, 0, 13.1, -3.499, 0, 13.4, 10.45, 0, 13.617, 8.006, 0, 14.067, 17.509, 0, 14.683, -2.473, 0, 15.3, 21.345, 0, 15.633, 11.282, 0, 15.867, 13.945, 1, 15.872, 13.945, 15.878, -30, 15.883, -30, 2, 16.033, -30, 0, 16.25, 21.881, 0, 16.6, -5.384, 0, 16.95, 2.898, 0, 17.283, -2.14, 0, 17.483, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -3.637, 0, 0.617, 7.459, 0, 1.133, -10.668, 2, 1.8, -10.668, 0, 2.433, -8, 0, 2.717, -16.874, 0, 2.933, 22.048, 0, 3.2, -12.494, 0, 3.65, 9.889, 0, 3.917, -6.809, 0, 4.2, 2.921, 0, 4.45, -1.168, 0, 4.567, 0.493, 0, 4.733, -7.68, 0, 4.967, 22.158, 0, 5.167, -30, 2, 5.183, -30, 0, 5.417, 26.536, 0, 5.683, -13.991, 0, 5.983, 6.11, 0, 6.133, 1.219, 0, 6.2, 1.635, 0, 6.417, -1.603, 0, 6.467, -1.404, 0, 6.483, -1.98, 0, 6.617, 3.508, 0, 6.75, 0, 2, 10.25, 0, 0, 10.783, -19.968, 0, 11, 13.382, 0, 11.267, -7.355, 0, 11.533, 1.272, 0, 11.783, -0.843, 0, 12.183, 0.6, 0, 12.25, 0.551, 0, 12.583, 3.368, 0, 12.8, 1.107, 0, 13, 2.012, 0, 13.267, -6.874, 0, 13.5, 3.136, 0, 13.767, -4.299, 0, 14.383, 3.646, 0, 15.1, -4.445, 0, 15.467, 3.807, 0, 15.733, -2.754, 0, 15.883, 30, 2, 15.95, 30, 1, 15.978, 30, 16.005, -30, 16.033, -30, 2, 16.167, -30, 0, 16.333, 30, 2, 16.367, 30, 0, 16.617, -11.238, 0, 17.05, 1.69, 0, 17.133, 1.19, 0, 17.2, 2.987, 0, 17.433, -2.078, 0, 17.467, -1.88, 0, 17.483, -2.466, 0, 17.633, 2.649, 0, 17.85, -2.795, 0, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -3.637, 0, 0.617, 7.459, 0, 1.133, -10.668, 2, 1.8, -10.668, 0, 2.433, -8.001, 0, 2.817, -18.673, 0, 3.25, 18.486, 0, 3.65, -13.377, 0, 4.6, 5.71, 0, 4.85, -19.669, 0, 5.05, 9.334, 0, 5.3, -20.835, 0, 5.767, 0, 2, 10.25, 0, 0, 10.867, 30, 2, 11, 30, 0, 11.333, 16.584, 0, 11.733, 20.779, 0, 12.9, -3.84, 0, 13.167, 4.962, 0, 13.35, 2.184, 0, 13.817, 17.299, 0, 14.367, 8.085, 0, 14.633, 9.659, 0, 15.217, 4.922, 0, 15.633, 17.479, 0, 15.883, -30, 2, 16.1, -30, 0, 16.35, 20.029, 0, 16.8, -3.188, 0, 17.217, 2.378, 0, 17.483, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 3.637, 0, 0.617, -7.459, 0, 1.133, 10.668, 2, 1.8, 10.668, 0, 2.433, 8.001, 0, 2.817, 18.673, 0, 3.25, -18.486, 0, 3.65, 13.377, 0, 4.6, -5.71, 0, 4.85, 19.669, 0, 5.05, -9.334, 0, 5.3, 20.835, 0, 5.767, 0, 2, 10.25, 0, 0, 10.867, -30, 2, 10.883, -30, 0, 11.133, 19.452, 0, 11.45, -8.825, 0, 11.817, 1.809, 0, 12.05, 0.566, 0, 12.717, 5.532, 0, 13.1, -5.882, 0, 13.317, 2.509, 0, 13.583, -7.557, 0, 13.983, 2.619, 0, 14.233, 1.334, 0, 14.35, 1.553, 0, 14.517, 1.075, 0, 14.733, 1.762, 0, 15.067, -1.715, 0, 15.133, -1.635, 0, 15.45, -5.604, 0, 15.767, 3.538, 0, 15.867, 2.417, 1, 15.872, 2.417, 15.878, 30, 15.883, 30, 2, 15.983, 30, 0, 16.067, -30, 2, 16.25, -30, 0, 16.467, 29.625, 0, 16.8, -9.503, 0, 17.05, 0.83, 0, 17.167, -2.301, 0, 17.433, 1.689, 0, 17.467, 1.635, 0, 17.483, 2.41, 0, 17.55, 0, 2, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Jewelry_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 0.303, 0, 0.6, -0.533, 0, 0.933, 0.876, 0, 1.317, -0.857, 0, 1.733, 0.545, 0, 2.15, -0.402, 0, 2.65, 0.608, 0, 3.067, -1.792, 0, 3.467, 2.891, 0, 3.867, -2.521, 0, 4.283, 1.334, 0, 4.617, -0.503, 0, 4.817, 0.969, 0, 5.033, -2.217, 0, 5.283, 2.137, 0, 5.65, -1.833, 0, 6.05, 1.347, 0, 6.467, -0.856, 0, 6.883, 0.544, 0, 7.3, -0.346, 0, 7.717, 0.221, 0, 8.133, -0.14, 0, 8.55, 0.047, 0, 8.95, -0.062, 0, 9.35, 0.082, 0, 9.767, -0.055, 0, 10.167, 0.072, 0, 10.533, -0.013, 0, 10.917, 0.077, 0, 11.4, -0.004, 2, 11.417, -0.004, 0, 11.7, 0.013, 0, 12.2, -0.052, 0, 12.817, 0.199, 0, 13.133, -0.514, 0, 13.4, 0.509, 0, 13.767, -0.443, 0, 14.15, 0.527, 0, 14.583, -0.819, 0, 15.133, 0.931, 0, 15.583, -0.98, 0, 15.867, 0.311, 0, 15.883, -1.208, 0, 16.217, 0.954, 0, 16.633, -0.607, 0, 17.05, 0.386, 0, 17.467, -0.246, 0, 17.883, 0.156, 0, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Jewelry_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -0.303, 0, 0.567, 0.426, 0, 0.783, -1.368, 0, 1.15, 2.369, 0, 1.533, -1.836, 0, 1.933, 1.19, 0, 2.35, -0.907, 0, 2.883, 1.711, 0, 3.283, -4.739, 0, 3.683, 6.571, 0, 4.067, -4.999, 0, 4.467, 2.484, 2, 4.483, 2.484, 0, 4.767, -1.347, 0, 4.983, 3.075, 0, 5.217, -4.583, 0, 5.517, 4.676, 0, 5.85, -4.468, 0, 6.25, 2.935, 0, 6.667, -1.818, 0, 7.083, 1.149, 0, 7.5, -0.733, 0, 7.917, 0.473, 0, 8.317, -0.298, 0, 8.633, 0.126, 0, 8.75, 0.092, 0, 8.8, 0.121, 0, 9.217, -0.192, 0, 9.583, 0.17, 0, 9.65, 0.145, 2, 9.667, 0.145, 0, 10.05, -0.2, 0, 10.45, 0.203, 0, 10.733, 0.004, 0, 11.15, 0.267, 0, 11.6, 0.112, 0, 11.85, 0.134, 0, 12.433, -0.097, 0, 12.633, -0.025, 0, 12.683, -0.028, 0, 13.033, 0.671, 0, 13.317, -1.265, 0, 13.617, 1.19, 0, 13.967, -1.192, 0, 14.367, 1.34, 0, 14.817, -2.022, 0, 15.383, 2.171, 0, 15.8, -1.965, 0, 15.883, -0.044, 0, 16.1, -1.818, 0, 16.45, 1.954, 0, 16.833, -1.328, 0, 17.25, 0.829, 0, 17.667, -0.521, 0, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Jewelry_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.317, -0.167, 0, 0.7, 0.879, 0, 0.967, -1.66, 0, 1.3, 2.153, 0, 1.65, -1.818, 0, 2.05, 1.222, 0, 2.467, -0.814, 0, 3.083, 2.008, 0, 3.45, -4.253, 0, 3.817, 5.5, 0, 4.2, -4.404, 0, 4.583, 2.785, 0, 4.9, -2.623, 0, 5.133, 3.758, 0, 5.383, -5.072, 0, 5.667, 5.406, 0, 6, -4.697, 0, 6.367, 3.123, 0, 6.767, -1.812, 0, 7.183, 1.075, 0, 7.583, -0.671, 0, 8.05, 0.464, 2, 8.067, 0.464, 0, 8.45, -0.382, 0, 8.75, 0.207, 0, 8.8, 0.155, 0, 8.867, 0.191, 0, 9.15, -0.076, 0, 9.217, -0.048, 0, 9.367, -0.241, 0, 9.65, 0.155, 0, 9.967, -0.037, 0, 10.017, -0.022, 0, 10.2, -0.186, 0, 10.567, 0.142, 0, 10.9, -0.142, 0, 11.267, 0.097, 0, 11.65, -0.039, 0, 12.083, 0.054, 2, 12.1, 0.054, 0, 12.583, -0.075, 0, 12.683, -0.067, 0, 12.883, -0.153, 0, 13.183, 0.763, 0, 13.467, -1.314, 0, 13.783, 1.422, 0, 14.117, -1.381, 0, 14.517, 1.365, 0, 14.95, -1.317, 0, 15.567, 1.379, 0, 15.883, -1.408, 0, 16.083, 0.342, 0, 16.3, -1.255, 0, 16.6, 1.757, 0, 16.967, -1.359, 0, 17.35, 0.833, 0, 17.8, -0.573, 0, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.417, -0.843, 0, 0.867, 1.652, 0, 1.233, -2.14, 0, 1.65, 1.375, 0, 2.067, -1.095, 0, 2.533, 0.985, 0, 2.95, -1.749, 0, 3.367, 1.768, 0, 3.967, -1.403, 0, 4.417, 1.611, 0, 4.767, -0.533, 0, 4.8, -0.53, 0, 5, -1.436, 0, 5.3, 1.58, 0, 5.65, -1.528, 0, 6.05, 1.119, 0, 6.517, -0.598, 0, 7.183, 0.627, 0, 7.783, -0.23, 0, 8.117, 0.498, 0, 8.483, -0.574, 0, 8.933, 0.404, 0, 9.383, -0.909, 0, 9.783, 1.878, 0, 10.15, -1.751, 0, 10.567, 1.155, 0, 10.983, -0.714, 0, 11.4, 0.464, 0, 11.817, -0.346, 0, 12.25, 0.203, 0, 12.55, -0.046, 0, 12.817, 0.293, 0, 13.167, -0.753, 0, 13.483, 0.945, 2, 13.5, 0.945, 0, 13.817, -0.911, 0, 14.2, 0.925, 0, 14.617, -1.056, 0, 15.1, 0.88, 0, 15.533, -0.59, 0, 15.867, 0.322, 0, 15.883, -0.049, 0, 16.1, 0.221, 0, 16.533, -0.166, 0, 16.95, 0.079, 0, 17.367, -0.065, 0, 17.817, 0.059, 0, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.35, 0.378, 0, 0.633, -2.224, 0, 1.083, 3.835, 0, 1.45, -3.87, 0, 1.85, 2.807, 0, 2.267, -2.331, 0, 2.767, 2.294, 0, 3.133, -3.865, 0, 3.6, 3.32, 0, 4.217, -2.817, 0, 4.617, 3.145, 0, 4.9, -0.251, 0, 4.95, -0.191, 0, 5.167, -3.009, 0, 5.5, 3.462, 0, 5.85, -3.411, 0, 6.267, 2.255, 0, 6.7, -0.914, 0, 6.867, -0.688, 0, 7.083, -0.994, 0, 7.4, 1.141, 0, 7.983, -0.519, 0, 8.317, 1.29, 0, 8.7, -1.321, 0, 9.2, 0.854, 0, 9.6, -2.469, 0, 9.967, 4.213, 0, 10.35, -3.425, 0, 10.767, 2.308, 0, 11.183, -1.441, 0, 11.6, 1.045, 0, 12.017, -0.739, 0, 12.467, 0.383, 0, 12.733, -0.142, 0, 13.05, 1.025, 0, 13.367, -1.923, 0, 13.683, 2.232, 0, 14.017, -2.205, 0, 14.4, 2.278, 0, 14.833, -2.343, 0, 15.317, 1.724, 0, 15.75, -1.126, 0, 16.35, 0.362, 0, 16.75, -0.328, 0, 17.15, 0.148, 0, 17.567, -0.174, 0, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 0.909, 0, 0.6, -1.6, 0, 0.933, 2.627, 0, 1.317, -2.572, 0, 1.733, 1.635, 0, 2.15, -1.207, 0, 2.65, 1.824, 0, 3.067, -5.377, 0, 3.467, 8.673, 0, 3.867, -7.562, 0, 4.283, 4.003, 0, 4.617, -1.509, 0, 4.817, 2.906, 0, 5.033, -6.652, 0, 5.283, 6.41, 0, 5.65, -5.499, 0, 6.05, 4.041, 0, 6.467, -2.567, 0, 6.883, 1.633, 0, 7.3, -1.039, 0, 7.717, 0.661, 0, 8.133, -0.421, 0, 8.55, 0.141, 0, 8.95, -0.186, 0, 9.35, 0.245, 0, 9.767, -0.164, 0, 10.167, 0.216, 0, 10.533, -0.038, 0, 10.917, 0.232, 0, 11.4, -0.011, 2, 11.417, -0.011, 0, 11.7, 0.038, 0, 12.2, -0.157, 0, 12.817, 0.597, 0, 13.133, -1.543, 0, 13.4, 1.527, 0, 13.767, -1.329, 0, 14.15, 1.58, 0, 14.583, -2.457, 0, 15.133, 2.794, 0, 15.583, -2.94, 0, 15.867, 0.933, 0, 15.883, -3.624, 0, 16.217, 2.863, 0, 16.633, -1.821, 0, 17.05, 1.158, 0, 17.467, -0.737, 0, 17.883, 0.469, 0, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -0.909, 0, 0.567, 1.277, 0, 0.783, -4.105, 0, 1.15, 7.109, 0, 1.533, -5.507, 0, 1.933, 3.57, 0, 2.35, -2.722, 0, 2.883, 5.133, 0, 3.283, -14.218, 0, 3.683, 19.713, 0, 4.067, -14.998, 0, 4.467, 7.452, 2, 4.483, 7.452, 0, 4.767, -4.04, 0, 4.983, 9.226, 0, 5.217, -13.748, 0, 5.517, 14.03, 0, 5.85, -13.403, 0, 6.25, 8.806, 0, 6.667, -5.453, 0, 7.083, 3.447, 0, 7.5, -2.199, 0, 7.917, 1.418, 0, 8.317, -0.895, 0, 8.633, 0.378, 0, 8.75, 0.276, 0, 8.8, 0.364, 0, 9.217, -0.575, 0, 9.583, 0.509, 0, 9.65, 0.434, 0, 9.667, 0.436, 0, 10.05, -0.599, 0, 10.45, 0.609, 0, 10.733, 0.011, 0, 11.15, 0.801, 0, 11.6, 0.334, 0, 11.85, 0.4, 0, 12.433, -0.291, 0, 12.633, -0.075, 0, 12.683, -0.085, 0, 13.033, 2.013, 0, 13.317, -3.794, 0, 13.617, 3.57, 0, 13.967, -3.575, 0, 14.367, 4.019, 0, 14.817, -6.067, 0, 15.383, 6.512, 0, 15.8, -5.895, 0, 15.883, -0.131, 0, 16.1, -5.455, 0, 16.45, 5.862, 0, 16.833, -3.984, 0, 17.25, 2.486, 0, 17.667, -1.564, 0, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.317, -0.5, 0, 0.7, 2.638, 0, 0.967, -4.979, 0, 1.3, 6.459, 0, 1.65, -5.455, 0, 2.05, 3.667, 0, 2.467, -2.441, 0, 3.083, 6.024, 0, 3.45, -12.759, 0, 3.817, 16.501, 0, 4.2, -13.213, 0, 4.583, 8.355, 0, 4.9, -7.868, 0, 5.133, 11.275, 0, 5.383, -15.216, 0, 5.667, 16.218, 0, 6, -14.092, 0, 6.367, 9.37, 0, 6.767, -5.437, 0, 7.183, 3.225, 0, 7.583, -2.013, 0, 8.067, 1.391, 0, 8.45, -1.147, 0, 8.75, 0.62, 0, 8.8, 0.465, 0, 8.867, 0.572, 0, 9.15, -0.228, 0, 9.217, -0.144, 0, 9.367, -0.724, 0, 9.65, 0.466, 0, 9.967, -0.11, 0, 10.017, -0.066, 0, 10.2, -0.557, 0, 10.567, 0.425, 0, 10.9, -0.426, 0, 11.267, 0.292, 0, 11.65, -0.117, 0, 12.083, 0.161, 2, 12.1, 0.161, 0, 12.583, -0.226, 0, 12.683, -0.201, 0, 12.883, -0.458, 0, 13.183, 2.288, 0, 13.467, -3.942, 0, 13.783, 4.266, 0, 14.117, -4.143, 0, 14.517, 4.094, 0, 14.95, -3.953, 0, 15.567, 4.138, 0, 15.883, -4.223, 0, 16.083, 1.027, 0, 16.3, -3.764, 0, 16.6, 5.271, 0, 16.967, -4.076, 0, 17.35, 2.499, 0, 17.8, -1.718, 0, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.5, -0.902, 0, 0.817, 2.093, 0, 1.083, -5.606, 0, 1.417, 8.418, 0, 1.767, -8.053, 0, 2.133, 5.851, 0, 2.533, -3.791, 0, 3.167, 6.452, 0, 3.533, -14.998, 0, 3.9, 20.668, 0, 4.283, -18.397, 0, 4.667, 12.78, 0, 5, -10.214, 0, 5.25, 11.562, 0, 5.5, -16.105, 0, 5.783, 20.04, 0, 6.117, -19.756, 0, 6.467, 14.807, 0, 6.833, -9.079, 0, 7.25, 5.131, 0, 7.667, -3.135, 0, 8.117, 1.766, 0, 8.533, -1.454, 0, 8.833, 0.884, 0, 9.217, -0.328, 0, 9.4, -0.022, 0, 9.55, -0.272, 0, 9.833, 0.812, 0, 10.217, -0.395, 0, 10.683, 0.509, 0, 11.017, -0.594, 0, 11.367, 0.47, 0, 11.733, -0.231, 0, 12.15, 0.218, 0, 12.7, -0.275, 0, 12.767, -0.267, 0, 12.967, -0.402, 0, 13.283, 2.4, 0, 13.583, -4.586, 0, 13.9, 5.697, 0, 14.233, -5.989, 0, 14.6, 5.935, 0, 15.033, -5.341, 0, 15.633, 4.455, 0, 15.983, -4.923, 2, 16, -4.923, 0, 16.233, 0.886, 0, 16.433, -3.19, 0, 16.733, 6.46, 0, 17.067, -6.134, 0, 17.433, 4.144, 0, 17.85, -2.299, 0, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.333, 0.971, 0, 0.633, -0.768, 0, 0.917, 2.019, 0, 1.2, -6.277, 0, 1.517, 10.578, 0, 1.867, -11.313, 0, 2.233, 9.026, 0, 2.617, -6.047, 0, 3.25, 6.639, 0, 3.617, -16.964, 0, 3.983, 24.471, 0, 4.367, -23.637, 0, 4.75, 17.87, 0, 5.1, -14.79, 0, 5.367, 14.285, 0, 5.633, -18.532, 0, 5.9, 23.896, 0, 6.217, -25.665, 0, 6.567, 21.375, 0, 6.933, -14.474, 0, 7.317, 8.452, 0, 7.733, -4.787, 0, 8.15, 2.731, 0, 8.633, -2.271, 0, 8.983, 1.698, 0, 9.35, -0.826, 0, 9.367, -0.808, 0, 9.417, -0.939, 0, 9.967, 0.634, 0, 10.317, -0.93, 0, 10.767, 0.693, 0, 11.117, -0.811, 0, 11.467, 0.717, 0, 11.833, -0.421, 0, 12.217, 0.33, 0, 12.8, -0.328, 0, 12.9, -0.316, 0, 13.033, -0.358, 0, 13.4, 2.549, 0, 13.7, -5.372, 0, 14.017, 7.361, 0, 14.333, -8.343, 0, 14.7, 8.57, 0, 15.1, -7.437, 0, 15.667, 4.868, 0, 16.1, -5.982, 0, 16.367, 1.627, 0, 16.567, -2.591, 0, 16.85, 7.435, 0, 17.183, -8.571, 0, 17.533, 6.613, 0, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.45, 1.004, 0, 0.733, -0.92, 0, 1.017, 2.139, 0, 1.317, -7.013, 0, 1.633, 12.995, 0, 1.967, -15.22, 0, 2.333, 13.278, 0, 2.7, -9.51, 0, 3.3, 6.471, 0, 3.683, -18.667, 0, 4.05, 27.705, 0, 4.433, -28.161, 0, 4.85, 23.204, 0, 5.2, -19.286, 0, 5.483, 17.033, 0, 5.75, -21.009, 0, 6, 28.091, 0, 6.3, -30, 2, 6.333, -30, 0, 6.667, 28.44, 0, 7.033, -21.134, 0, 7.417, 13.445, 0, 7.817, -7.671, 0, 8.233, 4.316, 0, 8.7, -2.658, 0, 9.083, 2.34, 0, 9.45, -1.347, 0, 10, 0.753, 0, 10.45, -1.059, 0, 10.85, 0.892, 0, 11.217, -1.076, 0, 11.583, 1.045, 0, 11.933, -0.712, 0, 12.3, 0.532, 0, 12.85, -0.376, 0, 13.483, 2.72, 0, 13.817, -6.246, 0, 14.133, 9.288, 0, 14.45, -11.292, 0, 14.8, 12.275, 0, 15.183, -10.637, 0, 15.65, 6.335, 0, 16.217, -7.267, 0, 16.5, 2.845, 0, 16.7, -1.96, 0, 16.967, 8.11, 0, 17.3, -11.257, 0, 17.65, 9.93, 0, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.533, 1.235, 0, 0.85, -1.648, 0, 1.117, 2.38, 0, 1.417, -7.829, 0, 1.733, 15.594, 0, 2.067, -19.709, 0, 2.433, 18.519, 0, 2.8, -14.318, 0, 3.2, 8.377, 0, 3.75, -19.756, 0, 4.1, 30, 0, 4.483, -30, 2, 4.517, -30, 0, 4.917, 27.837, 0, 5.3, -24.661, 0, 5.6, 21.909, 0, 5.867, -23.855, 0, 6.083, 30, 2, 6.133, 30, 0, 6.35, -30, 2, 6.45, -30, 0, 6.7, 30, 2, 6.8, 30, 0, 7.117, -28.332, 0, 7.5, 19.791, 0, 7.883, -12.146, 0, 8.3, 6.854, 0, 8.767, -3.967, 0, 9.167, 3.112, 0, 9.55, -2.21, 0, 9.967, 1.055, 0, 10.55, -1.246, 0, 10.933, 1.296, 0, 11.317, -1.442, 0, 11.683, 1.484, 0, 12.033, -1.134, 0, 12.4, 0.86, 0, 12.833, -0.5, 0, 13.583, 2.909, 0, 13.917, -7.247, 0, 14.233, 11.548, 0, 14.55, -14.761, 0, 14.9, 16.713, 0, 15.267, -15.121, 0, 15.683, 9.209, 0, 16.3, -7.958, 0, 16.617, 4.503, 0, 16.833, -1.593, 0, 17.083, 8.461, 0, 17.4, -14.093, 0, 17.75, 14.097, 0, 17.917, 0, 2, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.417, 1, 0, 3.25, 0, 0, 4.417, 1, 0, 6.25, 0, 0, 7.417, 1, 0, 9.25, 0, 0, 10.417, 1, 0, 12.25, 0, 0, 13.417, 1, 0, 15.25, 0, 0, 16.417, 1, 1, 17, 1, 17.584, 0.089, 18.167, 0.006]}, {"Target": "Parameter", "Id": "fenshangxuanzhuang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 6.222, 2.391, 12.195, 4.782, 18.167, 7.173]}, {"Target": "Parameter", "Id": "Mail_Transparency6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamSad2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 18.167, 1]}, {"Target": "Parameter", "Id": "ParamCRY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.2, 0, 18.167, -5.2]}, {"Target": "Parameter", "Id": "Position_Coordinates", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 18.167, 1]}, {"Target": "Parameter", "Id": "Man_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 18.167, 1]}, {"Target": "Parameter", "Id": "Man_TMD2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "BG_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 18.167, 1]}, {"Target": "Parameter", "Id": "FG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "BG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "BG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "BG_White4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "BG_White5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "BG_White2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "All_Size3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "All_Size4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "tuerzuo3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "faqiehuan", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "faqiehuan2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "faqiehuan4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "faqiehuan3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "tuerzuo4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Mail_Change", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "dafengshangzhuanchang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "tongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamTongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamBrowY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamSigh", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamDarken", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamCry", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_B", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_C", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "MB_Unidirectional_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "huqi1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "huqi2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamShoulderLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamArmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 18.167, 1]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamArmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamArmL_ForearmL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamForearmL_HandL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamHandLSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "xiongjiya", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "xiongjiya5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "xiongjiya3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "xiongjiya4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "xiongjiya2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamShoulderRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamArmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 18.167, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamArmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamArmR_ForearmR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamForearm<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 18.167, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_HandR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamHandRSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 18.167, 0.6]}, {"Target": "Parameter", "Id": "Change_idle_physics23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "taiqiugundong", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -22.5, 0, 18.167, -22.5]}, {"Target": "Parameter", "Id": "Change_idle_physics32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamLegL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 18.167, 1]}, {"Target": "Parameter", "Id": "ParamLegLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamCrusLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamCrusLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>oodLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamFoodLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamFoodLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamLegR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 18.167, 1]}, {"Target": "Parameter", "Id": "ParamLegRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamCrusRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamCrusRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamCrus<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>ood<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamFoodRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamFoodRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Physics_Provisional", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "physics_Add_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "physics_Add_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamBodyStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Physics_Body_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Physics_Body_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Physics_breast", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamBreath2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "touch_drag1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "touch_drag2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "touch_drag3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "touch_drag4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "touch_drag5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "touch_drag6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "touch_drag7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "touch_drag8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "touch_drag9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>pian<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh654", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh655", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh657", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh656", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh658", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh659", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh660", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 18.167, 0]}], "UserData": [{"Time": 0.25, "Value": ""}, {"Time": 17.667, "Value": ""}]}