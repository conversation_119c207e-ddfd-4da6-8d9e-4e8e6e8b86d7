{"Version": 3, "Meta": {"Duration": 8.667, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 303, "TotalSegmentCount": 1660, "TotalPointCount": 2138, "UserDataCount": 2, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.433, 0.6, 0, 0.683, -0.1, 0, 0.85, 0.7, 0, 1.167, -0.1, 0, 1.417, 1, 0, 1.617, -0.3, 0, 1.817, 1, 1, 2.05, 1, 2.284, 0.56, 2.517, -0.098, 1, 2.656, -0.49, 2.794, -0.6, 2.933, -0.6, 1, 2.955, -0.6, 2.978, -0.612, 3, -0.57, 1, 3.072, -0.435, 3.145, -0.173, 3.217, 0.036, 1, 3.367, 0.471, 3.517, 0.7, 3.667, 0.7, 0, 4.1, -0.8, 0, 4.85, 0.6, 0, 5.317, 0.1, 0, 5.6, 1, 0, 6.05, 0.3, 0, 6.467, 1, 0, 7.017, -0.5, 0, 7.267, 1, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY_SD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.333, 0, 0, 0.45, 1, 0, 0.567, 0, 0, 0.75, 1, 0, 0.867, 0, 0, 1.1, 1, 0, 1.267, 0, 0, 1.45, 1, 0, 1.567, 0, 0, 1.75, 1, 0, 1.867, 0, 0, 2.05, 1, 0, 2.3, 0, 2, 2.517, 0, 0, 2.7, 1, 0, 2.817, 0, 0, 2.933, 1, 1, 2.955, 1, 2.978, 0.774, 3, 0.395, 1, 3.017, 0.111, 3.033, 0, 3.05, 0, 1, 3.106, 0, 3.161, 0.348, 3.217, 0.802, 1, 3.239, 0.984, 3.261, 1, 3.283, 1, 0, 3.45, 0, 0, 3.633, 1, 0, 3.75, 0, 0, 3.933, 1, 0, 4.05, 0, 0, 4.233, 1, 0, 4.367, 0, 0, 4.517, 1, 0, 4.733, 0, 2, 5.05, 0, 0, 5.167, 1, 0, 5.283, 0, 0, 5.467, 1, 0, 5.583, 0, 0, 5.817, 1, 0, 5.983, 0, 0, 6.167, 1, 0, 6.283, 0, 0, 6.467, 1, 0, 6.583, 0, 0, 6.767, 1, 0, 6.9, 0, 0, 7.05, 1, 0, 7.217, 0, 0, 7.333, 1, 0, 7.45, 0, 0, 7.633, 1, 0, 7.75, 0, 2, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.3, 0, 0, 1.4, 30, 0, 1.467, 0, 2, 1.833, 0, 2, 1.983, 0, 2, 2.133, 0, 1, 2.155, 0, 2.178, 30, 2.2, 30, 2, 2.333, 30, 0, 2.5, 0, 2, 2.517, 0, 2, 3, 0, 2, 3.217, 0, 2, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 0.683, 1, 0, 0.817, 0, 0, 1, 1.3, 1, 1.028, 1.3, 1.055, 1.3, 1.083, 1, 1, 1.116, 0.632, 1.15, 0, 1.183, 0, 2, 1.883, 0, 2, 2.3, 0, 0, 2.45, 1, 1, 2.472, 1, 2.495, 0.833, 2.517, 0.5, 1, 2.539, 0.167, 2.561, 0, 2.583, 0, 0, 2.767, 1.3, 0, 2.85, 1, 2, 3, 1, 2, 3.217, 1, 2, 5.017, 1, 0, 5.233, 0, 0, 5.417, 1, 2, 5.75, 1, 0, 5.967, 0, 0, 6.15, 1, 2, 6.45, 1, 0, 6.683, 0, 0, 6.8, 1.3, 0, 6.95, 1, 2, 7.6, 1, 0, 7.733, 0, 0, 7.933, 1, 2, 8.367, 1, 2, 8.667, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.883, 1, 2, 2.317, 1, 0, 2.5, 0, 2, 2.517, 0, 2, 3, 0, 2, 3.217, 0, 2, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 0.683, 1, 0, 0.817, 0, 0, 1, 1.3, 1, 1.028, 1.3, 1.055, 1.3, 1.083, 1, 1, 1.116, 0.632, 1.15, 0, 1.183, 0, 2, 1.883, 0, 2, 2.3, 0, 0, 2.45, 1, 1, 2.472, 1, 2.495, 0.833, 2.517, 0.5, 1, 2.539, 0.167, 2.561, 0, 2.583, 0, 0, 2.767, 1.3, 0, 2.85, 1, 2, 3, 1, 2, 3.217, 1, 2, 5.017, 1, 0, 5.233, 0, 0, 5.417, 1, 2, 5.75, 1, 0, 5.967, 0, 0, 6.15, 1, 2, 6.45, 1, 0, 6.683, 0, 0, 6.8, 1.3, 0, 6.95, 1, 2, 7.6, 1, 0, 7.733, 0, 0, 7.933, 1, 2, 8.367, 1, 2, 8.667, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.883, 1, 2, 2.317, 1, 0, 2.5, 0, 2, 2.517, 0, 2, 3, 0, 2, 3.217, 0, 2, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.517, 0, 2, 3, 0, 2, 3.217, 0, 2, 5.017, 0, 1, 5.145, 0, 5.272, -0.377, 5.4, -0.5, 1, 5.517, -0.613, 5.633, -0.6, 5.75, -0.6, 1, 5.889, -0.6, 6.028, 0.371, 6.167, 0.5, 1, 6.3, 0.624, 6.434, 0.6, 6.567, 0.6, 0, 6.8, 0, 2, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.517, 0, 2, 3, 0, 2, 3.217, 0, 2, 5.017, 0, 1, 5.145, 0, 5.272, -0.209, 5.4, -0.4, 1, 5.517, -0.575, 5.633, -0.6, 5.75, -0.6, 0, 6.167, 0.2, 0, 6.567, 0, 2, 6.8, 0, 2, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 1.006, 0, 1.761, -0.038, 2.517, -0.12, 1, 2.678, -0.137, 2.839, -0.157, 3, -0.178, 1, 3.072, -0.187, 3.145, -0.199, 3.217, -0.213, 1, 4.011, -0.372, 4.806, -0.509, 5.6, -0.7, 1, 6.256, -0.857, 6.911, -1, 7.567, -1, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamBrowForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.483, 0, 2, 2.517, 0, 2, 2.983, 0, 1, 2.989, 0, 2.994, 0.003, 3, -0.001, 1, 3.072, -0.057, 3.145, -0.19, 3.217, -0.293, 1, 3.545, -0.759, 3.872, -1, 4.2, -1, 2, 6.55, -1, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamBrowAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.483, 1, 2, 2.517, 1, 2, 2.983, 1, 2, 3, 1, 2, 3.217, 1, 2, 4.2, 1, 2, 6.55, 1, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.683, 0, 0, 0.75, 1, 2, 0.9, 1, 0, 0.917, -1, 2, 1.05, -1, 0, 1.15, 1, 2, 1.267, 1, 0, 1.533, -0.31, 0, 1.85, 0.086, 0, 2.15, -0.024, 0, 2.3, -0.006, 0, 2.383, -1, 2, 2.517, -1, 2, 2.7, -1, 0, 2.833, 1, 2, 2.983, 1, 1, 2.989, 1, 2.994, 1, 3, 0.985, 1, 3.072, 0.619, 3.145, 0.043, 3.217, -0.291, 1, 3.228, -0.342, 3.239, -0.323, 3.25, -0.323, 0, 3.55, 0.089, 0, 3.867, -0.025, 0, 4.167, 0.007, 2, 4.183, 0.007, 0, 4.483, -0.002, 2, 4.5, -0.002, 2, 4.517, -0.002, 0, 4.75, 0, 2, 4.783, 0, 0, 4.8, 0.001, 0, 4.817, 0, 2, 4.85, 0, 2, 4.867, 0, 2, 4.883, 0, 2, 4.917, 0, 2, 4.933, 0, 2, 4.95, 0, 2, 4.967, 0, 2, 4.983, 0, 2, 5, 0, 2, 5.017, 0, 0, 5.133, 1, 2, 5.233, 1, 0, 5.367, -1, 2, 5.467, -1, 0, 5.733, 0.301, 0, 5.75, 0.293, 0, 5.85, 1, 2, 5.967, 1, 0, 6.083, -1, 2, 6.233, -1, 0, 6.55, 1, 2, 6.667, 1, 0, 6.75, -1, 2, 6.867, -1, 0, 6.883, 1, 2, 7.033, 1, 0, 7.267, -0.407, 0, 7.567, 0.112, 0, 7.6, 0.108, 0, 7.667, 1, 2, 7.867, 1, 0, 7.883, -1, 2, 7.95, -1, 0, 7.967, 1, 2, 8.217, 1, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeRsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.683, 0, 0, 0.75, 1, 2, 0.9, 1, 0, 0.917, -1, 2, 1.05, -1, 0, 1.15, 1, 2, 1.267, 1, 0, 1.533, -0.31, 0, 1.85, 0.086, 0, 2.15, -0.024, 0, 2.3, -0.006, 0, 2.383, -1, 2, 2.517, -1, 2, 2.7, -1, 0, 2.833, 1, 2, 2.983, 1, 1, 2.989, 1, 2.994, 1, 3, 0.985, 1, 3.072, 0.619, 3.145, 0.043, 3.217, -0.291, 1, 3.228, -0.342, 3.239, -0.323, 3.25, -0.323, 0, 3.55, 0.089, 0, 3.867, -0.025, 0, 4.167, 0.007, 2, 4.183, 0.007, 0, 4.483, -0.002, 2, 4.5, -0.002, 2, 4.517, -0.002, 0, 4.75, 0, 2, 4.783, 0, 0, 4.8, 0.001, 0, 4.817, 0, 2, 4.85, 0, 2, 4.867, 0, 2, 4.883, 0, 2, 4.917, 0, 2, 4.933, 0, 2, 4.95, 0, 2, 4.967, 0, 2, 4.983, 0, 2, 5, 0, 2, 5.017, 0, 0, 5.133, 1, 2, 5.233, 1, 0, 5.367, -1, 2, 5.467, -1, 0, 5.733, 0.301, 0, 5.75, 0.293, 0, 5.85, 1, 2, 5.967, 1, 0, 6.083, -1, 2, 6.233, -1, 0, 6.55, 1, 2, 6.667, 1, 0, 6.75, -1, 2, 6.867, -1, 0, 6.883, 1, 2, 7.033, 1, 0, 7.267, -0.407, 0, 7.567, 0.112, 0, 7.6, 0.108, 0, 7.667, 1, 2, 7.867, 1, 0, 7.883, -1, 2, 7.95, -1, 0, 7.967, 1, 2, 8.217, 1, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeLGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.683, 0, 0, 0.75, -0.305, 0, 0.8, -0.2, 0, 0.9, -1, 2, 0.917, -1, 0, 0.933, 1, 2, 0.967, 1, 0, 1.233, -0.359, 0, 1.45, 0.223, 0, 1.7, -0.183, 0, 2, 0.09, 0, 2.3, -0.036, 0, 2.367, 0.287, 0, 2.45, 0.15, 1, 2.472, 0.15, 2.495, 0.322, 2.517, 0.61, 1, 2.534, 0.826, 2.55, 0.911, 2.567, 0.911, 0, 2.8, -1, 1, 2.867, -1, 2.933, -0.605, 3, -0.02, 1, 3.039, 0.321, 3.078, 0.414, 3.117, 0.414, 1, 3.15, 0.414, 3.184, 0.385, 3.217, 0.243, 1, 3.284, -0.042, 3.35, -0.245, 3.417, -0.245, 0, 3.717, 0.108, 0, 4.017, -0.042, 0, 4.317, 0.015, 0, 4.633, -0.005, 0, 4.917, 0.002, 2, 4.967, 0.002, 0, 5.117, -0.287, 0, 5.317, 1, 2, 5.35, 1, 0, 5.65, -0.309, 0, 5.767, -0.12, 0, 5.833, -0.177, 0, 6.033, 1, 2, 6.083, 1, 0, 6.417, -0.31, 0, 6.483, -0.253, 0, 6.5, -0.256, 0, 6.717, 1, 2, 6.75, 1, 0, 6.767, -1, 2, 6.867, -1, 0, 7.133, 0.342, 0, 7.417, -0.271, 0, 7.6, 0.008, 0, 7.667, -0.236, 0, 7.717, -0.16, 0, 7.85, -1, 2, 7.9, -1, 0, 7.917, 1, 2, 8.017, 1, 0, 8.033, -1, 2, 8.183, -1, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeRGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.683, 0, 0, 0.75, -0.305, 0, 0.8, -0.2, 0, 0.9, -1, 2, 0.917, -1, 0, 0.933, 1, 2, 0.967, 1, 0, 1.233, -0.359, 0, 1.45, 0.223, 0, 1.7, -0.183, 0, 2, 0.09, 0, 2.3, -0.036, 0, 2.367, 0.287, 0, 2.45, 0.15, 1, 2.472, 0.15, 2.495, 0.322, 2.517, 0.61, 1, 2.534, 0.826, 2.55, 0.911, 2.567, 0.911, 0, 2.8, -1, 1, 2.867, -1, 2.933, -0.605, 3, -0.02, 1, 3.039, 0.321, 3.078, 0.414, 3.117, 0.414, 1, 3.15, 0.414, 3.184, 0.385, 3.217, 0.243, 1, 3.284, -0.042, 3.35, -0.245, 3.417, -0.245, 0, 3.717, 0.108, 0, 4.017, -0.042, 0, 4.317, 0.015, 0, 4.633, -0.005, 0, 4.917, 0.002, 2, 4.967, 0.002, 0, 5.117, -0.287, 0, 5.317, 1, 2, 5.35, 1, 0, 5.65, -0.309, 0, 5.767, -0.12, 0, 5.833, -0.177, 0, 6.033, 1, 2, 6.083, 1, 0, 6.417, -0.31, 0, 6.483, -0.253, 0, 6.5, -0.256, 0, 6.717, 1, 2, 6.75, 1, 0, 6.767, -1, 2, 6.867, -1, 0, 7.133, 0.342, 0, 7.417, -0.271, 0, 7.6, 0.008, 0, 7.667, -0.236, 0, 7.717, -0.16, 0, 7.85, -1, 2, 7.9, -1, 0, 7.917, 1, 2, 8.017, 1, 0, 8.033, -1, 2, 8.183, -1, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.417, 0, 0, 0.75, 2.52, 0, 1.083, 0, 2, 2.517, 0, 1, 2.678, 0, 2.839, 0.472, 3, 1.111, 1, 3.072, 1.397, 3.145, 1.44, 3.217, 1.44, 2, 7.767, 1.44, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.083, 0, 2, 2.517, 0, 1, 2.678, 0, 2.839, -2.071, 3, -4.863, 1, 3.072, -6.115, 3.145, -6.3, 3.217, -6.3, 2, 7.767, -6.3, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 30, 2, 0.25, 30, 1, 0.356, 30, 0.461, 30.627, 0.567, 28.486, 1, 0.739, 24.993, 0.911, 7.5, 1.083, 7.5, 2, 2.517, 7.5, 1, 2.678, 7.5, 2.839, 4.519, 3, 0.507, 1, 3.072, -1.292, 3.145, -1.56, 3.217, -1.56, 2, 7.767, -1.56, 0, 8.367, 30, 2, 8.667, 30]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.417, 0, 0, 0.783, -19, 0, 1.083, 0, 2, 2.517, 0, 2, 3, 0, 2, 3.217, 0, 2, 7.767, 0, 2, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1, 2, 0.25, -1, 2, 2.517, -1, 2, 2.6, -1, 0, 2.633, 1, 2, 3, 1, 2, 3.217, 1, 2, 8.017, 1, 0, 8.033, -1, 2, 8.367, -1, 2, 8.667, -1]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.5, 0, 0, 0.7, -0.404, 0, 1.083, 0, 2, 2.517, 0, 1, 2.678, 0, 2.839, 0.006, 3, 0.026, 1, 3.072, 0.035, 3.145, 0.046, 3.217, 0.064, 1, 3.556, 0.147, 3.894, 0.201, 4.233, 0.201, 0, 5.567, 0.115, 0, 6.5, 0.156, 1, 6.922, 0.156, 7.345, 0.155, 7.767, 0.132, 1, 7.967, 0.121, 8.167, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamHandLDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10, 2, 0.25, 10, 2, 0.733, 10, 2, 0.75, 3, 2, 2.517, 3, 2, 2.983, 3, 2, 3, 6, 2, 3.217, 6, 2, 8.017, 6, 2, 8.033, 10, 2, 8.367, 10, 2, 8.667, 10]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.083, 0, 2, 2.517, 0, 1, 2.678, 0, 2.839, 0.328, 3, 0.772, 1, 3.072, 0.971, 3.145, 1, 3.217, 1, 2, 7.767, 1, 0, 8.017, 0, 2, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.083, 0, 2, 2.517, 0, 1, 2.678, 0, 2.839, 1.248, 3, 2.933, 1, 3.072, 3.688, 3.145, 3.8, 3.217, 3.8, 2, 7.767, 3.8, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.483, 1, 2, 2.517, 1, 2, 3, 1, 2, 3.217, 1, 2, 7.95, 1, 0, 8.083, 0, 2, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.417, 0, 0, 0.75, -1.86, 0, 1.083, 0, 2, 2.517, 0, 2, 3, 0, 2, 3.217, 0, 2, 7.767, 0, 2, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.417, 0, 2, 1.083, 0, 2, 2.517, 0, 1, 2.678, 0, 2.839, -2.34, 3, -5.495, 1, 3.072, -6.909, 3.145, -7.12, 3.217, -7.12, 2, 7.767, -7.12, 0, 8.117, 0, 2, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.45, 1, 2, 2.517, 1, 2, 3, 1, 2, 3.217, 1, 2, 8.1, 1, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.417, 0, 1, 0.489, 0, 0.561, -4.085, 0.633, -5.104, 1, 0.783, -7.22, 0.933, -7.5, 1.083, -7.5, 2, 2.517, -7.5, 1, 2.678, -7.5, 2.839, -4.34, 3, -0.09, 1, 3.072, 1.815, 3.145, 2.1, 3.217, 2.1, 2, 7.767, 2.1, 0, 8.117, 0, 2, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.417, 0, 2, 1.083, 0, 2, 2.517, 0, 1, 2.678, 0, 2.839, 0.006, 3, 0.025, 1, 3.072, 0.034, 3.145, 0.045, 3.217, 0.062, 1, 3.556, 0.142, 3.894, 0.192, 4.233, 0.192, 0, 5.567, 0.146, 0, 6.5, 0.169, 1, 6.922, 0.169, 7.345, 0.165, 7.767, 0.144, 1, 7.884, 0.138, 8, 0, 8.117, 0, 2, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamHandRDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.483, 0, 2, 0.5, 4, 2, 1.067, 4, 2, 1.083, 3, 2, 2.517, 3, 2, 2.983, 3, 2, 3, 6, 2, 3.217, 6, 2, 8.017, 6, 2, 8.033, 0, 2, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.417, 0, 0, 0.5, -1, 0, 1.083, 0, 2, 2.517, 0, 2, 3, 0, 2, 3.217, 0, 2, 7.767, 0, 2, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.417, 0, 2, 1.083, 0, 2, 2.517, 0, 1, 2.678, 0, 2.839, 0.328, 3, 0.772, 1, 3.072, 0.971, 3.145, 1, 3.217, 1, 2, 7.767, 1, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.417, 0, 2, 1.083, 0, 2, 2.517, 0, 1, 2.678, 0, 2.839, -2.399, 3, -5.634, 1, 3.072, -7.084, 3.145, -7.3, 3.217, -7.3, 2, 7.767, -7.3, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.467, 0, 0, 0.483, 1, 2, 2.517, 1, 2, 3, 1, 2, 3.217, 1, 2, 8.117, 1, 0, 8.133, 0, 2, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.467, 0, 0, 0.483, 1, 2, 2.517, 1, 2, 3, 1, 2, 3.217, 1, 2, 8.117, 1, 0, 8.133, 0, 2, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.483, -0.098, 1, 1.161, -0.098, 1.839, -0.075, 2.517, -0.031, 1, 2.678, -0.021, 2.839, -0.008, 3, -0.002, 1, 3.072, 0.001, 3.145, 0, 3.217, 0, 2, 7.767, 0, 2, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.483, 0.054, 1, 1.161, 0.054, 1.839, 0.04, 2.517, 0.016, 1, 2.678, 0.01, 2.839, 0.004, 3, 0.001, 1, 3.072, 0, 3.145, 0, 3.217, 0, 2, 7.767, 0, 2, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.483, 0.262, 1, 1.161, 0.262, 1.839, 0.198, 2.517, 0.08, 1, 2.678, 0.052, 2.839, 0.019, 3, 0.005, 1, 3.072, -0.001, 3.145, 0, 3.217, 0, 2, 7.767, 0, 2, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.517, 0, 2, 3, 0, 2, 3.217, 0, 2, 5.267, 0, 0, 5.667, -7, 2, 5.933, -7, 0, 6.367, 6, 2, 6.7, 6, 0, 7, -0.759, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.683, 4.282, 0, 0.933, -14.746, 0, 1.133, 7, 0, 1.367, -15.62, 0, 1.833, 0, 0, 2.317, -20, 1, 2.384, -20, 2.45, -18.234, 2.517, -12.963, 1, 2.617, -5.056, 2.717, 0, 2.817, 0, 2, 3, 0, 2, 3.217, 0, 2, 3.917, 0, 0, 4.8, 6, 1, 5.1, 6, 5.4, 1.18, 5.7, -6, 1, 5.95, -11.983, 6.2, -14, 6.45, -14, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.517, 0, 2, 2.6, 0, 1, 2.733, 0, 2.867, -7.299, 3, -16.492, 1, 3.072, -21.471, 3.145, -25.273, 3.217, -28.126, 1, 3.261, -29.882, 3.306, -30, 3.35, -30, 0, 4.733, 13.662, 0, 5.95, -22, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.517, 0, 2, 3, 0, 2, 3.033, 0, 1, 3.094, 0, 3.156, 0.019, 3.217, -0.1, 1, 3.467, -0.586, 3.717, -1, 3.967, -1, 0, 5.1, 0, 0, 6.467, -0.96, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -7.795, 2, 0.25, -7.795, 1, 0.75, -7.795, 1.25, -4.364, 1.75, 0, 1, 2.006, 2.23, 2.261, 3.573, 2.517, 5.098, 1, 2.678, 6.059, 2.839, 7.013, 3, 7.578, 1, 3.072, 7.831, 3.145, 7.801, 3.217, 7.801, 1, 3.228, 7.801, 3.239, 7.84, 3.25, 7.795, 1, 3.75, 5.785, 4.25, 3.463, 4.75, 0, 1, 5.25, -3.463, 5.75, -7.795, 6.25, -7.795, 0, 7.75, 0, 0, 8.367, -7.795, 2, 8.667, -7.795]}, {"Target": "Parameter", "Id": "ParamBodyStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 0.294, 0, 0.339, 4.872, 0.383, 6.338, 1, 0.478, 9.454, 0.572, 10, 0.667, 10, 0, 1.083, -3, 0, 1.217, 1.044, 1, 1.284, 1.044, 1.35, 0.677, 1.417, 0, 1, 1.545, -1.298, 1.672, -1.92, 1.8, -1.92, 0, 2.4, 2.409, 1, 2.439, 2.409, 2.478, 2.175, 2.517, 1.519, 1, 2.572, 0.582, 2.628, 0, 2.683, 0, 1, 2.789, 0, 2.894, 4.815, 3, 9.743, 1, 3.011, 10, 3.022, 10, 3.033, 10, 1, 3.094, 10, 3.156, 10, 3.217, 9.702, 1, 3.284, 9.352, 3.35, 8.761, 3.417, 7.281, 1, 3.778, -0.736, 4.139, -7, 4.5, -7, 0, 5.617, 0.25, 0, 6.8, -5.184, 1, 7.094, -5.184, 7.389, -3.092, 7.683, 0.094, 1, 7.733, 0.635, 7.783, 0.616, 7.833, 0.616, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.517, 4.859, 0, 0.883, -1.073, 0, 1.483, 2.445, 0, 2.017, -0.586, 1, 2.184, -0.586, 2.35, -0.015, 2.517, 0.599, 1, 2.522, 0.619, 2.528, 0.603, 2.533, 0.603, 1, 2.689, 0.603, 2.844, 0.437, 3, -0.129, 1, 3.072, -0.392, 3.145, -0.871, 3.217, -1.33, 1, 3.8, -5.041, 4.384, -7, 4.967, -7, 0, 6.467, 6, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamLegLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 1.006, 0, 1.761, 6.847, 2.517, 14.159, 1, 2.595, 14.912, 2.672, 14.515, 2.75, 14.515, 1, 2.833, 14.515, 2.917, 14.051, 3, 11.993, 1, 3.072, 10.209, 3.145, 7.322, 3.217, 4.713, 1, 3.478, -4.721, 3.739, -9.742, 4, -9.742, 1, 4.406, -9.742, 4.811, -5.146, 5.217, 0, 1, 6.056, 10.644, 6.894, 14.515, 7.733, 14.515, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamLegRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.55, 7.732, 1, 1.872, 7.732, 2.195, 6.295, 2.517, 2.827, 1, 2.678, 1.093, 2.839, -1.124, 3, -3.092, 1, 3.072, -3.974, 3.145, -4.993, 3.217, -5.723, 1, 3.495, -8.529, 3.772, -9.756, 4.05, -9.756, 1, 4.439, -9.756, 4.828, -5.355, 5.217, 0, 1, 5.65, 5.967, 6.084, 7.732, 6.517, 7.732, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.483, 0, 0, 0.5, 1, 2, 2.517, 1, 2, 3, 1, 2, 3.217, 1, 2, 8.117, 1, 0, 8.133, 0, 2, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Physics_AngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.517, 0, 2, 3, 0, 2, 3.217, 0, 2, 5.4, 0, 0, 5.733, -4.67, 0, 6.4, 4.657, 0, 7.067, -1.361, 0, 7.45, -0.18, 0, 7.617, -0.214, 0, 7.967, 0, 2, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Physics_AngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.55, 0, 0, 0.717, 1.778, 0, 0.967, -9.126, 0, 1.2, 4.025, 0, 1.45, -10.105, 0, 1.867, 0.93, 0, 2.35, -12.65, 1, 2.406, -12.65, 2.461, -12.136, 2.517, -9.276, 1, 2.634, -3.27, 2.75, 1.142, 2.867, 1.142, 1, 2.911, 1.142, 2.956, 1.039, 3, 0.693, 1, 3.072, 0.13, 3.145, -0.24, 3.217, -0.24, 0, 3.383, 0, 2, 4.417, 0, 0, 4.817, 3.701, 0, 6.433, -8.167, 0, 8.333, 0.269, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Physics_AngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.517, 0, 2, 3, 0, 2, 3.217, 0, 2, 5.267, 0, 0, 5.533, 3.731, 0, 5.867, -0.601, 0, 5.95, -0.46, 0, 6.217, -3.736, 0, 6.55, 0.7, 0, 6.717, 0.248, 0, 6.917, 2.624, 0, 7.2, -0.773, 0, 7.533, 0.028, 0, 7.85, -0.068, 0, 8.117, 0.056, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.683, -2.855, 0, 0.917, 8.86, 0, 1.133, -11.571, 0, 1.367, 10.611, 0, 1.717, -9.381, 0, 2.117, 12.422, 1, 2.25, 12.422, 2.384, -1.798, 2.517, -14.284, 1, 2.522, -14.804, 2.528, -14.409, 2.533, -14.409, 0, 2.95, 12.355, 1, 2.967, 12.355, 2.983, 12.495, 3, 11.55, 1, 3.072, 7.456, 3.145, 0.904, 3.217, -3.675, 1, 3.267, -6.845, 3.317, -7.859, 3.367, -7.859, 0, 3.783, 5.001, 0, 4.217, -3.337, 0, 4.65, 2.174, 0, 5.05, -0.674, 0, 5.483, 1.25, 0, 5.5, 1.24, 0, 5.567, 1.338, 0, 6.05, -1.035, 0, 6.467, 0.263, 0, 6.883, -0.596, 0, 7.317, 0.024, 0, 7.683, -0.214, 0, 8.317, 1.067, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.683, 2.855, 0, 0.883, -7.975, 0, 1.083, 22.788, 0, 1.317, -24.979, 0, 1.6, 22.837, 0, 1.95, -26.026, 0, 2.3, 30, 2, 2.367, 30, 1, 2.417, 30, 2.467, 21.304, 2.517, 2.371, 1, 2.572, -18.666, 2.628, -30, 2.683, -30, 2, 2.817, -30, 1, 2.878, -30, 2.939, -19.643, 3, -0.222, 1, 3.056, 17.433, 3.111, 25.645, 3.167, 25.645, 1, 3.184, 25.645, 3.2, 26.746, 3.217, 23.821, 1, 3.334, 3.344, 3.45, -16.812, 3.567, -16.812, 0, 3.983, 10.7, 0, 4.417, -7.751, 0, 4.85, 3.984, 0, 5.233, -1.207, 0, 5.5, 1.994, 0, 5.517, 1.992, 0, 5.85, 3.736, 0, 6.283, -1.321, 0, 6.65, 0.754, 0, 7.1, -1.642, 0, 7.517, -0.598, 0, 7.867, -1.131, 0, 8.133, -0.587, 0, 8.233, -1.806, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_L", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.683, 0.999, 0, 0.933, -3.442, 0, 1.133, 1.633, 0, 1.35, -3.38, 0, 1.567, 5.975, 0, 1.933, -5.658, 0, 2.367, 6.417, 1, 2.417, 6.417, 2.467, 5.811, 2.517, 2.831, 1, 2.617, -3.129, 2.717, -7.439, 2.817, -7.439, 1, 2.878, -7.439, 2.939, -5.134, 3, -2.636, 1, 3.072, 0.317, 3.145, 3.08, 3.217, 4.879, 1, 3.228, 5.156, 3.239, 5.03, 3.25, 5.03, 0, 3.7, -2.615, 0, 4.15, 1.724, 0, 4.167, 1.721, 0, 4.217, 1.864, 0, 4.567, -1.763, 0, 5.1, 1.128, 0, 5.3, 0.424, 0, 5.383, 0.83, 0, 5.583, 0.173, 0, 5.867, 1.223, 0, 6.417, -2.031, 0, 6.767, 1.144, 0, 6.967, 0.755, 0, 7.017, 0.789, 0, 7.417, -1.298, 0, 7.85, 0.368, 0, 8.35, -0.675, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_R", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.683, -0.999, 0, 0.933, 3.442, 0, 1.133, -1.633, 0, 1.35, 3.38, 0, 1.567, -5.975, 0, 1.933, 5.658, 0, 2.367, -6.417, 1, 2.417, -6.417, 2.467, -5.811, 2.517, -2.831, 1, 2.617, 3.129, 2.717, 7.439, 2.817, 7.439, 1, 2.878, 7.439, 2.939, 5.134, 3, 2.636, 1, 3.072, -0.317, 3.145, -3.08, 3.217, -4.879, 1, 3.228, -5.156, 3.239, -5.03, 3.25, -5.03, 0, 3.7, 2.615, 0, 4.15, -1.724, 0, 4.167, -1.721, 0, 4.217, -1.864, 0, 4.567, 1.763, 0, 5.1, -1.128, 0, 5.317, -0.402, 0, 5.333, -0.415, 0, 6.217, 3.492, 0, 6.483, -5.74, 0, 6.933, 3.011, 0, 7.417, -1.967, 0, 7.85, 1.604, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.683, -5.71, 0, 0.917, 18.093, 0, 1.117, -19.091, 0, 1.333, 19.93, 0, 1.65, -12.784, 0, 2.083, 11.859, 1, 2.228, 11.859, 2.372, 0.389, 2.517, -10.462, 1, 2.528, -11.297, 2.539, -10.79, 2.55, -10.79, 0, 2.95, 3.944, 1, 2.967, 3.944, 2.983, 3.954, 3, 3.629, 1, 3.072, 2.22, 3.145, 0.128, 3.217, -1.219, 1, 3.245, -1.737, 3.272, -1.764, 3.3, -1.764, 0, 3.617, 1.852, 0, 3.933, -1.953, 0, 4.233, 0.598, 0, 4.533, -1.189, 0, 5.533, 16.825, 0, 5.867, -4.497, 0, 5.95, -3.224, 0, 6.217, -23.533, 0, 6.533, 10.446, 0, 6.733, 1.95, 0, 6.917, 14.706, 0, 7.217, -7.866, 0, 7.583, 0.289, 0, 7.9, -1.363, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.683, 5.71, 0, 0.9, -17.146, 0, 1.067, 29.569, 0, 1.267, -29.034, 0, 1.483, 23.532, 0, 1.767, -12.997, 0, 2.15, 4.583, 1, 2.272, 4.583, 2.395, 2.007, 2.517, -1.588, 1, 2.578, -3.385, 2.639, -3.75, 2.7, -3.75, 0, 2.817, -2.909, 0, 2.833, -2.925, 1, 2.889, -2.925, 2.944, -0.776, 3, 1.984, 1, 3.022, 3.088, 3.045, 3.193, 3.067, 3.193, 0, 3.15, 2.052, 0, 3.217, 2.428, 0, 3.45, -1.713, 0, 3.467, -1.651, 0, 3.517, -3.694, 0, 3.75, 2.204, 0, 3.783, 2.044, 0, 3.833, 3.999, 0, 4.05, -2.107, 0, 4.083, -1.979, 0, 4.133, -3.723, 0, 4.333, 2.529, 0, 4.6, -1.054, 0, 4.95, 0.065, 0, 5.433, -5.864, 0, 5.683, 10.626, 0, 5.933, -6.535, 0, 6.133, 9.507, 0, 6.4, -17.358, 0, 6.65, 11.804, 0, 6.867, -11.421, 0, 7.083, 13.655, 0, 7.317, -8.125, 0, 7.6, 3.082, 0, 7.883, -0.893, 0, 8.183, 0.026, 0, 8.3, -2.041, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.683, 5.71, 0, 0.933, -19.669, 0, 1.133, 9.334, 0, 1.367, -20.835, 0, 1.833, 0, 0, 2.267, -25.478, 1, 2.35, -25.478, 2.434, -14.156, 2.517, 2.736, 1, 2.567, 12.871, 2.617, 15.795, 2.667, 15.795, 1, 2.778, 15.795, 2.889, 8.141, 3, -0.57, 1, 3.028, -2.748, 3.055, -2.474, 3.083, -2.474, 1, 3.128, -2.474, 3.172, -2.062, 3.217, -1.237, 1, 3.261, -0.412, 3.306, 0, 3.35, 0, 2, 3.917, 0, 0, 4.8, 8.001, 0, 5.283, 3.138, 0, 5.583, 26.399, 0, 6.15, -30, 2, 6.283, -30, 0, 6.617, 11.053, 0, 6.717, 9.257, 0, 6.933, 22.585, 0, 7.283, -5.178, 0, 7.733, 2.078, 0, 8.183, -0.281, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.683, -5.71, 0, 0.933, 19.669, 0, 1.133, -9.334, 0, 1.367, 20.835, 0, 1.833, 0, 0, 2.25, 25.304, 0, 2.5, -30, 1, 2.506, -30, 2.511, -30, 2.517, -29.663, 1, 2.622, -8.109, 2.728, 16.79, 2.833, 16.79, 1, 2.889, 16.79, 2.944, 10.821, 3, 1.223, 1, 3.039, -5.496, 3.078, -7.9, 3.117, -7.9, 1, 3.15, -7.9, 3.184, -7.671, 3.217, -5.408, 1, 3.3, 0.25, 3.384, 4.64, 3.467, 4.64, 0, 3.75, -3.641, 0, 3.917, 0, 0, 4.8, -8.001, 0, 5.283, -3.138, 0, 5.55, -24.927, 0, 5.783, 27.593, 0, 6.017, -2.481, 0, 6.183, 3.277, 0, 6.433, -19.872, 0, 6.717, 10.505, 0, 6.917, -7.091, 0, 7.117, 12.894, 0, 7.433, -6.584, 0, 7.75, 2.189, 0, 8.117, -0.286, 0, 8.15, -0.271, 0, 8.167, -0.935, 0, 8.183, 0.281, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Jewelry_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.617, -0.425, 0, 0.9, 1.507, 0, 1.117, -2.341, 0, 1.367, 1.805, 0, 1.733, -1.532, 0, 2.117, 1.914, 1, 2.25, 1.914, 2.384, -0.166, 2.517, -2.27, 1, 2.522, -2.358, 2.528, -2.289, 2.533, -2.289, 0, 2.95, 1.976, 1, 2.967, 1.976, 2.983, 2.001, 3, 1.846, 1, 3.072, 1.173, 3.145, 0.106, 3.217, -0.628, 1, 3.267, -1.136, 3.317, -1.287, 3.367, -1.287, 0, 3.783, 0.817, 0, 4.217, -0.621, 0, 4.65, 0.387, 0, 5.067, -0.127, 0, 5.517, 0.546, 0, 5.917, -0.476, 0, 5.967, -0.461, 0, 6.117, -0.524, 0, 6.517, 0.673, 0, 6.833, -0.085, 0, 6.9, -0.079, 0, 7.133, -0.362, 0, 7.567, 0.18, 0, 7.967, -0.124, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Jewelry_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.583, 0.412, 0, 0.817, -1.504, 0, 1.067, 4.129, 0, 1.317, -4.702, 0, 1.6, 4.045, 0, 1.933, -4.041, 0, 2.333, 4.728, 1, 2.394, 4.728, 2.456, 3.566, 2.517, 0.694, 1, 2.595, -2.961, 2.672, -5.103, 2.75, -5.103, 1, 2.833, -5.103, 2.917, -2.83, 3, 0.743, 1, 3.056, 3.125, 3.111, 3.926, 3.167, 3.926, 1, 3.184, 3.926, 3.2, 4.112, 3.217, 3.663, 1, 3.339, 0.374, 3.461, -2.685, 3.583, -2.685, 0, 4, 1.736, 0, 4.417, -1.375, 0, 4.85, 0.711, 0, 5.25, -0.228, 0, 5.733, 1.604, 0, 6.383, -1.385, 0, 6.733, 1.338, 0, 7.383, -0.745, 0, 7.767, 0.262, 0, 8.167, -0.372, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Jewelry_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.583, 0, 0, 0.733, 0.646, 0, 0.967, -2.281, 0, 1.217, 4.49, 0, 1.467, -5.28, 0, 1.767, 4.961, 0, 2.083, -4.765, 0, 2.467, 4.503, 1, 2.484, 4.503, 2.5, 4.74, 2.517, 4.162, 1, 2.639, -0.078, 2.761, -4.062, 2.883, -4.062, 1, 2.922, -4.062, 2.961, -3.602, 3, -2.63, 1, 3.072, -0.824, 3.145, 1.37, 3.217, 2.842, 1, 3.245, 3.408, 3.272, 3.411, 3.3, 3.411, 0, 3.7, -2.494, 0, 4.1, 1.661, 0, 4.533, -1.101, 0, 4.95, 0.635, 0, 5.483, -0.498, 0, 5.883, 1.058, 0, 6.217, -0.31, 0, 6.317, -0.267, 0, 6.55, -0.883, 0, 6.867, 1.063, 0, 7.133, -0.231, 2, 7.15, -0.231, 0, 7.317, 0.013, 0, 7.567, -0.38, 0, 7.917, 0.388, 0, 8.317, -0.394, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.617, -1.276, 0, 0.9, 4.521, 0, 1.117, -7.023, 0, 1.367, 5.415, 0, 1.733, -4.596, 0, 2.117, 5.742, 1, 2.25, 5.742, 2.384, -0.66, 2.517, -6.809, 1, 2.522, -7.065, 2.528, -6.869, 2.533, -6.869, 0, 2.95, 5.928, 1, 2.967, 5.928, 2.983, 6.002, 3, 5.539, 1, 3.072, 3.535, 3.145, 0.349, 3.217, -1.861, 1, 3.267, -3.391, 3.317, -3.861, 3.367, -3.861, 0, 3.783, 2.451, 0, 4.217, -1.864, 0, 4.65, 1.161, 0, 5.067, -0.382, 0, 5.517, 1.639, 0, 5.917, -1.427, 0, 5.967, -1.382, 0, 6.117, -1.571, 0, 6.517, 2.018, 0, 6.833, -0.255, 0, 6.9, -0.238, 0, 7.133, -1.085, 0, 7.567, 0.539, 0, 7.967, -0.373, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.583, 1.235, 0, 0.817, -4.513, 0, 1.067, 12.387, 0, 1.317, -14.107, 0, 1.6, 12.136, 0, 1.933, -12.122, 0, 2.333, 14.184, 1, 2.394, 14.184, 2.456, 10.697, 2.517, 2.082, 1, 2.595, -8.883, 2.672, -15.309, 2.75, -15.309, 1, 2.833, -15.309, 2.917, -8.483, 3, 2.229, 1, 3.056, 9.37, 3.111, 11.779, 3.167, 11.779, 1, 3.184, 11.779, 3.2, 12.3, 3.217, 10.989, 1, 3.339, 1.376, 3.461, -8.054, 3.583, -8.054, 0, 4, 5.209, 0, 4.417, -4.125, 0, 4.85, 2.133, 0, 5.25, -0.685, 0, 5.733, 4.812, 0, 6.383, -4.155, 0, 6.733, 4.014, 0, 7.383, -2.234, 0, 7.767, 0.786, 0, 8.167, -1.117, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.583, 0, 0, 0.733, 1.938, 0, 0.967, -6.842, 0, 1.217, 13.471, 0, 1.467, -15.841, 0, 1.767, 14.884, 0, 2.083, -14.295, 0, 2.467, 13.509, 1, 2.484, 13.509, 2.5, 14.176, 2.517, 12.486, 1, 2.639, 0.091, 2.761, -12.185, 2.883, -12.185, 1, 2.922, -12.185, 2.961, -10.805, 3, -7.889, 1, 3.072, -2.473, 3.145, 4.133, 3.217, 8.518, 1, 3.245, 10.205, 3.272, 10.233, 3.3, 10.233, 0, 3.7, -7.484, 0, 4.1, 4.984, 0, 4.533, -3.301, 0, 4.95, 1.906, 0, 5.483, -1.495, 0, 5.883, 3.173, 0, 6.217, -0.929, 0, 6.317, -0.801, 0, 6.55, -2.649, 0, 6.867, 3.189, 0, 7.133, -0.694, 0, 7.317, 0.038, 0, 7.567, -1.14, 0, 7.917, 1.164, 0, 8.317, -1.181, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.7, 0, 0, 0.85, 1.839, 0, 1.067, -6.74, 0, 1.317, 13.067, 0, 1.6, -17.544, 0, 1.883, 19.396, 0, 2.2, -19.872, 1, 2.306, -19.872, 2.411, -0.622, 2.517, 18.075, 1, 2.528, 20.043, 2.539, 19.076, 2.55, 19.076, 0, 2.95, -16.554, 1, 2.967, -16.554, 2.983, -16.757, 3, -15.341, 1, 3.072, -9.204, 3.145, 0.668, 3.217, 7.574, 1, 3.267, 12.355, 3.317, 13.902, 3.367, 13.902, 0, 3.783, -10.781, 0, 4.183, 7.503, 0, 4.6, -4.887, 0, 5.017, 2.926, 0, 5.467, -2.014, 0, 5.983, 3.805, 0, 6.35, -1.997, 0, 6.483, -1.587, 0, 6.65, -2.302, 0, 6.983, 3.922, 0, 7.283, -1.631, 0, 7.517, -0.165, 0, 7.717, -0.945, 0, 8.033, 1.525, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.817, 0, 0, 0.983, 1.869, 0, 1.183, -6.856, 0, 1.433, 14.22, 0, 1.717, -20.4, 0, 2, 23.915, 0, 2.3, -25.789, 1, 2.372, -25.789, 2.445, -10.594, 2.517, 11.055, 1, 2.556, 22.712, 2.594, 25.47, 2.633, 25.47, 1, 2.755, 25.47, 2.878, 1.432, 3, -21.097, 1, 3.011, -23.145, 3.022, -22.024, 3.033, -22.024, 1, 3.094, -22.024, 3.156, -17.278, 3.217, -5.549, 1, 3.295, 9.379, 3.372, 18.126, 3.45, 18.126, 0, 3.867, -14.733, 0, 4.267, 10.921, 0, 4.683, -7.274, 0, 5.1, 4.445, 0, 5.533, -2.968, 0, 6.1, 4.302, 0, 6.45, -3.281, 0, 7.1, 4.578, 0, 7.417, -2.865, 0, 7.683, 0.179, 0, 7.867, -0.545, 0, 8.15, 1.889, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.967, 0, 0, 1.1, 0.994, 0, 1.3, -5.289, 0, 1.55, 14.29, 0, 1.817, -22.993, 0, 2.1, 28.757, 0, 2.367, -30, 2, 2.417, -30, 1, 2.45, -30, 2.484, -26.601, 2.517, -12.806, 1, 2.578, 12.484, 2.639, 30, 2.7, 30, 2, 2.75, 30, 1, 2.833, 30, 2.917, 9.763, 3, -16.451, 1, 3.033, -26.937, 3.067, -27.974, 3.1, -27.974, 1, 3.139, -27.974, 3.178, -27.207, 3.217, -18.295, 1, 3.317, 4.622, 3.417, 22.535, 3.517, 22.535, 0, 3.933, -18.938, 0, 4.35, 15.068, 0, 4.75, -10.602, 0, 5.167, 6.675, 0, 5.6, -4.42, 0, 6.183, 4.678, 0, 6.567, -4.723, 0, 7.2, 5.067, 0, 7.533, -4.311, 0, 7.833, 1.102, 0, 8.05, -0.328, 0, 8.283, 1.93, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.2, 0, 0, 1.383, -4.769, 0, 1.65, 14.786, 0, 1.917, -25.325, 0, 2.167, 30, 2, 2.233, 30, 0, 2.433, -30, 2, 2.517, -30, 2, 2.533, -30, 0, 2.75, 30, 2, 2.833, 30, 1, 2.889, 30, 2.944, 17.158, 3, -4.989, 1, 3.044, -22.707, 3.089, -30, 3.133, -30, 2, 3.2, -30, 1, 3.206, -30, 3.211, -30, 3.217, -29.689, 1, 3.339, -3.806, 3.461, 26.597, 3.583, 26.597, 0, 4.017, -22.809, 0, 4.433, 19.423, 0, 4.833, -14.757, 0, 5.25, 9.837, 0, 5.667, -6.559, 0, 6.25, 5.01, 0, 6.667, -6.225, 0, 7.3, 5.306, 0, 7.65, -5.913, 0, 7.967, 2.569, 0, 8.217, -0.208, 0, 8.367, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.417, 1, 0, 3.25, 0, 0, 4.417, 1, 0, 6.25, 0, 0, 7.417, 1, 1, 7.834, 1, 8.25, 0.535, 8.667, 0.239]}, {"Target": "Parameter", "Id": "fenshangxuanzhuang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 3.056, 1.123, 5.861, 2.247, 8.667, 3.37]}, {"Target": "Parameter", "Id": "Mail_Transparency6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamSad2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 8.667, 1]}, {"Target": "Parameter", "Id": "ParamCRY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.2, 0, 8.667, -5.2]}, {"Target": "Parameter", "Id": "Position_Coordinates", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 8.667, 1]}, {"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Man_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 8.667, 1]}, {"Target": "Parameter", "Id": "Man_TMD2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "BG_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 8.667, 1]}, {"Target": "Parameter", "Id": "FG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "FG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "BG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "BG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "BG_White4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "BG_White5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "BG_White2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "BG_White3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "All_Size2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "All_Size3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "All_Size4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "tuerzuo3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "faqiehuan", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "faqiehuan2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "faqiehuan4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "faqiehuan3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "tuerzuo4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Mail_Change", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "dafengshangzhuanchang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "tongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamTongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "EyeFrame_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamBrowY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamSigh", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamDarken", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamCry", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_B", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_C", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "MB_Unidirectional_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "jing<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "huqi1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "huqi2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamShoulderLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamArmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 8.667, 1]}, {"Target": "Parameter", "Id": "ParamForearmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamArmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamArmL_ForearmL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmL_HandL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamHandLSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "xiongjiya", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "xiongjiya5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "xiongjiya3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "xiongjiya4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "xiongjiya2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamShoulderRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamArmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 8.667, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamArmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamArmR_ForearmR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearm<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 8.667, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_HandR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamHandRSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 8.667, 0.6]}, {"Target": "Parameter", "Id": "Change_idle_physics23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param103", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "taiqiugundong", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -22.5, 0, 8.667, -22.5]}, {"Target": "Parameter", "Id": "Change_idle_physics32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamLegL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 8.667, 1]}, {"Target": "Parameter", "Id": "ParamLegLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamCrusLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamCrusLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>oodLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamFoodLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamFoodLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamLegR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 8.667, 1]}, {"Target": "Parameter", "Id": "ParamLegRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamCrusRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamCrusRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamCrus<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>ood<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamFoodRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamFoodRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Physics_Provisional", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "physics_Add_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "physics_Add_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Physics_Body_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Physics_Body_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Physics_breast", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamBreath2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "touch_drag1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "touch_drag2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "touch_drag3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "touch_drag4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "touch_drag5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "touch_drag6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "touch_drag7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "touch_drag8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "touch_drag9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>pian<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh654", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh655", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh657", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh656", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh658", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh659", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh660", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}], "UserData": [{"Time": 0.25, "Value": ""}, {"Time": 8.167, "Value": ""}]}