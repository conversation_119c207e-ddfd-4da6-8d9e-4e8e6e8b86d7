{"Version": 3, "Meta": {"Duration": 6.917, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 303, "TotalSegmentCount": 1231, "TotalPointCount": 1345, "UserDataCount": 2, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.4, 1, 0, 0.567, -1, 0, 0.8, 0.4, 0, 1.217, -0.6, 0, 1.45, 0.6, 0, 1.983, -1, 0, 3, -0.5, 0, 3.1, -0.8, 2, 3.4, -0.8, 2, 3.867, -0.8, 0, 4.217, 0.5, 0, 4.417, 0, 0, 4.6, 0.6, 0, 4.833, -0.6, 0, 5.167, 0, 0, 5.45, -1, 0, 5.617, 0.7, 0, 5.817, -0.8, 0, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY_SD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.367, 0, 0, 0.483, 1, 0, 0.6, 0, 0, 0.783, 1, 0, 0.9, 0, 0, 1.133, 1, 0, 1.3, 0, 0, 1.483, 1, 0, 1.6, 0, 0, 1.783, 1, 0, 1.9, 0, 0, 2.083, 1, 0, 2.217, 0, 0, 2.367, 1, 0, 2.533, 0, 0, 2.65, 1, 0, 2.917, 0, 2, 3, 0, 0, 3.1, 0.6, 1, 3.2, 0.6, 3.3, 0.61, 3.4, 0.5, 1, 3.606, 0.273, 3.811, 0, 4.017, 0, 0, 4.133, 1, 0, 4.25, 0, 0, 4.433, 1, 0, 4.683, 0, 0, 4.817, 1, 0, 4.95, 0, 0, 5.133, 1, 0, 5.25, 0, 0, 5.433, 1, 0, 5.55, 0, 0, 5.733, 1, 0, 5.867, 0, 2, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.85, 0, 0, 3.1, 30, 2, 3.4, 30, 0, 3.667, 0, 2, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 0, 0.767, 0, 2, 1.917, 0, 2, 3.033, 0, 0, 3.383, 1, 2, 5.933, 1, 0, 6.067, 0, 0, 6.25, 1.3, 0, 6.333, 1, 2, 6.633, 1, 2, 6.917, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 0, 0.767, 0, 2, 1.917, 0, 2, 3.033, 0, 0, 3.383, 1, 2, 5.933, 1, 0, 6.067, 0, 0, 6.25, 1.3, 0, 6.333, 1, 2, 6.633, 1, 2, 6.917, 1]}, {"Target": "Parameter", "Id": "EyeFrame_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.767, -0.2, 2, 1.917, -0.2, 0, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.767, -0.5, 2, 1.917, -0.5, 0, 2.65, 0, 0, 3.383, -1, 2, 5.933, -1, 0, 6.333, 0, 2, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.767, -0.2, 2, 1.917, -0.2, 1, 2.161, -0.2, 2.406, -0.222, 2.65, 0, 1, 2.894, 0.222, 3.139, 1, 3.383, 1, 2, 5.933, 1, 0, 6.333, 0, 2, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamBrowForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.767, 0.2, 2, 1.917, 0.2, 0, 2.65, 0, 0, 3.383, 1, 2, 5.933, 1, 0, 6.333, 0, 2, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamEyeLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.517, 0.694, 0, 0.867, -0.348, 0, 1.183, 0.096, 0, 1.483, -0.027, 0, 1.8, 0.007, 0, 2.1, -0.002, 2, 2.117, -0.002, 0, 2.383, 0.001, 2, 2.45, 0.001, 0, 2.467, 0, 2, 2.483, 0, 2, 2.517, 0, 2, 2.533, 0, 2, 2.567, 0, 2, 2.583, 0, 2, 2.6, 0, 2, 2.617, 0, 2, 2.633, 0, 2, 2.683, 0, 2, 2.7, 0, 2, 2.767, 0, 2, 2.783, 0, 2, 2.867, 0, 2, 2.883, 0, 2, 3.033, 0, 0, 3.25, -0.992, 0, 3.533, 0.453, 0, 3.85, -0.124, 0, 4.15, 0.035, 0, 4.467, -0.01, 0, 4.767, 0.003, 2, 4.783, 0.003, 0, 5.05, -0.001, 2, 5.067, -0.001, 2, 5.083, -0.001, 2, 5.1, -0.001, 2, 5.117, -0.001, 2, 5.133, -0.001, 0, 5.167, 0, 2, 5.183, 0, 2, 5.267, 0, 2, 5.283, 0, 2, 5.3, 0, 2, 5.317, 0, 2, 5.333, 0, 2, 5.467, 0, 2, 5.483, 0, 2, 5.533, 0, 2, 5.55, 0, 2, 5.65, 0, 2, 5.667, 0, 2, 5.75, 0, 2, 5.767, 0, 2, 5.933, 0, 0, 6, 1, 2, 6.15, 1, 0, 6.167, -1, 2, 6.3, -1, 0, 6.6, 0.244, 0, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamEyeRsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.517, 0.694, 0, 0.867, -0.348, 0, 1.183, 0.096, 0, 1.483, -0.027, 0, 1.8, 0.007, 0, 2.1, -0.002, 2, 2.117, -0.002, 0, 2.383, 0.001, 2, 2.45, 0.001, 0, 2.467, 0, 2, 2.483, 0, 2, 2.517, 0, 2, 2.533, 0, 2, 2.567, 0, 2, 2.583, 0, 2, 2.6, 0, 2, 2.617, 0, 2, 2.633, 0, 2, 2.683, 0, 2, 2.7, 0, 2, 2.767, 0, 2, 2.783, 0, 2, 2.867, 0, 2, 2.883, 0, 2, 3.033, 0, 0, 3.25, -0.992, 0, 3.533, 0.453, 0, 3.85, -0.124, 0, 4.15, 0.035, 0, 4.467, -0.01, 0, 4.767, 0.003, 2, 4.783, 0.003, 0, 5.05, -0.001, 2, 5.067, -0.001, 2, 5.083, -0.001, 2, 5.1, -0.001, 2, 5.117, -0.001, 2, 5.133, -0.001, 0, 5.167, 0, 2, 5.183, 0, 2, 5.267, 0, 2, 5.283, 0, 2, 5.3, 0, 2, 5.317, 0, 2, 5.333, 0, 2, 5.467, 0, 2, 5.483, 0, 2, 5.533, 0, 2, 5.55, 0, 2, 5.65, 0, 2, 5.667, 0, 2, 5.75, 0, 2, 5.767, 0, 2, 5.933, 0, 0, 6, 1, 2, 6.15, 1, 0, 6.167, -1, 2, 6.3, -1, 0, 6.6, 0.244, 0, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamEyeLGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.417, -0.154, 0, 0.75, 0.25, 0, 1.033, -0.217, 0, 1.333, 0.104, 0, 1.633, -0.042, 0, 1.95, 0.015, 0, 2.25, -0.005, 2, 2.267, -0.005, 0, 2.55, 0.002, 2, 2.567, 0.002, 0, 2.817, 0, 2, 2.833, 0, 0, 2.85, -0.001, 2, 2.9, -0.001, 0, 2.917, 0, 2, 2.933, 0, 2, 2.967, 0, 2, 2.983, 0, 2, 3.017, 0, 2, 3.033, 0, 0, 3.167, 0.225, 0, 3.417, -0.503, 0, 3.7, 0.306, 0, 4, -0.138, 0, 4.317, 0.055, 0, 4.617, -0.02, 0, 4.917, 0.007, 2, 4.933, 0.007, 0, 5.217, -0.002, 2, 5.25, -0.002, 0, 5.5, 0.001, 2, 5.583, 0.001, 2, 5.6, 0.001, 2, 5.617, 0.001, 0, 5.7, 0, 2, 5.717, 0, 2, 5.75, 0, 2, 5.767, 0, 2, 5.783, 0, 2, 5.933, 0, 0, 6, -0.305, 0, 6.05, -0.2, 0, 6.15, -1, 2, 6.167, -1, 0, 6.183, 1, 2, 6.217, 1, 0, 6.25, 0.832, 2, 6.267, 0.832, 0, 6.3, 0.864, 0, 6.55, -0.314, 0, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamEyeRGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.417, -0.154, 0, 0.75, 0.25, 0, 1.033, -0.217, 0, 1.333, 0.104, 0, 1.633, -0.042, 0, 1.95, 0.015, 0, 2.25, -0.005, 2, 2.267, -0.005, 0, 2.55, 0.002, 2, 2.567, 0.002, 0, 2.817, 0, 2, 2.833, 0, 0, 2.85, -0.001, 2, 2.9, -0.001, 0, 2.917, 0, 2, 2.933, 0, 2, 2.967, 0, 2, 2.983, 0, 2, 3.017, 0, 2, 3.033, 0, 0, 3.167, 0.225, 0, 3.417, -0.503, 0, 3.7, 0.306, 0, 4, -0.138, 0, 4.317, 0.055, 0, 4.617, -0.02, 0, 4.917, 0.007, 2, 4.933, 0.007, 0, 5.217, -0.002, 2, 5.25, -0.002, 0, 5.5, 0.001, 2, 5.583, 0.001, 2, 5.6, 0.001, 2, 5.617, 0.001, 0, 5.7, 0, 2, 5.717, 0, 2, 5.75, 0, 2, 5.767, 0, 2, 5.783, 0, 2, 5.933, 0, 0, 6, -0.305, 0, 6.05, -0.2, 0, 6.15, -1, 2, 6.167, -1, 0, 6.183, 1, 2, 6.217, 1, 0, 6.25, 0.832, 2, 6.267, 0.832, 0, 6.3, 0.864, 0, 6.55, -0.314, 0, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 3.083, 0, 0, 3.5, -5, 2, 5.917, -5, 0, 6.2, 0, 2, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamDarken", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 3.15, 0, 0, 3.5, 1, 2, 5.933, 1, 0, 6.133, 0, 2, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamShoulderLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.8, 4, 1, 1.267, 4, 1.733, -0.25, 2.2, -2, 1, 2.522, -3.208, 2.845, -3, 3.167, -3, 0, 4.467, 2, 2, 5.967, 2, 0, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 0.428, 0, 0.605, 0.792, 0.783, 2.489, 1, 0.905, 3.656, 1.028, 4.3, 1.15, 4.3, 2, 2.35, 4.3, 2, 3.75, 4.3, 1, 3.839, 4.3, 3.928, 2.877, 4.017, 2.538, 1, 4.15, 2.029, 4.284, 2.02, 4.417, 2.02, 2, 5.9, 2.02, 0, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.25, 0, 0, 2.35, -7, 2, 3.717, -7, 0, 4.667, 0, 2, 5.9, 0, 0, 6.283, -3, 0, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 30, 2, 0.25, 30, 1, 0.428, 30, 0.605, 26.858, 0.783, 18.135, 1, 0.905, 12.138, 1.028, 7.5, 1.15, 7.5, 0, 1.533, 10.189, 0, 1.883, 7.5, 0, 2.217, 9.936, 0, 2.667, 6.492, 0, 3.033, 10.543, 1, 3.272, 10.543, 3.511, 9.904, 3.75, 7.5, 1, 3.839, 6.605, 3.928, 3.413, 4.017, 2.102, 1, 4.15, 0.135, 4.284, -0.4, 4.417, -0.4, 1, 4.911, -0.4, 5.406, -0.399, 5.9, -0.22, 1, 6.144, -0.132, 6.389, 30, 6.633, 30, 2, 6.917, 30]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.25, 0, 2, 2.35, 0, 2, 3.75, 0, 2, 4.417, 0, 2, 5.9, 0, 0, 6.283, -30, 0, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1, 2, 0.25, -1, 2, 1.25, -1, 2, 2.35, -1, 2, 3.75, -1, 2, 3.983, -1, 2, 4, 1, 2, 4.417, 1, 2, 5.9, 1, 2, 6.2, 1, 2, 6.217, -1, 2, 6.633, -1, 2, 6.917, -1]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 0.356, 0, 0.461, -0.455, 0.567, -0.467, 1, 0.772, -0.49, 0.978, -0.49, 1.183, -0.49, 0, 1.417, -0.2, 0, 1.75, -0.56, 0, 2.083, -0.13, 0, 2.35, -0.585, 1, 2.478, -0.585, 2.605, -0.464, 2.733, -0.165, 1, 2.789, -0.035, 2.844, 0.067, 2.9, 0.067, 1, 3.011, 0.067, 3.122, -0.246, 3.233, -0.312, 1, 3.405, -0.414, 3.578, -0.416, 3.75, -0.416, 1, 3.833, -0.416, 3.917, -0.375, 4, -0.308, 1, 4.183, -0.161, 4.367, 0.117, 4.55, 0.118, 1, 5, 0.12, 5.45, 0.12, 5.9, 0.12, 0, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamHandLDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10, 2, 0.25, 10, 2, 0.917, 10, 0, 0.933, 5.5, 2, 3.983, 5.5, 2, 4, 0, 2, 6.133, 0, 1, 6.139, 0, 6.144, 10, 6.15, 10, 2, 6.633, 10, 2, 6.917, 10]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.817, -1, 1, 1.028, -1, 1.239, -0.804, 1.45, 0, 1, 1.544, 0.36, 1.639, 1, 1.733, 1, 0, 2.15, -0.2, 0, 2.667, 1, 0, 2.967, 0, 1, 3.084, 0, 3.2, 0.566, 3.317, 0.776, 1, 3.445, 1, 3.572, 1, 3.7, 1, 0, 3.983, -1, 0, 4.417, 1, 2, 5.9, 1, 0, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.817, -1, 1, 1.028, -1, 1.239, -0.804, 1.45, 0, 1, 1.544, 0.36, 1.639, 1, 1.733, 1, 0, 2.15, -0.5, 0, 2.667, 1, 0, 2.967, 0, 1, 3.084, 0, 3.2, 0.566, 3.317, 0.776, 1, 3.445, 1, 3.572, 1, 3.7, 1, 0, 3.983, -1, 2, 4.417, -1, 2, 5.9, -1, 0, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.95, 2.212, 0, 1.45, -8, 0, 1.867, 1.735, 0, 2.2, -6.848, 0, 2.617, 7.906, 0, 3.217, -13, 1, 3.422, -13, 3.628, -13.131, 3.833, -10.982, 1, 3.994, -9.297, 4.156, 1, 4.317, 1, 0, 6.25, 0, 2, 6.517, 0, 2, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -2.728, 0, 0.617, 5.594, 0, 1.133, -8, 2, 1.8, -8, 0, 2.433, -6, 0, 2.817, -14, 0, 3.25, 0, 0, 4.2, -5, 0, 5.767, 0, 2, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.6, -14, 0, 2.667, -3.15, 0, 3.233, -14.7, 1, 3.322, -14.7, 3.411, -14.313, 3.5, -11.273, 1, 3.783, -1.582, 4.067, 5, 4.35, 5, 0, 6.25, 0, 2, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.533, -10, 1, 0.716, -10, 0.9, 4.299, 1.083, 7.359, 1, 1.178, 8.935, 1.272, 8.37, 1.367, 8.37, 0, 1.733, -7.56, 0, 2.283, 8.798, 0, 2.733, -7.255, 0, 3.317, 7.159, 0, 3.967, -1, 0, 5.1, 6.343, 0, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -7.795, 2, 0.25, -7.795, 1, 0.75, -7.795, 1.25, -5.197, 1.75, 0, 1, 2.25, 5.197, 2.75, 7.795, 3.25, 7.795, 1, 3.75, 7.795, 4.25, 5.197, 4.75, 0, 1, 5.25, -5.197, 5.75, -7.795, 6.25, -7.795, 2, 6.633, -7.795, 2, 6.917, -7.795]}, {"Target": "Parameter", "Id": "ParamBodyStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 0.294, 0, 0.339, 4.872, 0.383, 6.338, 1, 0.478, 9.454, 0.572, 10, 0.667, 10, 0, 1.083, 0.878, 0, 1.317, 2.184, 0, 1.683, 0, 0, 2.1, 2.407, 0, 2.65, 0, 0, 3.15, 3.403, 0, 3.9, -4.017, 1, 4.011, -4.017, 4.122, -0.657, 4.233, -0.142, 1, 4.461, 0.914, 4.689, 1, 4.917, 1, 0, 5.533, -1, 0, 6.267, 0.25, 0, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 0.339, 0, 0.428, 3.782, 0.517, 4.859, 1, 0.639, 6.339, 0.761, 6.367, 0.883, 6.367, 0, 2.017, -0.586, 0, 3.35, 2.35, 0, 3.85, -0.309, 0, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamLegLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 2.25, 14.515, 0, 3.25, -9.742, 1, 3.572, -9.742, 3.895, -9.31, 4.217, 0, 1, 4.634, 12.038, 5.05, 27.852, 5.467, 27.852, 0, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamLegRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.283, 7.732, 0, 3.283, -9.756, 1, 3.594, -9.756, 3.906, -5.378, 4.217, 0, 1, 4.561, 5.954, 4.906, 7.732, 5.25, 7.732, 0, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "Physics_AngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.317, 0, 0, 0.433, -0.923, 0, 0.683, 3.649, 0, 1.183, -5.348, 0, 1.567, -4.571, 0, 1.817, -4.604, 0, 2.433, -3.381, 0, 2.85, -8.554, 0, 3.3, 0.917, 0, 4.217, -2.983, 0, 5.583, 0, 2, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 1.819, 0, 0.617, -3.729, 0, 1.133, 5.334, 2, 1.8, 5.334, 0, 2.433, 4, 0, 2.733, 8.55, 0, 3.117, -9.511, 0, 3.5, 8.037, 0, 3.933, -4.277, 0, 4.35, 2.357, 0, 4.767, -1.699, 0, 5.267, 1.489, 0, 5.65, -1.624, 0, 6.033, 1.826, 0, 6.45, -1.22, 0, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -1.819, 0, 0.617, 3.729, 0, 1.133, -5.334, 2, 1.8, -5.334, 0, 2.433, -4, 0, 2.717, -8.437, 0, 2.967, 20.441, 0, 3.317, -24.404, 0, 3.683, 17.774, 0, 4.117, -8.504, 0, 4.733, 13.977, 0, 5.283, -3.611, 0, 5.633, 6.695, 0, 6.033, -7.306, 0, 6.45, 4.88, 0, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_L", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -0.303, 0, 0.533, 0.801, 0, 0.783, -2.16, 0, 1.167, 3.224, 0, 1.6, -2.206, 0, 2.05, 1.374, 0, 2.533, -1.222, 0, 2.933, 2.771, 0, 3.283, -4.248, 0, 3.733, 2.683, 0, 4.217, -1.418, 0, 4.667, 0.812, 0, 5.117, -0.697, 0, 5.4, 0.366, 0, 5.533, -0.07, 0, 5.767, 0, 2, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_R", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 0.636, 0, 0.617, -1.305, 0, 1.133, 1.867, 2, 1.8, 1.867, 0, 2.433, 1.4, 0, 2.817, 3.268, 0, 3.25, 0, 0, 4.2, 1.167, 0, 5.767, 0, 2, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 3.637, 0, 0.617, -7.459, 0, 1.133, 10.668, 2, 1.8, 10.668, 0, 2.433, 8, 0, 2.733, 16.967, 0, 3.083, -11.352, 0, 3.433, 4.572, 0, 3.75, -1.483, 0, 4.067, 0.835, 0, 4.45, -0.548, 0, 4.733, -0.394, 0, 5.033, -0.468, 0, 5.583, 0.252, 0, 5.767, 0, 2, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -3.637, 0, 0.617, 7.459, 0, 1.133, -10.668, 2, 1.8, -10.668, 0, 2.433, -8, 0, 2.717, -16.874, 0, 2.917, 20.179, 0, 3.2, -11.265, 0, 3.483, 5.23, 0, 3.567, 3.608, 0, 3.633, 4.599, 0, 3.867, -2.678, 0, 4.15, 1.236, 0, 4.433, -0.417, 0, 4.717, 0.115, 0, 5.05, -0.029, 0, 5.067, -0.028, 2, 5.083, -0.028, 0, 5.117, -0.027, 0, 5.133, -0.028, 0, 5.15, -0.026, 0, 5.167, -0.027, 0, 5.183, -0.026, 2, 5.217, -0.026, 0, 5.233, -0.025, 0, 5.25, -0.027, 0, 5.267, -0.026, 0, 5.283, -0.028, 0, 5.3, -0.027, 0, 5.333, -0.028, 2, 5.35, -0.028, 0, 5.367, -0.029, 2, 5.383, -0.029, 0, 5.4, -0.03, 2, 5.417, -0.03, 0, 5.45, -0.031, 2, 5.467, -0.031, 0, 5.483, -0.033, 2, 5.5, -0.033, 0, 5.517, -0.034, 2, 5.533, -0.034, 0, 5.583, -1.186, 0, 5.7, 2.474, 0, 5.917, -2.647, 0, 6.133, 2.793, 0, 6.35, -2.947, 0, 6.567, 3.109, 0, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -3.637, 0, 0.617, 7.459, 0, 1.133, -10.668, 2, 1.8, -10.668, 0, 2.433, -8.001, 0, 2.817, -18.673, 0, 3.25, 0, 0, 4.2, -6.667, 0, 5.767, 0, 2, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 3.637, 0, 0.617, -7.459, 0, 1.133, 10.668, 2, 1.8, 10.668, 0, 2.433, 8.001, 0, 2.817, 18.673, 0, 3.25, 0, 0, 4.2, 6.667, 0, 5.767, 0, 2, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "Jewelry_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 0.303, 0, 0.6, -0.533, 0, 0.933, 0.876, 0, 1.317, -0.857, 0, 1.733, 0.545, 0, 2.15, -0.402, 0, 2.65, 0.608, 0, 3.05, -1.196, 2, 3.067, -1.196, 0, 3.45, 1.192, 0, 3.867, -0.717, 0, 4.283, 0.398, 0, 4.7, -0.287, 0, 5.117, 0.162, 0, 5.533, -0.1, 0, 5.917, 0.096, 0, 6.333, -0.064, 0, 6.567, 0, 2, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "Jewelry_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -0.303, 0, 0.567, 0.426, 0, 0.783, -1.368, 0, 1.15, 2.369, 0, 1.533, -1.836, 0, 1.933, 1.19, 0, 2.35, -0.907, 0, 2.867, 1.637, 0, 3.267, -3.035, 0, 3.667, 2.534, 0, 4.067, -1.434, 0, 4.483, 0.857, 0, 4.9, -0.645, 0, 5.3, 0.329, 0, 5.75, -0.258, 0, 6.15, 0.206, 0, 6.567, -0.146, 0, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "Jewelry_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.533, 0, 0, 0.7, 0.828, 0, 0.967, -1.693, 0, 1.3, 2.157, 0, 1.65, -1.817, 0, 2.05, 1.222, 0, 2.467, -0.814, 0, 3.05, 1.569, 0, 3.417, -2.527, 0, 3.8, 2.369, 0, 4.167, -1.501, 0, 4.583, 0.884, 0, 5, -0.531, 0, 5.433, 0.395, 0, 5.833, -0.226, 0, 6.217, 0.163, 0, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.417, -0.862, 0, 0.867, 1.449, 0, 1.267, -1.558, 0, 1.65, 1.096, 0, 2.033, -0.813, 0, 2.433, 0.657, 0, 2.917, -0.719, 0, 3.367, 0.845, 0, 3.833, -0.533, 0, 4.4, 0.394, 0, 4.817, -0.236, 0, 5.233, 0.222, 2, 5.25, 0.222, 0, 5.667, -0.214, 0, 6.117, 0.124, 0, 6.583, -0.031, 2, 6.617, -0.031, 0, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.35, 0.472, 0, 0.65, -2.269, 0, 1.083, 3.345, 0, 1.467, -3.061, 0, 1.85, 2.34, 0, 2.233, -1.843, 0, 2.65, 1.493, 0, 3.133, -1.656, 0, 3.583, 1.815, 0, 4.15, -0.874, 0, 4.633, 0.716, 0, 5.017, -0.515, 0, 5.45, 0.53, 0, 5.883, -0.46, 0, 6.317, 0.219, 0, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 0.909, 0, 0.6, -1.6, 0, 0.933, 2.627, 0, 1.317, -2.572, 0, 1.733, 1.635, 0, 2.15, -1.207, 0, 2.65, 1.824, 0, 3.05, -3.588, 0, 3.45, 3.577, 0, 3.867, -2.15, 0, 4.283, 1.193, 0, 4.7, -0.863, 0, 5.117, 0.486, 0, 5.533, -0.301, 0, 5.917, 0.289, 0, 6.333, -0.193, 0, 6.567, 0, 2, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -0.909, 0, 0.567, 1.277, 0, 0.783, -4.105, 0, 1.15, 7.109, 0, 1.533, -5.507, 0, 1.933, 3.57, 0, 2.35, -2.722, 0, 2.867, 4.91, 0, 3.267, -9.105, 0, 3.667, 7.602, 0, 4.067, -4.303, 0, 4.483, 2.572, 0, 4.9, -1.935, 0, 5.3, 0.985, 0, 5.75, -0.775, 0, 6.15, 0.619, 0, 6.567, -0.438, 0, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.533, 0, 0, 0.7, 2.485, 0, 0.967, -5.079, 0, 1.3, 6.471, 0, 1.65, -5.452, 0, 2.05, 3.665, 0, 2.467, -2.441, 0, 3.05, 4.708, 0, 3.417, -7.58, 0, 3.8, 7.106, 0, 4.167, -4.503, 0, 4.583, 2.651, 0, 5, -1.592, 0, 5.433, 1.183, 0, 5.833, -0.678, 0, 6.217, 0.489, 0, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.65, 0, 0, 0.817, 2.416, 0, 1.083, -5.924, 0, 1.417, 8.501, 0, 1.767, -8.056, 0, 2.133, 5.843, 0, 2.533, -3.787, 0, 3.15, 5.134, 0, 3.517, -9.333, 0, 3.9, 9.721, 0, 4.267, -7.139, 0, 4.65, 4.342, 0, 5.067, -2.481, 0, 5.517, 1.524, 0, 5.933, -1.276, 0, 5.95, -1.26, 0, 5.967, -1.263, 0, 6.317, 0.903, 0, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.75, 0, 0, 0.917, 2.476, 0, 1.2, -6.798, 0, 1.517, 10.833, 0, 1.867, -11.366, 0, 2.233, 9.016, 0, 2.617, -6.034, 0, 3.217, 5.369, 0, 3.617, -11.15, 0, 3.983, 12.935, 0, 4.367, -10.638, 0, 4.733, 7.027, 0, 5.133, -4.036, 0, 5.567, 2.257, 0, 5.95, -1.443, 0, 6.417, 1.225, 0, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.85, 0, 0, 1.017, 2.57, 0, 1.3, -7.736, 0, 1.633, 13.411, 0, 1.967, -15.378, 0, 2.333, 13.281, 0, 2.7, -9.488, 0, 3.25, 5.504, 0, 3.717, -12.862, 0, 4.083, 16.567, 0, 4.45, -14.953, 0, 4.833, 10.885, 0, 5.217, -6.602, 0, 5.633, 3.577, 0, 6.067, -2.333, 0, 6.5, 1.665, 0, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.95, 0, 0, 1.117, 2.715, 0, 1.417, -8.704, 0, 1.733, 16.244, 0, 2.067, -20.004, 0, 2.433, 18.548, 0, 2.8, -14.284, 0, 3.2, 8.235, 0, 3.8, -14.234, 0, 4.167, 20.27, 0, 4.55, -19.911, 0, 4.933, 15.768, 0, 5.3, -10.479, 0, 5.7, 5.903, 0, 6.133, -3.413, 0, 6.583, 2.342, 0, 6.633, 0, 2, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.417, 1, 0, 3.25, 0, 0, 4.417, 1, 0, 6.25, 0, 1, 6.472, 0, 6.695, 0.327, 6.917, 0.606]}, {"Target": "Parameter", "Id": "fenshangxuanzhuang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 2.472, 0.89, 4.695, 1.779, 6.917, 2.669]}, {"Target": "Parameter", "Id": "Mail_Transparency6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamSad2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.917, 1]}, {"Target": "Parameter", "Id": "ParamCRY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.2, 0, 6.917, -5.2]}, {"Target": "Parameter", "Id": "Position_Coordinates", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.917, 1]}, {"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Man_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.917, 1]}, {"Target": "Parameter", "Id": "Man_TMD2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "BG_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.917, 1]}, {"Target": "Parameter", "Id": "FG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "FG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "BG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "BG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "BG_White4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "BG_White5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "BG_White2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "BG_White3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "All_Size2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "All_Size3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "All_Size4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "tuerzuo3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "faqiehuan", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "faqiehuan2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "faqiehuan4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "faqiehuan3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "tuerzuo4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Mail_Change", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "dafengshangzhuanchang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "tongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamTongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamBrowAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamBrowY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamSigh", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamCry", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_B", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_C", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "MB_Unidirectional_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "jing<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "huqi1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "huqi2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamArmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.917, 1]}, {"Target": "Parameter", "Id": "ParamForearmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamArmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamArmL_ForearmL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamForearmL_HandL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamHandLSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "xiongjiya", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "xiongjiya5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "xiongjiya3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "xiongjiya4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "xiongjiya2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamShoulderRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamArmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.917, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamArmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamArmR_ForearmR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamForearm<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.917, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_HandR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamHandRDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamHandRSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 6.917, 0.6]}, {"Target": "Parameter", "Id": "Change_idle_physics23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Param103", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "taiqiugundong", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -22.5, 0, 6.917, -22.5]}, {"Target": "Parameter", "Id": "Change_idle_physics32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamLegL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.917, 1]}, {"Target": "Parameter", "Id": "ParamLegLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamCrusLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamCrusLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>oodLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamFoodLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamFoodLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamLegR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.917, 1]}, {"Target": "Parameter", "Id": "ParamLegRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamCrusRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamCrusRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamCrus<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>ood<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamFoodRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamFoodRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Physics_Provisional", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "physics_Add_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "physics_Add_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamBodyStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Physics_AngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Physics_AngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Physics_Body_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Physics_Body_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Physics_breast", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamBreath2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "touch_drag1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "touch_drag2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "touch_drag3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "touch_drag4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "touch_drag5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "touch_drag6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "touch_drag7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "touch_drag8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "touch_drag9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>pian<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh654", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh655", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh657", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh656", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh658", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh659", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh660", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.917, 0]}], "UserData": [{"Time": 0.25, "Value": ""}, {"Time": 6.417, "Value": ""}]}