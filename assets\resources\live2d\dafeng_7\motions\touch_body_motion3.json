{"Version": 3, "Meta": {"Duration": 10.0, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 303, "TotalSegmentCount": 1536, "TotalPointCount": 1646, "UserDataCount": 2, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.583, 1, 0, 1.083, -0.4, 0, 1.35, 0.6, 0, 1.533, -0.2, 0, 2.167, 0.9, 0, 3.05, 0, 0, 4.367, 0.6, 0, 4.75, 0.1, 0, 5.5, 0.9, 0, 6.417, -0.6, 0, 6.767, 0.6, 0, 7.317, 0, 0, 7.783, 0.7, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY_SD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.333, 0, 0, 0.45, 1, 0, 0.567, 0, 0, 0.75, 1, 0, 0.867, 0, 0, 1.1, 1, 0, 1.267, 0, 0, 1.45, 1, 0, 1.567, 0, 0, 1.75, 1, 0, 1.867, 0, 0, 2.05, 1, 0, 2.183, 0, 0, 2.333, 1, 0, 2.5, 0, 0, 2.617, 1, 0, 2.733, 0, 0, 2.917, 1, 0, 3.033, 0, 2, 3.75, 0, 0, 3.867, 1, 0, 3.983, 0, 0, 4.167, 1, 0, 4.283, 0, 0, 4.517, 1, 0, 4.683, 0, 0, 4.867, 1, 0, 4.983, 0, 0, 5.167, 1, 0, 5.283, 0, 0, 5.467, 1, 0, 5.6, 0, 0, 5.75, 1, 0, 5.917, 0, 0, 6.033, 1, 0, 6.15, 0, 0, 6.333, 1, 0, 6.45, 0, 0, 6.683, 1, 0, 6.85, 0, 0, 7.033, 1, 0, 7.15, 0, 0, 7.333, 1, 0, 7.45, 0, 0, 7.633, 1, 0, 7.767, 0, 0, 7.917, 1, 0, 8.083, 0, 0, 8.2, 1, 0, 8.317, 0, 0, 8.5, 1, 0, 8.75, 0, 2, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 0.467, 1, 0, 0.6, 0, 0, 0.783, 1.3, 0, 0.867, 1, 2, 3.117, 1, 0, 3.25, 0, 0, 3.433, 1.3, 0, 3.517, 1, 2, 8.65, 1, 0, 8.783, 0, 0, 8.967, 1.3, 0, 9.05, 1, 2, 9.75, 1, 2, 10, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 0.467, 1, 0, 0.6, 0, 0, 0.783, 1.3, 0, 0.867, 1, 2, 3.117, 1, 0, 3.25, 0, 0, 3.433, 1.3, 0, 3.517, 1, 2, 8.65, 1, 0, 8.783, 0, 0, 8.967, 1.3, 0, 9.05, 1, 2, 9.75, 1, 2, 10, 1]}, {"Target": "Parameter", "Id": "EyeFrame_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.45, -1, 2, 3.567, -1, 2, 4.083, -1, 2, 8.65, -1, 0, 9.133, 0, 2, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamBrowForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 3.567, 0, 0, 4.083, -1, 2, 8.65, -1, 0, 9.133, 0, 2, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamEyeLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.467, 0, 0, 0.533, 1, 2, 0.683, 1, 0, 0.7, -1, 2, 0.833, -1, 0, 1.133, 0.244, 0, 1.45, -0.068, 0, 1.75, 0.019, 0, 2.05, -0.005, 2, 2.067, -0.005, 0, 2.367, 0.002, 2, 2.383, 0.002, 0, 2.4, 0.001, 2, 2.417, 0.001, 0, 2.617, 0, 2, 2.633, 0, 2, 2.65, 0, 2, 2.733, 0, 2, 2.75, 0, 2, 2.767, 0, 2, 2.783, 0, 2, 2.8, 0, 2, 2.817, 0, 2, 2.85, 0, 2, 2.867, 0, 2, 2.9, 0, 2, 2.917, 0, 2, 3.1, 0, 2, 3.117, 0, 0, 3.183, 1, 2, 3.333, 1, 0, 3.35, -1, 2, 3.483, -1, 0, 3.783, 0.244, 0, 4.1, -0.068, 0, 4.4, 0.019, 0, 4.7, -0.005, 2, 4.717, -0.005, 0, 5.017, 0.002, 2, 5.033, 0.002, 0, 5.05, 0.001, 2, 5.067, 0.001, 0, 5.267, 0, 2, 5.283, 0, 2, 5.3, 0, 2, 5.383, 0, 2, 5.4, 0, 2, 5.417, 0, 2, 5.433, 0, 2, 5.45, 0, 2, 5.467, 0, 2, 5.5, 0, 2, 5.517, 0, 2, 5.55, 0, 2, 5.567, 0, 2, 5.75, 0, 2, 5.767, 0, 2, 8.65, 0, 0, 8.717, 1, 2, 8.867, 1, 0, 8.883, -1, 2, 9.017, -1, 0, 9.317, 0.244, 0, 9.633, -0.068, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamEyeRsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.467, 0, 0, 0.533, 1, 2, 0.683, 1, 0, 0.7, -1, 2, 0.833, -1, 0, 1.133, 0.244, 0, 1.45, -0.068, 0, 1.75, 0.019, 0, 2.05, -0.005, 2, 2.067, -0.005, 0, 2.367, 0.002, 2, 2.383, 0.002, 0, 2.4, 0.001, 2, 2.417, 0.001, 0, 2.617, 0, 2, 2.633, 0, 2, 2.65, 0, 2, 2.733, 0, 2, 2.75, 0, 2, 2.767, 0, 2, 2.783, 0, 2, 2.8, 0, 2, 2.817, 0, 2, 2.85, 0, 2, 2.867, 0, 2, 2.9, 0, 2, 2.917, 0, 2, 3.1, 0, 2, 3.117, 0, 0, 3.183, 1, 2, 3.333, 1, 0, 3.35, -1, 2, 3.483, -1, 0, 3.783, 0.244, 0, 4.1, -0.068, 0, 4.4, 0.019, 0, 4.7, -0.005, 2, 4.717, -0.005, 0, 5.017, 0.002, 2, 5.033, 0.002, 0, 5.05, 0.001, 2, 5.067, 0.001, 0, 5.267, 0, 2, 5.283, 0, 2, 5.3, 0, 2, 5.383, 0, 2, 5.4, 0, 2, 5.417, 0, 2, 5.433, 0, 2, 5.45, 0, 2, 5.467, 0, 2, 5.5, 0, 2, 5.517, 0, 2, 5.55, 0, 2, 5.567, 0, 2, 5.75, 0, 2, 5.767, 0, 2, 8.65, 0, 0, 8.717, 1, 2, 8.867, 1, 0, 8.883, -1, 2, 9.017, -1, 0, 9.317, 0.244, 0, 9.633, -0.068, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamEyeLGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.467, 0, 0, 0.533, -0.305, 0, 0.583, -0.2, 0, 0.683, -1, 2, 0.7, -1, 0, 0.717, 1, 2, 0.75, 1, 0, 0.783, 0.831, 2, 0.8, 0.831, 0, 0.833, 0.863, 0, 1.083, -0.314, 0, 1.35, 0.153, 0, 1.633, -0.069, 0, 1.933, 0.028, 0, 2.233, -0.01, 0, 2.517, 0.004, 2, 2.55, 0.004, 0, 2.8, -0.001, 2, 2.817, -0.001, 2, 2.833, -0.001, 2, 2.867, -0.001, 0, 3.083, 0, 2, 3.1, 0, 2, 3.117, 0, 0, 3.183, -0.305, 0, 3.233, -0.2, 0, 3.333, -1, 2, 3.35, -1, 0, 3.367, 1, 2, 3.4, 1, 0, 3.433, 0.831, 2, 3.45, 0.831, 0, 3.483, 0.863, 0, 3.733, -0.314, 0, 4, 0.153, 0, 4.283, -0.069, 0, 4.583, 0.028, 0, 4.883, -0.01, 0, 5.167, 0.004, 2, 5.2, 0.004, 0, 5.45, -0.001, 2, 5.467, -0.001, 2, 5.483, -0.001, 2, 5.517, -0.001, 0, 5.733, 0, 2, 5.75, 0, 2, 5.767, 0, 2, 5.833, 0, 2, 5.85, 0, 2, 5.883, 0, 2, 5.9, 0, 2, 5.917, 0, 2, 5.933, 0, 2, 5.95, 0, 2, 5.967, 0, 2, 6, 0, 2, 6.017, 0, 2, 6.217, 0, 2, 6.233, 0, 2, 8.65, 0, 0, 8.717, -0.305, 0, 8.767, -0.2, 0, 8.867, -1, 2, 8.883, -1, 0, 8.9, 1, 2, 8.933, 1, 0, 8.967, 0.831, 2, 8.983, 0.831, 0, 9.017, 0.863, 0, 9.267, -0.314, 0, 9.533, 0.153, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamEyeRGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.467, 0, 0, 0.533, -0.305, 0, 0.583, -0.2, 0, 0.683, -1, 2, 0.7, -1, 0, 0.717, 1, 2, 0.75, 1, 0, 0.783, 0.831, 2, 0.8, 0.831, 0, 0.833, 0.863, 0, 1.083, -0.314, 0, 1.35, 0.153, 0, 1.633, -0.069, 0, 1.933, 0.028, 0, 2.233, -0.01, 0, 2.517, 0.004, 2, 2.55, 0.004, 0, 2.8, -0.001, 2, 2.817, -0.001, 2, 2.833, -0.001, 2, 2.867, -0.001, 0, 3.083, 0, 2, 3.1, 0, 2, 3.117, 0, 0, 3.183, -0.305, 0, 3.233, -0.2, 0, 3.333, -1, 2, 3.35, -1, 0, 3.367, 1, 2, 3.4, 1, 0, 3.433, 0.831, 2, 3.45, 0.831, 0, 3.483, 0.863, 0, 3.733, -0.314, 0, 4, 0.153, 0, 4.283, -0.069, 0, 4.583, 0.028, 0, 4.883, -0.01, 0, 5.167, 0.004, 2, 5.2, 0.004, 0, 5.45, -0.001, 2, 5.467, -0.001, 2, 5.483, -0.001, 2, 5.517, -0.001, 0, 5.733, 0, 2, 5.75, 0, 2, 5.767, 0, 2, 5.833, 0, 2, 5.85, 0, 2, 5.883, 0, 2, 5.9, 0, 2, 5.917, 0, 2, 5.933, 0, 2, 5.95, 0, 2, 5.967, 0, 2, 6, 0, 2, 6.017, 0, 2, 6.217, 0, 2, 6.233, 0, 2, 8.65, 0, 0, 8.717, -0.305, 0, 8.767, -0.2, 0, 8.867, -1, 2, 8.883, -1, 0, 8.9, 1, 2, 8.933, 1, 0, 8.967, 0.831, 2, 8.983, 0.831, 0, 9.017, 0.863, 0, 9.267, -0.314, 0, 9.533, 0.153, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 3.3, 0, 0, 3.467, -5, 2, 8.667, -5, 0, 8.75, 0, 2, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 3.3, 0, 0, 3.467, 5, 2, 8.667, 5, 0, 8.75, 0, 2, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.517, 3.2, 2, 3, 3.2, 0, 4.067, 2.7, 2, 8.7, 2.7, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 30, 2, 0.25, 30, 1, 0.367, 30, 0.483, 30.478, 0.6, 28.789, 1, 0.794, 25.974, 0.989, 5.93, 1.183, 2.029, 1, 1.294, -0.2, 1.406, 0.3, 1.517, 0.3, 2, 1.8, 0.3, 1, 1.95, 0.3, 2.1, 0.548, 2.25, 0.6, 1, 2.5, 0.687, 2.75, 0.682, 3, 0.78, 1, 3.161, 0.843, 3.322, 5.927, 3.483, 5.927, 0, 4.067, -1.3, 2, 7.6, -1.3, 1, 7.778, -1.3, 7.955, -0.974, 8.133, -0.94, 1, 8.322, -0.904, 8.511, -0.909, 8.7, -0.88, 1, 8.783, -0.867, 8.867, 0.859, 8.95, 3.163, 1, 9.05, 5.927, 9.15, 23.801, 9.25, 27.055, 1, 9.344, 30.128, 9.439, 30, 9.533, 30, 2, 9.75, 30, 2, 10, 30]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.517, 0, 2, 3, 0, 0, 4.067, -3.78, 2, 8.7, -3.78, 0, 9.083, -30, 0, 9.433, 0, 2, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1, 2, 0.25, -1, 2, 0.733, -1, 0, 1.517, 1, 2, 3, 1, 2, 4.067, 1, 2, 8.7, 1, 2, 9.083, 1, 2, 9.1, -1, 2, 9.75, -1, 2, 10, -1]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.7, -0.42, 0, 1.517, 0.29, 2, 3, 0.29, 0, 4.067, -0.21, 0, 4.667, -0.194, 1, 4.95, -0.194, 5.234, -0.2, 5.517, -0.202, 1, 5.722, -0.203, 5.928, -0.203, 6.133, -0.203, 0, 7.233, -0.187, 0, 8.7, -0.21, 1, 8.85, -0.21, 9, -0.113, 9.15, 0.1, 1, 9.244, 0.234, 9.339, 0.312, 9.433, 0.312, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamHandLDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10, 2, 0.25, 10, 2, 0.867, 10, 2, 0.883, 6, 2, 3.483, 6, 3, 4.067, 5, 2, 8.7, 5, 2, 9.25, 5, 3, 9.75, 10, 2, 10, 10]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.517, 0, 2, 3, 0, 2, 3.483, 0, 0, 3.5, -1, 0, 3.9, 0.6, 0, 4.067, 0, 2, 8.6, 0, 2, 8.7, 0, 0, 9.25, -1, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.883, -1, 0, 1.133, 1, 0, 1.517, 0, 2, 3, 0, 0, 3.767, -1, 2, 4.067, -1, 2, 8.7, -1, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.417, 0, 0, 0.583, 1, 2, 9.417, 1, 0, 9.583, 0, 2, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 8.917, 0, 0, 9.167, 1, 2, 9.433, 1, 0, 9.567, 0, 2, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.517, -1.5, 2, 3, -1.5, 2, 4.067, -1.5, 2, 8.7, -1.5, 0, 9.167, 0, 2, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.533, 1, 2, 8.817, 1, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.417, 0, 0, 0.65, -4.875, 1, 0.756, -4.875, 0.861, -1.933, 0.967, -1.027, 1, 1.15, 0.547, 1.334, 0.7, 1.517, 0.7, 2, 1.8, 0.7, 1, 1.95, 0.7, 2.1, 0.452, 2.25, 0.4, 1, 2.5, 0.313, 2.75, 0.318, 3, 0.22, 1, 3.161, 0.157, 3.322, -3.285, 3.483, -3.285, 0, 4.067, 2.1, 2, 7.6, 2.1, 0, 8.133, 1.68, 2, 8.7, 1.68, 0, 9.167, 0, 2, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.65, 0.05, 0, 0.9, -0.342, 1, 0.944, -0.342, 0.989, -0.143, 1.033, -0.08, 1, 1.194, 0.147, 1.356, 0.21, 1.517, 0.21, 2, 3, 0.21, 0, 4.067, -0.25, 2, 8.7, -0.25, 1, 8.706, -0.25, 8.711, -0.173, 8.717, -0.169, 1, 8.867, -0.052, 9.017, 0, 9.167, 0, 2, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamHandRDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.65, 0, 3, 1.517, 6, 2, 3, 6, 2, 3.483, 6, 3, 4.067, 5, 2, 8.7, 5, 3, 8.717, 0, 3, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.667, 0, 2, 1.517, 0, 2, 3, 0, 2, 3.483, 0, 0, 3.5, -1, 0, 3.9, 0.5, 0, 4.067, 0, 2, 8.417, 0, 2, 8.6, 0, 0, 8.767, -1, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.667, -1, 0, 1.05, 1, 0, 1.517, 0, 2, 3, 0, 0, 3.767, -1, 0, 4.067, 0, 2, 8.7, 0, 2, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.4, 0, 2, 0.417, 1, 2, 8.7, 1, 0, 8.717, 0, 2, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.4, 0, 2, 0.417, 1, 2, 8.7, 1, 1, 8.706, 0.667, 8.711, 0.333, 8.717, 0, 2, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.417, -0.117, 0, 2.15, -0.078, 0, 3.733, -0.117, 0, 4.067, 0.032, 2, 8.717, 0.032, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.417, -0.015, 2, 3.733, -0.015, 0, 4.067, -0.094, 2, 8.717, -0.094, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.417, 0.128, 0, 2.15, 0.071, 1, 2.678, 0.071, 3.205, 0.083, 3.733, 0.128, 1, 3.844, 0.138, 3.956, 0.226, 4.067, 0.226, 2, 8.717, 0.226, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -2.728, 0, 0.617, 5.594, 0, 1.133, -8, 2, 1.8, -8, 0, 2.433, -6, 0, 2.817, -20.733, 0, 3.417, 14.174, 1, 3.539, 14.174, 3.661, -10.306, 3.783, -19.801, 1, 3.878, -27.138, 3.972, -26.593, 4.067, -26.593, 0, 4.483, 1.177, 0, 4.967, -12.086, 0, 5.45, -0.95, 0, 6, -15, 0, 6.817, -2.336, 0, 7.767, -12.305, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.867, 15, 0, 2.717, -17, 0, 4.583, 9, 0, 8.217, -11, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.9, 0, 2, 2.6, 0, 2, 3.45, 0, 2, 5.1, 0, 0, 6.467, -0.96, 0, 8.8, 0, 2, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -7.795, 2, 0.25, -7.795, 1, 0.75, -7.795, 1.25, -5.197, 1.75, 0, 1, 2.25, 5.197, 2.75, 7.795, 3.25, 7.795, 1, 3.75, 7.795, 4.25, 5.197, 4.75, 0, 1, 5.25, -5.197, 5.75, -7.795, 6.25, -7.795, 0, 7.75, 0, 0, 9.75, -7.795, 2, 10, -7.795]}, {"Target": "Parameter", "Id": "ParamBodyStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 0.294, 0, 0.339, 4.872, 0.383, 6.338, 1, 0.478, 9.454, 0.572, 10, 0.667, 10, 0, 1.083, -3, 0, 1.333, 0, 0, 1.633, -1.92, 0, 2.183, 2.409, 0, 2.683, 0, 0, 3.033, 10, 1, 3.161, 10, 3.289, 10, 3.417, 7.281, 1, 3.578, 3.273, 3.739, -10, 3.9, -10, 0, 4.233, -3, 0, 4.667, -9, 0, 5.033, -4.543, 0, 5.417, -7, 0, 6.017, 0.25, 0, 7.117, -8, 0, 8.283, 10, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.517, 4.859, 0, 0.883, -1.073, 0, 1.483, 2.445, 0, 2.017, -0.586, 0, 2.533, 0.603, 0, 4.967, -7, 0, 6.467, 6, 0, 8, -4, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamLegLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 2.383, 14.515, 0, 3.433, -9.742, 1, 3.783, -9.742, 4.133, -5.187, 4.483, 0, 1, 5.2, 10.622, 5.916, 14.515, 6.633, 14.515, 1, 7.1, 14.515, 7.566, 4.719, 8.033, 2.532, 1, 8.605, -0.15, 9.178, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamLegRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.35, 7.732, 0, 3.483, -9.756, 1, 3.816, -9.756, 4.15, -5.346, 4.483, 0, 1, 4.855, 5.97, 5.228, 7.732, 5.6, 7.732, 0, 7.75, -9.756, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 0.306, 0, 0.361, 0.387, 0.417, 0.5, 1, 0.611, 0.667, 0.806, 0.833, 1, 1, 2, 8.717, 1, 2, 9.15, 1, 1, 9.156, 1, 9.161, 0.51, 9.167, 0.5, 1, 9.361, 0.157, 9.556, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Physics_AngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.317, 0, 0, 0.433, -0.923, 0, 0.683, 3.649, 0, 1.183, -5.348, 0, 1.567, -4.571, 0, 1.817, -4.604, 0, 2.433, -3.381, 0, 2.85, -12.86, 0, 3.433, 9.654, 0, 3.983, -17.116, 0, 4.533, 2.455, 0, 5, -7.765, 0, 5.45, 0, 2, 5.55, 0, 0, 6.033, -9.366, 0, 6.833, -1.005, 0, 7.783, -7.296, 0, 9.6, 0, 2, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 1.819, 0, 0.617, -3.729, 0, 1.133, 5.334, 2, 1.8, 5.334, 0, 2.433, 4, 0, 2.667, 9.114, 0, 3.083, -13.778, 0, 3.583, 17.476, 0, 4.017, -16.758, 0, 4.6, 12.56, 0, 5.067, -10.317, 0, 5.55, 8.476, 0, 6.05, -6.346, 0, 6.483, 2.9, 0, 6.867, -0.741, 0, 7.25, 1.333, 0, 7.767, -1.152, 0, 8.183, 0.378, 0, 8.6, -0.55, 0, 9.033, 0.165, 0, 9.383, -0.102, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 0.256, 0, 0.261, -0.637, 0.267, -0.669, 1, 0.272, -0.7, 0.278, -0.694, 0.283, -0.694, 0, 0.3, -0.575, 0, 0.383, -1.819, 0, 0.617, 3.729, 0, 1.133, -5.334, 2, 1.8, -5.334, 0, 2.433, -4, 0, 2.617, -8.055, 0, 2.9, 25.14, 0, 3.233, -30, 2, 3.367, -30, 0, 3.7, 30, 2, 3.917, 30, 0, 4.217, -30, 2, 4.3, -30, 0, 4.833, 25.363, 0, 5.3, -21.772, 0, 5.783, 17.813, 0, 6.267, -12.304, 0, 6.717, 3.534, 0, 7.05, -1.994, 0, 7.45, 3.819, 0, 7.983, -2.059, 0, 8.383, 0.49, 0, 8.8, -1.701, 0, 9.25, -0.222, 0, 9.65, -1.96, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_L", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -0.636, 0, 0.617, 1.305, 0, 1.133, -1.867, 2, 1.8, -1.867, 0, 2.433, -1.4, 0, 2.667, -3.475, 0, 2.933, 6.904, 0, 3.367, -8.121, 0, 3.883, 11.43, 0, 4.4, -8.487, 0, 4.883, 8.256, 0, 5.367, -6.763, 0, 5.85, 5.356, 0, 6.367, -4.065, 0, 6.817, 1.684, 0, 7.283, -0.473, 0, 7.7, 0.99, 0, 8.15, -0.766, 2, 8.167, -0.766, 0, 8.6, 0.183, 0, 9.05, -0.473, 0, 9.483, 0.151, 0, 9.6, -0.047, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_R", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 0.636, 0, 0.617, -1.305, 0, 1.133, 1.867, 2, 1.8, 1.867, 0, 2.433, 1.4, 0, 2.667, 3.475, 0, 2.933, -6.904, 0, 3.367, 8.121, 0, 3.883, -11.43, 0, 4.4, 8.487, 0, 4.883, -8.256, 0, 5.367, 6.763, 0, 5.85, -5.356, 0, 6.367, 4.065, 0, 6.817, -1.684, 0, 7.283, 0.473, 0, 7.7, -0.99, 0, 8.15, 0.766, 2, 8.167, 0.766, 0, 8.6, -0.183, 0, 9.05, 0.473, 0, 9.483, -0.151, 0, 9.6, 0.047, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 3.637, 0, 0.55, -4.04, 0, 0.867, 30, 2, 0.917, 30, 0, 2.45, -19.154, 0, 2.683, -12.675, 0, 3.067, -30, 0, 3.7, 17.315, 0, 4.033, -1.023, 0, 4.1, 0, 0, 4.3, -3.063, 0, 4.683, 21.163, 0, 5.2, 1.799, 0, 5.667, 11.191, 0, 5.717, 10.908, 0, 5.75, 11.098, 0, 6.35, -4.48, 0, 6.367, -4.479, 0, 6.767, -5.577, 0, 6.883, -5.418, 0, 8.083, -13.593, 2, 8.1, -13.593, 0, 8.133, -13.59, 2, 8.15, -13.59, 0, 9.7, 2.055, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -3.637, 0, 0.517, 3.089, 0, 0.7, -18.674, 0, 1, 11.076, 0, 1.35, -2.098, 0, 1.65, 4.786, 0, 2.6, -2.425, 0, 2.883, 5.53, 0, 3.217, -6.826, 0, 3.417, -4.927, 0, 3.533, -6.604, 0, 3.85, 4.426, 0, 4.1, -6.009, 0, 4.283, 1.189, 0, 4.517, -8.349, 0, 4.8, 6.396, 0, 5.1, -0.895, 0, 5.217, -0.751, 0, 5.383, -1.91, 0, 5.717, 2.078, 0, 5.817, -0.362, 0, 6.017, 3.857, 0, 6.283, -0.906, 0, 6.55, 1.403, 0, 6.85, 0.234, 0, 7.1, 1.172, 0, 7.417, 0.698, 0, 7.633, 0.775, 0, 8.05, -0.086, 0, 8.233, 0.069, 0, 8.683, -1.283, 0, 8.75, -1.278, 0, 8.767, -1.279, 0, 8.8, -1.278, 0, 9.033, -1.348, 0, 9.55, -0.614, 0, 9.6, -1.787, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -3.637, 0, 0.617, 7.456, 0, 1.683, -10.668, 2, 1.717, -10.668, 2, 1.733, -10.668, 2, 1.783, -10.668, 0, 2.433, -7.993, 0, 2.817, -27.632, 0, 3.417, 18.899, 0, 3.8, -25.508, 0, 4.267, 30, 2, 4.417, 30, 0, 4.783, -3.057, 0, 5.25, 17.389, 0, 5.8, -5.357, 0, 6.25, 4.644, 0, 7.433, -13.062, 0, 7.45, -12.956, 0, 7.767, -16.403, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 3.637, 0, 0.617, -7.456, 0, 1.683, 10.668, 2, 1.717, 10.668, 2, 1.733, 10.668, 2, 1.783, 10.668, 0, 2.433, 7.993, 0, 2.817, 27.632, 0, 3.417, -18.899, 0, 3.767, 23.238, 0, 4.017, -29.557, 0, 4.533, 13.766, 0, 4.933, -8.31, 0, 5.467, 6.481, 0, 6, -2.887, 0, 6.383, 3.479, 0, 6.683, 1.885, 0, 6.833, 3.153, 0, 7.183, 0.559, 0, 7.417, 0.909, 0, 7.45, 0.479, 0, 7.65, 5.733, 0, 8.667, -0.439, 0, 8.683, -0.437, 0, 8.7, -0.438, 0, 9.433, 1.121, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Jewelry_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.367, 0.187, 0, 0.583, -0.659, 0, 0.917, 1.005, 0, 1.317, -0.945, 0, 1.733, 0.601, 0, 2.15, -0.438, 0, 2.667, 0.941, 0, 3.067, -1.925, 0, 3.6, 2.696, 0, 4.033, -2.643, 0, 4.6, 2.106, 0, 5.067, -1.74, 0, 5.55, 1.435, 0, 6.05, -1.075, 0, 6.483, 0.494, 0, 6.867, -0.13, 0, 7.25, 0.226, 0, 7.767, -0.195, 0, 8.183, 0.065, 0, 8.6, -0.093, 0, 9.033, 0.028, 0, 9.383, -0.018, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Jewelry_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.35, -0.218, 2, 0.367, -0.218, 0, 0.533, 0.581, 0, 0.783, -1.548, 0, 1.15, 2.605, 0, 1.517, -2.014, 0, 1.933, 1.281, 0, 2.35, -0.981, 0, 2.883, 2.632, 0, 3.3, -4.644, 0, 3.817, 6.017, 0, 4.267, -4.586, 0, 4.833, 4.142, 0, 5.3, -3.571, 0, 5.783, 2.952, 0, 6.267, -2.061, 0, 6.683, 0.761, 0, 7.033, -0.376, 0, 7.45, 0.643, 0, 7.983, -0.348, 0, 8.383, 0.085, 0, 8.8, -0.286, 0, 9.25, -0.036, 0, 9.567, -0.113, 0, 9.6, -0.111, 0, 9.65, -0.132, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Jewelry_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.467, -0.24, 0, 0.683, 0.8, 0, 0.95, -1.783, 0, 1.3, 2.334, 0, 1.65, -1.981, 0, 2.033, 1.32, 0, 2.467, -0.872, 0, 3.05, 2.522, 0, 3.45, -3.337, 0, 3.967, 3.82, 0, 4.35, -2.818, 0, 4.983, 2.743, 0, 5.417, -2.56, 0, 5.9, 1.878, 0, 6.383, -1.353, 0, 6.783, 0.882, 0, 7.167, -0.605, 0, 7.567, 0.408, 0, 8.083, -0.206, 0, 8.517, 0.184, 0, 8.917, -0.135, 0, 9.317, 0.07, 0, 9.6, -0.039, 1, 9.611, -0.039, 9.622, -0.041, 9.633, -0.037, 1, 9.672, -0.023, 9.711, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.417, -0.862, 0, 0.883, 1.672, 0, 1.267, -2.015, 0, 1.65, 1.364, 0, 2.05, -1.001, 0, 2.467, 0.849, 0, 2.933, -1.569, 0, 3.35, 1.346, 0, 4.05, -1.681, 0, 4.5, 1.714, 0, 4.917, -1.562, 0, 5.317, 1.179, 0, 5.717, -0.967, 0, 6.15, 0.78, 0, 6.567, -0.402, 0, 6.967, 0.292, 0, 7.417, -0.469, 0, 7.9, 0.119, 0, 8.167, 0.039, 0, 8.45, 0.158, 0, 8.9, -0.006, 0, 9.25, 0.047, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.35, 0.472, 0, 0.65, -2.269, 0, 1.083, 3.869, 0, 1.467, -3.846, 0, 1.85, 2.754, 0, 2.25, -2.264, 0, 2.683, 1.877, 0, 3.117, -3.523, 0, 3.583, 2.286, 0, 3.667, 2.274, 0, 3.767, 2.307, 0, 4.267, -3.486, 0, 4.7, 3.45, 0, 5.117, -3.177, 0, 5.517, 2.436, 0, 5.933, -2.145, 0, 6.35, 1.58, 0, 6.767, -0.736, 0, 7.167, 0.778, 0, 7.65, -1.124, 0, 8.1, -0.131, 0, 8.3, -0.222, 0, 8.7, 0.353, 0, 9.067, 0.101, 0, 9.433, 0.242, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.367, 0.562, 0, 0.583, -1.977, 0, 0.917, 3.015, 0, 1.317, -2.836, 0, 1.733, 1.803, 0, 2.15, -1.314, 0, 2.667, 2.823, 0, 3.067, -5.775, 0, 3.6, 8.087, 0, 4.033, -7.928, 0, 4.6, 6.317, 0, 5.067, -5.219, 0, 5.55, 4.304, 0, 6.05, -3.224, 0, 6.483, 1.482, 0, 6.867, -0.389, 0, 7.25, 0.677, 0, 7.767, -0.584, 0, 8.183, 0.194, 0, 8.6, -0.278, 0, 9.033, 0.084, 0, 9.383, -0.052, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.35, -0.653, 0, 0.533, 1.744, 0, 0.783, -4.643, 0, 1.15, 7.816, 0, 1.517, -6.043, 0, 1.933, 3.844, 0, 2.35, -2.942, 0, 2.883, 7.896, 0, 3.3, -13.933, 0, 3.817, 18.052, 0, 4.267, -13.759, 0, 4.833, 12.426, 0, 5.3, -10.713, 0, 5.783, 8.856, 0, 6.267, -6.183, 0, 6.683, 2.284, 0, 7.033, -1.128, 0, 7.45, 1.93, 0, 7.983, -1.044, 0, 8.383, 0.256, 0, 8.8, -0.857, 0, 9.25, -0.107, 0, 9.567, -0.338, 0, 9.6, -0.333, 0, 9.65, -0.396, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.467, -0.719, 0, 0.683, 2.399, 0, 0.95, -5.348, 0, 1.3, 7.003, 0, 1.65, -5.944, 0, 2.033, 3.96, 0, 2.467, -2.617, 0, 3.05, 7.566, 0, 3.45, -10.01, 0, 3.967, 11.459, 0, 4.35, -8.455, 0, 4.983, 8.228, 0, 5.417, -7.681, 0, 5.9, 5.634, 0, 6.383, -4.058, 0, 6.783, 2.647, 0, 7.167, -1.814, 0, 7.567, 1.224, 0, 8.083, -0.617, 0, 8.517, 0.553, 0, 8.917, -0.405, 0, 9.317, 0.21, 0, 9.6, -0.117, 1, 9.611, -0.117, 9.622, -0.122, 9.633, -0.11, 1, 9.672, -0.067, 9.711, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.35, 0.794, 0, 0.567, -0.527, 0, 0.783, 2.121, 0, 1.067, -6.129, 0, 1.4, 9.146, 0, 1.75, -8.698, 0, 2.133, 6.406, 0, 2.533, -4.057, 0, 3.15, 8.361, 0, 3.533, -12.79, 0, 4.033, 13.134, 0, 4.45, -11.716, 0, 5.083, 9.009, 0, 5.5, -10.087, 0, 5.967, 7.5, 0, 6.45, -5.246, 0, 6.883, 4.12, 0, 7.25, -2.954, 0, 7.65, 1.935, 0, 8.1, -0.858, 0, 8.6, 0.723, 0, 9, -0.592, 0, 9.4, 0.346, 0, 9.717, -0.149, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.45, 1.048, 0, 0.683, -0.465, 0, 0.9, 1.818, 0, 1.183, -6.857, 0, 1.517, 11.488, 0, 1.867, -12.151, 0, 2.233, 9.778, 0, 2.617, -6.482, 0, 3.233, 8.831, 0, 3.617, -15.533, 0, 4.083, 15.148, 0, 4.533, -14.678, 0, 5.167, 8.995, 0, 5.6, -12.583, 0, 6.033, 10.046, 0, 6.517, -6.757, 0, 6.967, 5.314, 0, 7.35, -4.43, 0, 7.717, 3.084, 0, 8.133, -1.42, 0, 8.683, 0.889, 0, 9.1, -0.826, 0, 9.483, 0.549, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.55, 1.31, 0, 0.8, -0.482, 0, 1, 1.515, 0, 1.3, -7.49, 0, 1.617, 14.088, 0, 1.967, -16.308, 0, 2.317, 14.218, 0, 2.7, -10.177, 0, 3.317, 8.814, 0, 3.683, -17.993, 0, 4.117, 17.706, 0, 4.6, -16.993, 0, 5.217, 8.337, 0, 5.683, -14.786, 0, 6.1, 13.162, 0, 6.583, -8.726, 0, 7.05, 6.961, 0, 7.45, -6.274, 0, 7.817, 4.998, 0, 8.2, -2.547, 0, 8.717, 1.075, 0, 9.183, -1.083, 0, 9.6, 0.918, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.65, 1.655, 0, 0.917, -0.635, 0, 1.117, 1.092, 0, 1.4, -8.018, 0, 1.717, 16.739, 0, 2.05, -20.971, 0, 2.417, 19.637, 0, 2.783, -15.132, 0, 3.35, 8.231, 0, 3.75, -19.92, 0, 4.167, 20.368, 0, 4.667, -18.348, 0, 5.1, 10.802, 0, 5.75, -16.465, 0, 6.167, 16.459, 0, 6.633, -11.232, 0, 7.133, 8.81, 0, 7.533, -8.392, 0, 7.917, 7.339, 0, 8.283, -4.41, 0, 8.7, 1.788, 0, 9.283, -1.315, 0, 9.7, 1.098, 0, 9.75, 0, 2, 10, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.417, 1, 0, 3.25, 0, 0, 4.417, 1, 0, 6.25, 0, 0, 7.417, 1, 0, 9.25, 0, 1, 9.5, 0, 9.75, 0.413, 10, 0.708]}, {"Target": "Parameter", "Id": "fenshangxuanzhuang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 3.5, 1.301, 6.75, 2.603, 10, 3.904]}, {"Target": "Parameter", "Id": "Mail_Transparency6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamSad2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 10, 1]}, {"Target": "Parameter", "Id": "ParamCRY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.2, 0, 10, -5.2]}, {"Target": "Parameter", "Id": "Position_Coordinates", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 10, 1]}, {"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Man_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 10, 1]}, {"Target": "Parameter", "Id": "Man_TMD2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "BG_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 10, 1]}, {"Target": "Parameter", "Id": "FG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "FG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "BG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "BG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "BG_White4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "BG_White5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "BG_White2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "BG_White3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "All_Size2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "All_Size3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "All_Size4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "tuerzuo3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "faqiehuan", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "faqiehuan2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "faqiehuan4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "faqiehuan3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "tuerzuo4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Mail_Change", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "dafengshangzhuanchang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "tongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamTongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "EyeFrame_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamBrowAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamBrowY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamSigh", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamDarken", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamCry", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_B", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_C", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "MB_Unidirectional_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "jing<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "huqi1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "huqi2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamShoulderLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamArmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 10, 1]}, {"Target": "Parameter", "Id": "ParamForearmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamArmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamArmL_ForearmL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamForearmL_HandL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamHandLSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "xiongjiya", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "xiongjiya5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "xiongjiya3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "xiongjiya4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "xiongjiya2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamShoulderRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamArmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 10, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamArmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamArmR_ForearmR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamForearm<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 10, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_HandR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamHandRSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 10, 0.6]}, {"Target": "Parameter", "Id": "Change_idle_physics23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Param103", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "taiqiugundong", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -22.5, 0, 10, -22.5]}, {"Target": "Parameter", "Id": "Change_idle_physics32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamLegL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 10, 1]}, {"Target": "Parameter", "Id": "ParamLegLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamCrusLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamCrusLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>oodLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamFoodLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamFoodLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamLegR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 10, 1]}, {"Target": "Parameter", "Id": "ParamLegRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamCrusRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamCrusRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamCrus<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>ood<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamFoodRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamFoodRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Physics_Provisional", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "physics_Add_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "physics_Add_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamBodyStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Physics_AngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Physics_AngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Physics_Body_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Physics_Body_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Physics_breast", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamBreath2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "touch_drag1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "touch_drag2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "touch_drag3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "touch_drag4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "touch_drag5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "touch_drag6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "touch_drag7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "touch_drag8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "touch_drag9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>pian<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh654", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh655", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh657", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh656", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh658", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh659", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh660", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10, 0]}], "UserData": [{"Time": 0.25, "Value": ""}, {"Time": 9.5, "Value": ""}]}