{"Version": 3, "Meta": {"Duration": 8.75, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 303, "TotalSegmentCount": 1699, "TotalPointCount": 1933, "UserDataCount": 2, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 3.15, 0.2, 2, 4.183, 0.2, 0, 4.933, 0.1, 1, 5.433, 0.1, 5.933, 0.116, 6.433, 0.22, 1, 6.616, 0.258, 6.8, 0.461, 6.983, 0.82, 1, 7.211, 1.267, 7.439, 2.691, 7.667, 2.691, 0, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 0.544, 0, 0.839, -1.363, 1.133, -2.234, 1, 1.25, -2.579, 1.366, -2.487, 1.483, -2.487, 0, 2.067, -1.909, 1, 2.245, -1.909, 2.422, -2.343, 2.6, -3.174, 1, 2.783, -4.03, 2.967, -4.44, 3.15, -4.44, 0, 4.183, -4.2, 0, 4.933, -7.98, 2, 5.25, -7.98, 0, 6.15, -6.674, 1, 6.428, -6.674, 6.705, -7.2, 6.983, -7.98, 1, 7.066, -8.214, 7.15, -8.241, 7.233, -8.241, 0, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 0.544, 0, 0.839, 3.121, 1.133, 3.821, 1, 1.805, 5.418, 2.478, 5.64, 3.15, 5.64, 2, 4.183, 5.64, 0, 4.933, 5.698, 2, 5.25, 5.698, 0, 5.85, 5.7, 1, 6.217, 5.7, 6.583, 5.879, 6.95, 5.144, 1, 7.467, 4.108, 7.983, 0, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.217, 5.67, 0, 2.217, 4.521, 1, 2.528, 4.521, 2.839, 12.868, 3.15, 13.94, 1, 3.494, 15.127, 3.839, 14.869, 4.183, 15.98, 1, 4.433, 16.787, 4.683, 26.1, 4.933, 26.1, 2, 5.25, 26.1, 0, 6.15, 17.654, 0, 6.983, 26.1, 0, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "BG_White3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 2.567, 0.7, 2, 7.083, 0.7, 0, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "All_Size2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 2.567, -30, 2, 7.083, -30, 0, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "All_Size3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 2.567, -3.9, 2, 7.083, -3.9, 0, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "tuerzuo4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 5.267, 0, 0, 5.783, 0.3, 0, 6.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 5.267, 0, 2, 5.783, 0, 0, 6, 1, 2, 6.167, 1, 0, 6.633, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 5.267, 0, 0, 5.8, -1, 0, 6.117, 0.2, 0, 6.3, -0.182, 0, 6.45, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.617, -0.5, 0, 1.117, 0.8, 0, 1.55, -0.5, 0, 2.117, 0.3, 0, 2.783, -0.7, 0, 4.017, 0.8, 1, 4.039, 0.8, 4.061, 0.837, 4.083, 0.753, 1, 4.255, 0.1, 4.428, -0.5, 4.6, -0.5, 0, 5.25, 0.7, 1, 5.422, 0.7, 5.595, 0.563, 5.767, 0.2, 1, 5.795, 0.141, 5.822, 0, 5.85, 0, 1, 5.9, 0, 5.95, -0.019, 6, 0.1, 1, 6.061, 0.245, 6.122, 0.7, 6.183, 0.7, 0, 6.483, 0.1, 0, 6.733, 0.8, 0, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY_SD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.35, 0, 0, 0.467, 1, 0, 0.583, 0, 0, 0.767, 1, 0, 0.883, 0, 0, 1.117, 1, 0, 1.283, 0, 0, 1.467, 1, 0, 1.583, 0, 0, 1.767, 1, 0, 2.017, 0, 2, 2.433, 0, 0, 2.55, 1, 0, 2.667, 0, 0, 2.85, 1, 0, 2.967, 0, 0, 3.2, 1, 0, 3.367, 0, 0, 3.55, 1, 0, 3.667, 0, 0, 3.85, 1, 0, 4.017, 0, 2, 4.083, 0, 2, 4.3, 0, 0, 4.417, 1, 0, 4.533, 0, 0, 4.717, 1, 0, 4.833, 0, 0, 5.067, 1, 0, 5.233, 0, 2, 5.767, 0, 0, 5.85, 0.6, 2, 6, 0.6, 1, 6.061, 0.6, 6.122, 0.311, 6.183, 0.2, 1, 6.283, 0.019, 6.383, 0, 6.483, 0, 2, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 0.8, 1, 0, 0.933, 0, 0, 1.117, 1.3, 0, 1.2, 1, 2, 4.083, 1, 2, 4.833, 1, 0, 5.283, 0, 2, 6.483, 0, 0, 6.967, 1, 2, 7.7, 1, 2, 8.5, 1, 2, 8.75, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 0.8, 1, 0, 0.933, 0, 0, 1.117, 1.3, 0, 1.2, 1, 2, 4.083, 1, 2, 4.833, 1, 0, 5.283, 0, 2, 6.483, 0, 0, 6.967, 1, 2, 7.7, 1, 2, 8.5, 1, 2, 8.75, 1]}, {"Target": "Parameter", "Id": "EyeFrame_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 0.75, 0, 1.25, -0.476, 1.75, -0.6, 1, 2.528, -0.793, 3.305, -0.8, 4.083, -0.8, 2, 4.133, -0.8, 0, 6.083, 0, 0, 6.967, -0.6, 0, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamBrowForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.133, -1, 2, 4.083, -1, 2, 4.683, -1, 0, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamEyeLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.6, -0.111, 0, 1.217, 0.037, 0, 1.517, -0.007, 0, 1.833, 0.007, 2, 1.867, 0.007, 0, 2.067, 0.006, 2, 2.117, 0.006, 1, 2.772, 0.006, 3.428, 0.016, 4.083, 0.025, 1, 4.2, 0.027, 4.316, 0.026, 4.433, 0.026, 2, 4.45, 0.026, 0, 5.083, 0.774, 0, 5.4, -0.4, 0, 5.717, 0.11, 0, 6.017, -0.031, 0, 6.333, 0.009, 0, 6.733, -0.744, 0, 7.067, 0.378, 0, 7.383, -0.104, 0, 7.7, 0.029, 0, 8, -0.008, 0, 8.3, 0.002, 2, 8.333, 0.002, 0, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamEyeRsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.6, -0.111, 0, 1.217, 0.037, 0, 1.517, -0.007, 0, 1.833, 0.007, 2, 1.867, 0.007, 0, 2.067, 0.006, 2, 2.117, 0.006, 1, 2.772, 0.006, 3.428, 0.016, 4.083, 0.025, 1, 4.2, 0.027, 4.316, 0.026, 4.433, 0.026, 2, 4.45, 0.026, 0, 5.083, 0.774, 0, 5.4, -0.4, 0, 5.717, 0.11, 0, 6.017, -0.031, 0, 6.333, 0.009, 0, 6.733, -0.744, 0, 7.067, 0.378, 0, 7.383, -0.104, 0, 7.7, 0.029, 0, 8, -0.008, 0, 8.3, 0.002, 2, 8.333, 0.002, 0, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamEyeLGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.45, 0.022, 0, 0.767, -0.018, 0, 1.017, -0.006, 2, 1.033, -0.006, 0, 1.15, -0.01, 0, 1.383, 0.017, 0, 1.683, -0.01, 0, 1.967, 0.004, 2, 1.983, 0.004, 0, 2.283, -0.002, 2, 2.3, -0.002, 0, 2.55, 0, 2, 2.65, 0, 2, 2.683, 0, 2, 2.7, 0, 2, 2.717, 0, 2, 2.733, 0, 2, 2.75, 0, 2, 2.767, 0, 2, 2.783, 0, 2, 2.8, 0, 2, 2.817, 0, 2, 2.833, 0, 2, 2.85, 0, 2, 2.967, 0, 2, 2.983, 0, 2, 3.05, 0, 2, 3.067, 0, 2, 3.25, 0, 2, 3.267, 0, 2, 3.283, 0, 2, 3.483, 0, 2, 3.5, 0, 2, 3.533, 0, 2, 3.55, 0, 2, 3.567, 0, 2, 3.583, 0, 2, 3.6, 0, 2, 3.667, 0, 2, 3.683, 0, 2, 3.717, 0, 2, 3.733, 0, 2, 3.85, 0, 2, 3.867, 0, 2, 3.883, 0, 2, 3.9, 0, 2, 3.983, 0, 2, 4, 0, 2, 4.05, 0, 2, 4.067, 0, 2, 4.083, 0, 2, 4.1, 0, 2, 4.133, 0, 2, 4.15, 0, 2, 4.217, 0, 2, 4.233, 0, 2, 4.25, 0, 2, 4.267, 0, 2, 4.283, 0, 2, 4.317, 0, 2, 4.333, 0, 2, 4.383, 0, 2, 4.4, 0, 2, 4.417, 0, 2, 4.433, 0, 2, 4.45, 0, 2, 4.483, 0, 2, 4.5, 0, 2, 4.6, 0, 2, 4.617, 0, 2, 4.633, 0, 2, 4.65, 0, 2, 4.767, 0, 2, 4.783, 0, 2, 4.8, 0, 2, 4.817, 0, 2, 4.833, 0, 0, 4.983, -0.168, 0, 5.3, 0.334, 0, 5.567, -0.255, 0, 5.867, 0.12, 0, 6.167, -0.048, 0, 6.633, 0.167, 0, 6.967, -0.296, 0, 7.233, 0.24, 0, 7.533, -0.114, 0, 7.85, 0.046, 0, 8.15, -0.017, 0, 8.45, 0.006, 2, 8.467, 0.006, 0, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamEyeRGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.45, 0.022, 0, 0.767, -0.018, 0, 1.017, -0.006, 2, 1.033, -0.006, 0, 1.15, -0.01, 0, 1.383, 0.017, 0, 1.683, -0.01, 0, 1.967, 0.004, 2, 1.983, 0.004, 0, 2.283, -0.002, 2, 2.3, -0.002, 0, 2.55, 0, 2, 2.65, 0, 2, 2.683, 0, 2, 2.7, 0, 2, 2.717, 0, 2, 2.733, 0, 2, 2.75, 0, 2, 2.767, 0, 2, 2.783, 0, 2, 2.8, 0, 2, 2.817, 0, 2, 2.833, 0, 2, 2.85, 0, 2, 2.967, 0, 2, 2.983, 0, 2, 3.05, 0, 2, 3.067, 0, 2, 3.25, 0, 2, 3.267, 0, 2, 3.283, 0, 2, 3.483, 0, 2, 3.5, 0, 2, 3.533, 0, 2, 3.55, 0, 2, 3.567, 0, 2, 3.583, 0, 2, 3.6, 0, 2, 3.667, 0, 2, 3.683, 0, 2, 3.717, 0, 2, 3.733, 0, 2, 3.85, 0, 2, 3.867, 0, 2, 3.883, 0, 2, 3.9, 0, 2, 3.983, 0, 2, 4, 0, 2, 4.05, 0, 2, 4.067, 0, 2, 4.083, 0, 2, 4.1, 0, 2, 4.133, 0, 2, 4.15, 0, 2, 4.217, 0, 2, 4.233, 0, 2, 4.25, 0, 2, 4.267, 0, 2, 4.283, 0, 2, 4.317, 0, 2, 4.333, 0, 2, 4.383, 0, 2, 4.4, 0, 2, 4.417, 0, 2, 4.433, 0, 2, 4.45, 0, 2, 4.483, 0, 2, 4.5, 0, 2, 4.6, 0, 2, 4.617, 0, 2, 4.633, 0, 2, 4.65, 0, 2, 4.767, 0, 2, 4.783, 0, 2, 4.8, 0, 2, 4.817, 0, 2, 4.833, 0, 0, 4.983, -0.168, 0, 5.3, 0.334, 0, 5.567, -0.255, 0, 5.867, 0.12, 0, 6.167, -0.048, 0, 6.633, 0.167, 0, 6.967, -0.296, 0, 7.233, 0.24, 0, 7.533, -0.114, 0, 7.85, 0.046, 0, 8.15, -0.017, 0, 8.45, 0.006, 2, 8.467, 0.006, 0, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.833, 0, 1, 0.933, 0, 1.033, 2.36, 1.133, 2.438, 1, 2.044, 3.146, 2.956, 3.4, 3.867, 3.4, 1, 3.939, 3.4, 4.011, 3.414, 4.083, 3.393, 1, 4.483, 3.276, 4.883, 2.931, 5.283, 2.438, 1, 5.561, 2.095, 5.839, 0, 6.117, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.65, 0, 1, 2.656, 0.014, 2.661, 0.029, 2.667, 0.043, 1, 2.789, 0.362, 2.911, 0.681, 3.033, 1, 2, 4.083, 1, 0, 4.75, 0.5, 2, 4.933, 0.5, 2, 5.25, 0.5, 0, 6.7, 1, 1, 6.878, 0.667, 7.055, 0.333, 7.233, 0, 2, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamShoulderLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.117, 7.8, 2, 2.233, 7.8, 1, 2.372, 5.2, 2.511, 2.6, 2.65, 0, 2, 2.667, 0, 2, 3.033, 0, 2, 4.083, 0, 2, 6.7, 0, 2, 7.233, 0, 2, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.117, -1.14, 2, 2.233, -1.14, 1, 2.35, -1.14, 2.466, -0.882, 2.583, 0, 1, 2.611, 0.21, 2.639, 1.169, 2.667, 1.986, 1, 2.695, 2.803, 2.722, 3.6, 2.75, 3.6, 0, 3.033, 0, 2, 4.083, 0, 2, 4.183, 0, 1, 4.433, 2.62, 4.683, 5.24, 4.933, 7.86, 1, 5.039, 7.267, 5.144, 6.673, 5.25, 6.08, 1, 5.428, 5.333, 5.605, 4.587, 5.783, 3.84, 1, 6.083, 2.56, 6.383, 1.28, 6.683, 0, 1, 6.783, 1.28, 6.883, 2.56, 6.983, 3.84, 1, 7.066, 2.56, 7.15, 1.28, 7.233, 0, 2, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.117, -24.54, 2, 2.233, -24.54, 0, 2.65, 0, 2, 2.667, 0, 2, 3.033, 0, 2, 4.083, 0, 2, 6.7, 0, 2, 7.233, 0, 2, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 30, 2, 0.25, 30, 2, 0.467, 30, 1, 0.528, 30, 0.589, 31.026, 0.65, 26.757, 1, 0.806, 15.89, 0.961, -3.42, 1.117, -3.42, 2, 2.233, -3.42, 0, 2.65, 0, 2, 2.667, 0, 2, 3.033, 0, 2, 4.083, 0, 2, 4.933, 0, 2, 5.25, 0, 0, 5.783, -17.94, 1, 6.083, -17.94, 6.383, -17.97, 6.683, -15.498, 1, 6.866, -13.988, 7.05, 13.51, 7.233, 22, 1, 7.389, 29.204, 7.544, 30, 7.7, 30, 2, 8.5, 30, 2, 8.75, 30]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.783, -1, 1, 0.833, -1, 0.883, -1.249, 0.933, 3, 1, 0.994, 8.194, 1.056, 22, 1.117, 22, 2, 2.233, 22, 1, 2.372, 14.667, 2.511, 7.333, 2.65, 0, 2, 2.667, 0, 2, 3.033, 0, 2, 4.083, 0, 2, 6.7, 0, 0, 7.233, -30, 0, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1, 2, 0.25, -1, 2, 0.467, -1, 2, 0.883, -1, 1, 0.889, -1, 0.894, 0.473, 0.9, 0.5, 1, 0.972, 0.857, 1.045, 1, 1.117, 1, 2, 2.667, 1, 2, 4.083, 1, 1, 4.166, 0.333, 4.25, -0.333, 4.333, -1, 2, 4.5, -1, 2, 5.25, -1, 1, 5.733, -0.333, 6.217, 0.333, 6.7, 1, 1, 6.794, 0.333, 6.889, -0.333, 6.983, -1, 2, 7.7, -1, 2, 8.5, -1, 2, 8.75, -1]}, {"Target": "Parameter", "Id": "ParamForearmL_HandL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.117, -1.5, 2, 2.233, -1.5, 1, 2.372, -1, 2.511, -0.5, 2.65, 0, 2, 2.667, 0, 2, 3.033, 0, 2, 4.083, 0, 2, 6.7, 0, 2, 7.233, 0, 2, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.117, -1, 2, 2.233, -1, 1, 2.372, -0.667, 2.511, -0.333, 2.65, 0, 2, 2.667, 0, 2, 3.033, 0, 2, 4.083, 0, 2, 5.25, 0, 2, 5.783, 0, 2, 6.683, 0, 2, 7.233, 0, 2, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamHandLDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10, 2, 0.25, 10, 2, 0.7, 10, 2, 0.717, 6, 2, 1.117, 6, 2, 2.233, 6, 2, 2.667, 10, 2, 3.05, 10, 2, 4.083, 10, 2, 4.333, 1, 2, 4.5, 1, 2, 5.25, 1, 2, 7.217, 1, 2, 7.233, 10, 2, 7.7, 10, 2, 8.5, 10, 2, 8.75, 10]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.117, -1, 2, 2.233, -1, 1, 2.372, -0.667, 2.511, -0.333, 2.65, 0, 2, 2.667, 0, 2, 3.033, 0, 2, 4.083, 0, 2, 6.7, 0, 2, 7.233, 0, 2, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.117, -1, 2, 2.233, -1, 1, 2.372, -0.667, 2.511, -0.333, 2.65, 0, 2, 2.667, 0, 2, 3.033, 0, 2, 4.083, 0, 2, 6.7, 0, 2, 7.233, 0, 2, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.117, 7.74, 2, 2.233, 7.74, 0, 2.65, 0, 2, 2.667, 0, 2, 3.033, 0, 2, 4.083, 0, 2, 6.7, 0, 2, 7.233, 0, 2, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.117, 17.4, 2, 2.233, 17.4, 0, 2.65, 0, 2, 2.667, 0, 2, 3.033, 0, 2, 4.083, 0, 2, 6.7, 0, 2, 7.233, 0, 2, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.4, 1, 2, 2.667, 1, 2, 4.083, 1, 2, 7.317, 1, 0, 7.367, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.65, 0, 2, 2.667, 0, 2, 3.033, 0, 2, 4.083, 0, 2, 6.7, 0, 2, 7.183, 0, 0, 7.25, 1, 0, 7.433, 0, 2, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "xiongjiya", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.383, 0, 2, 0.717, 0, 0, 1.117, 1, 2, 2.233, 1, 0, 2.65, 0, 2, 2.667, 0, 2, 4.083, 0, 2, 6.85, 0, 2, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.65, 0, 1, 2.656, 0.014, 2.661, 0.029, 2.667, 0.043, 1, 2.789, 0.362, 2.911, 0.681, 3.033, 1, 2, 4.083, 1, 0, 4.75, 0.5, 2, 5.25, 0.5, 0, 6.7, 1, 1, 6.878, 0.667, 7.055, 0.333, 7.233, 0, 2, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamShoulderRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.117, 10, 2, 2.233, 10, 1, 2.372, 6.667, 2.511, 3.333, 2.65, 0, 2, 2.667, 0, 2, 3.033, 0, 2, 4.083, 0, 2, 6.7, 0, 2, 7.233, 0, 2, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.383, 0, 0, 1.117, 30, 2, 2.233, 30, 1, 2.372, 30, 2.511, 18.569, 2.65, 0, 1, 2.656, -0.743, 2.661, -0.838, 2.667, -0.894, 1, 2.695, -1.176, 2.722, -1.26, 2.75, -1.26, 0, 3.033, 0, 2, 4.083, 0, 2, 4.183, 0, 1, 4.433, -1.74, 4.683, -3.48, 4.933, -5.22, 1, 5.039, -4.827, 5.144, -4.433, 5.25, -4.04, 1, 5.428, -3.543, 5.605, -3.047, 5.783, -2.55, 1, 6.083, -1.7, 6.383, -0.85, 6.683, 0, 1, 6.783, -1.1, 6.883, -2.2, 6.983, -3.3, 1, 7.066, -2.2, 7.15, -1.1, 7.233, 0, 2, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.117, -23.4, 2, 2.233, -23.4, 0, 2.65, 0, 2, 2.667, 0, 2, 3.033, 0, 2, 4.083, 0, 2, 6.7, 0, 2, 7.233, 0, 2, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.5, 1, 2, 2.667, 1, 2, 4.083, 1, 2, 7.2, 1, 0, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.383, 0, 0, 1.117, 2.88, 2, 2.233, 2.88, 0, 2.65, 0, 2, 2.667, 0, 2, 3.033, 0, 2, 4.083, 0, 2, 4.933, 0, 2, 5.25, 0, 1, 5.428, 6.64, 5.605, 13.28, 5.783, 19.92, 1, 6.083, 17.902, 6.383, 15.884, 6.683, 13.866, 1, 6.866, 9.244, 7.05, 4.622, 7.233, 0, 2, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.117, 30, 2, 2.233, 30, 2, 2.65, 0, 2, 2.667, 0, 2, 3.033, 0, 2, 4.083, 0, 2, 6.7, 0, 2, 7.233, 0, 2, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamForearm<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 1.117, 1, 2, 2.233, 1, 2, 2.65, 1, 2, 2.667, 1, 2, 3.033, 1, 2, 4.083, 1, 1, 4.166, 0.333, 4.25, -0.333, 4.333, -1, 2, 4.517, -1, 2, 5.25, -1, 1, 5.733, -0.333, 6.217, 0.333, 6.7, 1, 2, 7.233, 1, 0, 7.25, 0.8, 0, 8.5, 1, 2, 8.75, 1]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.383, 0, 0, 1.117, -1, 2, 2.233, -1, 1, 2.372, -0.667, 2.511, -0.333, 2.65, 0, 2, 2.667, 0, 2, 3.033, 0, 1, 3.383, -0.033, 3.733, -0.066, 4.083, -0.099, 1, 4.472, -0.136, 4.861, -0.173, 5.25, -0.21, 1, 5.428, -0.223, 5.605, -0.237, 5.783, -0.25, 1, 6.083, 0.006, 6.383, 0.262, 6.683, 0.518, 1, 6.866, 0.345, 7.05, 0.173, 7.233, 0, 2, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamHandRDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.7, 0, 2, 0.717, 6, 2, 1.117, 6, 2, 2.233, 6, 2, 2.65, 0, 2, 2.667, 0, 2, 3.033, 0, 2, 4.083, 0, 2, 4.333, 1, 2, 4.517, 1, 2, 5.25, 1, 2, 7.217, 1, 2, 7.233, 0, 2, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.117, 1, 2, 2.233, 1, 1, 2.372, 0.667, 2.511, 0.333, 2.65, 0, 2, 2.667, 0, 2, 3.033, 0, 2, 4.083, 0, 2, 6.7, 0, 2, 7.233, 0, 2, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.117, -1, 2, 2.233, -1, 1, 2.372, -0.667, 2.511, -0.333, 2.65, 0, 2, 2.667, 0, 2, 3.033, 0, 1, 3.383, -0.236, 3.733, -0.472, 4.083, -0.708, 1, 4.228, -0.805, 4.372, -0.903, 4.517, -1, 0, 4.933, 0, 2, 5.35, 0, 0, 5.8, 1, 0, 6.05, -1, 2, 6.15, -1, 0, 6.7, 0, 2, 7.233, 0, 2, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.117, 0, 2, 2.233, 0, 2, 2.65, 0, 2, 2.667, 0, 2, 3.033, 0, 1, 3.383, -3.684, 3.733, -7.369, 4.083, -11.053, 1, 4.366, -14.035, 4.65, -17.018, 4.933, -20, 1, 5.522, -13.333, 6.111, -6.667, 6.7, 0, 2, 7.233, 0, 2, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.117, -10.22, 2, 2.233, -10.22, 0, 2.65, 0, 2, 2.667, 0, 2, 3.033, 0, 2, 4.083, 0, 2, 6.7, 0, 2, 7.233, 0, 2, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.117, 2.86, 2, 2.233, 2.86, 0, 2.65, 0, 2, 2.667, 0, 2, 3.033, 0, 2, 4.083, 0, 2, 6.7, 0, 2, 7.233, 0, 2, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.417, 0, 2, 0.433, 1, 2, 2.667, 1, 2, 4.083, 1, 2, 7.217, 1, 0, 7.233, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.417, 0, 2, 0.433, 1, 2, 2.667, 1, 2, 4.083, 1, 2, 7.217, 1, 0, 7.233, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.433, 0.05, 0, 1.117, 0, 2, 2.233, 0, 2, 2.65, 0, 2, 2.667, 0, 2, 3.033, 0, 2, 4.083, 0, 2, 6.7, 0, 2, 7.233, 0, 2, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.433, 0.092, 0, 1.117, 0, 2, 2.233, 0, 2, 2.65, 0, 2, 2.667, 0, 2, 3.033, 0, 2, 4.083, 0, 2, 6.7, 0, 2, 7.233, 0, 2, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.433, 0.016, 0, 1.117, 0, 2, 2.233, 0, 2, 2.65, 0, 2, 2.667, 0, 2, 3.033, 0, 2, 4.083, 0, 2, 6.7, 0, 2, 7.233, 0, 2, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -2.728, 0, 0.617, 5.594, 0, 1.133, -8, 2, 1.8, -8, 0, 2.433, -6, 0, 2.7, -14, 0, 3.2, 14, 2, 4.25, 14, 0, 4.85, -14.746, 0, 5.05, 7, 0, 5.3, -15.62, 0, 5.767, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.383, -7.98, 2, 2.2, -7.98, 1, 2.617, -7.98, 3.033, -5.358, 3.45, -4.5, 1, 4.383, -2.578, 5.317, -1.876, 6.25, 0, 1, 6.628, 0.759, 7.005, 3, 7.383, 3, 0, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 3.033, 0, 0, 3.967, -1, 0, 5.1, 0, 0, 6.467, -0.96, 0, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -7.795, 2, 0.25, -7.795, 1, 0.75, -7.795, 1.25, -5.197, 1.75, 0, 1, 2.25, 5.197, 2.75, 7.795, 3.25, 7.795, 1, 3.75, 7.795, 4.25, 5.197, 4.75, 0, 1, 5.25, -5.197, 5.75, -7.795, 6.25, -7.795, 0, 7.75, 0, 0, 8.5, -7.795, 2, 8.75, -7.795]}, {"Target": "Parameter", "Id": "ParamBodyStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 0.294, 0, 0.339, 4.872, 0.383, 6.338, 1, 0.478, 9.454, 0.572, 10, 0.667, 10, 0, 1.083, -3, 0, 1.217, 1.044, 1, 1.284, 1.044, 1.35, 0.677, 1.417, 0, 1, 1.545, -1.298, 1.672, -1.92, 1.8, -1.92, 0, 2.4, 2.409, 0, 2.683, 0, 0, 3.033, 10, 1, 3.161, 10, 3.289, 9.836, 3.417, 7.281, 1, 3.9, -2.382, 4.384, -9, 4.867, -9, 0, 6.017, 0.25, 0, 6.633, -5.184, 0, 7.05, 2.395, 0, 7.183, 1.889, 0, 7.35, 2.493, 0, 7.683, 0.094, 0, 7.833, 0.616, 0, 8.217, -4, 0, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.683, 0, 0, 1.483, 2.445, 0, 2.017, -0.586, 1, 2.189, -0.586, 2.361, 0.107, 2.533, 0.603, 1, 3.844, 4.378, 5.156, 6, 6.467, 6, 0, 8, -4, 0, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamLegLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.45, 0, 0, 1.217, 14.515, 0, 2.017, 2.532, 0, 3, 27.852, 0, 3.733, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamLegRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.45, 0, 0, 1.8, 7.732, 0, 2.517, -9.756, 0, 3.067, 30, 0, 3.733, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 0.294, 0, 0.339, 0.339, 0.383, 0.5, 1, 0.494, 0.902, 0.606, 1, 0.717, 1, 2, 7.233, 1, 1, 7.239, 1, 7.244, 0.513, 7.25, 0.5, 1, 7.4, 0.155, 7.55, 0, 7.7, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "Physics_AngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.317, 0, 0, 0.433, -0.923, 0, 0.683, 3.649, 0, 1.183, -5.348, 0, 1.567, -4.571, 0, 1.817, -4.604, 0, 2.433, -3.381, 0, 2.75, -8.497, 0, 3.25, 9.64, 0, 3.633, 7.984, 0, 4.033, 8.067, 0, 4.867, -9.64, 0, 5.1, 4.579, 0, 5.383, -10.241, 0, 5.817, 0.954, 0, 6.167, -0.243, 0, 6.333, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 1.819, 0, 0.617, -3.729, 0, 1.133, 5.334, 2, 1.8, 5.334, 0, 2.433, 4, 0, 2.65, 8.444, 0, 3.017, -12.124, 0, 3.383, 11.796, 0, 3.8, -7.502, 0, 4.217, 4.773, 0, 5.017, -15.021, 0, 5.283, 18.914, 0, 5.65, -15.146, 0, 6.05, 10.717, 0, 6.467, -6.819, 0, 6.883, 4.339, 0, 7.3, -2.15, 0, 7.717, 1.436, 0, 8.133, -0.959, 0, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -1.819, 0, 0.617, 3.729, 0, 1.133, -5.334, 2, 1.8, -5.334, 0, 2.433, -4, 0, 2.617, -8.098, 0, 2.867, 21, 0, 3.167, -30, 2, 3.267, -30, 0, 3.6, 25.79, 0, 4, -16.432, 0, 4.633, 9.844, 0, 4.883, 7.454, 0, 4.917, 7.648, 0, 5.133, -30, 2, 5.25, -30, 0, 5.417, 30, 2, 5.567, 30, 0, 5.8, -30, 2, 5.917, -30, 0, 6.25, 23.64, 0, 6.667, -14.588, 0, 7.083, 9.192, 0, 7.483, -4.547, 0, 7.917, 2.89, 0, 8.4, -3.03, 0, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_L", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.367, -0.797, 0, 0.55, 1.04, 0, 0.8, -2.093, 0, 1.167, 3.491, 0, 1.583, -2.229, 0, 2.033, 1.369, 0, 2.55, -1.414, 0, 2.867, 3.436, 0, 3.233, -6.819, 0, 3.65, 4.38, 0, 4.083, -3.043, 0, 4.767, 4.135, 0, 4.883, 3.578, 0, 4.917, 3.651, 0, 5.2, -10.851, 0, 5.517, 9.131, 0, 5.9, -6.135, 0, 6.367, 3.243, 0, 6.833, -2.041, 0, 7.283, 1.358, 0, 7.783, -0.796, 0, 8.233, 0.66, 0, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_R", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.367, 0.797, 0, 0.55, -1.04, 0, 0.8, 2.093, 0, 1.167, -3.491, 0, 1.583, 2.229, 0, 2.033, -1.369, 0, 2.55, 1.414, 0, 2.867, -3.436, 0, 3.233, 6.819, 0, 3.65, -4.38, 0, 4.083, 3.043, 0, 4.767, -4.135, 0, 4.883, -3.578, 0, 4.917, -3.651, 0, 5.2, 10.851, 0, 5.517, -9.131, 0, 5.9, 6.135, 0, 6.367, -3.243, 0, 6.833, 2.041, 0, 7.283, -1.358, 0, 7.783, 0.796, 0, 8.233, -0.66, 0, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 3.637, 0, 0.617, -7.459, 0, 1.133, 10.668, 2, 1.8, 10.668, 0, 2.433, 8, 0, 2.65, 17.273, 0, 3, -16.348, 0, 3.35, 6.313, 0, 3.717, -1.55, 0, 4.083, 0.38, 0, 4.25, 0.097, 0, 4.583, 9.833, 0, 5.017, -22.865, 0, 5.267, 21.976, 0, 5.583, -13.543, 0, 5.933, 3.758, 0, 6.283, -1.681, 0, 6.6, 1.765, 0, 6.917, -1.853, 0, 7.233, 1.946, 0, 7.433, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -3.637, 0, 0.617, 7.459, 0, 1.133, -10.668, 2, 1.8, -10.668, 0, 2.433, -8, 0, 2.633, -17.004, 0, 2.833, 22.299, 0, 3.117, -13.204, 0, 3.417, 6.856, 0, 3.717, -2.359, 0, 4.033, 0.552, 0, 4.433, -3.244, 0, 4.75, 3.774, 0, 4.85, 3.184, 0, 4.967, 9.866, 0, 5.167, -22.108, 0, 5.4, 23.691, 0, 5.667, -12.853, 0, 5.967, 5.966, 0, 6.283, -1.512, 0, 6.45, -0.543, 0, 6.467, -1.419, 0, 6.7, 1.483, 0, 6.767, 1.168, 0, 6.8, 2.744, 0, 7.033, -1.958, 0, 7.083, -1.636, 0, 7.15, -3.699, 0, 7.383, 2.183, 0, 7.417, 2.026, 0, 7.433, 2.547, 0, 7.583, -3.071, 0, 7.8, 3.24, 0, 7.933, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -3.637, 0, 0.617, 7.459, 0, 1.133, -10.668, 2, 1.8, -10.668, 0, 2.433, -8.001, 0, 2.7, -18.673, 0, 3.2, 18.673, 2, 4.25, 18.673, 0, 4.85, -19.669, 0, 5.05, 9.334, 0, 5.3, -20.835, 0, 5.767, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 3.637, 0, 0.617, -7.459, 0, 1.133, 10.668, 2, 1.8, 10.668, 0, 2.433, 8.001, 0, 2.7, 18.673, 0, 3.2, -18.673, 2, 4.25, -18.673, 0, 4.85, 19.669, 0, 5.05, -9.334, 0, 5.3, 20.835, 0, 5.767, 0, 2, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "Jewelry_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 0.303, 0, 0.6, -0.533, 0, 0.933, 0.876, 0, 1.317, -0.857, 0, 1.733, 0.545, 0, 2.15, -0.402, 0, 2.633, 0.784, 0, 2.983, -1.744, 0, 3.367, 1.78, 0, 3.783, -1.128, 0, 4.217, 0.717, 0, 5.017, -2.515, 0, 5.283, 3.041, 0, 5.667, -2.399, 0, 6.05, 1.683, 0, 6.467, -1.032, 0, 6.883, 0.656, 0, 7.3, -0.417, 2, 7.317, -0.417, 0, 7.733, 0.266, 0, 8.133, -0.207, 0, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "Jewelry_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -0.303, 0, 0.567, 0.426, 0, 0.783, -1.368, 0, 1.15, 2.369, 0, 1.533, -1.836, 0, 1.933, 1.19, 0, 2.35, -0.907, 0, 2.817, 2.179, 0, 3.2, -4.527, 0, 3.583, 3.652, 0, 4, -2.357, 0, 4.417, 1.286, 0, 4.5, 1.272, 0, 4.767, 1.698, 0, 4.883, 1.419, 0, 4.9, 1.42, 0, 5.183, -5.891, 0, 5.5, 6.288, 0, 5.85, -5.453, 0, 6.25, 3.548, 0, 6.667, -2.166, 0, 7.083, 1.376, 0, 7.517, -0.882, 0, 7.967, 0.582, 0, 8.367, -0.446, 0, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "Jewelry_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.533, 0, 0, 0.7, 0.828, 0, 0.967, -1.693, 0, 1.3, 2.157, 0, 1.65, -1.817, 0, 2.05, 1.222, 0, 2.467, -0.815, 0, 3, 2.477, 0, 3.35, -3.782, 0, 3.717, 3.429, 0, 4.1, -2.333, 0, 4.467, 0.913, 0, 4.717, -0.134, 0, 5.067, 2.393, 0, 5.333, -5.989, 0, 5.633, 6.687, 0, 5.983, -5.506, 0, 6.367, 3.627, 0, 6.767, -2.101, 0, 7.2, 1.269, 0, 7.617, -0.814, 0, 8.05, 0.481, 0, 8.483, -0.38, 0, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, 0.909, 0, 0.6, -1.6, 0, 0.933, 2.627, 0, 1.317, -2.572, 0, 1.733, 1.635, 0, 2.15, -1.207, 0, 2.633, 2.354, 0, 2.983, -5.232, 0, 3.367, 5.339, 0, 3.783, -3.384, 0, 4.217, 2.15, 0, 5.017, -7.545, 0, 5.283, 9.123, 0, 5.667, -7.197, 0, 6.05, 5.049, 0, 6.467, -3.098, 0, 6.883, 1.968, 0, 7.317, -1.252, 0, 7.733, 0.797, 0, 8.133, -0.622, 0, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.383, -0.909, 0, 0.567, 1.277, 0, 0.783, -4.105, 0, 1.15, 7.109, 0, 1.533, -5.507, 0, 1.933, 3.57, 0, 2.35, -2.722, 0, 2.817, 6.537, 0, 3.2, -13.579, 0, 3.583, 10.955, 0, 4, -7.072, 0, 4.417, 3.858, 0, 4.5, 3.816, 0, 4.767, 5.093, 0, 4.883, 4.257, 0, 4.9, 4.259, 0, 5.183, -17.674, 0, 5.5, 18.865, 0, 5.85, -16.358, 0, 6.25, 10.643, 0, 6.667, -6.497, 0, 7.083, 4.129, 0, 7.517, -2.646, 0, 7.967, 1.746, 0, 8.367, -1.338, 0, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.533, 0, 0, 0.7, 2.485, 0, 0.967, -5.079, 0, 1.3, 6.471, 0, 1.65, -5.452, 0, 2.05, 3.665, 0, 2.467, -2.444, 0, 3, 7.432, 0, 3.35, -11.347, 0, 3.717, 10.287, 0, 4.1, -7, 0, 4.467, 2.74, 0, 4.717, -0.402, 0, 5.067, 7.179, 0, 5.333, -17.968, 0, 5.633, 20.06, 0, 5.983, -16.52, 0, 6.367, 10.881, 0, 6.767, -6.303, 0, 7.2, 3.808, 0, 7.617, -2.442, 0, 8.05, 1.442, 0, 8.483, -1.14, 0, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.65, 0, 0, 0.817, 2.416, 0, 1.083, -5.924, 0, 1.417, 8.501, 0, 1.767, -8.056, 0, 2.133, 5.843, 0, 2.533, -3.794, 0, 3.083, 8.213, 0, 3.45, -14.059, 0, 3.817, 14.336, 0, 4.2, -10.384, 0, 4.567, 5.157, 0, 4.85, -1.348, 0, 5.15, 7.448, 0, 5.433, -19.089, 0, 5.75, 25.197, 0, 6.083, -23.173, 0, 6.467, 16.657, 0, 6.85, -10.201, 0, 7.267, 5.899, 0, 7.683, -3.655, 0, 8.133, 2.495, 0, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.75, 0, 0, 0.917, 2.476, 0, 1.2, -6.798, 0, 1.517, 10.833, 0, 1.867, -11.366, 0, 2.233, 9.016, 0, 2.617, -6.048, 0, 3.167, 8.846, 0, 3.533, -16.698, 0, 3.9, 18.731, 0, 4.283, -15.215, 0, 4.65, 8.726, 0, 4.95, -3.099, 0, 5.25, 8.377, 0, 5.533, -20.692, 0, 5.833, 29.608, 0, 6.183, -29.782, 0, 6.55, 23.415, 0, 6.933, -15.651, 0, 7.333, 9.281, 0, 7.75, -5.506, 0, 8.2, 3.339, 0, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.85, 0, 0, 1.017, 2.57, 0, 1.3, -7.736, 0, 1.633, 13.411, 0, 1.967, -15.378, 0, 2.333, 13.281, 0, 2.7, -9.522, 0, 3.25, 9.383, 0, 3.617, -19.199, 0, 3.983, 23.231, 0, 4.367, -20.581, 0, 4.75, 13.479, 0, 5.067, -6.405, 0, 5.35, 10.606, 0, 5.617, -23.248, 0, 5.883, 30, 2, 5.95, 30, 0, 6.217, -30, 2, 6.3, -30, 0, 6.633, 30, 0, 7.017, -21.965, 0, 7.417, 14.156, 0, 7.833, -8.439, 0, 8.25, 5.067, 0, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.95, 0, 0, 1.117, 2.715, 0, 1.417, -8.704, 0, 1.733, 16.244, 0, 2.067, -20.004, 0, 2.433, 18.548, 0, 2.8, -14.312, 0, 3.267, 10.55, 0, 3.683, -21.235, 0, 4.067, 27.433, 0, 4.45, -25.933, 0, 4.833, 18.936, 0, 5.167, -10.349, 0, 5.45, 12.668, 0, 5.7, -26.046, 0, 5.933, 30, 2, 6.033, 30, 0, 6.283, -30, 2, 6.383, -30, 0, 6.667, 30, 2, 6.767, 30, 0, 7.1, -28.31, 0, 7.5, 19.942, 0, 7.9, -12.654, 0, 8.333, 7.642, 0, 8.5, 0, 2, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.417, 1, 0, 3.25, 0, 0, 4.417, 1, 0, 6.25, 0, 0, 7.417, 1, 1, 7.861, 1, 8.306, 0.471, 8.75, 0.183]}, {"Target": "Parameter", "Id": "fenshangxuanzhuang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 3.083, 1.134, 5.917, 2.269, 8.75, 3.403]}, {"Target": "Parameter", "Id": "Mail_Transparency6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamSad2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 8.75, 1]}, {"Target": "Parameter", "Id": "ParamCRY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.2, 0, 8.75, -5.2]}, {"Target": "Parameter", "Id": "Position_Coordinates", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 8.75, 1]}, {"Target": "Parameter", "Id": "Man_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 8.75, 1]}, {"Target": "Parameter", "Id": "Man_TMD2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "BG_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 8.75, 1]}, {"Target": "Parameter", "Id": "FG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "FG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "BG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "BG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "BG_White4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "BG_White5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "BG_White2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "All_Size4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "tuerzuo3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "faqiehuan", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "faqiehuan2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "faqiehuan4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "faqiehuan3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Mail_Change", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "dafengshangzhuanchang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "tongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamTongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "EyeFrame_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamBrowAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamBrowY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamSigh", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamDarken", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamCry", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_B", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_C", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "MB_Unidirectional_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "jing<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "huqi1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "huqi2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamArmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 8.75, 1]}, {"Target": "Parameter", "Id": "ParamForearmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamArmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamArmL_ForearmL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamHandLSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "xiongjiya5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "xiongjiya3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "xiongjiya4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "xiongjiya2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamArmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 8.75, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamArmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamArmR_ForearmR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_HandR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamHandRSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 8.75, 0.6]}, {"Target": "Parameter", "Id": "Change_idle_physics23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Param103", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "taiqiugundong", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -22.5, 0, 8.75, -22.5]}, {"Target": "Parameter", "Id": "Change_idle_physics32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamLegL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 8.75, 1]}, {"Target": "Parameter", "Id": "ParamLegLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamCrusLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamCrusLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>oodLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamFoodLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamFoodLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamLegR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 8.75, 1]}, {"Target": "Parameter", "Id": "ParamLegRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamCrusRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamCrusRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamCrus<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>ood<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamFoodRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamFoodRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Physics_Provisional", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "physics_Add_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "physics_Add_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamBodyStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Physics_AngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Physics_AngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Physics_Body_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Physics_Body_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Physics_breast", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamBreath2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "touch_drag1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "touch_drag2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "touch_drag3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "touch_drag4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "touch_drag5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "touch_drag6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "touch_drag7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "touch_drag8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "touch_drag9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>pian<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh654", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh655", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh657", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh656", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh658", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh659", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh660", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.75, 0]}], "UserData": [{"Time": 0.25, "Value": ""}, {"Time": 8.25, "Value": ""}]}