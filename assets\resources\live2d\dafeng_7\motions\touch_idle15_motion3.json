{"Version": 3, "Meta": {"Duration": 5.417, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 304, "TotalSegmentCount": 968, "TotalPointCount": 1286, "UserDataCount": 1, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Mail_Transparency6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.5, 1, 2, 2.25, 1, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.371, 2, 0.25, 2.371, 0, 1.667, 3.871, 2, 1.75, 3.871, 1, 1.917, 3.891, 2.083, 3.911, 2.25, 3.931, 2, 2.267, 0.751, 1, 2.4, 0.169, 2.534, -0.089, 2.667, -0.089, 0, 3.917, 0.651, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.047, 2, 0.25, -1.047, 0, 1.667, -2.727, 2, 1.75, -2.727, 1, 1.917, -3.067, 2.083, -3.407, 2.25, -3.747, 2, 2.267, -3.687, 0, 2.667, -4.21, 1, 3.084, -4.21, 3.5, -1.771, 3.917, -0.852, 1, 4.328, 0.055, 4.739, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.25, 0, 2, 2.267, 5.58, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 9.705, 2, 0.25, 9.705, 0, 1.667, 16.457, 2, 1.75, 16.457, 2, 2.25, 16.457, 2, 2.267, 7.397, 1, 3.228, 2.439, 4.189, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "FG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 1.833, 0, 0, 2.267, 0.9, 0, 3.583, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "BG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.5, 1, 2, 2.25, 1, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "faqiehuan", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.5, 2, 0.5, 0.5, 2, 2.25, 0.5, 2, 2.267, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "faqiehuan2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.5, 2, 0.5, 0.5, 2, 2.25, 0.5, 2, 2.267, 0, 2, 3.167, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "faqiehuan3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.5, 1, 2, 0.75, 1, 0, 1, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "tuerzuo4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 0, 1.333, 0.397, 2, 1.75, 0.397, 1, 1.917, 0.598, 2.083, 0.799, 2.25, 1, 1, 2.389, 1, 2.528, 0.558, 2.667, 0.445, 1, 3.111, 0.084, 3.556, 0, 4, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 2.25, 0, 0, 2.467, 1, 2, 2.633, 1, 0, 3.1, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 0, 2.25, -1, 2, 2.267, -1, 0, 2.583, 0.2, 0, 2.767, -0.182, 0, 2.917, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "dafengshangzhuanchang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.5, 0, 0, 3.917, 1, 2, 3.933, 1, 0, 4, 10, 2, 4.067, 10, 2, 4.083, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 0, 1.733, -0.3, 1, 1.861, -0.3, 1.989, -0.248, 2.117, -0.078, 1, 2.161, -0.019, 2.206, 0.077, 2.25, 0.077, 0, 2.35, 0, 1, 2.4, 0, 2.45, -0.019, 2.5, 0.1, 1, 2.561, 0.245, 2.622, 0.7, 2.683, 0.7, 0, 2.983, 0.1, 0, 3.233, 0.8, 0, 4.2, 0, 2, 5, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY_SD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 0.583, 0, 2, 1.733, 0, 1, 1.861, 0, 1.989, 0.115, 2.117, 0.314, 1, 2.161, 0.383, 2.206, 0.426, 2.25, 0.497, 1, 2.283, 0.551, 2.317, 0.6, 2.35, 0.6, 2, 2.5, 0.6, 1, 2.561, 0.6, 2.622, 0.311, 2.683, 0.2, 1, 2.783, 0.019, 2.883, 0, 2.983, 0, 2, 4.2, 0, 2, 5, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.5, 1, 2, 0.583, 1, 2, 1.333, 1, 0, 1.783, 0, 2, 2.25, 0, 2, 2.983, 0, 0, 3.467, 1, 2, 4.2, 1, 2, 5, 1, 2, 5.15, 1, 2, 5.417, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.5, 1, 2, 0.583, 1, 2, 1.333, 1, 0, 1.783, 0, 2, 2.25, 0, 2, 2.983, 0, 0, 3.467, 1, 2, 4.2, 1, 2, 5, 1, 2, 5.15, 1, 2, 5.417, 1]}, {"Target": "Parameter", "Id": "EyeFrame_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 0, 0.583, -0.8, 2, 0.633, -0.8, 1, 1.172, -0.8, 1.711, -0.456, 2.25, -0.062, 1, 2.361, 0.019, 2.472, 0, 2.583, 0, 0, 3.467, -0.6, 0, 5, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamBrowForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 0, 0.583, -1, 2, 1.183, -1, 1, 1.539, -1, 1.894, -0.944, 2.25, -0.713, 1, 2.9, -0.291, 3.55, 0, 4.2, 0, 2, 5, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamEyeLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.16, 2, 0.5, -0.16, 0, 0.583, 0.025, 1, 0.7, 0.026, 0.816, 0.026, 0.933, 0.026, 2, 0.95, 0.026, 0, 1.583, 0.774, 0, 1.9, -0.4, 0, 2.217, 0.11, 1, 2.228, 0.11, 2.239, 0.114, 2.25, 0.105, 1, 2.339, 0.033, 2.428, -0.031, 2.517, -0.031, 0, 2.833, 0.009, 0, 3.233, -0.744, 0, 3.567, 0.378, 0, 3.883, -0.104, 0, 4.2, 0.029, 0, 4.5, -0.008, 0, 4.8, 0.002, 2, 4.833, 0.002, 1, 4.889, 0.002, 4.944, 0.001, 5, 0, 1, 5.028, -0.001, 5.055, -0.001, 5.083, -0.001, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamEyeRsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.16, 2, 0.5, -0.16, 0, 0.583, 0.025, 1, 0.7, 0.026, 0.816, 0.026, 0.933, 0.026, 2, 0.95, 0.026, 0, 1.583, 0.774, 0, 1.9, -0.4, 0, 2.217, 0.11, 1, 2.228, 0.11, 2.239, 0.114, 2.25, 0.105, 1, 2.339, 0.033, 2.428, -0.031, 2.517, -0.031, 0, 2.833, 0.009, 0, 3.233, -0.744, 0, 3.567, 0.378, 0, 3.883, -0.104, 0, 4.2, 0.029, 0, 4.5, -0.008, 0, 4.8, 0.002, 2, 4.833, 0.002, 1, 4.889, 0.002, 4.944, 0.001, 5, 0, 1, 5.028, -0.001, 5.055, -0.001, 5.083, -0.001, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamEyeLGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.181, 2, 0.5, -0.181, 0, 0.583, 0, 2, 0.6, 0, 2, 0.633, 0, 2, 0.65, 0, 2, 0.717, 0, 2, 0.733, 0, 2, 0.75, 0, 2, 0.767, 0, 2, 0.783, 0, 2, 0.817, 0, 2, 0.833, 0, 2, 0.883, 0, 2, 0.9, 0, 2, 0.917, 0, 2, 0.933, 0, 2, 0.95, 0, 2, 0.983, 0, 2, 1, 0, 2, 1.1, 0, 2, 1.117, 0, 2, 1.133, 0, 2, 1.15, 0, 2, 1.267, 0, 2, 1.283, 0, 2, 1.3, 0, 2, 1.317, 0, 2, 1.333, 0, 0, 1.483, -0.168, 0, 1.8, 0.334, 0, 2.067, -0.255, 1, 2.128, -0.255, 2.189, -0.157, 2.25, -0.006, 1, 2.289, 0.09, 2.328, 0.12, 2.367, 0.12, 0, 2.667, -0.048, 0, 3.133, 0.167, 0, 3.467, -0.296, 0, 3.733, 0.24, 0, 4.033, -0.114, 0, 4.35, 0.046, 0, 4.65, -0.017, 0, 4.95, 0.006, 2, 4.967, 0.006, 0, 5, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamEyeRGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.181, 2, 0.5, -0.181, 0, 0.583, 0, 2, 0.6, 0, 2, 0.633, 0, 2, 0.65, 0, 2, 0.717, 0, 2, 0.733, 0, 2, 0.75, 0, 2, 0.767, 0, 2, 0.783, 0, 2, 0.817, 0, 2, 0.833, 0, 2, 0.883, 0, 2, 0.9, 0, 2, 0.917, 0, 2, 0.933, 0, 2, 0.95, 0, 2, 0.983, 0, 2, 1, 0, 2, 1.1, 0, 2, 1.117, 0, 2, 1.133, 0, 2, 1.15, 0, 2, 1.267, 0, 2, 1.283, 0, 2, 1.3, 0, 2, 1.317, 0, 2, 1.333, 0, 0, 1.483, -0.168, 0, 1.8, 0.334, 0, 2.067, -0.255, 1, 2.128, -0.255, 2.189, -0.157, 2.25, -0.006, 1, 2.289, 0.09, 2.328, 0.12, 2.367, 0.12, 0, 2.667, -0.048, 0, 3.133, 0.167, 0, 3.467, -0.296, 0, 3.733, 0.24, 0, 4.033, -0.114, 0, 4.35, 0.046, 0, 4.65, -0.017, 0, 4.95, 0.006, 2, 4.967, 0.006, 0, 5, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 0, 0.583, 3.393, 1, 0.983, 3.393, 1.383, 3.19, 1.783, 2.438, 1, 1.939, 2.146, 2.094, 1.393, 2.25, 0.796, 1, 2.372, 0.327, 2.495, 0, 2.617, 0, 2, 5, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.5, 1, 2, 0.583, 1, 0, 1.25, 0.5, 2, 1.433, 0.5, 2, 1.75, 0.5, 1, 1.917, 0.5, 2.083, 0.525, 2.25, 0.637, 1, 2.567, 0.85, 2.883, 1, 3.2, 1, 1, 3.378, 0.667, 3.555, 0.333, 3.733, 0, 2, 4.2, 0, 2, 5, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 0.583, 0, 2, 0.683, 0, 1, 0.933, 1.58, 1.183, 3.16, 1.433, 4.74, 1, 1.539, 4.727, 1.644, 4.713, 1.75, 4.7, 1, 1.917, 4.46, 2.083, 4.22, 2.25, 3.98, 1, 2.261, 3.933, 2.272, 3.887, 2.283, 3.84, 1, 2.583, 2.56, 2.883, 1.28, 3.183, 0, 1, 3.283, 1.28, 3.383, 2.56, 3.483, 3.84, 1, 3.566, 2.56, 3.65, 1.28, 3.733, 0, 2, 4.2, 0, 2, 5, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 0.583, 0, 2, 1.433, 0, 2, 1.75, 0, 1, 1.917, 0, 2.083, -9.016, 2.25, -17.739, 1, 2.261, -18.321, 2.272, -17.94, 2.283, -17.94, 1, 2.583, -17.94, 2.883, -17.97, 3.183, -15.498, 1, 3.366, -13.988, 3.55, 13.51, 3.733, 22, 1, 3.889, 29.204, 4.044, 30, 4.2, 30, 2, 5, 30, 2, 5.15, 30, 2, 5.417, 30]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 3.2, 0, 0, 3.667, -30, 1, 3.695, -30, 3.722, -11.4, 3.75, -10, 1, 3.9, -2.441, 4.05, 0, 4.2, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.641, 2, 0.5, 0.641, 1, 0.528, 0.761, 0.555, 0.88, 0.583, 1, 1, 0.666, 0.333, 0.75, -0.333, 0.833, -1, 2, 1, -1, 2, 1.75, -1, 1, 1.917, -0.77, 2.083, -0.54, 2.25, -0.31, 1, 2.567, 0.127, 2.883, 0.563, 3.2, 1, 1, 3.294, 0.333, 3.389, -0.333, 3.483, -1, 2, 4.2, -1, 2, 5, -1, 2, 5.15, -1, 2, 5.417, -1]}, {"Target": "Parameter", "Id": "ParamHandLDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10, 2, 0.5, 10, 2, 0.583, 10, 2, 0.833, 1, 2, 1, 1, 2, 1.75, 1, 2, 2.25, 1, 2, 3.717, 1, 2, 3.733, 10, 2, 4.2, 10, 2, 5, 10, 2, 5.15, 10, 2, 5.417, 10]}, {"Target": "Parameter", "Id": "XIONGJIYA2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 1, 0.528, 0.333, 0.555, 0.667, 0.583, 1, 2, 2.25, 1, 2, 3.817, 1, 0, 3.867, 0, 2, 5, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 0.583, 0, 2, 2.25, 0, 2, 3.2, 0, 2, 3.683, 0, 0, 3.75, 1, 0, 3.933, 0, 2, 4.2, 0, 2, 5, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.5, 1, 2, 0.583, 1, 0, 1.25, 0.5, 2, 1.75, 0.5, 1, 1.917, 0.5, 2.083, 0.525, 2.25, 0.637, 1, 2.567, 0.85, 2.883, 1, 3.2, 1, 1, 3.378, 0.667, 3.555, 0.333, 3.733, 0, 2, 4.2, 0, 2, 5, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 0.583, 0, 2, 0.683, 0, 1, 0.933, -1.74, 1.183, -3.48, 1.433, -5.22, 1, 1.539, -4.827, 1.644, -4.433, 1.75, -4.04, 1, 1.917, -3.574, 2.083, -3.109, 2.25, -2.643, 1, 2.261, -2.612, 2.272, -2.581, 2.283, -2.55, 1, 2.583, -1.7, 2.883, -0.85, 3.183, 0, 1, 3.283, -1.1, 3.383, -2.2, 3.483, -3.3, 1, 3.566, -2.2, 3.65, -1.1, 3.733, 0, 2, 4.2, 0, 2, 5, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.5, 1, 2, 0.583, 1, 2, 2.25, 1, 2, 3.7, 1, 0, 5, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 0.583, 0, 2, 1.433, 0, 2, 1.75, 0, 1, 1.917, 6.225, 2.083, 12.45, 2.25, 18.675, 1, 2.261, 19.09, 2.272, 19.505, 2.283, 19.92, 1, 2.583, 17.902, 2.883, 15.884, 3.183, 13.866, 1, 3.366, 9.244, 3.55, 4.622, 3.733, 0, 2, 4.2, 0, 2, 5, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamForearm<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.5, 1, 2, 0.583, 1, 1, 0.666, 0.333, 0.75, -0.333, 0.833, -1, 2, 1.017, -1, 2, 1.75, -1, 1, 1.917, -0.77, 2.083, -0.54, 2.25, -0.31, 1, 2.567, 0.127, 2.883, 0.563, 3.2, 1, 2, 3.733, 1, 2, 5, 1, 2, 5.15, 1, 2, 5.417, 1]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 1, 0.528, -0.033, 0.555, -0.066, 0.583, -0.099, 1, 0.972, -0.136, 1.361, -0.173, 1.75, -0.21, 1, 1.917, -0.222, 2.083, -0.235, 2.25, -0.247, 1, 2.261, -0.248, 2.272, -0.249, 2.283, -0.25, 1, 2.583, 0.006, 2.883, 0.262, 3.183, 0.518, 1, 3.366, 0.345, 3.55, 0.173, 3.733, 0, 2, 4.2, 0, 2, 5, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamHandRDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 0.583, 0, 2, 0.833, 1, 2, 1.017, 1, 2, 1.75, 1, 2, 2.25, 1, 2, 3.717, 1, 2, 3.733, 0, 2, 4.2, 0, 2, 5, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 1, 0.528, -0.236, 0.555, -0.472, 0.583, -0.708, 1, 0.728, -0.805, 0.872, -0.903, 1.017, -1, 0, 1.433, 0, 2, 1.85, 0, 1, 1.983, 0, 2.117, 0.464, 2.25, 0.966, 1, 2.267, 1, 2.283, 1, 2.3, 1, 0, 2.55, -1, 2, 2.65, -1, 0, 3.2, 0, 2, 3.733, 0, 2, 4.2, 0, 2, 5, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 1, 0.528, -3.684, 0.555, -7.369, 0.583, -11.053, 1, 0.866, -14.035, 1.15, -17.018, 1.433, -20, 1, 1.705, -16.918, 1.978, -13.837, 2.25, -10.755, 1, 2.567, -7.17, 2.883, -3.585, 3.2, 0, 2, 3.733, 0, 2, 4.2, 0, 2, 5, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.5, 1, 2, 0.583, 1, 2, 2.25, 1, 2, 3.483, 1, 2, 3.733, 1, 2, 3.75, 0, 2, 4.233, 0, 2, 5, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 2.25, 0, 1, 2.256, 0.333, 2.261, 0.667, 2.267, 1, 2, 3.733, 1, 2, 3.75, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 0.583, 0, 1, 1.139, -0.075, 1.694, -0.151, 2.25, -0.226, 2, 3.733, -0.226, 0, 5, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 0.583, 0, 1, 1.139, -0.047, 1.694, -0.093, 2.25, -0.14, 2, 3.733, -0.14, 0, 5, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 0.583, 0, 1, 1.139, -0.167, 1.694, -0.335, 2.25, -0.502, 1, 2.45, -0.502, 2.65, -0.198, 2.85, 0, 1, 3.144, 0.291, 3.439, 0.342, 3.733, 0.342, 0, 5, 0, 2, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.583, 2, 0.5, -3.583, 0, 1.667, 2.667, 1, 1.861, 2.667, 2.056, 1.586, 2.25, -0.527, 1, 2.439, -2.58, 2.628, -3.583, 2.817, -3.583, 0, 4.033, 2.667, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10, 2, 0.5, 10, 2, 2.25, 10, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -7.795, 2, 0.5, -7.795, 1, 0.883, -7.795, 1.267, -4.621, 1.65, 0, 1, 1.85, 2.411, 2.05, 4.395, 2.25, 5.969, 1, 2.439, 7.455, 2.628, 7.795, 2.817, 7.795, 1, 3.206, 7.795, 3.594, 5.197, 3.983, 0, 1, 4.372, -5.197, 4.761, -7.795, 5.15, -7.795, 2, 5.417, -7.795]}, {"Target": "Parameter", "Id": "ParamBodyStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 0, 2.05, -10, 1, 2.117, -10, 2.183, -10, 2.25, -9.691, 1, 2.961, -2.202, 3.672, 4.862, 4.383, 4.862, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 0, 1.55, -10, 1, 1.783, -10, 2.017, -9.254, 2.25, -6.872, 1, 2.439, -4.944, 2.628, -2.587, 2.817, 0.147, 1, 3.228, 6.099, 3.639, 9.828, 4.05, 9.828, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamLegLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10.58, 2, 0.5, 10.58, 2, 1.633, 10.58, 1, 1.839, 10.58, 2.044, 9.791, 2.25, 8.674, 1, 2.8, 5.686, 3.35, 0.29, 3.9, 0.098, 1, 4.317, -0.048, 4.733, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamCrusLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 1, 1.083, 0, 1.667, 1.603, 2.25, 3.568, 1, 2.428, 4.167, 2.605, 4.14, 2.783, 4.14, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamLegRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 7.86, 2, 0.5, 7.86, 2, 1.633, 7.86, 1, 1.839, 7.86, 2.044, 7.756, 2.25, 5.954, 1, 2.8, 1.133, 3.35, -2.622, 3.9, -2.622, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamCrusRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 1, 1.083, 0, 1.667, 2.484, 2.25, 5.532, 1, 2.428, 6.461, 2.605, 6.42, 2.783, 6.42, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.5, 1, 2, 2.25, 1, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "physics_Add_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -30, 2, 0.5, -30, 2, 2.25, -30, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "Physics_AngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.566, 2, 0.5, 1.566, 0, 1.683, 1.619, 1, 1.872, 1.619, 2.061, 1.014, 2.25, -0.225, 1, 2.444, -1.5, 2.639, -2.152, 2.833, -2.152, 0, 4.05, 1.615, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 3.075, 2, 0.5, 3.075, 0, 1.667, -1.778, 1, 1.861, -1.778, 2.056, -1.058, 2.25, 0.351, 1, 2.439, 1.72, 2.628, 2.389, 2.817, 2.389, 0, 4.033, -1.778, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -9.285, 2, 0.5, -9.285, 0, 1.667, 1.778, 1, 1.861, 1.778, 2.056, 1.058, 2.25, -0.351, 1, 2.439, -1.72, 2.628, -2.389, 2.817, -2.389, 0, 4.033, 1.778, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_L", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.622, 2, 0.5, 0.622, 2, 1.667, 0.622, 1, 1.861, 0.622, 2.056, 0.37, 2.25, -0.123, 1, 2.439, -0.602, 2.628, -0.836, 2.817, -0.836, 0, 4.033, 0.622, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_R", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.622, 2, 0.5, -0.622, 2, 1.667, -0.622, 1, 1.861, -0.622, 2.056, -0.37, 2.25, 0.123, 1, 2.439, 0.602, 2.628, 0.836, 2.817, 0.836, 0, 4.033, -0.622, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10.902, 2, 0.5, 10.902, 0, 1.667, -3.556, 1, 1.861, -3.556, 2.056, -2.116, 2.25, 0.702, 1, 2.439, 3.439, 2.628, 4.777, 2.817, 4.777, 0, 4.033, -3.556, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.345, 2, 0.5, 0.345, 0, 1.667, 3.556, 1, 1.861, 3.556, 2.056, 2.116, 2.25, -0.702, 1, 2.439, -3.439, 2.628, -4.777, 2.817, -4.777, 0, 4.033, 3.556, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 9.721, 2, 0.5, 9.721, 1, 0.889, 9.721, 1.278, 7.626, 1.667, 3.556, 1, 1.861, 1.521, 2.056, -0.646, 2.25, -2.431, 1, 2.439, -4.165, 2.628, -4.777, 2.817, -4.777, 0, 4.033, 3.556, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.732, 2, 0.5, -2.732, 0, 1.667, -3.556, 1, 1.861, -3.556, 2.056, -2.116, 2.25, 0.702, 1, 2.439, 3.439, 2.628, 4.777, 2.817, 4.777, 0, 4.033, -3.556, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "Jewelry_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.766, 2, 0.5, 0.766, 0, 1.667, -0.296, 1, 1.861, -0.296, 2.056, -0.176, 2.25, 0.059, 1, 2.439, 0.287, 2.628, 0.398, 2.817, 0.398, 0, 4.033, -0.296, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "Jewelry_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.302, 2, 0.5, 0.302, 1, 0.889, 0.302, 1.278, 0.302, 1.667, 0.296, 1, 1.861, 0.293, 2.056, 0.095, 2.25, -0.061, 1, 2.439, -0.213, 2.628, -0.398, 2.817, -0.398, 0, 4.033, 0.296, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "Jewelry_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.739, 2, 0.5, -1.739, 2, 2.25, -1.739, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.715, 2, 0.5, 0.715, 0, 0.517, -0.093, 0, 1, 0.02, 0, 1.267, -0.001, 2, 1.283, -0.001, 0, 1.867, 0.102, 2, 1.883, 0.102, 1, 2.005, 0.102, 2.128, 0.082, 2.25, 0.055, 1, 2.3, 0.044, 2.35, 0.043, 2.4, 0.043, 2, 2.417, 0.043, 0, 2.5, 0.044, 2, 2.533, 0.044, 0, 3.083, -0.021, 2, 3.133, -0.021, 0, 3.583, -0.067, 0, 4.017, -0.005, 0, 4.433, -0.035, 1, 4.555, -0.035, 4.678, -0.033, 4.8, -0.022, 1, 4.917, -0.011, 5.033, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 4.926, 2, 0.5, 4.926, 0, 0.733, -0.381, 0, 1.233, -0.13, 0, 1.4, -0.141, 0, 2.183, 0.287, 1, 2.205, 0.287, 2.228, 0.287, 2.25, 0.283, 1, 2.322, 0.271, 2.395, 0.262, 2.467, 0.262, 0, 2.75, 0.326, 0, 3.833, -0.158, 0, 4.2, -0.073, 0, 4.667, -0.154, 0, 4.783, -0.148, 0, 4.85, -0.156, 1, 4.922, -0.156, 4.995, -0.155, 5.067, -0.143, 1, 5.095, -0.138, 5.122, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.298, 2, 0.5, 2.298, 0, 1.667, -0.889, 1, 1.861, -0.889, 2.056, -0.529, 2.25, 0.176, 1, 2.439, 0.861, 2.628, 1.195, 2.817, 1.195, 0, 4.033, -0.889, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.907, 2, 0.5, 0.907, 1, 0.889, 0.907, 1.278, 0.907, 1.667, 0.889, 1, 1.861, 0.88, 2.056, 0.286, 2.25, -0.184, 1, 2.439, -0.64, 2.628, -1.195, 2.817, -1.195, 0, 4.033, 0.889, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.218, 2, 0.5, -5.218, 2, 2.25, -5.218, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -13.924, 2, 0.5, -13.924, 2, 2.25, -13.924, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -13.919, 2, 0.5, -13.919, 2, 2.25, -13.919, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.635, 2, 0.5, 1.635, 2, 2.25, 1.635, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 19.522, 2, 0.5, 19.522, 2, 2.25, 19.522, 0, 5.15, 0, 2, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 0, 1.667, 1, 0, 3.5, 0, 0, 4.667, 1, 1, 4.917, 1, 5.167, 0.833, 5.417, 0.635]}, {"Target": "Parameter", "Id": "fenshangxuanzhuang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 1, 2.139, 0.656, 3.778, 1.312, 5.417, 1.968]}, {"Target": "Parameter", "Id": "ParamSad2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 5.417, 1]}, {"Target": "Parameter", "Id": "ParamCRY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.2, 0, 5.417, -5.2]}, {"Target": "Parameter", "Id": "Position_Coordinates", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 5.417, 1]}, {"Target": "Parameter", "Id": "Man_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 5.417, 1]}, {"Target": "Parameter", "Id": "Man_TMD2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "BG_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 5.417, 1]}, {"Target": "Parameter", "Id": "FG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "BG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "BG_White4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "BG_White5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "BG_White2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "BG_White3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "All_Size2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "All_Size3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "All_Size4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "tuerzuo3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "faqiehuan4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Mail_Change", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "tongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamTongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "EyeFrame_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamBrowAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamBrowY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamSigh", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamDarken", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamCry", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_B", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_C", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "MB_Unidirectional_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "jing<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "huqi1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "huqi2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamShoulderLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamArmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 5.417, 1]}, {"Target": "Parameter", "Id": "ParamForearmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamArmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamArmL_ForearmL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamForearmL_HandL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamHandLSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "xiongjiya", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "xiongjiya5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "xiongjiya3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "xiongjiya4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "xiongjiya2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamShoulderRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamArmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 5.417, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamArmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamArmR_ForearmR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_HandR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamHandRSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 5.417, 0.6]}, {"Target": "Parameter", "Id": "Change_idle_physics23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Param103", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "taiqiugundong", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -22.5, 0, 5.417, -22.5]}, {"Target": "Parameter", "Id": "Change_idle_physics32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamLegL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 5.417, 1]}, {"Target": "Parameter", "Id": "ParamLegLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamCrusLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>oodLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamFoodLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamFoodLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamLegR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 5.417, 1]}, {"Target": "Parameter", "Id": "ParamLegRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamCrusRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamCrus<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>ood<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamFoodRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamFoodRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Physics_Provisional", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "physics_Add_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamBodyStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Physics_AngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Physics_AngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Physics_Body_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Physics_Body_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Physics_breast", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamBreath2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "touch_drag1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "touch_drag2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "touch_drag3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "touch_drag4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "touch_drag5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "touch_drag6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "touch_drag7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "touch_drag8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "touch_drag9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>pian<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh654", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh655", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh657", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh656", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh658", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh659", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh660", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.417, 0]}], "UserData": [{"Time": 4.917, "Value": ""}]}