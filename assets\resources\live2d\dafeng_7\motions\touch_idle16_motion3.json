{"Version": 3, "Meta": {"Duration": 5.0, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 304, "TotalSegmentCount": 856, "TotalPointCount": 1224, "UserDataCount": 1, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Mail_Transparency6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.5, 1, 2, 2.25, 1, 1, 3.167, 1, 4.083, 0.306, 5, 0.074]}, {"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.371, 2, 0.25, 2.371, 0, 1.667, 3.871, 2, 1.75, 3.871, 1, 1.917, -1.369, 2.083, -6.609, 2.25, -11.849, 2, 2.267, 0.031, 1, 2.467, 0.565, 2.667, 0.811, 2.867, 0.811, 0, 4.667, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.047, 2, 0.25, -1.047, 0, 1.667, -2.727, 2, 1.75, -2.727, 1, 1.917, -1.627, 2.083, -0.527, 2.25, 0.573, 2, 2.267, 7.173, 1, 2.467, 7.173, 2.667, 0.763, 2.867, 0.59, 1, 3.467, 0.071, 4.067, 0, 4.667, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.25, 0, 2, 2.267, 5.58, 0, 4.667, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 9.705, 2, 0.25, 9.705, 0, 1.667, 16.457, 2, 1.75, 16.457, 2, 2.25, 16.457, 2, 2.267, 7.337, 1, 3.067, 2.414, 3.867, 0, 4.667, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "FG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 1.833, 0, 0, 2.267, 0.9, 0, 3.983, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "BG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.5, 1, 2, 2.25, 1, 1, 3.167, 1, 4.083, 0.306, 5, 0.074]}, {"Target": "Parameter", "Id": "faqiehuan", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.5, 2, 0.5, 0.5, 2, 2.25, 0.5, 2, 2.267, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "faqiehuan2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.5, 2, 0.5, 0.5, 2, 2.25, 0.5, 2, 2.267, 0, 2, 3.567, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "faqiehuan3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.5, 1, 2, 0.75, 1, 0, 1, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "tuerzuo4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 0, 1.333, 0.397, 2, 1.75, 0.397, 1, 1.917, 0.598, 2.083, 0.799, 2.25, 1, 1, 2.456, 1, 2.661, 0.593, 2.867, 0.445, 1, 3.378, 0.077, 3.889, 0, 4.4, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 1.917, 0, 2, 2.083, 0, 0, 2.267, 1, 0, 2.733, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 0, 2.25, -1, 2, 2.267, -1, 0, 2.733, 0.2, 0, 3.017, -0.182, 0, 3.233, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 0, 1.733, -0.3, 1, 1.861, -0.3, 1.989, -0.248, 2.117, -0.078, 1, 2.161, -0.019, 2.206, 0.077, 2.25, 0.077, 0, 2.383, 0, 1, 2.461, 0, 2.539, -0.017, 2.617, 0.1, 1, 2.706, 0.234, 2.794, 0.7, 2.883, 0.7, 0, 3.333, 0.1, 0, 3.633, 0.8, 0, 4.6, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY_SD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 0.583, 0, 2, 1.733, 0, 1, 1.861, 0, 1.989, 0.115, 2.117, 0.314, 1, 2.161, 0.383, 2.206, 0.433, 2.25, 0.497, 1, 2.294, 0.562, 2.339, 0.6, 2.383, 0.6, 2, 2.617, 0.6, 1, 2.706, 0.6, 2.794, 0.307, 2.883, 0.2, 1, 3.033, 0.019, 3.183, 0, 3.333, 0, 2, 4.6, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.5, 1, 2, 0.583, 1, 2, 1.333, 1, 0, 1.783, 0, 2, 2.25, 0, 2, 3.333, 0, 0, 3.867, 1, 2, 4.6, 1, 2, 5, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.5, 1, 2, 0.583, 1, 2, 1.333, 1, 0, 1.783, 0, 2, 2.25, 0, 2, 3.333, 0, 0, 3.867, 1, 2, 4.6, 1, 2, 5, 1]}, {"Target": "Parameter", "Id": "EyeFrame_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 0, 0.583, -0.8, 2, 0.633, -0.8, 1, 1.172, -0.8, 1.711, -0.372, 2.25, -0.062, 1, 2.411, 0.031, 2.572, 0, 2.733, 0, 0, 3.867, -0.6, 1, 4.245, -0.6, 4.622, -0.272, 5, -0.101]}, {"Target": "Parameter", "Id": "ParamBrowForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 0, 0.583, -1, 2, 1.183, -1, 1, 1.539, -1, 1.894, -0.92, 2.25, -0.713, 1, 3.033, -0.256, 3.817, 0, 4.6, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "ParamEyeLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.16, 2, 0.5, -0.16, 0, 0.583, 0.025, 1, 0.7, 0.026, 0.816, 0.026, 0.933, 0.026, 2, 0.95, 0.026, 0, 1.583, 0.774, 0, 1.9, -0.4, 0, 2.217, 0.11, 1, 2.228, 0.11, 2.239, 0.112, 2.25, 0.105, 1, 2.378, 0.023, 2.505, -0.031, 2.633, -0.031, 0, 3.117, 0.009, 0, 3.633, -0.744, 0, 3.967, 0.378, 0, 4.283, -0.104, 0, 4.6, 0.029, 0, 4.9, -0.008, 1, 4.933, -0.008, 4.967, -0.007, 5, -0.005]}, {"Target": "Parameter", "Id": "ParamEyeRsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.16, 2, 0.5, -0.16, 0, 0.583, 0.025, 1, 0.7, 0.026, 0.816, 0.026, 0.933, 0.026, 2, 0.95, 0.026, 0, 1.583, 0.774, 0, 1.9, -0.4, 0, 2.217, 0.11, 1, 2.228, 0.11, 2.239, 0.112, 2.25, 0.105, 1, 2.378, 0.023, 2.505, -0.031, 2.633, -0.031, 0, 3.117, 0.009, 0, 3.633, -0.744, 0, 3.967, 0.378, 0, 4.283, -0.104, 0, 4.6, 0.029, 0, 4.9, -0.008, 1, 4.933, -0.008, 4.967, -0.007, 5, -0.005]}, {"Target": "Parameter", "Id": "ParamEyeLGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.181, 2, 0.5, -0.181, 0, 0.583, 0, 2, 0.6, 0, 2, 0.633, 0, 2, 0.65, 0, 2, 0.717, 0, 2, 0.733, 0, 2, 0.75, 0, 2, 0.767, 0, 2, 0.783, 0, 2, 0.817, 0, 2, 0.833, 0, 2, 0.883, 0, 2, 0.9, 0, 2, 0.917, 0, 2, 0.933, 0, 2, 0.95, 0, 2, 0.983, 0, 2, 1, 0, 2, 1.1, 0, 2, 1.117, 0, 2, 1.133, 0, 2, 1.15, 0, 2, 1.267, 0, 2, 1.283, 0, 2, 1.3, 0, 2, 1.317, 0, 2, 1.333, 0, 0, 1.483, -0.168, 0, 1.8, 0.334, 0, 2.067, -0.255, 1, 2.128, -0.255, 2.189, -0.126, 2.25, -0.006, 1, 2.306, 0.103, 2.361, 0.12, 2.417, 0.12, 0, 2.867, -0.048, 0, 3.533, 0.167, 0, 3.867, -0.296, 0, 4.133, 0.24, 0, 4.433, -0.114, 0, 4.75, 0.046, 1, 4.833, 0.046, 4.917, 0.002, 5, -0.012]}, {"Target": "Parameter", "Id": "ParamEyeRGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.181, 2, 0.5, -0.181, 0, 0.583, 0, 2, 0.6, 0, 2, 0.633, 0, 2, 0.65, 0, 2, 0.717, 0, 2, 0.733, 0, 2, 0.75, 0, 2, 0.767, 0, 2, 0.783, 0, 2, 0.817, 0, 2, 0.833, 0, 2, 0.883, 0, 2, 0.9, 0, 2, 0.917, 0, 2, 0.933, 0, 2, 0.95, 0, 2, 0.983, 0, 2, 1, 0, 2, 1.1, 0, 2, 1.117, 0, 2, 1.133, 0, 2, 1.15, 0, 2, 1.267, 0, 2, 1.283, 0, 2, 1.3, 0, 2, 1.317, 0, 2, 1.333, 0, 0, 1.483, -0.168, 0, 1.8, 0.334, 0, 2.067, -0.255, 1, 2.128, -0.255, 2.189, -0.126, 2.25, -0.006, 1, 2.306, 0.103, 2.361, 0.12, 2.417, 0.12, 0, 2.867, -0.048, 0, 3.533, 0.167, 0, 3.867, -0.296, 0, 4.133, 0.24, 0, 4.433, -0.114, 0, 4.75, 0.046, 1, 4.833, 0.046, 4.917, 0.002, 5, -0.012]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 0, 0.583, 3.393, 1, 0.983, 3.393, 1.383, 3.19, 1.783, 2.438, 1, 1.939, 2.146, 2.094, 1.296, 2.25, 0.796, 1, 2.428, 0.225, 2.605, 0, 2.783, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.5, 1, 2, 0.583, 1, 0, 1.25, 0.5, 2, 1.433, 0.5, 2, 1.75, 0.5, 0, 2.25, 0.1, 0, 2.417, 0.17, 2, 2.433, 0, 2, 2.983, 0, 2, 4.133, 0, 2, 4.6, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 0.583, 0, 2, 0.683, 0, 1, 0.933, 1.58, 1.183, 3.16, 1.433, 4.74, 1, 1.539, 4.727, 1.644, 4.713, 1.75, 4.7, 1, 2.039, 3.87, 2.328, 3.04, 2.617, 2.21, 1, 2.739, 1.54, 2.861, 0.871, 2.983, 0.201, 1, 3.366, 0.134, 3.75, 0.067, 4.133, 0, 2, 4.6, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 2.25, 0, 1, 2.311, -4.1, 2.372, -8.2, 2.433, -12.3, 1, 2.822, -8.2, 3.211, -4.1, 3.6, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 0.583, 0, 2, 1.433, 0, 2, 1.75, 0, 0, 2.25, -0.639, 1, 2.306, 0.342, 2.361, 1.322, 2.417, 2.303, 1, 2.422, 3.355, 2.428, 4.406, 2.433, 5.458, 1, 2.616, 11.707, 2.8, 22.072, 2.983, 23.811, 1, 3.522, 28.922, 4.061, 30, 4.6, 30, 2, 5, 30]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 2.25, 0, 1, 2.311, -6.667, 2.372, -13.333, 2.433, -20, 1, 2.822, -13.333, 3.211, -6.667, 3.6, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.641, 2, 0.5, 0.641, 1, 0.528, 0.761, 0.555, 0.88, 0.583, 1, 1, 0.666, 0.333, 0.75, -0.333, 0.833, -1, 2, 1.75, -1, 1, 1.917, -0.77, 2.083, -0.54, 2.25, -0.31, 1, 2.378, -0.54, 2.505, -0.77, 2.633, -1, 2, 5, -1]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 0.583, 0, 2, 1.75, 0, 2, 2.25, 0, 1, 2.311, -0.07, 2.372, -0.14, 2.433, -0.21, 1, 2.816, -0.14, 3.2, -0.07, 3.583, 0, 2, 4.133, 0, 2, 4.6, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "ParamHandLDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10, 2, 0.5, 10, 2, 0.817, 10, 2, 0.833, 1, 2, 2.417, 1, 2, 2.433, 3, 2, 2.633, 3, 2, 2.667, 10, 2, 5, 10]}, {"Target": "Parameter", "Id": "XIONGJIYA2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 1, 0.528, 0.333, 0.555, 0.667, 0.583, 1, 2, 2.25, 1, 2, 4.217, 1, 0, 4.267, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 0.583, 0, 2, 2.25, 0, 2, 3.6, 0, 2, 4.083, 0, 0, 4.15, 1, 0, 4.333, 0, 2, 4.6, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.5, 1, 2, 0.583, 1, 0, 1.25, 0.5, 2, 1.75, 0.5, 0, 2.25, 0.087, 0, 2.417, 0.12, 2, 2.433, 0, 2, 2.983, 0, 2, 4.133, 0, 2, 4.6, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 0.583, 0, 2, 0.683, 0, 1, 0.933, -1.74, 1.183, -3.48, 1.433, -5.22, 1, 1.539, -4.827, 1.644, -4.433, 1.75, -4.04, 1, 1.917, -3.054, 2.083, -2.069, 2.25, -1.083, 1, 2.372, -0.985, 2.495, -0.888, 2.617, -0.79, 1, 2.739, -0.334, 2.861, 0.123, 2.983, 0.579, 1, 3.366, 0.386, 3.75, 0.193, 4.133, 0, 2, 4.6, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 0.583, 0, 2, 2.25, 0, 1, 2.311, -3.14, 2.372, -6.28, 2.433, -9.42, 1, 2.822, -6.28, 3.211, -3.14, 3.6, 0, 2, 4.133, 0, 2, 4.6, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.5, 1, 2, 0.583, 1, 2, 2.25, 1, 2, 4.1, 1, 1, 4.4, 1, 4.7, 0.521, 5, 0.226]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 0.583, 0, 2, 1.433, 0, 2, 1.75, 0, 0, 2.25, 1.635, 1, 2.306, 1.635, 2.361, -0.271, 2.417, -4.197, 1, 2.422, -4.59, 2.428, -4.85, 2.433, -4.85, 0, 3.233, 0.72, 1, 3.389, 0.72, 3.544, 0.211, 3.7, 0.093, 1, 3.844, -0.017, 3.989, 0, 4.133, 0, 2, 4.6, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 0.583, 0, 2, 2.25, 0, 1, 2.311, -5.42, 2.372, -10.84, 2.433, -16.26, 1, 2.822, -10.84, 3.211, -5.42, 3.6, 0, 2, 4.133, 0, 2, 4.6, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "ParamForearm<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.5, 1, 2, 0.583, 1, 1, 0.666, 0.333, 0.75, -0.333, 0.833, -1, 2, 1.017, -1, 2, 1.75, -1, 1, 1.917, -0.333, 2.083, 0.333, 2.25, 1, 2, 5, 1]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 1, 0.528, 0, 0.555, -0.094, 0.583, -0.099, 1, 1.139, -0.202, 1.694, -0.247, 2.25, -0.247, 1, 2.489, -0.247, 2.728, -0.227, 2.967, -0.155, 1, 3.056, -0.128, 3.144, -0.009, 3.233, -0.007, 1, 3.533, -0.001, 3.833, 0, 4.133, 0, 2, 4.6, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "ParamHandRDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 0.817, 0, 2, 0.833, 1, 2, 2.433, 3, 2, 2.733, 0, 2, 4.133, 0, 2, 4.6, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 1, 0.528, -0.236, 0.555, -0.472, 0.583, -0.708, 1, 0.728, -0.805, 0.872, -0.903, 1.017, -1, 0, 1.433, 0, 2, 1.85, 0, 0, 2.25, 0.966, 0, 2.683, -1, 2, 2.833, -1, 0, 3.6, 0, 2, 4.133, 0, 2, 4.6, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 1, 0.528, -3.684, 0.555, -7.369, 0.583, -11.053, 1, 0.866, -14.035, 1.15, -17.018, 1.433, -20, 1, 1.705, -16.918, 1.978, -13.837, 2.25, -10.755, 1, 2.7, -7.17, 3.15, -3.585, 3.6, 0, 2, 4.133, 0, 2, 4.6, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.5, 1, 2, 3.217, 1, 2, 3.233, 0, 2, 4.15, 0, 2, 4.633, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 2.25, 0, 2, 2.267, 1, 2, 3.217, 1, 2, 3.233, 0, 2, 4.15, 0, 2, 5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 0.583, 0, 1, 1.144, -0.318, 1.706, -0.635, 2.267, -0.953, 0, 3.233, -0.226, 2, 4.133, -0.226, 1, 4.422, -0.226, 4.711, -0.12, 5, -0.053]}, {"Target": "Parameter", "Id": "Change_idle_physics5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 0.583, 0, 1, 1.144, -0.062, 1.706, -0.123, 2.267, -0.185, 0, 3.233, -0.14, 2, 4.133, -0.14, 1, 4.422, -0.14, 4.711, -0.074, 5, -0.033]}, {"Target": "Parameter", "Id": "Change_idle_physics6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 0.583, 0, 1, 1.144, -0.112, 1.706, -0.223, 2.267, -0.335, 1, 2.422, -0.335, 2.578, -0.341, 2.733, -0.317, 1, 2.9, -0.291, 3.066, 0.342, 3.233, 0.342, 2, 4.133, 0.342, 1, 4.422, 0.342, 4.711, 0.182, 5, 0.081]}, {"Target": "Parameter", "Id": "ParamAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.583, 2, 0.5, -3.583, 0, 1.667, 2.667, 1, 1.861, 2.667, 2.056, 1.123, 2.25, -0.527, 1, 2.528, -2.884, 2.805, -3.583, 3.083, -3.583, 0, 4.433, 2.667, 1, 4.622, 2.667, 4.811, 1.98, 5, 1.304]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10, 2, 0.5, 10, 2, 2.25, 10, 1, 3.167, 10, 4.083, 3.056, 5, 0.741]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -7.795, 2, 0.5, -7.795, 1, 0.883, -7.795, 1.267, -4.621, 1.65, 0, 1, 1.85, 2.411, 2.05, 4.825, 2.25, 5.969, 1, 2.528, 7.558, 2.805, 7.795, 3.083, 7.795, 1, 3.516, 7.795, 3.95, 5.461, 4.383, 0, 1, 4.589, -2.591, 4.794, -4.62, 5, -5.958]}, {"Target": "Parameter", "Id": "ParamBodyStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 0, 2.05, -10, 1, 2.117, -10, 2.183, -10, 2.25, -9.691, 1, 3.094, -1.608, 3.939, 4.862, 4.783, 4.862, 1, 4.855, 4.862, 4.928, 4.474, 5, 3.917]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 0, 1.55, -10, 1, 1.783, -10, 2.017, -8.89, 2.25, -6.872, 1, 2.528, -4.469, 2.805, -2.978, 3.083, 0.147, 1, 3.539, 5.272, 3.994, 9.828, 4.45, 9.828, 1, 4.633, 9.828, 4.817, 7.371, 5, 4.914]}, {"Target": "Parameter", "Id": "ParamLegLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10.58, 2, 0.5, 10.58, 2, 1.633, 10.58, 1, 1.839, 10.58, 2.044, 9.603, 2.25, 8.674, 1, 2.933, 5.585, 3.617, 0.364, 4.3, 0.098, 1, 4.533, 0.007, 4.767, -0.013, 5, -0.013]}, {"Target": "Parameter", "Id": "ParamCrusLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 1, 1.083, 0, 1.667, 2.038, 2.25, 3.568, 1, 2.511, 4.253, 2.772, 4.14, 3.033, 4.14, 1, 3.689, 4.14, 4.344, 1.612, 5, 0.507]}, {"Target": "Parameter", "Id": "ParamLegRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 7.86, 2, 0.5, 7.86, 2, 1.633, 7.86, 1, 1.839, 7.86, 2.044, 7.544, 2.25, 5.954, 1, 2.933, 0.669, 3.617, -2.622, 4.3, -2.622, 1, 4.533, -2.622, 4.767, -1.8, 5, -1.076]}, {"Target": "Parameter", "Id": "ParamCrusRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 1, 1.083, 0, 1.667, 3.158, 2.25, 5.532, 1, 2.511, 6.595, 2.772, 6.42, 3.033, 6.42, 1, 3.689, 6.42, 4.344, 2.499, 5, 0.786]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.5, 1, 2, 2.25, 1, 1, 3.167, 1, 4.083, 0.306, 5, 0.074]}, {"Target": "Parameter", "Id": "physics_Add_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -30, 2, 0.5, -30, 2, 2.25, -30, 1, 3.167, -30, 4.083, -9.167, 5, -2.222]}, {"Target": "Parameter", "Id": "Physics_AngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.566, 2, 0.5, 1.566, 0, 1.683, 1.619, 1, 1.872, 1.619, 2.061, 0.735, 2.25, -0.225, 1, 2.539, -1.693, 2.828, -2.152, 3.117, -2.152, 0, 4.45, 1.615, 1, 4.633, 1.615, 4.817, 1.211, 5, 0.807]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 3.075, 2, 0.5, 3.075, 0, 1.667, -1.778, 1, 1.861, -1.778, 2.056, -0.749, 2.25, 0.351, 1, 2.528, 1.923, 2.805, 2.389, 3.083, 2.389, 0, 4.433, -1.778, 1, 4.622, -1.778, 4.811, -1.32, 5, -0.869]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -9.285, 2, 0.5, -9.285, 0, 1.667, 1.778, 1, 1.861, 1.778, 2.056, 0.749, 2.25, -0.351, 1, 2.528, -1.923, 2.805, -2.389, 3.083, -2.389, 0, 4.433, 1.778, 1, 4.622, 1.778, 4.811, 1.32, 5, 0.869]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_L", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.622, 2, 0.5, 0.622, 2, 1.667, 0.622, 1, 1.861, 0.622, 2.056, 0.262, 2.25, -0.123, 1, 2.528, -0.673, 2.805, -0.836, 3.083, -0.836, 0, 4.433, 0.622, 1, 4.622, 0.622, 4.811, 0.462, 5, 0.304]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_R", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.622, 2, 0.5, -0.622, 2, 1.667, -0.622, 1, 1.861, -0.622, 2.056, -0.262, 2.25, 0.123, 1, 2.528, 0.673, 2.805, 0.836, 3.083, 0.836, 0, 4.433, -0.622, 1, 4.622, -0.622, 4.811, -0.462, 5, -0.304]}, {"Target": "Parameter", "Id": "L_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10.902, 2, 0.5, 10.902, 0, 1.667, -3.556, 1, 1.861, -3.556, 2.056, -1.498, 2.25, 0.702, 1, 2.528, 3.845, 2.805, 4.777, 3.083, 4.777, 0, 4.433, -3.556, 1, 4.622, -3.556, 4.811, -2.64, 5, -1.738]}, {"Target": "Parameter", "Id": "L_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.345, 2, 0.5, 0.345, 0, 1.667, 3.556, 1, 1.861, 3.556, 2.056, 1.498, 2.25, -0.702, 1, 2.528, -3.845, 2.805, -4.777, 3.083, -4.777, 0, 4.433, 3.556, 1, 4.622, 3.556, 4.811, 2.64, 5, 1.738]}, {"Target": "Parameter", "Id": "R_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 9.721, 2, 0.5, 9.721, 1, 0.889, 9.721, 1.278, 7.626, 1.667, 3.556, 1, 1.861, 1.521, 2.056, -1.087, 2.25, -2.431, 1, 2.528, -4.351, 2.805, -4.777, 3.083, -4.777, 0, 4.433, 3.556, 1, 4.622, 3.556, 4.811, 2.64, 5, 1.738]}, {"Target": "Parameter", "Id": "R_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.732, 2, 0.5, -2.732, 0, 1.667, -3.556, 1, 1.861, -3.556, 2.056, -1.498, 2.25, 0.702, 1, 2.528, 3.845, 2.805, 4.777, 3.083, 4.777, 0, 4.433, -3.556, 1, 4.622, -3.556, 4.811, -2.64, 5, -1.738]}, {"Target": "Parameter", "Id": "Jewelry_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.766, 2, 0.5, 0.766, 0, 1.667, -0.296, 1, 1.861, -0.296, 2.056, -0.124, 2.25, 0.059, 1, 2.528, 0.321, 2.805, 0.398, 3.083, 0.398, 0, 4.433, -0.296, 1, 4.622, -0.296, 4.811, -0.22, 5, -0.145]}, {"Target": "Parameter", "Id": "Jewelry_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.302, 2, 0.5, 0.302, 1, 0.889, 0.302, 1.278, 0.302, 1.667, 0.296, 1, 1.861, 0.293, 2.056, 0.078, 2.25, -0.061, 1, 2.528, -0.259, 2.805, -0.398, 3.083, -0.398, 0, 4.433, 0.296, 1, 4.622, 0.296, 4.811, 0.22, 5, 0.145]}, {"Target": "Parameter", "Id": "Jewelry_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.739, 2, 0.5, -1.739, 2, 2.25, -1.739, 1, 3.167, -1.739, 4.083, -0.531, 5, -0.129]}, {"Target": "Parameter", "Id": "Physics_Joint_5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.715, 2, 0.5, 0.715, 0, 0.517, -0.093, 0, 1, 0.02, 0, 1.267, -0.001, 2, 1.283, -0.001, 0, 1.867, 0.102, 2, 1.883, 0.102, 1, 2.005, 0.102, 2.128, 0.076, 2.25, 0.055, 1, 2.322, 0.043, 2.395, 0.043, 2.467, 0.043, 2, 2.483, 0.043, 0, 2.617, 0.044, 2, 2.667, 0.044, 0, 3.483, -0.021, 2, 3.533, -0.021, 0, 3.983, -0.067, 0, 4.417, -0.005, 0, 4.833, -0.035, 1, 4.889, -0.035, 4.944, -0.035, 5, -0.033]}, {"Target": "Parameter", "Id": "Physics_Joint_4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 4.926, 2, 0.5, 4.926, 0, 0.733, -0.381, 0, 1.233, -0.13, 0, 1.4, -0.141, 0, 2.183, 0.287, 1, 2.205, 0.287, 2.228, 0.286, 2.25, 0.283, 1, 2.356, 0.269, 2.461, 0.262, 2.567, 0.262, 0, 2.983, 0.326, 0, 4.233, -0.158, 0, 4.6, -0.073, 1, 4.733, -0.073, 4.867, -0.133, 5, -0.15]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.298, 2, 0.5, 2.298, 0, 1.667, -0.889, 1, 1.861, -0.889, 2.056, -0.374, 2.25, 0.176, 1, 2.528, 0.962, 2.805, 1.195, 3.083, 1.195, 0, 4.433, -0.889, 1, 4.622, -0.889, 4.811, -0.66, 5, -0.435]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.907, 2, 0.5, 0.907, 1, 0.889, 0.907, 1.278, 0.907, 1.667, 0.889, 1, 1.861, 0.88, 2.056, 0.233, 2.25, -0.184, 1, 2.528, -0.78, 2.805, -1.195, 3.083, -1.195, 0, 4.433, 0.889, 1, 4.622, 0.889, 4.811, 0.66, 5, 0.435]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.218, 2, 0.5, -5.218, 2, 2.25, -5.218, 1, 3.167, -5.218, 4.083, -1.594, 5, -0.387]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -13.924, 2, 0.5, -13.924, 2, 2.25, -13.924, 1, 3.167, -13.924, 4.083, -4.255, 5, -1.031]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -13.919, 2, 0.5, -13.919, 2, 2.25, -13.919, 1, 3.167, -13.919, 4.083, -4.253, 5, -1.031]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.635, 2, 0.5, 1.635, 2, 2.25, 1.635, 1, 3.167, 1.635, 4.083, 0.5, 5, 0.121]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 19.522, 2, 0.5, 19.522, 2, 2.25, 19.522, 1, 3.167, 19.522, 4.083, 5.965, 5, 1.446]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 0, 1.667, 1, 0, 3.9, 0, 1, 4.267, 0, 4.633, 0.889, 5, 0.991]}, {"Target": "Parameter", "Id": "fenshangxuanzhuang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 1, 2, 0.601, 3.5, 1.201, 5, 1.802]}, {"Target": "Parameter", "Id": "ParamSad2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 5, 1]}, {"Target": "Parameter", "Id": "ParamCRY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.2, 0, 5, -5.2]}, {"Target": "Parameter", "Id": "Position_Coordinates", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 5, 1]}, {"Target": "Parameter", "Id": "Man_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 5, 1]}, {"Target": "Parameter", "Id": "Man_TMD2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "BG_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 5, 1]}, {"Target": "Parameter", "Id": "FG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "BG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "BG_White4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "BG_White5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "BG_White2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "BG_White3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "All_Size2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "All_Size3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "All_Size4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "tuerzuo3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "faqiehuan4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Mail_Change", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "dafengshangzhuanchang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "tongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamTongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "EyeFrame_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamBrowAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamBrowY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamSigh", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamDarken", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamCry", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_B", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_C", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "MB_Unidirectional_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "jing<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "huqi1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "huqi2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamShoulderLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamArmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 5, 1]}, {"Target": "Parameter", "Id": "ParamForearmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamArmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamArmL_ForearmL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamForearmL_HandL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamHandLSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "xiongjiya", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "xiongjiya5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "xiongjiya3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "xiongjiya4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "xiongjiya2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamShoulderRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamArmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 5, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamArmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamArmR_ForearmR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_HandR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamHandRSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 5, 0.6]}, {"Target": "Parameter", "Id": "Change_idle_physics23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Param103", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "taiqiugundong", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -22.5, 0, 5, -22.5]}, {"Target": "Parameter", "Id": "Change_idle_physics32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamLegL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 5, 1]}, {"Target": "Parameter", "Id": "ParamLegLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamCrusLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>oodLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamFoodLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamFoodLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamLegR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 5, 1]}, {"Target": "Parameter", "Id": "ParamLegRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamCrusRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamCrus<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>ood<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamFoodRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamFoodRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Physics_Provisional", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "physics_Add_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamBodyStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Physics_AngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Physics_AngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Physics_Body_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Physics_Body_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Physics_breast", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamBreath2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "touch_drag1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "touch_drag2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "touch_drag3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "touch_drag4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "touch_drag5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "touch_drag6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "touch_drag7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "touch_drag8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "touch_drag9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>pian<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh654", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh655", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh657", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh656", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh658", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh659", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh660", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5, 0]}], "UserData": [{"Time": 4.5, "Value": ""}]}