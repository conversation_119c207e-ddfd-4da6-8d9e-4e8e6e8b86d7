{"Version": 3, "Meta": {"Duration": 4.667, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 307, "TotalSegmentCount": 1160, "TotalPointCount": 1494, "UserDataCount": 1, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "MB_Lightning_Splinter_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.75, 30, 2, 0.767, 30, 0, 0.933, 100, 2, 1.017, 100, 2, 1.033, 0, 2, 4.667, 0]}, {"Target": "Parameter", "Id": "MB_Lightning_Splinter_C", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.75, 30, 2, 0.767, 30, 0, 0.933, 100, 2, 1.017, 100, 2, 1.033, 0, 2, 4.667, 0]}, {"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 1.45, 0, 1, 1.494, 0, 1.539, 0.342, 1.583, 1.112, 1, 1.722, 3.519, 1.861, 7.776, 2, 8.423, 1, 2.222, 9.458, 2.445, 10.26, 2.667, 10.556, 1, 2.9, 10.867, 3.134, 10.856, 3.367, 10.856, 2, 3.817, 10.856, 2, 3.833, 0, 1, 4.028, -0.077, 4.222, -0.153, 4.417, -0.23, 2, 4.667, -0.23]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 1.45, 0, 1, 1.494, 0, 1.539, 0.009, 1.583, -0.431, 1, 1.722, -1.807, 1.861, -2.922, 2, -2.922, 1, 2.222, -2.922, 2.445, -0.37, 2.667, 0.48, 1, 2.9, 1.373, 3.134, 1.32, 3.367, 1.32, 2, 3.817, 1.32, 2, 3.833, -0.18, 1, 4.028, -0.363, 4.222, -0.547, 4.417, -0.73, 2, 4.667, -0.73]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 1.583, 0, 2, 3.383, 0, 2, 3.817, 0, 2, 3.833, 1.5, 2, 4.417, 1.5, 2, 4.667, 1.5]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 1.583, 0, 2, 3.383, 0, 2, 3.817, 0, 2, 3.833, 4.14, 1, 4.028, 4.237, 4.222, 4.333, 4.417, 4.43, 2, 4.667, 4.43]}, {"Target": "Parameter", "Id": "FG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 1.583, 0, 2, 3.1, 0, 0, 3.817, 1, 0, 4.417, 0, 2, 4.667, 0]}, {"Target": "Parameter", "Id": "BG_White4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 3.817, 1, 2, 3.833, 0, 0, 4.417, 0.989, 2, 4.667, 0.989]}, {"Target": "Parameter", "Id": "All_Size2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -12.72, 2, 0.333, -12.72, 2, 1.167, -12.72, 2, 1.417, -12.72, 1, 1.472, -12.72, 1.528, -12.606, 1.583, -14.173, 1, 1.778, -19.657, 1.972, -24.24, 2.167, -24.24, 2, 3.817, -24.24, 1, 3.822, -24.24, 3.828, -12.72, 3.833, -12.72, 2, 4.417, -12.72, 2, 4.667, -12.72]}, {"Target": "Parameter", "Id": "All_Size3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 1.417, 0, 1, 1.472, 0, 1.528, 0.009, 1.583, -0.106, 1, 1.778, -0.508, 1.972, -0.84, 2.167, -0.84, 2, 3.817, -0.84, 0, 3.833, 0, 2, 4.417, 0, 2, 4.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 1.583, 0, 2, 3.817, 0, 0, 4.417, 1, 2, 4.667, 1]}, {"Target": "Parameter", "Id": "Mail_Transparency2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.333, 1, 0, 0.917, 0, 2, 3.817, 0, 2, 3.833, 0, 2, 4.417, 0, 2, 4.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.083, 1, 2, 0.333, 1, 2, 2.75, 1, 0, 2.833, 0, 2, 4.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.333, 0.5, 2, 0.5, 0.5, 0, 0.833, 1, 0, 4.417, 0, 2, 4.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 1.5, 1, 0, 3, 0, 2, 4.417, 0, 2, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 1.583, 0, 2, 3.383, 0, 2, 3.817, 0, 0, 3.833, 0.5, 2, 4.417, 0.5, 2, 4.667, 0.5]}, {"Target": "Parameter", "Id": "ParamMouthOpenY_SD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 1.583, 0, 2, 3.383, 0, 2, 3.817, 0, 0, 3.833, 0.6, 2, 4.417, 0.6, 2, 4.667, 0.6]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.333, 1, 2, 1.383, 1, 0, 1.517, 0, 1, 1.539, 0, 1.561, 0.081, 1.583, 0.39, 1, 1.622, 0.931, 1.661, 1.3, 1.7, 1.3, 0, 1.783, 1, 2, 3.383, 1, 2, 3.817, 1, 2, 3.833, 1, 2, 4.417, 1, 2, 4.667, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.333, 1, 2, 1.383, 1, 0, 1.517, 0, 1, 1.539, 0, 1.561, 0.081, 1.583, 0.39, 1, 1.622, 0.931, 1.661, 1.3, 1.7, 1.3, 0, 1.783, 1, 2, 3.383, 1, 2, 3.817, 1, 2, 3.833, 1, 2, 4.417, 1, 2, 4.667, 1]}, {"Target": "Parameter", "Id": "EyeFrame_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1, 2, 0.333, -1, 2, 1.583, -1, 2, 1.8, -1, 0, 2.933, 0, 2, 3.383, 0, 2, 3.817, 0, 0, 3.833, -1, 2, 4.417, -1, 2, 4.667, -1]}, {"Target": "Parameter", "Id": "ParamBrowForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 1.583, 0, 2, 3.383, 0, 2, 3.817, 0, 0, 3.833, -1, 2, 4.417, -1, 2, 4.667, -1]}, {"Target": "Parameter", "Id": "ParamBrowAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.4, 2, 0.333, 0.4, 2, 1.583, 0.4, 2, 1.8, 0.4, 0, 2.933, 0, 2, 3.383, 0, 2, 3.817, 0, 0, 3.833, 0.284, 2, 4.417, 0.284, 2, 4.667, 0.284]}, {"Target": "Parameter", "Id": "ParamEyeLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 1.383, 0, 0, 1.45, 1, 2, 1.583, 1, 2, 1.6, 1, 0, 1.617, -1, 2, 1.75, -1, 0, 2.033, 0.452, 0, 2.35, 0.029, 0, 2.617, 0.106, 2, 3.383, 0.106, 2, 3.817, 0.106, 0, 3.833, 0, 1, 4.028, -0.001, 4.222, -0.002, 4.417, -0.002, 2, 4.667, -0.002]}, {"Target": "Parameter", "Id": "ParamEyeRsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 1.383, 0, 0, 1.45, 1, 2, 1.583, 1, 2, 1.6, 1, 0, 1.617, -1, 2, 1.75, -1, 0, 2.033, 0.452, 0, 2.35, 0.029, 0, 2.617, 0.106, 2, 3.383, 0.106, 2, 3.817, 0.106, 0, 3.833, 0, 1, 4.028, -0.001, 4.222, -0.002, 4.417, -0.002, 2, 4.667, -0.002]}, {"Target": "Parameter", "Id": "ParamEyeLGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 1.383, 0, 0, 1.45, -0.305, 0, 1.5, -0.2, 1, 1.528, -0.2, 1.555, -0.544, 1.583, -0.94, 1, 1.589, -1, 1.594, -1, 1.6, -1, 2, 1.617, -1, 0, 1.633, 1, 2, 1.667, 1, 0, 1.717, 0.826, 0, 1.733, 0.846, 0, 1.983, -0.307, 0, 2.25, 0.171, 0, 2.533, -0.077, 0, 2.833, 0.035, 0, 3.133, -0.01, 2, 3.383, -0.01, 2, 3.817, -0.01, 0, 3.833, 0, 0, 4.417, -0.001, 2, 4.667, -0.001]}, {"Target": "Parameter", "Id": "ParamEyeRGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 1.383, 0, 0, 1.45, -0.305, 0, 1.5, -0.2, 1, 1.528, -0.2, 1.555, -0.544, 1.583, -0.94, 1, 1.589, -1, 1.594, -1, 1.6, -1, 2, 1.617, -1, 0, 1.633, 1, 2, 1.667, 1, 0, 1.717, 0.826, 0, 1.733, 0.846, 0, 1.983, -0.307, 0, 2.25, 0.171, 0, 2.533, -0.077, 0, 2.833, 0.035, 0, 3.133, -0.01, 2, 3.383, -0.01, 2, 3.817, -0.01, 0, 3.833, 0, 0, 4.417, -0.001, 2, 4.667, -0.001]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.3, 0, 2, 1.317, 5, 2, 1.533, 5, 2, 1.55, 0, 2, 3.817, 0, 2, 3.833, -5, 2, 4.417, -5, 2, 4.667, -5]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.3, 0, 2, 1.317, -5, 2, 1.533, -5, 2, 1.55, 0, 2, 3.817, 0, 2, 3.833, 5, 2, 4.417, 5, 2, 4.667, 5]}, {"Target": "Parameter", "Id": "jing<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 1.583, 0, 2, 1.85, 0, 0, 1.917, 1, 2, 1.933, 1, 2, 1.967, 1, 2, 1.983, 1, 2, 2, 0, 2, 3.383, 0, 2, 3.817, 0, 2, 3.833, 0, 2, 4.417, 0, 2, 4.667, 0]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.7, 2, 0.333, -3.7, 1, 0.75, -3.7, 1.166, -6.291, 1.583, -9.591, 1, 1.683, -10.383, 1.783, -10.521, 1.883, -11.02, 1, 1.889, -11.048, 1.894, -11.357, 1.9, -11.357, 1, 1.933, -11.357, 1.967, -10.921, 2, -7.8, 1, 2.039, -4.158, 2.078, 0, 2.117, 0, 2, 3.383, 0, 2, 3.817, 0, 0, 3.833, -2.374, 2, 4.417, -2.374, 2, 4.667, -2.374]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10.794, 2, 0.333, 10.794, 1, 0.75, 10.794, 1.166, 5.546, 1.583, 2.049, 1, 1.683, 1.21, 1.783, 1.431, 1.883, 1.1, 1, 1.889, 1.082, 1.894, 0.921, 1.9, 0.921, 0, 2, 21.78, 0, 2.117, 0, 2, 3.383, 0, 2, 3.817, 0, 0, 3.833, 6.925, 2, 4.417, 6.925, 2, 4.667, 6.925]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.284, 2, 0.333, -5.284, 1, 0.75, -5.284, 1.166, -6.391, 1.583, -6.798, 1, 1.683, -6.896, 1.783, -6.84, 1.883, -6.84, 1, 1.922, -6.84, 1.961, -1.055, 2, -0.48, 1, 2.039, 0.095, 2.078, 0, 2.117, 0, 2, 3.383, 0, 2, 3.817, 0, 0, 3.833, -4.027, 2, 4.417, -4.027, 2, 4.667, -4.027]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -13.132, 2, 0.333, -13.132, 1, 0.75, -13.132, 1.166, -14.544, 1.583, -14.96, 1, 1.683, -15.06, 1.783, -15, 1.883, -15, 2, 2, -15, 0, 2.117, 0, 2, 3.383, 0, 2, 3.817, 0, 1, 3.822, 0, 3.828, -10.008, 3.833, -10.008, 2, 4.417, -10.008, 2, 4.667, -10.008]}, {"Target": "Parameter", "Id": "ParamShoulderLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.168, 2, 0.333, 0.168, 0, 0.85, 2, 0, 1.517, -1, 1, 1.539, -1, 1.561, -1.082, 1.583, -0.927, 1, 1.8, 0.585, 2.016, 2, 2.233, 2, 0, 2.983, -1, 2, 3.383, -1, 2, 3.817, -1, 0, 3.833, -0.548, 2, 4.417, -0.548, 2, 4.667, -0.548]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -8.2, 2, 0.333, -8.2, 2, 1.45, -8.2, 1, 1.494, -8.2, 1.539, -7.969, 1.583, -9.36, 1, 1.772, -15.274, 1.961, -20.41, 2.15, -20.41, 2, 3.383, -20.41, 2, 3.817, -20.41, 1, 3.822, -20.41, 3.828, -8.2, 3.833, -8.2, 2, 4.417, -8.2, 2, 4.667, -8.2]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.076, 2, 0.333, 0.076, 1, 0.75, 0.076, 1.166, 0.065, 1.583, 0.061, 1, 1.772, 0.059, 1.961, 0.06, 2.15, 0.06, 2, 3.383, 0.06, 2, 3.817, 0.06, 0, 3.833, 0.087, 2, 4.417, 0.087, 2, 4.667, 0.087]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.551, 2, 0.333, -0.551, 0, 0.45, -0.6, 0, 0.867, -0.242, 0, 1.267, -0.6, 0, 1.5, -0.377, 1, 1.528, -0.377, 1.555, -0.372, 1.583, -0.395, 1, 1.716, -0.506, 1.85, -0.6, 1.983, -0.6, 0, 3.383, -0.5, 0, 3.833, -0.507, 2, 4.417, -0.507, 2, 4.667, -0.507]}, {"Target": "Parameter", "Id": "xiongjiya5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 1.583, 0, 2, 3.383, 0, 2, 3.817, 0, 2, 3.833, 0, 0, 4.417, 0.7, 2, 4.667, 0.7]}, {"Target": "Parameter", "Id": "xiongjiya3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.663, 2, 0.333, 0.663, 2, 1.583, 0.663, 2, 3.383, 0.663, 2, 3.817, 0.663, 0, 3.833, 0.85, 2, 4.417, 0.85, 2, 4.667, 0.85]}, {"Target": "Parameter", "Id": "xiongjiya4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.663, 2, 0.333, 0.663, 2, 1.583, 0.663, 2, 3.383, 0.663, 2, 3.817, 0.663, 0, 3.833, 0.85, 2, 4.417, 0.85, 2, 4.667, 0.85]}, {"Target": "Parameter", "Id": "ParamShoulderRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.168, 2, 0.333, 0.168, 0, 0.85, 2, 0, 1.517, -1, 1, 1.539, -1, 1.561, -1.082, 1.583, -0.927, 1, 1.8, 0.585, 2.016, 2, 2.233, 2, 0, 2.983, -1, 2, 3.383, -1, 2, 3.817, -1, 0, 3.833, -0.548, 2, 4.417, -0.548, 2, 4.667, -0.548]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -7.9, 2, 0.333, -7.9, 2, 1.45, -7.9, 1, 1.494, -8.324, 1.539, -8.749, 1.583, -9.173, 1, 1.772, -10.977, 1.961, -12.78, 2.15, -14.584, 2, 3.383, -14.584, 2, 3.817, -14.584, 1, 3.822, -14.584, 3.828, -7.9, 3.833, -7.9, 2, 4.417, -7.9, 2, 4.667, -7.9]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.72, 2, 0.333, -0.72, 2, 1.583, -0.72, 2, 2.767, -0.72, 0, 3.183, 0, 2, 3.383, 0, 2, 3.817, 0, 0, 3.833, -0.72, 2, 4.417, -0.72, 2, 4.667, -0.72]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.539, 2, 0.333, -0.539, 0, 0.45, -0.6, 0, 0.867, -0.157, 0, 1.267, -0.6, 0, 1.5, -0.323, 1, 1.528, -0.323, 1.555, -0.317, 1.583, -0.345, 1, 1.716, -0.482, 1.85, -0.6, 1.983, -0.6, 0, 3.833, -0.505, 2, 4.417, -0.505, 2, 4.667, -0.505]}, {"Target": "Parameter", "Id": "ParamHandRDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 8, 2, 0.333, 8, 2, 1.583, 8, 2, 2.75, 8, 2, 3.817, 8, 2, 3.833, 8, 2, 4.417, 8, 2, 4.667, 8]}, {"Target": "Parameter", "Id": "Change_idle_physics8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.333, 1, 2, 1.583, 1, 2, 1.717, 1, 0, 1.983, 0, 2, 2.15, 0, 2, 3.383, 0, 2, 3.817, 0, 0, 3.833, 1, 2, 4.417, 1, 2, 4.667, 1]}, {"Target": "Parameter", "Id": "Change_idle_physics4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.159, 2, 0.333, -0.159, 0, 1.067, -0.168, 0, 1.5, -0.154, 1, 1.528, -0.154, 1.555, -0.184, 1.583, -0.223, 1, 1.589, -0.231, 1.594, -0.233, 1.6, -0.236, 1, 1.75, -0.314, 1.9, -0.352, 2.05, -0.352, 2, 2.75, -0.352, 2, 3.383, -0.352, 2, 3.817, -0.352, 1, 3.822, -0.352, 3.828, -0.138, 3.833, -0.137, 1, 4.028, -0.108, 4.222, -0.095, 4.417, -0.095, 2, 4.667, -0.095]}, {"Target": "Parameter", "Id": "Change_idle_physics5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.3, 2, 0.333, -0.3, 2, 1.583, -0.3, 2, 1.717, -0.3, 0, 2.05, -0.298, 2, 2.75, -0.298, 2, 3.383, -0.298, 2, 3.817, -0.298, 0, 3.833, -0.3, 2, 4.417, -0.3, 2, 4.667, -0.3]}, {"Target": "Parameter", "Id": "Change_idle_physics6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.622, 2, 0.333, 0.622, 0, 1.583, 0.644, 2, 1.717, 0.644, 2, 3.817, 0.644, 0, 3.833, 0.615, 2, 4.417, 0.615, 2, 4.667, 0.615]}, {"Target": "Parameter", "Id": "Change_idle_physics10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.603, 2, 0.333, 0.603, 0, 0.833, 0.679, 2, 1.233, 0.679, 0, 1.517, 0, 2, 1.583, 0, 2, 3.383, 0, 2, 3.817, 0, 0, 3.833, 0.321, 0, 4.417, 0, 2, 4.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.555, 2, 0.333, -0.555, 0, 0.767, 0.8, 0, 1.217, -1, 1, 1.311, -1, 1.406, -0.985, 1.5, -0.9, 1, 1.528, -0.875, 1.555, 0.269, 1.583, 0.819, 1, 1.589, 0.929, 1.594, 0.956, 1.6, 0.956, 0, 1.633, 0.92, 0, 1.717, 1, 2, 3.383, 1, 2, 3.817, 1, 0, 3.833, -0.479, 2, 4.417, -0.479, 2, 4.667, -0.479]}, {"Target": "Parameter", "Id": "Change_idle_physics14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 1.583, 0, 1, 1.683, 0.167, 1.783, 0.335, 1.883, 0.502, 1, 1.983, 0.601, 2.083, 0.701, 2.183, 0.8, 1, 2.233, 0.867, 2.283, 0.933, 2.333, 1, 1, 2.778, 1.333, 3.222, 1.667, 3.667, 2, 2, 3.817, 2, 0, 3.833, 0, 2, 4.417, 0, 2, 4.667, 0]}, {"Target": "Parameter", "Id": "Param103", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 27, 2, 0.333, 27, 2, 1.583, 27, 2, 1.883, 27, 1, 1.95, 27, 2.016, 15.037, 2.083, 3.19, 1, 2.222, -21.491, 2.361, -30, 2.5, -30, 2, 3.133, -30, 2, 3.383, -30, 2, 3.817, -30, 1, 3.822, -30, 3.828, -17.662, 3.833, -17.662, 2, 4.417, -17.662, 2, 4.667, -17.662]}, {"Target": "Parameter", "Id": "taiqiugundong", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -22.5, 2, 0.333, -22.5, 2, 1.583, -22.5, 2, 2.4, -22.5, 1, 2.678, -5, 2.955, 12.5, 3.233, 30, 2, 3.283, 30, 1, 3.461, 30, 3.639, 26.64, 3.817, 19.397, 1, 3.822, 19.171, 3.828, -22.5, 3.833, -22.5, 2, 4.417, -22.5, 2, 4.667, -22.5]}, {"Target": "Parameter", "Id": "Change_idle_physics32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 1.583, 0, 2, 2.4, 0, 1, 2.561, 0.2, 2.722, 0.4, 2.883, 0.6, 1, 3.061, 0.733, 3.239, 0.867, 3.417, 1, 2, 3.817, 1, 0, 3.833, 0, 2, 4.417, 0, 2, 4.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 1.583, 0, 2, 1.867, 0, 1, 1.872, 0, 1.878, 0, 1.883, 0.007, 1, 2.1, 0.628, 2.316, 1, 2.533, 1, 2, 3.367, 1, 2, 3.383, 0, 2, 3.817, 0, 2, 3.833, 0, 2, 4.417, 0, 2, 4.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 1.583, 0, 2, 2.233, 0, 1, 2.255, 0, 2.278, -0.015, 2.3, 0.062, 1, 2.322, 0.139, 2.345, 1, 2.367, 1, 1, 2.395, 1, 2.422, 0.62, 2.45, 0.062, 1, 2.478, -0.496, 2.505, -0.695, 2.533, -0.695, 1, 2.561, -0.695, 2.589, -0.404, 2.617, 0.039, 1, 2.645, 0.482, 2.672, 0.647, 2.7, 0.647, 1, 2.728, 0.647, 2.755, 0.201, 2.783, 0.039, 1, 2.811, -0.123, 2.839, -0.113, 2.867, -0.113, 0, 2.95, 0, 2, 3.383, 0, 2, 3.817, 0, 2, 3.833, 0, 2, 4.417, 0, 2, 4.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 1.583, 0, 2, 2.233, 0, 0, 2.3, -1, 1, 2.322, -1, 2.345, -0.512, 2.367, 0.062, 1, 2.395, 0.78, 2.422, 1, 2.45, 1, 1, 2.478, 1, 2.505, 0.594, 2.533, 0.039, 1, 2.561, -0.516, 2.589, -0.695, 2.617, -0.695, 1, 2.645, -0.695, 2.672, -0.404, 2.7, 0.039, 1, 2.728, 0.482, 2.755, 0.647, 2.783, 0.647, 1, 2.811, 0.647, 2.839, 0.439, 2.867, 0.238, 1, 2.895, 0.037, 2.922, 0, 2.95, 0, 2, 3.383, 0, 2, 3.817, 0, 2, 3.833, 0, 2, 4.417, 0, 2, 4.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.983, 0, 0, 3.117, -1, 0, 3.217, 1, 1, 3.272, 1, 3.328, 0.529, 3.383, 0.4, 1, 3.528, 0.064, 3.672, 0, 3.817, 0, 2, 3.833, 0, 2, 4.417, 0, 2, 4.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.983, 0, 0, 3.117, 1, 0, 3.217, -1, 0, 3.383, 0.3, 0, 3.817, 0, 2, 3.833, 0, 2, 4.417, 0, 2, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -9.8, 2, 0.333, -9.8, 0, 0.767, 8.24, 0, 1.217, -13.185, 1, 1.339, -13.185, 1.461, -1.839, 1.583, 15.5, 1, 1.661, 26.534, 1.739, 30, 1.817, 30, 2, 3.383, 30, 2, 3.817, 30, 1, 3.822, 30, 3.828, -4.226, 3.833, -4.226, 2, 4.417, -4.226, 2, 4.667, -4.226]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -4, 2, 0.333, -4, 0, 0.767, 4, 1, 0.917, 4, 1.067, -1.941, 1.217, -4, 1, 1.311, -5.296, 1.406, -5, 1.5, -5, 1, 1.528, -5, 1.555, -5.102, 1.583, -3.812, 1, 1.683, 0.834, 1.783, 4.768, 1.883, 4.768, 0, 2.2, 0, 2, 3.383, 0, 2, 3.817, 0, 0, 3.833, -2.009, 2, 4.417, -2.009, 2, 4.667, -2.009]}, {"Target": "Parameter", "Id": "ParamBodyStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.017, 2, 0.333, -2.017, 0, 0.55, 8.411, 0, 1.15, -10, 0, 1.4, 3.534, 1, 1.461, 3.534, 1.522, 0.886, 1.583, -2.225, 1, 1.6, -3.073, 1.616, -3, 1.633, -3, 0, 1.967, 0, 2, 3.383, 0, 2, 3.817, 0, 0, 4.133, -6.876, 0, 4.417, -1.483, 2, 4.667, -1.483]}, {"Target": "Parameter", "Id": "Change_idle_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.399, 2, 0.333, 0.399, 1, 0.75, 0.399, 1.166, 0.681, 1.583, 0.806, 1, 2.178, 0.984, 2.772, 1, 3.367, 1, 2, 3.383, 1, 2, 3.817, 1, 0, 3.833, 0.187, 2, 4.417, 0.187, 2, 4.667, 0.187]}, {"Target": "Parameter", "Id": "Physics_AngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.894, 2, 0.333, -5.894, 0, 0.383, -6.559, 0, 0.8, 5.861, 0, 1.25, -8.874, 1, 1.361, -8.874, 1.472, -2.867, 1.583, 7.487, 1, 1.672, 15.77, 1.761, 19.183, 1.85, 19.183, 2, 3.383, 19.183, 2, 3.817, 19.183, 1, 3.822, 19.183, 3.828, -3.467, 3.833, -3.467, 0, 4.417, -2.434, 2, 4.667, -2.434]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 17.608, 2, 0.333, 17.608, 0, 0.633, -22.473, 0, 1.033, 27.524, 0, 1.45, -29.737, 0, 1.517, -26.876, 0, 1.567, -27.928, 1, 1.572, -27.928, 1.578, -28.963, 1.583, -27.462, 1, 1.683, -0.437, 1.783, 30, 1.883, 30, 2, 1.917, 30, 0, 2.317, -18.545, 0, 2.733, 12.629, 0, 3.15, -7.456, 2, 3.383, -7.456, 2, 3.817, -7.456, 1, 3.822, -7.456, 3.828, 2.68, 3.833, 2.68, 0, 4.417, -1.602, 2, 4.667, -1.602]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 16.981, 2, 0.333, 16.981, 0, 0.383, 30, 2, 0.567, 30, 0, 0.733, -30, 2, 0.95, -30, 0, 1.1, 30, 2, 1.367, 30, 1, 1.417, 30, 1.467, -30, 1.517, -30, 2, 1.583, -30, 2, 1.85, -30, 1, 1.9, -30, 1.95, 30, 2, 30, 2, 2.25, 30, 0, 2.45, -30, 2, 2.6, -30, 0, 2.933, 28.287, 0, 3.35, -14.133, 2, 3.383, -14.133, 2, 3.817, -14.133, 1, 3.822, -14.133, 3.828, 5.531, 3.833, 5.531, 0, 4.417, 2.667, 2, 4.667, 2.667]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_L", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.372, 2, 0.333, -0.372, 0, 0.5, 5.171, 0, 0.883, -5.99, 0, 1.3, 7.631, 1, 1.394, 7.631, 1.489, 2.956, 1.583, -3.942, 1, 1.639, -7.999, 1.694, -9.131, 1.75, -9.131, 0, 2.2, 7.194, 0, 2.65, -3.887, 0, 3.1, 3.21, 2, 3.383, 3.21, 2, 3.817, 3.21, 1, 3.822, 3.21, 3.828, 2.566, 3.833, 2.498, 1, 4.028, 0.121, 4.222, -0.986, 4.417, -0.986, 2, 4.667, -0.986]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_R", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.372, 2, 0.333, 0.372, 0, 0.5, -5.171, 0, 0.883, 5.99, 0, 1.3, -7.631, 1, 1.394, -7.631, 1.489, -2.956, 1.583, 3.942, 1, 1.639, 7.999, 1.694, 9.131, 1.75, 9.131, 0, 2.2, -7.194, 0, 2.65, 3.887, 0, 3.1, -3.21, 2, 3.383, -3.21, 2, 3.817, -3.21, 1, 3.822, -3.21, 3.828, -2.566, 3.833, -2.498, 1, 4.028, -0.121, 4.222, 0.986, 4.417, 0.986, 2, 4.667, 0.986]}, {"Target": "Parameter", "Id": "L_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 13.068, 2, 0.333, 13.068, 0, 0.767, -10.988, 0, 1.183, 16.735, 0, 1.533, -19.088, 1, 1.55, -19.088, 1.566, -19.772, 1.583, -17.952, 1, 1.7, -5.215, 1.816, 7.339, 1.933, 7.339, 0, 2.283, -0.189, 0, 2.683, 2.291, 2, 3.383, 2.291, 2, 3.817, 2.291, 1, 3.822, 2.291, 3.828, 8.88, 3.833, 8.88, 0, 4.417, 5.635, 2, 4.667, 5.635]}, {"Target": "Parameter", "Id": "L_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -13.068, 2, 0.333, -13.068, 0, 0.767, 10.988, 0, 1.167, -16.601, 0, 1.367, 22.14, 1, 1.439, 22.14, 1.511, 6.133, 1.583, -11.85, 1, 1.6, -16, 1.616, -15.306, 1.633, -15.306, 0, 2, 5.345, 0, 2.317, -2.029, 0, 2.633, 0.505, 0, 2.983, -0.078, 0, 3.367, 0.088, 2, 3.383, 0.088, 2, 3.817, 0.088, 0, 3.833, -6.646, 0, 4.417, -5.635, 2, 4.667, -5.635]}, {"Target": "Parameter", "Id": "R_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -13.069, 2, 0.333, -13.069, 0, 0.767, 10.988, 0, 1.217, -17.586, 1, 1.339, -17.586, 1.461, 1.337, 1.583, 23.279, 1, 1.616, 29.263, 1.65, 28.754, 1.683, 28.754, 0, 2.117, -8.361, 0, 2.533, -1.363, 0, 2.983, -3.137, 2, 3.383, -3.137, 2, 3.817, -3.137, 1, 3.822, -3.137, 3.828, -8.88, 3.833, -8.88, 0, 4.417, -5.635, 2, 4.667, -5.635]}, {"Target": "Parameter", "Id": "R_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 13.069, 2, 0.333, 13.069, 0, 0.767, -10.988, 0, 1.217, 17.586, 1, 1.339, 17.586, 1.461, -3.191, 1.583, -24.149, 1, 1.6, -27.007, 1.616, -25.88, 1.633, -25.88, 0, 1.883, 28.688, 0, 2.2, -11.457, 0, 2.55, 3.24, 0, 2.917, -0.608, 0, 3.233, -0.057, 2, 3.383, -0.057, 2, 3.817, -0.057, 1, 3.822, -0.057, 3.828, 9.971, 3.833, 9.971, 0, 4.417, 5.635, 2, 4.667, 5.635]}, {"Target": "Parameter", "Id": "Jewelry_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.417, 2, 0.333, 1.417, 0, 0.633, -1.925, 0, 1.033, 2.429, 0, 1.45, -3.11, 1, 1.494, -3.11, 1.539, -2.977, 1.583, -1.859, 1, 1.689, 0.796, 1.794, 2.807, 1.9, 2.807, 0, 2.317, -1.652, 0, 2.733, 1.155, 0, 3.167, -0.66, 2, 3.383, -0.66, 2, 3.817, -0.66, 0, 3.833, 0.297, 0, 4.417, -0.088, 2, 4.667, -0.088]}, {"Target": "Parameter", "Id": "Jewelry_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.801, 2, 0.333, 0.801, 0, 0.483, 3.993, 0, 0.833, -4.844, 0, 1.233, 5.713, 1, 1.35, 5.713, 1.466, 0.486, 1.583, -5.461, 1, 1.611, -6.877, 1.639, -6.658, 1.667, -6.658, 0, 2.117, 5.18, 0, 2.517, -3.231, 0, 2.933, 2.595, 0, 3.367, -1.18, 2, 3.383, -1.18, 2, 3.817, -1.18, 0, 3.833, 0.492, 0, 4.417, 0.194, 2, 4.667, 0.194]}, {"Target": "Parameter", "Id": "Jewelry_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.312, 2, 0.333, -3.312, 0, 0.633, 4.475, 0, 0.983, -5.037, 0, 1.367, 5.224, 1, 1.439, 5.224, 1.511, 3.416, 1.583, 0.003, 1, 1.65, -3.148, 1.716, -4.626, 1.783, -4.626, 0, 2.233, 4.026, 0, 2.65, -3.134, 0, 3.05, 2.137, 2, 3.383, 2.137, 2, 3.817, 2.137, 0, 3.833, -0.295, 0, 4.417, 0.324, 2, 4.667, 0.324]}, {"Target": "Parameter", "Id": "Physics_Joint_5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.113, 2, 0.333, 0.113, 0, 0.517, -2.062, 0, 0.9, 2.414, 0, 1.35, -3.531, 1, 1.428, -3.531, 1.505, -1.572, 1.583, 1.018, 1, 1.616, 2.128, 1.65, 2.27, 1.683, 2.27, 0, 2.117, -1.146, 0, 2.533, 0.754, 0, 2.95, -0.455, 0, 3.367, 0.301, 2, 3.383, 0.301, 2, 3.817, 0.301, 1, 3.822, 0.301, 3.828, -0.027, 3.833, -0.032, 1, 4.028, -0.211, 4.222, -0.293, 4.417, -0.293, 2, 4.667, -0.293]}, {"Target": "Parameter", "Id": "Physics_Joint_4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.968, 2, 0.333, 2.968, 0, 0.383, 3.448, 0, 0.7, -4.485, 0, 1.083, 4.816, 0, 1.5, -6.408, 1, 1.528, -6.408, 1.555, -6.514, 1.583, -5.172, 1, 1.683, -0.34, 1.783, 3.756, 1.883, 3.756, 0, 2.317, -2.215, 0, 2.733, 1.568, 0, 3.15, -0.898, 2, 3.383, -0.898, 2, 3.817, -0.898, 0, 3.833, 0.723, 0, 4.417, -0.111, 2, 4.667, -0.111]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 4.25, 2, 0.333, 4.25, 0, 0.633, -5.774, 0, 1.033, 7.288, 0, 1.45, -9.331, 1, 1.494, -9.331, 1.539, -8.914, 1.583, -5.577, 1, 1.689, 2.349, 1.794, 8.42, 1.9, 8.42, 0, 2.317, -4.955, 0, 2.733, 3.464, 0, 3.167, -1.98, 2, 3.383, -1.98, 2, 3.817, -1.98, 0, 3.833, 0.891, 0, 4.417, -0.264, 2, 4.667, -0.264]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.402, 2, 0.333, 2.402, 0, 0.483, 11.979, 0, 0.833, -14.533, 0, 1.233, 17.14, 1, 1.35, 17.14, 1.466, 1.339, 1.583, -16.382, 1, 1.611, -20.601, 1.639, -19.974, 1.667, -19.974, 0, 2.117, 15.539, 0, 2.517, -9.692, 0, 2.933, 7.786, 0, 3.367, -3.54, 2, 3.383, -3.54, 2, 3.817, -3.54, 0, 3.833, 1.476, 0, 4.417, 0.581, 2, 4.667, 0.581]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -9.934, 2, 0.333, -9.934, 0, 0.633, 13.426, 0, 0.983, -15.11, 0, 1.367, 15.673, 1, 1.439, 15.673, 1.511, 10.251, 1.583, 0.011, 1, 1.65, -9.441, 1.716, -13.878, 1.783, -13.878, 0, 2.233, 12.079, 0, 2.65, -9.401, 0, 3.05, 6.412, 2, 3.383, 6.412, 2, 3.817, 6.412, 0, 3.833, -0.884, 0, 4.417, 0.972, 2, 4.667, 0.972]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -6.598, 2, 0.333, -6.598, 0, 0.433, -11.222, 0, 0.75, 16.749, 0, 1.083, -20.062, 0, 1.45, 20.838, 1, 1.494, 20.838, 1.539, 19.125, 1.583, 10.711, 1, 1.672, -6.117, 1.761, -18.288, 1.85, -18.288, 0, 2.317, 15.159, 0, 2.733, -12.923, 0, 3.133, 9.459, 2, 3.383, 9.459, 2, 3.817, 9.459, 1, 3.822, 9.459, 3.828, 0.527, 3.833, 0.527, 0, 4.417, 1.479, 2, 4.667, 1.479]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.577, 2, 0.333, 1.577, 0, 0.55, -12.54, 0, 0.85, 20.18, 0, 1.167, -25.228, 0, 1.517, 26.49, 1, 1.539, 26.49, 1.561, 27.595, 1.583, 22.797, 1, 1.694, -1.193, 1.806, -23.18, 1.917, -23.18, 0, 2.383, 18.267, 0, 2.817, -16.592, 0, 3.217, 13.258, 2, 3.383, 13.258, 2, 3.817, 13.258, 1, 3.822, 13.258, 3.828, 0.874, 3.833, 0.874, 0, 4.417, 1.483, 2, 4.667, 1.483]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 4.656, 2, 0.333, 4.656, 0, 0.35, 4.729, 0, 0.65, -13.965, 0, 0.933, 23.828, 0, 1.233, -30, 2, 1.25, -30, 0, 1.567, 30, 2, 1.583, 30, 2, 1.6, 30, 0, 1.983, -27.924, 0, 2.433, 21.078, 0, 2.883, -19.9, 0, 3.3, 17.392, 2, 3.383, 17.392, 2, 3.817, 17.392, 1, 3.822, 17.392, 3.828, 0.251, 3.833, 0.236, 1, 4.028, -0.277, 4.222, -0.513, 4.417, -0.513, 2, 4.667, -0.513]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.656, 2, 0.333, 1.656, 0, 0.467, 4.451, 0, 0.75, -15.122, 0, 1.017, 27.354, 0, 1.283, -30, 2, 1.35, -30, 1, 1.428, -30, 1.505, 0.651, 1.583, 29.241, 1, 1.589, 30, 1.594, 30, 1.6, 30, 2, 1.683, 30, 0, 2.017, -30, 2, 2.05, -30, 0, 2.483, 23.208, 0, 2.95, -22.242, 0, 3.367, 21.139, 2, 3.383, 21.139, 2, 3.817, 21.139, 1, 3.822, 21.139, 3.828, -1.59, 3.833, -1.67, 1, 4.028, -4.46, 4.222, -5.752, 4.417, -5.752, 2, 4.667, -5.752]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1, 1, 0, 2.833, 0, 0, 4, 1, 0, 4.417, 0, 2, 4.667, 0]}, {"Target": "Parameter", "Id": "fenshangxuanzhuang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 4.417, 0, 1, 4.5, 0.044, 4.584, 0.089, 4.667, 0.133]}, {"Target": "Parameter", "Id": "touch_drag6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.625, 0, 4.417, 0, 2, 4.667, 0]}, {"Target": "Parameter", "Id": "MB_Lightning_Splinter_B", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 4.667, 1]}, {"Target": "Parameter", "Id": "ParamSad2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 4.667, 1]}, {"Target": "Parameter", "Id": "ParamCRY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.2, 0, 4.667, -5.2]}, {"Target": "Parameter", "Id": "Position_Coordinates", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 4.667, 1]}, {"Target": "Parameter", "Id": "Man_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 4.667, 1]}, {"Target": "Parameter", "Id": "Man_TMD2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "BG_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 4.667, 1]}, {"Target": "Parameter", "Id": "FG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "BG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "BG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "BG_White5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "BG_White2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "BG_White3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.3, 0, 4.667, 0.3]}, {"Target": "Parameter", "Id": "All_Size4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "tuerzuo3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "faqiehuan", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "faqiehuan2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "faqiehuan4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "faqiehuan3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "tuerzuo4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Mail_Change", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "dafengshangzhuanchang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "tongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamTongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "EyeFrame_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamBrowY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamSigh", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamDarken", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamCry", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_B", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_C", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "MB_Unidirectional_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "huqi1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "huqi2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamArmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 4.667, 1]}, {"Target": "Parameter", "Id": "ParamForearmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -30, 0, 4.667, -30]}, {"Target": "Parameter", "Id": "ParamArmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamArmL_ForearmL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 4.667, 1]}, {"Target": "Parameter", "Id": "ParamForearmL_HandL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamHandLDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 8, 0, 4.667, 8]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamHandLSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "xiongjiya", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 4.667, 1]}, {"Target": "Parameter", "Id": "xiongjiya2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamArmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 4.667, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 30, 0, 4.667, 30]}, {"Target": "Parameter", "Id": "ParamArmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamArmR_ForearmR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 4.667, 1]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamForearm<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 4.667, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_HandR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamHandRSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 4.667, 1]}, {"Target": "Parameter", "Id": "Change_idle_physics7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 4.667, 0.6]}, {"Target": "Parameter", "Id": "Change_idle_physics23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 4.667, 1]}, {"Target": "Parameter", "Id": "Change_idle_physics24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 4.667, 1]}, {"Target": "Parameter", "Id": "Change_idle_physics20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamLegL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 4.667, 1]}, {"Target": "Parameter", "Id": "ParamLegLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamLegLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamCrusLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamCrusLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>oodLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamFoodLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamFoodLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamLegR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 4.667, 1]}, {"Target": "Parameter", "Id": "ParamLegRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamLegRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamCrusRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamCrusRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamCrus<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>ood<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamFoodRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamFoodRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Physics_Provisional", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "physics_Add_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "physics_Add_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Physics_AngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Physics_AngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Physics_Body_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Physics_Body_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Physics_breast", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamBreath2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "touch_drag1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "touch_drag2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "touch_drag3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "touch_drag4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "touch_drag5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "touch_drag7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "touch_drag8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "touch_drag9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>pian<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh654", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh655", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh657", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh656", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh658", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh659", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh660", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.667, 0]}], "UserData": [{"Time": 4.167, "Value": ""}]}