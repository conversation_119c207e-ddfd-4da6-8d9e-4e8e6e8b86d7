{"Version": 3, "Meta": {"Duration": 6.833, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 310, "TotalSegmentCount": 1580, "TotalPointCount": 1798, "UserDataCount": 1, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Mail_Transparency6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.946, 2, 0.333, 0.946, 0, 3.9, 0, 2, 6.25, 0, 0, 6.267, 1, 2, 6.583, 1, 2, 6.833, 1]}, {"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 1.45, 0, 1, 1.633, 0, 1.817, 6.921, 2, 8.423, 1, 2.222, 10.243, 2.445, 10.136, 2.667, 10.136, 2, 3.883, 10.136, 2, 3.9, 24.724, 2, 6.583, 24.724, 2, 6.833, 24.724]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 1.45, 0, 1, 1.633, 0, 1.817, -2.045, 2, -2.922, 1, 2.222, -3.985, 2.445, -4.02, 2.667, -4.02, 2, 3.883, -4.02, 2, 3.9, -0.988, 1, 4.794, 0.119, 5.689, 1.227, 6.583, 2.334, 2, 6.833, 2.334]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 3.883, 0, 2, 3.9, -0.72, 2, 6.583, -0.72, 2, 6.833, -0.72]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 3.883, 0, 2, 3.9, 23, 2, 6.583, 23, 2, 6.833, 23]}, {"Target": "Parameter", "Id": "FG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 3.167, 0, 0, 3.883, 1, 0, 4.483, 0, 2, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "BG_White4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 3.883, 0.971, 2, 3.9, 0, 2, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "All_Size2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -12.72, 2, 1.7, -12.72, 0, 2.483, -20.52, 0, 3.883, -12.72, 1, 3.889, -12.72, 3.894, -27, 3.9, -27, 2, 6.583, -27, 2, 6.833, -27]}, {"Target": "Parameter", "Id": "All_Size3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.7, 0, 2, 3.883, 0, 1, 3.889, 0, 3.894, -9, 3.9, -9, 2, 6.583, -9, 2, 6.833, -9]}, {"Target": "Parameter", "Id": "faqiehuan", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 3.383, 0, 2, 3.883, 0, 2, 3.9, -1, 2, 6.267, -1, 2, 6.583, -1, 2, 6.833, -1]}, {"Target": "Parameter", "Id": "Mail_Transparency2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.333, 1, 0, 0.917, 0, 2, 3.883, 0, 2, 3.9, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 2.75, 1, 0, 2.833, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.333, 0.5, 2, 0.5, 0.5, 0, 0.833, 1, 2, 6.25, 1, 2, 6.583, 1, 2, 6.833, 1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 3.383, 0, 2, 3.883, 0, 2, 3.9, 0, 2, 4.883, 0, 0, 5, -1, 0, 5.15, 1, 0, 5.3, -0.4, 0, 5.4, 0.3, 0, 5.483, 0, 2, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY_SD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 3.383, 0, 2, 3.883, 0, 2, 3.9, 0, 2, 3.917, 0, 2, 4.317, 0, 2, 4.633, 0, 0, 4.917, 1, 2, 5.583, 1, 1, 5.639, 1, 5.694, 0.655, 5.75, 0.5, 1, 5.894, 0.098, 6.039, 0, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 3.9, 0, 0, 3.917, 3, 2, 6.25, 3, 2, 6.583, 3, 2, 6.833, 3]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.333, 1, 2, 1.383, 1, 0, 1.517, 0, 0, 1.7, 1.3, 0, 1.783, 1, 2, 3.383, 1, 2, 3.883, 1, 0, 3.9, 0, 1, 4.022, 0, 4.145, 0.655, 4.267, 1, 1, 4.328, 1.173, 4.389, 1.132, 4.45, 1.132, 1, 4.744, 1.132, 5.039, 1.131, 5.333, 1.102, 1, 5.472, 1.088, 5.611, 0, 5.75, 0, 0, 6.167, 1, 2, 6.583, 1, 2, 6.833, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.333, 1, 2, 1.383, 1, 0, 1.517, 0, 0, 1.7, 1.3, 0, 1.783, 1, 2, 3.383, 1, 2, 3.883, 1, 0, 3.9, 0, 1, 4.022, 0, 4.145, 0.655, 4.267, 1, 1, 4.328, 1.173, 4.389, 1.132, 4.45, 1.132, 1, 4.744, 1.132, 5.039, 1.131, 5.333, 1.102, 1, 5.472, 1.088, 5.611, 0, 5.75, 0, 0, 6.167, 1, 2, 6.583, 1, 2, 6.833, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 3.383, 0, 2, 3.883, 0, 2, 3.9, 0, 2, 4.15, 0, 0, 4.517, 0.1, 0, 4.917, 0, 2, 4.95, 0, 2, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 3.383, 0, 2, 3.883, 0, 2, 3.9, 0, 0, 4.15, -1, 0, 4.517, 0.4, 0, 4.917, 0, 2, 4.95, 0, 2, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 3.383, 0, 2, 3.883, 0, 2, 3.9, 0, 0, 4.717, 0.6, 1, 5, 0.6, 5.284, 0.605, 5.567, 0.546, 1, 5.772, 0.503, 5.978, 0, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1, 2, 0.333, -1, 2, 1.8, -1, 0, 2.933, 0, 2, 3.383, 0, 2, 3.883, 0, 2, 3.9, 0, 2, 6.183, 0, 2, 6.25, 0, 2, 6.4, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamBrowAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.4, 2, 0.333, 0.4, 2, 1.8, 0.4, 0, 2.933, 0, 2, 3.383, 0, 2, 3.883, 0, 2, 3.9, 0, 2, 4.133, 0, 0, 4.55, 1, 1, 4.878, 1, 5.205, 1, 5.533, 0.9, 1, 5.75, 0.83, 5.966, 0, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.001, 0, 0.433, 0, 2, 0.45, 0, 2, 1.383, 0, 0, 1.45, 1, 2, 1.6, 1, 0, 1.617, -1, 2, 1.75, -1, 0, 2.05, 0.244, 0, 2.367, -0.068, 0, 2.667, 0.019, 0, 2.967, -0.005, 2, 2.983, -0.005, 0, 3.283, 0.002, 2, 3.3, 0.002, 0, 3.317, 0.001, 2, 3.333, 0.001, 0, 3.533, 0, 2, 3.55, 0, 2, 3.567, 0, 2, 3.65, 0, 2, 3.667, 0, 2, 3.683, 0, 2, 3.7, 0, 2, 3.717, 0, 2, 3.733, 0, 2, 3.767, 0, 2, 3.783, 0, 2, 3.817, 0, 2, 3.833, 0, 2, 3.883, 0, 0, 3.9, 1, 0, 3.917, -1, 2, 4.067, -1, 0, 4.45, 0.368, 0, 4.767, -0.099, 0, 5.083, 0.033, 0, 5.367, 0, 0, 5.717, 1, 2, 5.85, 1, 0, 6.033, -1, 2, 6.117, -1, 0, 6.217, 1, 2, 6.317, 1, 0, 6.333, -1, 2, 6.417, -1, 0, 6.583, 0.508, 2, 6.833, 0.508]}, {"Target": "Parameter", "Id": "ParamEyeRsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.001, 0, 0.433, 0, 2, 0.45, 0, 2, 1.383, 0, 0, 1.45, 1, 2, 1.6, 1, 0, 1.617, -1, 2, 1.75, -1, 0, 2.05, 0.244, 0, 2.367, -0.068, 0, 2.667, 0.019, 0, 2.967, -0.005, 2, 2.983, -0.005, 0, 3.283, 0.002, 2, 3.3, 0.002, 0, 3.317, 0.001, 2, 3.333, 0.001, 0, 3.533, 0, 2, 3.55, 0, 2, 3.567, 0, 2, 3.65, 0, 2, 3.667, 0, 2, 3.683, 0, 2, 3.7, 0, 2, 3.717, 0, 2, 3.733, 0, 2, 3.767, 0, 2, 3.783, 0, 2, 3.817, 0, 2, 3.833, 0, 2, 3.883, 0, 0, 3.9, 1, 0, 3.917, -1, 2, 4.067, -1, 0, 4.45, 0.368, 0, 4.767, -0.099, 0, 5.083, 0.033, 0, 5.367, 0, 0, 5.717, 1, 2, 5.85, 1, 0, 6.033, -1, 2, 6.117, -1, 0, 6.217, 1, 2, 6.317, 1, 0, 6.333, -1, 2, 6.417, -1, 0, 6.583, 0.508, 2, 6.833, 0.508]}, {"Target": "Parameter", "Id": "ParamEyeLGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.002, 0, 0.4, 0, 2, 0.417, 0, 2, 0.433, 0, 2, 0.45, 0, 2, 0.467, 0, 2, 0.55, 0, 2, 0.567, 0, 2, 0.6, 0, 2, 0.617, 0, 2, 0.667, 0, 2, 0.683, 0, 2, 0.733, 0, 2, 0.75, 0, 2, 0.917, 0, 2, 0.933, 0, 2, 1.383, 0, 0, 1.45, -0.305, 0, 1.5, -0.2, 0, 1.6, -1, 2, 1.617, -1, 0, 1.633, 1, 2, 1.667, 1, 0, 1.7, 0.831, 2, 1.717, 0.831, 0, 1.75, 0.863, 0, 2, -0.314, 0, 2.267, 0.153, 0, 2.55, -0.069, 0, 2.85, 0.028, 0, 3.15, -0.01, 0, 3.433, 0.004, 2, 3.467, 0.004, 0, 3.717, -0.001, 2, 3.733, -0.001, 2, 3.75, -0.001, 2, 3.783, -0.001, 0, 3.883, 0, 0, 3.917, -1, 0, 4.017, -0.074, 0, 4.1, -0.095, 0, 4.283, 0.044, 0, 4.417, -0.08, 0, 4.633, 0.146, 0, 4.933, -0.087, 0, 5.233, 0.038, 0, 5.533, -0.015, 0, 5.617, -0.01, 0, 5.717, -0.286, 0, 5.95, 1, 2, 5.967, 1, 0, 6.2, -0.794, 0, 6.233, -0.578, 0, 6.283, -1, 2, 6.3, -1, 0, 6.317, 1, 2, 6.333, 1, 0, 6.5, -0.811, 0, 6.583, -0.498, 2, 6.833, -0.498]}, {"Target": "Parameter", "Id": "ParamEyeRGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.002, 0, 0.4, 0, 2, 0.417, 0, 2, 0.433, 0, 2, 0.45, 0, 2, 0.467, 0, 2, 0.55, 0, 2, 0.567, 0, 2, 0.6, 0, 2, 0.617, 0, 2, 0.667, 0, 2, 0.683, 0, 2, 0.733, 0, 2, 0.75, 0, 2, 0.917, 0, 2, 0.933, 0, 2, 1.383, 0, 0, 1.45, -0.305, 0, 1.5, -0.2, 0, 1.6, -1, 2, 1.617, -1, 0, 1.633, 1, 2, 1.667, 1, 0, 1.7, 0.831, 2, 1.717, 0.831, 0, 1.75, 0.863, 0, 2, -0.314, 0, 2.267, 0.153, 0, 2.55, -0.069, 0, 2.85, 0.028, 0, 3.15, -0.01, 0, 3.433, 0.004, 2, 3.467, 0.004, 0, 3.717, -0.001, 2, 3.733, -0.001, 2, 3.75, -0.001, 2, 3.783, -0.001, 0, 3.883, 0, 0, 3.917, -1, 0, 4.017, -0.074, 0, 4.1, -0.095, 0, 4.283, 0.044, 0, 4.417, -0.08, 0, 4.633, 0.146, 0, 4.933, -0.087, 0, 5.233, 0.038, 0, 5.533, -0.015, 0, 5.617, -0.01, 0, 5.717, -0.286, 0, 5.95, 1, 2, 5.967, 1, 0, 6.2, -0.794, 0, 6.233, -0.578, 0, 6.283, -1, 2, 6.3, -1, 0, 6.317, 1, 2, 6.333, 1, 0, 6.5, -0.811, 0, 6.583, -0.498, 2, 6.833, -0.498]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.3, 0, 2, 1.317, -5, 2, 1.533, -5, 2, 1.55, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.3, 0, 2, 1.317, -5, 2, 1.533, -5, 2, 1.55, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.7, 2, 0.333, -3.7, 0, 3.383, 0, 2, 3.883, 0, 2, 3.9, 0, 2, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10.794, 2, 0.333, 10.794, 0, 3.383, 0, 2, 3.883, 0, 2, 3.9, 0, 2, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.284, 2, 0.333, -5.284, 0, 3.383, 0, 2, 3.883, 0, 2, 3.9, 0, 2, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -13.132, 2, 0.333, -13.132, 0, 3.383, 0, 2, 3.883, 0, 2, 3.9, 0, 2, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamShoulderLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.168, 2, 0.333, 0.168, 0, 0.85, 2, 0, 1.517, -1, 0, 2.233, 2, 0, 2.983, -1, 2, 3.383, -1, 2, 3.883, -1, 0, 3.9, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -30, 2, 0.333, -30, 2, 1.867, -30, 2, 3.383, -30, 2, 3.883, -30, 1, 3.889, -30, 3.894, 0, 3.9, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -8.2, 2, 0.333, -8.2, 2, 1.45, -8.2, 0, 2.15, -20.41, 2, 3.383, -20.41, 2, 3.883, -20.41, 1, 3.889, -20.41, 3.894, 0, 3.9, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.076, 2, 0.333, 0.076, 0, 2.15, 0.06, 2, 3.383, 0.06, 2, 3.883, 0.06, 0, 3.9, 30, 2, 6.583, 30, 2, 6.833, 30]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 3.883, 1, 0, 3.9, -1, 2, 6.583, -1, 2, 6.833, -1]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.551, 2, 0.333, -0.551, 0, 0.45, -0.6, 0, 0.867, -0.242, 0, 1.267, -0.6, 0, 1.5, -0.377, 0, 1.983, -0.6, 2, 3.383, -0.6, 2, 3.883, -0.6, 0, 3.9, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamHandLDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 8, 2, 3.883, 8, 2, 3.9, 10, 2, 6.583, 10, 2, 6.833, 10]}, {"Target": "Parameter", "Id": "xiongjiya", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.333, 1, 2, 1.45, 1, 2, 2.083, 1, 2, 3.883, 1, 0, 3.9, 0, 2, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "xiongjiya3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.663, 2, 0.333, 0.663, 2, 3.383, 0.663, 2, 3.883, 0.663, 0, 3.9, 0, 2, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "xiongjiya4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.663, 2, 0.333, 0.663, 2, 3.383, 0.663, 2, 3.883, 0.663, 0, 3.9, 0, 2, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamShoulderRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.168, 2, 0.333, 0.168, 0, 0.85, 2, 0, 1.517, -1, 0, 2.233, 2, 0, 2.983, -1, 2, 3.383, -1, 2, 3.883, -1, 0, 3.9, 0, 2, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 3.383, 0, 2, 3.883, 0, 2, 3.9, 0, 2, 6.183, 0, 2, 6.25, 0, 0, 6.267, 1, 2, 6.583, 1, 2, 6.833, 1]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 30, 2, 0.333, 30, 2, 1.867, 30, 2, 3.383, 30, 2, 3.883, 30, 1, 3.889, 30, 3.894, 0, 3.9, 0, 2, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -7.9, 2, 0.333, -7.9, 2, 1.45, -7.9, 1, 1.683, -10.128, 1.917, -12.356, 2.15, -14.584, 2, 3.383, -14.584, 2, 3.883, -14.584, 1, 3.889, -14.584, 3.894, 0, 3.9, 0, 2, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.333, 1, 2, 3.383, 1, 2, 3.883, 1, 0, 3.9, 0, 2, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.72, 2, 0.333, -0.72, 2, 2.767, -0.72, 0, 3.883, 0, 2, 3.9, 0, 2, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.539, 2, 0.333, -0.539, 0, 0.45, -0.6, 0, 0.867, -0.157, 0, 1.267, -0.6, 0, 1.5, -0.323, 0, 1.983, -0.6, 2, 3.883, -0.6, 0, 3.917, 0, 2, 6.183, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamHandRDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 8, 2, 0.333, 8, 2, 2.75, 8, 2, 3.883, 8, 1, 3.889, 8, 3.894, 0, 3.9, 0, 2, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.333, 1, 2, 2.75, 1, 2, 3.883, 1, 0, 3.9, 0, 2, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.333, 1, 2, 1.717, 1, 0, 1.983, 0, 2, 2.15, 0, 2, 3.383, 0, 2, 3.883, 0, 2, 3.9, 0, 2, 6.183, 0, 2, 6.25, 0, 0, 6.267, 1, 2, 6.583, 1, 2, 6.833, 1]}, {"Target": "Parameter", "Id": "Change_idle_physics3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.333, 1, 2, 2.75, 1, 2, 3.883, 1, 0, 3.9, 0, 2, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.159, 2, 0.333, -0.159, 1, 0.416, -0.159, 0.5, -0.155, 0.583, -0.17, 1, 0.778, -0.204, 0.972, -0.259, 1.167, -0.259, 1, 1.289, -0.259, 1.411, -0.141, 1.533, -0.089, 1, 1.578, -0.07, 1.622, -0.081, 1.667, -0.066, 1, 1.728, -0.046, 1.789, 0.03, 1.85, 0.03, 0, 2.75, -0.352, 2, 3.383, -0.352, 2, 3.883, -0.352, 0, 3.9, 0, 2, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.3, 2, 0.333, -0.3, 2, 0.583, -0.3, 2, 1.717, -0.3, 0, 2.05, -0.298, 2, 2.75, -0.298, 2, 3.383, -0.298, 2, 3.883, -0.298, 0, 3.9, 0, 2, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.622, 2, 0.333, 0.622, 0, 1.1, 0.704, 0, 1.6, 0.58, 2, 3.883, 0.58, 0, 3.9, 0, 2, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.603, 2, 0.333, 0.603, 0, 0.833, 0.679, 2, 1.233, 0.679, 0, 1.517, 0, 2, 3.383, 0, 2, 3.883, 0, 2, 3.9, 0, 2, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.555, 2, 0.333, -0.555, 0, 0.767, 0.8, 0, 1.217, -1, 1, 1.311, -1, 1.406, -0.99, 1.5, -0.9, 1, 1.533, -0.868, 1.567, 0.956, 1.6, 0.956, 0, 1.633, 0.92, 0, 1.717, 1, 2, 3.383, 1, 2, 3.883, 1, 0, 3.9, 0, 2, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 3.883, 1, 2, 3.9, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 3.15, 0, 0, 3.383, 1, 2, 3.883, 1, 0, 3.9, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 1.6, 0, 1, 1.683, -0.102, 1.767, -0.204, 1.85, -0.306, 1, 1.917, -0.356, 1.983, -0.406, 2.05, -0.456, 1, 2.261, -0.576, 2.472, -0.696, 2.683, -0.816, 1, 2.816, -0.839, 2.95, -0.861, 3.083, -0.884, 1, 3.094, -0.891, 3.106, -0.898, 3.117, -0.905, 1, 3.134, -0.905, 3.15, -0.906, 3.167, -0.906, 1, 3.195, -0.903, 3.222, -0.899, 3.25, -0.896, 2, 3.883, -0.896, 1, 3.889, -0.597, 3.894, -0.299, 3.9, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 1.6, 0, 1, 1.683, 0.076, 1.767, 0.151, 1.85, 0.227, 1, 1.917, 0.262, 1.983, 0.296, 2.05, 0.331, 1, 2.261, 0.349, 2.472, 0.368, 2.683, 0.386, 1, 2.816, 0.423, 2.95, 0.459, 3.083, 0.496, 1, 3.094, 0.494, 3.106, 0.492, 3.117, 0.49, 1, 3.134, 0.501, 3.15, 0.511, 3.167, 0.522, 1, 3.195, 0.517, 3.222, 0.513, 3.25, 0.508, 2, 3.883, 0.508, 1, 3.889, 0.339, 3.894, 0.169, 3.9, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 1.85, 0, 1, 2.067, -0.215, 2.283, -0.429, 2.5, -0.644, 1, 2.561, -0.615, 2.622, -0.585, 2.683, -0.556, 1, 3.044, -0.482, 3.406, -0.409, 3.767, -0.335, 2, 3.9, -0.335, 1, 4.683, -0.223, 5.467, -0.112, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 1.85, 0, 1, 2.067, 0.094, 2.283, 0.188, 2.5, 0.282, 1, 2.561, 0.264, 2.622, 0.246, 2.683, 0.228, 1, 3.044, 0.135, 3.406, 0.043, 3.767, -0.05, 2, 3.9, -0.05, 1, 4.683, -0.033, 5.467, -0.017, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.019, 2, 0.333, -0.019, 0, 1.85, -0.1, 2, 2.517, -0.1, 1, 2.572, -0.1, 2.628, -0.067, 2.683, -0.04, 1, 2.8, 0.016, 2.916, 0.024, 3.033, 0.104, 1, 3.133, 0.172, 3.233, 0.556, 3.333, 0.556, 2, 3.883, 0.556, 0, 3.9, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "Param103", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 26.961, 2, 0.333, 26.961, 1, 0.839, 26.961, 1.344, 26.915, 1.85, 26.1, 1, 2.067, 7.4, 2.283, -11.3, 2.5, -30, 1, 2.794, -20, 3.089, -10, 3.383, 0, 1, 3.55, 3.86, 3.716, 7.72, 3.883, 11.58, 0, 3.9, 0, 2, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 3.167, 0, 1, 3.189, 0, 3.211, -0.015, 3.233, 0.062, 1, 3.255, 0.139, 3.278, 1, 3.3, 1, 1, 3.328, 1, 3.355, 0.62, 3.383, 0.062, 1, 3.411, -0.496, 3.439, -0.695, 3.467, -0.695, 1, 3.495, -0.695, 3.522, -0.404, 3.55, 0.039, 1, 3.578, 0.482, 3.605, 0.647, 3.633, 0.647, 1, 3.661, 0.647, 3.689, 0.201, 3.717, 0.039, 1, 3.745, -0.123, 3.772, -0.113, 3.8, -0.113, 0, 3.883, 0, 2, 3.9, 0, 2, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 3.167, 0, 0, 3.233, -1, 1, 3.255, -1, 3.278, -0.512, 3.3, 0.062, 1, 3.328, 0.78, 3.355, 1, 3.383, 1, 1, 3.411, 1, 3.439, 0.594, 3.467, 0.039, 1, 3.495, -0.516, 3.522, -0.695, 3.55, -0.695, 1, 3.578, -0.695, 3.605, -0.404, 3.633, 0.039, 1, 3.661, 0.482, 3.689, 0.647, 3.717, 0.647, 1, 3.745, 0.647, 3.772, 0.439, 3.8, 0.238, 1, 3.828, 0.037, 3.855, 0, 3.883, 0, 2, 3.9, 0, 2, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 3.383, 0, 2, 4.25, 0, 0, 4.967, -1.68, 0, 5.617, 0, 2, 6.183, 0, 0, 6.25, 0.003, 0, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -9.8, 2, 0.333, -9.8, 0, 0.767, 8.24, 0, 1.217, -13.185, 0, 1.817, 30, 2, 3.383, 30, 0, 4.25, -17.7, 0, 4.967, 2.64, 0, 5.617, 0, 2, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 3.383, 0, 2, 3.883, 0, 2, 3.9, 0, 2, 4.583, 0, 0, 5.4, 7, 0, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 3.383, 0, 2, 3.883, 0, 2, 3.9, 0, 0, 5.4, 4.783, 0, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -4, 2, 0.333, -4, 0, 0.767, 4, 1, 0.917, 4, 1.067, -1.941, 1.217, -4, 1, 1.311, -5.296, 1.406, -5, 1.5, -5, 0, 1.883, 4.768, 0, 2.2, 0, 2, 3.383, 0, 2, 3.883, 0, 1, 3.889, 0, 3.894, -7.795, 3.9, -7.795, 0, 5.4, 0, 0, 6.183, -7.795, 2, 6.267, -7.795, 2, 6.583, -7.795, 2, 6.833, -7.795]}, {"Target": "Parameter", "Id": "ParamBodyStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.017, 2, 0.333, -2.017, 0, 0.55, 8.411, 0, 1.15, -10, 0, 1.4, 3.534, 0, 1.633, -3, 0, 1.967, 0, 2, 3.383, 0, 0, 3.983, -10, 0, 4.817, 0.95, 0, 5.2, -3, 0, 5.667, 0.795, 0, 6.067, -3.094, 0, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 3.383, 0, 2, 3.883, 0, 2, 3.9, 0, 0, 6.15, 3.269, 0, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamLegRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 2, 3.383, 0, 2, 3.883, 0, 2, 3.9, 0, 0, 5.467, 7.732, 0, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.399, 2, 0.333, 0.399, 0, 3.367, 1, 2, 3.383, 1, 2, 3.883, 1, 0, 3.9, 0, 2, 6.183, 0, 2, 6.25, 0, 2, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "Physics_AngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.644, 1, 0.111, -5.644, 0.222, -5.645, 0.333, -5.644, 1, 0.489, -5.643, 0.644, 5.804, 0.8, 5.804, 0, 1.25, -8.871, 0, 1.85, 19.186, 0, 2.25, 17.183, 0, 2.633, 17.284, 0, 2.933, 17.279, 2, 2.95, 17.279, 2, 2.983, 17.279, 2, 3.083, 17.279, 2, 3.1, 17.279, 2, 3.167, 17.279, 2, 3.183, 17.279, 2, 3.3, 17.279, 2, 3.317, 17.279, 2, 3.383, 17.279, 0, 4.267, -11.361, 0, 5, 2.199, 0, 5.617, -0.3, 0, 5.8, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "Physics_AngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 4.25, 0, 0, 4.967, 1.4, 0, 5.617, 0, 2, 6.183, 0, 0, 6.25, -0.003, 0, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.819, 1, 0.117, 1.819, 0.233, 1.655, 0.35, -0.847, 1, 0.433, -2.634, 0.517, -7.629, 0.6, -7.629, 0, 1, 18.599, 0, 1.433, -24.117, 0, 1.517, -20.942, 0, 1.567, -22.901, 0, 1.9, 26.694, 0, 2.317, -16.954, 0, 2.733, 10.781, 0, 3.15, -6.858, 0, 3.65, 8.207, 0, 3.883, 1.45, 1, 3.889, 1.45, 3.894, 7.324, 3.9, 7.324, 0, 4.267, -8.937, 0, 4.733, 3.619, 0, 5.267, -0.701, 0, 5.3, -0.678, 0, 5.417, -1.03, 0, 5.85, 1, 0, 6.25, -1.318, 0, 6.583, 0.671, 2, 6.833, 0.671]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.361, 0, 0.35, -1.913, 0, 0.483, 0.266, 0, 0.833, -23.974, 0, 1.117, 30, 2, 1.333, 30, 0, 1.517, -30, 2, 1.85, -30, 1, 1.9, -30, 1.95, 30, 2, 30, 2, 2.233, 30, 0, 2.45, -30, 2, 2.583, -30, 0, 2.933, 22.913, 0, 3.35, -14.536, 0, 3.867, 20.009, 0, 3.9, 13.509, 0, 4.067, 18.796, 0, 4.5, -17.401, 0, 4.933, 4.504, 0, 5.3, -0.89, 0, 5.45, 0.53, 0, 5.717, -2.295, 0, 6.15, 3.511, 0, 6.5, -3.036, 0, 6.583, -1.979, 2, 6.833, -1.979]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_L", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.595, 1, 0.117, -0.595, 0.233, -0.282, 0.35, 0.279, 1, 0.394, 0.493, 0.439, 0.571, 0.483, 0.571, 0, 0.85, -3.804, 0, 1.267, 6.83, 0, 1.733, -9.175, 0, 2.183, 6.897, 0, 2.633, -4.259, 0, 3.083, 2.68, 0, 3.55, -2.227, 0, 4.017, 4.968, 0, 4.583, -3.428, 0, 5.183, 1.298, 0, 5.717, -1.141, 0, 6.117, 0.955, 0, 6.583, -0.679, 2, 6.833, -0.679]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_R", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.287, 2, 0.333, 2.287, 0, 0.767, -1.923, 0, 1.217, 3.077, 0, 1.583, -3.425, 0, 1.867, 7.876, 0, 2.283, -4.665, 0, 2.75, 2.764, 0, 3.2, -1.776, 0, 3.6, 1.535, 0, 4.033, -4.362, 0, 4.617, 3.545, 0, 5.217, -1.608, 0, 5.667, 0.615, 0, 5.85, -0.855, 0, 6.1, 0.671, 0, 6.35, -0.527, 0, 6.5, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.001, 0, 0.333, 0.003, 0, 0.617, -8.773, 0, 1.017, 12.719, 0, 1.483, -16.806, 0, 1.933, 6.372, 0, 2.3, -1.565, 0, 2.667, 0.384, 0, 3.017, -0.094, 0, 3.767, 10.339, 0, 4.433, -4.77, 0, 5.15, 8.463, 0, 6.117, -1.987, 0, 6.45, 2.012, 0, 6.583, 0.969, 2, 6.833, 0.969]}, {"Target": "Parameter", "Id": "L_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.004, 0, 0.5, 3.307, 0, 0.783, -7.283, 0, 1.15, 6.043, 0, 1.617, -4.284, 0, 2.05, 4.721, 0, 2.333, -2.095, 0, 2.65, 0.606, 0, 2.967, -0.116, 0, 3.383, 0.016, 0, 3.583, -2.822, 0, 3.883, 2.07, 0, 4.083, 0.065, 0, 4.283, 1.782, 0, 4.533, -1.543, 0, 4.7, -0.64, 0, 5.033, -4.755, 0, 5.267, 1.157, 0, 5.467, -0.393, 0, 5.8, 2.509, 0, 5.95, 1.605, 0, 6, 2.333, 0, 6.267, -1.466, 0, 6.3, -1.409, 0, 6.367, -3.825, 0, 6.583, 2.192, 2, 6.833, 2.192]}, {"Target": "Parameter", "Id": "R_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -13.069, 2, 0.333, -13.069, 0, 0.767, 10.988, 0, 1.217, -17.585, 0, 1.683, 28.754, 0, 2.1, -7.098, 0, 2.55, 1.342, 0, 3.017, -0.254, 0, 3.383, 0.035, 0, 3.85, -14.776, 0, 4.583, 11.594, 0, 5.1, 2.006, 0, 5.567, 7.318, 0, 5.967, 0, 2, 6.183, 0, 0, 6.25, -0.016, 0, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 13.069, 2, 0.333, 13.069, 0, 0.767, -10.988, 0, 1.217, 17.585, 0, 1.633, -25.88, 0, 1.883, 28.596, 0, 2.2, -11.819, 0, 2.55, 3.272, 0, 2.9, -0.611, 0, 3.25, 0.049, 0, 3.383, 0.015, 0, 3.633, 4.428, 0, 4.05, -3.775, 0, 4.233, -3.468, 0, 4.267, -3.487, 0, 4.683, 2.127, 0, 4.8, 1.251, 0, 4.933, 4.76, 0, 5.25, -4.794, 0, 5.833, 3.936, 0, 6.017, 0, 2, 6.183, 0, 0, 6.25, 0.016, 0, 6.267, 0, 2, 6.583, 0, 2, 6.833, 0]}, {"Target": "Parameter", "Id": "Jewelry_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.184, 1, 0.117, 0.184, 0.233, 0.168, 0.35, -0.087, 1, 0.433, -0.269, 0.517, -0.722, 0.6, -0.722, 0, 1, 1.728, 0, 1.45, -2.652, 0, 1.9, 2.495, 0, 2.317, -1.577, 0, 2.733, 1.001, 0, 3.15, -0.636, 0, 3.667, 1.108, 0, 4.267, -0.851, 0, 4.833, 0.282, 0, 4.917, 0.277, 0, 4.983, 0.284, 0, 5.417, -0.145, 0, 5.833, 0.104, 0, 6.25, -0.07, 0, 6.583, 0.035, 2, 6.833, 0.035]}, {"Target": "Parameter", "Id": "Jewelry_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.028, 0, 0.35, -0.191, 0, 0.483, 0.01, 2, 0.5, 0.01, 0, 0.833, -2.28, 0, 1.217, 4.315, 0, 1.667, -5.906, 0, 2.117, 4.657, 0, 2.517, -3.227, 0, 2.933, 2.097, 0, 3.35, -1.342, 0, 3.917, 2.829, 0, 4.517, -1.509, 0, 5.233, 0.449, 0, 5.617, -0.277, 0, 5.633, -0.274, 0, 5.667, -0.285, 0, 6.067, 0.223, 0, 6.483, -0.157, 0, 6.583, -0.107, 2, 6.833, -0.107]}, {"Target": "Parameter", "Id": "Jewelry_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.336, 1, 0.144, -0.336, 0.289, -0.322, 0.433, -0.099, 1, 0.511, 0.021, 0.589, 0.692, 0.667, 0.692, 0, 1, -2.361, 0, 1.383, 3.832, 0, 1.8, -3.984, 0, 2.233, 3.767, 0, 2.633, -2.929, 0, 3.05, 1.965, 0, 3.467, -1.277, 0, 4.067, 1.263, 0, 4.633, -0.588, 0, 5.033, 0.133, 0, 5.183, 0.074, 0, 5.4, 0.254, 0, 5.783, -0.232, 0, 6.133, 0.179, 0, 6.583, -0.127, 2, 6.833, -0.127]}, {"Target": "Parameter", "Id": "Physics_Joint_5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.163, 1, 0.111, 0.163, 0.222, 0.155, 0.333, -0.108, 1, 0.394, -0.252, 0.456, -1.331, 0.517, -1.331, 0, 0.883, 2.088, 0, 1.333, -3.317, 0, 1.683, 2.284, 0, 2.1, -1.174, 0, 2.517, 0.742, 0, 2.933, -0.471, 0, 3.35, 0.299, 0, 3.4, 0.288, 0, 3.667, 0.517, 0, 4.167, -0.783, 0, 4.7, 0.33, 0, 4.85, 0.29, 0, 5, 0.326, 0, 5.417, -0.59, 0, 5.867, 0.746, 0, 6.283, -0.786, 0, 6.583, 0.367, 2, 6.833, 0.367]}, {"Target": "Parameter", "Id": "Physics_Joint_4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.211, 1, 0.117, -0.211, 0.233, -0.185, 0.35, -0.054, 1, 0.383, -0.017, 0.417, 0.377, 0.45, 0.377, 0, 0.7, -3.144, 0, 1.083, 4.396, 0, 1.5, -6.193, 0, 1.883, 3.797, 0, 2.3, -2.301, 0, 2.717, 1.504, 0, 3.133, -0.982, 0, 3.917, 1.573, 0, 4.417, -1.599, 0, 5.233, 0.891, 0, 5.633, -1.398, 0, 6.083, 1.76, 0, 6.5, -1.726, 0, 6.583, -1.377, 2, 6.833, -1.377]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.553, 1, 0.117, 0.553, 0.233, 0.502, 0.35, -0.26, 1, 0.433, -0.804, 0.517, -2.167, 0.6, -2.167, 0, 1, 5.185, 0, 1.45, -7.956, 0, 1.9, 7.485, 0, 2.317, -4.73, 0, 2.733, 3.002, 0, 3.15, -1.908, 0, 3.667, 3.325, 0, 4.267, -2.552, 0, 4.833, 0.846, 0, 4.917, 0.831, 0, 4.983, 0.851, 0, 5.417, -0.436, 0, 5.833, 0.311, 0, 6.25, -0.209, 0, 6.583, 0.104, 2, 6.833, 0.104]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.083, 0, 0.35, -0.573, 0, 0.5, 0.031, 0, 0.833, -6.839, 0, 1.217, 12.945, 0, 1.667, -17.719, 0, 2.117, 13.97, 0, 2.517, -9.682, 0, 2.933, 6.29, 0, 3.35, -4.026, 0, 3.917, 8.486, 0, 4.517, -4.527, 0, 5.233, 1.346, 0, 5.617, -0.83, 0, 5.633, -0.823, 0, 5.667, -0.855, 0, 6.067, 0.668, 0, 6.483, -0.47, 0, 6.583, -0.322, 2, 6.833, -0.322]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.008, 1, 0.144, -1.008, 0.289, -0.966, 0.433, -0.297, 1, 0.511, 0.063, 0.589, 2.075, 0.667, 2.075, 0, 1, -7.082, 0, 1.383, 11.497, 0, 1.8, -11.952, 0, 2.233, 11.3, 0, 2.633, -8.787, 0, 3.05, 5.895, 0, 3.467, -3.832, 0, 4.067, 3.789, 0, 4.633, -1.764, 0, 5.033, 0.4, 0, 5.183, 0.221, 0, 5.4, 0.762, 0, 5.783, -0.695, 0, 6.133, 0.536, 0, 6.583, -0.382, 2, 6.833, -0.382]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.738, 1, 0.172, -2.738, 0.345, -2.368, 0.517, -0.555, 1, 0.606, 0.381, 0.694, 2.154, 0.783, 2.154, 0, 1.1, -8.247, 0, 1.467, 14.279, 0, 1.867, -15.652, 0, 2.317, 14.283, 0, 2.733, -12.246, 0, 3.133, 8.796, 0, 3.55, -5.811, 0, 4.15, 4.38, 0, 4.633, -2.292, 0, 5.133, 0.754, 0, 5.333, 0.316, 0, 5.517, 0.704, 0, 5.867, -1.178, 0, 6.233, 0.929, 0, 6.583, -0.391, 2, 6.833, -0.391]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.141, 1, 0.206, -5.141, 0.411, -4.278, 0.617, -1.106, 1, 0.711, 0.352, 0.806, 2.515, 0.9, 2.515, 0, 1.2, -9.517, 0, 1.55, 17.247, 0, 1.95, -19.705, 0, 2.383, 17.416, 0, 2.817, -15.977, 0, 3.217, 12.527, 0, 3.617, -8.692, 0, 4.217, 4.715, 0, 4.7, -3.237, 0, 5.2, 1.133, 0, 5.483, 0.14, 0, 5.667, 0.527, 0, 6, -1.337, 0, 6.35, 1.33, 0, 6.583, -0.273, 2, 6.833, -0.273]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.58, 0, 0.333, 5.057, 0, 0.7, -2.188, 0, 1, 2.825, 0, 1.3, -10.891, 0, 1.633, 20.302, 0, 2.017, -23.705, 0, 2.45, 20.532, 0, 2.883, -19.522, 0, 3.3, 16.729, 0, 3.7, -12.493, 0, 4.15, 5.69, 0, 4.783, -4.242, 0, 5.267, 1.673, 0, 5.6, -0.09, 0, 5.8, 0.396, 0, 6.117, -1.555, 0, 6.45, 1.838, 0, 6.583, 0.889, 2, 6.833, 0.889]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 6.918, 0, 0.417, 8.442, 0, 0.783, -4.173, 0, 1.1, 3.81, 0, 1.4, -12.5, 0, 1.683, 23.568, 0, 2.067, -27.451, 0, 2.5, 23.076, 0, 2.95, -22.299, 0, 3.367, 20.743, 0, 3.783, -16.883, 0, 4.2, 8.87, 0, 4.883, -5.036, 0, 5.317, 2.601, 0, 5.7, -0.556, 0, 5.933, 0.399, 0, 6.233, -1.787, 0, 6.567, 2.462, 0, 6.583, 2.442, 2, 6.833, 2.442]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1, 1, 0, 2.833, 0, 0, 4, 1, 0, 5.833, 0, 0, 6.583, 0.708, 2, 6.833, 0.708]}, {"Target": "Parameter", "Id": "fenshangxuanzhuang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 2.194, 0.901, 4.389, 1.802, 6.583, 2.703, 2, 6.833, 2.703]}, {"Target": "Parameter", "Id": "MB_Lightning_Splinter_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "MB_Lightning_Splinter_B", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "MB_Lightning_Splinter_C", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamSad2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "Parameter", "Id": "ParamCRY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.2, 0, 6.833, -5.2]}, {"Target": "Parameter", "Id": "Position_Coordinates", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "Parameter", "Id": "Man_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "Parameter", "Id": "Man_TMD2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "BG_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "Parameter", "Id": "FG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "BG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "BG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "BG_White5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "BG_White2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "BG_White3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.3, 0, 6.833, 0.3]}, {"Target": "Parameter", "Id": "All_Size4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "tuerzuo3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "faqiehuan2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "faqiehuan4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "faqiehuan3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "tuerzuo4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Mail_Change", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "dafengshangzhuanchang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "tongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamTongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "EyeFrame_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamBrowForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamBrowY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamSigh", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamDarken", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamCry", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_B", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_C", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "MB_Unidirectional_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "jing<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "huqi1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "huqi2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamArmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "Parameter", "Id": "ParamForearmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamArmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamArmL_ForearmL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmL_HandL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamHandLSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "xiongjiya5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "xiongjiya2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamArmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "Parameter", "Id": "ParamArmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamArmR_ForearmR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamForearm<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_HandR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamHandRSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 6.833, 0.6]}, {"Target": "Parameter", "Id": "Change_idle_physics23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "taiqiugundong", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -22.5, 0, 6.833, -22.5]}, {"Target": "Parameter", "Id": "Change_idle_physics32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamLegL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "Parameter", "Id": "ParamLegLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamLegLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamCrusLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamCrusLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>oodLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamFoodLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamFoodLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamLegR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "Parameter", "Id": "ParamLegRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamCrusRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamCrusRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamCrus<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>ood<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamFoodRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamFoodRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Physics_Provisional", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "physics_Add_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "physics_Add_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Physics_AngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Physics_Body_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Physics_Body_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Physics_breast", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamBreath2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "touch_drag1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "touch_drag2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "touch_drag3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "touch_drag4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "touch_drag5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "touch_drag6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 6.833, 1]}, {"Target": "Parameter", "Id": "touch_drag7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "touch_drag8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "touch_drag9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>pian<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh654", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh655", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh657", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh656", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh658", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh659", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh660", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.833, 0]}], "UserData": [{"Time": 6.333, "Value": ""}]}