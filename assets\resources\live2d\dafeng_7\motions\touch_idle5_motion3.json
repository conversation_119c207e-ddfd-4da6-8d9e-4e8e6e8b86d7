{"Version": 3, "Meta": {"Duration": 13.25, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 303, "TotalSegmentCount": 3598, "TotalPointCount": 3864, "UserDataCount": 1, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Mail_Transparency6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 0, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 1.417, -0.171, 2.583, -0.341, 3.75, -0.512, 2, 3.767, -1.44, 1, 4.595, -0.96, 5.422, -0.48, 6.25, 0, 1, 7.578, 0.371, 8.905, 0.741, 10.233, 1.112, 1, 10.372, 1.155, 10.511, 7.776, 10.65, 8.423, 1, 10.872, 9.458, 11.095, 10.26, 11.317, 10.556, 1, 11.55, 10.867, 11.784, 10.856, 12.017, 10.856, 2, 12.467, 10.856, 2, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.14, 2, 0.25, -1.14, 1, 1.417, -1.233, 2.583, -1.327, 3.75, -1.42, 2, 3.767, -3.63, 1, 4.595, -2.8, 5.422, -1.97, 6.25, -1.14, 1, 7.578, -0.904, 8.905, -0.667, 10.233, -0.431, 0, 10.65, -2.922, 1, 10.872, -2.922, 11.095, -0.37, 11.317, 0.48, 1, 11.55, 1.373, 11.784, 1.32, 12.017, 1.32, 2, 12.467, 1.32, 2, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 3.54, 2, 0.25, 3.54, 1, 1.417, 3.688, 2.583, 3.835, 3.75, 3.983, 2, 3.767, 6.264, 1, 4.595, 5.356, 5.422, 4.448, 6.25, 3.54, 1, 7.578, 2.36, 8.905, 1.18, 10.233, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.16, 2, 0.25, 2.16, 1, 1.417, 3.474, 2.583, 4.788, 3.75, 6.102, 2, 3.767, 10.26, 1, 4.595, 7.56, 5.422, 4.86, 6.25, 2.16, 1, 7.578, 1.44, 8.905, 0.72, 10.233, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "FG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.85, 0, 2, 1.433, 0, 2, 2.017, 0, 2, 2.417, 0, 2, 2.833, 0, 2, 3.233, 0, 2, 3.65, 0, 2, 4.05, 0, 2, 4.467, 0, 2, 4.867, 0, 2, 5.283, 0, 2, 5.6, 0, 2, 5.933, 0, 2, 6.25, 0, 2, 6.65, 0, 2, 7.25, 0, 2, 7.65, 0, 2, 8.05, 0, 2, 8.65, 0, 2, 9.05, 0, 2, 9.45, 0, 2, 10.05, 0, 2, 10.233, 0, 2, 11.75, 0, 0, 12.467, 1, 0, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "BG_White4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.989, 2, 0.25, 0.989, 0, 0.433, 1, 0, 0.85, 0.989, 0, 1.017, 1, 0, 1.433, 0.989, 0, 1.6, 1, 0, 2.017, 0.989, 0, 2.133, 1, 0, 2.417, 0.989, 0, 2.533, 1, 0, 2.833, 0.989, 0, 2.95, 1, 0, 3.233, 0.989, 0, 3.35, 1, 0, 3.65, 0.989, 0, 3.767, 1, 0, 4.05, 0.989, 0, 4.167, 1, 0, 4.467, 0.989, 0, 4.583, 1, 0, 4.867, 0.989, 0, 4.983, 1, 0, 5.283, 0.989, 0, 5.367, 1, 0, 5.6, 0.989, 0, 5.683, 1, 0, 5.933, 0.989, 0, 6.017, 1, 0, 6.25, 0.989, 0, 6.367, 1, 0, 6.65, 0.989, 0, 6.967, 1, 0, 7.25, 0.989, 0, 7.367, 1, 0, 7.65, 0.989, 0, 7.767, 1, 0, 8.05, 0.989, 0, 8.367, 1, 0, 8.65, 0.989, 0, 8.767, 1, 0, 9.05, 0.989, 0, 9.167, 1, 0, 9.45, 0.989, 0, 9.767, 1, 0, 10.05, 0.989, 0, 10.167, 1, 0, 10.233, 0.971, 2, 12.033, 0.971, 2, 12.467, 0.971, 0, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "BG_White3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.3, 2, 0.25, 0.3, 2, 10.05, 0.3, 1, 10.811, 0.3, 11.572, 0.267, 12.333, 0.2, 2, 12.35, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "All_Size2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -12.72, 2, 0.25, -12.72, 2, 10.05, -12.72, 1, 10.111, -12.72, 10.172, -12.537, 10.233, -14.173, 1, 10.428, -19.377, 10.622, -24.24, 10.817, -24.24, 2, 12.467, -24.24, 0, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "All_Size3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 10.05, 0, 1, 10.111, 0, 10.172, 0.014, 10.233, -0.106, 1, 10.428, -0.488, 10.622, -0.84, 10.817, -0.84, 2, 12.467, -0.84, 0, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 10.05, 1, 0, 11.1, 0, 2, 12.1, 0, 2, 12.467, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 12.467, 1, 0, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.733, -1, 2, 3.167, -1, 0, 3.55, 0.1, 2, 5.483, 0.1, 0, 6.883, -1, 0, 9.05, 0, 2, 10.233, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY_SD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 0, 1.733, 0.5, 2, 3.167, 0.5, 1, 3.295, 0.5, 3.422, 0.884, 3.55, 0.9, 1, 4.194, 0.979, 4.839, 1, 5.483, 1, 0, 6.883, 0.5, 0, 9.05, 1, 0, 10.233, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 6.25, 1, 2, 7.25, 1, 2, 7.65, 1, 2, 8.65, 1, 2, 9.05, 1, 2, 10.05, 1, 0, 10.233, 0, 2, 12.467, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 0, 0.533, 1.3, 0, 0.75, 0, 2, 1.2, 0, 0, 1.75, 0.538, 2, 2.383, 0.538, 0, 2.533, 0, 0, 2.617, 1.3, 0, 2.733, 1.252, 2, 3.183, 1.252, 0, 3.283, 0, 2, 6.35, 0, 0, 6.9, 0.538, 2, 7.533, 0.538, 0, 7.683, 0, 0, 7.767, 1.3, 1, 7.806, 1.3, 7.844, 1.3, 7.883, 1.252, 1, 7.966, 1.069, 8.05, 0, 8.133, 0, 2, 8.583, 0, 1, 8.739, 0, 8.894, 0.498, 9.05, 1, 1, 9.139, 1.287, 9.228, 1.3, 9.317, 1.3, 0, 9.533, 0, 2, 9.983, 0, 1, 10.066, 0, 10.15, 0.042, 10.233, 0.39, 1, 10.272, 0.553, 10.311, 1.3, 10.35, 1.3, 0, 10.433, 1, 2, 12.033, 1, 2, 12.467, 1, 2, 12.483, 1, 2, 12.933, 1, 2, 13.25, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 0, 0.533, 1.3, 0, 0.75, 0, 2, 1.2, 0, 0, 1.75, 0.538, 2, 2.383, 0.538, 0, 2.533, 0, 0, 2.617, 1.3, 0, 2.733, 1.252, 2, 3.183, 1.252, 0, 3.283, 0, 2, 6.35, 0, 0, 6.9, 0.538, 2, 7.533, 0.538, 0, 7.683, 0, 0, 7.767, 1.3, 1, 7.806, 1.3, 7.844, 1.3, 7.883, 1.252, 1, 7.966, 1.069, 8.05, 0, 8.133, 0, 2, 8.583, 0, 1, 8.739, 0, 8.894, 0.498, 9.05, 1, 1, 9.139, 1.287, 9.228, 1.3, 9.317, 1.3, 0, 9.533, 0, 2, 9.983, 0, 1, 10.066, 0, 10.15, 0.042, 10.233, 0.39, 1, 10.272, 0.553, 10.311, 1.3, 10.35, 1.3, 0, 10.433, 1, 2, 12.033, 1, 2, 12.467, 1, 2, 12.483, 1, 2, 12.933, 1, 2, 13.25, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.533, 0, 1, 0.661, 0, 0.789, -0.377, 0.917, -0.5, 1, 1.034, -0.613, 1.15, -0.6, 1.267, -0.6, 1, 1.406, -0.6, 1.544, 0.446, 1.683, 0.5, 1, 1.939, 0.6, 2.194, 0.6, 2.45, 0.6, 0, 2.617, 0, 2, 3.75, 0, 2, 4.133, 0, 0, 6.417, -0.6, 1, 6.556, -0.6, 6.694, 0.446, 6.833, 0.5, 1, 7.089, 0.6, 7.344, 0.6, 7.6, 0.6, 1, 7.656, 0.6, 7.711, 0.066, 7.767, 0, 1, 7.945, -0.21, 8.122, -0.367, 8.3, -0.5, 1, 8.417, -0.587, 8.533, -0.6, 8.65, -0.6, 0, 9.05, 0, 2, 9.317, 0, 1, 9.445, 0, 9.572, -0.377, 9.7, -0.5, 1, 9.817, -0.613, 9.933, -0.6, 10.05, -0.6, 0, 10.233, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.533, 0, 1, 0.661, 0, 0.789, -0.209, 0.917, -0.4, 1, 1.034, -0.575, 1.15, -0.6, 1.267, -0.6, 0, 1.683, 0.2, 0, 2.45, 0, 2, 2.617, 0, 2, 3.75, 0, 2, 4.133, 0, 0, 6.417, -0.6, 0, 6.833, 0.2, 0, 7.6, 0, 2, 7.767, 0, 1, 7.945, 0, 8.122, -0.163, 8.3, -0.4, 1, 8.417, -0.556, 8.533, -0.6, 8.65, -0.6, 0, 9.05, 0, 2, 9.317, 0, 1, 9.445, 0, 9.572, -0.209, 9.7, -0.4, 1, 9.817, -0.575, 9.933, -0.6, 10.05, -0.6, 0, 10.233, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1, 2, 0.25, -1, 2, 9.05, -1, 2, 10.233, -1, 2, 10.45, -1, 0, 11.583, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamBrowForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1, 2, 0.25, -1, 2, 9.05, -1, 0, 10.233, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamBrowAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.284, 2, 0.25, 0.284, 0, 0.717, 0.4, 0, 0.85, 0.284, 0, 1.3, 0.4, 0, 1.433, 0.284, 0, 1.883, 0.4, 0, 2.017, 0.284, 0, 2.333, 0.4, 0, 2.417, 0.284, 0, 2.733, 0.4, 0, 2.833, 0.284, 0, 3.15, 0.4, 0, 3.233, 0.284, 0, 3.55, 0.4, 0, 3.65, 0.284, 0, 3.967, 0.4, 0, 4.05, 0.284, 0, 4.367, 0.4, 0, 4.467, 0.284, 0, 4.783, 0.4, 0, 4.867, 0.284, 0, 5.183, 0.4, 0, 5.283, 0.284, 0, 5.533, 0.4, 0, 5.6, 0.284, 0, 5.85, 0.4, 0, 5.933, 0.284, 0, 6.183, 0.4, 2, 6.45, 0.4, 0, 6.583, 0.284, 0, 7.033, 0.4, 0, 7.167, 0.284, 0, 7.483, 0.4, 0, 7.567, 0.284, 0, 7.883, 0.4, 2, 8.1, 0.4, 0, 8.233, 0.284, 0, 8.683, 0.4, 0, 8.817, 0.284, 2, 9.05, 0.284, 0, 9.5, 0.4, 0, 9.633, 0.284, 0, 10.083, 0.4, 0, 10.217, 0.284, 0, 10.233, 0.4, 2, 10.45, 0.4, 0, 11.583, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamEyeLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.45, -0.43, 0, 0.617, 1, 2, 0.817, 1, 0, 1.067, -0.344, 0, 1.267, -0.048, 0, 1.517, -0.331, 0, 1.85, 0.175, 0, 2.167, -0.049, 0, 2.5, 1, 2, 2.517, 1, 0, 2.6, -1, 2, 2.967, -1, 0, 3.217, 1, 2, 3.317, 1, 0, 3.333, -1, 2, 3.467, -1, 0, 3.683, 0.429, 0, 4, -0.118, 0, 4.317, 0.033, 0, 4.617, -0.009, 2, 4.633, -0.009, 0, 4.917, 0.003, 2, 4.933, 0.003, 0, 5.2, -0.001, 2, 5.283, -0.001, 0, 5.317, 0, 2, 5.333, 0, 2, 5.4, 0, 2, 5.417, 0, 2, 5.45, 0, 2, 5.483, 0, 2, 5.5, 0, 2, 5.617, 0, 2, 5.633, 0, 2, 5.683, 0, 2, 5.7, 0, 2, 5.8, 0, 2, 5.817, 0, 2, 5.9, 0, 2, 5.917, 0, 2, 6.35, 0, 0, 6.633, -0.368, 0, 7, 0.179, 0, 7.3, -0.05, 0, 7.65, 1, 2, 7.667, 1, 0, 7.75, -1, 2, 7.917, -1, 0, 7.933, 1, 2, 8.05, 1, 0, 8.25, -0.957, 0, 8.567, 0.24, 0, 8.9, -0.68, 0, 9.4, 1, 2, 9.55, 1, 0, 9.783, -0.372, 0, 10.033, 0.052, 0, 10.267, -1, 2, 10.4, -1, 0, 10.717, 0.225, 0, 11.033, -0.063, 0, 11.35, 0.018, 0, 11.65, -0.005, 2, 11.667, -0.005, 0, 11.95, 0.001, 2, 11.983, 0.001, 0, 12.183, 0, 2, 12.2, 0, 2, 12.217, 0, 2, 12.233, 0, 2, 12.25, 0, 2, 12.317, 0, 2, 12.333, 0, 2, 12.35, 0, 2, 12.367, 0, 2, 12.4, 0, 2, 12.417, 0, 2, 12.433, 0, 2, 12.45, 0, 2, 12.483, 0, 2, 12.5, 0, 2, 12.7, 0, 2, 12.717, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamEyeRsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.45, -0.43, 0, 0.617, 1, 2, 0.817, 1, 0, 1.067, -0.344, 0, 1.267, -0.048, 0, 1.517, -0.331, 0, 1.85, 0.175, 0, 2.167, -0.049, 0, 2.5, 1, 2, 2.517, 1, 0, 2.6, -1, 2, 2.967, -1, 0, 3.217, 1, 2, 3.317, 1, 0, 3.333, -1, 2, 3.467, -1, 0, 3.683, 0.429, 0, 4, -0.118, 0, 4.317, 0.033, 0, 4.617, -0.009, 2, 4.633, -0.009, 0, 4.917, 0.003, 2, 4.933, 0.003, 0, 5.2, -0.001, 2, 5.283, -0.001, 0, 5.317, 0, 2, 5.333, 0, 2, 5.4, 0, 2, 5.417, 0, 2, 5.45, 0, 2, 5.483, 0, 2, 5.5, 0, 2, 5.617, 0, 2, 5.633, 0, 2, 5.683, 0, 2, 5.7, 0, 2, 5.8, 0, 2, 5.817, 0, 2, 5.9, 0, 2, 5.917, 0, 2, 6.35, 0, 0, 6.633, -0.368, 0, 7, 0.179, 0, 7.3, -0.05, 0, 7.65, 1, 2, 7.667, 1, 0, 7.75, -1, 2, 7.917, -1, 0, 7.933, 1, 2, 8.05, 1, 0, 8.25, -0.957, 0, 8.567, 0.24, 0, 8.9, -0.68, 0, 9.4, 1, 2, 9.55, 1, 0, 9.783, -0.372, 0, 10.033, 0.052, 0, 10.267, -1, 2, 10.4, -1, 0, 10.717, 0.225, 0, 11.033, -0.063, 0, 11.35, 0.018, 0, 11.65, -0.005, 2, 11.667, -0.005, 0, 11.95, 0.001, 2, 11.983, 0.001, 0, 12.183, 0, 2, 12.2, 0, 2, 12.217, 0, 2, 12.233, 0, 2, 12.25, 0, 2, 12.317, 0, 2, 12.333, 0, 2, 12.35, 0, 2, 12.367, 0, 2, 12.4, 0, 2, 12.417, 0, 2, 12.433, 0, 2, 12.45, 0, 2, 12.483, 0, 2, 12.5, 0, 2, 12.7, 0, 2, 12.717, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamEyeLGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.417, 0.124, 0, 0.6, -0.416, 0, 0.85, 1, 2, 1.033, 1, 0, 1.3, -0.222, 0, 1.533, 0.058, 0, 1.767, -0.115, 0, 2.033, 0.1, 0, 2.317, -0.05, 0, 2.383, -0.041, 0, 2.483, -0.274, 0, 2.567, 0.567, 0, 2.6, 0.403, 0, 2.683, 1, 2, 2.95, 1, 0, 3.167, -0.374, 0, 3.183, -0.368, 0, 3.2, -0.409, 0, 3.267, -0.138, 0, 3.417, -0.581, 0, 3.817, 0.317, 0, 4.133, -0.14, 0, 4.45, 0.055, 0, 4.75, -0.02, 2, 4.767, -0.02, 0, 5.067, 0.007, 0, 5.35, -0.002, 2, 5.367, -0.002, 2, 5.383, -0.002, 0, 5.65, 0.001, 2, 5.733, 0.001, 0, 5.783, 0, 2, 5.8, 0, 2, 5.867, 0, 2, 5.883, 0, 2, 5.9, 0, 2, 5.917, 0, 2, 5.933, 0, 2, 6.083, 0, 2, 6.1, 0, 2, 6.15, 0, 2, 6.167, 0, 2, 6.233, 0, 2, 6.25, 0, 2, 6.35, 0, 0, 6.533, 0.084, 0, 6.867, -0.125, 0, 7.15, 0.112, 0, 7.45, -0.055, 0, 7.533, -0.04, 0, 7.633, -0.271, 0, 7.717, 0.571, 0, 7.75, 0.405, 0, 7.833, 1, 2, 7.9, 1, 0, 7.917, -1, 2, 7.95, -1, 0, 8.133, 0.757, 0, 8.417, -0.488, 0, 8.733, 0.332, 0, 9.033, -0.172, 0, 9.067, -0.169, 0, 9.167, -0.199, 0, 9.333, -0.032, 0, 9.4, -0.176, 0, 9.65, 0.943, 0, 9.983, -0.316, 0, 10.25, 0.288, 0, 10.433, -0.823, 0, 10.85, 0.254, 0, 11.167, -0.099, 0, 11.483, 0.036, 0, 11.783, -0.013, 0, 12.1, 0.004, 0, 12.4, -0.001, 2, 12.417, -0.001, 0, 12.667, 0, 2, 12.783, 0, 2, 12.8, 0, 2, 12.817, 0, 2, 12.833, 0, 2, 12.85, 0, 2, 12.867, 0, 2, 12.883, 0, 2, 12.9, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamEyeRGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.417, 0.124, 0, 0.6, -0.416, 0, 0.85, 1, 2, 1.033, 1, 0, 1.3, -0.222, 0, 1.533, 0.058, 0, 1.767, -0.115, 0, 2.033, 0.1, 0, 2.317, -0.05, 0, 2.383, -0.041, 0, 2.483, -0.274, 0, 2.567, 0.567, 0, 2.6, 0.403, 0, 2.683, 1, 2, 2.95, 1, 0, 3.167, -0.374, 0, 3.183, -0.368, 0, 3.2, -0.409, 0, 3.267, -0.138, 0, 3.417, -0.581, 0, 3.817, 0.317, 0, 4.133, -0.14, 0, 4.45, 0.055, 0, 4.75, -0.02, 2, 4.767, -0.02, 0, 5.067, 0.007, 0, 5.35, -0.002, 2, 5.367, -0.002, 2, 5.383, -0.002, 0, 5.65, 0.001, 2, 5.733, 0.001, 0, 5.783, 0, 2, 5.8, 0, 2, 5.867, 0, 2, 5.883, 0, 2, 5.9, 0, 2, 5.917, 0, 2, 5.933, 0, 2, 6.083, 0, 2, 6.1, 0, 2, 6.15, 0, 2, 6.167, 0, 2, 6.233, 0, 2, 6.25, 0, 2, 6.35, 0, 0, 6.533, 0.084, 0, 6.867, -0.125, 0, 7.15, 0.112, 0, 7.45, -0.055, 0, 7.533, -0.04, 0, 7.633, -0.271, 0, 7.717, 0.571, 0, 7.75, 0.405, 0, 7.833, 1, 2, 7.9, 1, 0, 7.917, -1, 2, 7.95, -1, 0, 8.133, 0.757, 0, 8.417, -0.488, 0, 8.733, 0.332, 0, 9.033, -0.172, 0, 9.067, -0.169, 0, 9.167, -0.199, 0, 9.333, -0.032, 0, 9.4, -0.176, 0, 9.65, 0.943, 0, 9.983, -0.316, 0, 10.25, 0.288, 0, 10.433, -0.823, 0, 10.85, 0.254, 0, 11.167, -0.099, 0, 11.483, 0.036, 0, 11.783, -0.013, 0, 12.1, 0.004, 0, 12.4, -0.001, 2, 12.417, -0.001, 0, 12.667, 0, 2, 12.783, 0, 2, 12.8, 0, 2, 12.817, 0, 2, 12.833, 0, 2, 12.85, 0, 2, 12.867, 0, 2, 12.883, 0, 2, 12.9, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5, 2, 0.25, 5, 2, 3.867, 5, 0, 3.883, 0, 2, 10.517, 0, 2, 11.35, 0, 0, 11.367, -5, 2, 12.467, -5, 0, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 3.867, 0, 0, 3.883, -5, 2, 10.517, -5, 2, 11.35, -5, 1, 11.356, -5, 11.361, 5, 11.367, 5, 2, 12.467, 5, 0, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 9.05, 1, 0, 10.233, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "jing<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.85, 0, 2, 1.433, 0, 2, 2.017, 0, 2, 2.417, 0, 2, 2.833, 0, 2, 3.233, 0, 2, 3.65, 0, 2, 4.05, 0, 2, 4.467, 0, 2, 4.867, 0, 2, 5.283, 0, 2, 5.6, 0, 2, 5.933, 0, 2, 6.583, 0, 2, 7.167, 0, 2, 7.567, 0, 2, 8.233, 0, 2, 8.817, 0, 2, 9.05, 0, 2, 9.633, 0, 2, 10.217, 0, 2, 10.233, 0, 2, 10.5, 0, 0, 10.567, 1, 2, 10.583, 1, 2, 10.617, 1, 2, 10.633, 1, 2, 10.65, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.374, 2, 0.25, -2.374, 2, 0.85, -2.374, 2, 1.433, -2.374, 2, 2.017, -2.374, 2, 2.417, -2.374, 2, 2.833, -2.374, 2, 3.233, -2.374, 2, 3.65, -2.374, 2, 4.05, -2.374, 2, 4.467, -2.374, 2, 4.867, -2.374, 2, 5.283, -2.374, 2, 5.6, -2.374, 2, 5.933, -2.374, 2, 6.583, -2.374, 2, 7.167, -2.374, 2, 7.567, -2.374, 2, 8.233, -2.374, 2, 8.817, -2.374, 2, 9.05, -2.374, 2, 9.633, -2.374, 2, 10.217, -2.374, 1, 10.222, -2.374, 10.228, -9.563, 10.233, -9.591, 1, 10.333, -10.092, 10.433, -10.52, 10.533, -11.02, 1, 10.539, -11.048, 10.544, -11.597, 10.55, -11.597, 1, 10.583, -11.597, 10.617, -11.057, 10.65, -7.8, 1, 10.689, -4, 10.728, 0, 10.767, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 6.925, 2, 0.25, 6.925, 2, 0.85, 6.925, 2, 1.433, 6.925, 2, 2.017, 6.925, 2, 2.417, 6.925, 2, 2.833, 6.925, 2, 3.233, 6.925, 2, 3.65, 6.925, 2, 4.05, 6.925, 2, 4.467, 6.925, 2, 4.867, 6.925, 2, 5.283, 6.925, 2, 5.6, 6.925, 2, 5.933, 6.925, 2, 6.583, 6.925, 2, 7.167, 6.925, 2, 7.567, 6.925, 2, 8.233, 6.925, 2, 8.817, 6.925, 2, 9.05, 6.925, 2, 9.633, 6.925, 2, 10.217, 6.925, 1, 10.222, 6.925, 10.228, 2.086, 10.233, 2.049, 1, 10.333, 1.384, 10.433, 1.1, 10.533, 1.1, 1, 10.539, 1.1, 10.544, 0.706, 10.55, 1.341, 1, 10.583, 5.152, 10.617, 21.78, 10.65, 21.78, 0, 10.767, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -4.027, 2, 0.25, -4.027, 2, 0.85, -4.027, 2, 1.433, -4.027, 2, 2.017, -4.027, 2, 2.417, -4.027, 2, 2.833, -4.027, 2, 3.233, -4.027, 2, 3.65, -4.027, 2, 4.05, -4.027, 2, 4.467, -4.027, 2, 4.867, -4.027, 2, 5.283, -4.027, 2, 5.6, -4.027, 2, 5.933, -4.027, 2, 6.583, -4.027, 2, 7.167, -4.027, 2, 7.567, -4.027, 2, 8.233, -4.027, 2, 8.817, -4.027, 2, 9.05, -4.027, 2, 9.633, -4.027, 2, 10.217, -4.027, 1, 10.222, -4.027, 10.228, -6.796, 10.233, -6.798, 1, 10.333, -6.828, 10.433, -6.84, 10.533, -6.84, 1, 10.572, -6.84, 10.611, -1.055, 10.65, -0.48, 1, 10.689, 0.095, 10.728, 0, 10.767, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10.008, 2, 0.25, -10.008, 2, 0.85, -10.008, 2, 1.433, -10.008, 2, 2.017, -10.008, 2, 2.417, -10.008, 2, 2.833, -10.008, 2, 3.233, -10.008, 2, 3.65, -10.008, 2, 4.05, -10.008, 2, 4.467, -10.008, 2, 4.867, -10.008, 2, 5.283, -10.008, 2, 5.6, -10.008, 2, 5.933, -10.008, 2, 6.583, -10.008, 2, 7.167, -10.008, 2, 7.567, -10.008, 2, 8.233, -10.008, 2, 8.817, -10.008, 2, 9.05, -10.008, 2, 9.633, -10.008, 2, 10.217, -10.008, 1, 10.222, -10.008, 10.228, -14.958, 10.233, -14.96, 1, 10.333, -14.988, 10.433, -15, 10.533, -15, 2, 10.65, -15, 0, 10.767, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamShoulderLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.548, 2, 0.25, -0.548, 0, 0.467, -1, 0, 0.85, -0.548, 0, 1.05, -1, 0, 1.433, -0.548, 0, 1.633, -1, 0, 2.017, -0.548, 0, 2.15, -1, 0, 2.417, -0.548, 0, 2.567, -1, 0, 2.833, -0.548, 0, 2.967, -1, 0, 3.233, -0.548, 0, 3.383, -1, 0, 3.65, -0.548, 0, 3.783, -1, 0, 4.05, -0.548, 0, 4.2, -1, 0, 4.467, -0.548, 0, 4.6, -1, 0, 4.867, -0.548, 0, 5.017, -1, 0, 5.283, -0.548, 0, 5.383, -1, 0, 5.6, -0.548, 0, 5.717, -1, 0, 5.933, -0.548, 0, 6.033, -1, 0, 6.25, -0.548, 0, 6.383, -1, 0, 6.65, -0.548, 0, 6.983, -1, 0, 7.25, -0.548, 0, 7.383, -1, 0, 7.65, -0.548, 0, 7.783, -1, 0, 8.05, -0.548, 0, 8.183, -1, 0, 8.45, -0.548, 0, 8.6, -1, 0, 8.867, -0.548, 0, 9, -1, 0, 9.267, -0.548, 0, 9.4, -1, 0, 9.667, -0.548, 0, 9.817, -1, 0, 10.083, -0.548, 0, 10.217, -1, 1, 10.306, -1, 10.394, -1.086, 10.483, -0.548, 1, 10.616, 0.258, 10.75, 2, 10.883, 2, 0, 11.633, -1, 2, 12.033, -1, 2, 12.467, -1, 0, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -30, 2, 0.25, -30, 2, 0.85, -30, 2, 1.433, -30, 2, 2.017, -30, 2, 2.417, -30, 2, 2.833, -30, 2, 3.233, -30, 2, 3.65, -30, 2, 4.05, -30, 2, 4.467, -30, 2, 4.867, -30, 2, 5.283, -30, 2, 5.6, -30, 2, 5.933, -30, 2, 6.25, -30, 2, 6.65, -30, 2, 7.25, -30, 2, 7.65, -30, 2, 8.05, -30, 2, 8.45, -30, 2, 8.867, -30, 2, 9.267, -30, 2, 9.667, -30, 2, 10.083, -30, 2, 10.483, -30, 2, 10.517, -30, 2, 12.033, -30, 2, 12.467, -30, 1, 12.472, -30, 12.478, 0, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -8.2, 2, 0.25, -8.2, 2, 0.85, -8.2, 2, 1.433, -8.2, 2, 2.017, -8.2, 2, 2.417, -8.2, 2, 2.833, -8.2, 2, 3.233, -8.2, 2, 3.65, -8.2, 2, 4.05, -8.2, 2, 4.467, -8.2, 2, 4.867, -8.2, 2, 5.283, -8.2, 2, 5.6, -8.2, 2, 5.933, -8.2, 2, 6.25, -8.2, 2, 6.65, -8.2, 2, 7.25, -8.2, 2, 7.65, -8.2, 2, 8.05, -8.2, 2, 8.45, -8.2, 2, 8.867, -8.2, 2, 9.267, -8.2, 2, 9.667, -8.2, 2, 10.083, -8.2, 2, 10.483, -8.2, 2, 10.8, -20.41, 2, 12.033, -20.41, 2, 12.467, -20.41, 1, 12.472, -20.41, 12.478, 0, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.087, 2, 0.25, 0.087, 2, 0.85, 0.087, 2, 1.433, 0.087, 2, 2.017, 0.087, 2, 2.417, 0.087, 2, 2.833, 0.087, 2, 3.233, 0.087, 2, 3.65, 0.087, 2, 4.05, 0.087, 2, 4.467, 0.087, 2, 4.867, 0.087, 2, 5.283, 0.087, 2, 5.6, 0.087, 2, 5.933, 0.087, 2, 6.25, 0.087, 2, 6.65, 0.087, 2, 7.25, 0.087, 2, 7.65, 0.087, 2, 8.05, 0.087, 2, 8.45, 0.087, 2, 8.867, 0.087, 2, 9.267, 0.087, 2, 9.667, 0.087, 2, 10.083, 0.087, 2, 10.483, 0.087, 0, 10.8, 0.06, 2, 12.033, 0.06, 2, 12.467, 0.06, 1, 12.472, 0.06, 12.478, 30, 12.483, 30, 2, 12.933, 30, 2, 13.25, 30]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 0.85, 1, 2, 1.433, 1, 2, 2.017, 1, 2, 2.417, 1, 2, 2.833, 1, 2, 3.233, 1, 2, 3.65, 1, 2, 4.05, 1, 2, 4.467, 1, 2, 4.867, 1, 2, 5.283, 1, 2, 5.6, 1, 2, 5.933, 1, 2, 6.25, 1, 2, 6.65, 1, 2, 7.25, 1, 2, 7.65, 1, 2, 8.05, 1, 2, 8.45, 1, 2, 8.867, 1, 2, 9.267, 1, 2, 9.667, 1, 2, 10.083, 1, 2, 10.483, 1, 2, 10.85, 1, 1, 10.872, 0.333, 10.895, -0.333, 10.917, -1, 2, 12.033, -1, 2, 12.467, -1, 2, 12.483, -1, 2, 12.933, -1, 2, 13.25, -1]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.582, 2, 0.25, -0.582, 0, 0.583, -0.377, 0, 0.85, -0.582, 0, 1.167, -0.377, 0, 1.433, -0.582, 0, 1.75, -0.377, 0, 2.017, -0.582, 0, 2.233, -0.377, 0, 2.417, -0.582, 0, 2.65, -0.377, 0, 2.833, -0.582, 0, 3.05, -0.377, 0, 3.233, -0.582, 0, 3.467, -0.377, 0, 3.65, -0.582, 0, 3.867, -0.377, 0, 4.05, -0.582, 0, 4.283, -0.377, 0, 4.467, -0.582, 0, 4.683, -0.377, 0, 4.867, -0.582, 0, 5.1, -0.377, 0, 5.283, -0.582, 0, 5.45, -0.377, 0, 5.6, -0.582, 0, 5.783, -0.377, 0, 5.933, -0.582, 0, 6.1, -0.377, 0, 6.25, -0.582, 0, 6.467, -0.377, 0, 6.65, -0.582, 0, 7.067, -0.377, 0, 7.25, -0.582, 0, 7.467, -0.377, 0, 7.65, -0.582, 0, 7.867, -0.377, 0, 8.05, -0.582, 0, 8.267, -0.377, 0, 8.45, -0.582, 0, 8.683, -0.377, 0, 8.867, -0.582, 0, 9.083, -0.377, 0, 9.267, -0.582, 0, 9.483, -0.377, 0, 9.667, -0.582, 0, 9.9, -0.377, 0, 10.083, -0.582, 0, 10.3, -0.377, 1, 10.361, -0.377, 10.422, -0.553, 10.483, -0.582, 1, 10.533, -0.606, 10.583, -0.6, 10.633, -0.6, 2, 12.033, -0.6, 2, 12.467, -0.6, 0, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamHandLDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 8, 2, 0.25, 8, 2, 0.85, 8, 2, 1.433, 8, 2, 2.017, 8, 2, 2.417, 8, 2, 2.833, 8, 2, 3.233, 8, 2, 3.65, 8, 2, 4.05, 8, 2, 4.467, 8, 2, 4.867, 8, 2, 5.283, 8, 2, 5.6, 8, 2, 5.933, 8, 2, 6.25, 8, 2, 6.65, 8, 2, 7.25, 8, 2, 7.65, 8, 2, 8.05, 8, 2, 8.45, 8, 2, 8.867, 8, 2, 9.267, 8, 2, 9.667, 8, 2, 10.083, 8, 2, 10.483, 8, 2, 11.4, 8, 0, 11.417, 10, 2, 12.033, 10, 2, 12.467, 10, 2, 12.483, 10, 2, 12.933, 10, 2, 13.25, 10]}, {"Target": "Parameter", "Id": "xiongjiya", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 12.467, 1, 0, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "xiongjiya5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 2, 0.25, 0.6, 1, 1.178, 0.6, 2.105, 0.591, 3.033, 0.5, 1, 3.766, 0.428, 4.5, 0.3, 5.233, 0.3, 2, 7.033, 0.3, 0, 8.25, 0.764, 0, 9.233, 0.5, 0, 9.917, 1, 0, 12.2, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "xiongjiya3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.85, 2, 0.25, 0.85, 2, 0.85, 0.85, 2, 1.433, 0.85, 2, 2.017, 0.85, 2, 2.417, 0.85, 2, 2.833, 0.85, 2, 3.233, 0.85, 2, 3.65, 0.85, 2, 4.05, 0.85, 2, 4.467, 0.85, 2, 4.867, 0.85, 2, 5.283, 0.85, 2, 5.6, 0.85, 2, 5.933, 0.85, 2, 6.25, 0.85, 2, 6.65, 0.85, 2, 7.25, 0.85, 2, 7.65, 0.85, 2, 8.05, 0.85, 2, 8.45, 0.85, 2, 8.867, 0.85, 2, 9.267, 0.85, 2, 9.667, 0.85, 2, 10.083, 0.85, 2, 10.483, 0.85, 0, 12.033, 0.663, 2, 12.467, 0.663, 0, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "xiongjiya4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.85, 2, 0.25, 0.85, 2, 0.85, 0.85, 2, 1.433, 0.85, 2, 2.017, 0.85, 2, 2.417, 0.85, 2, 2.833, 0.85, 2, 3.233, 0.85, 2, 3.65, 0.85, 2, 4.05, 0.85, 2, 4.467, 0.85, 2, 4.867, 0.85, 2, 5.283, 0.85, 2, 5.6, 0.85, 2, 5.933, 0.85, 2, 6.25, 0.85, 2, 6.65, 0.85, 2, 7.25, 0.85, 2, 7.65, 0.85, 2, 8.05, 0.85, 2, 8.45, 0.85, 2, 8.867, 0.85, 2, 9.267, 0.85, 2, 9.667, 0.85, 2, 10.083, 0.85, 2, 10.483, 0.85, 0, 12.033, 0.663, 2, 12.467, 0.663, 0, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamShoulderRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.548, 2, 0.25, -0.548, 0, 0.467, -1, 0, 0.85, -0.548, 0, 1.05, -1, 0, 1.433, -0.548, 0, 1.633, -1, 0, 2.017, -0.548, 0, 2.15, -1, 0, 2.417, -0.548, 0, 2.567, -1, 0, 2.833, -0.548, 0, 2.967, -1, 0, 3.233, -0.548, 0, 3.383, -1, 0, 3.65, -0.548, 0, 3.783, -1, 0, 4.05, -0.548, 0, 4.2, -1, 0, 4.467, -0.548, 0, 4.6, -1, 0, 4.867, -0.548, 0, 5.017, -1, 0, 5.283, -0.548, 0, 5.383, -1, 0, 5.6, -0.548, 0, 5.717, -1, 0, 5.933, -0.548, 0, 6.033, -1, 0, 6.25, -0.548, 0, 6.383, -1, 0, 6.65, -0.548, 0, 6.983, -1, 0, 7.25, -0.548, 0, 7.383, -1, 0, 7.65, -0.548, 0, 7.783, -1, 0, 8.05, -0.548, 0, 8.183, -1, 0, 8.45, -0.548, 0, 8.6, -1, 0, 8.867, -0.548, 0, 9, -1, 0, 9.267, -0.548, 0, 9.4, -1, 0, 9.667, -0.548, 0, 9.817, -1, 0, 10.083, -0.548, 0, 10.217, -1, 1, 10.306, -1, 10.394, -1.086, 10.483, -0.548, 1, 10.616, 0.258, 10.75, 2, 10.883, 2, 0, 11.633, -1, 2, 12.033, -1, 2, 12.467, -1, 0, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 30, 2, 0.25, 30, 2, 0.85, 30, 2, 1.433, 30, 2, 2.017, 30, 2, 2.417, 30, 2, 2.833, 30, 2, 3.233, 30, 2, 3.65, 30, 2, 4.05, 30, 2, 4.467, 30, 2, 4.867, 30, 2, 5.283, 30, 2, 5.6, 30, 2, 5.933, 30, 2, 6.25, 30, 2, 6.65, 30, 2, 7.25, 30, 2, 7.65, 30, 2, 8.05, 30, 2, 8.45, 30, 2, 8.867, 30, 2, 9.267, 30, 2, 9.667, 30, 2, 10.083, 30, 2, 10.483, 30, 2, 10.517, 30, 2, 12.033, 30, 2, 12.467, 30, 1, 12.472, 30, 12.478, 0, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -7.9, 2, 0.25, -7.9, 2, 0.85, -7.9, 2, 1.433, -7.9, 2, 2.017, -7.9, 2, 2.417, -7.9, 2, 2.833, -7.9, 2, 3.233, -7.9, 2, 3.65, -7.9, 2, 4.05, -7.9, 2, 4.467, -7.9, 2, 4.867, -7.9, 2, 5.283, -7.9, 2, 5.6, -7.9, 2, 5.933, -7.9, 2, 6.25, -7.9, 2, 6.65, -7.9, 2, 7.25, -7.9, 2, 7.65, -7.9, 2, 8.05, -7.9, 2, 8.45, -7.9, 2, 8.867, -7.9, 2, 9.267, -7.9, 2, 9.667, -7.9, 2, 10.083, -7.9, 2, 10.483, -7.9, 1, 10.589, -10.128, 10.694, -12.356, 10.8, -14.584, 2, 12.033, -14.584, 2, 12.467, -14.584, 0, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 0.85, 1, 2, 1.433, 1, 2, 2.017, 1, 2, 2.417, 1, 2, 2.833, 1, 2, 3.233, 1, 2, 3.65, 1, 2, 4.05, 1, 2, 4.467, 1, 2, 4.867, 1, 2, 5.283, 1, 2, 5.6, 1, 2, 5.933, 1, 2, 6.25, 1, 2, 6.65, 1, 2, 7.25, 1, 2, 7.65, 1, 2, 8.05, 1, 2, 8.45, 1, 2, 8.867, 1, 2, 9.267, 1, 2, 9.667, 1, 2, 10.083, 1, 2, 10.483, 1, 2, 12.033, 1, 2, 12.467, 1, 0, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.72, 2, 0.25, -0.72, 2, 0.85, -0.72, 2, 1.433, -0.72, 2, 2.017, -0.72, 2, 2.417, -0.72, 2, 2.833, -0.72, 2, 3.233, -0.72, 2, 3.65, -0.72, 2, 4.05, -0.72, 2, 4.467, -0.72, 2, 4.867, -0.72, 2, 5.283, -0.72, 2, 5.6, -0.72, 2, 5.933, -0.72, 2, 6.25, -0.72, 2, 6.65, -0.72, 2, 7.25, -0.72, 2, 7.65, -0.72, 2, 8.05, -0.72, 2, 8.45, -0.72, 2, 8.867, -0.72, 2, 9.267, -0.72, 2, 9.667, -0.72, 2, 10.083, -0.72, 2, 10.483, -0.72, 2, 11.417, -0.72, 0, 11.833, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.578, 2, 0.25, -0.578, 0, 0.583, -0.323, 0, 0.85, -0.578, 0, 1.167, -0.323, 0, 1.433, -0.578, 0, 1.75, -0.323, 0, 2.017, -0.578, 0, 2.233, -0.323, 0, 2.417, -0.578, 0, 2.65, -0.323, 0, 2.833, -0.578, 0, 3.05, -0.323, 0, 3.233, -0.578, 0, 3.467, -0.323, 0, 3.65, -0.578, 0, 3.867, -0.323, 0, 4.05, -0.578, 0, 4.283, -0.323, 0, 4.467, -0.578, 0, 4.683, -0.323, 0, 4.867, -0.578, 0, 5.1, -0.323, 0, 5.283, -0.578, 0, 5.45, -0.323, 0, 5.6, -0.578, 0, 5.783, -0.323, 0, 5.933, -0.578, 0, 6.1, -0.323, 0, 6.25, -0.578, 0, 6.467, -0.323, 0, 6.65, -0.578, 0, 7.067, -0.323, 0, 7.25, -0.578, 0, 7.467, -0.323, 0, 7.65, -0.578, 0, 7.867, -0.323, 0, 8.05, -0.578, 0, 8.267, -0.323, 0, 8.45, -0.578, 0, 8.683, -0.323, 0, 8.867, -0.578, 0, 9.083, -0.323, 0, 9.267, -0.578, 0, 9.483, -0.323, 0, 9.667, -0.578, 0, 9.9, -0.323, 0, 10.083, -0.578, 0, 10.3, -0.323, 1, 10.361, -0.323, 10.422, -0.543, 10.483, -0.578, 1, 10.533, -0.607, 10.583, -0.6, 10.633, -0.6, 0, 11.583, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamHandRDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 8, 2, 0.25, 8, 2, 0.85, 8, 2, 1.433, 8, 2, 2.017, 8, 2, 2.417, 8, 2, 2.833, 8, 2, 3.233, 8, 2, 3.65, 8, 2, 4.05, 8, 2, 4.467, 8, 2, 4.867, 8, 2, 5.283, 8, 2, 5.6, 8, 2, 5.933, 8, 2, 6.25, 8, 2, 6.65, 8, 2, 7.25, 8, 2, 7.65, 8, 2, 8.05, 8, 2, 8.45, 8, 2, 8.867, 8, 2, 9.267, 8, 2, 9.667, 8, 2, 10.083, 8, 2, 10.483, 8, 2, 11.4, 8, 2, 12.467, 8, 1, 12.472, 8, 12.478, 0, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 0.85, 1, 2, 1.433, 1, 2, 2.017, 1, 2, 2.417, 1, 2, 2.833, 1, 2, 3.233, 1, 2, 3.65, 1, 2, 4.05, 1, 2, 4.467, 1, 2, 4.867, 1, 2, 5.283, 1, 2, 5.6, 1, 2, 5.933, 1, 2, 6.25, 1, 2, 6.65, 1, 2, 7.25, 1, 2, 7.65, 1, 2, 8.05, 1, 2, 8.45, 1, 2, 8.867, 1, 2, 9.267, 1, 2, 9.667, 1, 2, 10.083, 1, 2, 10.483, 1, 2, 11.4, 1, 2, 12.467, 1, 0, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 0.85, 1, 2, 1.433, 1, 2, 2.017, 1, 2, 2.417, 1, 2, 2.833, 1, 2, 3.233, 1, 2, 3.65, 1, 2, 4.05, 1, 2, 4.467, 1, 2, 4.867, 1, 2, 5.283, 1, 2, 5.6, 1, 2, 5.933, 1, 2, 6.25, 1, 2, 6.65, 1, 2, 7.25, 1, 2, 7.65, 1, 2, 8.05, 1, 2, 8.45, 1, 2, 8.867, 1, 2, 9.267, 1, 2, 9.667, 1, 2, 10.083, 1, 2, 10.483, 1, 0, 10.633, 0, 2, 10.8, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 0.85, 1, 2, 1.433, 1, 2, 2.017, 1, 2, 2.417, 1, 2, 2.833, 1, 2, 3.233, 1, 2, 3.65, 1, 2, 4.05, 1, 2, 4.467, 1, 2, 4.867, 1, 2, 5.283, 1, 2, 5.6, 1, 2, 5.933, 1, 2, 6.25, 1, 2, 6.65, 1, 2, 7.25, 1, 2, 7.65, 1, 2, 8.05, 1, 2, 8.45, 1, 2, 8.867, 1, 2, 9.267, 1, 2, 9.667, 1, 2, 10.083, 1, 2, 10.483, 1, 2, 11.4, 1, 2, 12.467, 1, 0, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.1, 2, 0.25, -0.1, 2, 10.483, -0.1, 0, 10.7, -0.352, 2, 11.4, -0.352, 2, 12.033, -0.352, 2, 12.467, -0.352, 0, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.3, 2, 0.25, -0.3, 2, 10.483, -0.3, 0, 10.7, -0.298, 2, 11.4, -0.298, 2, 12.033, -0.298, 2, 12.467, -0.298, 0, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.615, 2, 0.25, 0.615, 2, 0.85, 0.615, 2, 1.433, 0.615, 2, 2.017, 0.615, 2, 2.417, 0.615, 2, 2.833, 0.615, 2, 3.233, 0.615, 2, 3.65, 0.615, 2, 4.05, 0.615, 2, 4.467, 0.615, 2, 4.867, 0.615, 2, 5.283, 0.615, 2, 5.6, 0.615, 2, 5.933, 0.615, 2, 6.25, 0.615, 2, 6.65, 0.615, 2, 7.25, 0.615, 2, 7.65, 0.615, 2, 8.05, 0.615, 2, 8.45, 0.615, 2, 8.867, 0.615, 2, 9.267, 0.615, 2, 9.667, 0.615, 2, 10.083, 0.615, 2, 10.483, 0.615, 0, 12.467, 0.644, 0, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.321, 2, 0.25, 0.321, 2, 0.85, 0.321, 2, 1.433, 0.321, 2, 2.017, 0.321, 2, 2.417, 0.321, 2, 2.833, 0.321, 2, 3.233, 0.321, 2, 3.65, 0.321, 2, 4.05, 0.321, 2, 4.467, 0.321, 2, 4.867, 0.321, 2, 5.283, 0.321, 2, 5.6, 0.321, 2, 5.933, 0.321, 2, 6.25, 0.321, 2, 6.65, 0.321, 2, 7.25, 0.321, 2, 7.65, 0.321, 2, 8.05, 0.321, 2, 8.45, 0.321, 2, 8.867, 0.321, 2, 9.267, 0.321, 2, 9.667, 0.321, 2, 10.083, 0.321, 2, 10.483, 0.321, 0, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.479, 2, 0.25, -0.479, 0, 0.533, 0.8, 0, 0.85, -0.479, 0, 1.117, 0.8, 0, 1.433, -0.479, 0, 1.7, 0.8, 0, 2.017, -0.479, 0, 2.2, 0.8, 0, 2.417, -0.479, 0, 2.617, 0.8, 0, 2.833, -0.479, 0, 3.017, 0.8, 0, 3.233, -0.479, 0, 3.433, 0.8, 0, 3.65, -0.479, 0, 3.833, 0.8, 0, 4.05, -0.479, 0, 4.25, 0.8, 0, 4.467, -0.479, 0, 4.65, 0.8, 0, 4.867, -0.479, 0, 5.067, 0.8, 0, 5.283, -0.479, 0, 5.417, 0.8, 0, 5.6, -0.479, 0, 5.75, 0.8, 0, 5.933, -0.479, 0, 6.083, 0.8, 0, 6.25, -0.479, 0, 6.433, 0.8, 0, 6.65, -0.479, 0, 7.033, 0.8, 0, 7.25, -0.479, 0, 7.433, 0.8, 0, 7.65, -0.479, 0, 7.833, 0.8, 0, 8.05, -0.479, 0, 8.233, 0.8, 0, 8.45, -0.479, 0, 8.65, 0.8, 0, 8.867, -0.479, 0, 9.05, 0.8, 0, 9.267, -0.479, 0, 9.45, 0.8, 0, 9.667, -0.479, 0, 9.867, 0.8, 0, 10.083, -0.479, 0, 10.267, 0.8, 0, 10.483, -0.479, 0, 12.033, 1, 2, 12.467, 1, 0, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 0.85, 1, 2, 1.433, 1, 2, 2.017, 1, 2, 2.417, 1, 2, 2.833, 1, 2, 3.233, 1, 2, 3.65, 1, 2, 4.05, 1, 2, 4.467, 1, 2, 4.867, 1, 2, 5.283, 1, 2, 5.6, 1, 2, 5.933, 1, 2, 6.25, 1, 2, 6.65, 1, 2, 7.25, 1, 2, 7.65, 1, 2, 8.05, 1, 2, 8.45, 1, 2, 8.867, 1, 2, 9.267, 1, 2, 9.667, 1, 2, 10.083, 1, 2, 10.483, 1, 2, 12.033, 1, 2, 12.467, 1, 0, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.85, 0, 2, 1.433, 0, 2, 2.017, 0, 2, 2.417, 0, 2, 2.833, 0, 2, 3.233, 0, 2, 3.65, 0, 2, 4.05, 0, 2, 4.467, 0, 2, 4.867, 0, 2, 5.283, 0, 2, 5.6, 0, 2, 5.933, 0, 2, 6.25, 0, 2, 6.65, 0, 2, 7.25, 0, 2, 7.65, 0, 2, 8.05, 0, 2, 8.45, 0, 2, 8.867, 0, 2, 9.267, 0, 2, 9.667, 0, 2, 10.083, 0, 2, 10.483, 0, 1, 10.5, 0, 10.516, 0.483, 10.533, 0.502, 1, 10.633, 0.601, 10.733, 0.701, 10.833, 0.8, 1, 10.883, 0.867, 10.933, 0.933, 10.983, 1, 1, 11.428, 1.333, 11.872, 1.667, 12.317, 2, 2, 12.467, 2, 0, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Param103", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -17.662, 2, 0.25, -17.662, 2, 0.85, -17.662, 2, 1.433, -17.662, 2, 2.017, -17.662, 2, 2.417, -17.662, 2, 2.833, -17.662, 2, 3.233, -17.662, 2, 3.65, -17.662, 2, 4.05, -17.662, 2, 4.467, -17.662, 2, 4.867, -17.662, 2, 5.283, -17.662, 2, 5.6, -17.662, 2, 5.933, -17.662, 2, 6.25, -17.662, 2, 6.65, -17.662, 2, 7.25, -17.662, 2, 7.65, -17.662, 2, 8.05, -17.662, 2, 8.45, -17.662, 2, 8.867, -17.662, 2, 9.267, -17.662, 2, 9.667, -17.662, 2, 10.083, -17.662, 2, 10.483, -17.662, 0, 10.533, 27, 1, 10.6, 27, 10.666, 15.037, 10.733, 3.19, 1, 10.872, -21.491, 11.011, -30, 11.15, -30, 2, 11.783, -30, 2, 12.033, -30, 2, 12.467, -30, 1, 12.472, -30, 12.478, 0, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "taiqiugundong", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -22.5, 2, 0.25, -22.5, 2, 11.05, -22.5, 1, 11.206, -5, 11.361, 12.5, 11.517, 30, 1, 11.656, 26.466, 11.794, 22.931, 11.933, 19.397, 2, 12.467, 19.397, 0, 12.933, -22.5, 2, 13.25, -22.5]}, {"Target": "Parameter", "Id": "Change_idle_physics32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 11.05, 0, 1, 11.211, 0.2, 11.372, 0.4, 11.533, 0.6, 1, 11.711, 0.733, 11.889, 0.867, 12.067, 1, 2, 12.467, 1, 0, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.85, 0, 2, 1.433, 0, 2, 2.017, 0, 2, 2.417, 0, 2, 2.833, 0, 2, 3.233, 0, 2, 3.65, 0, 2, 4.05, 0, 2, 4.467, 0, 2, 4.867, 0, 2, 5.283, 0, 2, 5.6, 0, 2, 5.933, 0, 2, 6.25, 0, 2, 6.65, 0, 2, 7.25, 0, 2, 7.65, 0, 2, 8.05, 0, 2, 8.45, 0, 2, 8.867, 0, 2, 9.267, 0, 2, 9.667, 0, 2, 10.083, 0, 2, 10.483, 0, 2, 10.517, 0, 1, 10.522, 0, 10.528, 0, 10.533, 0.007, 1, 10.75, 0.628, 10.966, 1, 11.183, 1, 2, 12.017, 1, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.85, 0, 2, 1.433, 0, 2, 2.017, 0, 2, 2.417, 0, 2, 2.833, 0, 2, 3.233, 0, 2, 3.65, 0, 2, 4.05, 0, 2, 4.467, 0, 2, 4.867, 0, 2, 5.283, 0, 2, 5.6, 0, 2, 5.933, 0, 2, 6.25, 0, 2, 6.65, 0, 2, 7.25, 0, 2, 7.65, 0, 2, 8.05, 0, 2, 8.45, 0, 2, 8.867, 0, 2, 9.267, 0, 2, 9.667, 0, 2, 10.083, 0, 2, 10.483, 0, 2, 10.883, 0, 1, 10.905, 0, 10.928, -0.015, 10.95, 0.062, 1, 10.972, 0.139, 10.995, 1, 11.017, 1, 1, 11.045, 1, 11.072, 0.62, 11.1, 0.062, 1, 11.128, -0.496, 11.155, -0.695, 11.183, -0.695, 1, 11.211, -0.695, 11.239, -0.404, 11.267, 0.039, 1, 11.295, 0.482, 11.322, 0.647, 11.35, 0.647, 1, 11.378, 0.647, 11.405, 0.201, 11.433, 0.039, 1, 11.461, -0.123, 11.489, -0.113, 11.517, -0.113, 0, 11.6, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.85, 0, 2, 1.433, 0, 2, 2.017, 0, 2, 2.417, 0, 2, 2.833, 0, 2, 3.233, 0, 2, 3.65, 0, 2, 4.05, 0, 2, 4.467, 0, 2, 4.867, 0, 2, 5.283, 0, 2, 5.6, 0, 2, 5.933, 0, 2, 6.25, 0, 2, 6.65, 0, 2, 7.25, 0, 2, 7.65, 0, 2, 8.05, 0, 2, 8.45, 0, 2, 8.867, 0, 2, 9.267, 0, 2, 9.667, 0, 2, 10.083, 0, 2, 10.483, 0, 2, 10.883, 0, 0, 10.95, -1, 1, 10.972, -1, 10.995, -0.512, 11.017, 0.062, 1, 11.045, 0.78, 11.072, 1, 11.1, 1, 1, 11.128, 1, 11.155, 0.594, 11.183, 0.039, 1, 11.211, -0.516, 11.239, -0.695, 11.267, -0.695, 1, 11.295, -0.695, 11.322, -0.404, 11.35, 0.039, 1, 11.378, 0.482, 11.405, 0.647, 11.433, 0.647, 1, 11.461, 0.647, 11.489, 0.439, 11.517, 0.238, 1, 11.545, 0.037, 11.572, 0, 11.6, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -6.66, 2, 0.25, -6.66, 0, 0.533, 4.883, 0, 0.85, -6.66, 0, 1.117, 4.883, 0, 1.433, -6.66, 0, 1.7, 4.883, 0, 2.017, -6.66, 0, 2.2, 4.883, 0, 2.417, -6.66, 0, 2.617, 4.883, 0, 2.833, -6.66, 0, 3.017, 4.883, 0, 3.233, -6.66, 0, 3.433, 4.883, 0, 3.65, -6.66, 0, 3.833, 4.883, 0, 4.05, -6.66, 0, 4.25, 4.883, 0, 4.467, -6.66, 0, 4.65, 4.883, 0, 4.867, -6.66, 0, 5.067, 4.883, 0, 5.283, -6.66, 0, 5.417, 4.883, 0, 5.6, -6.66, 0, 5.75, 4.883, 0, 5.933, -6.66, 0, 6.083, 4.883, 0, 6.25, -6.66, 0, 6.433, 4.883, 0, 6.65, -6.66, 0, 7.033, 4.883, 0, 7.25, -6.66, 0, 7.433, 4.883, 0, 7.65, -6.66, 0, 7.833, 4.883, 0, 8.05, -6.66, 0, 8.233, 4.883, 0, 8.45, -6.66, 0, 8.65, 4.883, 0, 8.867, -6.66, 0, 9.05, 4.883, 0, 9.267, -6.66, 0, 9.45, 4.883, 0, 9.667, -6.66, 0, 9.867, 4.883, 0, 10.083, -6.66, 0, 10.267, 4.883, 0, 10.483, -6.66, 0, 12.033, 30, 2, 12.467, 30, 1, 12.472, 30, 12.478, 0, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.009, 2, 0.25, -2.009, 0, 0.533, 2, 0, 0.85, -2.009, 0, 1.117, 2, 0, 1.433, -2.009, 0, 1.7, 2, 0, 2.017, -2.009, 0, 2.2, 2, 0, 2.417, -2.009, 0, 2.617, 2, 0, 2.833, -2.009, 0, 3.017, 2, 0, 3.233, -2.009, 0, 3.433, 2, 0, 3.65, -2.009, 0, 3.833, 2, 0, 4.05, -2.009, 0, 4.25, 2, 0, 4.467, -2.009, 0, 4.65, 2, 0, 4.867, -2.009, 0, 5.067, 2, 0, 5.283, -2.009, 0, 5.417, 2, 0, 5.6, -2.009, 0, 5.75, 2, 0, 5.933, -2.009, 0, 6.083, 2, 0, 6.25, -2.009, 0, 6.433, 2, 0, 6.65, -2.009, 0, 7.033, 2, 0, 7.25, -2.009, 0, 7.433, 2, 0, 7.65, -2.009, 0, 7.833, 2, 0, 8.05, -2.009, 0, 8.233, 2, 0, 8.45, -2.009, 0, 8.65, 2, 0, 8.867, -2.009, 0, 9.05, 2, 0, 9.267, -2.009, 0, 9.45, 2, 0, 9.667, -2.009, 0, 9.867, 2, 0, 10.083, -2.009, 0, 10.267, 2, 0, 10.483, -2.009, 0, 10.533, 4.768, 0, 10.85, 0, 2, 12.033, 0, 2, 12.467, 0, 1, 12.472, 0, 12.478, -7.795, 12.483, -7.795, 2, 12.933, -7.795, 2, 13.25, -7.795]}, {"Target": "Parameter", "Id": "ParamBodyStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.136, 2, 0.25, -1.136, 0, 0.467, 5.027, 0, 0.75, -7.331, 0, 1.05, 5.027, 0, 1.333, -7.331, 0, 1.633, 5.027, 0, 1.917, -7.331, 0, 2.15, 5.027, 0, 2.35, -7.331, 0, 2.567, 5.027, 0, 2.767, -7.331, 0, 2.967, 5.027, 0, 3.167, -7.331, 0, 3.383, 5.027, 0, 3.583, -7.331, 0, 3.783, 5.027, 0, 3.983, -7.331, 0, 4.2, 5.027, 0, 4.4, -7.331, 0, 4.6, 5.027, 0, 4.8, -7.331, 0, 5.017, 5.027, 0, 5.217, -7.331, 0, 5.383, 5.027, 0, 5.55, -7.331, 0, 5.717, 5.027, 0, 5.883, -7.331, 0, 6.033, 5.027, 0, 6.2, -7.331, 1, 6.217, -7.331, 6.233, -2.959, 6.25, -1.136, 1, 6.294, 3.725, 6.339, 5.027, 6.383, 5.027, 0, 6.583, -7.331, 0, 6.983, 5.027, 0, 7.183, -7.331, 1, 7.205, -7.331, 7.228, -3.571, 7.25, -1.136, 1, 7.294, 3.733, 7.339, 5.027, 7.383, 5.027, 0, 7.583, -7.331, 1, 7.605, -7.331, 7.628, -3.571, 7.65, -1.136, 1, 7.694, 3.733, 7.739, 5.027, 7.783, 5.027, 0, 7.983, -7.331, 0, 8.183, 5.027, 0, 8.383, -7.331, 0, 8.6, 5.027, 0, 8.8, -7.331, 0, 9, 5.027, 0, 9.2, -7.331, 0, 9.4, 5.027, 0, 9.6, -7.331, 0, 9.817, 5.027, 0, 10.017, -7.331, 0, 10.217, 5.027, 0, 10.417, -7.331, 0, 10.617, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.187, 2, 0.25, 0.187, 2, 0.85, 0.187, 2, 1.433, 0.187, 2, 2.017, 0.187, 2, 2.417, 0.187, 2, 2.833, 0.187, 2, 3.233, 0.187, 2, 3.65, 0.187, 2, 4.05, 0.187, 2, 4.467, 0.187, 2, 4.867, 0.187, 2, 5.283, 0.187, 2, 5.6, 0.187, 2, 5.933, 0.187, 2, 6.25, 0.187, 2, 6.65, 0.187, 2, 7.25, 0.187, 2, 7.65, 0.187, 2, 8.05, 0.187, 2, 8.65, 0.187, 2, 9.05, 0.187, 2, 9.45, 0.187, 2, 10.05, 0.187, 1, 10.111, 0.187, 10.172, 0.791, 10.233, 0.806, 1, 10.828, 0.948, 11.422, 1, 12.017, 1, 2, 12.033, 1, 2, 12.467, 1, 0, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Physics_AngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.836, 2, 0.25, -3.836, 0, 0.583, 3.503, 0, 0.9, -4.455, 0, 1.167, 3.38, 0, 1.483, -4.436, 0, 1.75, 3.378, 0, 2.05, -4.341, 0, 2.267, 2.685, 0, 2.467, -3.68, 0, 2.667, 2.646, 0, 2.883, -3.672, 0, 3.083, 2.508, 0, 3.283, -3.705, 0, 3.483, 2.654, 0, 3.7, -3.672, 0, 3.9, 2.508, 0, 4.1, -3.705, 0, 4.3, 2.654, 0, 4.517, -3.672, 0, 4.717, 2.508, 0, 4.917, -3.705, 0, 5.117, 2.654, 0, 5.317, -3.465, 0, 5.483, 1.827, 0, 5.65, -2.937, 0, 5.8, 1.878, 0, 5.983, -3.237, 0, 6.133, 1.939, 0, 6.3, -3.163, 0, 6.5, 2.466, 0, 6.733, -4.284, 0, 7.067, 3.442, 0, 7.3, -3.957, 0, 7.5, 2.49, 0, 7.7, -3.629, 0, 7.9, 2.515, 0, 8.1, -3.644, 0, 8.3, 2.514, 0, 8.5, -3.706, 0, 8.7, 2.654, 0, 8.917, -3.672, 0, 9.117, 2.508, 0, 9.317, -3.642, 0, 9.517, 2.514, 0, 9.717, -3.706, 0, 9.917, 2.654, 0, 10.133, -3.672, 0, 10.333, 2.508, 0, 10.6, -4.495, 0, 12.05, 17.602, 0, 12.433, 17.262, 0, 12.467, 17.263, 0, 12.683, -2.844, 0, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 7.635, 2, 0.25, 7.635, 0, 0.533, -8.456, 0, 0.8, 10.896, 0, 1.083, -15.751, 0, 1.4, 16.761, 0, 1.683, -16.742, 0, 2, 15.438, 0, 2.2, -11.461, 0, 2.417, 6.869, 0, 2.6, -10.297, 0, 2.817, 12.967, 0, 3.017, -10.089, 0, 3.233, 8.553, 0, 3.417, -11.368, 0, 3.65, 11.362, 0, 3.833, -9.752, 0, 4.033, 8.729, 0, 4.233, -11.578, 0, 4.467, 11.584, 0, 4.65, -9.671, 0, 4.85, 9.055, 0, 5.05, -11.24, 0, 5.283, 11.539, 0, 5.417, -8.022, 0, 5.6, 8.181, 0, 5.75, -11.548, 0, 5.933, 9.238, 0, 6.083, -8.815, 0, 6.25, 9.443, 0, 6.433, -9.669, 0, 6.633, 11.007, 0, 6.95, -12.71, 0, 7.233, 17.87, 0, 7.45, -10.326, 0, 7.65, 5.236, 0, 7.817, -11.385, 0, 8.05, 12.457, 0, 8.233, -10.415, 0, 8.45, 8.178, 0, 8.633, -10.76, 0, 8.867, 12.376, 0, 9.05, -10.172, 0, 9.267, 8.277, 0, 9.45, -10.84, 0, 9.667, 11.341, 0, 9.867, -10.364, 0, 10.083, 10.618, 0, 10.267, -10.155, 0, 10.467, 9.687, 0, 10.867, -8.682, 0, 11.3, 3.926, 0, 11.683, -2.765, 0, 12.1, 3.229, 0, 12.467, -1.883, 0, 12.483, 24.155, 0, 12.867, -15.753, 0, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -7.635, 2, 0.25, -7.635, 0, 0.517, 8.409, 0, 0.733, -20.262, 0, 1, 30, 0, 1.233, -30, 2, 1.35, -30, 0, 1.517, 30, 2, 1.667, 30, 1, 1.717, 30, 1.767, -30, 1.817, -30, 2, 1.95, -30, 0, 2.117, 30, 2, 2.217, 30, 1, 2.278, 30, 2.339, -22.255, 2.4, -22.255, 0, 2.583, 11.69, 0, 2.783, -20.405, 0, 3, 24.877, 0, 3.2, -17.821, 0, 3.4, 17.889, 0, 3.6, -21.679, 0, 3.817, 21.823, 0, 4.017, -14.801, 0, 4.233, 21.501, 0, 4.45, -23.832, 0, 4.65, 12.906, 0, 4.85, -14.115, 0, 5.05, 20.15, 0, 5.267, -23.738, 0, 5.417, 6.316, 0, 5.567, -10.988, 0, 5.75, 20.41, 0, 5.933, -14.041, 0, 6.083, 9.486, 0, 6.233, -15.293, 0, 6.417, 13.509, 0, 6.633, -21.918, 0, 6.883, 24.451, 0, 7.117, -30, 2, 7.217, -30, 1, 7.267, -30, 7.317, 30, 7.367, 30, 2, 7.467, 30, 0, 7.633, -15.831, 0, 7.8, 8.246, 0, 8, -20.045, 0, 8.217, 26.441, 0, 8.433, -17.61, 0, 8.617, 15.467, 0, 8.817, -22.564, 0, 9.033, 24.893, 0, 9.233, -16.32, 0, 9.417, 15.553, 0, 9.617, -20.356, 0, 9.833, 22.398, 0, 10.05, -21.631, 0, 10.25, 20.055, 0, 10.45, -17.153, 0, 10.717, 20.724, 0, 11.083, -21.471, 0, 11.483, 5.607, 0, 11.883, -8.702, 0, 12.317, 6.506, 0, 12.483, -22.258, 0, 12.65, 30, 2, 12.817, 30, 0, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_L", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.554, 2, 0.25, -1.554, 0, 0.533, 1.139, 0, 0.85, -1.554, 0, 1.117, 1.139, 0, 1.433, -1.554, 0, 1.7, 1.139, 0, 2.017, -1.554, 0, 2.2, 1.139, 0, 2.417, -1.554, 0, 2.617, 1.139, 0, 2.833, -1.554, 0, 3.017, 1.139, 0, 3.233, -1.554, 0, 3.433, 1.139, 0, 3.65, -1.554, 0, 3.833, 1.139, 0, 4.05, -1.554, 0, 4.25, 1.139, 0, 4.467, -1.554, 0, 4.65, 1.139, 0, 4.867, -1.554, 0, 5.067, 1.139, 0, 5.283, -1.554, 0, 5.417, 1.139, 0, 5.6, -1.554, 0, 5.75, 1.139, 0, 5.933, -1.554, 0, 6.083, 1.139, 0, 6.25, -1.554, 0, 6.433, 1.139, 0, 6.65, -1.554, 0, 7.033, 1.139, 0, 7.25, -1.554, 0, 7.433, 1.139, 0, 7.65, -1.554, 0, 7.833, 1.139, 0, 8.05, -1.554, 0, 8.233, 1.139, 0, 8.45, -1.554, 0, 8.65, 1.139, 0, 8.867, -1.554, 0, 9.05, 1.139, 0, 9.267, -1.554, 0, 9.45, 1.139, 0, 9.667, -1.554, 0, 9.867, 1.139, 0, 10.083, -1.554, 0, 10.267, 1.139, 0, 10.483, -1.554, 0, 11.35, 3.479, 0, 11.633, -5.72, 0, 12.083, 2.218, 0, 12.483, -7.712, 0, 12.75, 8.389, 0, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_R", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.554, 2, 0.25, 1.554, 0, 0.533, -1.139, 0, 0.85, 1.554, 0, 1.117, -1.139, 0, 1.433, 1.554, 0, 1.7, -1.139, 0, 2.017, 1.554, 0, 2.2, -1.139, 0, 2.417, 1.554, 0, 2.617, -1.139, 0, 2.833, 1.554, 0, 3.017, -1.139, 0, 3.233, 1.554, 0, 3.433, -1.139, 0, 3.65, 1.554, 0, 3.833, -1.139, 0, 4.05, 1.554, 0, 4.25, -1.139, 0, 4.467, 1.554, 0, 4.65, -1.139, 0, 4.867, 1.554, 0, 5.067, -1.139, 0, 5.283, 1.554, 0, 5.417, -1.139, 0, 5.6, 1.554, 0, 5.75, -1.139, 0, 5.933, 1.554, 0, 6.083, -1.139, 0, 6.25, 1.554, 0, 6.433, -1.139, 0, 6.65, 1.554, 0, 7.033, -1.139, 0, 7.25, 1.554, 0, 7.433, -1.139, 0, 7.65, 1.554, 0, 7.833, -1.139, 0, 8.05, 1.554, 0, 8.233, -1.139, 0, 8.45, 1.554, 0, 8.65, -1.139, 0, 8.867, 1.554, 0, 9.05, -1.139, 0, 9.267, 1.554, 0, 9.45, -1.139, 0, 9.667, 1.554, 0, 9.867, -1.139, 0, 10.083, 1.554, 0, 10.267, -1.139, 0, 10.483, 1.554, 0, 11.35, -3.479, 0, 11.633, 5.72, 0, 12.083, -2.218, 0, 12.483, 7.712, 0, 12.75, -8.389, 0, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 8.881, 2, 0.25, 8.881, 0, 0.533, -6.511, 0, 0.85, 8.881, 0, 1.117, -6.511, 0, 1.433, 8.881, 0, 1.7, -6.511, 0, 2.017, 8.881, 0, 2.2, -6.511, 0, 2.417, 8.881, 0, 2.617, -6.511, 0, 2.833, 8.881, 0, 3.017, -6.511, 0, 3.233, 8.881, 0, 3.433, -6.511, 0, 3.65, 8.881, 0, 3.833, -6.511, 0, 4.05, 8.881, 0, 4.25, -6.511, 0, 4.467, 8.881, 0, 4.65, -6.511, 0, 4.867, 8.881, 0, 5.067, -6.511, 0, 5.283, 8.881, 0, 5.417, -6.511, 0, 5.6, 8.881, 0, 5.75, -6.511, 0, 5.933, 8.881, 0, 6.083, -6.511, 0, 6.25, 8.881, 0, 6.433, -6.511, 0, 6.65, 8.881, 0, 7.033, -6.511, 0, 7.25, 8.881, 0, 7.433, -6.511, 0, 7.65, 8.881, 0, 7.833, -6.511, 0, 8.05, 8.881, 0, 8.233, -6.511, 0, 8.45, 8.881, 0, 8.65, -6.511, 0, 8.867, 8.881, 0, 9.05, -6.511, 0, 9.267, 8.881, 0, 9.45, -6.511, 0, 9.667, 8.881, 0, 9.867, -6.511, 0, 10.083, 8.881, 0, 10.267, -6.511, 0, 10.483, 8.881, 0, 11.317, -17.125, 0, 11.7, 1.213, 0, 11.95, -0.826, 0, 12.217, 0.436, 0, 12.467, -0.033, 1, 12.472, -0.033, 12.478, 30, 12.483, 30, 2, 12.533, 30, 0, 12.817, -9.809, 0, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -8.881, 2, 0.25, -8.881, 0, 0.533, 6.511, 0, 0.85, -8.881, 0, 1.117, 6.511, 0, 1.433, -8.881, 0, 1.7, 6.511, 0, 2.017, -8.881, 0, 2.2, 6.511, 0, 2.417, -8.881, 0, 2.617, 6.511, 0, 2.833, -8.881, 0, 3.017, 6.511, 0, 3.233, -8.881, 0, 3.433, 6.511, 0, 3.65, -8.881, 0, 3.833, 6.511, 0, 4.05, -8.881, 0, 4.25, 6.511, 0, 4.467, -8.881, 0, 4.65, 6.511, 0, 4.867, -8.881, 0, 5.067, 6.511, 0, 5.283, -8.881, 0, 5.417, 6.511, 0, 5.6, -8.881, 0, 5.75, 6.511, 0, 5.933, -8.881, 0, 6.083, 6.511, 0, 6.25, -8.881, 0, 6.433, 6.511, 0, 6.65, -8.881, 0, 7.033, 6.511, 0, 7.25, -8.881, 0, 7.433, 6.511, 0, 7.65, -8.881, 0, 7.833, 6.511, 0, 8.05, -8.881, 0, 8.233, 6.511, 0, 8.45, -8.881, 0, 8.65, 6.511, 0, 8.867, -8.881, 0, 9.05, 6.511, 0, 9.267, -8.881, 0, 9.45, 6.511, 0, 9.667, -8.881, 0, 9.867, 6.511, 0, 10.083, -8.881, 0, 10.267, 6.511, 0, 10.483, -8.881, 0, 11.283, 16.745, 0, 11.483, -14.863, 0, 11.75, 6.291, 0, 12.017, -2.462, 0, 12.283, 1.017, 0, 12.483, -30, 2, 12.5, -30, 0, 12.683, 24.546, 0, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -8.881, 2, 0.25, -8.881, 0, 0.533, 6.511, 0, 0.85, -8.881, 0, 1.117, 6.511, 0, 1.433, -8.881, 0, 1.7, 6.511, 0, 2.017, -8.881, 0, 2.2, 6.511, 0, 2.417, -8.881, 0, 2.617, 6.511, 0, 2.833, -8.881, 0, 3.017, 6.511, 0, 3.233, -8.881, 0, 3.433, 6.511, 0, 3.65, -8.881, 0, 3.833, 6.511, 0, 4.05, -8.881, 0, 4.25, 6.511, 0, 4.467, -8.881, 0, 4.65, 6.511, 0, 4.867, -8.881, 0, 5.067, 6.511, 0, 5.283, -8.881, 0, 5.417, 6.511, 0, 5.6, -8.881, 0, 5.75, 6.511, 0, 5.933, -8.881, 0, 6.083, 6.511, 0, 6.25, -8.881, 0, 6.433, 6.511, 0, 6.65, -8.881, 0, 7.033, 6.511, 0, 7.25, -8.881, 0, 7.433, 6.511, 0, 7.65, -8.881, 0, 7.833, 6.511, 0, 8.05, -8.881, 0, 8.233, 6.511, 0, 8.45, -8.881, 0, 8.65, 6.511, 0, 8.867, -8.881, 0, 9.05, 6.511, 0, 9.267, -8.881, 0, 9.45, 6.511, 0, 9.667, -8.881, 0, 9.867, 6.511, 0, 10.083, -8.881, 0, 10.267, 6.511, 0, 10.483, -8.881, 0, 11.483, 25.519, 0, 12.033, -3.418, 0, 12.467, 0.636, 0, 12.483, -30, 2, 12.55, -30, 0, 12.917, 7.43, 0, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 8.881, 2, 0.25, 8.881, 0, 0.533, -6.511, 0, 0.85, 8.881, 0, 1.117, -6.511, 0, 1.433, 8.881, 0, 1.7, -6.511, 0, 2.017, 8.881, 0, 2.2, -6.511, 0, 2.417, 8.881, 0, 2.617, -6.511, 0, 2.833, 8.881, 0, 3.017, -6.511, 0, 3.233, 8.881, 0, 3.433, -6.511, 0, 3.65, 8.881, 0, 3.833, -6.511, 0, 4.05, 8.881, 0, 4.25, -6.511, 0, 4.467, 8.881, 0, 4.65, -6.511, 0, 4.867, 8.881, 0, 5.067, -6.511, 0, 5.283, 8.881, 0, 5.417, -6.511, 0, 5.6, 8.881, 0, 5.75, -6.511, 0, 5.933, 8.881, 0, 6.083, -6.511, 0, 6.25, 8.881, 0, 6.433, -6.511, 0, 6.65, 8.881, 0, 7.033, -6.511, 0, 7.25, 8.881, 0, 7.433, -6.511, 0, 7.65, 8.881, 0, 7.833, -6.511, 0, 8.05, 8.881, 0, 8.233, -6.511, 0, 8.45, 8.881, 0, 8.65, -6.511, 0, 8.867, 8.881, 0, 9.05, -6.511, 0, 9.267, 8.881, 0, 9.45, -6.511, 0, 9.667, 8.881, 0, 9.867, -6.511, 0, 10.083, 8.881, 0, 10.267, -6.511, 0, 10.483, 8.881, 0, 11.467, -25.192, 0, 11.717, 22.324, 0, 12.033, -7.334, 0, 12.4, 1.448, 0, 12.467, 1.186, 0, 12.483, 30, 2, 12.517, 30, 0, 12.733, -23.548, 0, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Jewelry_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.12, 2, 0.25, 0.12, 0, 0.483, -0.771, 0, 0.783, 1.337, 0, 1.1, -1.579, 0, 1.417, 1.395, 0, 1.7, -1.184, 0, 2, 1.022, 0, 2.2, -0.911, 0, 2.417, 0.666, 0, 2.6, -0.796, 0, 2.817, 0.932, 0, 3.017, -0.824, 0, 3.217, 0.708, 0, 3.417, -0.868, 0, 3.65, 0.88, 0, 3.833, -0.793, 0, 4.033, 0.755, 0, 4.25, -0.865, 0, 4.467, 0.871, 0, 4.65, -0.784, 0, 4.85, 0.763, 0, 5.067, -0.87, 0, 5.283, 0.867, 0, 5.417, -0.664, 0, 5.6, 0.663, 0, 5.75, -0.856, 0, 5.933, 0.729, 0, 6.083, -0.733, 0, 6.25, 0.741, 0, 6.433, -0.798, 0, 6.633, 0.847, 0, 6.95, -0.979, 0, 7.233, 1.398, 0, 7.45, -0.817, 0, 7.65, 0.425, 0, 7.817, -0.896, 0, 8.05, 1.042, 0, 8.233, -0.755, 0, 8.433, 0.655, 0, 8.633, -0.897, 0, 8.867, 0.935, 0, 9.05, -0.759, 0, 9.267, 0.741, 0, 9.45, -0.829, 0, 9.667, 0.841, 0, 9.867, -0.84, 0, 10.083, 0.794, 0, 10.267, -0.825, 0, 10.467, 0.784, 0, 10.867, -0.787, 0, 11.3, 0.313, 0, 11.683, -0.23, 0, 12.1, 0.321, 0, 12.467, -0.188, 0, 12.483, 2.992, 0, 12.867, -1.933, 0, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Jewelry_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.028, 2, 0.25, 0.028, 0, 0.4, 0.557, 0, 0.667, -2.186, 0, 0.983, 3.446, 0, 1.283, -3.731, 0, 1.6, 3.492, 0, 1.9, -2.922, 0, 2.167, 2.73, 0, 2.4, -1.763, 0, 2.583, 1.186, 0, 2.783, -1.509, 0, 3, 1.798, 0, 3.2, -1.424, 0, 3.4, 1.432, 0, 3.6, -1.655, 0, 3.817, 1.703, 0, 4.017, -1.392, 0, 4.217, 1.482, 0, 4.433, -1.673, 0, 4.633, 1.634, 0, 4.833, -1.363, 0, 5.033, 1.503, 0, 5.25, -1.687, 0, 5.417, 1.401, 0, 5.583, -0.732, 0, 5.733, 1.121, 0, 5.9, -1.35, 0, 6.067, 1.19, 0, 6.233, -1.013, 0, 6.4, 1.201, 0, 6.6, -1.486, 0, 6.85, 1.794, 0, 7.15, -2.814, 0, 7.417, 3.064, 0, 7.633, -1.369, 0, 7.817, 0.684, 0, 8, -1.707, 0, 8.217, 2.004, 0, 8.417, -1.289, 0, 8.617, 1.294, 0, 8.817, -1.771, 0, 9.033, 1.781, 0, 9.233, -1.371, 0, 9.433, 1.328, 0, 9.633, -1.492, 0, 9.833, 1.623, 0, 10.05, -1.54, 0, 10.233, 1.512, 0, 10.45, -1.446, 0, 10.717, 1.648, 0, 11.083, -1.961, 0, 11.5, 0.315, 0, 11.867, -0.823, 0, 12.317, 0.639, 0, 12.483, -2.492, 0, 12.733, 5.012, 0, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Jewelry_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.209, 2, 0.25, -0.209, 0, 0.55, 1.056, 0, 0.833, -2.47, 0, 1.133, 3.815, 0, 1.45, -4.346, 0, 1.75, 4.324, 0, 2.05, -3.9, 0, 2.317, 3.257, 0, 2.55, -1.918, 0, 2.75, 0.882, 0, 2.933, -1.001, 0, 3.133, 1.368, 0, 3.35, -1.257, 0, 3.55, 1.199, 0, 3.75, -1.413, 0, 3.967, 1.357, 0, 4.167, -1.158, 0, 4.367, 1.206, 0, 4.567, -1.374, 0, 4.783, 1.332, 0, 4.983, -1.142, 0, 5.183, 1.227, 0, 5.383, -1.326, 0, 5.567, 0.712, 0, 5.717, -0.035, 0, 5.85, 0.603, 0, 6.033, -0.997, 0, 6.217, 0.616, 0, 6.367, -0.469, 0, 6.55, 0.842, 0, 6.767, -1.443, 0, 7.017, 2.202, 0, 7.3, -3.312, 0, 7.567, 3.142, 0, 7.8, -1.423, 0, 7.967, 0.201, 0, 8.133, -1.152, 0, 8.35, 1.662, 0, 8.567, -1.208, 0, 8.767, 1.048, 0, 8.967, -1.491, 0, 9.183, 1.484, 0, 9.383, -1.081, 0, 9.583, 0.919, 0, 9.767, -1.118, 0, 9.983, 1.38, 0, 10.183, -1.38, 0, 10.383, 1.279, 0, 10.617, -1.335, 0, 10.883, 1.846, 0, 11.217, -1.526, 0, 11.583, 0.879, 0, 12, -0.623, 0, 12.45, 0.541, 0, 12.617, -2.584, 0, 12.9, 4.439, 0, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.066, 2, 0.25, 0.066, 0, 0.45, -0.887, 0, 0.717, 1.887, 0, 1.017, -2.372, 0, 1.317, 2.259, 0, 1.633, -1.952, 0, 1.917, 1.766, 0, 2.15, -1.631, 0, 2.35, 1.252, 0, 2.567, -1.352, 0, 2.767, 1.549, 0, 2.967, -1.403, 0, 3.167, 1.296, 0, 3.383, -1.481, 0, 3.583, 1.5, 0, 3.783, -1.339, 0, 3.983, 1.343, 0, 4.2, -1.512, 0, 4.4, 1.466, 0, 4.6, -1.329, 0, 4.8, 1.366, 0, 5.017, -1.514, 0, 5.217, 1.452, 0, 5.383, -1.213, 0, 5.55, 1.25, 0, 5.717, -1.423, 0, 5.883, 1.263, 0, 6.033, -1.202, 0, 6.2, 1.316, 0, 6.367, -1.04, 0, 6.583, 1.674, 0, 6.9, -1.899, 0, 7.183, 2.469, 0, 7.4, -0.746, 0, 7.567, 1.05, 0, 7.767, -1.667, 0, 7.983, 1.704, 0, 8.183, -1.178, 0, 8.383, 1.27, 0, 8.6, -1.612, 0, 8.8, 1.49, 0, 9, -1.262, 0, 9.2, 1.369, 0, 9.4, -1.503, 0, 9.6, 1.377, 0, 9.817, -1.408, 0, 10.017, 1.465, 0, 10.217, -1.393, 0, 10.417, 1.351, 0, 10.683, -0.799, 0, 11.1, 0.448, 0, 11.517, -0.313, 0, 11.95, 0.223, 0, 12.383, -0.137, 0, 12.483, 0.718, 0, 12.883, -0.457, 0, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.017, 2, 0.25, 0.017, 0, 0.383, 0.554, 0, 0.617, -2.361, 0, 0.883, 4.202, 0, 1.183, -4.943, 0, 1.5, 4.643, 0, 1.8, -4.194, 0, 2.083, 3.827, 0, 2.317, -2.993, 0, 2.533, 2.156, 0, 2.733, -2.315, 0, 2.933, 2.501, 0, 3.133, -2.204, 0, 3.35, 2.204, 0, 3.55, -2.55, 0, 3.75, 2.432, 0, 3.95, -2.13, 0, 4.15, 2.279, 0, 4.367, -2.58, 0, 4.567, 2.376, 0, 4.767, -2.133, 0, 4.967, 2.315, 0, 5.183, -2.576, 0, 5.367, 2.151, 0, 5.533, -1.477, 0, 5.7, 1.777, 0, 5.867, -1.975, 0, 6.033, 1.623, 0, 6.183, -1.59, 0, 6.333, 1.298, 0, 6.533, -2.224, 0, 6.783, 3.267, 0, 7.083, -4.654, 0, 7.333, 3.531, 0, 7.567, -1.433, 0, 7.717, 1.771, 0, 7.933, -3.079, 0, 8.15, 2.757, 0, 8.367, -1.898, 0, 8.55, 2.193, 0, 8.767, -2.73, 0, 8.967, 2.388, 0, 9.167, -2.034, 0, 9.367, 2.284, 0, 9.567, -2.407, 0, 9.783, 2.311, 0, 9.983, -2.425, 0, 10.183, 2.401, 0, 10.383, -2.212, 0, 10.6, 2.202, 0, 10.917, -1.879, 0, 11.3, 0.957, 0, 11.7, -0.766, 0, 12.15, 0.431, 0, 12.483, -0.956, 0, 12.75, 1.342, 0, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.359, 2, 0.25, 0.359, 0, 0.483, -2.312, 0, 0.783, 4.011, 0, 1.1, -4.737, 0, 1.417, 4.185, 0, 1.7, -3.552, 0, 2, 3.067, 0, 2.2, -2.734, 0, 2.417, 2, 0, 2.6, -2.388, 0, 2.817, 2.796, 0, 3.017, -2.471, 0, 3.217, 2.125, 0, 3.417, -2.605, 0, 3.65, 2.639, 0, 3.833, -2.378, 0, 4.033, 2.265, 0, 4.25, -2.595, 0, 4.467, 2.613, 0, 4.65, -2.352, 0, 4.85, 2.288, 0, 5.067, -2.611, 0, 5.283, 2.6, 0, 5.417, -1.993, 0, 5.6, 1.989, 0, 5.75, -2.568, 0, 5.933, 2.188, 0, 6.083, -2.199, 0, 6.25, 2.222, 0, 6.433, -2.394, 0, 6.633, 2.54, 0, 6.95, -2.936, 0, 7.233, 4.194, 0, 7.45, -2.451, 0, 7.65, 1.274, 0, 7.817, -2.688, 0, 8.05, 3.127, 0, 8.233, -2.264, 0, 8.433, 1.965, 0, 8.633, -2.691, 0, 8.867, 2.806, 0, 9.05, -2.279, 0, 9.267, 2.222, 0, 9.45, -2.487, 0, 9.667, 2.522, 0, 9.867, -2.521, 0, 10.083, 2.382, 0, 10.267, -2.475, 0, 10.467, 2.351, 0, 10.867, -2.36, 0, 11.3, 0.938, 0, 11.683, -0.689, 0, 12.1, 0.962, 0, 12.467, -0.565, 1, 12.472, -0.565, 12.478, 8.976, 12.483, 8.976, 0, 12.867, -5.799, 0, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.084, 2, 0.25, 0.084, 0, 0.4, 1.672, 0, 0.667, -6.558, 0, 0.983, 10.337, 0, 1.283, -11.192, 0, 1.6, 10.476, 0, 1.9, -8.765, 0, 2.167, 8.19, 0, 2.4, -5.288, 0, 2.583, 3.559, 0, 2.783, -4.528, 0, 3, 5.395, 0, 3.2, -4.271, 0, 3.4, 4.296, 0, 3.6, -4.964, 0, 3.817, 5.108, 0, 4.017, -4.175, 0, 4.217, 4.445, 0, 4.433, -5.02, 0, 4.633, 4.902, 0, 4.833, -4.088, 0, 5.033, 4.508, 0, 5.25, -5.059, 0, 5.417, 4.203, 0, 5.583, -2.195, 0, 5.733, 3.363, 0, 5.9, -4.049, 0, 6.067, 3.569, 0, 6.233, -3.04, 0, 6.4, 3.603, 0, 6.6, -4.457, 0, 6.85, 5.382, 0, 7.15, -8.441, 0, 7.417, 9.193, 0, 7.633, -4.106, 0, 7.817, 2.051, 0, 8, -5.12, 0, 8.217, 6.011, 0, 8.417, -3.868, 0, 8.617, 3.882, 0, 8.817, -5.313, 0, 9.033, 5.344, 0, 9.233, -4.114, 0, 9.433, 3.983, 0, 9.633, -4.476, 0, 9.833, 4.868, 0, 10.05, -4.622, 0, 10.233, 4.537, 0, 10.45, -4.338, 0, 10.717, 4.944, 0, 11.083, -5.882, 0, 11.5, 0.946, 0, 11.867, -2.469, 0, 12.317, 1.916, 0, 12.483, -7.476, 0, 12.733, 15.036, 0, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.627, 2, 0.25, -0.627, 0, 0.55, 3.168, 0, 0.833, -7.411, 0, 1.133, 11.445, 0, 1.45, -13.038, 0, 1.75, 12.972, 0, 2.05, -11.699, 0, 2.317, 9.771, 0, 2.55, -5.755, 0, 2.75, 2.645, 0, 2.933, -3.002, 0, 3.133, 4.104, 0, 3.35, -3.771, 0, 3.55, 3.596, 0, 3.75, -4.241, 0, 3.967, 4.072, 0, 4.167, -3.473, 0, 4.367, 3.618, 0, 4.567, -4.122, 0, 4.783, 3.995, 0, 4.983, -3.425, 0, 5.183, 3.682, 0, 5.383, -3.978, 0, 5.567, 2.135, 0, 5.717, -0.106, 0, 5.85, 1.809, 0, 6.033, -2.99, 0, 6.217, 1.848, 0, 6.367, -1.408, 0, 6.55, 2.525, 0, 6.767, -4.33, 0, 7.017, 6.605, 0, 7.3, -9.936, 0, 7.567, 9.426, 0, 7.8, -4.27, 0, 7.967, 0.603, 0, 8.133, -3.456, 0, 8.35, 4.985, 0, 8.567, -3.623, 0, 8.767, 3.146, 0, 8.967, -4.473, 0, 9.183, 4.453, 0, 9.383, -3.244, 0, 9.583, 2.758, 0, 9.767, -3.354, 0, 9.983, 4.14, 0, 10.183, -4.141, 0, 10.383, 3.837, 0, 10.617, -4.003, 0, 10.883, 5.539, 0, 11.217, -4.576, 0, 11.583, 2.638, 0, 12, -1.867, 0, 12.45, 1.622, 0, 12.617, -7.751, 0, 12.9, 13.318, 0, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.796, 2, 0.25, -1.796, 0, 0.633, 3.429, 0, 0.933, -8.368, 0, 1.25, 13.649, 0, 1.55, -16.651, 0, 1.867, 17.024, 0, 2.167, -15.651, 0, 2.45, 12.695, 0, 2.7, -7.131, 0, 2.933, 1.28, 0, 3.067, -0.354, 0, 3.25, 2.934, 0, 3.483, -3.125, 0, 3.683, 2.596, 0, 3.883, -3, 0, 4.1, 3.151, 0, 4.3, -2.394, 0, 4.5, 2.307, 0, 4.7, -3.322, 0, 4.917, 3.119, 0, 5.117, -2.273, 0, 5.317, 2.397, 0, 5.517, -2.819, 0, 5.783, 1.643, 0, 5.85, 1.607, 0, 5.867, 1.61, 0, 6.15, -2.649, 0, 6.35, 1.315, 0, 6.5, 0.17, 0, 6.65, 1.478, 0, 6.883, -4.07, 0, 7.133, 7.302, 0, 7.417, -11.132, 0, 7.7, 11.341, 0, 7.967, -5.692, 0, 8.467, 4.686, 0, 8.7, -3.417, 0, 8.9, 1.867, 0, 9.1, -2.722, 0, 9.317, 3.568, 0, 9.533, -2.318, 0, 9.717, 1.234, 0, 9.9, -2.136, 0, 10.1, 3.127, 0, 10.317, -3.452, 0, 10.533, 2.839, 0, 10.75, -3.708, 0, 11.017, 6.574, 0, 11.333, -6.718, 0, 11.683, 4.554, 0, 12.067, -3.005, 0, 12.467, 2.01, 0, 12.483, 1.835, 0, 12.5, 1.944, 0, 12.733, -7.258, 0, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.494, 2, 0.25, -3.494, 0, 0.283, -3.563, 0, 0.717, 4.091, 0, 1.05, -9.615, 0, 1.35, 16.327, 0, 1.667, -20.805, 0, 1.983, 22.062, 0, 2.283, -21.001, 0, 2.583, 16.887, 0, 2.867, -10.393, 0, 3.167, 3.22, 0, 3.6, -3.239, 0, 3.817, 2.088, 0, 4.033, -2.021, 0, 4.233, 2.256, 0, 4.45, -1.62, 0, 4.633, 1.266, 0, 4.833, -2.044, 0, 5.05, 2.48, 0, 5.267, -1.483, 0, 5.45, 1.266, 0, 5.65, -2.579, 0, 5.967, 2.71, 0, 6.267, -3.144, 0, 6.533, 1.624, 0, 6.983, -3.832, 0, 7.267, 8.113, 0, 7.533, -12.849, 0, 7.817, 14.04, 0, 8.117, -9.337, 0, 8.55, 5.517, 0, 8.833, -4.163, 0, 9.067, 0.902, 0, 9.233, -1.373, 0, 9.45, 3.053, 0, 9.683, -1.977, 0, 9.867, 0.053, 0, 10, -1.025, 0, 10.217, 2.842, 0, 10.45, -3.083, 0, 10.667, 2.081, 0, 10.883, -2.983, 0, 11.15, 7.336, 0, 11.467, -9.094, 0, 11.8, 7.265, 0, 12.167, -5.024, 0, 12.567, 3.009, 0, 12.833, -7.691, 0, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.306, 2, 0.25, -2.306, 0, 0.4, -5.807, 0, 0.783, 5.479, 0, 1.15, -11.311, 0, 1.45, 19.542, 0, 1.767, -25.728, 0, 2.083, 28.038, 0, 2.4, -26.729, 0, 2.7, 22.757, 0, 3, -15.426, 0, 3.317, 7.945, 0, 3.683, -4.89, 0, 3.95, 2.675, 0, 4.167, -1.474, 0, 4.367, 1.803, 0, 4.583, -1.188, 0, 4.767, 0.285, 0, 4.95, -1.449, 0, 5.183, 2.262, 0, 5.417, -1.17, 0, 5.583, 0.484, 0, 5.783, -2.284, 0, 6.083, 3.501, 0, 6.383, -4.01, 0, 6.7, 2.972, 0, 7.083, -4.396, 0, 7.367, 9.034, 0, 7.65, -15.062, 0, 7.933, 17.624, 0, 8.25, -14.04, 0, 8.617, 8.876, 0, 8.95, -6.294, 0, 9.217, 1.694, 0, 9.383, 0.12, 0, 9.583, 2.053, 0, 9.833, -2.04, 0, 10.033, -0.421, 0, 10.067, -0.426, 0, 10.333, 2.678, 0, 10.583, -3.209, 0, 10.8, 1.652, 0, 11, -2.122, 0, 11.267, 7.843, 0, 11.567, -11.639, 0, 11.917, 10.723, 0, 12.267, -8.124, 0, 12.633, 5.034, 0, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.574, 2, 0.25, 5.574, 0, 0.5, -8.988, 0, 0.867, 7.976, 0, 1.233, -13.496, 0, 1.55, 22.922, 0, 1.85, -30, 2, 1.867, -30, 0, 2.133, 30, 2, 2.217, 30, 0, 2.467, -30, 2, 2.533, -30, 0, 2.817, 29.047, 0, 3.133, -22.115, 0, 3.45, 14.013, 0, 3.783, -8.499, 0, 4.083, 4.585, 0, 4.333, -1.596, 0, 4.533, 1.147, 0, 4.75, -0.844, 0, 4.9, -0.343, 0, 5.05, -0.987, 0, 5.317, 2.281, 0, 5.567, -1.32, 0, 5.717, -0.099, 0, 5.917, -1.816, 0, 6.2, 4.098, 0, 6.5, -5.205, 0, 6.817, 4.633, 0, 7.167, -5.586, 0, 7.483, 10.417, 0, 7.767, -17.44, 0, 8.05, 21.777, 0, 8.367, -19.849, 0, 8.717, 14.493, 0, 9.067, -9.873, 0, 9.367, 4.075, 0, 9.617, 0.777, 0, 9.683, 0.851, 0, 9.967, -2.255, 0, 10.417, 2.845, 0, 10.7, -3.748, 0, 10.95, 1.75, 0, 11.133, -1.031, 0, 11.383, 7.979, 0, 11.683, -14.228, 0, 12.017, 14.934, 0, 12.367, -12.422, 0, 12.717, 7.94, 0, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.417, 1, 0, 3.25, 0, 0, 4.417, 1, 0, 6.25, 0, 0, 7.417, 1, 0, 9.25, 0, 0, 10.417, 1, 0, 12.25, 0, 1, 12.583, 0, 12.917, 0.735, 13.25, 0.945]}, {"Target": "Parameter", "Id": "fenshangxuanzhuang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 4.583, 1.735, 8.917, 3.47, 13.25, 5.205]}, {"Target": "Parameter", "Id": "touch_drag5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 3.233, 0, 2, 5.6, 0, 2, 6.25, 0, 2, 7.25, 0, 2, 7.65, 0, 2, 8.65, 0, 2, 9.05, 0, 2, 10.05, 0, 1, 10.111, 0.333, 10.172, 0.667, 10.233, 1, 2, 12.467, 1, 0, 12.483, 0, 2, 12.933, 0, 2, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamSad2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 13.25, 1]}, {"Target": "Parameter", "Id": "ParamCRY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.2, 0, 13.25, -5.2]}, {"Target": "Parameter", "Id": "Position_Coordinates", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 13.25, 1]}, {"Target": "Parameter", "Id": "Man_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 13.25, 1]}, {"Target": "Parameter", "Id": "Man_TMD2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "BG_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 13.25, 1]}, {"Target": "Parameter", "Id": "FG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "BG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "BG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "BG_White5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "BG_White2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "All_Size4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "tuerzuo3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "faqiehuan", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "faqiehuan2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "faqiehuan4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "faqiehuan3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "tuerzuo4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Mail_Change", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "dafengshangzhuanchang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "tongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamTongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "EyeFrame_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamBrowY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamSigh", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamDarken", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamCry", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_B", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_C", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "MB_Unidirectional_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "huqi1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "huqi2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamArmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 13.25, 1]}, {"Target": "Parameter", "Id": "ParamForearmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamArmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamArmL_ForearmL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamForearmL_HandL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamHandLSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "xiongjiya2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamArmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 13.25, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamArmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamArmR_ForearmR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamForearm<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 13.25, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_HandR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamHandRSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 13.25, 0.6]}, {"Target": "Parameter", "Id": "Change_idle_physics23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamLegL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 13.25, 1]}, {"Target": "Parameter", "Id": "ParamLegLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamLegLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamCrusLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamCrusLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>oodLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamFoodLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamFoodLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamLegR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 13.25, 1]}, {"Target": "Parameter", "Id": "ParamLegRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamLegRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamCrusRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamCrusRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamCrus<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>ood<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamFoodRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamFoodRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Physics_Provisional", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "physics_Add_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "physics_Add_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamBodyStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Physics_AngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Physics_AngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Physics_Body_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Physics_Body_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Physics_breast", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamBreath2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "touch_drag1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "touch_drag2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "touch_drag3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "touch_drag4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "touch_drag6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "touch_drag7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "touch_drag8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "touch_drag9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>pian<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh654", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh655", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh657", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh656", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh658", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh659", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh660", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.25, 0]}], "UserData": [{"Time": 12.75, "Value": ""}]}