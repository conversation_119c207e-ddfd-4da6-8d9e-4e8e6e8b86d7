{"Version": 3, "Meta": {"Duration": 12.917, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 304, "TotalSegmentCount": 3134, "TotalPointCount": 3508, "UserDataCount": 1, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "Mail_Transparency6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 0, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 1.483, -0.171, 2.717, -0.341, 3.95, -0.512, 2, 3.967, -1.2, 1, 5.278, -1.807, 6.589, -2.415, 7.9, -3.022, 2, 7.917, 3.677, 1, 8.672, 3.231, 9.428, 2.786, 10.183, 2.34, 2, 10.967, 2.34, 1, 11.039, 2.34, 11.111, 6.191, 11.183, 7.904, 1, 11.361, 12.12, 11.539, 13.14, 11.717, 13.14, 1, 11.856, 13.14, 11.994, 12.956, 12.133, 12.54, 2, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.18, 2, 0.25, -0.18, 1, 1.483, -0.593, 2.717, -1.007, 3.95, -1.42, 2, 7.9, 0.518, 2, 7.917, -0.754, 1, 8.672, -0.863, 9.428, -0.971, 10.183, -1.08, 2, 10.967, -1.08, 1, 11.039, -1.08, 11.111, -5.041, 11.183, -5.571, 1, 11.361, -6.876, 11.539, -8.411, 11.717, -8.52, 1, 11.856, -8.605, 11.994, -8.58, 12.133, -8.58, 2, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.5, 2, 0.25, 1.5, 2, 3.95, 1.5, 2, 3.967, 7.5, 1, 5.278, 5.116, 6.589, 2.731, 7.9, 0.347, 2, 10.183, 3.6, 2, 10.967, 3.6, 2, 12.133, 3.6, 2, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 4.14, 2, 0.25, 4.14, 1, 1.483, 4.36, 2.717, 4.58, 3.95, 4.8, 2, 3.967, 10.26, 1, 5.278, 12.696, 6.589, 15.131, 7.9, 17.567, 2, 7.917, 7.495, 1, 8.672, 5.757, 9.428, 4.018, 10.183, 2.28, 2, 10.967, 2.28, 2, 12.133, 2.28, 2, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "FG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.517, 0, 2, 2.783, 0, 2, 3.667, 0, 2, 4.55, 0, 2, 5.433, 0, 2, 6.317, 0, 2, 6.933, 0, 2, 7.55, 0, 2, 8.183, 0, 2, 8.8, 0, 2, 9.417, 0, 2, 10.05, 0, 2, 10.183, 0, 2, 11.717, 0, 2, 11.733, 0, 0, 12.133, 1, 0, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "BG_White3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.3, 2, 0.25, 0.3, 0, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "All_Size2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -12.72, 2, 0.25, -12.72, 2, 12.133, -12.72, 1, 12.139, -12.72, 12.144, 0, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 4.55, 1, 2, 7.55, 1, 2, 10.183, 1, 0, 11.233, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.267, 0, 0, 1.733, -1, 2, 3.167, -1, 0, 3.55, 0.1, 2, 5.483, 0.1, 0, 6.883, -1, 0, 9.05, 0, 2, 10.233, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY_SD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 0.267, 1, 0, 1.733, 0.5, 2, 3.167, 0.5, 1, 3.295, 0.5, 3.422, 0.884, 3.55, 0.9, 1, 4.194, 0.979, 4.839, 1, 5.483, 1, 0, 6.883, 0.5, 0, 9.05, 1, 0, 10.233, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 0.267, 1, 0, 0.533, 1.3, 0, 0.75, 0, 2, 1.2, 0, 0, 1.75, 0.538, 2, 2.383, 0.538, 0, 2.533, 0, 0, 2.617, 1.3, 0, 2.733, 1.252, 2, 3.183, 1.252, 0, 3.283, 0, 2, 6.35, 0, 0, 6.9, 0.538, 2, 7.533, 0.538, 0, 7.683, 0, 0, 7.767, 1.3, 1, 7.806, 1.3, 7.844, 1.3, 7.883, 1.252, 1, 7.966, 1.069, 8.05, 0, 8.133, 0, 2, 8.583, 0, 1, 8.739, 0, 8.894, 0.498, 9.05, 1, 1, 9.139, 1.287, 9.228, 1.3, 9.317, 1.3, 0, 9.533, 0, 2, 9.983, 0, 1, 10.066, 0, 10.15, 0.042, 10.233, 0.39, 1, 10.272, 0.553, 10.311, 1.3, 10.35, 1.3, 0, 10.433, 1, 2, 12.033, 1, 2, 12.467, 1, 2, 12.483, 1, 2, 12.6, 1, 2, 12.917, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 0.267, 1, 0, 0.533, 1.3, 0, 0.75, 0, 2, 1.2, 0, 0, 1.75, 0.538, 2, 2.383, 0.538, 0, 2.533, 0, 0, 2.617, 1.3, 0, 2.733, 1.252, 2, 3.183, 1.252, 0, 3.283, 0, 2, 6.35, 0, 0, 6.9, 0.538, 2, 7.533, 0.538, 0, 7.683, 0, 0, 7.767, 1.3, 1, 7.806, 1.3, 7.844, 1.3, 7.883, 1.252, 1, 7.966, 1.069, 8.05, 0, 8.133, 0, 2, 8.583, 0, 1, 8.739, 0, 8.894, 0.498, 9.05, 1, 1, 9.139, 1.287, 9.228, 1.3, 9.317, 1.3, 0, 9.533, 0, 2, 9.983, 0, 1, 10.066, 0, 10.15, 0.042, 10.233, 0.39, 1, 10.272, 0.553, 10.311, 1.3, 10.35, 1.3, 0, 10.433, 1, 2, 12.033, 1, 2, 12.467, 1, 2, 12.483, 1, 2, 12.6, 1, 2, 12.917, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.267, 0, 2, 0.533, 0, 1, 0.661, 0, 0.789, -0.377, 0.917, -0.5, 1, 1.034, -0.613, 1.15, -0.6, 1.267, -0.6, 1, 1.406, -0.6, 1.544, 0.446, 1.683, 0.5, 1, 1.939, 0.6, 2.194, 0.6, 2.45, 0.6, 0, 2.617, 0, 2, 3.75, 0, 2, 4.133, 0, 0, 6.417, -0.6, 1, 6.556, -0.6, 6.694, 0.446, 6.833, 0.5, 1, 7.089, 0.6, 7.344, 0.6, 7.6, 0.6, 1, 7.656, 0.6, 7.711, 0.066, 7.767, 0, 1, 7.945, -0.21, 8.122, -0.367, 8.3, -0.5, 1, 8.417, -0.587, 8.533, -0.6, 8.65, -0.6, 0, 9.05, 0, 2, 9.317, 0, 1, 9.445, 0, 9.572, -0.377, 9.7, -0.5, 1, 9.817, -0.613, 9.933, -0.6, 10.05, -0.6, 0, 10.233, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.267, 0, 2, 0.533, 0, 1, 0.661, 0, 0.789, -0.209, 0.917, -0.4, 1, 1.034, -0.575, 1.15, -0.6, 1.267, -0.6, 0, 1.683, 0.2, 0, 2.45, 0, 2, 2.617, 0, 2, 3.75, 0, 2, 4.133, 0, 0, 6.417, -0.6, 0, 6.833, 0.2, 0, 7.6, 0, 2, 7.767, 0, 1, 7.945, 0, 8.122, -0.163, 8.3, -0.4, 1, 8.417, -0.556, 8.533, -0.6, 8.65, -0.6, 0, 9.05, 0, 2, 9.317, 0, 1, 9.445, 0, 9.572, -0.209, 9.7, -0.4, 1, 9.817, -0.575, 9.933, -0.6, 10.05, -0.6, 0, 10.233, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1, 2, 0.25, -1, 2, 0.267, -1, 2, 9.05, -1, 2, 10.233, -1, 2, 10.45, -1, 0, 11.583, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamBrowForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1, 2, 0.25, -1, 2, 0.267, -1, 2, 9.05, -1, 0, 10.233, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamBrowAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.284, 2, 0.25, 0.284, 2, 0.267, 0.284, 0, 0.717, 0.4, 0, 0.85, 0.284, 0, 1.3, 0.4, 0, 1.433, 0.284, 0, 1.883, 0.4, 0, 2.017, 0.284, 0, 2.333, 0.4, 0, 2.417, 0.284, 0, 2.733, 0.4, 0, 2.833, 0.284, 0, 3.15, 0.4, 0, 3.233, 0.284, 0, 3.55, 0.4, 0, 3.65, 0.284, 0, 3.967, 0.4, 0, 4.05, 0.284, 0, 4.367, 0.4, 0, 4.467, 0.284, 0, 4.783, 0.4, 0, 4.867, 0.284, 0, 5.183, 0.4, 0, 5.283, 0.284, 0, 5.533, 0.4, 0, 5.6, 0.284, 0, 5.85, 0.4, 0, 5.933, 0.284, 0, 6.183, 0.4, 2, 6.45, 0.4, 0, 6.583, 0.284, 0, 7.033, 0.4, 0, 7.167, 0.284, 0, 7.483, 0.4, 0, 7.567, 0.284, 0, 7.883, 0.4, 2, 8.1, 0.4, 0, 8.233, 0.284, 0, 8.683, 0.4, 0, 8.817, 0.284, 2, 9.05, 0.284, 0, 9.5, 0.4, 0, 9.633, 0.284, 0, 10.083, 0.4, 0, 10.217, 0.284, 0, 10.233, 0.4, 2, 10.45, 0.4, 0, 11.583, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamEyeLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.267, 0, 0, 0.45, -0.43, 0, 0.617, 1, 2, 0.817, 1, 0, 1.067, -0.344, 0, 1.267, -0.048, 0, 1.517, -0.331, 0, 1.85, 0.175, 0, 2.167, -0.049, 0, 2.5, 1, 2, 2.517, 1, 0, 2.6, -1, 2, 2.967, -1, 0, 3.217, 1, 2, 3.317, 1, 0, 3.333, -1, 2, 3.467, -1, 0, 3.683, 0.429, 0, 4, -0.118, 0, 4.317, 0.033, 0, 4.617, -0.009, 2, 4.633, -0.009, 0, 4.917, 0.003, 2, 4.933, 0.003, 0, 5.2, -0.001, 2, 5.283, -0.001, 0, 5.317, 0, 2, 5.333, 0, 2, 5.4, 0, 2, 5.417, 0, 2, 5.45, 0, 2, 5.483, 0, 2, 5.5, 0, 2, 5.617, 0, 2, 5.633, 0, 2, 5.683, 0, 2, 5.7, 0, 2, 5.8, 0, 2, 5.817, 0, 2, 5.9, 0, 2, 5.917, 0, 2, 6.35, 0, 0, 6.633, -0.368, 0, 7, 0.179, 0, 7.3, -0.05, 0, 7.65, 1, 2, 7.667, 1, 0, 7.75, -1, 2, 7.917, -1, 0, 7.933, 1, 2, 8.05, 1, 0, 8.25, -0.957, 0, 8.567, 0.24, 0, 8.9, -0.68, 0, 9.4, 1, 2, 9.55, 1, 0, 9.783, -0.372, 0, 10.033, 0.052, 0, 10.267, -1, 2, 10.4, -1, 0, 10.717, 0.225, 0, 11.033, -0.063, 0, 11.35, 0.018, 0, 11.65, -0.005, 2, 11.667, -0.005, 0, 11.95, 0.001, 2, 11.983, 0.001, 0, 12.183, 0, 2, 12.2, 0, 2, 12.217, 0, 2, 12.233, 0, 2, 12.25, 0, 2, 12.317, 0, 2, 12.333, 0, 2, 12.35, 0, 2, 12.367, 0, 2, 12.4, 0, 2, 12.417, 0, 2, 12.433, 0, 2, 12.45, 0, 2, 12.483, 0, 2, 12.5, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamEyeRsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.267, 0, 0, 0.45, -0.43, 0, 0.617, 1, 2, 0.817, 1, 0, 1.067, -0.344, 0, 1.267, -0.048, 0, 1.517, -0.331, 0, 1.85, 0.175, 0, 2.167, -0.049, 0, 2.5, 1, 2, 2.517, 1, 0, 2.6, -1, 2, 2.967, -1, 0, 3.217, 1, 2, 3.317, 1, 0, 3.333, -1, 2, 3.467, -1, 0, 3.683, 0.429, 0, 4, -0.118, 0, 4.317, 0.033, 0, 4.617, -0.009, 2, 4.633, -0.009, 0, 4.917, 0.003, 2, 4.933, 0.003, 0, 5.2, -0.001, 2, 5.283, -0.001, 0, 5.317, 0, 2, 5.333, 0, 2, 5.4, 0, 2, 5.417, 0, 2, 5.45, 0, 2, 5.483, 0, 2, 5.5, 0, 2, 5.617, 0, 2, 5.633, 0, 2, 5.683, 0, 2, 5.7, 0, 2, 5.8, 0, 2, 5.817, 0, 2, 5.9, 0, 2, 5.917, 0, 2, 6.35, 0, 0, 6.633, -0.368, 0, 7, 0.179, 0, 7.3, -0.05, 0, 7.65, 1, 2, 7.667, 1, 0, 7.75, -1, 2, 7.917, -1, 0, 7.933, 1, 2, 8.05, 1, 0, 8.25, -0.957, 0, 8.567, 0.24, 0, 8.9, -0.68, 0, 9.4, 1, 2, 9.55, 1, 0, 9.783, -0.372, 0, 10.033, 0.052, 0, 10.267, -1, 2, 10.4, -1, 0, 10.717, 0.225, 0, 11.033, -0.063, 0, 11.35, 0.018, 0, 11.65, -0.005, 2, 11.667, -0.005, 0, 11.95, 0.001, 2, 11.983, 0.001, 0, 12.183, 0, 2, 12.2, 0, 2, 12.217, 0, 2, 12.233, 0, 2, 12.25, 0, 2, 12.317, 0, 2, 12.333, 0, 2, 12.35, 0, 2, 12.367, 0, 2, 12.4, 0, 2, 12.417, 0, 2, 12.433, 0, 2, 12.45, 0, 2, 12.483, 0, 2, 12.5, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamEyeLGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.267, 0, 0, 0.417, 0.124, 0, 0.6, -0.416, 0, 0.85, 1, 2, 1.033, 1, 0, 1.3, -0.222, 0, 1.533, 0.058, 0, 1.767, -0.115, 0, 2.033, 0.1, 0, 2.317, -0.05, 0, 2.383, -0.041, 0, 2.483, -0.274, 0, 2.567, 0.567, 0, 2.6, 0.403, 0, 2.683, 1, 2, 2.95, 1, 0, 3.167, -0.374, 0, 3.183, -0.368, 0, 3.2, -0.409, 0, 3.267, -0.138, 0, 3.417, -0.581, 0, 3.817, 0.317, 0, 4.133, -0.14, 0, 4.45, 0.055, 0, 4.75, -0.02, 2, 4.767, -0.02, 0, 5.067, 0.007, 0, 5.35, -0.002, 2, 5.367, -0.002, 2, 5.383, -0.002, 0, 5.65, 0.001, 2, 5.733, 0.001, 0, 5.783, 0, 2, 5.8, 0, 2, 5.867, 0, 2, 5.883, 0, 2, 5.9, 0, 2, 5.917, 0, 2, 5.933, 0, 2, 6.083, 0, 2, 6.1, 0, 2, 6.15, 0, 2, 6.167, 0, 2, 6.233, 0, 2, 6.25, 0, 2, 6.35, 0, 0, 6.533, 0.084, 0, 6.867, -0.125, 0, 7.15, 0.112, 0, 7.45, -0.055, 0, 7.533, -0.04, 0, 7.633, -0.271, 0, 7.717, 0.571, 0, 7.75, 0.405, 0, 7.833, 1, 2, 7.9, 1, 0, 7.917, -1, 2, 7.95, -1, 0, 8.133, 0.757, 0, 8.417, -0.488, 0, 8.733, 0.332, 0, 9.033, -0.172, 0, 9.067, -0.169, 0, 9.167, -0.199, 0, 9.333, -0.032, 0, 9.4, -0.176, 0, 9.65, 0.943, 0, 9.983, -0.316, 0, 10.25, 0.288, 0, 10.433, -0.823, 0, 10.85, 0.254, 0, 11.167, -0.099, 0, 11.483, 0.036, 0, 11.783, -0.013, 0, 12.1, 0.004, 0, 12.4, -0.001, 2, 12.417, -0.001, 0, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamEyeRGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.267, 0, 0, 0.417, 0.124, 0, 0.6, -0.416, 0, 0.85, 1, 2, 1.033, 1, 0, 1.3, -0.222, 0, 1.533, 0.058, 0, 1.767, -0.115, 0, 2.033, 0.1, 0, 2.317, -0.05, 0, 2.383, -0.041, 0, 2.483, -0.274, 0, 2.567, 0.567, 0, 2.6, 0.403, 0, 2.683, 1, 2, 2.95, 1, 0, 3.167, -0.374, 0, 3.183, -0.368, 0, 3.2, -0.409, 0, 3.267, -0.138, 0, 3.417, -0.581, 0, 3.817, 0.317, 0, 4.133, -0.14, 0, 4.45, 0.055, 0, 4.75, -0.02, 2, 4.767, -0.02, 0, 5.067, 0.007, 0, 5.35, -0.002, 2, 5.367, -0.002, 2, 5.383, -0.002, 0, 5.65, 0.001, 2, 5.733, 0.001, 0, 5.783, 0, 2, 5.8, 0, 2, 5.867, 0, 2, 5.883, 0, 2, 5.9, 0, 2, 5.917, 0, 2, 5.933, 0, 2, 6.083, 0, 2, 6.1, 0, 2, 6.15, 0, 2, 6.167, 0, 2, 6.233, 0, 2, 6.25, 0, 2, 6.35, 0, 0, 6.533, 0.084, 0, 6.867, -0.125, 0, 7.15, 0.112, 0, 7.45, -0.055, 0, 7.533, -0.04, 0, 7.633, -0.271, 0, 7.717, 0.571, 0, 7.75, 0.405, 0, 7.833, 1, 2, 7.9, 1, 0, 7.917, -1, 2, 7.95, -1, 0, 8.133, 0.757, 0, 8.417, -0.488, 0, 8.733, 0.332, 0, 9.033, -0.172, 0, 9.067, -0.169, 0, 9.167, -0.199, 0, 9.333, -0.032, 0, 9.4, -0.176, 0, 9.65, 0.943, 0, 9.983, -0.316, 0, 10.25, 0.288, 0, 10.433, -0.823, 0, 10.85, 0.254, 0, 11.167, -0.099, 0, 11.483, 0.036, 0, 11.783, -0.013, 0, 12.1, 0.004, 0, 12.4, -0.001, 2, 12.417, -0.001, 0, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5, 2, 0.25, 5, 2, 0.267, 5, 2, 3.867, 5, 0, 3.883, 0, 2, 9.867, 0, 2, 9.883, 0, 0, 10.633, -5, 2, 12.467, -5, 0, 12.483, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.267, 0, 2, 3.867, 0, 0, 3.883, -5, 2, 9.867, -5, 1, 9.872, -5, 9.878, -0.076, 9.883, 0, 1, 10.133, 3.398, 10.383, 5, 10.633, 5, 2, 11.367, 5, 2, 12.467, 5, 0, 12.483, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 0.267, 1, 2, 9.05, 1, 0, 10.233, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "jing<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.267, 0, 2, 0.85, 0, 2, 1.433, 0, 2, 2.017, 0, 2, 2.417, 0, 2, 2.833, 0, 2, 3.233, 0, 2, 3.65, 0, 2, 4.05, 0, 2, 4.467, 0, 2, 4.867, 0, 2, 5.283, 0, 2, 5.6, 0, 2, 5.933, 0, 2, 6.583, 0, 2, 7.167, 0, 2, 7.567, 0, 2, 8.233, 0, 2, 8.817, 0, 2, 9.05, 0, 2, 9.633, 0, 2, 10.217, 0, 2, 10.233, 0, 2, 10.5, 0, 0, 10.567, 1, 2, 10.583, 1, 2, 10.617, 1, 2, 10.633, 1, 2, 10.65, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.374, 2, 0.25, -2.374, 2, 0.267, -2.374, 2, 0.85, -2.374, 2, 1.433, -2.374, 2, 2.017, -2.374, 2, 2.417, -2.374, 2, 2.833, -2.374, 2, 3.233, -2.374, 2, 3.65, -2.374, 2, 4.05, -2.374, 2, 4.467, -2.374, 2, 4.867, -2.374, 2, 5.283, -2.374, 2, 5.6, -2.374, 2, 5.933, -2.374, 2, 6.583, -2.374, 2, 7.167, -2.374, 2, 7.567, -2.374, 2, 8.233, -2.374, 2, 8.817, -2.374, 2, 9.05, -2.374, 2, 9.633, -2.374, 2, 10.217, -2.374, 1, 10.222, -2.374, 10.228, -9.563, 10.233, -9.591, 1, 10.333, -10.092, 10.433, -10.52, 10.533, -11.02, 1, 10.539, -11.048, 10.544, -11.597, 10.55, -11.597, 1, 10.583, -11.597, 10.617, -11.057, 10.65, -7.8, 1, 10.689, -4, 10.728, 0, 10.767, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 6.925, 2, 0.25, 6.925, 2, 0.267, 6.925, 2, 0.85, 6.925, 2, 1.433, 6.925, 2, 2.017, 6.925, 2, 2.417, 6.925, 2, 2.833, 6.925, 2, 3.233, 6.925, 2, 3.65, 6.925, 2, 4.05, 6.925, 2, 4.467, 6.925, 2, 4.867, 6.925, 2, 5.283, 6.925, 2, 5.6, 6.925, 2, 5.933, 6.925, 2, 6.583, 6.925, 2, 7.167, 6.925, 2, 7.567, 6.925, 2, 8.233, 6.925, 2, 8.817, 6.925, 2, 9.05, 6.925, 2, 9.633, 6.925, 2, 10.217, 6.925, 1, 10.222, 6.925, 10.228, 2.086, 10.233, 2.049, 1, 10.333, 1.384, 10.433, 1.1, 10.533, 1.1, 1, 10.539, 1.1, 10.544, 0.706, 10.55, 1.341, 1, 10.583, 5.152, 10.617, 21.78, 10.65, 21.78, 0, 10.767, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -4.027, 2, 0.25, -4.027, 2, 0.267, -4.027, 2, 0.85, -4.027, 2, 1.433, -4.027, 2, 2.017, -4.027, 2, 2.417, -4.027, 2, 2.833, -4.027, 2, 3.233, -4.027, 2, 3.65, -4.027, 2, 4.05, -4.027, 2, 4.467, -4.027, 2, 4.867, -4.027, 2, 5.283, -4.027, 2, 5.6, -4.027, 2, 5.933, -4.027, 2, 6.583, -4.027, 2, 7.167, -4.027, 2, 7.567, -4.027, 2, 8.233, -4.027, 2, 8.817, -4.027, 2, 9.05, -4.027, 2, 9.633, -4.027, 2, 10.217, -4.027, 1, 10.222, -4.027, 10.228, -6.796, 10.233, -6.798, 1, 10.333, -6.828, 10.433, -6.84, 10.533, -6.84, 1, 10.572, -6.84, 10.611, -1.055, 10.65, -0.48, 1, 10.689, 0.095, 10.728, 0, 10.767, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10.008, 2, 0.25, -10.008, 2, 0.267, -10.008, 2, 0.85, -10.008, 2, 1.433, -10.008, 2, 2.017, -10.008, 2, 2.417, -10.008, 2, 2.833, -10.008, 2, 3.233, -10.008, 2, 3.65, -10.008, 2, 4.05, -10.008, 2, 4.467, -10.008, 2, 4.867, -10.008, 2, 5.283, -10.008, 2, 5.6, -10.008, 2, 5.933, -10.008, 2, 6.583, -10.008, 2, 7.167, -10.008, 2, 7.567, -10.008, 2, 8.233, -10.008, 2, 8.817, -10.008, 2, 9.05, -10.008, 2, 9.633, -10.008, 2, 10.217, -10.008, 1, 10.222, -10.008, 10.228, -14.958, 10.233, -14.96, 1, 10.333, -14.988, 10.433, -15, 10.533, -15, 2, 10.65, -15, 0, 10.767, 0, 2, 12.033, 0, 2, 12.467, 0, 2, 12.483, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.8, 2, 0.25, -2.8, 2, 1.517, -2.8, 2, 2.783, -2.8, 2, 3.667, -2.8, 2, 4.55, -2.8, 2, 5.433, -2.8, 2, 6.317, -2.8, 2, 6.933, -2.8, 2, 7.55, -2.8, 2, 8.183, -2.8, 2, 8.8, -2.8, 2, 9.417, -2.8, 2, 10.05, -2.8, 2, 10.183, -2.8, 2, 11.717, -2.8, 2, 12.133, -2.8, 0, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -11.8, 2, 0.25, -11.8, 2, 12.117, -11.8, 0, 12.15, 30, 2, 12.6, 30, 2, 12.917, 30]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 1.517, 1, 2, 2.783, 1, 2, 3.667, 1, 2, 4.55, 1, 2, 5.433, 1, 2, 6.317, 1, 2, 6.933, 1, 2, 7.55, 1, 2, 8.183, 1, 2, 8.8, 1, 2, 9.417, 1, 2, 10.05, 1, 2, 10.183, 1, 2, 11.717, 1, 2, 12.133, 1, 0, 12.15, -1, 2, 12.6, -1, 2, 12.917, -1]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.26, 2, 0.25, 0.26, 2, 1.517, 0.26, 2, 2.783, 0.26, 2, 3.667, 0.26, 2, 4.55, 0.26, 2, 5.433, 0.26, 2, 6.317, 0.26, 2, 6.933, 0.26, 2, 7.55, 0.26, 2, 8.183, 0.26, 2, 8.8, 0.26, 2, 9.417, 0.26, 2, 10.05, 0.26, 2, 10.183, 0.26, 2, 11.717, 0.26, 2, 12.133, 0.26, 0, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamHandLDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 6, 2, 0.25, 6, 2, 1.517, 6, 2, 2.783, 6, 2, 3.667, 6, 2, 4.55, 6, 2, 5.433, 6, 2, 6.317, 6, 2, 6.933, 6, 2, 7.55, 6, 2, 8.183, 6, 2, 8.8, 6, 2, 9.417, 6, 2, 10.05, 6, 2, 10.183, 6, 2, 11.717, 6, 2, 12.133, 6, 0, 12.15, 10, 2, 12.6, 10, 2, 12.917, 10]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.032, 2, 0.25, -0.032, 0, 0.383, -0.072, 1, 0.472, -0.072, 0.561, -0.056, 0.65, 0, 1, 0.756, 0.066, 0.861, 0.114, 0.967, 0.114, 1, 1.111, 0.114, 1.256, 0.078, 1.4, 0, 1, 1.483, -0.045, 1.567, -0.072, 1.65, -0.072, 1, 1.739, -0.072, 1.828, -0.056, 1.917, 0, 1, 2.022, 0.066, 2.128, 0.114, 2.233, 0.114, 1, 2.378, 0.114, 2.522, 0.083, 2.667, 0, 1, 2.734, -0.038, 2.8, -0.072, 2.867, -0.072, 1, 2.928, -0.072, 2.989, -0.054, 3.05, 0, 1, 3.128, 0.069, 3.205, 0.114, 3.283, 0.114, 1, 3.383, 0.114, 3.483, 0.079, 3.583, 0, 1, 3.639, -0.044, 3.694, -0.072, 3.75, -0.072, 1, 3.817, -0.072, 3.883, -0.057, 3.95, 0, 1, 4.022, 0.062, 4.095, 0.114, 4.167, 0.114, 1, 4.267, 0.114, 4.367, 0.077, 4.467, 0, 1, 4.528, -0.047, 4.589, -0.072, 4.65, -0.072, 1, 4.711, -0.072, 4.772, -0.056, 4.833, 0, 1, 4.905, 0.066, 4.978, 0.114, 5.05, 0.114, 1, 5.15, 0.114, 5.25, 0.077, 5.35, 0, 1, 5.411, -0.047, 5.472, -0.072, 5.533, -0.072, 1, 5.594, -0.072, 5.656, -0.056, 5.717, 0, 1, 5.789, 0.066, 5.861, 0.114, 5.933, 0.114, 1, 6.033, 0.114, 6.133, 0.067, 6.233, 0.013, 1, 6.283, -0.014, 6.333, -0.016, 6.383, -0.016, 1, 6.422, -0.016, 6.461, -0.007, 6.5, 0.013, 1, 6.556, 0.042, 6.611, 0.058, 6.667, 0.058, 1, 6.739, 0.058, 6.811, 0.045, 6.883, 0.013, 1, 6.922, -0.004, 6.961, -0.016, 7, -0.016, 1, 7.044, -0.016, 7.089, -0.01, 7.133, 0.013, 1, 7.183, 0.038, 7.233, 0.058, 7.283, 0.058, 1, 7.355, 0.058, 7.428, 0.045, 7.5, 0.013, 1, 7.539, -0.004, 7.578, -0.016, 7.617, -0.016, 1, 7.661, -0.016, 7.706, -0.01, 7.75, 0.013, 1, 7.8, 0.038, 7.85, 0.058, 7.9, 0.058, 1, 7.972, 0.058, 8.045, 0.044, 8.117, 0.013, 1, 8.161, -0.006, 8.206, -0.016, 8.25, -0.016, 1, 8.289, -0.016, 8.328, -0.007, 8.367, 0.013, 1, 8.422, 0.042, 8.478, 0.058, 8.533, 0.058, 1, 8.6, 0.058, 8.666, 0.047, 8.733, 0, 1, 8.778, -0.032, 8.822, -0.072, 8.867, -0.072, 1, 8.911, -0.072, 8.956, -0.057, 9, 0, 1, 9.05, 0.064, 9.1, 0.114, 9.15, 0.114, 1, 9.217, 0.114, 9.283, 0.074, 9.35, 0, 1, 9.394, -0.05, 9.439, -0.072, 9.483, -0.072, 1, 9.528, -0.072, 9.572, -0.057, 9.617, 0, 1, 9.667, 0.064, 9.717, 0.114, 9.767, 0.114, 1, 9.839, 0.114, 9.911, 0.08, 9.983, 0, 1, 10.022, -0.043, 10.061, -0.072, 10.1, -0.072, 0, 10.183, 0, 2, 11.717, 0, 2, 12.133, 0, 2, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 1.517, 1, 2, 2.783, 1, 2, 3.667, 1, 2, 4.55, 1, 2, 5.433, 1, 2, 6.317, 1, 2, 6.933, 1, 2, 7.55, 1, 2, 8.183, 1, 2, 8.8, 1, 2, 9.417, 1, 2, 10.05, 1, 2, 10.183, 1, 2, 12.133, 1, 0, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "xiongjiya2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.98, 2, 0.25, -0.98, 2, 11.817, -0.98, 0, 12.133, 0, 2, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.76, 2, 0.25, -2.76, 2, 1.517, -2.76, 2, 2.783, -2.76, 2, 3.667, -2.76, 2, 4.55, -2.76, 2, 5.433, -2.76, 2, 6.317, -2.76, 2, 6.933, -2.76, 2, 7.55, -2.76, 2, 8.183, -2.76, 2, 8.8, -2.76, 2, 9.417, -2.76, 2, 10.05, -2.76, 2, 10.183, -2.76, 2, 11.717, -2.76, 2, 12.133, -2.76, 0, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.951, 2, 0.25, 0.951, 2, 1.517, 0.951, 2, 2.783, 0.951, 2, 3.667, 0.951, 2, 4.55, 0.951, 2, 5.433, 0.951, 2, 6.317, 0.951, 2, 6.933, 0.951, 2, 7.55, 0.951, 2, 8.183, 0.951, 2, 8.8, 0.951, 2, 9.417, 0.951, 1, 9.628, 0.951, 9.839, 0.889, 10.05, 0.756, 1, 10.094, 0.728, 10.139, 0.691, 10.183, 0.661, 1, 10.839, 0.219, 11.494, 0, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.5, 2, 0.25, -5.5, 0, 0.733, -5.8, 0, 1.517, -5.5, 0, 2, -5.8, 0, 2.783, -5.5, 0, 3.117, -5.8, 0, 3.667, -5.5, 0, 4, -5.8, 0, 4.55, -5.5, 0, 4.883, -5.8, 0, 5.433, -5.5, 0, 5.767, -5.8, 0, 6.317, -5.5, 0, 6.55, -5.8, 0, 6.933, -5.5, 0, 7.167, -5.8, 0, 7.55, -5.5, 0, 7.783, -5.8, 0, 8.183, -5.5, 0, 8.417, -5.8, 0, 8.8, -5.5, 0, 9.033, -5.8, 0, 9.417, -5.5, 0, 9.65, -5.8, 0, 10.05, -5.5, 2, 10.183, -5.5, 2, 11.717, -5.5, 2, 12.133, -5.5, 1, 12.139, -5.5, 12.144, 0, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 7.4, 2, 0.25, 7.4, 2, 1.517, 7.4, 2, 2.783, 7.4, 2, 3.667, 7.4, 2, 4.55, 7.4, 2, 5.433, 7.4, 2, 6.317, 7.4, 2, 6.933, 7.4, 2, 7.55, 7.4, 2, 8.183, 7.4, 2, 8.8, 7.4, 2, 9.417, 7.4, 2, 10.05, 7.4, 2, 10.183, 7.4, 2, 11.717, 7.4, 2, 12.133, 7.4, 1, 12.139, 7.4, 12.144, 0, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.13, 2, 0.25, 0.13, 0, 0.533, 0.172, 0, 1.1, 0.002, 1, 1.239, 0.002, 1.378, 0.07, 1.517, 0.13, 1, 1.611, 0.171, 1.706, 0.172, 1.8, 0.172, 0, 2.367, 0.002, 1, 2.506, 0.002, 2.644, 0.053, 2.783, 0.13, 1, 2.844, 0.164, 2.906, 0.172, 2.967, 0.172, 0, 3.367, 0.002, 1, 3.467, 0.002, 3.567, 0.07, 3.667, 0.13, 1, 3.734, 0.17, 3.8, 0.172, 3.867, 0.172, 0, 4.25, 0.002, 1, 4.35, 0.002, 4.45, 0.07, 4.55, 0.13, 1, 4.617, 0.17, 4.683, 0.172, 4.75, 0.172, 0, 5.15, 0.002, 1, 5.244, 0.002, 5.339, 0.072, 5.433, 0.13, 1, 5.5, 0.171, 5.566, 0.172, 5.633, 0.172, 0, 6.033, 0.002, 1, 6.128, 0.002, 6.222, 0.055, 6.317, 0.13, 1, 6.361, 0.165, 6.406, 0.172, 6.45, 0.172, 0, 6.733, 0.002, 1, 6.8, 0.002, 6.866, 0.07, 6.933, 0.13, 1, 6.978, 0.17, 7.022, 0.172, 7.067, 0.172, 0, 7.35, 0.002, 1, 7.417, 0.002, 7.483, 0.075, 7.55, 0.13, 1, 7.6, 0.171, 7.65, 0.172, 7.7, 0.172, 0, 7.967, 0.002, 1, 8.039, 0.002, 8.111, 0.066, 8.183, 0.13, 1, 8.228, 0.169, 8.272, 0.172, 8.317, 0.172, 0, 8.6, 0.002, 1, 8.667, 0.002, 8.733, 0.07, 8.8, 0.13, 1, 8.844, 0.17, 8.889, 0.172, 8.933, 0.172, 0, 9.217, 0.002, 1, 9.284, 0.002, 9.35, 0.07, 9.417, 0.13, 1, 9.461, 0.17, 9.506, 0.172, 9.55, 0.172, 0, 9.833, 0.002, 0, 10.05, 0.13, 2, 10.183, 0.13, 2, 11.717, 0.13, 2, 12.133, 0.13, 0, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamHandRDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2, 2, 0.25, 2, 2, 1.517, 2, 2, 2.783, 2, 2, 3.667, 2, 2, 4.55, 2, 2, 5.433, 2, 2, 6.317, 2, 2, 6.933, 2, 2, 7.55, 2, 2, 8.183, 2, 2, 8.8, 2, 2, 9.417, 2, 2, 10.05, 2, 2, 10.183, 2, 2, 11.717, 2, 2, 12.133, 2, 0, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 1.517, 1, 2, 2.783, 1, 2, 3.667, 1, 2, 4.55, 1, 2, 5.433, 1, 2, 6.317, 1, 2, 6.933, 1, 2, 7.55, 1, 2, 8.183, 1, 2, 8.8, 1, 2, 9.417, 1, 2, 10.05, 1, 2, 10.183, 1, 2, 11.717, 1, 2, 12.133, 1, 0, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 1.517, 1, 2, 2.783, 1, 2, 3.667, 1, 2, 4.55, 1, 2, 5.433, 1, 2, 6.317, 1, 2, 6.933, 1, 2, 7.55, 1, 2, 8.183, 1, 2, 8.8, 1, 2, 9.417, 1, 2, 10.05, 1, 2, 10.183, 1, 2, 11.717, 1, 2, 12.133, 1, 0, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.187, 2, 0.25, 0.187, 2, 1.517, 0.187, 2, 2.783, 0.187, 2, 3.667, 0.187, 2, 4.55, 0.187, 2, 5.433, 0.187, 2, 6.317, 0.187, 2, 6.933, 0.187, 2, 7.55, 0.187, 2, 8.183, 0.187, 2, 8.8, 0.187, 2, 9.417, 0.187, 1, 9.628, 0.187, 9.839, 0.201, 10.05, 0.224, 1, 10.094, 0.229, 10.139, 0.236, 10.183, 0.236, 1, 10.444, 0.238, 10.706, 0.238, 10.967, 0.238, 2, 12.133, 0.238, 0, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.311, 2, 0.25, -0.311, 2, 1.517, -0.311, 2, 2.783, -0.311, 2, 3.667, -0.311, 2, 4.55, -0.311, 2, 5.433, -0.311, 2, 6.317, -0.311, 2, 6.933, -0.311, 2, 7.55, -0.311, 2, 8.183, -0.311, 2, 8.8, -0.311, 2, 9.417, -0.311, 1, 9.628, -0.311, 9.839, -0.314, 10.05, -0.319, 1, 10.094, -0.32, 10.139, -0.321, 10.183, -0.321, 1, 10.444, -0.322, 10.706, -0.322, 10.967, -0.322, 2, 12.133, -0.322, 0, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.933, 2, 0.25, -0.933, 2, 1.517, -0.933, 2, 2.783, -0.933, 2, 3.667, -0.933, 2, 4.55, -0.933, 2, 5.433, -0.933, 2, 6.317, -0.933, 2, 6.933, -0.933, 2, 7.55, -0.933, 2, 8.183, -0.933, 2, 8.8, -0.933, 2, 9.417, -0.933, 1, 9.628, -0.933, 9.839, -0.935, 10.05, -0.941, 1, 10.094, -0.942, 10.139, -0.946, 10.183, -0.946, 1, 10.444, -0.948, 10.706, -0.949, 10.967, -0.952, 1, 11.356, -0.956, 11.744, -0.962, 12.133, -0.962, 0, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.57, 2, 0.25, 0.57, 2, 1.517, 0.57, 2, 2.783, 0.57, 2, 3.667, 0.57, 2, 4.55, 0.57, 2, 5.433, 0.57, 2, 6.317, 0.57, 2, 6.933, 0.57, 2, 7.55, 0.57, 2, 8.183, 0.57, 2, 8.8, 0.57, 2, 9.417, 0.57, 2, 10.05, 0.57, 2, 10.183, 0.57, 2, 11.1, 0.57, 0, 11.717, 0, 2, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 1.517, 1, 2, 2.783, 1, 2, 3.667, 1, 2, 4.55, 1, 2, 5.433, 1, 2, 6.317, 1, 2, 6.933, 1, 2, 7.55, 1, 2, 8.183, 1, 2, 8.8, 1, 2, 9.417, 1, 2, 10.05, 1, 2, 10.183, 1, 2, 11.717, 1, 2, 12.133, 1, 0, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.5, -0.479, 0, 0.767, 0, 0, 1.083, -0.691, 0, 1.517, 0, 0, 1.767, -0.479, 0, 2.033, -0.002, 0, 2.35, -0.691, 0, 2.783, 0, 0, 2.95, -0.479, 0, 3.133, -0.002, 0, 3.367, -0.691, 0, 3.667, 0, 0, 3.833, -0.479, 0, 4.017, -0.002, 0, 4.25, -0.691, 0, 4.55, 0, 0, 4.717, -0.479, 0, 4.917, -0.002, 0, 5.133, -0.691, 0, 5.433, 0, 0, 5.617, -0.479, 0, 5.8, -0.002, 0, 6.017, -0.691, 0, 6.317, 0, 0, 6.433, -0.479, 0, 6.567, -0.002, 0, 6.717, -0.691, 0, 6.933, 0, 0, 7.05, -0.479, 0, 7.183, -0.002, 0, 7.35, -0.691, 0, 7.55, 0, 0, 7.667, -0.479, 0, 7.8, -0.002, 0, 7.967, -0.691, 0, 8.183, 0, 0, 8.3, -0.479, 0, 8.433, -0.002, 0, 8.583, -0.691, 0, 8.8, 0, 0, 8.917, -0.479, 0, 9.05, -0.002, 0, 9.217, -0.691, 0, 9.5, 0, 0, 9.65, -0.479, 0, 9.817, -0.002, 0, 10, -0.691, 0, 10.3, -0.028, 0, 10.583, -1, 1, 10.678, -1, 10.772, -0.989, 10.867, -0.9, 1, 10.9, -0.868, 10.934, 0.656, 10.967, 0.656, 0, 11, 0.586, 0, 11.083, 1, 1, 11.433, 1, 11.783, 0.861, 12.133, 0.57, 1, 12.139, 0.565, 12.144, 0, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 1.517, 1, 2, 2.783, 1, 2, 3.667, 1, 2, 4.55, 1, 2, 5.433, 1, 2, 6.317, 1, 2, 6.933, 1, 2, 7.55, 1, 2, 8.183, 1, 2, 8.8, 1, 2, 9.417, 1, 2, 10.05, 1, 2, 10.183, 1, 2, 12.133, 1, 0, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.517, 0, 2, 2.783, 0, 2, 3.667, 0, 2, 4.55, 0, 2, 5.433, 0, 2, 6.317, 0, 2, 6.933, 0, 2, 7.55, 0, 2, 8.183, 0, 2, 8.8, 0, 2, 9.417, 0, 2, 10.05, 0, 2, 10.183, 0, 2, 10.95, 0, 0, 11.25, 0.52, 0, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "Param103", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -30, 2, 0.25, -30, 2, 1.517, -30, 2, 2.783, -30, 2, 3.667, -30, 2, 4.55, -30, 2, 5.433, -30, 2, 6.317, -30, 2, 6.933, -30, 2, 7.55, -30, 2, 8.183, -30, 2, 8.8, -30, 2, 9.417, -30, 2, 10.05, -30, 1, 10.094, -30, 10.139, -28.122, 10.183, -28.024, 1, 10.539, -27.242, 10.894, -26.822, 11.25, -25.92, 1, 11.356, -25.652, 11.461, 6.558, 11.567, 17.477, 1, 11.667, 27.821, 11.767, 30, 11.867, 30, 2, 12.133, 30, 1, 12.139, 30, 12.144, 0, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.517, 0, 2, 2.783, 0, 2, 3.667, 0, 2, 4.55, 0, 2, 5.433, 0, 2, 6.317, 0, 2, 6.933, 0, 2, 7.55, 0, 2, 8.183, 0, 2, 8.8, 0, 2, 9.417, 0, 2, 10.05, 0, 2, 10.183, 0, 2, 11.233, 0, 1, 11.239, 0, 11.244, 0, 11.25, 0.007, 1, 11.467, 0.628, 11.683, 1, 11.9, 1, 2, 12.133, 1, 2, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.517, 0, 2, 2.783, 0, 2, 3.667, 0, 2, 4.55, 0, 2, 5.433, 0, 2, 6.317, 0, 2, 6.933, 0, 2, 7.55, 0, 2, 8.183, 0, 2, 8.8, 0, 2, 9.417, 0, 2, 10.05, 0, 2, 10.183, 0, 2, 11.6, 0, 1, 11.622, 0, 11.645, -0.019, 11.667, 0.1, 1, 11.689, 0.219, 11.711, 1, 11.733, 1, 0, 11.9, -0.7, 0, 12.067, 0.648, 1, 12.089, 0.648, 12.111, 0.502, 12.133, 0.161, 1, 12.139, 0.076, 12.144, 0, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.517, 0, 2, 2.783, 0, 2, 3.667, 0, 2, 4.55, 0, 2, 5.433, 0, 2, 6.317, 0, 2, 6.933, 0, 2, 7.55, 0, 2, 8.183, 0, 2, 8.8, 0, 2, 9.417, 0, 2, 10.05, 0, 2, 10.183, 0, 2, 11.6, 0, 0, 11.667, -1, 0, 11.817, 1, 0, 11.983, -0.7, 0, 12.133, 0.609, 0, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -21.5, 2, 0.25, -21.5, 2, 1.517, -21.5, 2, 2.783, -21.5, 2, 3.667, -21.5, 2, 4.55, -21.5, 2, 5.433, -21.5, 2, 6.317, -21.5, 2, 6.933, -21.5, 2, 7.55, -21.5, 2, 8.183, -21.5, 2, 8.8, -21.5, 2, 9.417, -21.5, 2, 10.05, -21.5, 2, 10.183, -21.5, 0, 10.85, -15, 0, 11.2, -27, 0, 11.717, -21.5, 2, 12.133, -21.5, 1, 12.139, -21.5, 12.144, 0, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 6.876, 2, 0.25, 6.876, 0, 0.583, 3.313, 0, 0.817, 7.399, 0, 1.05, 3.329, 0, 1.517, 6.876, 0, 1.85, 3.313, 0, 2.083, 7.399, 0, 2.317, 3.329, 0, 2.783, 6.876, 0, 3.017, 3.313, 0, 3.167, 7.399, 0, 3.333, 3.329, 0, 3.667, 6.876, 0, 3.9, 3.313, 0, 4.067, 7.399, 0, 4.217, 3.329, 0, 4.55, 6.876, 0, 4.783, 3.313, 0, 4.95, 7.399, 0, 5.117, 3.329, 0, 5.433, 6.876, 0, 5.667, 3.313, 0, 5.833, 7.399, 0, 6, 3.329, 0, 6.317, 6.876, 0, 6.467, 3.313, 0, 6.583, 7.399, 0, 6.7, 3.329, 0, 6.933, 6.876, 0, 7.1, 3.313, 0, 7.2, 7.399, 0, 7.317, 3.329, 0, 7.55, 6.876, 0, 7.717, 3.313, 0, 7.833, 7.399, 0, 7.95, 3.329, 0, 8.183, 6.876, 0, 8.333, 3.313, 0, 8.45, 7.399, 0, 8.567, 3.329, 0, 8.8, 6.876, 0, 8.95, 3.313, 0, 9.067, 7.399, 0, 9.183, 3.329, 0, 9.417, 6.876, 0, 9.583, 3.313, 0, 9.7, 7.399, 0, 9.817, 3.329, 0, 10.05, 7.761, 1, 10.094, 7.761, 10.139, 8.121, 10.183, 7.608, 1, 10.405, 5.042, 10.628, -19, 10.85, -19, 1, 10.967, -19, 11.083, 17.426, 11.2, 20, 1, 11.222, 20.49, 11.245, 20.146, 11.267, 20.146, 0, 11.717, 6.9, 2, 12.133, 6.9, 0, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.938, 2, 0.25, 5.938, 0, 0.633, 0.9, 0, 1.517, 5.938, 0, 1.9, 0.9, 0, 2.783, 5.938, 0, 3.05, 0.9, 0, 3.667, 5.938, 0, 3.933, 0.9, 0, 4.55, 5.938, 0, 4.817, 0.9, 0, 5.433, 5.938, 0, 5.7, 0.9, 0, 6.317, 5.938, 0, 6.5, 0.9, 0, 6.933, 5.938, 0, 7.133, 0.9, 0, 7.55, 5.938, 0, 7.75, 0.9, 0, 8.183, 5.938, 0, 8.367, 0.9, 0, 8.8, 5.938, 0, 8.983, 0.9, 0, 9.417, 5.938, 0, 9.6, 0.9, 0, 10.05, 4.772, 0, 10.533, -14, 0, 11.017, 19.956, 0, 11.717, 12, 2, 12.133, 12, 0, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 24, 2, 0.25, 24, 2, 12.033, 24, 3, 12.05, 0, 2, 12.083, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -9.45, 2, 0.25, -9.45, 0, 0.683, -8.9, 0, 1.517, -9.45, 0, 1.95, -8.9, 0, 2.783, -9.45, 0, 3.083, -8.9, 0, 3.667, -9.45, 0, 3.967, -8.9, 0, 4.55, -9.45, 0, 4.85, -8.9, 0, 5.433, -9.45, 0, 5.733, -8.9, 0, 6.317, -9.45, 0, 6.533, -8.9, 0, 6.933, -9.45, 0, 7.15, -8.9, 0, 7.55, -9.45, 0, 7.767, -8.9, 0, 8.183, -9.45, 0, 8.383, -8.9, 0, 8.8, -9.45, 0, 9, -8.9, 0, 9.417, -9.45, 0, 9.633, -8.9, 1, 9.772, -8.9, 9.911, -9.715, 10.05, -9.879, 1, 10.094, -9.931, 10.139, -9.902, 10.183, -9.926, 1, 10.239, -9.955, 10.294, -10, 10.35, -10, 0, 11.2, -9.02, 0, 12.133, -10, 1, 12.139, -10, 12.144, 0, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.517, 0, 2, 2.783, 0, 2, 3.667, 0, 2, 4.55, 0, 2, 5.433, 0, 2, 6.317, 0, 2, 6.933, 0, 2, 7.55, 0, 2, 8.183, 0, 2, 8.8, 0, 2, 9.417, 0, 2, 10.05, 0, 2, 10.183, 0, 2, 11.717, 0, 2, 12.133, 0, 1, 12.139, 0, 12.144, -7.795, 12.15, -7.795, 2, 12.6, -7.795, 2, 12.917, -7.795]}, {"Target": "Parameter", "Id": "ParamBodyStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.562, 2, 0.25, 1.562, 0, 0.617, -2.837, 0, 0.917, 0.769, 0, 1.183, -6.03, 0, 1.517, 1.562, 0, 1.883, -2.837, 0, 2.183, 0.769, 0, 2.45, -6.03, 0, 2.783, 1.562, 0, 3.033, -2.837, 0, 3.25, 0.769, 0, 3.433, -6.03, 0, 3.667, 1.562, 0, 3.917, -2.837, 0, 4.133, 0.769, 0, 4.317, -6.03, 0, 4.55, 1.562, 0, 4.8, -2.837, 0, 5.017, 0.769, 0, 5.2, -6.03, 0, 5.433, 1.562, 0, 5.7, -2.837, 0, 5.9, 0.769, 0, 6.083, -6.03, 0, 6.317, 1.562, 0, 6.5, -2.837, 0, 6.633, 0.769, 0, 6.783, -6.03, 0, 6.933, 1.562, 0, 7.117, -2.837, 0, 7.267, 0.769, 0, 7.4, -6.03, 0, 7.55, 1.562, 0, 7.733, -2.837, 0, 7.883, 0.769, 0, 8.017, -6.03, 0, 8.183, 1.562, 0, 8.35, -2.837, 0, 8.5, 0.769, 0, 8.633, -6.03, 0, 8.8, 1.562, 0, 8.983, -2.837, 0, 9.117, 0.769, 0, 9.283, -6.03, 0, 9.5, 1.562, 0, 9.733, -2.837, 0, 9.9, 0.769, 0, 10.083, -6.03, 0, 10.3, 0.94, 0, 10.667, -10, 0, 11.083, 10, 1, 11.161, 10, 11.239, 2.466, 11.317, 2, 1, 11.595, 0.335, 11.872, 0, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.316, 2, 0.25, 0.316, 0, 0.75, 0.4, 0, 1.517, 0.316, 0, 2.017, 0.4, 0, 2.783, 0.316, 0, 3.133, 0.4, 0, 3.667, 0.316, 0, 4.017, 0.4, 0, 4.55, 0.316, 0, 4.9, 0.4, 0, 5.433, 0.316, 0, 5.783, 0.4, 0, 6.317, 0.316, 0, 6.55, 0.4, 0, 6.933, 0.316, 0, 7.183, 0.4, 0, 7.55, 0.316, 0, 7.8, 0.4, 0, 8.183, 0.316, 0, 8.417, 0.4, 0, 8.8, 0.316, 0, 9.033, 0.4, 0, 9.417, 0.316, 0, 9.65, 0.4, 1, 9.783, 0.4, 9.917, 0.348, 10.05, 0.325, 1, 10.094, 0.317, 10.139, 0.328, 10.183, 0.316, 1, 10.833, 0.147, 11.483, 0, 12.133, 0, 2, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 1.517, 1, 2, 2.783, 1, 2, 3.667, 1, 2, 4.55, 1, 2, 5.433, 1, 2, 6.317, 1, 2, 6.933, 1, 2, 7.55, 1, 2, 8.183, 1, 2, 8.8, 1, 2, 9.417, 1, 2, 10.05, 1, 1, 10.75, 0.667, 11.45, 0.333, 12.15, 0, 2, 12.6, 0, 2, 12.917, 0]}, {"Target": "Parameter", "Id": "Physics_AngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -11.633, 2, 0.25, -11.633, 2, 0.267, -11.633, 2, 0.3, -11.633, 0, 0.317, -11.632, 2, 0.433, -11.632, 0, 0.45, -11.633, 2, 0.517, -11.633, 2, 0.533, -11.633, 2, 10.183, -11.633, 0, 10.85, -7.731, 0, 11.25, -16.206, 0, 11.717, -11.04, 0, 12.05, -11.714, 2, 12.067, -11.714, 0, 12.333, 3.834, 0, 12.6, -0.052, 2, 12.917, -0.052]}, {"Target": "Parameter", "Id": "Physics_AngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 3.96, 2, 0.25, 3.96, 0, 0.617, 1.708, 0, 0.867, 4.393, 0, 1.133, 1.71, 0, 1.55, 4.168, 0, 1.883, 1.702, 0, 2.133, 4.389, 0, 2.4, 1.71, 0, 2.8, 4.162, 0, 3.05, 1.854, 0, 3.217, 3.982, 0, 3.417, 1.962, 0, 3.7, 4.108, 0, 3.933, 1.884, 0, 4.117, 4.017, 0, 4.317, 1.994, 0, 4.583, 4.101, 0, 4.817, 1.884, 0, 5, 4.054, 0, 5.2, 1.964, 0, 5.467, 4.088, 0, 5.7, 1.891, 0, 5.883, 4.054, 0, 6.083, 1.964, 0, 6.35, 4.047, 0, 6.5, 2.252, 0, 6.617, 3.656, 0, 6.767, 2.188, 0, 6.967, 3.893, 0, 7.133, 2.25, 0, 7.25, 3.601, 0, 7.383, 2.192, 0, 7.583, 3.898, 0, 7.75, 2.209, 0, 7.867, 3.678, 0, 8.017, 2.202, 0, 8.217, 3.879, 0, 8.367, 2.279, 0, 8.483, 3.685, 0, 8.633, 2.194, 0, 8.833, 3.878, 0, 8.983, 2.28, 0, 9.1, 3.686, 0, 9.25, 2.194, 0, 9.45, 3.892, 0, 9.617, 2.209, 0, 9.733, 3.678, 0, 9.883, 2.242, 0, 10.167, 4.835, 0, 10.867, -11.832, 0, 11.283, 14.509, 0, 11.767, 3.051, 0, 12.133, 4.018, 0, 12.35, -0.526, 0, 12.6, 0.155, 2, 12.917, 0.155]}, {"Target": "Parameter", "Id": "Physics_AngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.267, 0, 2, 0.283, 0, 2, 0.317, 0, 2, 0.333, 0, 2, 0.383, 0, 2, 0.4, 0, 2, 10.183, 0, 0, 10.567, -1.223, 0, 11.083, 4.371, 0, 11.45, -1.976, 0, 11.867, 0.305, 0, 12.15, -16.421, 0, 12.467, 1.927, 0, 12.6, 1.078, 2, 12.917, 1.078]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.005, 2, 0.25, 0.005, 0, 0.467, 3.446, 0, 0.75, -6.095, 0, 1.05, 6.966, 0, 1.4, -6.987, 0, 1.75, 8.587, 0, 2.05, -7.438, 0, 2.333, 5.617, 0, 2.683, -6.007, 0, 2.967, 6.929, 0, 3.167, -2.089, 0, 3.333, 2.353, 0, 3.6, -5.332, 0, 3.85, 6.229, 0, 4.05, -2.021, 0, 4.217, 2.806, 0, 4.483, -5.923, 0, 4.75, 6.351, 0, 4.95, -2.038, 0, 5.1, 2.901, 0, 5.367, -6.117, 0, 5.633, 6.574, 0, 5.833, -1.925, 0, 5.983, 2.871, 0, 6.25, -6.19, 0, 6.467, 5.088, 0, 6.567, 0.454, 0, 6.7, 6.329, 0, 6.933, -6.995, 0, 7.083, 0.829, 0, 7.183, -1.963, 0, 7.333, 7.215, 0, 7.55, -4.753, 0, 7.683, 0.603, 0, 7.817, -4.642, 0, 7.967, 2.353, 0, 8.183, -4.584, 0, 8.317, 0.766, 0, 8.433, -4.769, 0, 8.583, 2.353, 0, 8.8, -4.584, 0, 8.933, 0.766, 0, 9.067, -4.792, 0, 9.217, 2.257, 0, 9.45, -3.958, 0, 9.617, 0.042, 0, 9.733, -2.793, 0, 9.983, -0.093, 0, 10.233, -3.71, 0, 10.567, 10.638, 0, 10.983, -24.58, 0, 11, -24.48, 0, 11.033, -24.727, 0, 11.417, 27.633, 0, 11.833, -19.614, 0, 12.2, 18.474, 0, 12.6, -11.602, 2, 12.917, -11.602]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.998, 2, 0.25, -0.998, 0, 0.383, -2.94, 0, 0.667, 10.026, 0, 0.95, -15.424, 0, 1.25, 17.194, 0, 1.617, -19.887, 0, 1.95, 21.475, 0, 2.25, -19.086, 0, 2.567, 15.623, 0, 2.9, -18.207, 0, 3.15, 14.95, 0, 3.367, -2.067, 0, 3.55, 3.399, 0, 3.8, -13.789, 0, 4.033, 12.075, 0, 4.217, -1.535, 0, 4.417, 5.333, 0, 4.667, -14.332, 0, 4.917, 12.73, 0, 5.117, -1.937, 0, 5.3, 6.116, 0, 5.567, -15.538, 0, 5.817, 12.832, 0, 6, -1.133, 0, 6.183, 6.915, 0, 6.433, -16.398, 0, 6.617, 1.576, 0, 6.667, 0.839, 0, 6.867, 14.528, 0, 7.1, -12.593, 0, 7.2, -6.867, 0, 7.283, -8.756, 0, 7.517, 15.159, 0, 7.7, -6.54, 0, 7.85, 0.687, 0, 7.933, -0.935, 0, 8.15, 5.572, 0, 8.317, -0.766, 0, 8.433, 4.769, 0, 8.583, -2.353, 0, 8.8, 4.584, 0, 8.933, -0.766, 0, 9.067, 4.792, 0, 9.217, -2.257, 0, 9.45, 3.958, 0, 9.617, -0.042, 0, 9.733, 2.793, 0, 9.983, 0.093, 0, 10.233, 3.71, 0, 10.5, -8.55, 0, 10.783, 26.807, 0, 11.1, -30, 2, 11.367, -30, 0, 11.5, 30, 2, 11.767, 30, 0, 11.967, -30, 2, 12.167, -30, 0, 12.35, 30, 2, 12.517, 30, 0, 12.6, 19.491, 2, 12.917, 19.491]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_L", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.086, 2, 0.25, -0.086, 0, 0.317, 0, 0, 0.367, -0.003, 0, 0.7, 1.202, 0, 0.983, -1.923, 0, 1.283, 1.405, 0, 1.65, -1.186, 0, 2, 1.523, 0, 2.25, -1.473, 0, 2.533, 1.013, 0, 2.9, -1.143, 0, 3.15, 1.542, 0, 3.333, -0.663, 0, 3.55, 0.613, 0, 3.817, -1.273, 0, 4.05, 1.474, 0, 4.217, -0.612, 0, 4.433, 0.619, 0, 4.7, -1.275, 0, 4.933, 1.448, 0, 5.117, -0.669, 0, 5.317, 0.683, 0, 5.583, -1.274, 0, 5.817, 1.39, 0, 6, -0.674, 0, 6.2, 0.715, 0, 6.45, -1.339, 0, 6.583, 0.619, 0, 6.7, -0.264, 0, 6.883, 0.92, 0, 7.083, -0.965, 0, 7.2, 0.454, 0, 7.317, -0.606, 0, 7.5, 0.721, 0, 7.7, -0.817, 0, 7.833, 0.731, 0, 7.95, -0.505, 0, 8.133, 0.638, 0, 8.317, -0.919, 0, 8.45, 0.602, 0, 8.567, -0.507, 0, 8.75, 0.748, 0, 8.933, -0.888, 0, 9.067, 0.566, 0, 9.183, -0.566, 0, 9.367, 0.72, 0, 9.567, -0.873, 0, 9.7, 0.687, 0, 9.817, -0.507, 0, 10, 0.727, 0, 10.283, -1.279, 0, 10.717, 2.931, 0, 11.283, -8.645, 0, 11.717, 7.411, 0, 12.133, -4.725, 0, 12.15, -3.126, 0, 12.283, -4.334, 0, 12.6, 2.878, 2, 12.917, 2.878]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_R", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.455, 2, 0.25, -0.455, 0, 0.45, 0.979, 0, 0.733, -1.096, 0, 0.983, 1.252, 0, 1.25, -1.163, 0, 1.617, 1.199, 0, 1.983, -1.698, 0, 2.267, 1.637, 0, 2.55, -0.995, 0, 2.9, 1.075, 0, 3.15, -1.479, 0, 3.333, 0.713, 0, 3.55, -0.642, 0, 3.817, 1.248, 0, 4.05, -1.45, 0, 4.217, 0.635, 0, 4.433, -0.628, 0, 4.7, 1.262, 0, 4.933, -1.44, 0, 5.117, 0.678, 0, 5.317, -0.685, 0, 5.583, 1.268, 0, 5.817, -1.388, 0, 6, 0.678, 0, 6.2, -0.715, 0, 6.45, 1.336, 0, 6.583, -0.62, 0, 6.7, 0.265, 0, 6.883, -0.918, 0, 7.083, 0.965, 0, 7.2, -0.455, 0, 7.317, 0.604, 0, 7.5, -0.721, 0, 7.7, 0.817, 0, 7.833, -0.73, 0, 7.95, 0.505, 0, 8.133, -0.638, 0, 8.317, 0.918, 0, 8.45, -0.602, 0, 8.567, 0.508, 0, 8.75, -0.748, 0, 8.933, 0.888, 0, 9.067, -0.566, 0, 9.183, 0.566, 0, 9.367, -0.72, 0, 9.567, 0.874, 0, 9.7, -0.686, 0, 9.817, 0.507, 0, 10, -0.728, 0, 10.3, 1.372, 0, 10.733, -3.937, 0, 11.3, 13.079, 0, 11.717, -11.36, 0, 12.15, 11.949, 0, 12.467, -7.809, 0, 12.6, -5.481, 2, 12.917, -5.481]}, {"Target": "Parameter", "Id": "L_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 30, 2, 0.25, 30, 2, 0.55, 30, 0, 0.75, 23.095, 0, 0.9, 30, 2, 1.833, 30, 0, 2.017, 22.797, 0, 2.167, 30, 2, 3.033, 30, 0, 3.15, 21.864, 0, 3.283, 30, 2, 3.917, 30, 0, 4.05, 22.048, 0, 4.167, 30, 2, 4.8, 30, 0, 4.933, 22.056, 0, 5.05, 30, 2, 5.683, 30, 0, 5.817, 22.102, 0, 5.933, 30, 2, 6.5, 30, 0, 6.583, 23.064, 0, 6.683, 30, 2, 7.133, 30, 0, 7.2, 23.482, 0, 7.317, 30, 2, 7.75, 30, 0, 7.833, 23.189, 0, 7.933, 30, 2, 8.367, 30, 0, 8.45, 23.465, 0, 8.55, 30, 2, 8.983, 30, 0, 9.067, 23.496, 0, 9.167, 30, 2, 9.617, 30, 0, 9.7, 23.022, 0, 9.817, 30, 2, 10.233, 30, 0, 10.55, 3.4, 0, 10.783, 30, 2, 12.133, 30, 1, 12.139, 30, 12.144, -30, 12.15, -30, 2, 12.25, -30, 0, 12.4, 30, 2, 12.6, 30, 2, 12.917, 30]}, {"Target": "Parameter", "Id": "L_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.105, 2, 0.25, -0.105, 0, 0.3, -0.142, 0, 0.583, 4.716, 0, 0.9, -7.283, 0, 1.15, 4.547, 0, 1.417, -3.264, 0, 1.817, 5.419, 0, 2.167, -7.379, 0, 2.417, 4.612, 0, 2.683, -3.277, 0, 3.067, 8.459, 0, 3.3, -11.689, 0, 3.517, 4.366, 0, 3.733, -2.596, 0, 3.967, 8.424, 0, 4.183, -11.833, 0, 4.4, 4.399, 0, 4.617, -2.54, 0, 4.85, 8.374, 0, 5.067, -11.623, 0, 5.3, 4.737, 0, 5.5, -2.657, 0, 5.733, 8.376, 0, 5.95, -11.577, 0, 6.183, 4.713, 0, 6.383, -2.87, 0, 6.55, 10.716, 0, 6.717, -9.797, 0, 6.95, 0.819, 0, 6.983, 0.752, 0, 7.167, 11.462, 0, 7.35, -10.033, 0, 7.783, 11.252, 0, 7.967, -10.975, 0, 8.2, 1.126, 0, 8.233, 0.931, 0, 8.417, 11.287, 0, 8.583, -10.591, 0, 8.817, 0.907, 0, 8.85, 0.77, 0, 9.033, 11.371, 0, 9.2, -10.582, 0, 9.433, 0.928, 0, 9.45, 0.915, 0, 9.65, 10.64, 0, 9.833, -10.487, 0, 10.033, 1.68, 0, 10.083, 1.371, 0, 10.367, 12.457, 0, 10.8, -22.553, 0, 11.2, 16.569, 0, 11.483, -6.323, 0, 11.75, 1.822, 0, 12.133, -0.128, 1, 12.139, -0.128, 12.144, 30, 12.15, 30, 2, 12.217, 30, 0, 12.3, -30, 2, 12.433, -30, 1, 12.483, -30, 12.533, 30, 12.583, 30, 2, 12.6, 30, 2, 12.917, 30]}, {"Target": "Parameter", "Id": "R_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 30, 2, 0.25, 30, 2, 0.433, 30, 0, 0.6, 25.756, 0, 0.75, 30, 2, 0.883, 30, 0, 1, 28.102, 0, 1.1, 30, 2, 1.7, 30, 0, 1.867, 24.935, 0, 2.033, 30, 2, 2.167, 30, 0, 2.267, 28.259, 0, 2.35, 30, 2, 2.917, 30, 0, 3.033, 24.997, 0, 3.167, 29.307, 0, 3.3, 26.576, 0, 3.4, 30, 2, 3.817, 30, 0, 3.933, 25.515, 0, 4.067, 29.22, 0, 4.183, 26.314, 0, 4.283, 30, 2, 4.717, 30, 0, 4.817, 25.562, 0, 4.95, 29.226, 0, 5.083, 26.625, 0, 5.183, 30, 2, 5.6, 30, 0, 5.7, 25.596, 0, 5.833, 29.213, 0, 5.967, 26.602, 0, 6.067, 30, 2, 6.433, 30, 0, 6.5, 27.996, 0, 6.567, 29.526, 0, 6.683, 24.434, 0, 6.783, 30, 2, 7.083, 30, 0, 7.117, 29.389, 0, 7.15, 30, 2, 7.217, 30, 0, 7.3, 24.968, 0, 7.417, 30, 2, 7.7, 30, 0, 7.75, 29.174, 0, 7.783, 30, 2, 7.833, 30, 0, 7.933, 25.223, 0, 8.033, 30, 2, 8.333, 30, 0, 8.367, 29.564, 0, 8.4, 30, 2, 8.467, 30, 0, 8.55, 25.151, 0, 8.65, 30, 2, 8.95, 30, 0, 8.983, 29.571, 0, 9.017, 30, 2, 9.083, 30, 0, 9.167, 25.177, 0, 9.267, 30, 2, 9.567, 30, 0, 9.6, 28.66, 0, 9.667, 30, 2, 9.7, 30, 0, 9.8, 24.991, 0, 9.917, 30, 2, 10.217, 30, 0, 10.567, -19.645, 0, 10.833, 30, 2, 11.367, 30, 0, 11.533, 0.235, 0, 11.75, 30, 2, 12.133, 30, 1, 12.139, 30, 12.144, -30, 12.15, -30, 2, 12.3, -30, 0, 12.483, 30, 2, 12.6, 30, 2, 12.917, 30]}, {"Target": "Parameter", "Id": "R_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.057, 2, 0.25, 0.057, 0, 0.517, 5.826, 0, 0.783, -5.038, 0, 0.983, 2.078, 0, 1.233, -5.245, 0, 1.733, 6.095, 0, 2.033, -5.644, 0, 2.25, 2.393, 0, 2.5, -5.225, 0, 2.983, 8.569, 0, 3.183, -4.846, 0, 3.3, -1.622, 0, 3.5, -6.326, 0, 3.867, 11.188, 0, 4.083, -5.596, 0, 4.2, -2.336, 0, 4.367, -6.345, 0, 4.75, 11.21, 0, 4.967, -5.612, 0, 5.1, -2.378, 0, 5.267, -6.259, 0, 5.633, 11.37, 0, 5.85, -5.591, 0, 5.983, -2.437, 0, 6.15, -6.279, 0, 6.467, 12.97, 0, 6.85, -12.201, 0, 7.117, 14.057, 0, 7.483, -12.969, 0, 7.733, 13.962, 0, 8.1, -12.483, 0, 8.35, 13.765, 0, 8.717, -12.704, 0, 8.967, 13.711, 0, 9.333, -12.702, 0, 9.6, 14.445, 0, 9.967, -11.572, 0, 10.367, 26.238, 0, 10.717, -30, 2, 11.05, -30, 1, 11.094, -30, 11.139, 30, 11.183, 30, 2, 11.417, 30, 0, 11.617, -30, 2, 11.7, -30, 0, 12.033, 12.385, 0, 12.133, 8.958, 1, 12.139, 8.958, 12.144, 30, 12.15, 30, 2, 12.25, 30, 0, 12.333, -30, 2, 12.517, -30, 0, 12.6, -10.243, 2, 12.917, -10.243]}, {"Target": "Parameter", "Id": "Jewelry_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.195, 2, 0.25, 0.195, 0, 0.767, -0.478, 0, 1.033, 0.566, 0, 1.417, -0.466, 0, 1.783, 0.525, 0, 2.083, -0.56, 0, 2.35, 0.337, 0, 2.7, -0.278, 0, 3, 0.413, 0, 3.167, -0.287, 0, 3.333, 0.138, 0, 3.6, -0.256, 0, 3.883, 0.424, 0, 4.067, -0.317, 0, 4.217, 0.127, 0, 4.483, -0.239, 0, 4.767, 0.419, 0, 4.95, -0.327, 0, 5.117, 0.137, 0, 5.383, -0.235, 0, 5.65, 0.401, 0, 5.833, -0.322, 0, 6, 0.151, 0, 6.267, -0.24, 0, 6.467, 0.371, 0, 6.583, -0.144, 0, 6.7, 0.256, 0, 6.917, -0.335, 0, 7.1, 0.245, 0, 7.2, -0.184, 0, 7.317, 0.308, 0, 7.55, -0.269, 0, 7.717, 0.219, 0, 7.833, -0.243, 0, 7.95, 0.262, 0, 8.167, -0.241, 0, 8.333, 0.242, 0, 8.45, -0.232, 0, 8.567, 0.257, 0, 8.783, -0.254, 0, 8.95, 0.242, 0, 9.067, -0.224, 0, 9.183, 0.266, 0, 9.4, -0.256, 0, 9.583, 0.247, 0, 9.7, -0.23, 0, 9.817, 0.256, 0, 10.033, -0.318, 0, 10.45, 0.704, 0, 11.067, -1.651, 0, 11.45, 2.086, 0, 11.867, -1.58, 0, 12.133, 0.379, 0, 12.15, -1.087, 0, 12.45, 1.122, 0, 12.6, 0.579, 2, 12.917, 0.579]}, {"Target": "Parameter", "Id": "Jewelry_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.432, 2, 0.25, 0.432, 0, 0.3, 0.535, 0, 0.533, 0.209, 0, 0.683, 0.343, 0, 0.95, -1.198, 0, 1.267, 1.285, 0, 1.617, -1.222, 0, 1.967, 1.399, 0, 2.267, -1.317, 0, 2.567, 0.875, 0, 2.9, -0.881, 0, 3.15, 1.018, 0, 3.35, -0.315, 0, 3.55, 0.187, 0, 3.8, -0.695, 0, 4.05, 0.98, 0, 4.233, -0.35, 0, 4.45, 0.155, 0, 4.683, -0.655, 0, 4.933, 0.974, 0, 5.133, -0.431, 0, 5.333, 0.203, 0, 5.583, -0.633, 0, 5.817, 0.923, 0, 6, -0.424, 0, 6.217, 0.233, 0, 6.433, -0.673, 0, 6.583, 0.417, 0, 6.7, 0.033, 0, 6.85, 0.495, 0, 7.083, -0.715, 0, 7.2, 0.115, 0, 7.3, -0.212, 0, 7.5, 0.559, 0, 7.7, -0.546, 0, 7.833, 0.214, 0, 7.95, -0.272, 0, 8.133, 0.435, 0, 8.317, -0.49, 0, 8.45, 0.242, 0, 8.567, -0.252, 0, 8.75, 0.433, 0, 8.933, -0.517, 0, 9.067, 0.225, 0, 9.167, -0.249, 0, 9.367, 0.453, 0, 9.567, -0.534, 0, 9.7, 0.273, 0, 9.817, -0.22, 0, 9.983, 0.442, 0, 10.267, -0.815, 0, 10.667, 1.901, 0, 11.267, -4.425, 0, 11.667, 4.438, 0, 12.067, -3.231, 0, 12.167, -1.07, 0, 12.317, -1.574, 0, 12.6, 1.652, 2, 12.917, 1.652]}, {"Target": "Parameter", "Id": "Jewelry_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.044, 2, 0.25, -0.044, 0, 0.383, 0.463, 0, 0.633, -0.073, 0, 0.85, 0.458, 0, 1.117, -1.179, 0, 1.417, 1.42, 0, 1.767, -1.409, 0, 2.117, 1.599, 0, 2.417, -1.566, 0, 2.717, 1.244, 0, 3.05, -1.188, 0, 3.3, 1.02, 0, 3.533, -0.384, 0, 3.733, 0.209, 0, 3.967, -0.665, 0, 4.183, 0.815, 0, 4.417, -0.363, 0, 4.617, 0.206, 0, 4.85, -0.637, 0, 5.067, 0.827, 0, 5.3, -0.44, 0, 5.517, 0.268, 0, 5.733, -0.62, 0, 5.95, 0.766, 0, 6.183, -0.413, 0, 6.383, 0.271, 0, 6.567, -0.423, 0, 6.983, 0.488, 0, 7.2, -0.522, 0, 7.617, 0.571, 0, 7.833, -0.402, 0, 8.25, 0.431, 0, 8.45, -0.29, 0, 8.583, -0.131, 0, 8.65, -0.149, 0, 8.867, 0.421, 0, 9.067, -0.302, 0, 9.2, -0.144, 0, 9.25, -0.155, 0, 9.5, 0.444, 0, 9.7, -0.361, 0, 10.133, 0.508, 0, 10.45, -1.106, 0, 10.833, 1.308, 0, 11.433, -3.362, 0, 11.8, 3.782, 0, 12.15, -2.719, 0, 12.383, 0.07, 0, 12.55, -0.562, 0, 12.6, -0.397, 2, 12.917, -0.397]}, {"Target": "Parameter", "Id": "Physics_Joint_5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.006, 2, 0.25, 0.006, 0, 0.5, 0.378, 0, 0.833, -0.807, 0, 1.133, 1.202, 0, 1.467, -1.371, 0, 1.817, 1.282, 0, 2.15, -1.073, 0, 2.433, 0.93, 0, 2.733, -1.018, 0, 3.017, 1.01, 0, 3.25, -0.384, 0, 3.417, 0.441, 0, 3.633, -1.139, 0, 3.917, 1.028, 0, 4.133, -0.277, 0, 4.3, 0.458, 0, 4.517, -1.215, 0, 4.8, 1.047, 0, 5.033, -0.244, 0, 5.183, 0.464, 0, 5.4, -1.246, 0, 5.7, 1.091, 0, 5.917, -0.226, 0, 6.05, 0.428, 0, 6.283, -1.272, 0, 6.517, 0.798, 0, 6.633, 0.323, 0, 6.767, 1.086, 0, 6.95, -1.217, 0, 7.133, -0.002, 0, 7.233, -0.214, 0, 7.4, 1.222, 0, 7.567, -0.651, 0, 7.733, 0.165, 0, 7.867, -0.44, 0, 8.017, 0.892, 0, 8.183, -0.796, 0, 8.35, 0.323, 0, 8.483, -0.238, 0, 8.617, 0.948, 0, 8.8, -0.907, 0, 8.983, 0.262, 0, 9.1, -0.216, 0, 9.267, 0.978, 0, 9.483, -1.048, 0, 9.767, 0.544, 0, 9.9, 0.002, 0, 10.067, 0.722, 0, 10.283, -1.095, 0, 10.6, 1.426, 0, 10.967, -2.599, 0, 11.317, 2.985, 0, 11.75, -1.859, 0, 12.133, 1.113, 0, 12.15, 0.744, 0, 12.183, 0.771, 0, 12.6, -0.489, 2, 12.917, -0.489]}, {"Target": "Parameter", "Id": "Physics_Joint_4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.043, 2, 0.25, 0.043, 0, 0.4, -0.105, 0, 0.7, 1.194, 0, 1.033, -2.099, 0, 1.333, 2.862, 0, 1.65, -3.197, 0, 2, 2.982, 0, 2.333, -2.536, 0, 2.633, 2.289, 0, 2.933, -2.626, 0, 3.2, 2.177, 0, 3.417, -0.546, 0, 3.583, 0.98, 0, 3.833, -2.366, 0, 4.1, 1.986, 0, 4.3, -0.286, 0, 4.467, 1.094, 0, 4.717, -2.517, 0, 4.983, 2.011, 0, 5.183, -0.24, 0, 5.35, 1.124, 0, 5.6, -2.572, 0, 5.883, 2.101, 0, 6.067, -0.095, 0, 6.233, 1.064, 0, 6.467, -2.599, 0, 6.667, 0.42, 0, 6.717, 0.385, 0, 6.9, 2.608, 0, 7.133, -1.838, 0, 7.267, -0.883, 0, 7.35, -1.116, 0, 7.533, 2.227, 0, 7.75, -0.791, 0, 7.867, -0.149, 0, 7.983, -1.078, 0, 8.167, 1.613, 0, 8.35, -1.1, 0, 8.5, -0.076, 0, 8.6, -0.732, 0, 8.767, 1.86, 0, 8.983, -1.359, 0, 9.117, -0.286, 0, 9.217, -0.67, 0, 9.433, 2.106, 0, 9.7, -2.037, 0, 9.917, 0.8, 0, 10.05, 0.194, 0, 10.217, 1.651, 0, 10.483, -2.281, 0, 10.783, 3.691, 0, 11.133, -5.66, 0, 11.5, 4.953, 0, 11.95, -3.241, 0, 12.35, 1.581, 0, 12.6, 0.087, 2, 12.917, 0.087]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.583, 2, 0.25, 0.583, 0, 0.767, -1.435, 0, 1.033, 1.698, 0, 1.417, -1.397, 0, 1.783, 1.575, 0, 2.083, -1.681, 0, 2.35, 1.01, 0, 2.7, -0.835, 0, 3, 1.241, 0, 3.167, -0.862, 0, 3.333, 0.415, 0, 3.6, -0.769, 0, 3.883, 1.274, 0, 4.067, -0.95, 0, 4.217, 0.38, 0, 4.483, -0.716, 0, 4.767, 1.256, 0, 4.95, -0.981, 0, 5.117, 0.412, 0, 5.383, -0.705, 0, 5.65, 1.204, 0, 5.833, -0.965, 0, 6, 0.453, 0, 6.267, -0.719, 0, 6.467, 1.113, 0, 6.583, -0.433, 0, 6.7, 0.768, 0, 6.917, -1.004, 0, 7.1, 0.735, 0, 7.2, -0.553, 0, 7.317, 0.925, 0, 7.55, -0.806, 0, 7.717, 0.657, 0, 7.833, -0.73, 0, 7.95, 0.786, 0, 8.167, -0.724, 0, 8.333, 0.725, 0, 8.45, -0.697, 0, 8.567, 0.771, 0, 8.783, -0.763, 0, 8.95, 0.726, 0, 9.067, -0.672, 0, 9.183, 0.798, 0, 9.4, -0.769, 0, 9.583, 0.742, 0, 9.7, -0.689, 0, 9.817, 0.768, 0, 10.033, -0.953, 0, 10.45, 2.11, 0, 11.067, -4.954, 0, 11.45, 6.257, 0, 11.867, -4.74, 0, 12.133, 1.136, 0, 12.15, -3.26, 0, 12.45, 3.366, 0, 12.6, 1.737, 2, 12.917, 1.737]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.295, 2, 0.25, 1.295, 0, 0.3, 1.605, 0, 0.533, 0.626, 0, 0.683, 1.03, 0, 0.95, -3.595, 0, 1.267, 3.855, 0, 1.617, -3.667, 0, 1.967, 4.198, 0, 2.267, -3.951, 0, 2.567, 2.625, 0, 2.9, -2.643, 0, 3.15, 3.054, 0, 3.35, -0.946, 0, 3.55, 0.561, 0, 3.8, -2.084, 0, 4.05, 2.941, 0, 4.233, -1.05, 0, 4.45, 0.466, 0, 4.683, -1.964, 0, 4.933, 2.923, 0, 5.133, -1.293, 0, 5.333, 0.61, 0, 5.583, -1.898, 0, 5.817, 2.769, 0, 6, -1.272, 0, 6.217, 0.698, 0, 6.433, -2.018, 0, 6.583, 1.252, 0, 6.7, 0.099, 0, 6.85, 1.484, 0, 7.083, -2.146, 0, 7.2, 0.345, 0, 7.3, -0.637, 0, 7.5, 1.677, 0, 7.7, -1.637, 0, 7.833, 0.643, 0, 7.95, -0.816, 0, 8.133, 1.304, 0, 8.317, -1.47, 0, 8.45, 0.725, 0, 8.567, -0.756, 0, 8.75, 1.299, 0, 8.933, -1.552, 0, 9.067, 0.675, 0, 9.167, -0.748, 0, 9.367, 1.358, 0, 9.567, -1.603, 0, 9.7, 0.819, 0, 9.817, -0.66, 0, 9.983, 1.325, 0, 10.267, -2.445, 0, 10.667, 5.702, 0, 11.267, -13.274, 0, 11.667, 13.314, 0, 12.067, -9.693, 0, 12.167, -3.211, 0, 12.317, -4.723, 0, 12.6, 4.956, 2, 12.917, 4.956]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.131, 2, 0.25, -0.131, 0, 0.383, 1.388, 0, 0.633, -0.22, 0, 0.85, 1.373, 0, 1.117, -3.536, 0, 1.417, 4.26, 0, 1.767, -4.227, 0, 2.117, 4.797, 0, 2.417, -4.697, 0, 2.717, 3.733, 0, 3.05, -3.564, 0, 3.3, 3.061, 0, 3.533, -1.152, 0, 3.733, 0.628, 0, 3.967, -1.994, 0, 4.183, 2.446, 0, 4.417, -1.088, 0, 4.617, 0.617, 0, 4.85, -1.912, 0, 5.067, 2.482, 0, 5.3, -1.32, 0, 5.517, 0.804, 0, 5.733, -1.861, 0, 5.95, 2.297, 0, 6.183, -1.241, 0, 6.383, 0.812, 0, 6.567, -1.27, 0, 6.983, 1.464, 0, 7.2, -1.567, 0, 7.617, 1.713, 0, 7.833, -1.206, 0, 8.25, 1.292, 0, 8.45, -0.871, 0, 8.583, -0.394, 0, 8.65, -0.449, 0, 8.867, 1.262, 0, 9.067, -0.906, 0, 9.2, -0.431, 0, 9.25, -0.465, 0, 9.5, 1.333, 0, 9.7, -1.084, 0, 10.133, 1.524, 0, 10.45, -3.319, 0, 10.833, 3.923, 0, 11.433, -10.087, 0, 11.8, 11.347, 0, 12.15, -8.158, 0, 12.383, 0.209, 0, 12.55, -1.687, 0, 12.6, -1.19, 2, 12.917, -1.19]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.081, 2, 0.25, -2.081, 0, 0.467, 2.299, 0, 0.75, -0.581, 0, 0.967, 1.128, 0, 1.233, -3.763, 0, 1.533, 5.473, 0, 1.867, -5.999, 0, 2.217, 6.517, 0, 2.533, -6.487, 0, 2.833, 5.59, 0, 3.15, -5.115, 0, 3.433, 4.115, 0, 3.7, -1.774, 0, 3.9, -0.002, 0, 4.083, -1.387, 0, 4.317, 2.413, 0, 4.583, -1.308, 0, 4.767, 0.117, 0, 4.95, -1.433, 0, 5.2, 2.458, 0, 5.45, -1.525, 0, 5.667, 0.396, 0, 5.85, -1.351, 0, 6.083, 2.187, 0, 6.333, -1.338, 0, 6.533, 0.286, 0, 6.733, -0.747, 0, 7.067, 1.616, 0, 7.367, -1.926, 0, 7.717, 2.095, 0, 8, -1.608, 0, 8.35, 1.536, 0, 8.65, -1.137, 0, 8.983, 1.385, 0, 9.267, -1.136, 0, 9.6, 1.472, 0, 9.867, -1.227, 0, 10.233, 1.767, 0, 10.567, -4.041, 0, 10.933, 5.367, 0, 11.533, -10.893, 0, 11.9, 14.796, 0, 12.25, -11.547, 0, 12.55, 2.365, 0, 12.6, 1.858, 2, 12.917, 1.858]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.37, 2, 0.25, -5.37, 0, 0.533, 3.945, 0, 0.867, -1.414, 0, 1.1, 0.997, 0, 1.35, -3.961, 0, 1.65, 6.77, 0, 1.983, -8.201, 0, 2.317, 8.937, 0, 2.65, -9.015, 0, 2.967, 8.159, 0, 3.267, -7.375, 0, 3.567, 6.002, 0, 3.867, -3.176, 0, 4.1, -0.224, 0, 4.183, -0.339, 0, 4.45, 2.385, 0, 4.717, -1.763, 0, 4.933, -0.264, 0, 5.067, -0.804, 0, 5.333, 2.502, 0, 5.6, -1.962, 0, 5.817, 0.13, 0, 5.983, -0.676, 0, 6.217, 2.142, 0, 6.483, -1.647, 0, 6.717, 0.086, 0, 6.883, -0.373, 0, 7.183, 1.774, 0, 7.483, -2.528, 0, 7.817, 2.795, 0, 8.133, -2.473, 0, 8.467, 2.181, 0, 8.767, -1.827, 0, 9.083, 1.833, 0, 9.4, -1.728, 0, 9.7, 1.889, 0, 10, -1.79, 0, 10.333, 2.287, 0, 10.667, -4.961, 0, 11.033, 7.158, 0, 11.617, -11.318, 0, 11.983, 18.064, 0, 12.35, -15.879, 0, 12.6, 2.604, 2, 12.917, 2.604]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -11.489, 2, 0.25, -11.489, 0, 0.633, 6.781, 0, 0.967, -2.974, 0, 1.217, 1.212, 0, 1.467, -4.1, 0, 1.767, 8.14, 0, 2.1, -10.813, 0, 2.433, 12.173, 0, 2.75, -12.427, 0, 3.083, 11.638, 0, 3.383, -10.557, 0, 3.7, 8.838, 0, 4, -5.587, 0, 4.533, 2.273, 0, 4.85, -2.372, 0, 5.433, 2.514, 0, 5.733, -2.579, 0, 6, 0.333, 0, 6.1, 0.166, 0, 6.333, 2.011, 0, 6.617, -2.116, 0, 6.9, 0.398, 0, 7.033, 0.132, 0, 7.283, 1.764, 0, 7.6, -3.153, 0, 7.933, 3.755, 0, 8.25, -3.614, 0, 8.583, 3.207, 0, 8.883, -2.781, 0, 9.2, 2.609, 0, 9.517, -2.516, 0, 9.817, 2.584, 0, 10.133, -2.546, 0, 10.433, 3.038, 0, 10.767, -6.131, 0, 11.133, 9.393, 0, 11.683, -11.372, 0, 12.067, 20.864, 0, 12.45, -20.692, 0, 12.6, -8.26, 2, 12.917, -8.26]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -6.355, 2, 0.25, -6.355, 0, 0.35, -16.969, 0, 0.717, 11.07, 0, 1.067, -5.67, 0, 1.35, 2.158, 0, 1.6, -4.268, 0, 1.883, 9.53, 0, 2.2, -13.793, 0, 2.533, 16.077, 0, 2.867, -16.863, 0, 3.183, 16.126, 0, 3.5, -14.878, 0, 3.817, 12.861, 0, 4.133, -9.18, 0, 4.483, 4.281, 0, 4.95, -3.07, 0, 5.533, 2.46, 0, 5.85, -3.297, 0, 6.2, 1.235, 0, 6.233, 1.223, 2, 6.25, 1.223, 0, 6.433, 1.77, 0, 6.75, -2.651, 0, 7.067, 1.065, 0, 7.2, 0.713, 0, 7.4, 1.553, 0, 7.717, -3.743, 0, 8.05, 4.93, 0, 8.367, -5.106, 0, 8.683, 4.696, 0, 9.017, -4.153, 0, 9.317, 3.798, 0, 9.633, -3.628, 0, 9.933, 3.613, 0, 10.25, -3.585, 0, 10.55, 4.077, 0, 10.883, -7.631, 0, 11.217, 12.157, 0, 11.717, -11.526, 0, 12.133, 22.937, 0, 12.533, -25.056, 0, 12.6, -21.453, 2, 12.917, -21.453]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.417, 1, 0, 3.25, 0, 0, 4.417, 1, 0, 6.25, 0, 0, 7.417, 1, 0, 9.25, 0, 0, 10.417, 1, 0, 12.25, 0, 0, 12.6, 0.216, 2, 12.917, 0.216]}, {"Target": "Parameter", "Id": "fenshangxuanzhuang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 4.367, 1.648, 8.483, 3.297, 12.6, 4.945, 2, 12.917, 4.945]}, {"Target": "Parameter", "Id": "ParamSad2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 12.917, 1]}, {"Target": "Parameter", "Id": "ParamCRY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.2, 0, 12.917, -5.2]}, {"Target": "Parameter", "Id": "Position_Coordinates", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 12.917, 1]}, {"Target": "Parameter", "Id": "Man_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 12.917, 1]}, {"Target": "Parameter", "Id": "Man_TMD2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "BG_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 12.917, 1]}, {"Target": "Parameter", "Id": "FG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "BG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "BG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "BG_White4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "BG_White5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "BG_White2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "All_Size3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "All_Size4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "tuerzuo3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "faqiehuan", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "faqiehuan2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "faqiehuan4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "faqiehuan3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "tuerzuo4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Mail_Change", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 12.917, 1]}, {"Target": "Parameter", "Id": "Mail_Transparency2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "dafengshangzhuanchang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "tongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamTongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "EyeFrame_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamBrowY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamSigh", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamDarken", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamCry", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_B", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_C", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "MB_Unidirectional_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "huqi1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "huqi2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamShoulderLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamArmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 12.917, 1]}, {"Target": "Parameter", "Id": "ParamForearmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamArmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamArmL_ForearmL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamForearmL_HandL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamHandLSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "xiongjiya", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "xiongjiya5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "xiongjiya3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "xiongjiya4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamShoulderRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamArmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 12.917, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamArmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamArmR_ForearmR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamForearm<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 12.917, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_HandR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamHandRSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 12.917, 0.6]}, {"Target": "Parameter", "Id": "Change_idle_physics23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "taiqiugundong", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -22.5, 0, 12.917, -22.5]}, {"Target": "Parameter", "Id": "Change_idle_physics32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamLegL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 12.917, 1]}, {"Target": "Parameter", "Id": "ParamLegLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamLegLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamCrusLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamCrusLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>oodLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamFoodLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamFoodLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamLegR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 12.917, 1]}, {"Target": "Parameter", "Id": "ParamLegRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamLegRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamCrusRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamCrusRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamCrus<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>ood<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamFoodRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamFoodRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Physics_Provisional", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "physics_Add_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "physics_Add_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamBodyStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Physics_Body_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Physics_Body_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Physics_breast", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamBreath2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "touch_drag1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "touch_drag2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "touch_drag3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "touch_drag4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "touch_drag5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "touch_drag6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "touch_drag7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "touch_drag8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "touch_drag9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>pian<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh654", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh655", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh657", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh656", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh658", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh659", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh660", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 12.917, 0]}], "UserData": [{"Time": 12.417, "Value": ""}]}