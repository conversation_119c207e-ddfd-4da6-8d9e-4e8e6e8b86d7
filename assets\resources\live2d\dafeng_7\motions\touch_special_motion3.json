{"Version": 3, "Meta": {"Duration": 8.667, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 304, "TotalSegmentCount": 1569, "TotalPointCount": 1765, "UserDataCount": 2, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 5.833, 0, 0, 6.75, 2.7, 0, 7.167, 2.64, 0, 7.75, 2.7, 0, 7.767, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 0.589, 0, 0.928, -0.438, 1.267, -0.717, 1, 1.956, -1.284, 2.644, -1.791, 3.333, -2.13, 1, 3.605, -2.264, 3.878, -2.247, 4.15, -2.247, 0, 4.433, -2.035, 0, 4.733, -2.25, 0, 5.167, -1.916, 0, 5.583, -2.421, 0, 5.833, -2.099, 1, 5.861, -2.099, 5.889, -7.835, 5.917, -8.239, 1, 6.028, -9.856, 6.139, -10.056, 6.25, -12.259, 1, 6.417, -15.564, 6.583, -21.199, 6.75, -21.199, 0, 7.167, -21.01, 0, 7.75, -21.199, 1, 7.756, -21.199, 7.761, 0, 7.767, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 1.278, 0, 2.305, 5.204, 3.333, 5.4, 1, 4.166, 5.559, 5, 5.51, 5.833, 5.51, 2, 7.75, 5.51, 0, 7.767, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 3.333, 2.704, 0, 5.583, 2.16, 2, 5.833, 2.16, 0, 5.917, 5.943, 1, 6.528, 5.943, 7.139, 5.942, 7.75, 5.94, 1, 7.756, 5.94, 7.761, 0, 7.767, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "FG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 7.05, 0, 0, 7.75, 1, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "BG_White4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 4.3, 1, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "BG_White3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 3.917, 0.3, 2, 7.583, 0.3, 0, 7.95, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "All_Size2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 3.917, -12.72, 2, 5.55, -12.72, 2, 7.583, -12.72, 0, 7.95, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "All_Size4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 3.917, 30, 2, 7.733, 30, 0, 7.817, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.45, -0.7, 0, 0.75, 0.7, 0, 0.917, -0.1, 0, 1.217, 0.7, 1, 1.55, 0.7, 1.884, 0.679, 2.217, 0.6, 1, 2.278, 0.586, 2.339, -0.1, 2.4, -0.1, 0, 2.783, 0.5, 0, 2.967, -0.6, 0, 3.417, 1, 0, 4.25, 0.1, 0, 5.633, 0.8, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY_SD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.333, 0, 0, 0.45, 1, 0, 0.567, 0, 0, 0.75, 1, 0, 0.867, 0, 0, 1.1, 1, 0, 1.267, 0, 0, 1.45, 1, 0, 1.683, 0, 2, 2.35, 0, 0, 2.467, 1, 0, 2.583, 0, 0, 2.767, 1, 0, 2.883, 0, 0, 3.117, 1, 0, 3.283, 0, 0, 3.467, 1, 0, 3.583, 0, 2, 4.05, 0, 0, 4.167, 1, 0, 4.367, 0, 0, 4.55, 1, 0, 4.667, 0, 0, 4.817, 1, 0, 5.217, 0, 2, 5.433, 0, 0, 5.517, 0.7, 0, 5.65, 0, 2, 6.05, 0, 0, 6.167, 1, 0, 6.283, 0, 0, 6.467, 1, 0, 6.583, 0, 0, 6.817, 1, 0, 6.983, 0, 0, 7.167, 1, 0, 7.283, 0, 0, 7.467, 1, 0, 7.583, 0, 0, 7.767, 1, 0, 7.9, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 0.683, 1, 0, 0.817, 0, 0, 1, 1.3, 0, 1.083, 1, 2, 5.767, 1, 0, 5.9, 0, 0, 6.083, 1.3, 1, 6.111, 1.3, 6.139, 1.033, 6.167, 1, 1, 6.75, 0.307, 7.334, 0, 7.917, 0, 0, 8.417, 1, 2, 8.667, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.25, 1, 2, 0.683, 1, 0, 0.817, 0, 0, 1, 1.3, 0, 1.083, 1, 2, 5.767, 1, 0, 5.9, 0, 0, 6.083, 1.3, 1, 6.111, 1.3, 6.139, 1.033, 6.167, 1, 1, 6.75, 0.307, 7.334, 0, 7.917, 0, 0, 8.417, 1, 2, 8.667, 1]}, {"Target": "Parameter", "Id": "EyeFrame_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 3.317, 0, 0, 4.583, -1, 2, 6.183, -1, 0, 7.317, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamBrowAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 3.317, 0, 0, 4.583, 0.4, 2, 6.183, 0.4, 0, 7.317, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.683, 0, 0, 0.75, 1, 2, 0.9, 1, 0, 0.917, -1, 2, 1.05, -1, 0, 1.35, 0.244, 0, 1.667, -0.068, 0, 1.967, 0.019, 0, 2.267, -0.005, 2, 2.283, -0.005, 0, 2.583, 0.002, 2, 2.6, 0.002, 0, 2.833, 0, 2, 2.85, 0, 2, 2.867, 0, 2, 2.95, 0, 2, 2.967, 0, 2, 2.983, 0, 2, 3, 0, 2, 3.017, 0, 2, 3.033, 0, 2, 3.067, 0, 2, 3.083, 0, 2, 3.117, 0, 2, 3.133, 0, 2, 3.317, 0, 2, 3.333, 0, 2, 5.767, 0, 0, 5.833, 1, 2, 5.983, 1, 0, 6, -1, 2, 6.133, -1, 0, 6.417, 0.452, 0, 6.733, 0.029, 0, 7, 0.106, 0, 8.183, -0.715, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeRsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.683, 0, 0, 0.75, 1, 2, 0.9, 1, 0, 0.917, -1, 2, 1.05, -1, 0, 1.35, 0.244, 0, 1.667, -0.068, 0, 1.967, 0.019, 0, 2.267, -0.005, 2, 2.283, -0.005, 0, 2.583, 0.002, 2, 2.6, 0.002, 0, 2.833, 0, 2, 2.85, 0, 2, 2.867, 0, 2, 2.95, 0, 2, 2.967, 0, 2, 2.983, 0, 2, 3, 0, 2, 3.017, 0, 2, 3.033, 0, 2, 3.067, 0, 2, 3.083, 0, 2, 3.117, 0, 2, 3.133, 0, 2, 3.317, 0, 2, 3.333, 0, 2, 5.767, 0, 0, 5.833, 1, 2, 5.983, 1, 0, 6, -1, 2, 6.133, -1, 0, 6.417, 0.452, 0, 6.733, 0.029, 0, 7, 0.106, 0, 8.183, -0.715, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeLGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.683, 0, 0, 0.75, -0.305, 0, 0.8, -0.2, 0, 0.9, -1, 2, 0.917, -1, 0, 0.933, 1, 2, 0.967, 1, 0, 1, 0.831, 2, 1.017, 0.831, 0, 1.05, 0.863, 0, 1.3, -0.314, 0, 1.567, 0.153, 0, 1.85, -0.069, 0, 2.15, 0.028, 0, 2.45, -0.01, 0, 2.733, 0.004, 2, 2.767, 0.004, 0, 3.017, -0.001, 2, 3.033, -0.001, 2, 3.05, -0.001, 2, 3.083, -0.001, 0, 3.3, 0, 2, 3.317, 0, 2, 3.333, 0, 2, 3.4, 0, 2, 3.417, 0, 2, 3.45, 0, 2, 3.467, 0, 2, 3.483, 0, 2, 3.5, 0, 2, 3.517, 0, 2, 3.533, 0, 2, 3.567, 0, 2, 3.583, 0, 2, 3.783, 0, 2, 3.8, 0, 2, 5.767, 0, 0, 5.833, -0.305, 0, 5.883, -0.2, 0, 5.983, -1, 2, 6, -1, 0, 6.017, 1, 2, 6.05, 1, 0, 6.1, 0.826, 0, 6.117, 0.846, 0, 6.367, -0.307, 0, 6.633, 0.171, 0, 6.917, -0.077, 0, 7.217, 0.035, 0, 7.517, -0.01, 0, 7.817, 0.007, 0, 7.917, 0.005, 0, 8.083, 0.154, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeRGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.683, 0, 0, 0.75, -0.305, 0, 0.8, -0.2, 0, 0.9, -1, 2, 0.917, -1, 0, 0.933, 1, 2, 0.967, 1, 0, 1, 0.831, 2, 1.017, 0.831, 0, 1.05, 0.863, 0, 1.3, -0.314, 0, 1.567, 0.153, 0, 1.85, -0.069, 0, 2.15, 0.028, 0, 2.45, -0.01, 0, 2.733, 0.004, 2, 2.767, 0.004, 0, 3.017, -0.001, 2, 3.033, -0.001, 2, 3.05, -0.001, 2, 3.083, -0.001, 0, 3.3, 0, 2, 3.317, 0, 2, 3.333, 0, 2, 3.4, 0, 2, 3.417, 0, 2, 3.45, 0, 2, 3.467, 0, 2, 3.483, 0, 2, 3.5, 0, 2, 3.517, 0, 2, 3.533, 0, 2, 3.567, 0, 2, 3.583, 0, 2, 3.783, 0, 2, 3.8, 0, 2, 5.767, 0, 0, 5.833, -0.305, 0, 5.883, -0.2, 0, 5.983, -1, 2, 6, -1, 0, 6.017, 1, 2, 6.05, 1, 0, 6.1, 0.826, 0, 6.117, 0.846, 0, 6.367, -0.307, 0, 6.633, 0.171, 0, 6.917, -0.077, 0, 7.217, 0.035, 0, 7.517, -0.01, 0, 7.817, 0.007, 0, 7.917, 0.005, 0, 8.083, 0.154, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 5.683, 0, 0, 5.7, 5, 2, 5.917, 5, 0, 5.933, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 5.683, 0, 0, 5.7, -5, 2, 5.917, -5, 0, 5.933, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "jing<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 6.233, 0, 0, 6.3, 1, 2, 6.317, 1, 2, 6.35, 1, 2, 6.367, 1, 2, 6.383, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 6.267, -11.62, 1, 6.272, -11.62, 6.278, -8.121, 6.283, -8.102, 1, 6.316, -7.985, 6.35, -8.008, 6.383, -7.8, 1, 6.422, -7.557, 6.461, 0, 6.5, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 2.256, 0, 4.261, 0.464, 6.267, 1.4, 1, 6.272, 1.403, 6.278, 23.466, 6.283, 23.466, 1, 6.316, 23.466, 6.35, 23.756, 6.383, 21.78, 1, 6.422, 19.474, 6.461, 0, 6.5, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 6.267, -6.84, 1, 6.272, -6.84, 6.278, -6.202, 6.283, -5.88, 1, 6.316, -3.945, 6.35, -0.924, 6.383, -0.48, 1, 6.422, 0.038, 6.461, 0, 6.5, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 6.267, -17, 2, 6.383, -17, 0, 6.5, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamShoulderLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.783, 4, 1, 1.15, 4, 1.516, 4.024, 1.883, 3, 1, 2.161, 2.224, 2.439, -1, 2.717, -1, 0, 3.517, 2, 0, 4.333, -1, 0, 5.233, 2, 0, 5.9, -1, 0, 6.617, 2, 0, 7.367, -1, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 0.417, 0, 0.583, -0.143, 0.75, 0.336, 1, 0.967, 0.959, 1.183, 8.108, 1.4, 8.108, 1, 1.617, 8.108, 1.833, 8.749, 2.05, 5.174, 1, 2.283, 1.324, 2.517, -30, 2.75, -30, 2, 6.25, -30, 0, 7.817, 2.051, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.483, 0, 0, 1.983, -20.41, 0, 2.75, -4.72, 0, 3.767, -8.2, 2, 5.833, -8.2, 0, 6.533, -20.41, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 30, 2, 0.25, 30, 1, 0.372, 30, 0.495, 30.275, 0.617, 29.371, 1, 0.789, 28.098, 0.961, 6.356, 1.133, 5.464, 1, 1.439, 3.881, 1.744, 3.6, 2.05, 2.28, 1, 2.283, 1.272, 2.517, 0.06, 2.75, 0.06, 2, 6.533, 0.06, 1, 6.961, 0.06, 7.389, 16.816, 7.817, 27.72, 1, 7.95, 31.119, 8.084, 30, 8.217, 30, 2, 8.417, 30, 2, 8.667, 30]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1, 2, 0.25, -1, 2, 1.05, -1, 1, 1.144, -1, 1.239, -0.714, 1.333, -0.5, 1, 1.805, 0.571, 2.278, 1, 2.75, 1, 2, 3.767, 1, 2, 5.833, 1, 2, 6.583, 1, 1, 6.605, 0.333, 6.628, -0.333, 6.65, -1, 2, 8.417, -1, 2, 8.667, -1]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.483, 0, 0, 0.933, -0.5, 0, 2.017, -0.194, 0, 2.75, -0.6, 2, 4.067, -0.6, 0, 4.45, -0.377, 0, 4.833, -0.6, 0, 5.25, -0.242, 0, 5.65, -0.6, 0, 5.883, -0.377, 0, 6.367, -0.6, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamHandLDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10, 2, 0.25, 10, 2, 0.933, 10, 0, 0.95, 8, 2, 2.75, 8, 2, 5.833, 8, 2, 7.133, 8, 0, 7.15, 10, 2, 8.417, 10, 2, 8.667, 10]}, {"Target": "Parameter", "Id": "XIONGJIYA2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.583, 0, 0, 0.6, 1, 2, 2.133, 1, 0, 2.15, 0, 2, 8.017, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "xiongjiya", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.583, 0, 2, 1.983, 0, 2, 2.117, 0, 1, 2.172, 0, 2.228, 0.346, 2.283, 0.491, 1, 2.439, 0.898, 2.594, 1, 2.75, 1, 2, 3.767, 1, 2, 5.833, 1, 1, 6.066, 0.667, 6.3, 0.333, 6.533, 0, 2, 8.033, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "xiongjiya3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.083, 0, 2, 2.533, 0, 0, 2.75, 1, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "xiongjiya4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 2.05, 0, 0, 2.5, 1, 2, 6.1, 1, 2, 6.7, 1, 0, 7.05, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamShoulderRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.783, 4, 1, 1.15, 4, 1.516, 4.024, 1.883, 3, 1, 2.161, 2.224, 2.439, -1, 2.717, -1, 0, 3.517, 2, 0, 4.333, -1, 0, 5.233, 2, 0, 5.9, -1, 0, 6.617, 2, 0, 7.367, -1, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.583, 0, 0, 1.4, -3.172, 1, 1.617, -3.172, 1.833, -3.746, 2.05, -0.649, 1, 2.283, 2.686, 2.517, 30, 2.75, 30, 2, 6.25, 30, 0, 8.033, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.583, 0, 0, 1.983, -14.584, 0, 2.75, -5.92, 1, 3.089, -6.58, 3.428, -7.24, 3.767, -7.9, 2, 5.833, -7.9, 1, 6.066, -10.128, 6.3, -12.356, 6.533, -14.584, 0, 8.033, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.617, 1, 2, 8, 1, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.583, 0, 0, 0.917, -2.35, 0, 1.4, 5.209, 1, 1.489, 5.209, 1.578, 5.344, 1.667, 3.769, 1, 1.772, 1.898, 1.878, -2.635, 1.983, -2.635, 0, 2.75, -0.72, 2, 7.15, -0.72, 0, 7.567, 0, 2, 8.033, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.583, 0, 0, 0.917, 0.137, 0, 1.4, -0.392, 0, 1.667, -0.17, 1, 1.772, -0.17, 1.878, -0.159, 1.983, -0.229, 1, 2.239, -0.398, 2.494, -0.6, 2.75, -0.6, 2, 4.067, -0.6, 0, 4.45, -0.323, 0, 4.833, -0.6, 0, 5.25, -0.157, 0, 5.65, -0.6, 0, 5.883, -0.323, 0, 6.367, -0.6, 0, 7.317, 0, 2, 8.033, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamHandRDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.733, 0, 2, 1.75, 8, 2, 7.133, 8, 2, 7.15, 0, 2, 8.033, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.583, 0, 2, 1.4, 0, 0, 1.417, 1, 2, 7.133, 1, 0, 7.15, 0, 2, 8.033, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.583, 0, 2, 1.983, 0, 2, 2.15, 0, 0, 2.383, 1, 2, 3.867, 1, 2, 6.1, 1, 0, 6.367, 0, 2, 6.533, 0, 2, 8.033, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.583, 0, 2, 1.4, 0, 0, 1.417, 1, 2, 7.133, 1, 0, 7.15, 0, 2, 8.033, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.583, 0, 0, 1.417, -0.352, 2, 2.067, -0.352, 0, 2.383, -0.1, 0, 5.45, -0.168, 0, 5.883, -0.154, 1, 5.916, -0.154, 5.95, -0.216, 5.983, -0.236, 1, 6.133, -0.324, 6.283, -0.352, 6.433, -0.352, 2, 7.133, -0.352, 0, 8.033, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.583, 0, 0, 1.417, -0.298, 2, 2.067, -0.298, 0, 2.383, -0.3, 2, 6.1, -0.3, 0, 6.433, -0.298, 2, 7.133, -0.298, 0, 8.033, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.583, 0, 0, 1.417, 0.894, 2, 2.067, 0.894, 0, 2.383, 0.644, 2, 6.1, 0.644, 0, 6.433, 0.894, 2, 7.133, 0.894, 0, 8.033, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.617, 0, 2, 3.433, 0, 1, 3.633, 0, 3.833, 0.097, 4.033, 0.249, 1, 4.428, 0.548, 4.822, 0.679, 5.217, 0.679, 2, 5.617, 0.679, 0, 5.9, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 3.883, 0, 0, 4.133, -0.479, 0, 4.4, 0.8, 0, 4.717, -0.555, 0, 5.15, 0.8, 0, 5.6, -1, 1, 5.694, -1, 5.789, -0.99, 5.883, -0.9, 1, 5.916, -0.868, 5.95, 0.956, 5.983, 0.956, 0, 6.017, 0.92, 0, 6.1, 1, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1.383, 0, 0, 3.417, 1, 2, 5.783, 1, 2, 7.517, 1, 0, 7.533, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 5.967, 0, 1, 6.067, 0, 6.167, 0.376, 6.267, 0.502, 1, 6.584, 0.9, 6.9, 1, 7.217, 1, 2, 7.3, 1, 2, 7.517, 1, 0, 7.533, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Param103", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 6.267, 26.88, 1, 6.35, 26.88, 6.434, -0.064, 6.517, -11.335, 1, 6.639, -27.866, 6.761, -30, 6.883, -30, 2, 7.517, -30, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 6.25, 0, 1, 6.256, 0, 6.261, 0, 6.267, 0.007, 1, 6.328, 0.15, 6.389, 0.385, 6.45, 0.521, 1, 6.606, 0.866, 6.761, 1, 6.917, 1, 2, 7.15, 1, 2, 7.167, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 6.617, 0, 1, 6.639, 0, 6.661, -0.015, 6.683, 0.062, 1, 6.705, 0.14, 6.728, 1, 6.75, 1, 1, 6.778, 1, 6.805, 0.621, 6.833, 0.062, 1, 6.861, -0.496, 6.889, -0.695, 6.917, -0.695, 1, 6.945, -0.695, 6.972, -0.404, 7, 0.039, 1, 7.028, 0.482, 7.055, 0.647, 7.083, 0.647, 1, 7.111, 0.647, 7.139, 0.201, 7.167, 0.039, 1, 7.195, -0.123, 7.222, -0.113, 7.25, -0.113, 0, 7.333, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 6.617, 0, 0, 6.683, -1, 1, 6.705, -1, 6.728, -0.512, 6.75, 0.062, 1, 6.778, 0.78, 6.805, 1, 6.833, 1, 1, 6.861, 1, 6.889, 0.593, 6.917, 0.039, 1, 6.945, -0.516, 6.972, -0.695, 7, -0.695, 1, 7.028, -0.695, 7.055, -0.404, 7.083, 0.039, 1, 7.111, 0.482, 7.139, 0.647, 7.167, 0.647, 1, 7.195, 0.647, 7.222, 0.438, 7.25, 0.238, 1, 7.278, 0.037, 7.305, 0, 7.333, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 0.278, 0, 0.305, 0.554, 0.333, 1.059, 1, 0.455, 3.281, 0.578, 4.192, 0.7, 4.192, 0, 1.6, -30, 0, 2.9, 0, 2, 3.883, 0, 0, 4.133, -6.66, 0, 4.4, 4.883, 0, 4.717, -9.8, 0, 5.15, 8.24, 0, 5.6, -13.185, 0, 6.2, 30, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -7.795, 2, 0.25, -7.795, 0, 3.883, 0, 0, 4.133, -2.009, 0, 4.4, 2, 0, 4.717, -4, 0, 5.15, 4, 1, 5.3, 4, 5.45, -1.941, 5.6, -4, 1, 5.694, -5.296, 5.789, -5, 5.883, -5, 0, 6.267, 4.768, 1, 6.372, 4.768, 6.478, 1.002, 6.583, 0, 1, 7.194, -5.801, 7.806, -7.795, 8.417, -7.795, 2, 8.667, -7.795]}, {"Target": "Parameter", "Id": "ParamBodyStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 3.817, 0, 0, 4.067, -2.288, 0, 4.333, 2.866, 0, 4.617, -5.233, 0, 4.933, 8.411, 0, 5.533, -10, 0, 5.783, 3.534, 0, 6.017, -3, 0, 6.35, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.533, 0, 1, 0.55, 0, 0.566, 0.167, 0.583, 0.5, 1, 0.6, 0.833, 0.616, 1, 0.633, 1, 2, 7.75, 1, 0, 7.783, 0, 2, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Physics_AngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.9, 0, 0, 1.617, -18.071, 0, 2.917, 0.366, 0, 3.117, 0, 2, 3.95, 0, 0, 4.183, -3.934, 0, 4.45, 3.345, 0, 4.767, -6.559, 0, 5.183, 5.861, 0, 5.633, -8.874, 0, 6.233, 19.183, 0, 8.4, -0.301, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.7, -2.795, 0, 1.2, 9.128, 0, 1.65, -8.022, 0, 2.067, 3.432, 0, 2.467, -2.863, 0, 2.917, 2.978, 0, 3.333, -1.641, 0, 3.75, 1.096, 0, 3.9, 0.657, 0, 4.083, 4.354, 0, 4.35, -13.784, 0, 4.667, 19.949, 0, 5.017, -22.473, 0, 5.417, 27.524, 0, 5.833, -29.737, 0, 5.9, -26.876, 0, 5.95, -27.928, 0, 6.267, 30, 2, 6.3, 30, 0, 6.7, -18.545, 0, 7.117, 12.629, 0, 7.533, -7.456, 0, 7.95, 4.885, 0, 8.383, -3.366, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.7, 2.795, 0, 1.15, -8.062, 0, 1.433, 24.029, 0, 1.867, -16.551, 0, 2.267, 5.583, 0, 2.667, -8.316, 0, 3.133, 5.854, 0, 3.533, -3.462, 0, 3.883, 2.188, 0, 4.033, -0.623, 0, 4.267, 15.004, 0, 4.5, -30, 2, 4.6, -30, 0, 4.767, 30, 2, 4.95, 30, 0, 5.117, -30, 2, 5.333, -30, 0, 5.483, 30, 2, 5.75, 30, 1, 5.8, 30, 5.85, -30, 5.9, -30, 2, 6.233, -30, 1, 6.283, -30, 6.333, 30, 6.383, 30, 2, 6.633, 30, 0, 6.833, -30, 2, 6.983, -30, 0, 7.317, 28.287, 0, 7.733, -14.133, 0, 8.15, 11.69, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_L", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.7, 0.978, 0, 1.2, -3.48, 0, 1.483, 6.385, 0, 1.95, -3.889, 0, 2.4, 1.141, 0, 2.817, -1.778, 0, 2.867, -1.625, 0, 2.9, -1.692, 0, 3.317, 1.447, 0, 3.8, -1.073, 0, 4.267, 3.38, 0, 4.567, -5.103, 0, 4.883, 5.171, 0, 5.267, -5.99, 0, 5.683, 7.631, 0, 6.133, -9.131, 0, 6.583, 7.194, 0, 7.033, -3.887, 0, 7.483, 3.21, 0, 8.033, -1.546, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_R", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.7, -0.978, 0, 1.2, 3.48, 0, 1.483, -6.385, 0, 1.95, 3.889, 0, 2.4, -1.141, 0, 2.817, 1.778, 0, 2.867, 1.625, 0, 2.9, 1.692, 0, 3.317, -1.447, 0, 3.8, 1.073, 0, 4.267, -3.38, 0, 4.567, 5.103, 0, 4.883, -5.171, 0, 5.267, 5.99, 0, 5.683, -7.631, 0, 6.133, 9.131, 0, 6.583, -7.194, 0, 7.033, 3.887, 0, 7.483, -3.21, 0, 8.033, 1.546, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.7, -5.589, 0, 1.183, 17.444, 0, 1.667, -3.896, 0, 1.933, -2.563, 0, 2.283, -3.53, 0, 2.983, 1.879, 0, 3.3, -1.972, 0, 3.5, 0, 2, 3.883, 0, 0, 4.133, 8.88, 0, 4.4, -6.511, 0, 4.717, 13.068, 0, 5.15, -10.988, 0, 5.567, 16.735, 0, 5.917, -19.088, 0, 6.317, 7.339, 0, 6.667, -0.189, 0, 7.067, 2.291, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.7, 5.589, 0, 1.15, -15.999, 0, 1.35, 14.773, 0, 1.617, -4.731, 0, 1.9, 1.049, 0, 2.483, -0.294, 0, 2.5, -0.292, 0, 2.517, -0.301, 0, 2.533, -0.297, 0, 2.55, -0.303, 0, 2.567, -0.297, 0, 2.583, -0.302, 0, 2.6, -0.296, 0, 2.617, -0.3, 0, 2.633, -0.295, 0, 2.65, -0.3, 0, 2.667, -0.296, 0, 2.683, -0.302, 2, 2.7, -0.302, 0, 2.917, -1.974, 0, 3.133, 1.554, 0, 3.15, 1.549, 0, 3.2, 3.899, 0, 3.433, -2.324, 0, 3.483, -1.985, 0, 3.5, -2.457, 0, 3.65, 2.584, 0, 3.867, -2.726, 0, 3.9, -2.562, 0, 3.967, -3.381, 0, 4, -3.079, 0, 4.15, -6.773, 0, 4.183, -6.329, 0, 4.2, -6.475, 0, 4.45, 7.051, 0, 4.717, -13.068, 0, 5.15, 10.988, 0, 5.55, -16.601, 0, 5.75, 22.14, 0, 6.017, -15.306, 0, 6.383, 5.345, 0, 6.7, -2.029, 0, 7.017, 0.505, 0, 7.367, -0.078, 0, 7.75, 0.088, 2, 7.783, 0.088, 0, 7.8, 0.093, 0, 7.817, 0.091, 0, 7.833, 0.096, 0, 7.867, 0.092, 0, 7.9, 0.098, 0, 7.933, 0.093, 0, 7.95, 0.095, 0, 7.967, 0.093, 0, 7.983, 0.095, 0, 8.017, 0.091, 0, 8.033, 0.094, 0, 8.05, 0.093, 0, 8.067, 0.096, 0, 8.1, 0.095, 0, 8.367, 1.741, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.7, 5.589, 0, 1.3, -25.885, 0, 1.817, 7.83, 0, 2.267, 4.44, 0, 2.3, 4.461, 0, 2.317, 4.46, 0, 2.333, 4.474, 0, 2.35, 4.472, 0, 2.367, 4.481, 0, 3.033, -2.415, 0, 3.3, 0, 2, 3.883, 0, 0, 4.133, -8.88, 0, 4.4, 6.511, 0, 4.717, -13.069, 0, 5.15, 10.988, 0, 5.6, -17.586, 0, 6.067, 28.754, 0, 6.5, -8.361, 0, 6.917, -1.363, 0, 7.367, -3.137, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.7, -5.589, 0, 1.25, 24.691, 0, 1.517, -23.274, 0, 1.85, 7.795, 0, 2.2, -1.907, 0, 2.583, 0.853, 2, 2.6, 0.853, 0, 2.617, 0.857, 0, 2.733, 0.812, 0, 2.75, 0.814, 0, 2.767, 0.813, 0, 2.883, 1.685, 0, 3.167, -1.827, 0, 3.283, -1.178, 0, 3.3, -1.882, 0, 3.45, 4.094, 0, 3.733, -3.213, 0, 4.1, 10.559, 0, 4.4, -6.511, 0, 4.717, 13.069, 0, 5.15, -10.988, 0, 5.6, 17.586, 0, 6.017, -25.88, 0, 6.267, 28.688, 0, 6.583, -11.457, 0, 6.933, 3.24, 0, 7.3, -0.608, 1, 7.406, -0.608, 7.511, -0.078, 7.617, -0.057, 1, 7.884, -0.005, 8.15, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Jewelry_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.567, -0.417, 0, 1.017, 0.85, 0, 1.55, -0.597, 0, 2.017, 0.082, 0, 2.383, -0.202, 0, 2.917, 0.265, 0, 3.333, -0.169, 0, 3.75, 0.107, 0, 3.9, 0.06, 0, 4.083, 0.445, 0, 4.35, -1.157, 0, 4.667, 1.612, 0, 5.017, -1.925, 0, 5.417, 2.429, 0, 5.833, -3.11, 0, 6.283, 2.807, 0, 6.7, -1.652, 0, 7.117, 1.155, 0, 7.55, -0.66, 0, 7.95, 0.438, 0, 8.383, -0.339, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Jewelry_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.55, 0.412, 0, 0.817, -1.127, 0, 1.233, 2.248, 2, 1.25, 2.248, 0, 1.817, -0.98, 0, 2.2, -0.098, 0, 2.583, -0.804, 0, 3.133, 0.466, 0, 3.533, -0.349, 2, 3.55, -0.349, 0, 3.9, 0.209, 0, 4.033, -0.1, 0, 4.267, 1.404, 0, 4.55, -2.939, 0, 4.867, 3.993, 0, 5.217, -4.844, 0, 5.617, 5.713, 0, 6.05, -6.658, 0, 6.5, 5.18, 0, 6.9, -3.231, 0, 7.317, 2.595, 0, 7.75, -1.18, 0, 8.15, 1.096, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Jewelry_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.55, 0, 0, 0.717, 0.763, 0, 1, -1.459, 0, 1.367, 1.333, 0, 1.85, -0.452, 0, 2.333, 0.369, 0, 2.8, -0.457, 0, 3.25, 0.343, 0, 3.683, -0.373, 0, 3.983, 0.187, 0, 4.183, -0.384, 0, 4.433, 1.686, 0, 4.717, -3.312, 0, 5.017, 4.475, 0, 5.367, -5.037, 0, 5.75, 5.224, 0, 6.167, -4.626, 0, 6.617, 4.026, 0, 7.033, -3.134, 0, 7.433, 2.137, 0, 7.85, -1.343, 0, 8.267, 0.902, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.7, -0.116, 0, 1.15, 0.278, 0, 1.633, -0.252, 0, 2.05, 0.089, 0, 2.433, -0.088, 2, 2.45, -0.088, 0, 2.917, 0.096, 0, 3.333, -0.061, 0, 3.567, 0, 2, 3.817, 0, 0, 4.033, 0.385, 0, 4.3, -0.933, 0, 4.583, 1.46, 0, 4.9, -2.062, 0, 5.283, 2.414, 0, 5.733, -3.531, 0, 6.067, 2.27, 0, 6.5, -1.146, 0, 6.917, 0.754, 0, 7.333, -0.455, 0, 7.75, 0.301, 0, 8.167, -0.196, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.7, 0.116, 0, 1.067, -0.213, 0, 1.383, 0.741, 0, 1.85, -0.491, 0, 2.25, 0.119, 0, 2.65, -0.281, 0, 3.133, 0.181, 0, 3.55, -0.13, 0, 3.767, 0.063, 0, 3.983, -0.285, 0, 4.217, 1.138, 0, 4.483, -2.363, 0, 4.767, 3.448, 0, 5.083, -4.485, 0, 5.467, 4.816, 0, 5.883, -6.408, 0, 6.267, 3.756, 0, 6.7, -2.215, 0, 7.117, 1.568, 0, 7.533, -0.898, 0, 7.95, 0.685, 0, 8.383, -0.385, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.567, -1.251, 0, 1.017, 2.551, 0, 1.55, -1.792, 0, 2.017, 0.246, 0, 2.383, -0.605, 0, 2.917, 0.796, 0, 3.333, -0.506, 0, 3.75, 0.322, 0, 3.9, 0.18, 0, 4.083, 1.334, 0, 4.35, -3.47, 0, 4.667, 4.835, 0, 5.017, -5.774, 0, 5.417, 7.288, 0, 5.833, -9.331, 0, 6.283, 8.42, 0, 6.7, -4.955, 0, 7.117, 3.464, 0, 7.55, -1.98, 0, 7.95, 1.313, 0, 8.383, -1.018, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 0.55, 1.236, 0, 0.817, -3.38, 0, 1.25, 6.744, 0, 1.817, -2.941, 0, 2.2, -0.293, 0, 2.583, -2.412, 0, 3.133, 1.397, 0, 3.55, -1.049, 0, 3.9, 0.627, 0, 4.033, -0.3, 0, 4.267, 4.211, 0, 4.55, -8.818, 0, 4.867, 11.979, 0, 5.217, -14.533, 0, 5.617, 17.14, 0, 6.05, -19.974, 0, 6.5, 15.539, 0, 6.9, -9.692, 0, 7.317, 7.786, 0, 7.75, -3.54, 0, 8.15, 3.287, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.55, 0, 0, 0.717, 2.29, 0, 1, -4.376, 0, 1.367, 4, 0, 1.85, -1.357, 0, 2.333, 1.107, 0, 2.8, -1.371, 0, 3.25, 1.03, 0, 3.683, -1.119, 0, 3.983, 0.561, 0, 4.183, -1.152, 0, 4.433, 5.058, 0, 4.717, -9.934, 0, 5.017, 13.426, 0, 5.367, -15.11, 0, 5.75, 15.673, 0, 6.167, -13.878, 0, 6.617, 12.079, 0, 7.033, -9.401, 0, 7.433, 6.412, 0, 7.85, -4.028, 0, 8.267, 2.707, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.683, 0, 0, 0.833, 2.185, 0, 1.117, -5.408, 0, 1.483, 5.81, 0, 1.867, -2.563, 0, 2.417, 1.354, 0, 2.85, -1.518, 2, 2.867, -1.518, 0, 3.317, 1.377, 0, 3.333, 1.371, 0, 3.35, 1.384, 0, 3.783, -1.306, 0, 4.083, 0.743, 0, 4.283, -0.64, 0, 4.533, 4.914, 0, 4.817, -11.222, 0, 5.133, 16.749, 0, 5.467, -20.062, 0, 5.833, 20.838, 0, 6.233, -18.288, 0, 6.7, 15.159, 0, 7.117, -12.923, 0, 7.517, 9.459, 0, 7.933, -6.125, 0, 8.35, 4.209, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.783, 0, 0, 0.95, 2.586, 0, 1.233, -6.529, 0, 1.583, 8.062, 0, 1.95, -4.61, 0, 2.417, 1.706, 0, 2.933, -1.983, 0, 3.35, 1.658, 0, 3.867, -1.696, 0, 4.183, 1.059, 0, 4.383, -0.411, 0, 4.633, 4.847, 0, 4.933, -12.54, 0, 5.233, 20.18, 0, 5.55, -25.228, 0, 5.9, 26.49, 0, 6.3, -23.18, 0, 6.767, 18.267, 0, 7.2, -16.592, 0, 7.6, 13.258, 0, 8, -9.142, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 0.9, 0, 0, 1.067, 2.599, 0, 1.35, -7.651, 0, 1.683, 10.694, 0, 2.05, -7.517, 0, 2.433, 3.26, 0, 3.017, -2.35, 0, 3.467, 2.393, 0, 3.933, -2.017, 0, 4.267, 1.463, 0, 4.5, -0.277, 0, 4.733, 4.729, 0, 5.033, -13.965, 0, 5.317, 23.828, 0, 5.617, -30, 2, 5.633, -30, 0, 5.95, 30, 2, 5.983, 30, 0, 6.367, -27.924, 0, 6.817, 21.078, 0, 7.267, -19.9, 0, 7.683, 17.392, 0, 8.083, -13.017, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 2, 1, 0, 0, 1.167, 2.703, 0, 1.45, -8.802, 0, 1.783, 13.67, 0, 2.15, -11.297, 0, 2.517, 6.089, 0, 3.067, -2.59, 0, 3.567, 3.043, 0, 4.017, -2.758, 0, 4.4, 2.144, 0, 4.617, -0.16, 0, 4.85, 4.451, 0, 5.133, -15.122, 0, 5.4, 27.354, 0, 5.667, -30, 2, 5.733, -30, 0, 5.983, 30, 2, 6.067, 30, 0, 6.4, -30, 2, 6.433, -30, 0, 6.867, 23.208, 0, 7.333, -22.242, 0, 7.75, 21.139, 0, 8.167, -17.377, 0, 8.417, 0, 2, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 0, 1.417, 1, 0, 3.25, 0, 0, 4.417, 1, 0, 6.25, 0, 0, 7.417, 1, 1, 7.834, 1, 8.25, 0.535, 8.667, 0.239]}, {"Target": "Parameter", "Id": "fenshangxuanzhuang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.25, 0, 1, 3.056, 1.123, 5.861, 2.247, 8.667, 3.37]}, {"Target": "Parameter", "Id": "Mail_Transparency6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamSad2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 8.667, 1]}, {"Target": "Parameter", "Id": "ParamCRY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.2, 0, 8.667, -5.2]}, {"Target": "Parameter", "Id": "Position_Coordinates", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 8.667, 1]}, {"Target": "Parameter", "Id": "Man_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 8.667, 1]}, {"Target": "Parameter", "Id": "Man_TMD2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "BG_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 8.667, 1]}, {"Target": "Parameter", "Id": "FG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "BG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "BG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "BG_White5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "BG_White2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "All_Size3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "tuerzuo3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "faqiehuan", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "faqiehuan2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "faqiehuan4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "faqiehuan3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "tuerzuo4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Mail_Change", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "dafengshangzhuanchang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "tongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamTongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "EyeFrame_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamBrowForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamBrowY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamSigh", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamDarken", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamCry", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_B", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_C", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "MB_Unidirectional_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "huqi1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "huqi2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamArmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 8.667, 1]}, {"Target": "Parameter", "Id": "ParamForearmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamArmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamArmL_ForearmL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmL_HandL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamHandLSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "xiongjiya5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "xiongjiya2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamArmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 8.667, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamArmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamArmR_ForearmR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearm<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 8.667, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_HandR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamHandRSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 8.667, 0.6]}, {"Target": "Parameter", "Id": "Change_idle_physics23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "taiqiugundong", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -22.5, 0, 8.667, -22.5]}, {"Target": "Parameter", "Id": "Change_idle_physics32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamLegL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 8.667, 1]}, {"Target": "Parameter", "Id": "ParamLegLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamLegLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamCrusLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamCrusLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>oodLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamFoodLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamFoodLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamLegR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 8.667, 1]}, {"Target": "Parameter", "Id": "ParamLegRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamLegRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamCrusRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamCrusRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamCrus<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>ood<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamFoodRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamFoodRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Physics_Provisional", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "physics_Add_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "physics_Add_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Physics_AngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Physics_AngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Physics_Body_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Physics_Body_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Physics_breast", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamBreath2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "touch_drag1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "touch_drag2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "touch_drag3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "touch_drag4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "touch_drag5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "touch_drag6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "touch_drag7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "touch_drag8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "touch_drag9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>pian<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh654", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh655", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh657", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh656", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh658", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh659", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh660", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.667, 0]}], "UserData": [{"Time": 0.25, "Value": ""}, {"Time": 8.167, "Value": ""}]}