{"Version": 3, "Meta": {"Duration": 30.583, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 303, "TotalSegmentCount": 4273, "TotalPointCount": 4867, "UserDataCount": 2, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "BG_White3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 2.317, 0.7, 0, 4.7, 0, 2, 4.717, 0, 2, 9.65, 0, 2, 9.917, 0, 2, 10.517, 0, 2, 13.483, 0, 2, 17.333, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "All_Size2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 2.317, -30, 0, 4.7, 0, 2, 4.717, 0, 2, 9.65, 0, 2, 9.917, 0, 2, 10.517, 0, 2, 13.483, 0, 2, 17.333, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "All_Size3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 2.317, -3.9, 0, 4.7, 0, 2, 4.717, 0, 2, 9.65, 0, 2, 9.917, 0, 2, 10.517, 0, 2, 13.483, 0, 2, 17.333, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.367, -0.5, 0, 0.867, 0.8, 0, 1.3, -0.5, 0, 1.867, 0.3, 0, 2.533, -0.7, 0, 3.767, 0.8, 1, 4.078, 0.8, 4.389, 0.603, 4.7, 0.187, 1, 4.706, 0.18, 4.711, -0.57, 4.717, -0.57, 0, 5.383, 0.7, 0, 5.817, -0.8, 0, 6.567, 0.6, 0, 7.033, 0.1, 0, 7.317, 1, 0, 7.767, 0.3, 0, 8.183, 1, 0, 8.783, -0.5, 0, 9.083, 1, 1, 9.272, 1, 9.461, 0.59, 9.65, 0.036, 1, 9.739, -0.225, 9.828, -0.437, 9.917, -0.57, 1, 9.945, -0.612, 9.972, -0.6, 10, -0.6, 1, 10.172, -0.6, 10.345, -0.475, 10.517, -0.098, 1, 10.828, 0.583, 11.139, 1, 11.45, 1, 0, 11.717, -0.3, 0, 11.983, 1, 1, 12.233, 1, 12.483, 0.95, 12.733, 0.7, 1, 12.805, 0.628, 12.878, -0.1, 12.95, -0.1, 0, 13.3, 0.6, 0, 13.483, 0, 2, 14, 0, 0, 14.133, -0.2, 0, 15.1, 0, 2, 17.333, 0, 0, 17.667, 1, 0, 18.167, -0.4, 0, 18.433, 0.6, 0, 18.617, -0.2, 0, 19.25, 0.9, 0, 20.133, 0, 0, 21.45, 0.6, 0, 21.833, 0.1, 0, 22.583, 0.9, 0, 23.5, -0.6, 0, 23.85, 0.6, 0, 24.4, 0, 0, 24.867, 0.7, 0, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY_SD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.583, 0, 0, 0.7, 1, 0, 0.817, 0, 0, 1, 1, 0, 1.117, 0, 0, 1.35, 1, 0, 1.517, 0, 0, 1.7, 1, 0, 1.817, 0, 0, 2, 1, 0, 2.117, 0, 0, 2.3, 1, 0, 2.433, 0, 0, 2.583, 1, 0, 2.75, 0, 0, 2.867, 1, 0, 2.983, 0, 0, 3.167, 1, 0, 3.283, 0, 0, 3.517, 1, 0, 3.683, 0, 0, 3.867, 1, 0, 3.983, 0, 0, 4.167, 1, 0, 4.283, 0, 2, 5.133, 0, 0, 5.25, 1, 0, 5.367, 0, 0, 5.55, 1, 0, 5.667, 0, 0, 5.9, 1, 0, 6.067, 0, 0, 6.25, 1, 0, 6.367, 0, 0, 6.55, 1, 0, 6.667, 0, 0, 6.85, 1, 0, 6.983, 0, 0, 7.133, 1, 0, 7.3, 0, 0, 7.417, 1, 0, 7.533, 0, 0, 7.717, 1, 0, 7.833, 0, 0, 8.067, 1, 0, 8.233, 0, 0, 8.417, 1, 0, 8.533, 0, 0, 8.717, 1, 0, 8.833, 0, 0, 9.017, 1, 0, 9.15, 0, 0, 9.3, 1, 0, 9.467, 0, 0, 9.583, 1, 0, 9.7, 0, 0, 9.883, 1, 0, 10, 0, 0, 10.233, 1, 0, 10.4, 0, 0, 10.583, 1, 0, 11, 0, 2, 14, 0, 0, 14.133, 0.8, 0, 15.1, 0, 0, 15.217, 1, 0, 15.333, 0, 0, 15.517, 1, 0, 15.633, 0, 0, 15.867, 1, 0, 16.033, 0, 0, 16.217, 1, 0, 16.333, 0, 0, 16.517, 1, 0, 16.633, 0, 0, 16.817, 1, 0, 17.2, 0, 2, 17.983, 0, 0, 18.1, 1, 0, 18.217, 0, 0, 18.4, 1, 0, 18.517, 0, 0, 18.75, 1, 0, 18.917, 0, 2, 19.4, 0, 0, 19.517, 1, 0, 19.633, 0, 0, 19.817, 1, 0, 19.933, 0, 0, 20.167, 1, 0, 20.333, 0, 0, 20.517, 1, 0, 20.633, 0, 0, 20.817, 1, 0, 20.933, 0, 0, 21.117, 1, 0, 21.25, 0, 0, 21.4, 1, 0, 21.567, 0, 0, 21.683, 1, 0, 21.8, 0, 0, 21.983, 1, 0, 22.1, 0, 0, 22.333, 1, 0, 22.583, 0, 2, 23.733, 0, 0, 23.85, 1, 0, 23.967, 0, 0, 24.15, 1, 0, 24.267, 0, 0, 24.5, 1, 0, 24.667, 0, 0, 24.85, 1, 0, 24.967, 0, 0, 25.15, 1, 0, 25.267, 0, 0, 25.45, 1, 0, 25.583, 0, 0, 25.733, 1, 0, 25.9, 0, 2, 26.85, 0, 0, 27, 1, 0, 27.15, 0, 0, 27.383, 1, 0, 27.533, 0, 0, 27.833, 1, 0, 28.05, 0, 0, 28.3, 1, 0, 28.45, 0, 0, 28.683, 1, 0, 28.833, 0, 0, 29.067, 1, 0, 29.25, 0, 0, 29.45, 1, 0, 29.883, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 4.7, 0, 2, 4.717, 0, 2, 9.65, 0, 2, 9.917, 0, 2, 10.517, 0, 2, 10.533, 0, 0, 10.75, 30, 2, 10.933, 30, 0, 11.033, 0, 2, 11.217, 0, 2, 11.417, 0, 2, 11.917, 0, 0, 12, 4, 0, 12.133, 0, 2, 13.483, 0, 2, 17.333, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.35, 1, 0, 0.483, 0, 0, 0.667, 1.3, 0, 0.75, 1, 2, 2.4, 1, 0, 2.533, 0, 0, 2.717, 1.3, 0, 2.8, 1, 2, 4.133, 1, 0, 4.267, 0, 0, 4.45, 1.3, 0, 4.533, 1, 2, 10.583, 1, 2, 11.017, 1, 0, 11.15, 0, 0, 11.35, 1, 0, 11.417, 0.9, 0, 13, 0.909, 0, 13.25, 0, 2, 13.85, 0, 0, 14.2, 1, 2, 15.5, 1, 0, 15.833, 0, 0, 16.317, 1, 2, 16.867, 1, 0, 17.083, 0, 0, 17.267, 1, 2, 17.6, 1, 0, 17.817, 0, 0, 18, 1, 2, 18.3, 1, 0, 18.533, 0, 0, 18.65, 1.3, 0, 18.8, 1, 2, 19.45, 1, 0, 19.6, 0, 0, 19.85, 1, 2, 20.8, 1, 2, 24.933, 1, 0, 25.067, 0, 0, 25.25, 1.3, 0, 25.333, 1, 2, 26.483, 1, 0, 26.767, 0, 0, 27.15, 1.3, 0, 27.333, 1, 2, 29.017, 1, 0, 29.217, 0, 0, 29.5, 1.3, 0, 29.617, 1, 2, 30.317, 1, 2, 30.583, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 10.583, 0, 2, 12.733, 0, 0, 13, 1, 2, 14.667, 1, 2, 14.8, 1, 0, 15.267, 0, 2, 20.267, 0, 2, 20.8, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.35, 1, 0, 0.483, 0, 0, 0.667, 1.3, 0, 0.75, 1, 2, 2.4, 1, 0, 2.533, 0, 0, 2.717, 1.3, 0, 2.8, 1, 2, 4.133, 1, 0, 4.267, 0, 0, 4.45, 1.3, 0, 4.533, 1, 2, 10.583, 1, 2, 11.017, 1, 0, 11.15, 0, 0, 11.35, 1, 0, 11.417, 0.9, 0, 13, 0.909, 0, 13.25, 0, 2, 13.85, 0, 0, 14.2, 1, 2, 15.5, 1, 0, 15.833, 0, 0, 16.317, 1, 2, 16.867, 1, 0, 17.083, 0, 0, 17.267, 1, 2, 17.6, 1, 0, 17.817, 0, 0, 18, 1, 2, 18.3, 1, 0, 18.533, 0, 0, 18.65, 1.3, 0, 18.8, 1, 2, 19.45, 1, 0, 19.6, 0, 0, 19.85, 1, 2, 20.8, 1, 2, 24.933, 1, 0, 25.067, 0, 0, 25.25, 1.3, 0, 25.333, 1, 2, 26.483, 1, 0, 26.767, 0, 0, 27.15, 1.3, 0, 27.333, 1, 2, 29.017, 1, 0, 29.217, 0, 0, 29.5, 1.3, 0, 29.617, 1, 2, 30.317, 1, 2, 30.583, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 10.583, 0, 2, 12.733, 0, 0, 13, 1, 2, 14.667, 1, 2, 14.8, 1, 0, 15.267, 0, 2, 20.267, 0, 2, 20.8, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 10.433, 0, 2, 10.583, 0, 2, 16.867, 0, 1, 16.995, 0, 17.122, -0.377, 17.25, -0.5, 1, 17.367, -0.613, 17.483, -0.6, 17.6, -0.6, 1, 17.739, -0.6, 17.878, 0.371, 18.017, 0.5, 1, 18.15, 0.624, 18.284, 0.6, 18.417, 0.6, 0, 18.65, 0, 2, 20.267, 0, 2, 20.8, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 10.433, 0, 2, 10.583, 0, 2, 16.867, 0, 1, 16.995, 0, 17.122, -0.209, 17.25, -0.4, 1, 17.367, -0.575, 17.483, -0.6, 17.6, -0.6, 0, 18.017, 0.2, 0, 18.417, 0, 2, 18.65, 0, 2, 20.267, 0, 2, 20.8, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "EyeFrame_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 10.583, 0, 0, 12.083, 1, 2, 12.817, 1, 0, 13.533, 0, 2, 20.8, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.733, 0, 0, 1.317, -1, 2, 3.367, -1, 1, 5.772, -1, 8.178, -0.749, 10.583, 0, 1, 10.939, 0.111, 11.294, 1, 11.65, 1, 2, 13.15, 1, 0, 13.967, -0.8, 0, 15.183, 0, 1, 15.939, 0, 16.694, -0.384, 17.45, -0.7, 1, 18.106, -0.974, 18.761, -1, 19.417, -1, 0, 20.8, 0, 0, 21.167, -1, 2, 29.217, -1, 0, 29.7, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamBrowForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.583, -1, 0, 10.433, 0, 2, 10.583, 0, 2, 11.017, 0, 0, 11.417, 1, 2, 13.017, 1, 0, 13.617, 0, 2, 14.8, 0, 0, 15.267, 1, 2, 17.2, 1, 2, 18.517, 1, 0, 18.717, 0, 2, 18.883, 0, 2, 20.267, 0, 2, 20.8, 0, 0, 21.167, -1, 2, 29.217, -1, 0, 29.7, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamBrowAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.583, 1, 0, 10.433, 0, 2, 10.583, 0, 2, 11.017, 0, 0, 11.417, -1, 2, 13.017, -1, 0, 13.617, 1, 2, 14.8, 1, 0, 15.267, 0, 2, 17.2, 0, 2, 18.517, 0, 2, 20.267, 0, 2, 20.8, 0, 2, 21.167, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamEyeLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.35, 0, 0, 0.417, 1, 2, 0.567, 1, 0, 0.583, -1, 2, 0.717, -1, 0, 1.017, 0.244, 0, 1.333, -0.068, 0, 1.633, 0.019, 0, 1.933, -0.005, 2, 1.95, -0.005, 0, 2.25, 0.002, 2, 2.267, 0.002, 0, 2.4, 0, 0, 2.467, 1, 2, 2.617, 1, 0, 2.633, -1, 2, 2.767, -1, 0, 3.067, 0.247, 0, 3.383, -0.069, 0, 3.683, 0.019, 0, 4, -0.005, 0, 4.2, 1, 2, 4.35, 1, 0, 4.367, -1, 2, 4.5, -1, 0, 4.8, 0.235, 0, 5.117, -0.065, 0, 5.417, 0.018, 0, 5.717, -0.005, 2, 5.733, -0.005, 0, 6.017, 0.001, 2, 6.067, 0.001, 0, 6.283, 0, 2, 6.3, 0, 2, 6.317, 0, 2, 6.383, 0, 2, 6.4, 0, 2, 6.433, 0, 2, 6.45, 0, 2, 6.467, 0, 2, 6.483, 0, 2, 6.517, 0, 2, 6.533, 0, 2, 6.567, 0, 2, 6.583, 0, 2, 6.767, 0, 2, 6.783, 0, 2, 10.583, 0, 2, 11.017, 0, 0, 11.083, 1, 2, 11.283, 1, 0, 11.3, -1, 2, 11.383, -1, 0, 11.4, 1, 2, 11.7, 1, 0, 11.933, -0.4, 0, 12.25, 0.108, 0, 12.55, -0.032, 0, 12.867, 0.008, 0, 13, 0.003, 0, 13.133, 1, 2, 13.217, 1, 0, 13.467, -0.377, 0, 13.767, 0.104, 0, 14.083, -0.029, 0, 14.383, 0.008, 2, 14.4, 0.008, 0, 14.7, -0.002, 2, 14.717, -0.002, 0, 14.967, 0.001, 2, 15.05, 0.001, 0, 15.067, 0, 2, 15.083, 0, 2, 15.117, 0, 2, 15.133, 0, 2, 15.167, 0, 2, 15.183, 0, 2, 15.2, 0, 2, 15.217, 0, 2, 15.233, 0, 2, 15.267, 0, 2, 15.283, 0, 2, 15.367, 0, 2, 15.383, 0, 2, 15.467, 0, 2, 15.483, 0, 2, 15.617, 0, 2, 15.633, 0, 2, 15.65, 0, 2, 15.833, 0, 0, 16.083, -0.742, 0, 16.417, 0.378, 0, 16.733, -0.104, 0, 16.983, 1, 2, 17.1, 1, 0, 17.217, -1, 2, 17.3, -1, 0, 17.567, 0.298, 0, 17.6, 0.287, 0, 17.7, 1, 2, 17.817, 1, 0, 17.933, -1, 2, 18.083, -1, 0, 18.4, 1, 2, 18.517, 1, 0, 18.6, -1, 2, 18.717, -1, 0, 18.733, 1, 2, 18.883, 1, 0, 19.117, -0.407, 0, 19.417, 0.112, 0, 19.45, 0.108, 0, 19.517, 1, 2, 19.767, 1, 0, 19.783, -1, 2, 19.867, -1, 0, 19.883, 1, 2, 20.183, 1, 0, 20.45, -0.4, 1, 20.694, -0.4, 20.939, -0.258, 21.183, -0.068, 1, 21.283, 0.01, 21.383, 0.019, 21.483, 0.019, 0, 21.783, -0.005, 2, 21.8, -0.005, 0, 22.1, 0.002, 2, 22.117, 0.002, 0, 22.133, 0.001, 2, 22.15, 0.001, 0, 22.35, 0, 2, 22.367, 0, 2, 22.383, 0, 2, 22.467, 0, 2, 22.483, 0, 2, 22.5, 0, 2, 22.517, 0, 2, 22.533, 0, 2, 22.55, 0, 2, 22.583, 0, 2, 22.6, 0, 2, 22.633, 0, 2, 22.65, 0, 2, 22.833, 0, 2, 22.85, 0, 2, 29.217, 0, 0, 29.283, 1, 2, 29.433, 1, 0, 29.45, -1, 2, 29.583, -1, 0, 29.883, 0.244, 0, 30.2, -0.068, 0, 30.317, 0, 0, 30.333, -0.021, 2, 30.583, -0.021]}, {"Target": "Parameter", "Id": "ParamEyeRsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.35, 0, 0, 0.417, 1, 2, 0.567, 1, 0, 0.583, -1, 2, 0.717, -1, 0, 1.017, 0.244, 0, 1.333, -0.068, 0, 1.633, 0.019, 0, 1.933, -0.005, 2, 1.95, -0.005, 0, 2.25, 0.002, 2, 2.267, 0.002, 0, 2.4, 0, 0, 2.467, 1, 2, 2.617, 1, 0, 2.633, -1, 2, 2.767, -1, 0, 3.067, 0.247, 0, 3.383, -0.069, 0, 3.683, 0.019, 0, 4, -0.005, 0, 4.2, 1, 2, 4.35, 1, 0, 4.367, -1, 2, 4.5, -1, 0, 4.8, 0.235, 0, 5.117, -0.065, 0, 5.417, 0.018, 0, 5.717, -0.005, 2, 5.733, -0.005, 0, 6.017, 0.001, 2, 6.067, 0.001, 0, 6.283, 0, 2, 6.3, 0, 2, 6.317, 0, 2, 6.383, 0, 2, 6.4, 0, 2, 6.433, 0, 2, 6.45, 0, 2, 6.467, 0, 2, 6.483, 0, 2, 6.517, 0, 2, 6.533, 0, 2, 6.567, 0, 2, 6.583, 0, 2, 6.767, 0, 2, 6.783, 0, 2, 10.583, 0, 2, 11.017, 0, 0, 11.083, 1, 2, 11.283, 1, 0, 11.3, -1, 2, 11.383, -1, 0, 11.4, 1, 2, 11.7, 1, 0, 11.933, -0.4, 0, 12.25, 0.108, 0, 12.55, -0.032, 0, 12.867, 0.008, 0, 13, 0.003, 0, 13.133, 1, 2, 13.217, 1, 0, 13.467, -0.377, 0, 13.767, 0.104, 0, 14.083, -0.029, 0, 14.383, 0.008, 2, 14.4, 0.008, 0, 14.7, -0.002, 2, 14.717, -0.002, 0, 14.967, 0.001, 2, 15.05, 0.001, 0, 15.067, 0, 2, 15.083, 0, 2, 15.117, 0, 2, 15.133, 0, 2, 15.167, 0, 2, 15.183, 0, 2, 15.2, 0, 2, 15.217, 0, 2, 15.233, 0, 2, 15.267, 0, 2, 15.283, 0, 2, 15.367, 0, 2, 15.383, 0, 2, 15.467, 0, 2, 15.483, 0, 2, 15.617, 0, 2, 15.633, 0, 2, 15.65, 0, 2, 15.833, 0, 0, 16.083, -0.742, 0, 16.417, 0.378, 0, 16.733, -0.104, 0, 16.983, 1, 2, 17.1, 1, 0, 17.217, -1, 2, 17.3, -1, 0, 17.567, 0.298, 0, 17.6, 0.287, 0, 17.7, 1, 2, 17.817, 1, 0, 17.933, -1, 2, 18.083, -1, 0, 18.4, 1, 2, 18.517, 1, 0, 18.6, -1, 2, 18.717, -1, 0, 18.733, 1, 2, 18.883, 1, 0, 19.117, -0.407, 0, 19.417, 0.112, 0, 19.45, 0.108, 0, 19.517, 1, 2, 19.767, 1, 0, 19.783, -1, 2, 19.867, -1, 0, 19.883, 1, 2, 20.183, 1, 0, 20.45, -0.4, 1, 20.694, -0.4, 20.939, -0.258, 21.183, -0.068, 1, 21.283, 0.01, 21.383, 0.019, 21.483, 0.019, 0, 21.783, -0.005, 2, 21.8, -0.005, 0, 22.1, 0.002, 2, 22.117, 0.002, 0, 22.133, 0.001, 2, 22.15, 0.001, 0, 22.35, 0, 2, 22.367, 0, 2, 22.383, 0, 2, 22.467, 0, 2, 22.483, 0, 2, 22.5, 0, 2, 22.517, 0, 2, 22.533, 0, 2, 22.55, 0, 2, 22.583, 0, 2, 22.6, 0, 2, 22.633, 0, 2, 22.65, 0, 2, 22.833, 0, 2, 22.85, 0, 2, 29.217, 0, 0, 29.283, 1, 2, 29.433, 1, 0, 29.45, -1, 2, 29.583, -1, 0, 29.883, 0.244, 0, 30.2, -0.068, 0, 30.317, 0, 0, 30.333, -0.021, 2, 30.583, -0.021]}, {"Target": "Parameter", "Id": "ParamEyeLGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.35, 0, 0, 0.417, -0.305, 0, 0.467, -0.2, 0, 0.567, -1, 2, 0.583, -1, 0, 0.6, 1, 2, 0.633, 1, 0, 0.667, 0.831, 2, 0.683, 0.831, 0, 0.717, 0.863, 0, 0.967, -0.314, 0, 1.233, 0.153, 0, 1.517, -0.069, 0, 1.817, 0.028, 0, 2.117, -0.01, 0, 2.4, 0.004, 0, 2.467, -0.304, 0, 2.517, -0.2, 0, 2.617, -1, 2, 2.633, -1, 0, 2.65, 1, 2, 2.683, 1, 0, 2.717, 0.824, 0, 2.767, 0.86, 0, 3.017, -0.315, 0, 3.283, 0.154, 0, 3.567, -0.07, 0, 3.867, 0.028, 0, 4.2, -0.31, 0, 4.25, -0.199, 0, 4.35, -1, 2, 4.367, -1, 0, 4.383, 1, 2, 4.417, 1, 0, 4.467, 0.846, 0, 4.483, 0.868, 0, 4.75, -0.31, 0, 5.017, 0.149, 0, 5.3, -0.066, 0, 5.6, 0.026, 0, 5.9, -0.01, 0, 6.2, 0.003, 2, 6.217, 0.003, 0, 6.483, -0.001, 2, 6.533, -0.001, 0, 6.75, 0, 2, 6.767, 0, 2, 6.783, 0, 2, 6.833, 0, 2, 6.85, 0, 2, 6.9, 0, 2, 6.917, 0, 2, 6.933, 0, 2, 6.95, 0, 2, 6.967, 0, 2, 6.983, 0, 2, 7.017, 0, 2, 7.033, 0, 2, 7.233, 0, 2, 7.25, 0, 2, 10.583, 0, 2, 11.017, 0, 0, 11.083, -0.305, 0, 11.133, -0.2, 0, 11.25, -1, 2, 11.283, -1, 0, 11.3, 1, 2, 11.417, 1, 0, 11.433, -1, 2, 11.65, -1, 0, 11.867, 0.391, 0, 12.133, -0.217, 0, 12.417, 0.104, 0, 12.717, -0.043, 0, 13, 0.015, 0, 13.117, -0.26, 0, 13.333, 0.552, 0, 13.633, -0.291, 0, 13.933, 0.126, 0, 14.233, -0.049, 0, 14.55, 0.018, 0, 14.85, -0.006, 2, 14.867, -0.006, 0, 15.15, 0.002, 2, 15.167, 0.002, 0, 15.4, 0, 2, 15.417, 0, 0, 15.433, -0.001, 2, 15.517, -0.001, 0, 15.55, 0, 2, 15.567, 0, 2, 15.6, 0, 2, 15.617, 0, 2, 15.65, 0, 2, 15.667, 0, 2, 15.683, 0, 2, 15.7, 0, 2, 15.717, 0, 2, 15.833, 0, 0, 16, 0.166, 0, 16.317, -0.297, 0, 16.583, 0.239, 0, 16.95, -0.342, 0, 17.15, 1, 2, 17.3, 1, 0, 17.55, -0.313, 0, 17.617, -0.229, 0, 17.667, -0.257, 0, 17.883, 1, 2, 17.967, 1, 0, 18.283, -0.322, 0, 18.317, -0.307, 0, 18.35, -0.317, 0, 18.567, 1, 2, 18.583, 1, 0, 18.6, -1, 2, 18.7, -1, 0, 18.983, 0.345, 0, 19.267, -0.277, 0, 19.45, 0.016, 0, 19.517, -0.234, 0, 19.583, -0.162, 0, 19.75, -1, 2, 19.783, -1, 0, 19.8, 1, 2, 19.95, 1, 0, 19.967, -1, 2, 20.15, -1, 0, 20.383, 0.396, 0, 20.7, -0.213, 0, 21.083, 0.153, 0, 21.367, -0.069, 0, 21.667, 0.028, 0, 21.967, -0.01, 0, 22.25, 0.004, 2, 22.283, 0.004, 0, 22.533, -0.001, 2, 22.55, -0.001, 2, 22.567, -0.001, 2, 22.6, -0.001, 0, 22.817, 0, 2, 22.833, 0, 2, 22.85, 0, 2, 22.917, 0, 2, 22.933, 0, 2, 22.967, 0, 2, 22.983, 0, 2, 23, 0, 2, 23.017, 0, 2, 23.033, 0, 2, 23.05, 0, 2, 23.083, 0, 2, 23.1, 0, 2, 23.3, 0, 2, 23.317, 0, 2, 29.217, 0, 0, 29.283, -0.305, 0, 29.333, -0.2, 0, 29.433, -1, 2, 29.45, -1, 0, 29.467, 1, 2, 29.5, 1, 0, 29.533, 0.831, 2, 29.55, 0.831, 0, 29.583, 0.863, 0, 29.833, -0.314, 0, 30.1, 0.153, 1, 30.172, 0.153, 30.245, 0.108, 30.317, 0, 1, 30.322, -0.008, 30.328, -0.057, 30.333, -0.057, 2, 30.583, -0.057]}, {"Target": "Parameter", "Id": "ParamEyeRGG", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.35, 0, 0, 0.417, -0.305, 0, 0.467, -0.2, 0, 0.567, -1, 2, 0.583, -1, 0, 0.6, 1, 2, 0.633, 1, 0, 0.667, 0.831, 2, 0.683, 0.831, 0, 0.717, 0.863, 0, 0.967, -0.314, 0, 1.233, 0.153, 0, 1.517, -0.069, 0, 1.817, 0.028, 0, 2.117, -0.01, 0, 2.4, 0.004, 0, 2.467, -0.304, 0, 2.517, -0.2, 0, 2.617, -1, 2, 2.633, -1, 0, 2.65, 1, 2, 2.683, 1, 0, 2.717, 0.824, 0, 2.767, 0.86, 0, 3.017, -0.315, 0, 3.283, 0.154, 0, 3.567, -0.07, 0, 3.867, 0.028, 0, 4.2, -0.31, 0, 4.25, -0.199, 0, 4.35, -1, 2, 4.367, -1, 0, 4.383, 1, 2, 4.417, 1, 0, 4.467, 0.846, 0, 4.483, 0.868, 0, 4.75, -0.31, 0, 5.017, 0.149, 0, 5.3, -0.066, 0, 5.6, 0.026, 0, 5.9, -0.01, 0, 6.2, 0.003, 2, 6.217, 0.003, 0, 6.483, -0.001, 2, 6.533, -0.001, 0, 6.75, 0, 2, 6.767, 0, 2, 6.783, 0, 2, 6.833, 0, 2, 6.85, 0, 2, 6.9, 0, 2, 6.917, 0, 2, 6.933, 0, 2, 6.95, 0, 2, 6.967, 0, 2, 6.983, 0, 2, 7.017, 0, 2, 7.033, 0, 2, 7.233, 0, 2, 7.25, 0, 2, 10.583, 0, 2, 11.017, 0, 0, 11.083, -0.305, 0, 11.133, -0.2, 0, 11.25, -1, 2, 11.283, -1, 0, 11.3, 1, 2, 11.417, 1, 0, 11.433, -1, 2, 11.65, -1, 0, 11.867, 0.391, 0, 12.133, -0.217, 0, 12.417, 0.104, 0, 12.717, -0.043, 0, 13, 0.015, 0, 13.117, -0.26, 0, 13.333, 0.552, 0, 13.633, -0.291, 0, 13.933, 0.126, 0, 14.233, -0.049, 0, 14.55, 0.018, 0, 14.85, -0.006, 2, 14.867, -0.006, 0, 15.15, 0.002, 2, 15.167, 0.002, 0, 15.4, 0, 2, 15.417, 0, 0, 15.433, -0.001, 2, 15.517, -0.001, 0, 15.55, 0, 2, 15.567, 0, 2, 15.6, 0, 2, 15.617, 0, 2, 15.65, 0, 2, 15.667, 0, 2, 15.683, 0, 2, 15.7, 0, 2, 15.717, 0, 2, 15.833, 0, 0, 16, 0.166, 0, 16.317, -0.297, 0, 16.583, 0.239, 0, 16.95, -0.342, 0, 17.15, 1, 2, 17.3, 1, 0, 17.55, -0.313, 0, 17.617, -0.229, 0, 17.667, -0.257, 0, 17.883, 1, 2, 17.967, 1, 0, 18.283, -0.322, 0, 18.317, -0.307, 0, 18.35, -0.317, 0, 18.567, 1, 2, 18.583, 1, 0, 18.6, -1, 2, 18.7, -1, 0, 18.983, 0.345, 0, 19.267, -0.277, 0, 19.45, 0.016, 0, 19.517, -0.234, 0, 19.583, -0.162, 0, 19.75, -1, 2, 19.783, -1, 0, 19.8, 1, 2, 19.95, 1, 0, 19.967, -1, 2, 20.15, -1, 0, 20.383, 0.396, 0, 20.7, -0.213, 0, 21.083, 0.153, 0, 21.367, -0.069, 0, 21.667, 0.028, 0, 21.967, -0.01, 0, 22.25, 0.004, 2, 22.283, 0.004, 0, 22.533, -0.001, 2, 22.55, -0.001, 2, 22.567, -0.001, 2, 22.6, -0.001, 0, 22.817, 0, 2, 22.833, 0, 2, 22.85, 0, 2, 22.917, 0, 2, 22.933, 0, 2, 22.967, 0, 2, 22.983, 0, 2, 23, 0, 2, 23.017, 0, 2, 23.033, 0, 2, 23.05, 0, 2, 23.083, 0, 2, 23.1, 0, 2, 23.3, 0, 2, 23.317, 0, 2, 29.217, 0, 0, 29.283, -0.305, 0, 29.333, -0.2, 0, 29.433, -1, 2, 29.45, -1, 0, 29.467, 1, 2, 29.5, 1, 0, 29.533, 0.831, 2, 29.55, 0.831, 0, 29.583, 0.863, 0, 29.833, -0.314, 0, 30.1, 0.153, 1, 30.172, 0.153, 30.245, 0.108, 30.317, 0, 1, 30.322, -0.008, 30.328, -0.057, 30.333, -0.057, 2, 30.583, -0.057]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 4.75, 0, 0, 4.783, -5, 2, 10.35, -5, 0, 10.367, 0, 2, 10.433, 0, 2, 10.583, 0, 2, 20.8, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.4, 0, 1, 2.406, 0.014, 2.411, 0.029, 2.417, 0.043, 1, 2.539, 0.362, 2.661, 0.681, 2.783, 1, 2, 3.833, 1, 2, 3.867, 1, 2, 4, 1, 2, 4.083, 1, 2, 4.267, 1, 1, 4.411, 1, 4.556, 0.637, 4.7, 0.015, 1, 4.706, 0, 4.711, 0, 4.717, 0, 2, 9.65, 0, 2, 9.917, 0, 2, 10.517, 0, 2, 13.483, 0, 2, 17.333, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamShoulderLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.867, 7.8, 2, 1.983, 7.8, 1, 2.122, 5.2, 2.261, 2.6, 2.4, 0, 2, 2.417, 0, 2, 2.783, 0, 2, 3.867, 0, 2, 4, 0, 2, 4.083, 0, 2, 4.267, 0, 2, 4.7, 0, 2, 4.717, 0, 2, 4.933, 0, 2, 9.65, 0, 2, 9.917, 0, 2, 10.517, 0, 2, 12.417, 0, 2, 13.483, 0, 2, 17.333, 0, 2, 18.6, 0, 2, 20.083, 0, 2, 21.15, 0, 2, 29.267, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.867, -1.14, 2, 1.983, -1.14, 1, 2.1, -1.14, 2.216, -0.882, 2.333, 0, 1, 2.361, 0.21, 2.389, 1.169, 2.417, 1.986, 1, 2.445, 2.803, 2.472, 3.6, 2.5, 3.6, 0, 2.783, 0, 2, 3.867, 0, 2, 4, 0, 2, 4.083, 0, 2, 4.267, 0, 1, 4.417, 0, 4.567, 0.496, 4.717, 1.111, 1, 4.789, 1.407, 4.861, 1.44, 4.933, 1.44, 2, 9.65, 1.44, 1, 9.806, 1.44, 9.961, 1.47, 10.117, 1.255, 1, 10.272, 1.04, 10.428, 0, 10.583, 0, 2, 12.417, 0, 0, 12.883, 2.52, 0, 13.317, 0, 2, 13.483, 0, 2, 17.333, 0, 0, 18.6, 3.2, 2, 20.083, 3.2, 0, 21.15, 2.7, 2, 29.267, 2.7, 0, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.867, -24.54, 2, 1.983, -24.54, 0, 2.4, 0, 2, 2.417, 0, 2, 2.783, 0, 2, 3.867, 0, 2, 4, 0, 2, 4.267, 0, 1, 4.417, 0, 4.567, -2.175, 4.717, -4.863, 1, 4.789, -6.157, 4.861, -6.3, 4.933, -6.3, 2, 9.65, -6.3, 1, 9.739, -6.3, 9.828, -6.11, 9.917, -4.863, 1, 10.117, -2.058, 10.317, 0, 10.517, 0, 2, 12.417, 0, 2, 13.483, 0, 2, 17.333, 0, 2, 18.6, 0, 2, 20.083, 0, 2, 21.15, 0, 2, 29.267, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 30, 2, 0.217, 30, 1, 0.278, 30, 0.339, 31.026, 0.4, 26.757, 1, 0.556, 15.89, 0.711, -3.42, 0.867, -3.42, 2, 1.983, -3.42, 0, 2.4, 0, 2, 2.417, 0, 2, 2.783, 0, 2, 3.867, 0, 2, 4, 0, 2, 4.083, 0, 2, 4.267, 0, 0, 4.717, 0.507, 1, 4.722, 0.507, 4.728, 0.274, 4.733, 0.173, 1, 4.8, -1.034, 4.866, -1.56, 4.933, -1.56, 2, 9.65, -1.56, 1, 9.728, -0.291, 9.805, 0.978, 9.883, 2.247, 1, 10.222, 6.883, 10.561, 8.4, 10.9, 8.4, 0, 12.417, 7.5, 1, 12.65, 7.5, 12.884, 24.177, 13.117, 28.486, 1, 13.239, 30.743, 13.361, 30, 13.483, 30, 2, 17.333, 30, 1, 17.45, 30, 17.566, 30.478, 17.683, 28.789, 1, 17.878, 25.974, 18.072, 5.93, 18.267, 2.029, 1, 18.378, -0.2, 18.489, 0.3, 18.6, 0.3, 2, 18.883, 0.3, 1, 19.033, 0.3, 19.183, 0.548, 19.333, 0.6, 1, 19.583, 0.687, 19.833, 0.682, 20.083, 0.78, 1, 20.244, 0.843, 20.406, 5.927, 20.567, 5.927, 0, 21.15, -1.3, 2, 24.683, -1.3, 1, 24.861, -1.3, 25.039, -0.943, 25.217, -0.94, 1, 26.567, -0.917, 27.917, -0.901, 29.267, -0.88, 1, 29.35, -0.879, 29.434, 0.859, 29.517, 3.163, 1, 29.617, 5.928, 29.717, 23.801, 29.817, 27.055, 1, 29.911, 30.128, 30.006, 30, 30.1, 30, 2, 30.317, 30, 2, 30.583, 30]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.533, -1, 1, 0.583, -1, 0.633, -1.249, 0.683, 3, 1, 0.744, 8.194, 0.806, 22, 0.867, 22, 2, 1.983, 22, 1, 2.122, 14.667, 2.261, 7.333, 2.4, 0, 2, 2.417, 0, 2, 2.783, 0, 2, 3.867, 0, 2, 4, 0, 2, 4.083, 0, 2, 4.267, 0, 0, 4.7, -29.564, 1, 4.706, -29.564, 4.711, 0, 4.717, 0, 2, 4.933, 0, 2, 9.65, 0, 2, 9.917, 0, 2, 10.517, 0, 2, 12.417, 0, 0, 12.817, -19, 0, 13.317, 0, 2, 13.483, 0, 2, 17.333, 0, 2, 18.6, 0, 2, 20.083, 0, 0, 21.15, -3.78, 2, 29.267, -3.78, 0, 29.65, -30, 0, 30, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1, 2, 0.217, -1, 2, 0.633, -1, 1, 0.639, -1, 0.644, 0.473, 0.65, 0.5, 1, 0.722, 0.857, 0.795, 1, 0.867, 1, 2, 2.417, 1, 2, 3.833, 1, 2, 3.867, 1, 2, 4, 1, 2, 4.267, 1, 2, 4.7, 1, 2, 4.717, 1, 2, 9.65, 1, 2, 9.917, 1, 2, 10.383, 1, 2, 10.5, 1, 0, 10.517, -1, 2, 13.483, -1, 2, 17.333, -1, 0, 18.6, 1, 2, 20.083, 1, 2, 21.15, 1, 2, 29.267, 1, 2, 29.65, 1, 2, 29.667, -1, 2, 30.317, -1, 2, 30.583, -1]}, {"Target": "Parameter", "Id": "ParamForearmL_HandL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.867, -1.5, 2, 1.983, -1.5, 1, 2.122, -1, 2.261, -0.5, 2.4, 0, 2, 2.417, 0, 2, 2.783, 0, 2, 3.867, 0, 2, 4, 0, 2, 4.083, 0, 2, 4.267, 0, 2, 4.7, 0, 2, 4.717, 0, 2, 4.933, 0, 2, 9.65, 0, 2, 9.917, 0, 2, 10.517, 0, 2, 12.417, 0, 2, 13.483, 0, 2, 17.333, 0, 2, 18.6, 0, 2, 20.083, 0, 2, 21.15, 0, 2, 29.267, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.867, -1, 2, 1.983, -1, 1, 2.122, -0.667, 2.261, -0.333, 2.4, 0, 2, 2.417, 0, 2, 2.783, 0, 2, 3.867, 0, 2, 4, 0, 2, 4.083, 0, 2, 4.267, 0, 1, 4.417, 0, 4.567, 0.007, 4.717, 0.026, 1, 4.789, 0.035, 4.861, 0.061, 4.933, 0.064, 1, 5.116, 0.071, 5.3, 0.071, 5.483, 0.079, 1, 5.639, 0.086, 5.794, 0.119, 5.95, 0.119, 1, 6.394, 0.119, 6.839, 0.118, 7.283, 0.115, 1, 7.594, 0.113, 7.906, 0.112, 8.217, 0.108, 1, 8.695, 0.101, 9.172, 0.081, 9.65, 0.064, 1, 9.739, 0.061, 9.828, 0.026, 9.917, 0.026, 0, 10.383, 0.12, 0, 11.017, -0.089, 1, 11.484, -0.089, 11.95, -0.075, 12.417, 0, 1, 12.556, 0.022, 12.694, 0.314, 12.833, 0.314, 0, 13.2, -0.071, 1, 13.294, -0.071, 13.389, -0.023, 13.483, 0, 1, 13.633, 0.037, 13.783, 0.042, 13.933, 0.042, 0, 15.017, -0.073, 0, 16.017, 0.052, 0, 16.867, -0.037, 0, 17.333, 0, 0, 17.783, -0.42, 0, 18.6, 0.29, 2, 20.083, 0.29, 0, 21.15, -0.21, 0, 21.75, -0.194, 1, 22.033, -0.194, 22.317, -0.2, 22.6, -0.202, 1, 22.806, -0.203, 23.011, -0.203, 23.217, -0.203, 0, 24.317, -0.187, 0, 29.267, -0.21, 1, 29.417, -0.21, 29.567, -0.113, 29.717, 0.1, 1, 29.811, 0.234, 29.906, 0.312, 30, 0.312, 0, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamHandLDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10, 2, 0.45, 10, 2, 0.467, 6, 2, 0.867, 6, 2, 1.983, 6, 2, 2.417, 10, 2, 2.8, 10, 2, 3.833, 10, 2, 3.867, 10, 2, 4, 10, 2, 4.7, 10, 1, 4.706, 8.667, 4.711, 7.333, 4.717, 6, 2, 9.65, 6, 2, 9.917, 6, 2, 9.933, 3, 2, 10.517, 3, 2, 12.883, 3, 2, 12.9, 10, 2, 13.483, 10, 2, 17.333, 10, 2, 17.95, 10, 2, 17.967, 6, 2, 20.567, 6, 3, 21.15, 5, 2, 29.267, 5, 2, 29.817, 5, 3, 30.317, 10, 2, 30.583, 10]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.867, -1, 2, 1.983, -1, 1, 2.122, -0.667, 2.261, -0.333, 2.4, 0, 2, 2.417, 0, 2, 2.783, 0, 2, 3.867, 0, 2, 4, 0, 2, 4.083, 0, 2, 4.267, 0, 2, 4.7, 0, 2, 4.717, 0, 2, 4.933, 0, 2, 9.65, 0, 2, 9.917, 0, 2, 10.517, 0, 2, 12.417, 0, 2, 13.483, 0, 0, 14.45, 0.8, 0, 15.417, 0, 0, 16.367, 0.4, 0, 17.333, 0, 2, 18.6, 0, 2, 20.083, 0, 2, 20.567, 0, 0, 20.583, -1, 0, 20.983, 0.6, 0, 21.15, 0, 2, 29.167, 0, 2, 29.267, 0, 0, 29.817, -1, 0, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.867, -1, 2, 1.983, -1, 1, 2.122, -0.667, 2.261, -0.333, 2.4, 0, 2, 2.417, 0, 2, 2.783, 0, 2, 3.867, 0, 2, 4, 0, 2, 4.083, 0, 2, 4.267, 0, 1, 4.417, 0, 4.567, 0.345, 4.717, 0.772, 1, 4.789, 0.978, 4.861, 1, 4.933, 1, 2, 9.65, 1, 0, 9.917, -1, 0, 10.517, 0, 2, 12.417, 0, 2, 13.483, 0, 0, 14.45, 0.4, 0, 15.417, 0, 0, 16.367, 0.5, 1, 16.689, 0.5, 17.011, 0.454, 17.333, 0, 1, 17.544, -0.298, 17.756, -1, 17.967, -1, 0, 18.217, 1, 0, 18.6, 0, 2, 20.083, 0, 0, 20.85, -1, 2, 21.15, -1, 2, 29.267, -1, 0, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.867, 7.74, 2, 1.983, 7.74, 0, 2.4, 0, 2, 2.417, 0, 2, 2.783, 0, 2, 3.867, 0, 2, 4, 0, 2, 4.083, 0, 2, 4.267, 0, 2, 4.7, 0, 1, 4.706, 0.978, 4.711, 1.955, 4.717, 2.933, 1, 4.789, 3.551, 4.861, 3.8, 4.933, 3.8, 2, 9.65, 3.8, 1, 9.739, 3.8, 9.828, 3.685, 9.917, 2.933, 1, 10.117, 1.24, 10.317, 0, 10.517, 0, 2, 12.417, 0, 2, 13.483, 0, 2, 17.333, 0, 2, 18.6, 0, 2, 20.083, 0, 2, 21.15, 0, 2, 29.267, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.867, 17.4, 2, 1.983, 17.4, 0, 2.4, 0, 2, 2.417, 0, 2, 2.783, 0, 2, 3.867, 0, 2, 4, 0, 2, 4.083, 0, 2, 4.267, 0, 2, 4.7, 0, 2, 4.717, 0, 2, 4.933, 0, 2, 9.65, 0, 2, 9.917, 0, 2, 10.517, 0, 2, 12.417, 0, 2, 13.483, 0, 2, 17.333, 0, 2, 18.6, 0, 2, 20.083, 0, 2, 21.15, 0, 2, 29.267, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.15, 1, 2, 29.983, 1, 0, 30.15, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.4, 0, 2, 2.417, 0, 2, 2.783, 0, 2, 3.867, 0, 2, 4, 0, 2, 4.083, 0, 2, 4.7, 0, 2, 4.717, 0, 2, 9.65, 0, 2, 9.917, 0, 2, 10.517, 0, 2, 13.483, 0, 2, 17.333, 0, 2, 29.483, 0, 0, 29.733, 1, 2, 30, 1, 0, 30.133, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.4, 0, 1, 2.406, 0.014, 2.411, 0.029, 2.417, 0.043, 1, 2.539, 0.362, 2.661, 0.681, 2.783, 1, 2, 3.833, 1, 2, 3.867, 1, 2, 4, 1, 2, 4.083, 1, 2, 4.267, 1, 1, 4.411, 1, 4.556, 0.637, 4.7, 0.015, 1, 4.706, 0, 4.711, 0, 4.717, 0, 2, 9.65, 0, 2, 9.917, 0, 2, 10.517, 0, 2, 13.483, 0, 2, 17.333, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamShoulderRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.867, 10, 2, 1.983, 10, 1, 2.122, 6.667, 2.261, 3.333, 2.4, 0, 2, 2.417, 0, 2, 2.783, 0, 2, 3.867, 0, 2, 4, 0, 2, 4.083, 0, 2, 4.267, 0, 2, 4.7, 0, 2, 4.717, 0, 2, 4.933, 0, 2, 9.65, 0, 2, 9.917, 0, 2, 10.517, 0, 2, 12.417, 0, 2, 13.317, 0, 2, 13.483, 0, 2, 17.333, 0, 2, 18.6, 0, 2, 20.083, 0, 2, 21.15, 0, 2, 29.267, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.133, 0, 0, 0.867, 30, 2, 1.983, 30, 1, 2.122, 30, 2.261, 18.569, 2.4, 0, 1, 2.406, -0.743, 2.411, -0.838, 2.417, -0.894, 1, 2.445, -1.176, 2.472, -1.26, 2.5, -1.26, 0, 2.783, 0, 2, 3.867, 0, 2, 4, 0, 2, 4.083, 0, 2, 4.267, 0, 0, 4.7, -1.96, 0, 4.933, 0, 2, 9.65, 0, 2, 9.917, 0, 2, 10.517, 0, 2, 12.417, 0, 0, 12.883, -1.86, 0, 13.317, 0, 2, 13.483, 0, 2, 17.333, 0, 0, 18.6, -1.5, 2, 20.083, -1.5, 2, 21.15, -1.5, 2, 29.267, -1.5, 0, 29.733, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.867, -23.4, 2, 1.983, -23.4, 0, 2.4, 0, 2, 2.417, 0, 2, 2.783, 0, 2, 3.867, 0, 2, 4, 0, 2, 4.083, 0, 2, 4.267, 0, 1, 4.417, 0, 4.567, -2.458, 4.717, -5.495, 1, 4.789, -6.957, 4.861, -7.12, 4.933, -7.12, 2, 9.65, -7.12, 1, 9.739, -7.12, 9.828, -6.904, 9.917, -5.495, 1, 10.117, -2.325, 10.317, 0, 10.517, 0, 2, 12.417, 0, 2, 13.317, 0, 2, 13.483, 0, 2, 17.333, 0, 2, 18.6, 0, 2, 20.083, 0, 2, 21.15, 0, 2, 29.267, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "XIONGJIYA3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.25, 1, 2, 2.417, 1, 2, 3.867, 1, 2, 4, 1, 2, 4.7, 1, 2, 4.717, 1, 2, 9.65, 1, 2, 9.917, 1, 2, 10.517, 1, 2, 13.283, 1, 0, 13.483, 0, 2, 17.333, 0, 0, 17.617, 1, 2, 29.383, 1, 0, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.133, 0, 0, 0.867, 2.88, 2, 1.983, 2.88, 0, 2.4, 0, 2, 2.417, 0, 2, 2.783, 0, 2, 3.867, 0, 2, 4, 0, 2, 4.083, 0, 2, 4.267, 0, 0, 4.717, -0.09, 1, 4.722, -0.09, 4.728, 0.076, 4.733, 0.186, 1, 4.8, 1.504, 4.866, 2.1, 4.933, 2.1, 2, 9.65, 2.1, 1, 9.728, 0.83, 9.805, -0.44, 9.883, -1.71, 1, 10.222, -6.923, 10.561, -8.7, 10.9, -8.7, 1, 11.406, -8.7, 11.911, -8.544, 12.417, -7.5, 1, 12.617, -7.087, 12.817, -4.089, 13.017, -2.044, 1, 13.117, -1.022, 13.217, 0, 13.317, 0, 2, 13.483, 0, 2, 17.333, 0, 2, 17.5, 0, 0, 17.733, -4.875, 1, 17.839, -4.875, 17.944, -1.933, 18.05, -1.027, 1, 18.233, 0.547, 18.417, 0.7, 18.6, 0.7, 2, 18.883, 0.7, 1, 19.033, 0.7, 19.183, 0.452, 19.333, 0.4, 1, 19.583, 0.313, 19.833, 0.318, 20.083, 0.22, 1, 20.244, 0.157, 20.406, -3.285, 20.567, -3.285, 0, 21.15, 2.1, 2, 24.683, 2.1, 0, 25.217, 1.68, 2, 29.267, 1.68, 0, 29.733, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.867, 30, 2, 1.983, 30, 2, 2.4, 0, 2, 2.417, 0, 2, 2.783, 0, 2, 3.867, 0, 2, 4, 0, 2, 4.083, 0, 2, 4.267, 0, 2, 4.7, 0, 2, 4.717, 0, 2, 4.933, 0, 2, 9.65, 0, 2, 9.917, 0, 2, 10.517, 0, 2, 12.417, 0, 2, 13.317, 0, 2, 13.483, 0, 2, 17.333, 0, 2, 18.6, 0, 2, 20.083, 0, 2, 21.15, 0, 2, 29.267, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.133, 0, 0, 0.867, -1, 2, 1.983, -1, 1, 2.122, -0.667, 2.261, -0.333, 2.4, 0, 2, 2.417, 0, 2, 2.783, 0, 2, 3.867, 0, 2, 4, 0, 2, 4.083, 0, 2, 4.267, 0, 1, 4.417, 0, 4.567, 0.006, 4.717, 0.025, 1, 4.789, 0.034, 4.861, 0.06, 4.933, 0.062, 1, 5.116, 0.066, 5.3, 0.067, 5.483, 0.072, 1, 5.639, 0.077, 5.794, 0.11, 5.95, 0.118, 1, 6.394, 0.14, 6.839, 0.146, 7.283, 0.146, 1, 7.594, 0.146, 7.906, 0.139, 8.217, 0.125, 1, 8.695, 0.104, 9.172, 0.086, 9.65, 0.062, 1, 9.739, 0.057, 9.828, 0.025, 9.917, 0.025, 0, 10.383, 0.148, 0, 11.017, -0.086, 0, 12.417, 0, 2, 13.317, 0, 2, 13.483, 0, 2, 17.333, 0, 0, 17.733, 0.05, 0, 17.983, -0.342, 1, 18.028, -0.342, 18.072, -0.143, 18.117, -0.08, 1, 18.278, 0.147, 18.439, 0.21, 18.6, 0.21, 2, 20.083, 0.21, 0, 21.15, -0.25, 2, 29.267, -0.25, 1, 29.272, -0.25, 29.278, -0.173, 29.283, -0.169, 1, 29.433, -0.052, 29.583, 0, 29.733, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamHandRDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.45, 0, 2, 0.467, 6, 2, 0.867, 6, 2, 1.983, 6, 2, 2.4, 0, 2, 2.417, 0, 2, 2.783, 0, 2, 3.833, 0, 2, 3.867, 0, 2, 4, 0, 2, 4.083, 0, 2, 4.7, 0, 2, 4.717, 6, 2, 9.65, 6, 2, 9.917, 6, 2, 9.933, 3, 2, 10.517, 3, 2, 13.033, 3, 2, 13.05, 0, 2, 13.217, 0, 2, 13.483, 0, 2, 17.333, 0, 2, 17.733, 0, 3, 18.6, 6, 2, 20.083, 6, 2, 20.567, 6, 3, 21.15, 5, 2, 29.267, 5, 3, 29.283, 0, 3, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.867, 1, 2, 1.983, 1, 1, 2.122, 0.667, 2.261, 0.333, 2.4, 0, 2, 2.417, 0, 2, 2.783, 0, 2, 3.867, 0, 2, 4, 0, 2, 4.083, 0, 2, 4.267, 0, 2, 4.7, 0, 2, 4.717, 0, 2, 4.933, 0, 2, 9.65, 0, 2, 9.917, 0, 2, 10.517, 0, 2, 12.417, 0, 0, 13.2, -1, 0, 13.317, 0, 2, 13.483, 0, 2, 17.333, 0, 2, 17.75, 0, 2, 18.6, 0, 2, 20.083, 0, 2, 20.567, 0, 0, 20.583, -1, 0, 20.983, 0.5, 0, 21.15, 0, 2, 25.5, 0, 2, 29.167, 0, 0, 29.333, -1, 0, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.867, -1, 2, 1.983, -1, 1, 2.122, -0.667, 2.261, -0.333, 2.4, 0, 2, 2.417, 0, 2, 2.783, 0, 2, 3.867, 0, 2, 4, 0, 2, 4.083, 0, 2, 4.267, 0, 2, 4.7, 0, 1, 4.706, 0.257, 4.711, 0.515, 4.717, 0.772, 1, 4.789, 0.935, 4.861, 1, 4.933, 1, 2, 9.65, 1, 0, 9.917, -1, 0, 10.517, 0, 2, 12.417, 0, 2, 13.317, 0, 2, 13.483, 0, 2, 17.333, 0, 0, 17.75, -1, 0, 18.133, 1, 0, 18.6, 0, 2, 20.083, 0, 0, 20.85, -1, 0, 21.15, 0, 2, 29.267, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.867, -10.22, 2, 1.983, -10.22, 0, 2.4, 0, 2, 2.417, 0, 2, 2.783, 0, 2, 3.867, 0, 2, 4, 0, 2, 4.083, 0, 2, 4.267, 0, 2, 4.7, 0, 1, 4.706, -1.878, 4.711, -3.756, 4.717, -5.634, 1, 4.789, -6.818, 4.861, -7.3, 4.933, -7.3, 2, 9.65, -7.3, 1, 9.739, -7.3, 9.828, -7.078, 9.917, -5.634, 1, 10.117, -2.384, 10.317, 0, 10.517, 0, 2, 12.417, 0, 2, 13.317, 0, 2, 13.483, 0, 2, 17.333, 0, 2, 18.6, 0, 2, 20.083, 0, 2, 21.15, 0, 2, 29.267, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.867, 2.86, 2, 1.983, 2.86, 0, 2.4, 0, 2, 2.417, 0, 2, 2.783, 0, 2, 3.867, 0, 2, 4, 0, 2, 4.083, 0, 2, 4.267, 0, 2, 4.7, 0, 2, 4.717, 0, 2, 4.933, 0, 2, 9.65, 0, 2, 9.917, 0, 2, 10.517, 0, 2, 12.417, 0, 2, 13.317, 0, 2, 13.483, 0, 2, 17.333, 0, 2, 18.6, 0, 2, 20.083, 0, 2, 21.15, 0, 2, 29.267, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.167, 0, 2, 0.183, 1, 2, 2.417, 1, 2, 3.867, 1, 2, 4, 1, 2, 4.7, 1, 2, 4.717, 1, 2, 9.65, 1, 2, 9.917, 1, 2, 10.517, 1, 2, 13.217, 1, 0, 13.233, 0, 2, 13.483, 0, 2, 17.333, 0, 2, 17.483, 0, 2, 17.5, 1, 2, 29.267, 1, 0, 29.283, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.167, 0, 2, 0.183, 1, 2, 2.417, 1, 2, 3.867, 1, 2, 4, 1, 2, 4.7, 1, 2, 4.717, 1, 2, 9.65, 1, 2, 9.917, 1, 2, 10.517, 1, 2, 13.217, 1, 0, 13.233, 0, 2, 13.483, 0, 2, 17.333, 0, 2, 17.483, 0, 2, 17.5, 1, 2, 29.267, 1, 1, 29.272, 0.667, 29.278, 0.333, 29.283, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.183, 0.05, 0, 0.867, 0, 2, 1.983, 0, 2, 2.4, 0, 2, 2.417, 0, 2, 2.783, 0, 2, 3.867, 0, 2, 4, 0, 2, 4.083, 0, 2, 4.267, 0, 1, 4.411, -0.014, 4.556, -0.027, 4.7, -0.041, 1, 4.706, -0.028, 4.711, -0.015, 4.717, -0.002, 1, 4.789, -0.001, 4.861, 0, 4.933, 0, 2, 9.65, 0, 1, 9.739, 0, 9.828, 0.001, 9.917, -0.002, 1, 10.117, -0.008, 10.317, -0.021, 10.517, -0.031, 1, 11.417, -0.075, 12.317, -0.098, 13.217, -0.098, 0, 13.483, 0, 2, 17.333, 0, 0, 17.5, -0.117, 0, 19.233, -0.078, 0, 20.817, -0.117, 0, 21.15, 0.032, 2, 29.283, 0.032, 0, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.183, 0.092, 0, 0.867, 0, 2, 1.983, 0, 2, 2.4, 0, 2, 2.417, 0, 2, 2.783, 0, 2, 3.867, 0, 2, 4, 0, 2, 4.083, 0, 2, 4.267, 0, 1, 4.411, 0.004, 4.556, 0.007, 4.7, 0.011, 1, 4.706, 0.008, 4.711, 0.004, 4.717, 0.001, 1, 4.789, 0, 4.861, 0, 4.933, 0, 2, 9.65, 0, 1, 9.739, 0, 9.828, 0, 9.917, 0.001, 1, 10.117, 0.004, 10.317, 0.01, 10.517, 0.016, 1, 11.417, 0.041, 12.317, 0.054, 13.217, 0.054, 0, 13.483, 0, 2, 17.333, 0, 0, 17.5, -0.015, 2, 20.817, -0.015, 0, 21.15, -0.094, 2, 29.283, -0.094, 0, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.183, 0.016, 0, 0.867, 0, 2, 1.983, 0, 2, 2.4, 0, 2, 2.417, 0, 2, 2.783, 0, 2, 3.867, 0, 2, 4, 0, 2, 4.083, 0, 2, 4.267, 0, 1, 4.411, 0.027, 4.556, 0.053, 4.7, 0.08, 1, 4.706, 0.055, 4.711, 0.03, 4.717, 0.005, 1, 4.789, 0.001, 4.861, 0, 4.933, 0, 2, 9.65, 0, 1, 9.739, 0, 9.828, -0.001, 9.917, 0.005, 1, 10.117, 0.02, 10.317, 0.053, 10.517, 0.08, 1, 11.417, 0.2, 12.317, 0.262, 13.217, 0.262, 0, 13.483, 0, 2, 17.333, 0, 0, 17.5, 0.128, 0, 19.233, 0.071, 1, 19.761, 0.071, 20.289, 0.083, 20.817, 0.128, 1, 20.928, 0.137, 21.039, 0.226, 21.15, 0.226, 2, 29.283, 0.226, 0, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 4.867, 0, 2, 6.983, 0, 0, 7.383, -7, 2, 7.65, -7, 0, 8.083, 6, 2, 8.417, 6, 0, 8.75, -0.759, 1, 9.05, -0.759, 9.35, -0.615, 9.65, 0, 1, 9.739, 0.182, 9.828, 1, 9.917, 1, 0, 10.317, -2, 1, 11.372, -2, 12.428, -1.62, 13.483, 0, 1, 13.75, 0.409, 14.016, 4.138, 14.283, 4.138, 1, 15.061, 4.138, 15.839, 3.588, 16.617, 1.738, 1, 16.856, 1.17, 17.094, 0, 17.333, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.133, -2.728, 0, 0.367, 5.594, 0, 0.883, -8, 2, 1.55, -8, 0, 2.183, -6, 0, 2.45, -14, 1, 2.617, -14, 2.783, 13.706, 2.95, 14, 1, 3.45, 14.883, 3.95, 15, 4.45, 15, 0, 4.867, -1.911, 1, 5.122, -1.911, 5.378, -1.924, 5.633, 0, 1, 5.928, 2.217, 6.222, 6, 6.517, 6, 1, 6.817, 6, 7.117, 1.18, 7.417, -6, 1, 7.667, -11.983, 7.917, -14, 8.167, -14, 1, 8.661, -14, 9.156, -10.515, 9.65, 0, 1, 9.739, 1.89, 9.828, 12, 9.917, 12, 1, 9.995, 12, 10.072, 8.649, 10.15, 0, 1, 10.206, -6.178, 10.261, -20.174, 10.317, -21, 1, 10.456, -23.064, 10.594, -23.337, 10.733, -23.337, 0, 11.383, -13.674, 0, 12.05, -15.62, 1, 12.239, -15.62, 12.428, -15.647, 12.617, -14.746, 1, 12.728, -14.216, 12.839, 4.282, 12.95, 4.282, 1, 13.128, 4.282, 13.305, 2.638, 13.483, 0, 1, 13.816, -4.946, 14.15, -7.13, 14.483, -7.13, 0, 15.417, -0.006, 0, 16.417, -7.13, 0, 17.333, 0, 0, 17.467, -2.728, 0, 17.7, 5.594, 0, 18.217, -8, 2, 18.883, -8, 0, 19.517, -6, 0, 19.9, -20.733, 0, 20.5, 14.174, 1, 20.622, 14.174, 20.745, -10.306, 20.867, -19.801, 1, 20.961, -27.138, 21.056, -26.593, 21.15, -26.593, 0, 21.567, 1.177, 0, 22.05, -12.086, 0, 22.533, -0.95, 0, 23.083, -15, 0, 23.9, -2.336, 0, 24.85, -12.305, 0, 26.733, -1, 0, 28.583, -13.376, 0, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.133, -7.98, 2, 1.95, -7.98, 1, 2.367, -7.98, 2.783, -7.18, 3.2, -4.5, 1, 3.589, -1.999, 3.978, 0, 4.367, 0, 0, 5.067, -30, 0, 6.45, 13.662, 0, 7.667, -22, 0, 9.083, 8, 0, 10.417, 0, 2, 10.517, 0, 2, 13.483, 0, 2, 17.333, 0, 0, 17.95, 15, 0, 19.8, -17, 0, 21.667, 9, 0, 25.3, -11, 0, 27.417, 16.382, 0, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.783, 0, 0, 3.717, -1, 0, 4.7, 0, 2, 4.717, 0, 2, 4.75, 0, 0, 5.683, -1, 0, 6.817, 0, 1, 7.272, 0, 7.728, -0.018, 8.183, -0.96, 1, 8.955, -2.557, 9.728, -4.28, 10.5, -4.28, 1, 11.494, -4.28, 12.489, -3.452, 13.483, 0, 1, 13.805, 1.119, 14.128, 4.783, 14.45, 4.783, 1, 14.772, 4.783, 15.095, 3.216, 15.417, 0, 1, 15.734, -3.161, 16.05, -4.783, 16.367, -4.783, 0, 17.333, 0, 2, 18.983, 0, 2, 19.683, 0, 2, 20.533, 0, 2, 22.183, 0, 0, 23.55, -0.96, 0, 29.367, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -7.795, 1, 0.5, -7.795, 1, -5.197, 1.5, 0, 1, 2, 5.197, 2.5, 7.795, 3, 7.795, 0, 4.367, -7.795, 2, 4.7, -7.795, 1, 4.706, -7.795, 4.711, 7.568, 4.717, 7.578, 1, 4.8, 7.732, 4.884, 7.795, 4.967, 7.795, 1, 5.467, 7.795, 5.967, 5.197, 6.467, 0, 1, 6.967, -5.197, 7.467, -7.795, 7.967, -7.795, 0, 9.65, 7.801, 1, 9.739, 7.801, 9.828, 7.831, 9.917, 7.578, 1, 10.117, 7.009, 10.317, 6.027, 10.517, 5.098, 1, 10.856, 3.523, 11.194, 2.25, 11.533, 0, 1, 12.183, -4.316, 12.833, -7.795, 13.483, -7.795, 1, 13.805, -7.795, 14.128, -5.197, 14.45, 0, 1, 14.772, 5.197, 15.095, 7.795, 15.417, 7.795, 1, 15.734, 7.795, 16.05, 5.151, 16.367, 0, 1, 16.689, -5.241, 17.011, -7.795, 17.333, -7.795, 1, 17.833, -7.795, 18.333, -5.197, 18.833, 0, 1, 19.333, 5.197, 19.833, 7.795, 20.333, 7.795, 1, 20.833, 7.795, 21.333, 5.197, 21.833, 0, 1, 22.333, -5.197, 22.833, -7.795, 23.333, -7.795, 0, 24.833, 0, 0, 30.317, -7.795, 2, 30.583, -7.795]}, {"Target": "Parameter", "Id": "ParamBodyStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 0.044, 0, 0.089, 4.872, 0.133, 6.338, 1, 0.228, 9.454, 0.322, 10, 0.417, 10, 0, 0.833, -3, 0, 0.967, 1.044, 1, 1.034, 1.044, 1.1, 0.677, 1.167, 0, 1, 1.295, -1.298, 1.422, -1.92, 1.55, -1.92, 0, 2.15, 2.409, 0, 2.433, 0, 0, 2.783, 10, 1, 2.911, 10, 3.039, 7.84, 3.167, 7.281, 1, 3.622, 5.289, 4.078, 4.095, 4.533, 2.272, 1, 4.589, 2.05, 4.644, 0.916, 4.7, 0.916, 0, 5.133, 7.281, 0, 6.217, -7, 0, 7.333, 0.25, 0, 8.517, -5.184, 1, 8.778, -5.184, 9.039, -4.071, 9.3, 0.094, 1, 9.417, 1.955, 9.533, 9.052, 9.65, 9.702, 1, 9.728, 10, 9.805, 10, 9.883, 10, 1, 9.894, 10, 9.906, 10, 9.917, 9.743, 1, 10.05, 4.239, 10.184, 0, 10.317, 0, 1, 10.384, 0, 10.45, 0.67, 10.517, 1.519, 1, 10.572, 2.227, 10.628, 2.409, 10.683, 2.409, 0, 11.483, -1.92, 1, 11.65, -1.92, 11.816, -1.289, 11.983, 0, 1, 12.072, 0.687, 12.161, 1.044, 12.25, 1.044, 0, 12.417, -3, 0, 12.983, 10, 1, 13.105, 10, 13.228, 9.376, 13.35, 6.338, 1, 13.394, 5.233, 13.439, 0, 13.483, 0, 0, 14.45, 4.293, 1, 14.772, 4.293, 15.095, 2.877, 15.417, 0, 1, 15.734, -2.827, 16.05, -4.264, 16.367, -4.264, 1, 16.689, -4.264, 17.011, -3.145, 17.333, 0, 1, 17.378, 0.434, 17.422, 4.999, 17.467, 6.338, 1, 17.561, 9.183, 17.656, 10, 17.75, 10, 0, 18.167, -3, 0, 18.417, 0, 0, 18.717, -1.92, 0, 19.267, 2.409, 0, 19.767, 0, 0, 20.117, 10, 1, 20.245, 10, 20.372, 10, 20.5, 7.281, 1, 20.661, 3.273, 20.822, -10, 20.983, -10, 0, 21.317, -3, 0, 21.75, -9, 0, 22.117, -4.543, 0, 22.5, -7, 0, 23.1, 0.25, 0, 24.2, -8, 0, 25.367, 10, 0, 26.25, -4, 0, 27.233, 6.862, 0, 28.317, -6.027, 0, 29.317, 2.688, 0, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.433, 0, 0, 1.233, 2.445, 0, 1.767, -0.586, 1, 1.939, -0.586, 2.111, 0.531, 2.283, 0.603, 1, 3.089, 0.94, 3.894, 1.026, 4.7, 1.026, 1, 4.706, 1.026, 4.711, -0.09, 4.717, -0.129, 1, 5.372, -4.745, 6.028, -7, 6.683, -7, 0, 8.183, 6, 0, 9.65, -1.33, 1, 9.739, -1.33, 9.828, -0.417, 9.917, -0.129, 1, 10.111, 0.502, 10.306, 0.603, 10.5, 0.603, 1, 10.506, 0.603, 10.511, 0.616, 10.517, 0.599, 1, 10.739, -0.084, 10.961, -0.586, 11.183, -0.586, 0, 11.9, 2.445, 0, 12.7, -1.073, 0, 13.183, 4.859, 0, 13.483, 0, 0, 14.933, 3.269, 0, 16.2, -2.807, 1, 16.578, -2.807, 16.955, -2.235, 17.333, 0, 1, 17.422, 0.526, 17.511, 4.859, 17.6, 4.859, 0, 17.967, -1.073, 0, 18.567, 2.445, 0, 19.1, -0.586, 0, 19.617, 0.603, 0, 22.05, -7, 0, 23.55, 6, 0, 25.083, -4, 0, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamLegLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.2, 0, 0, 0.967, 14.515, 0, 1.767, 2.532, 0, 2.75, 27.852, 0, 3.483, 0, 0, 4.717, 11.993, 0, 5.717, -9.742, 1, 6.122, -9.742, 6.528, -2.08, 6.933, 0, 1, 7.928, 5.1, 8.922, 7.688, 9.917, 11.993, 1, 10.022, 12.45, 10.128, 14.515, 10.233, 14.515, 1, 10.328, 14.515, 10.422, 14.883, 10.517, 14.159, 1, 11.506, 6.578, 12.494, 0, 13.483, 0, 0, 15.417, 14.515, 0, 17.333, 0, 0, 19.467, 14.515, 0, 20.517, -9.742, 1, 20.867, -9.742, 21.217, -5.187, 21.567, 0, 1, 22.284, 10.622, 23, 14.515, 23.717, 14.515, 1, 24.184, 14.515, 24.65, 3.1, 25.117, 2.532, 1, 26.85, 0.423, 28.584, 0, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamLegRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.2, 0, 0, 1.55, 7.732, 0, 2.267, -9.756, 0, 2.817, 30, 0, 3.483, 0, 0, 4.7, 4.21, 1, 4.706, 4.21, 4.711, -3.02, 4.717, -3.092, 1, 5.067, -7.597, 5.417, -9.756, 5.767, -9.756, 1, 6.156, -9.756, 6.544, -5.355, 6.933, 0, 1, 7.366, 5.967, 7.8, 7.732, 8.233, 7.732, 0, 9.65, -5.723, 1, 9.739, -5.723, 9.828, -4.127, 9.917, -3.092, 1, 10.117, -0.763, 10.317, 1.18, 10.517, 2.827, 1, 10.945, 6.349, 11.372, 7.732, 11.8, 7.732, 0, 13.483, 0, 0, 14.483, 7.732, 0, 16.417, -9.756, 1, 16.722, -9.756, 17.028, -5.074, 17.333, 0, 1, 17.7, 6.089, 18.066, 7.732, 18.433, 7.732, 0, 20.567, -9.756, 1, 20.9, -9.756, 21.234, -5.346, 21.567, 0, 1, 21.939, 5.97, 22.311, 7.732, 22.683, 7.732, 0, 24.833, -9.756, 0, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 0.044, 0, 0.089, 0.339, 0.133, 0.5, 1, 0.244, 0.902, 0.356, 1, 0.467, 1, 0, 4.7, 0.015, 0, 4.717, 1, 2, 9.65, 1, 2, 9.917, 1, 2, 10.517, 1, 2, 13.2, 1, 0, 13.217, 0, 2, 13.483, 0, 2, 17.333, 0, 1, 17.389, 0, 17.444, 0.387, 17.5, 0.5, 1, 17.694, 0.667, 17.889, 0.833, 18.083, 1, 2, 29.283, 1, 2, 29.717, 1, 1, 29.722, 1, 29.728, 0.51, 29.733, 0.5, 1, 29.928, 0.157, 30.122, 0, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "Physics_AngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 7.117, 0, 0, 7.45, -4.67, 0, 8.117, 4.657, 0, 8.817, -1.303, 0, 9.183, -0.22, 0, 9.317, -0.238, 0, 9.583, 0, 2, 30.317, 0, 2, 30.333, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "Physics_AngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.067, 0, 0, 0.183, -0.923, 0, 0.433, 3.649, 0, 0.933, -5.348, 0, 1.317, -4.571, 0, 1.567, -4.604, 0, 2.183, -3.381, 0, 2.5, -8.498, 0, 3, 9.652, 0, 3.35, 8.323, 0, 4.4, 8.641, 2, 4.45, 8.641, 0, 4.933, -2.223, 0, 6.533, 3.554, 0, 8.15, -8.167, 0, 9.967, 7.817, 0, 10.45, -15.255, 0, 11.417, -7.501, 0, 12.083, -9.07, 0, 13.017, 3.916, 0, 14.483, -4.163, 0, 15.417, 0.366, 0, 16.433, -4.243, 0, 17.333, 0.377, 0, 17.517, -1.262, 0, 17.767, 3.542, 0, 18.267, -5.352, 0, 18.65, -4.57, 0, 18.9, -4.604, 0, 19.517, -3.381, 0, 19.933, -12.86, 0, 20.517, 9.654, 0, 21.067, -17.116, 0, 21.617, 2.455, 0, 22.083, -7.765, 0, 22.533, 0, 2, 22.633, 0, 0, 23.117, -9.366, 0, 23.917, -1.005, 0, 24.867, -7.296, 0, 29.967, 0, 2, 30.317, 0, 2, 30.333, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "Physics_AngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 6.983, 0, 0, 7.25, 3.731, 0, 7.583, -0.601, 0, 7.667, -0.46, 0, 7.933, -3.736, 0, 8.267, 0.7, 0, 8.433, 0.239, 0, 8.65, 2.414, 0, 9, -0.26, 0, 9.3, -0.044, 0, 9.667, -0.158, 0, 9.733, 0.006, 0, 9.883, -0.162, 0, 10.15, 0.954, 0, 10.467, -0.209, 0, 10.8, 0.015, 0, 13.483, -0.111, 0, 13.5, -0.025, 0, 14.2, -3.357, 0, 14.55, 0.415, 0, 14.883, -0.017, 0, 16.9, 0.246, 0, 17.283, -0.014, 0, 17.333, 0, 2, 30.317, 0, 2, 30.333, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.133, 1.819, 0, 0.367, -3.729, 0, 0.883, 5.334, 2, 1.55, 5.334, 0, 2.183, 4, 0, 2.4, 8.444, 0, 2.767, -12.094, 0, 3.15, 11.262, 0, 3.567, -7.161, 0, 3.983, 4.568, 0, 4.4, -2.899, 0, 4.733, 6.093, 0, 5.1, -6.002, 0, 5.517, 3.543, 0, 6.517, -4, 0, 7.833, 8.425, 0, 8.25, -5.736, 0, 8.667, 3.362, 0, 9.083, -2.599, 0, 9.5, 0.996, 0, 9.85, -3.589, 0, 10.25, 13.782, 0, 10.583, -13.521, 0, 11, 7.051, 0, 11.383, -3.695, 0, 11.8, 2.603, 0, 12.217, -1.861, 0, 12.617, 0.925, 0, 12.867, -6.089, 0, 13.2, 6.83, 0, 13.633, -4.154, 0, 14.05, 2.561, 0, 14.467, -1.823, 0, 14.9, 0.486, 0, 15.25, -0.268, 0, 15.6, 1.439, 0, 16.017, -0.687, 0, 16.367, 0.147, 0, 16.75, -0.864, 0, 17.45, 2.092, 0, 17.667, -4.183, 0, 18.033, 5.532, 0, 18.417, -5.21, 0, 18.833, 3.317, 0, 19.233, -2.438, 0, 19.75, 5.646, 0, 20.167, -11.574, 0, 20.683, 16.335, 0, 21.117, -16.081, 0, 21.683, 12.685, 0, 22.15, -10.48, 0, 22.633, 8.62, 0, 23.133, -6.435, 0, 23.567, 2.955, 0, 23.95, -0.778, 0, 24.333, 1.358, 0, 24.85, -1.165, 0, 25.267, 0.687, 0, 25.683, -0.504, 0, 26.1, 0.245, 0, 26.517, -0.237, 0, 26.933, 0.067, 0, 27.35, -0.125, 0, 27.767, 0.003, 2, 27.783, 0.003, 0, 28.183, -0.07, 0, 28.617, -0.011, 2, 28.633, -0.011, 0, 28.983, -0.033, 0, 29.517, 0.002, 2, 29.55, 0.002, 0, 29.7, 0, 2, 29.717, 0, 2, 29.733, 0, 0, 30.067, 0.876, 1, 30.15, 0.876, 30.234, 0.618, 30.317, 0, 1, 30.322, -0.041, 30.328, -0.56, 30.333, -0.56, 2, 30.583, -0.56]}, {"Target": "Parameter", "Id": "ZD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.133, -1.819, 0, 0.367, 3.729, 0, 0.883, -5.334, 2, 1.55, -5.334, 0, 2.183, -4, 0, 2.367, -8.098, 0, 2.617, 20.986, 0, 2.933, -30, 2, 3.017, -30, 0, 3.35, 24.363, 0, 3.767, -15.223, 0, 4.183, 9.694, 0, 4.6, -7.617, 0, 4.933, 16.923, 0, 5.317, -13.155, 0, 5.717, 7.551, 0, 5.933, -2.722, 0, 6.267, 5.517, 0, 6.533, 2.369, 0, 6.7, 3.614, 0, 7.833, -8.425, 0, 8.083, 19.88, 0, 8.467, -13.362, 0, 8.867, 7.172, 0, 9.283, -6.071, 0, 9.583, -0.184, 0, 10, -14.376, 0, 10.367, 30, 2, 10.5, 30, 0, 10.8, -28.676, 0, 11.2, 14.399, 0, 11.583, -8.737, 0, 12, 5.926, 0, 12.417, -3.918, 0, 12.767, 3.738, 0, 13.05, -17.228, 0, 13.417, 15.27, 0, 13.833, -8.192, 0, 14.233, 7.405, 0, 14.667, -3.723, 0, 15.083, 0.326, 0, 15.417, -2.6, 0, 15.867, 4.748, 0, 16.217, -1.21, 0, 16.533, 0.961, 0, 16.967, -2.428, 0, 17.133, -1.632, 0, 17.15, -1.635, 0, 17.367, -0.086, 0, 17.4, -0.157, 0, 17.583, 3.927, 0, 17.883, -8.506, 0, 18.233, 14.643, 0, 18.617, -11.293, 0, 19.033, 7.279, 0, 19.45, -5.515, 0, 19.967, 16.049, 0, 20.383, -29.293, 0, 20.8, 30, 2, 21, 30, 0, 21.317, -30, 2, 21.367, -30, 0, 21.917, 25.819, 0, 22.383, -22.197, 0, 22.867, 18.129, 0, 23.35, -12.484, 0, 23.767, 4.589, 0, 23.783, 4.57, 0, 23.817, 4.731, 0, 24.133, -2.413, 0, 24.533, 3.945, 0, 25.067, -2.112, 0, 25.467, 1.387, 0, 25.883, -1.167, 0, 26.3, 0.389, 0, 26.717, -0.669, 0, 27.133, -0.046, 0, 27.55, -0.467, 0, 27.983, -0.2, 0, 28.367, -0.349, 0, 28.85, -0.207, 0, 29.15, -0.235, 0, 29.883, -0.101, 0, 30.083, -3.372, 1, 30.161, -3.372, 30.239, -2.37, 30.317, 0, 1, 30.322, 0.169, 30.328, 2.24, 30.333, 2.24, 2, 30.583, 2.24]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_L", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 0.039, 0, 0.078, -0.039, 0.117, 0.102, 1, 0.172, 0.305, 0.228, 1.335, 0.283, 1.335, 0, 0.533, -3.554, 0, 0.917, 4.057, 0, 1.333, -2.486, 0, 1.783, 3.284, 0, 2.167, -1.23, 0, 2.2, -1.222, 0, 2.283, -1.383, 0, 2.617, 3.419, 0, 2.983, -6.446, 0, 3.4, 4.143, 0, 3.867, -2.514, 0, 4.317, 1.596, 0, 4.65, -1.194, 0, 4.967, 3.055, 0, 5.35, -1.883, 0, 5.75, 0.615, 0, 6.083, -0.713, 0, 6.783, 0.476, 0, 7.133, -0.002, 0, 7.467, 1.492, 0, 8.133, -1.833, 0, 8.483, 1.172, 0, 8.717, 0.59, 0, 8.767, 0.604, 0, 9.15, -1.081, 0, 9.55, 0.085, 0, 9.667, -0.045, 0, 9.733, 0.048, 0, 10.05, -3.564, 0, 10.45, 9.704, 0, 10.85, -5.947, 0, 11.317, 2.419, 0, 11.767, -1.299, 0, 12.2, 0.906, 0, 12.617, -0.588, 0, 12.783, 0.387, 0, 13.05, -4.168, 0, 13.45, 2.997, 0, 13.95, -1.162, 0, 14.45, 0.687, 0, 14.933, -0.813, 0, 15.95, 0.622, 0, 16.317, 0.084, 0, 16.483, 0.117, 0, 17, -0.424, 0, 17.133, -0.356, 0, 17.233, -0.715, 0, 17.633, 1.747, 0, 17.9, -1.837, 0, 18.267, 3.13, 0, 18.683, -1.972, 0, 19.133, 1.202, 0, 19.65, -1.382, 0, 20.017, 4.286, 0, 20.45, -7.032, 0, 20.967, 11.188, 0, 21.5, -8.128, 0, 21.983, 8.125, 0, 22.45, -6.667, 0, 22.95, 5.893, 0, 23.433, -4.262, 0, 23.883, 1.947, 0, 24.367, -0.347, 0, 24.783, 0.886, 0, 25.217, -0.652, 0, 25.667, 0.352, 0, 26.133, -0.299, 0, 26.583, 0.09, 0, 27.033, -0.172, 2, 27.05, -0.172, 0, 27.483, -0.016, 2, 27.5, -0.016, 0, 27.95, -0.117, 0, 28.417, -0.048, 0, 28.817, -0.08, 2, 28.833, -0.08, 0, 29.383, -0.04, 2, 29.4, -0.04, 0, 29.617, -0.043, 2, 29.633, -0.043, 0, 29.867, -0.034, 0, 29.983, -0.332, 1, 30.094, -0.332, 30.206, -0.232, 30.317, 0, 1, 30.322, 0.012, 30.328, 0.33, 30.333, 0.33, 2, 30.583, 0.33]}, {"Target": "Parameter", "Id": "ZD_BustMotionX_R", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 0.039, 0, 0.078, 0.038, 0.117, -0.097, 1, 0.172, -0.291, 0.228, -1.335, 0.283, -1.335, 0, 0.533, 3.508, 0, 0.917, -4.037, 0, 1.333, 2.476, 0, 1.783, -3.28, 0, 2.167, 1.224, 0, 2.2, 1.216, 0, 2.283, 1.379, 0, 2.617, -3.416, 0, 2.983, 6.445, 0, 3.4, -4.142, 0, 3.867, 2.514, 0, 4.317, -1.595, 0, 4.65, 1.194, 0, 4.967, -3.056, 0, 5.35, 1.883, 0, 5.75, -0.614, 0, 6.083, 0.714, 0, 6.783, -0.474, 0, 7.017, -0.263, 0, 7.15, -0.369, 0, 7.467, 0.304, 0, 7.75, -0.666, 0, 7.817, -0.656, 0, 8.117, -1.647, 0, 8.467, 1.238, 0, 8.7, 0.62, 0, 8.8, 0.718, 0, 9.133, -0.474, 0, 9.583, 0.825, 0, 9.8, -0.55, 0, 10.067, 2.93, 0, 10.45, -8.564, 0, 10.85, 5.174, 0, 11.317, -1.961, 0, 11.767, 0.985, 0, 12.2, -0.732, 0, 12.617, 0.439, 0, 12.783, -0.489, 0, 13.05, 4.86, 0, 13.433, -3.448, 0, 13.9, 1.321, 0, 14.333, -1.221, 0, 14.85, 0.847, 0, 15.317, -0.074, 0, 15.583, 0.087, 0, 16.083, -0.423, 0, 16.967, 0.476, 0, 17.167, 0.38, 0, 17.183, 0.408, 0, 17.333, 0.041, 0, 17.433, 0.346, 0, 17.617, -1.295, 0, 17.883, 3.149, 0, 18.25, -3.999, 0, 18.667, 2.386, 0, 19.117, -1.464, 0, 19.633, 1.494, 0, 20.017, -4.401, 0, 20.45, 7.109, 0, 20.967, -11.213, 0, 21.5, 8.139, 0, 21.983, -8.129, 0, 22.45, 6.669, 0, 22.95, -5.819, 0, 23.433, 4.239, 0, 23.9, -1.776, 0, 24.333, 0.672, 0, 24.767, -1.036, 0, 25.217, 0.752, 0, 25.667, -0.418, 0, 26.117, 0.339, 2, 26.133, 0.339, 0, 26.567, -0.115, 0, 27.033, 0.189, 0, 27.483, 0.006, 0, 27.933, 0.124, 0, 28.4, 0.044, 0, 28.817, 0.083, 0, 29.367, 0.038, 2, 29.383, 0.038, 0, 29.633, 0.043, 2, 29.65, 0.043, 0, 29.867, 0.034, 0, 29.983, 0.329, 1, 30.094, 0.329, 30.206, 0.23, 30.317, 0, 1, 30.322, -0.011, 30.328, -0.852, 30.333, -0.852, 2, 30.583, -0.852]}, {"Target": "Parameter", "Id": "L_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.133, 3.637, 0, 0.367, -7.459, 0, 0.783, 8.484, 0, 1.133, -14.46, 0, 1.483, -8.015, 0, 1.867, -10.235, 0, 2.383, -2.772, 0, 2.733, -21.346, 0, 3.133, -0.653, 0, 3.417, -4.251, 0, 4.35, 0.143, 0, 4.867, -30, 2, 5.3, -30, 0, 6.417, 18.257, 0, 7.033, -3.737, 0, 7.2, -0.09, 0, 7.517, -30, 2, 8.05, -30, 0, 8.3, 0.801, 0, 8.433, -1.232, 0, 8.7, 18.497, 0, 8.95, 3.941, 0, 9.233, 8.367, 0, 9.85, -6.62, 0, 10.233, 27.06, 0, 10.533, -10.741, 0, 10.85, 1.2, 0, 11.167, -3.052, 0, 11.55, 1.368, 0, 12.133, -0.466, 0, 12.3, -0.414, 0, 12.85, -11.874, 0, 13.167, 4.939, 0, 13.467, -0.19, 0, 13.55, 0.375, 0, 14.267, -12.957, 0, 14.5, -12.494, 0, 14.983, -16.923, 0, 15.367, 4.103, 0, 15.683, 0.671, 2, 15.7, 0.671, 0, 16.017, 1.829, 2, 16.033, 1.829, 0, 16.683, -0.264, 0, 17, 0.176, 0, 17.017, -0.22, 0, 17.467, 3.637, 0, 17.633, -4.04, 0, 17.95, 30, 2, 18, 30, 0, 19.533, -19.154, 0, 19.767, -12.675, 0, 20.15, -30, 0, 20.783, 17.315, 0, 21.117, -1.023, 0, 21.183, 0, 0, 21.383, -3.063, 0, 21.767, 21.163, 0, 22.283, 1.799, 0, 22.75, 11.191, 0, 22.8, 10.907, 0, 22.833, 11.099, 0, 23.433, -4.48, 2, 23.45, -4.48, 0, 23.85, -5.577, 0, 23.967, -5.418, 0, 25.033, -13.248, 0, 29.933, 0.231, 0, 30.317, 0, 2, 30.333, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "L_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.133, -3.637, 0, 0.367, 7.459, 0, 0.767, -8.315, 0, 0.95, 15.197, 0, 1.217, -7.43, 0, 1.5, 2.506, 0, 1.817, -0.433, 0, 2.083, -0.182, 0, 2.317, -2.879, 0, 2.567, 7.081, 0, 2.85, -7.229, 0, 2.917, -5.174, 0, 2.967, -5.855, 0, 3.217, 3.714, 0, 3.5, -2.461, 0, 3.817, 0, 0, 4.05, -0.339, 0, 4.833, 13.422, 0, 5.183, -8.585, 0, 5.433, -2.297, 0, 5.767, -6.646, 0, 6.983, 5.416, 0, 7.15, -0.517, 0, 7.4, 14.021, 0, 7.667, -6.599, 0, 7.85, 6.716, 0, 8.1, -19.556, 0, 8.35, 7.324, 0, 8.583, -14.01, 0, 8.833, 8.83, 0, 9.067, -6.576, 0, 9.383, 2.332, 0, 9.633, -1.009, 0, 9.8, 4.871, 0, 10.033, -9.663, 0, 10.383, 17.4, 0, 10.633, -11.678, 0, 10.917, 5.224, 0, 11.217, -2.04, 0, 11.617, 0.9, 0, 11.917, -0.222, 0, 12.117, 0.08, 0, 12.3, -0.043, 0, 12.583, 0.141, 0, 12.617, 0.134, 0, 12.767, 4.658, 0, 13, -8.977, 0, 13.25, 5.414, 0, 13.45, -0.887, 0, 13.467, -0.843, 0, 13.55, -3.608, 0, 14.267, 12.957, 0, 14.5, 12.494, 0, 14.983, 16.923, 0, 15.183, -17.41, 0, 15.433, 7.484, 0, 15.7, -2.525, 0, 15.983, 0.658, 0, 16.25, 0.034, 0, 16.5, 0.199, 0, 16.683, -0.783, 0, 16.733, -0.715, 0, 16.933, -1.092, 0, 17.017, 0.22, 0, 17.467, -3.637, 0, 17.6, 3.089, 0, 17.783, -18.674, 0, 18.083, 11.076, 0, 18.433, -2.098, 0, 18.733, 4.786, 0, 19.683, -2.425, 0, 19.967, 5.53, 0, 20.3, -6.826, 0, 20.5, -4.927, 0, 20.617, -6.604, 0, 20.933, 4.426, 0, 21.183, -6.009, 0, 21.367, 1.189, 0, 21.6, -8.349, 0, 21.883, 6.396, 0, 22.183, -0.895, 0, 22.3, -0.751, 0, 22.467, -1.911, 0, 22.8, 2.079, 0, 22.9, -0.369, 0, 23.1, 3.86, 0, 23.367, -0.907, 0, 23.633, 1.403, 0, 23.933, 0.234, 0, 24.183, 1.172, 0, 24.5, 0.698, 0, 24.717, 0.775, 0, 25.1, -0.202, 0, 25.333, 0.109, 0, 27.767, -0.403, 0, 27.783, -0.402, 2, 27.8, -0.402, 0, 28.083, -0.396, 0, 28.1, -0.397, 0, 28.433, -0.376, 2, 28.45, -0.376, 0, 29.917, -0.117, 0, 29.933, -1.268, 0, 30.317, 0, 2, 30.333, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.133, -3.637, 0, 0.367, 7.459, 0, 0.883, -10.666, 0, 0.933, -10.665, 2, 0.95, -10.665, 2, 0.983, -10.665, 2, 1, -10.665, 2, 1.017, -10.665, 2, 1.033, -10.665, 2, 1.05, -10.665, 2, 1.117, -10.665, 2, 1.133, -10.665, 2, 1.55, -10.665, 0, 2.183, -7.998, 0, 2.45, -18.669, 0, 4.417, 20.007, 0, 4.717, -30, 2, 5.3, -30, 0, 6.383, 20.868, 0, 7.033, -9.765, 0, 7.217, -4.781, 0, 7.483, -30, 2, 8.083, -30, 0, 8.683, 26.691, 0, 9.017, 7.364, 0, 9.35, 10.24, 0, 9.683, 6.859, 0, 9.817, 8.559, 0, 10.283, -20.716, 0, 10.667, 4.942, 0, 10.967, 2.824, 0, 11.117, 3.039, 0, 11.633, -1.888, 0, 12.2, -0.047, 0, 12.417, -0.166, 0, 12.867, 13.422, 0, 13.233, -6.88, 0, 13.55, -3.267, 0, 13.883, -6.214, 0, 14.483, 1.585, 0, 14.617, 1.524, 0, 15.033, 2.06, 0, 15.817, -1.073, 0, 16.9, 3.999, 0, 17.467, -3.637, 0, 17.7, 7.456, 0, 18.767, -10.668, 2, 18.8, -10.668, 2, 18.817, -10.668, 2, 18.867, -10.668, 0, 19.517, -7.993, 0, 19.9, -27.632, 0, 20.5, 18.899, 0, 20.883, -25.508, 0, 21.35, 30, 2, 21.5, 30, 0, 21.867, -3.057, 0, 22.333, 17.389, 0, 22.883, -5.357, 0, 23.333, 4.644, 0, 24.517, -13.062, 0, 24.533, -12.956, 0, 24.85, -16.404, 0, 30.317, 0, 2, 30.333, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "R_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.133, 3.637, 0, 0.367, -7.459, 0, 0.883, 10.666, 0, 0.933, 10.665, 2, 0.95, 10.665, 2, 0.983, 10.665, 2, 1, 10.665, 2, 1.017, 10.665, 2, 1.033, 10.665, 2, 1.05, 10.665, 2, 1.117, 10.665, 2, 1.133, 10.665, 2, 1.55, 10.665, 0, 2.183, 7.998, 0, 2.45, 18.669, 0, 4.417, -20.007, 0, 4.6, 30, 2, 4.75, 30, 0, 5.017, -7.38, 0, 5.25, -4.529, 0, 5.733, -9.223, 0, 5.967, -5.912, 0, 6.117, -6.614, 0, 6.867, 10.366, 0, 7.183, -0.973, 0, 7.45, 18.79, 0, 7.7, -4.369, 0, 7.867, 3.928, 0, 8.15, -26.427, 0, 8.433, 4.851, 0, 8.633, -11.796, 0, 8.867, 8.808, 0, 9.15, -5.554, 0, 9.5, 3.372, 0, 9.817, -1.081, 0, 10.067, 6.475, 0, 10.1, 6.446, 0, 10.233, 8.836, 0, 10.45, -16.32, 0, 10.767, 7.032, 0, 11.083, -2.35, 0, 11.417, 2.163, 0, 11.733, -1.083, 0, 12.283, 0.161, 0, 12.8, -6.577, 0, 13.067, 14.847, 0, 13.367, -6.548, 0, 13.7, 6.436, 0, 14.033, -2.664, 0, 14.55, 0.844, 0, 14.867, -0.425, 0, 15.45, 0.454, 0, 15.95, -0.243, 0, 16, -0.238, 0, 16.017, -0.239, 0, 16.05, -0.237, 0, 16.083, -0.24, 0, 16.1, -0.238, 0, 16.533, -0.505, 0, 17.333, 1.389, 0, 17.4, -1.02, 0, 18.167, 25.718, 0, 19.533, -11.006, 0, 19.917, 7.809, 0, 20.467, -28.453, 0, 20.867, 23.472, 0, 21.133, -22.905, 0, 21.617, 14.612, 0, 22, -8.491, 0, 22.55, 6.462, 0, 23.083, -2.887, 0, 23.467, 3.479, 0, 23.767, 1.885, 0, 23.917, 3.154, 0, 24.267, 0.558, 0, 24.5, 0.91, 0, 24.533, 0.481, 0, 24.733, 5.734, 0, 25.083, 3.195, 0, 25.267, 3.297, 0, 29.483, 0.218, 0, 29.5, 0.989, 0, 30.317, 0, 2, 30.333, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "Jewelry_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.117, 0.247, 0, 0.333, -0.703, 0, 0.683, 1.014, 0, 1.067, -0.945, 0, 1.483, 0.601, 0, 1.9, -0.438, 0, 2.367, 0.806, 0, 2.733, -1.752, 0, 3.117, 1.762, 0, 3.55, -1.116, 0, 3.967, 0.711, 0, 4.383, -0.451, 0, 4.733, 1.021, 0, 5.1, -1.015, 0, 5.517, 0.599, 0, 5.917, -0.44, 0, 6.35, 0.289, 0, 6.75, -0.08, 0, 7.233, 0.508, 0, 7.617, -0.454, 0, 7.683, -0.435, 0, 7.85, -0.512, 0, 8.233, 0.666, 0, 8.583, -0.093, 0, 8.633, -0.09, 0, 8.883, -0.323, 0, 9.283, 0.104, 0, 9.833, -0.647, 0, 10.25, 2.451, 0, 10.583, -2.457, 0, 11, 1.295, 0, 11.383, -0.701, 0, 11.8, 0.484, 0, 12.217, -0.346, 0, 12.617, 0.173, 0, 12.867, -1.022, 0, 13.2, 1.126, 0, 13.65, -0.667, 0, 14.067, 0.385, 0, 14.517, -0.242, 0, 14.983, 0.059, 0, 15.283, -0.005, 0, 15.683, 0.132, 0, 16.25, -0.048, 2, 16.267, -0.048, 0, 16.35, -0.046, 0, 16.683, -0.088, 0, 17.45, 0.321, 0, 17.667, -0.758, 0, 18.017, 1.028, 0, 18.4, -0.925, 0, 18.833, 0.588, 0, 19.233, -0.429, 0, 19.75, 0.95, 0, 20.167, -1.929, 0, 20.683, 2.702, 0, 21.117, -2.647, 0, 21.683, 2.107, 0, 22.15, -1.74, 0, 22.633, 1.435, 0, 23.133, -1.074, 0, 23.567, 0.494, 0, 23.95, -0.13, 0, 24.333, 0.226, 0, 24.85, -0.195, 0, 25.267, 0.115, 0, 25.683, -0.084, 0, 26.1, 0.041, 0, 26.517, -0.04, 0, 26.933, 0.011, 0, 27.35, -0.021, 0, 27.767, 0, 2, 27.783, 0, 0, 28.167, -0.012, 2, 28.2, -0.012, 0, 28.617, -0.002, 2, 28.633, -0.002, 0, 28.967, -0.005, 2, 29.017, -0.005, 0, 29.483, 0, 2, 29.583, 0, 2, 29.6, 0, 2, 29.617, 0, 2, 29.633, 0, 2, 29.667, 0, 2, 29.683, 0, 2, 29.767, 0, 2, 29.783, 0, 2, 29.8, 0, 0, 30.25, 0.042, 0, 30.317, 0, 0, 30.333, 0.034, 2, 30.583, 0.034]}, {"Target": "Parameter", "Id": "Jewelry_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 0.006, 0, 0.011, -0.09, 0.017, -0.102, 1, 0.039, -0.152, 0.061, -0.164, 0.083, -0.164, 0, 0.283, 1.093, 0, 0.533, -1.767, 0, 0.9, 2.683, 0, 1.283, -2.029, 0, 1.683, 1.321, 0, 2.1, -0.983, 0, 2.567, 2.222, 0, 2.95, -4.533, 0, 3.333, 3.603, 0, 3.75, -2.372, 0, 4.167, 1.499, 0, 4.583, -1.192, 0, 4.933, 2.8, 0, 5.3, -2.186, 0, 5.7, 1.269, 0, 6.1, -1.021, 0, 6.55, 0.516, 0, 6.933, -0.141, 0, 7.433, 1.523, 0, 8.1, -1.379, 0, 8.45, 1.284, 0, 8.75, 0.046, 0, 8.783, 0.055, 0, 9.133, -0.652, 0, 9.5, 0.09, 0, 10.017, -2.1, 0, 10.417, 6.285, 0, 10.8, -4.865, 0, 11.2, 2.543, 0, 11.583, -1.613, 0, 12, 1.087, 0, 12.417, -0.736, 0, 12.767, 0.649, 0, 13.05, -2.857, 0, 13.417, 2.472, 0, 13.85, -1.321, 0, 14.267, 0.805, 0, 14.733, -0.484, 0, 15.167, 0.001, 0, 15.45, -0.121, 0, 15.917, 0.356, 0, 16.95, -0.243, 0, 17.15, -0.144, 0, 17.217, -0.163, 0, 17.35, -0.085, 0, 17.417, -0.121, 0, 17.6, 0.979, 0, 17.867, -1.818, 0, 18.233, 2.628, 0, 18.617, -1.983, 0, 19.033, 1.333, 0, 19.433, -0.973, 0, 19.967, 2.653, 0, 20.383, -4.658, 0, 20.9, 6.028, 0, 21.35, -4.593, 0, 21.917, 4.144, 0, 22.383, -3.571, 0, 22.867, 2.951, 0, 23.35, -2.06, 0, 23.767, 0.761, 0, 24.117, -0.376, 0, 24.533, 0.643, 0, 25.067, -0.354, 0, 25.467, 0.232, 0, 25.883, -0.195, 0, 26.3, 0.065, 0, 26.717, -0.112, 0, 27.133, -0.008, 0, 27.55, -0.078, 0, 27.983, -0.033, 0, 28.367, -0.058, 2, 28.383, -0.058, 0, 28.85, -0.035, 2, 28.867, -0.035, 0, 29.15, -0.039, 0, 30.05, -0.013, 0, 30.25, -0.167, 0, 30.317, 0, 0, 30.333, -0.137, 2, 30.583, -0.137]}, {"Target": "Parameter", "Id": "Jewelry_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.233, -0.614, 0, 0.45, 1.396, 0, 0.717, -2.154, 0, 1.05, 2.518, 0, 1.4, -2.037, 0, 1.8, 1.349, 0, 2.217, -0.889, 0, 2.75, 2.497, 0, 3.1, -3.771, 0, 3.467, 3.399, 0, 3.85, -2.305, 0, 4.267, 1.429, 0, 4.733, -1.575, 0, 5.083, 2.45, 0, 5.433, -2.132, 0, 5.817, 1.425, 0, 6.217, -0.986, 0, 6.633, 0.469, 0, 7.233, -0.392, 0, 7.6, 0.964, 0, 7.933, -0.264, 0, 8.017, -0.222, 0, 8.267, -0.896, 0, 8.583, 1.081, 0, 8.867, -0.29, 0, 9.067, 0.071, 0, 9.317, -0.304, 0, 9.65, 0.372, 0, 9.767, 0.324, 0, 9.883, 0.374, 0, 10.217, -2.485, 0, 10.567, 5.064, 0, 10.917, -4.492, 0, 11.3, 2.866, 0, 11.683, -1.755, 0, 12.1, 1.045, 0, 12.517, -0.649, 0, 12.917, 1.464, 0, 13.217, -2.587, 0, 13.567, 2.301, 0, 13.933, -1.37, 0, 14.367, 0.739, 0, 14.783, -0.327, 0, 15.217, 0.135, 0, 15.633, -0.168, 0, 16.05, 0.146, 0, 16.417, -0.033, 0, 16.733, 0.076, 0, 17.117, -0.103, 0, 17.283, 0.027, 0, 17.517, -0.3, 0, 17.75, 1.19, 0, 18.05, -2.046, 0, 18.383, 2.425, 0, 18.75, -1.965, 0, 19.15, 1.31, 0, 19.55, -0.895, 0, 20.133, 2.547, 0, 20.533, -3.353, 0, 21.05, 3.828, 0, 21.433, -2.824, 0, 22.067, 2.743, 0, 22.5, -2.56, 0, 22.983, 1.877, 0, 23.467, -1.352, 0, 23.867, 0.882, 0, 24.25, -0.605, 0, 24.65, 0.408, 0, 25.183, -0.245, 0, 25.6, 0.223, 0, 26, -0.155, 0, 26.4, 0.1, 0, 26.817, -0.061, 0, 27.233, 0.039, 2, 27.25, 0.039, 0, 27.667, -0.025, 0, 28.083, 0.015, 0, 28.5, -0.011, 0, 28.9, 0.005, 2, 28.917, 0.005, 0, 29.317, -0.006, 2, 29.35, -0.006, 0, 29.717, 0.001, 2, 29.767, 0.001, 0, 30.05, -0.003, 0, 30.083, 0.026, 0, 30.1, 0.009, 0, 30.25, 0.201, 0, 30.317, 0, 0, 30.333, 0.155, 2, 30.583, 0.155]}, {"Target": "Parameter", "Id": "Physics_Joint_5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.167, -0.862, 0, 0.633, 1.672, 0, 0.983, -2.144, 0, 1.4, 1.377, 0, 1.817, -1.096, 0, 2.3, 1.019, 0, 2.683, -1.894, 0, 3.067, 1.789, 0, 3.483, -1.134, 0, 3.9, 0.734, 0, 4.317, -0.443, 0, 4.7, 0.566, 0, 5.017, -0.811, 0, 5.417, 0.884, 0, 5.85, -0.45, 0, 6.233, 0.104, 0, 6.617, -0.182, 0, 7.2, 0.188, 0, 7.867, -0.097, 2, 7.883, -0.097, 0, 8.233, 0.159, 0, 8.883, -0.138, 0, 9.167, -0.097, 0, 9.517, -0.476, 0, 10.05, 1.059, 0, 10.5, -1.435, 0, 10.9, 1.064, 0, 11.333, -0.649, 0, 11.75, 0.327, 0, 12.117, -0.255, 0, 12.4, 0.644, 0, 12.767, -1.224, 0, 13.167, 1.435, 0, 13.683, -1.291, 0, 14.117, 0.76, 0, 14.533, -0.413, 0, 14.967, 0.297, 0, 15.367, -0.097, 0, 15.733, 0.097, 0, 16.2, -0.077, 0, 16.567, -0.018, 0, 17, -0.068, 0, 17.333, -0.045, 0, 17.483, -0.667, 0, 17.967, 1.542, 0, 18.35, -1.94, 0, 18.733, 1.326, 0, 19.133, -0.981, 0, 19.55, 0.837, 0, 20.017, -1.559, 0, 20.433, 1.339, 0, 21.133, -1.681, 0, 21.583, 1.714, 0, 22, -1.562, 0, 22.4, 1.179, 0, 22.8, -0.967, 0, 23.233, 0.78, 0, 23.65, -0.402, 0, 24.05, 0.292, 0, 24.5, -0.469, 0, 24.983, 0.122, 0, 25.25, 0.052, 0, 25.467, 0.134, 0, 25.883, -0.074, 0, 26.3, 0.06, 0, 26.717, -0.024, 0, 27.133, 0.031, 0, 27.55, -0.004, 2, 27.567, -0.004, 0, 27.95, 0.017, 2, 27.967, 0.017, 0, 28.383, 0.002, 2, 28.417, 0.002, 0, 28.767, 0.009, 2, 28.8, 0.009, 0, 29.25, 0.001, 2, 29.267, 0.001, 2, 29.283, 0.001, 2, 29.3, 0.001, 2, 29.317, 0.001, 2, 29.333, 0.001, 0, 29.5, 0.002, 2, 29.55, 0.002, 2, 29.567, 0.002, 2, 29.583, 0.002, 2, 29.6, 0.002, 0, 30.117, -0.007, 0, 30.3, 0, 2, 30.317, 0, 2, 30.333, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.1, 0.472, 0, 0.4, -2.269, 0, 0.833, 3.869, 0, 1.2, -3.879, 0, 1.6, 2.809, 0, 2.017, -2.332, 0, 2.517, 2.446, 0, 2.867, -4.201, 0, 3.267, 3.458, 0, 3.683, -2.19, 0, 4.1, 1.555, 0, 4.533, -0.905, 0, 4.883, 1.476, 0, 5.233, -2.043, 0, 5.633, 2.004, 0, 6.067, -0.634, 0, 6.4, 0.316, 0, 6.817, -0.515, 0, 7.483, 0.38, 0, 8.1, -0.192, 0, 8.433, 0.421, 0, 9.25, -0.357, 2, 9.267, -0.357, 0, 9.3, -0.358, 0, 9.417, -0.277, 0, 9.717, -1.587, 0, 10.317, 2.59, 0, 10.717, -2.878, 0, 11.117, 2.191, 0, 11.533, -1.256, 0, 11.95, 0.669, 0, 12.35, -0.889, 0, 12.6, 1.509, 0, 12.983, -3.234, 0, 13.367, 2.736, 0, 13.483, 2.122, 0, 13.5, 2.141, 0, 13.917, -2.418, 0, 14.317, 1.45, 0, 14.733, -0.857, 0, 15.167, 0.646, 0, 15.55, -0.102, 0, 15.933, 0.346, 0, 16.433, -0.087, 0, 16.7, -0.033, 0, 17.283, -0.238, 0, 17.433, 0.132, 0, 17.717, -1.897, 0, 18.167, 3.67, 0, 18.55, -3.739, 0, 18.933, 2.699, 0, 19.333, -2.231, 0, 19.767, 1.85, 0, 20.2, -3.512, 0, 20.667, 2.275, 0, 20.75, 2.263, 0, 20.85, 2.301, 0, 21.35, -3.487, 0, 21.783, 3.454, 0, 22.2, -3.179, 0, 22.6, 2.436, 0, 23.017, -2.145, 0, 23.433, 1.58, 0, 23.85, -0.735, 0, 24.25, 0.778, 0, 24.733, -1.124, 0, 25.2, -0.123, 0, 25.383, -0.19, 0, 25.717, 0.261, 0, 26.083, -0.155, 0, 26.5, 0.149, 0, 26.917, -0.023, 0, 27.333, 0.098, 2, 27.35, 0.098, 0, 27.75, 0.028, 0, 28.167, 0.074, 0, 28.6, 0.041, 2, 28.617, 0.041, 0, 28.95, 0.053, 0, 29.583, 0.029, 2, 29.6, 0.029, 2, 29.617, 0.029, 2, 29.633, 0.029, 0, 30.1, 0.011, 0, 30.117, 0.019, 0, 30.25, -0.038, 0, 30.317, 0, 0, 30.333, -0.027, 2, 30.583, -0.027]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.117, 0.742, 0, 0.333, -2.109, 0, 0.683, 3.043, 0, 1.067, -2.836, 0, 1.483, 1.803, 0, 1.9, -1.314, 0, 2.367, 2.418, 0, 2.733, -5.255, 0, 3.117, 5.285, 0, 3.55, -3.349, 0, 3.967, 2.134, 0, 4.383, -1.353, 0, 4.733, 3.063, 0, 5.1, -3.044, 0, 5.517, 1.798, 0, 5.917, -1.319, 0, 6.35, 0.866, 0, 6.75, -0.24, 0, 7.233, 1.524, 0, 7.617, -1.363, 0, 7.683, -1.306, 0, 7.85, -1.538, 0, 8.233, 1.997, 0, 8.583, -0.278, 0, 8.633, -0.269, 0, 8.883, -0.969, 0, 9.283, 0.312, 0, 9.833, -1.942, 0, 10.25, 7.354, 0, 10.583, -7.371, 0, 11, 3.884, 0, 11.383, -2.102, 0, 11.8, 1.454, 0, 12.217, -1.039, 0, 12.617, 0.518, 0, 12.867, -3.067, 0, 13.2, 3.377, 0, 13.65, -2.002, 0, 14.067, 1.155, 0, 14.517, -0.727, 0, 14.983, 0.177, 0, 15.283, -0.015, 0, 15.683, 0.395, 0, 16.25, -0.143, 2, 16.267, -0.143, 0, 16.35, -0.14, 0, 16.683, -0.265, 0, 17.45, 0.964, 0, 17.667, -2.274, 0, 18.017, 3.085, 0, 18.4, -2.776, 0, 18.833, 1.765, 0, 19.233, -1.288, 0, 19.75, 2.85, 0, 20.167, -5.788, 0, 20.683, 8.106, 0, 21.117, -7.941, 0, 21.683, 6.321, 0, 22.15, -5.221, 0, 22.633, 4.303, 0, 23.133, -3.223, 0, 23.567, 1.481, 0, 23.95, -0.389, 0, 24.333, 0.677, 0, 24.85, -0.584, 0, 25.267, 0.344, 0, 25.683, -0.253, 0, 26.1, 0.123, 0, 26.517, -0.119, 0, 26.933, 0.034, 0, 27.35, -0.063, 0, 27.767, 0.002, 2, 27.783, 0.002, 0, 28.183, -0.035, 0, 28.617, -0.005, 2, 28.633, -0.005, 0, 28.983, -0.017, 2, 29, -0.017, 0, 29.517, 0.001, 2, 29.55, 0.001, 0, 29.7, 0, 2, 29.75, 0, 0, 30.25, 0.127, 0, 30.317, 0, 0, 30.333, 0.103, 2, 30.583, 0.103]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 0.006, 0, 0.011, -0.269, 0.017, -0.306, 1, 0.039, -0.455, 0.061, -0.492, 0.083, -0.492, 0, 0.283, 3.279, 0, 0.533, -5.302, 0, 0.9, 8.048, 0, 1.283, -6.087, 0, 1.683, 3.961, 0, 2.1, -2.948, 0, 2.567, 6.666, 0, 2.95, -13.599, 0, 3.333, 10.81, 0, 3.75, -7.117, 0, 4.167, 4.497, 0, 4.583, -3.575, 0, 4.933, 8.399, 0, 5.3, -6.559, 0, 5.7, 3.807, 0, 6.1, -3.064, 0, 6.55, 1.549, 0, 6.933, -0.422, 0, 7.433, 4.568, 0, 8.1, -4.138, 0, 8.45, 3.852, 0, 8.75, 0.139, 0, 8.783, 0.166, 0, 9.133, -1.956, 0, 9.5, 0.27, 0, 10.017, -6.301, 0, 10.417, 18.856, 0, 10.8, -14.596, 0, 11.2, 7.629, 0, 11.583, -4.839, 0, 12, 3.262, 0, 12.417, -2.207, 0, 12.767, 1.946, 0, 13.05, -8.572, 0, 13.417, 7.416, 0, 13.85, -3.964, 0, 14.267, 2.414, 0, 14.733, -1.453, 0, 15.167, 0.003, 0, 15.45, -0.363, 0, 15.917, 1.067, 0, 16.95, -0.73, 0, 17.15, -0.433, 0, 17.217, -0.488, 0, 17.35, -0.255, 0, 17.417, -0.363, 0, 17.6, 2.937, 0, 17.867, -5.454, 0, 18.233, 7.885, 0, 18.617, -5.948, 0, 19.033, 4, 0, 19.433, -2.918, 0, 19.967, 7.96, 0, 20.383, -13.973, 0, 20.9, 18.084, 0, 21.35, -13.778, 0, 21.917, 12.431, 0, 22.383, -10.714, 0, 22.867, 8.854, 0, 23.35, -6.181, 0, 23.767, 2.283, 0, 24.117, -1.127, 0, 24.533, 1.929, 0, 25.067, -1.062, 0, 25.467, 0.696, 0, 25.883, -0.585, 0, 26.3, 0.195, 0, 26.717, -0.335, 0, 27.133, -0.023, 0, 27.55, -0.234, 0, 27.983, -0.1, 0, 28.367, -0.174, 0, 28.85, -0.103, 0, 29.15, -0.117, 0, 30.05, -0.039, 0, 30.25, -0.502, 0, 30.317, 0, 0, 30.333, -0.412, 2, 30.583, -0.412]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.233, -1.843, 0, 0.45, 4.189, 0, 0.717, -6.463, 0, 1.05, 7.553, 0, 1.4, -6.112, 0, 1.8, 4.047, 0, 2.217, -2.666, 0, 2.75, 7.491, 0, 3.1, -11.314, 0, 3.467, 10.196, 0, 3.85, -6.916, 0, 4.267, 4.288, 0, 4.733, -4.724, 0, 5.083, 7.35, 0, 5.433, -6.395, 0, 5.817, 4.275, 0, 6.217, -2.958, 0, 6.633, 1.407, 0, 7.233, -1.175, 0, 7.6, 2.891, 0, 7.933, -0.792, 0, 8.017, -0.666, 0, 8.267, -2.689, 0, 8.583, 3.243, 0, 8.867, -0.87, 0, 9.067, 0.214, 0, 9.317, -0.914, 0, 9.65, 1.118, 0, 9.767, 0.97, 0, 9.883, 1.122, 0, 10.217, -7.454, 0, 10.567, 15.193, 0, 10.917, -13.475, 0, 11.3, 8.599, 0, 11.683, -5.264, 0, 12.1, 3.135, 0, 12.517, -1.946, 0, 12.917, 4.392, 0, 13.217, -7.761, 0, 13.567, 6.903, 0, 13.933, -4.109, 0, 14.367, 2.218, 0, 14.783, -0.981, 0, 15.217, 0.404, 0, 15.633, -0.505, 0, 16.05, 0.439, 0, 16.417, -0.097, 0, 16.733, 0.229, 0, 17.117, -0.31, 0, 17.283, 0.08, 0, 17.517, -0.9, 0, 17.75, 3.569, 0, 18.05, -6.139, 0, 18.383, 7.274, 0, 18.75, -5.895, 0, 19.15, 3.93, 0, 19.55, -2.685, 0, 20.133, 7.641, 0, 20.533, -10.06, 0, 21.05, 11.484, 0, 21.433, -8.47, 0, 22.067, 8.229, 0, 22.5, -7.681, 0, 22.983, 5.632, 0, 23.467, -4.056, 0, 23.867, 2.646, 0, 24.25, -1.816, 0, 24.65, 1.225, 0, 25.183, -0.735, 0, 25.6, 0.67, 0, 26, -0.466, 0, 26.4, 0.3, 0, 26.817, -0.184, 0, 27.233, 0.118, 2, 27.25, 0.118, 0, 27.667, -0.076, 0, 28.083, 0.046, 0, 28.5, -0.033, 0, 28.9, 0.016, 2, 28.917, 0.016, 0, 29.333, -0.017, 0, 29.75, 0.002, 0, 30.05, -0.009, 0, 30.083, 0.079, 0, 30.1, 0.028, 0, 30.25, 0.603, 0, 30.317, 0, 0, 30.333, 0.466, 2, 30.583, 0.466]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.233, 0, 0, 0.383, -1.175, 0, 0.583, 3.004, 0, 0.85, -7.262, 0, 1.167, 10.003, 0, 1.517, -9.14, 0, 1.883, 6.461, 0, 2.283, -4.152, 0, 2.833, 8.293, 0, 3.2, -14.038, 0, 3.567, 14.191, 0, 3.95, -10.571, 0, 4.333, 6.721, 0, 4.8, -5.843, 0, 5.183, 9.114, 0, 5.55, -9.171, 0, 5.917, 6.795, 0, 6.317, -4.412, 0, 6.7, 2.412, 0, 7.15, -1.506, 0, 7.717, 3.367, 0, 8.067, -1.74, 0, 8.183, -1.468, 0, 8.383, -2.239, 0, 8.717, 3.827, 0, 9.017, -1.76, 0, 9.25, 0.222, 0, 9.467, -0.779, 0, 9.817, 1.527, 0, 10.317, -8.472, 0, 10.667, 17.791, 0, 11.017, -18.484, 0, 11.4, 13.28, 0, 11.783, -8.528, 0, 12.167, 5.094, 0, 12.583, -2.985, 0, 13.017, 4.985, 0, 13.333, -9.432, 0, 13.667, 9.911, 0, 14.033, -6.835, 0, 14.433, 3.74, 0, 14.85, -1.669, 0, 15.283, 0.697, 0, 15.717, -0.666, 0, 16.15, 0.66, 0, 16.517, -0.246, 0, 16.85, 0.312, 0, 17.25, -0.459, 0, 17.267, -0.446, 0, 17.283, -0.455, 0, 17.45, -0.141, 0, 17.6, -0.575, 0, 17.867, 3.231, 0, 18.167, -7.266, 0, 18.483, 9.708, 0, 18.85, -8.792, 0, 19.233, 6.123, 0, 19.633, -4.136, 0, 20.233, 8.465, 0, 20.617, -12.862, 0, 21.117, 13.171, 0, 21.533, -11.734, 0, 22.167, 9.009, 0, 22.583, -10.086, 0, 23.05, 7.497, 0, 23.533, -5.243, 0, 23.967, 4.097, 0, 24.333, -2.946, 0, 24.733, 1.936, 0, 25.217, -0.939, 0, 25.7, 0.88, 0, 26.083, -0.697, 0, 26.483, 0.468, 0, 26.9, -0.286, 0, 27.317, 0.179, 0, 27.733, -0.114, 0, 28.15, 0.07, 0, 28.567, -0.049, 0, 28.983, 0.026, 0, 29.4, -0.023, 2, 29.417, -0.023, 0, 29.817, 0.006, 0, 30.05, -0.006, 0, 30.1, 0.041, 0, 30.183, -0.237, 1, 30.228, -0.237, 30.272, -0.171, 30.317, 0, 1, 30.322, 0.021, 30.328, 0.1, 30.333, 0.1, 2, 30.583, 0.1]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.183, 0.227, 0, 0.45, -0.254, 0, 0.683, 3.015, 0, 0.95, -8.202, 0, 1.267, 12.693, 0, 1.617, -12.965, 0, 1.983, 10.027, 0, 2.367, -6.629, 0, 2.917, 8.939, 0, 3.283, -16.684, 0, 3.65, 18.572, 0, 4.033, -15.25, 0, 4.433, 10.362, 0, 4.867, -7.825, 0, 5.283, 11.136, 0, 5.65, -12.43, 0, 6.017, 10.246, 0, 6.383, -6.97, 0, 6.783, 4.053, 0, 7.2, -2.348, 0, 7.817, 3.79, 0, 8.183, -2.886, 0, 8.817, 4.469, 0, 9.133, -2.93, 0, 9.417, 0.698, 0, 9.633, -0.562, 0, 9.933, 1.963, 0, 10.4, -9.311, 0, 10.75, 20.172, 0, 11.117, -23.404, 0, 11.483, 18.869, 0, 11.867, -13.172, 0, 12.25, 8.265, 0, 12.65, -4.743, 0, 13.1, 5.957, 0, 13.433, -11.397, 0, 13.783, 13.38, 0, 14.133, -10.603, 0, 14.5, 6.389, 0, 14.9, -2.997, 0, 15.333, 1.163, 0, 15.783, -0.893, 0, 16.25, 0.711, 0, 16.617, -0.415, 0, 16.95, 0.445, 0, 17.267, -0.513, 0, 17.517, -0.126, 0, 17.683, -0.36, 0, 17.967, 3.57, 0, 18.267, -8.593, 0, 18.6, 12.544, 0, 18.95, -12.496, 0, 19.317, 9.514, 0, 19.717, -6.436, 0, 20.317, 8.976, 0, 20.7, -15.612, 0, 21.167, 15.2, 0, 21.617, -14.698, 0, 22.25, 8.992, 0, 22.683, -12.58, 0, 23.117, 10.043, 0, 23.6, -6.753, 0, 24.05, 5.309, 0, 24.433, -4.417, 0, 24.817, 3.099, 0, 25.233, -1.49, 0, 25.783, 1.084, 0, 26.183, -0.991, 0, 26.567, 0.722, 0, 26.967, -0.452, 0, 27.383, 0.275, 0, 27.817, -0.172, 0, 28.233, 0.107, 0, 28.65, -0.072, 0, 29.067, 0.041, 0, 29.483, -0.033, 0, 29.9, 0.012, 0, 30.1, -0.002, 0, 30.15, 0.087, 0, 30.283, -0.273, 0, 30.317, 0, 0, 30.333, -0.23, 2, 30.583, -0.23]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.117, -0.686, 0, 0.383, 0.768, 0, 0.617, -0.397, 0, 0.8, 3.263, 0, 1.067, -9.172, 0, 1.383, 15.642, 0, 1.717, -17.522, 0, 2.083, 14.799, 0, 2.45, -10.456, 0, 2.983, 9.511, 0, 3.367, -19.163, 0, 3.733, 23.063, 0, 4.117, -20.522, 0, 4.517, 15.177, 0, 4.917, -11.116, 0, 5.367, 13.478, 0, 5.733, -16.071, 0, 6.1, 14.578, 0, 6.483, -10.78, 0, 6.867, 6.669, 0, 7.267, -3.864, 0, 7.917, 4.08, 0, 8.283, -4.179, 0, 8.933, 4.996, 0, 9.267, -4.328, 0, 9.55, 1.665, 0, 9.783, -0.479, 0, 10.067, 2.362, 0, 10.483, -10.319, 0, 10.817, 22.506, 0, 11.183, -27.949, 0, 11.567, 24.61, 0, 11.95, -18.773, 0, 12.333, 12.773, 0, 12.733, -7.664, 0, 13.167, 7.475, 0, 13.533, -13.726, 0, 13.883, 17.378, 0, 14.233, -15.364, 0, 14.6, 10.392, 0, 14.983, -5.432, 0, 15.383, 2.208, 0, 15.833, -1.224, 0, 16.25, 0.993, 0, 16.717, -0.573, 0, 17.05, 0.619, 0, 17.383, -0.783, 0, 17.65, -0.033, 0, 17.783, -0.264, 0, 17.8, -0.246, 0, 17.817, -0.294, 0, 18.067, 3.595, 0, 18.383, -9.912, 0, 18.7, 15.74, 0, 19.05, -17.001, 0, 19.417, 14.093, 0, 19.8, -9.986, 0, 20.4, 9.141, 0, 20.767, -18.084, 0, 21.2, 17.761, 0, 21.683, -17.015, 0, 22.3, 8.336, 0, 22.767, -14.78, 0, 23.183, 13.158, 0, 23.667, -8.721, 0, 24.133, 6.957, 0, 24.533, -6.257, 0, 24.9, 4.805, 0, 25.3, -2.588, 0, 25.85, 1.262, 0, 26.283, -1.326, 0, 26.667, 1.083, 0, 27.05, -0.719, 0, 27.467, 0.434, 0, 27.883, -0.262, 0, 28.3, 0.162, 0, 28.717, -0.108, 0, 29.133, 0.063, 0, 29.55, -0.048, 0, 29.967, 0.021, 0, 30.133, 0.007, 0, 30.2, 0.146, 1, 30.239, 0.146, 30.278, 0.109, 30.317, 0, 1, 30.322, -0.016, 30.328, -0.16, 30.333, -0.16, 2, 30.583, -0.16]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.283, -1.087, 0, 0.55, 0.889, 0, 0.733, -0.171, 0, 0.9, 2.14, 0, 1.183, -9.712, 0, 1.483, 18.612, 0, 1.817, -22.595, 0, 2.167, 20.502, 0, 2.55, -15.621, 0, 3, 11.03, 0, 3.433, -21.16, 0, 3.817, 27.244, 0, 4.2, -25.843, 0, 4.6, 20.628, 0, 5, -15.733, 0, 5.433, 16.308, 0, 5.833, -20.002, 0, 6.2, 19.634, 0, 6.583, -15.694, 0, 6.95, 10.537, 0, 7.333, -6.402, 0, 8, 4.085, 0, 8.4, -5.57, 0, 9.017, 5.296, 0, 9.367, -5.894, 0, 9.683, 3.156, 0, 9.933, -0.774, 0, 10.2, 2.653, 0, 10.567, -11.581, 0, 10.883, 24.834, 0, 11.233, -30, 2, 11.267, -30, 0, 11.65, 29.813, 0, 12.05, -24.613, 0, 12.433, 18.341, 0, 12.817, -11.916, 0, 13.233, 9.959, 0, 13.617, -16.434, 0, 13.967, 21.826, 0, 14.333, -20.89, 0, 14.7, 15.66, 0, 15.067, -9.28, 0, 15.45, 4.332, 0, 15.867, -1.95, 0, 16.367, 1.69, 0, 16.783, -0.858, 0, 17.15, 0.853, 0, 17.5, -1.028, 0, 17.783, 0.135, 2, 17.8, 0.135, 0, 17.85, 0.193, 0, 17.9, 0.178, 0, 18.167, 3.462, 0, 18.483, -11.187, 0, 18.8, 19.157, 0, 19.15, -22.094, 0, 19.5, 19.666, 0, 19.883, -14.914, 0, 20.417, 8.951, 0, 20.833, -20.031, 0, 21.25, 20.394, 0, 21.75, -18.365, 0, 22.183, 10.813, 0, 22.833, -16.457, 0, 23.25, 16.454, 0, 23.717, -11.227, 0, 24.217, 8.805, 0, 24.617, -8.492, 0, 25, 7.135, 0, 25.383, -4.374, 0, 25.817, 1.757, 0, 26.367, -1.652, 0, 26.767, 1.551, 0, 27.15, -1.125, 0, 27.533, 0.701, 0, 27.95, -0.409, 0, 28.367, 0.245, 0, 28.8, -0.16, 0, 29.217, 0.098, 0, 29.633, -0.07, 0, 30.05, 0.035, 0, 30.167, 0.024, 0, 30.25, 0.146, 1, 30.272, 0.146, 30.295, 0.116, 30.317, 0, 1, 30.322, -0.029, 30.328, -0.171, 30.333, -0.171, 2, 30.583, -0.171]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.167, 1, 0, 3, 0, 0, 4.167, 1, 0, 6, 0, 0, 7.167, 1, 0, 9, 0, 0, 10.167, 1, 0, 12, 0, 0, 13.167, 1, 0, 15, 0, 0, 16.167, 1, 0, 18, 0, 0, 18.5, 1, 0, 20.333, 0, 0, 21.5, 1, 0, 23.333, 0, 0, 24.5, 1, 0, 29.817, 0, 2, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "fenshangxuanzhuang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 5.994, 2.4, 11.989, 4.8, 17.983, 7.2, 1, 22.094, 4.8, 26.206, 2.4, 30.317, 0, 2, 30.583, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamSad2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 30.583, 1]}, {"Target": "Parameter", "Id": "ParamCRY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.2, 0, 30.583, -5.2]}, {"Target": "Parameter", "Id": "Position_Coordinates", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 30.583, 1]}, {"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Man_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 30.583, 1]}, {"Target": "Parameter", "Id": "Man_TMD2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "BG_TMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 30.583, 1]}, {"Target": "Parameter", "Id": "FG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "FG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "BG_Black", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "BG_White", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "BG_White4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "BG_White5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "BG_White2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "All_Size4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "tuerzuo3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "faqiehuan", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "faqiehuan2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "faqiehuan4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "faqiehuan3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "tuerzuo4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Mail_Change", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Mail_Transparency9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "dafengshangzhuanchang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "tongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamTongue", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "EyeFrame_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamBrowY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallLsize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamEyeDisplay_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamSigh", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamDarken", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamCry", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_B", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "MB_Cycle_C", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "MB_Unidirectional_A", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "jing<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "huqi1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "huqi2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamArmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 30.583, 1]}, {"Target": "Parameter", "Id": "ParamForearmL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamArmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamArmL_ForearmL", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamHandLAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamHandLSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "xiongjiya", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "xiongjiya5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "xiongjiya3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "xiongjiya4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "xiongjiya2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamForearmR_RE4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamArmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 30.583, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamArmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamArmR_ForearmR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamForearm<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 30.583, 1]}, {"Target": "Parameter", "Id": "ParamForearmR_HandR", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamHandRAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamHandRSize", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 30.583, 0.6]}, {"Target": "Parameter", "Id": "Change_idle_physics23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Param103", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "taiqiugundong", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -22.5, 0, 30.583, -22.5]}, {"Target": "Parameter", "Id": "Change_idle_physics32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Change_idle_physics22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamLegL_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 30.583, 1]}, {"Target": "Parameter", "Id": "ParamLegLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamCrusLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamCrusLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "<PERSON>m<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>oodLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamFoodLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamFoodLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamLegR_RE", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 30.583, 1]}, {"Target": "Parameter", "Id": "ParamLegRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamCrusRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamCrusRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamCrus<PERSON>ayer", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Param<PERSON>ood<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamFoodRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamFoodRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Physics_Provisional", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "physics_Add_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "physics_Add_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamBodyStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Physics_Body_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Physics_Body_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Physics_Meat_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Physics_Joint_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Physics_breast", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "SD_ParamHair_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "SD_BustMotionY_3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamBreath2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngle_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyX2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamLowerBodyZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "ParamBGMoveY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "touch_drag1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "touch_drag2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "touch_drag3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "touch_drag4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "touch_drag5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "touch_drag6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "touch_drag7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "touch_drag8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "touch_drag9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Paramtouch_idle19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON>pian<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "zhaopiankuoshang5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_1_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_2_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_3_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_4_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh653", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh654", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh655", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh657", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh656", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh658", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh659", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}, {"Target": "Parameter", "Id": "Param_Angle_Rotation_5_ArtMesh660", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 30.583, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 30.083, "Value": ""}]}