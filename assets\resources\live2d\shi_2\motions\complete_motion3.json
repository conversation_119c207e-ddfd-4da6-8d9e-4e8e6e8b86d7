{"Version": 3, "Meta": {"Duration": 8.5, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 285, "TotalSegmentCount": 2102, "TotalPointCount": 2602, "UserDataCount": 2, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.667, 0.123, 0, 2.367, -0.492, 0, 3.483, 0.031, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.667, 0.059, 1, 1.234, 0.059, 1.8, 0.06, 2.367, 0.046, 1, 2.739, 0.037, 3.111, -0.209, 3.483, -0.209, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "All_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.134, 0, 0.417, -0.281, 0, 0.733, 0.311, 0, 1.117, -0.417, 0, 1.75, 0.592, 0, 2.183, 0.461, 0, 2.65, 0.769, 0, 3.267, -0.492, 0, 3.85, 0.2, 0, 4.467, -0.132, 0, 4.85, 0.254, 0, 5.15, 0.106, 0, 5.533, 0.37, 0, 6.05, 0.226, 0, 6.45, 0.817, 0, 6.783, -0.032, 0, 7.333, 0.263, 0, 7.65, 0.195, 0, 8.317, 0.507, 0, 8.5, -0.134]}, {"Target": "Parameter", "Id": "All_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.244, 0, 0.45, -0.516, 0, 0.967, 0.667, 0, 2.367, -0.886, 0, 2.8, 0.807, 0, 3.567, -0.594, 0, 4.183, -0.295, 0, 4.517, -0.454, 1, 4.728, -0.454, 4.939, -0.472, 5.15, -0.38, 1, 5.372, -0.283, 5.595, 0.43, 5.817, 0.43, 0, 6.483, -0.706, 0, 7.2, 0.032, 0, 7.517, -0.021, 0, 7.717, 0.047, 0, 8.383, -0.498, 0, 8.5, -0.244]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 0.222, 0, 0.445, -0.095, 0.667, 0.39, 1, 1.234, 1.628, 1.8, 3.203, 2.367, 3.203, 1, 2.739, 3.203, 3.111, 2.053, 3.483, 1.753, 1, 5.155, 0.405, 6.828, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.783, 10, 0, 2.7, -4.62, 0, 3.95, -4.018, 0, 5.367, -8.284, 1, 5.689, -8.284, 6.011, -8.749, 6.333, -4.169, 1, 6.644, 0.253, 6.956, 20, 7.267, 20, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamAngleX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.304, 0, 0.3, -8.126, 0, 0.85, 17.786, 0, 2.317, -14.702, 0, 3.117, 17.554, 0, 3.65, 3.597, 0, 4.25, 8.107, 0, 5.083, 3.052, 0, 5.5, 9.326, 0, 6.2, -11.927, 0, 6.917, -0.316, 0, 7.217, -3.699, 0, 7.517, 4, 0, 8.033, -15.085, 0, 8.5, -1.304]}, {"Target": "Parameter", "Id": "ParamAngleY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.124, 0, 0.233, -3.912, 0, 0.583, 9.215, 0, 1.083, -8.809, 0, 1.7, 14.328, 0, 2.15, 11.751, 0, 2.617, 17.114, 0, 3.283, -6.412, 0, 3.883, 6.259, 0, 4.583, 0.154, 0, 5.183, 11.593, 0, 5.767, 5.059, 0, 6.167, 16.445, 0, 6.533, 2.492, 0, 7.067, 6.421, 0, 7.383, 4.744, 0, 8.117, 15.804, 0, 8.5, -0.124]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.543, 0, 0.3, -3.35, 0, 0.7, -1.899, 0, 1.083, -5.918, 1, 1.3, -5.918, 1.516, -3.749, 1.733, -3.342, 1, 2.061, -2.726, 2.389, -2.728, 2.717, -2.728, 1, 2.861, -2.728, 3.006, -4.585, 3.15, -5.621, 1, 3.272, -6.497, 3.395, -6.472, 3.517, -6.472, 0, 3.967, -4.851, 0, 4.6, -5.506, 0, 5.217, 0, 0, 5.633, -4.66, 0, 6.2, 2, 1, 6.35, 2, 6.5, -5.575, 6.65, -7.411, 1, 6.911, -10.608, 7.172, -10.852, 7.433, -10.852, 0, 8.15, 1.944, 0, 8.5, 0.543]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -4.357, 0, 0.517, -0.087, 0, 0.9, -10.506, 1, 1.117, -10.506, 1.333, 3.166, 1.55, 4.273, 1, 1.878, 5.948, 2.205, 5.867, 2.533, 5.867, 1, 2.678, 5.867, 2.822, -6.66, 2.967, -9.735, 1, 3.089, -12.337, 3.211, -11.942, 3.333, -11.942, 0, 3.783, -7.739, 0, 4.417, -9.438, 1, 4.761, -9.438, 5.106, -9.453, 5.45, -8, 1, 5.672, -7.063, 5.895, -0.775, 6.117, -0.775, 1, 6.272, -0.775, 6.428, -5.577, 6.583, -7.411, 1, 6.844, -10.49, 7.106, -10.852, 7.367, -10.852, 0, 8.017, -1.315, 0, 8.5, -4.357]}, {"Target": "Parameter", "Id": "ParamBodyAngleX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -8.417, 0, 0.183, -13, 0, 0.633, 8.898, 1, 0.822, 8.898, 1.011, -1.611, 1.2, -3.294, 1, 1.522, -6.165, 1.845, -6.258, 2.167, -6.258, 1, 2.306, -6.258, 2.444, 5.671, 2.583, 7.377, 1, 2.694, 8.741, 2.806, 8.418, 2.917, 8.418, 0, 3.417, 1.913, 0, 4.083, 3.784, 0, 4.867, 2.496, 0, 5.283, 6.328, 0, 6, -7.606, 0, 6.333, 1.013, 0, 6.817, -19, 0, 7.267, 0, 0, 7.85, -14.891, 0, 8.317, 1.013, 0, 8.5, -8.417]}, {"Target": "Parameter", "Id": "ParamBodyAngleY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -9.558, 0, 0.417, 8.992, 0, 0.917, -24.296, 0, 1.483, 23.968, 0, 1.933, 20.304, 0, 2.433, 28.939, 1, 2.583, 28.939, 2.733, -3.871, 2.883, -18.303, 1, 2.983, -27.925, 3.083, -26.947, 3.183, -26.947, 0, 3.7, 1.602, 0, 4.383, -7.125, 0, 4.767, 0.512, 0, 5.083, -4.082, 1, 5.25, -4.082, 5.416, 3.387, 5.583, 9, 1, 5.722, 13.678, 5.861, 14, 6, 14, 0, 6.4, -2, 0, 7.083, 13.762, 0, 7.55, -1, 0, 7.967, 14, 0, 8.5, -9.558]}, {"Target": "Parameter", "Id": "ParamWaistZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -13.805, 0, 0.183, -14.902, 0, 0.6, -8.143, 0, 1.167, -19.479, 0, 1.6, -17.429, 0, 2.133, -19.592, 0, 2.683, -7.929, 0, 3.483, -14.873, 1, 3.589, -14.873, 3.694, -14.182, 3.8, -14.09, 1, 3.85, -14.046, 3.9, -14.066, 3.95, -14.066, 1, 4.122, -14.066, 4.295, -15.913, 4.467, -15.955, 1, 4.606, -15.989, 4.744, -15.978, 4.883, -15.978, 0, 5.367, -11.925, 0, 5.9, -17.722, 0, 6.183, -16.878, 0, 6.483, -18.103, 0, 7.25, -11.376, 0, 7.767, -17.55, 0, 8.35, -12.564, 0, 8.483, -13.81, 0, 8.5, -13.805]}, {"Target": "Parameter", "Id": "ParamShoulderStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.893, 0, 0.283, 8.992, 0, 0.783, -24.296, 0, 1.35, 16.791, 0, 1.8, 14.271, 0, 2.3, 20.208, 1, 2.45, 20.208, 2.6, -2.299, 2.75, -12.273, 1, 2.85, -18.922, 2.95, -18.216, 3.05, -18.216, 0, 3.567, 1.602, 0, 4.25, -7.125, 0, 4.633, -0.487, 1, 4.739, -0.487, 4.844, -2.722, 4.95, -5.082, 1, 5.117, -8.808, 5.283, -9.971, 5.45, -9.971, 0, 5.883, 2.28, 1, 6.072, 2.28, 6.261, -5.821, 6.45, -6.765, 1, 6.767, -8.347, 7.083, -8.35, 7.4, -8.35, 0, 7.95, -1.991, 0, 8.5, -2.893]}, {"Target": "Parameter", "Id": "ParamShoulderStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.371, 0, 0.483, 8.992, 0, 0.983, -24.296, 0, 1.55, 16.791, 0, 2, 14.271, 0, 2.5, 20.208, 1, 2.65, 20.208, 2.8, -2.299, 2.95, -12.273, 1, 3.05, -18.922, 3.15, -18.216, 3.25, -18.216, 0, 3.767, 1.602, 0, 4.45, -7.125, 0, 4.833, -0.487, 1, 4.939, -0.487, 5.044, -1.097, 5.15, -3.573, 1, 5.317, -7.482, 5.483, -10.317, 5.65, -10.317, 0, 6.083, 0.605, 1, 6.239, 0.605, 6.394, -6.536, 6.55, -7.306, 1, 6.872, -8.901, 7.195, -9.006, 7.517, -9.006, 0, 8.05, -2.187, 0, 8.5, -3.371]}, {"Target": "Parameter", "Id": "ParamButtXA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -6.39, 1, 0.061, -6.387, 0.122, -5.23, 0.183, -5.227, 1, 0.328, -5.233, 0.472, -9.995, 0.617, -10, 1, 0.806, -9.996, 0.994, 8.557, 1.183, 8.566, 1, 1.344, 8.565, 1.506, 7.473, 1.667, 7.472, 1, 1.828, 7.476, 1.989, 11.074, 2.15, 11.077, 1, 2.394, 11.065, 2.639, -6.102, 2.883, -6.102, 1, 3.422, -6.127, 3.961, 2.981, 4.5, 2.978, 1, 4.767, 2.977, 5.033, -2, 5.3, -2, 1, 5.433, -1.996, 5.567, 5.996, 5.7, 6, 1, 5.878, 5.992, 6.055, -1.531, 6.233, -1.539, 1, 6.333, -1.541, 6.433, -0.388, 6.533, -0.39, 1, 6.772, -0.388, 7.011, -4.002, 7.25, -4, 1, 7.389, -3.997, 7.528, -0.003, 7.667, 0, 1, 7.85, 0.01, 8.034, -8.519, 8.217, -8.518, 1, 8.311, -8.509, 8.406, -6.39, 8.5, -6.39]}, {"Target": "Parameter", "Id": "Param43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.915, 1, 0.083, -1.923, 0.167, -3.992, 0.25, -4, 1, 0.417, -4.012, 0.583, 6.612, 0.75, 6.613, 1, 0.939, 6.607, 1.128, -4.913, 1.317, -4.911, 1, 1.467, -4.913, 1.617, -4.034, 1.767, -4.036, 1, 1.934, -4.032, 2.1, -6.102, 2.267, -6.098, 1, 2.495, -6.102, 2.722, 7.258, 2.95, 7.261, 1, 3.5, 7.248, 4.05, -0.593, 4.6, -0.59, 1, 4.706, -0.597, 4.811, 1.199, 4.917, 1.192, 1, 5.078, 1.188, 5.239, -2.996, 5.4, -3, 1, 5.55, -3.006, 5.7, 0.136, 5.85, 0.13, 1, 6.011, 0.129, 6.172, -1.252, 6.333, -1.253, 1, 6.416, -1.249, 6.5, -0.092, 6.583, -0.088, 1, 6.844, -0.091, 7.106, -5.997, 7.367, -6, 1, 7.517, -5.998, 7.667, 3.435, 7.817, 3.44, 1, 7.984, 3.447, 8.15, -3.147, 8.317, -3.148, 1, 8.378, -3.145, 8.439, -1.915, 8.5, -1.915]}, {"Target": "Parameter", "Id": "ParamButtZA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.239, 1, 0.15, -5.236, 0.3, -7.003, 0.45, -7, 1, 0.606, -7.009, 0.761, 4.207, 0.917, 4.198, 1, 1.1, 4.197, 1.284, 3.624, 1.467, 3.623, 1, 1.617, 3.619, 1.767, 5.522, 1.917, 5.518, 1, 2.217, 5.523, 2.517, -3.511, 2.817, -3.51, 1, 3.322, -3.538, 3.828, 1.411, 4.333, 1.404, 1, 4.45, 1.408, 4.566, -0.643, 4.683, -0.639, 1, 4.828, -0.641, 4.972, 0.67, 5.117, 0.674, 1, 5.417, 0.675, 5.717, -4.995, 6.017, -5, 1, 6.161, -4.999, 6.306, -4.45, 6.45, -4.449, 1, 6.583, -4.44, 6.717, -9.009, 6.85, -9, 1, 7.017, -9.014, 7.183, 2.998, 7.35, 3, 1, 7.561, 2.995, 7.772, -8.218, 7.983, -8.218, 1, 8.155, -8.224, 8.328, -5.239, 8.5, -5.239]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.7, 0, 2, 2.717, 15, 2, 3.05, 15, 2, 3.067, 0, 2, 7.4, 0, 0, 7.517, 15, 2, 8, 15, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param58", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.583, 0, 2, 2.467, 0, 0, 3.483, 15, 0, 6.833, 14.102, 0, 7.75, 30, 0, 8, 0, 2, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.583, 0, 2, 2.467, 0, 0, 3.05, 16, 1, 4.311, 16, 5.572, 15.84, 6.833, 15.111, 1, 7.139, 14.934, 7.444, 8.465, 7.75, 4.278, 1, 7.833, 3.136, 7.917, 0, 8, 0, 2, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 24.978, 1, 0.356, 24.978, 0.711, 24.947, 1.067, 23.768, 1, 1.239, 23.197, 1.411, -12, 1.583, -12, 2, 2.467, -12, 0, 2.767, 21.32, 0, 3.067, -0.52, 1, 4.322, -0.52, 5.578, 1.114, 6.833, 5.214, 1, 7.011, 5.794, 7.189, 7, 7.367, 7, 0, 7.75, 0, 1, 7.833, 0, 7.917, 24.95, 8, 24.959, 1, 8.167, 24.978, 8.333, 24.978, 8.5, 24.978]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -9.422, 0, 1.583, -2, 2, 2.467, -2, 0, 2.767, -6.962, 0, 3.067, 1.027, 0, 3.483, -7.94, 1, 4.778, -7.94, 6.072, -6.56, 7.367, -3, 1, 7.495, -2.649, 7.622, 0, 7.75, 0, 0, 8, -9.471, 0, 8.5, -9.422]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.038, 0, 0.483, -0.727, 1, 0.85, -0.727, 1.216, 19.791, 1.583, 22.42, 1, 1.878, 24.531, 2.172, 23.995, 2.467, 23.995, 1, 2.667, 23.995, 2.867, 12.619, 3.067, 4.19, 1, 3.206, -1.663, 3.344, -1.744, 3.483, -1.744, 1, 4.6, -1.744, 5.716, -0.942, 6.833, 1.07, 1, 6.989, 1.35, 7.144, 1.95, 7.3, 1.95, 0, 7.683, -1.166, 0, 7.933, 3.828, 0, 8.5, 2.038]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.871, 0, 0.267, 0.696, 0, 0.65, 1.863, 0, 1.583, -22.2, 1, 1.878, -22.2, 2.172, -22.246, 2.467, -18.84, 1, 2.667, -16.526, 2.867, 5.565, 3.067, 15.73, 1, 3.206, 22.789, 3.344, 24.824, 3.483, 24.824, 0, 6.833, 22.014, 0, 7.433, 26.86, 0, 8.067, -3.413, 0, 8.5, 0.871]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.131, 0, 0.15, 0.879, 0, 0.417, -5, 0, 0.933, 7.587, 0, 1.583, -5.498, 2, 2.467, -5.498, 1, 2.667, -5.498, 2.867, 2.67, 3.067, 6.22, 1, 3.206, 8.685, 3.344, 8.327, 3.483, 8.327, 0, 4.15, 7.917, 0, 4.6, 9.976, 0, 5.417, 7.066, 0, 5.883, 12.019, 1, 6.2, 12.019, 6.516, 11.238, 6.833, 7.24, 1, 7.011, 4.996, 7.189, 1, 7.367, 1, 0, 7.75, 18.58, 0, 8, -8.674, 0, 8.5, 0.131]}, {"Target": "Parameter", "Id": "Param5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.583, 0, 2, 2.467, 0, 0, 3.05, -24.6, 0, 6.833, -24.528, 0, 7.75, -24.84, 0, 8, 0, 2, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.583, 0, 2, 2.467, 0, 2, 2.883, 0, 0, 3.067, -30, 0, 5.417, -12.48, 0, 5.883, -18.571, 0, 6.833, -4.32, 0, 7.2, -30, 0, 7.583, 0, 0, 7.75, -8.033, 0, 7.983, 0, 2, 8, 0, 2, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.583, 0, 2, 2.467, 0, 0, 2.883, -30, 1, 2.944, -30, 3.006, 20.475, 3.067, 20.7, 1, 3.85, 23.588, 4.634, 26.786, 5.417, 28.76, 1, 5.889, 29.95, 6.361, 30, 6.833, 30, 1, 6.955, 30, 7.078, 30, 7.2, 19, 1, 7.328, 7.159, 7.455, -30, 7.583, -30, 0, 7.75, 0, 0, 7.983, -30, 1, 7.989, -30, 7.994, 0, 8, 0, 2, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamHandRREDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.583, 0, 2, 2.683, 0, 2, 2.7, 4, 2, 6.833, 4, 2, 7.75, 4, 2, 7.95, 0, 2, 8.5, 0]}, {"Target": "Parameter", "Id": "Param55", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 2, 0.917, 0, 2, 1.583, 0, 2, 2.467, 0, 0, 3.05, 18, 2, 7.25, 18, 0, 8, 0, 2, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 2, 0.917, 0, 2, 1.583, 0, 2, 2.8, 0, 2, 2.817, 20, 2, 7.25, 20, 2, 8, 20, 2, 8.017, 0, 2, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 2, 0.917, 0, 2, 1.583, 0, 2, 2.467, 0, 0, 3.05, -30, 2, 7.25, -30, 0, 8, 0, 2, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 2, 0.917, 0, 2, 1.583, 0, 2, 2.467, 0, 0, 3.05, 6.6, 2, 7.25, 6.6, 0, 8, 0, 2, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 0, 0.917, 4.38, 2, 1.583, 4.38, 2, 2.467, 4.38, 0, 3.05, 4.68, 2, 7.25, 4.68, 0, 8, 0, 2, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.089, 0, 0.417, -3, 1, 0.722, -3, 1.028, 17.912, 1.333, 24, 1, 1.461, 26.546, 1.589, 25.543, 1.717, 25.543, 1, 1.967, 25.543, 2.217, 25.358, 2.467, 22.358, 1, 2.617, 20.558, 2.767, 5.686, 2.917, 3.72, 1, 3.178, 0.298, 3.439, 0, 3.7, 0, 0, 5.317, 1.41, 0, 6.883, 1.128, 0, 7.1, 1.45, 0, 7.417, 0.085, 0, 7.933, 2.921, 0, 8.5, -0.089]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.769, 0, 0.333, -0.96, 1, 0.639, -0.96, 0.944, 12.167, 1.25, 21, 1, 1.378, 24.694, 1.505, 23.933, 1.633, 23.933, 1, 1.911, 23.933, 2.189, 23.275, 2.467, 18.173, 1, 2.617, 15.418, 2.767, -2.419, 2.917, -4.7, 1, 3.178, -8.671, 3.439, -9, 3.7, -9, 1, 4.239, -9, 4.778, -8.149, 5.317, -7.66, 1, 5.917, -7.116, 6.517, -7.08, 7.117, -7.08, 0, 7.483, -8.168, 0, 8, 5.449, 0, 8.5, 0.769]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.105, 0, 0.517, 9, 1, 0.822, 9, 1.128, 6.214, 1.433, -0.32, 1, 1.561, -3.052, 1.689, -5, 1.817, -5, 0, 2.467, -0.32, 0, 2.917, -20.11, 1, 3.178, -20.11, 3.439, -12.488, 3.7, -11.58, 1, 4.239, -9.707, 4.778, -9.58, 5.317, -9.58, 0, 7.183, -10.08, 0, 7.55, -3, 0, 8.067, -16.305, 0, 8.5, -3.105]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 2, 0.917, 0, 2, 1.583, 0, 2, 2.467, 0, 0, 3.05, -8.1, 2, 7.25, -8.1, 0, 8, 0, 2, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 2, 0.917, 0, 2, 1.583, 0, 2, 2.467, 0, 0, 3.083, 30, 0, 3.7, 1, 2, 7.25, 1, 0, 8, 0, 2, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 2, 0.917, 0, 2, 1.583, 0, 2, 2.467, 0, 0, 3.083, -2, 0, 3.7, 7, 2, 7.25, 7, 0, 8, 0, 2, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamHandLREDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.417, 1, 2, 2.817, 1, 2, 2.833, 5, 2, 7.783, 5, 2, 7.8, 1, 2, 8.5, 1]}, {"Target": "Parameter", "Id": "Param3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.499, 1, 0.1, -0.612, 0.2, -1.422, 0.3, -1.408, 1, 0.444, -1.41, 0.589, 0.301, 0.733, 0.298, 1, 0.755, 0.299, 0.778, 0.294, 0.8, 0.253, 1, 0.806, 0.25, 0.811, 0.348, 0.817, 0.345, 1, 0.872, 0.278, 0.928, 0.153, 0.983, 0.033, 1, 0.994, -0.02, 1.006, 0.471, 1.017, 0.41, 1, 1.322, 0.348, 1.628, -1.455, 1.933, -1.517, 1, 2.022, -1.551, 2.111, -0.993, 2.2, -1, 1, 2.222, -1.081, 2.245, -1.129, 2.267, -1.125, 1, 2.278, -1.117, 2.289, -1.108, 2.3, -1.1, 1, 2.306, -1.111, 2.311, -1.122, 2.317, -1.133, 1, 2.456, -1.074, 2.594, 2.36, 2.733, 2.36, 1, 2.744, 2.402, 2.756, 2.034, 2.767, 2.077, 1, 2.789, 2.217, 2.811, 2.188, 2.833, 2.205, 1, 3.139, 2.203, 3.444, -0.971, 3.75, -0.971, 1, 4.128, -0.968, 4.505, 1.289, 4.883, 1.297, 1, 5.022, 1.297, 5.161, 0.701, 5.3, 0.7, 1, 5.456, 0.69, 5.611, 3.378, 5.767, 3.382, 1, 5.984, 3.388, 6.2, -1.095, 6.417, -1.075, 1, 6.567, -1.076, 6.717, 0.477, 6.867, 0.493, 1, 6.989, 0.496, 7.111, -1.145, 7.233, -1.139, 1, 7.416, -1.143, 7.6, 2.37, 7.783, 2.362, 1, 7.989, 2.359, 8.194, -0.69, 8.4, -0.71, 1, 8.433, -0.639, 8.467, -0.499, 8.5, -0.499]}, {"Target": "Parameter", "Id": "Param2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.661, 1, 0.106, 1.232, 0.211, 3.608, 0.317, 3.593, 1, 0.45, 3.562, 0.584, -1.035, 0.717, -1.061, 1, 0.745, -1.062, 0.772, -0.957, 0.8, -0.909, 1, 0.806, -0.906, 0.811, -1.038, 0.817, -1.034, 1, 0.872, -1.007, 0.928, -0.371, 0.983, -0.076, 1, 0.994, 0.036, 1.006, -1.058, 1.017, -0.917, 1, 1.345, -0.771, 1.672, 5.434, 2, 5.425, 1, 2.244, 5.421, 2.489, -5.276, 2.733, -5.281, 1, 2.744, -5.394, 2.756, -4.541, 2.767, -4.625, 1, 2.789, -4.739, 2.811, -4.776, 2.833, -4.77, 1, 3.144, -4.76, 3.456, 1.636, 3.767, 1.64, 1, 4.139, 1.635, 4.511, -3.132, 4.883, -3.154, 1, 5.022, -3.154, 5.161, -2.084, 5.3, -2.085, 1, 5.456, -2.084, 5.611, -7.35, 5.767, -7.346, 1, 5.984, -7.378, 6.2, 2.528, 6.417, 2.52, 1, 6.572, 2.513, 6.728, -1.269, 6.883, -1.223, 1, 7, -1.225, 7.116, 1.961, 7.233, 2.017, 1, 7.239, 2.017, 7.244, 2.016, 7.25, 2.016, 1, 7.267, 1.975, 7.283, 2.227, 7.3, 2.154, 1, 7.456, 2.132, 7.611, -5.437, 7.767, -5.435, 1, 7.956, -5.455, 8.144, 1.264, 8.333, 1.267, 1, 8.389, 1.256, 8.444, 0.661, 8.5, 0.661]}, {"Target": "Parameter", "Id": "Param", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 7.32, 1, 0.111, 8.067, 0.222, 9.272, 0.333, 9.274, 1, 0.972, 9.274, 1.611, -10.704, 2.25, -10.692, 1, 2.872, -10.673, 3.495, 8.654, 4.117, 8.658, 1, 4.284, 8.651, 4.45, 7.491, 4.617, 7.474, 1, 4.872, 7.452, 5.128, 10.349, 5.383, 10.347, 1, 5.722, 10.345, 6.061, 1.786, 6.4, 1.784, 1, 6.439, 1.772, 6.478, 1.88, 6.517, 1.859, 1, 6.561, 1.865, 6.606, 1.815, 6.65, 1.82, 1, 6.878, 1.826, 7.105, 9.329, 7.333, 9.323, 1, 7.533, 9.312, 7.733, 3.467, 7.933, 3.482, 1, 8.122, 3.489, 8.311, 7.32, 8.5, 7.32]}, {"Target": "Parameter", "Id": "Param44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 0.778, 0.001, 1.555, 2.399, 2.333, 2.4, 1, 2.822, 2.401, 3.311, -0.001, 3.8, 0, 2, 8.5, 0]}, {"Target": "Parameter", "Id": "Param6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.948, 0, 0.533, 3.066, 0, 1.15, 1.673, 0, 1.967, 3.167, 0, 3.283, 1.488, 0, 4.983, 3.341, 0, 5.517, 2.537, 0, 5.9, 3.016, 0, 6.35, 1.513, 0, 6.983, 2.529, 0, 7.35, 2.336, 0, 7.767, 3.366, 0, 8.5, 1.948]}, {"Target": "Parameter", "Id": "Param48", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -7.628, 0, 0.55, -11.883, 0, 1.15, -6.598, 0, 1.983, -12.411, 0, 3.283, -5.905, 0, 4.983, -12.873, 1, 5.289, -12.873, 5.594, -12.799, 5.9, -11.622, 1, 6.05, -11.044, 6.2, -6.035, 6.35, -6.035, 0, 6.983, -9.84, 0, 7.35, -9.112, 0, 7.767, -13.004, 0, 8.5, -7.628]}, {"Target": "Parameter", "Id": "Param37", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 9.009, 0, 0.65, 12.248, 0, 1.117, 7.972, 0, 2.033, 20.035, 0, 4.65, 7.201, 0, 5.067, 8.386, 0, 5.733, 4.843, 0, 6.433, 15.801, 0, 6.983, 11.467, 0, 7.233, 11.993, 0, 7.583, 10.286, 0, 8.067, 12.204, 0, 8.5, 9.009]}, {"Target": "Parameter", "Id": "Param38", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 2.333, -2.445, 0, 3.8, 0, 2, 5.233, 0, 0, 6.25, -2.97, 0, 7.05, 0, 0, 7.633, -1.779, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.9, 0, 0.333, 1, 0, 0.817, 0, 2, 1.433, 0, 0, 2.117, 0.9, 2, 3.033, 0.9, 0, 3.35, 0, 0, 3.667, 0.9, 2, 4.517, 0.9, 0, 4.717, 0, 0, 4.933, 0.9, 2, 5.8, 0.9, 0, 6.05, 1.056, 0, 6.3, 0.9, 2, 7.267, 0.9, 0, 7.467, 0, 0, 7.683, 0.9, 2, 8.5, 0.9]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 1, 0.394, 0, 0.456, 0.104, 0.517, 0.2, 1, 0.617, 0.356, 0.717, 0.4, 0.817, 0.4, 2, 1.35, 0.4, 0, 1.517, 0, 0, 1.817, 0.6, 0, 2.117, 0, 2, 3.2, 0, 0, 3.45, 0.5, 0, 3.883, 0, 2, 4.617, 0, 0, 4.783, 0.6, 0, 5.067, 0, 2, 5.9, 0, 0, 6.15, 0.084, 0, 6.4, 0, 2, 7.367, 0, 0, 7.533, 1, 0, 7.817, 0, 2, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.9, 0, 0.333, 1, 0, 0.817, 0, 2, 1.433, 0, 0, 2.117, 0.9, 2, 3.033, 0.9, 0, 3.35, 0, 0, 3.667, 0.9, 2, 4.517, 0.9, 0, 4.717, 0, 0, 4.933, 0.9, 2, 5.8, 0.9, 0, 6.05, 1.047, 0, 6.3, 0.9, 2, 7.267, 0.9, 0, 7.467, 0, 0, 7.683, 0.9, 2, 8.5, 0.9]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 1, 0.394, 0, 0.456, 0.104, 0.517, 0.2, 1, 0.617, 0.356, 0.717, 0.4, 0.817, 0.4, 2, 1.35, 0.4, 0, 1.517, 0, 0, 1.817, 0.6, 0, 2.117, 0, 2, 3.2, 0, 0, 3.45, 0.5, 0, 3.883, 0, 2, 4.617, 0, 0, 4.783, 0.6, 0, 5.067, 0, 2, 5.9, 0, 0, 6.15, 0.091, 0, 6.4, 0, 2, 7.367, 0, 0, 7.533, 1, 0, 7.817, 0, 2, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.817, 0.07, 2, 1.433, 0.07, 0, 2.117, 0, 2, 3.033, 0, 0, 3.333, 0.07, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, 0.07, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, 0.07, 0, 7.683, 0, 2, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.817, 0.364, 2, 1.433, 0.364, 0, 2.117, 0, 2, 3.033, 0, 0, 3.333, 0.364, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, 0.364, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, 0.364, 0, 7.683, 0, 2, 8.5, 0]}, {"Target": "Parameter", "Id": "Param4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.817, -0.586, 2, 1.433, -0.586, 0, 2.117, 0, 2, 3.033, 0, 0, 3.333, -0.586, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, -0.586, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, -0.586, 0, 7.683, 0, 2, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.817, 0.262, 2, 1.433, 0.262, 0, 2.117, 0, 2, 3.033, 0, 0, 3.333, 0.262, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, 0.262, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, 0.262, 0, 7.683, 0, 2, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.817, 0.366, 2, 1.433, 0.366, 0, 2.117, 0, 2, 3.033, 0, 0, 3.333, 0.366, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, 0.366, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, 0.366, 0, 7.683, 0, 2, 8.5, 0]}, {"Target": "Parameter", "Id": "Param11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.817, -0.572, 2, 1.433, -0.572, 0, 2.117, 0, 2, 3.033, 0, 0, 3.333, -0.572, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, -0.572, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, -0.572, 0, 7.683, 0, 2, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.15, -0.244, 0, 0.333, -0.01, 0, 0.45, -0.319, 2, 0.633, -0.319, 2, 0.817, -0.319, 0, 0.9, 0.656, 0, 0.95, -0.319, 2, 1.133, -0.319, 2, 1.333, -0.319, 2, 1.367, -0.319, 2, 1.533, -0.319, 2, 1.683, -0.319, 0, 1.75, 0.34, 1, 1.817, 0.34, 1.883, 0.257, 1.95, -0.01, 1, 1.989, -0.166, 2.028, -0.319, 2.067, -0.319, 0, 2.283, -0.244, 0, 2.4, -0.319, 0, 2.617, 0.808, 2, 3.35, 0.808, 0, 3.55, 0, 0, 4.067, 1, 0, 4.15, 0.656, 0, 4.2, 1, 0, 4.383, -0.319, 0, 4.583, 0.224, 2, 4.717, 0.224, 0, 4.883, 0.34, 1, 5.161, 0.34, 5.439, 0.256, 5.717, 0, 1, 5.767, -0.046, 5.817, -0.244, 5.867, -0.244, 0, 6.05, -0.01, 0, 6.167, -0.319, 2, 6.35, -0.319, 2, 6.533, -0.319, 0, 6.617, 0.656, 0, 6.667, -0.319, 2, 6.85, -0.319, 2, 7.05, -0.319, 2, 7.083, -0.319, 2, 7.25, -0.319, 2, 7.4, -0.319, 0, 7.467, 0.34, 1, 7.534, 0.34, 7.6, 0.257, 7.667, -0.01, 1, 7.706, -0.166, 7.744, -0.319, 7.783, -0.319, 0, 8.133, 0, 2, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 0.05, 0, 0.1, 0.185, 0.15, 0.5, 1, 0.206, 0.85, 0.261, 1, 0.317, 1, 0, 0.5, 0.102, 0, 0.683, 0.969, 0, 0.9, 0.012, 0, 1.1, 0.663, 0, 1.183, 0.024, 0, 1.333, 0.871, 2, 1.367, 0.871, 0, 1.65, 0, 0, 1.933, 1, 0, 2.1, 0.102, 0, 2.283, 0.5, 1, 2.333, 0.5, 2.383, 0.188, 2.433, 0.102, 1, 2.494, 0, 2.556, 0, 2.617, 0, 2, 3.35, 0, 0, 3.55, 0.5, 0, 4.083, 0.323, 0, 4.2, 0.9, 0, 4.283, 0.016, 0, 4.35, 0.663, 0, 4.433, 0.024, 0, 4.583, 0.871, 2, 4.717, 0.871, 0, 4.883, 0, 2, 5.717, 0, 1, 5.767, 0, 5.817, 0.185, 5.867, 0.5, 1, 5.922, 0.85, 5.978, 1, 6.033, 1, 0, 6.217, 0.102, 0, 6.4, 0.969, 0, 6.617, 0.012, 0, 6.817, 0.663, 0, 6.9, 0.024, 0, 7.05, 0.871, 2, 7.083, 0.871, 0, 7.367, 0, 0, 7.65, 1, 1, 7.706, 1, 7.761, 0.21, 7.817, 0.102, 1, 7.878, 0, 7.939, 0, 8, 0, 2, 8.133, 0, 2, 8.5, 0]}, {"Target": "Parameter", "Id": "Hair_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.233, 3.354, 0, 0.717, -5, 0, 1.25, 2.861, 0, 1.683, -2.017, 0, 2.133, 6.153, 0, 2.567, -3.961, 0, 2.983, 2.6, 0, 3.333, -3.965, 0, 3.983, 1.635, 0, 4.333, -1.438, 0, 5.183, 2.718, 0, 5.517, -5.793, 0, 5.95, 3.691, 0, 6.433, -3.202, 0, 6.85, 2.604, 0, 7.4, -2.699, 0, 7.833, 1.26, 0, 8.317, -1.381, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Hair_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.233, 3.354, 0, 0.717, -5, 0, 1.25, 2.861, 0, 1.683, -2.017, 0, 2.133, 6.153, 0, 2.567, -3.961, 0, 2.983, 2.6, 0, 3.333, -3.965, 0, 3.983, 1.635, 0, 4.333, -1.438, 0, 5.183, 2.718, 0, 5.517, -5.793, 0, 5.95, 3.691, 0, 6.433, -3.202, 0, 6.85, 2.604, 0, 7.4, -2.699, 0, 7.833, 1.26, 0, 8.317, -1.381, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param69", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.233, 3.354, 0, 0.717, -8.454, 0, 1.25, 6.288, 0, 1.683, -5.444, 0, 2.133, 7.707, 0, 2.567, -4.903, 0, 2.983, 2.6, 0, 3.333, -3.965, 0, 3.983, 1.635, 0, 4.333, -1.438, 0, 5.183, 2.718, 0, 5.517, -5.793, 0, 5.95, 3.691, 0, 6.433, -3.202, 0, 6.85, 2.604, 0, 7.4, -2.699, 0, 7.833, 4.171, 0, 8.317, -1.381, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param153", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.2, 9.751, 0, 0.517, -18.636, 0, 0.9, 18.447, 0, 1.4, -14.236, 0, 1.867, 8.604, 0, 2.983, -11.843, 0, 3.4, 15.844, 0, 3.85, -8.439, 0, 4.617, 13.067, 0, 5.067, -13.93, 0, 5.55, 11.746, 0, 6.267, -12.909, 0, 6.85, 21.592, 0, 7.367, -22.954, 0, 7.783, 8.152, 0, 8.017, 1.798, 0, 8.3, 16.589, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param154", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.017, 0.354, 0, 0.183, -3.772, 0, 0.467, 14.598, 0, 0.783, -19.579, 0, 1.2, 17.732, 0, 1.65, -12.759, 0, 2.05, 6.862, 0, 2.483, -1.991, 0, 2.9, 5.988, 0, 3.283, -12.664, 0, 3.667, 14.297, 0, 4.083, -8.236, 0, 4.4, -0.067, 0, 4.567, -2.415, 0, 4.9, 11.228, 0, 5.333, -12.925, 0, 5.8, 8.634, 0, 6.65, -9.833, 0, 7.167, 16.035, 0, 7.583, -19.123, 0, 8, 10.285, 0, 8.283, -8.403, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param155", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.283, -0.875, 0, 0.6, 9.642, 0, 0.967, -16.193, 0, 1.383, 18.085, 0, 1.8, -15.364, 0, 2.25, 9.414, 0, 2.667, -3.855, 0, 3.067, 5.16, 0, 3.433, -10.743, 0, 3.85, 13.618, 0, 4.267, -9.857, 0, 5.083, 8.246, 0, 5.5, -12.435, 0, 5.967, 10.201, 0, 6.75, -8.067, 0, 7.317, 15.216, 0, 7.75, -18.457, 0, 8.15, 11.642, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param156", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.117, -5.86, 0, 0.75, 7.257, 0, 1.117, -13.459, 0, 1.533, 16.851, 0, 1.967, -16.295, 0, 2.417, 11.445, 0, 2.85, -5.717, 0, 3.233, 4.871, 0, 3.617, -9.354, 0, 4.033, 12.677, 0, 4.433, -10.987, 0, 5.183, 5.749, 0, 5.667, -11.073, 0, 6.133, 10.916, 0, 6.767, -6.731, 0, 7.433, 13.412, 0, 7.917, -17.443, 0, 8.333, 12.547, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param66", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.1, 0, 0, 0.267, 7.043, 0, 0.5, -14.704, 0, 0.817, 18.511, 0, 1.2, -14.037, 0, 1.783, 12.887, 0, 2.217, -16.514, 0, 2.667, 21.018, 0, 3.067, -23.694, 0, 3.467, 25.628, 0, 3.8, -18.354, 0, 4.1, 5.478, 0, 4.3, -2.724, 0, 4.533, 10.002, 0, 4.833, -8.729, 0, 5.117, 3.966, 0, 5.45, -7.813, 0, 5.75, 17.539, 0, 6.067, -20.361, 0, 6.417, 14.824, 0, 6.883, -7.629, 0, 7.3, 5.885, 0, 7.567, 0.527, 0, 7.683, 1.139, 0, 8, -7.606, 0, 8.333, 8.606, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param67", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.183, -12.957, 0, 0.5, 25.343, 0, 0.95, -27.311, 0, 1.467, 26.789, 0, 1.9, -30, 2, 1.917, -30, 0, 2.283, 30, 2, 2.433, 30, 0, 2.7, -30, 2, 2.85, -30, 0, 3.117, 30, 2, 3.25, 30, 0, 3.533, -21.492, 0, 4.217, 19.331, 0, 4.533, -11.917, 0, 5.417, 22.705, 0, 5.75, -26.889, 0, 6.133, 19.262, 0, 6.633, -14.934, 0, 7.067, 10.869, 0, 7.65, -14.764, 0, 8.033, 13.996, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param68", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.15, 7.586, 0, 0.4, -16.887, 0, 0.75, 18.082, 0, 1.133, -13.358, 0, 1.717, 15.904, 0, 2.15, -17.993, 0, 2.6, 24.368, 0, 3, -25.29, 0, 3.367, 28.271, 0, 3.7, -15.709, 0, 4, 2.013, 0, 4.167, -4.281, 0, 4.4, 13.176, 0, 4.7, -7.886, 0, 5.017, 1.975, 0, 5.35, -9.896, 0, 5.633, 20.655, 0, 5.967, -20.377, 0, 6.333, 13.057, 0, 6.85, -8.889, 0, 7.217, 5.738, 0, 7.417, 1.813, 0, 7.567, 3.208, 0, 7.9, -9.935, 0, 8.25, 8.415, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param62", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.1, 0, 0, 0.267, -7.043, 0, 0.5, 14.704, 0, 0.817, -18.511, 0, 1.2, 14.037, 0, 1.783, -12.887, 0, 2.217, 16.514, 0, 2.667, -21.018, 0, 3.067, 23.694, 0, 3.467, -25.628, 0, 3.8, 18.354, 0, 4.1, -5.478, 0, 4.3, 2.724, 0, 4.533, -10.002, 0, 4.833, 8.729, 0, 5.117, -3.966, 0, 5.45, 7.813, 0, 5.75, -17.539, 0, 6.067, 20.361, 0, 6.417, -14.824, 0, 6.883, 7.629, 0, 7.3, -5.885, 0, 7.567, -0.527, 0, 7.683, -1.139, 0, 8, 7.606, 0, 8.333, -8.606, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param63", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.183, 12.957, 0, 0.5, -25.343, 0, 0.95, 27.311, 0, 1.467, -26.789, 0, 1.9, 30, 2, 1.917, 30, 0, 2.283, -30, 2, 2.433, -30, 0, 2.7, 30, 2, 2.85, 30, 0, 3.117, -30, 2, 3.25, -30, 0, 3.533, 21.492, 0, 4.217, -19.331, 0, 4.533, 11.917, 0, 5.417, -22.705, 0, 5.75, 26.889, 0, 6.133, -19.262, 0, 6.633, 14.934, 0, 7.067, -10.869, 0, 7.65, 14.764, 0, 8.033, -13.996, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param64", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.15, -7.586, 0, 0.4, 16.887, 0, 0.75, -18.082, 0, 1.133, 13.358, 0, 1.717, -15.904, 0, 2.15, 17.993, 0, 2.6, -24.368, 0, 3, 25.29, 0, 3.367, -28.271, 0, 3.7, 15.709, 0, 4, -2.013, 0, 4.167, 4.281, 0, 4.4, -13.176, 0, 4.7, 7.886, 0, 5.017, -1.975, 0, 5.35, 9.896, 0, 5.633, -20.655, 0, 5.967, 20.377, 0, 6.333, -13.057, 0, 6.85, 8.889, 0, 7.217, -5.738, 0, 7.417, -1.813, 0, 7.567, -3.208, 0, 7.9, 9.935, 0, 8.25, -8.415, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param86", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.233, -1.467, 0, 0.717, 2.187, 0, 1.25, -1.252, 0, 1.683, 0.882, 0, 2.133, -2.692, 0, 2.567, 2.531, 0, 2.983, -2.203, 0, 3.333, 2.534, 0, 3.983, -1.507, 0, 4.35, 1.857, 0, 5.183, -1.189, 0, 5.517, 2.534, 0, 5.95, -1.614, 0, 6.433, 1.4, 0, 6.85, -1.139, 0, 7.4, 1.18, 0, 7.833, -0.551, 0, 8.317, 0.604, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param89", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.183, -9.455, 0, 0.5, 11.883, 0, 0.917, -9.699, 0, 1.45, 7.08, 0, 1.917, -10.436, 0, 2.367, 16.693, 0, 2.767, -18.213, 0, 3.183, 20.602, 0, 3.533, -12.757, 0, 4.217, 11.627, 0, 4.533, -7.167, 0, 5.417, 13.648, 0, 5.75, -16.183, 0, 6.15, 11.469, 0, 6.633, -9.015, 0, 7.067, 6.537, 0, 7.633, -5.452, 0, 8, 4.524, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.167, 7.697, 0, 0.4, -13.522, 0, 0.733, 11.069, 0, 1.1, -6.551, 0, 1.75, 4.65, 0, 2.217, -8.584, 0, 2.617, 14.391, 0, 3.017, -16.024, 0, 3.383, 19.606, 0, 3.7, -11.001, 0, 4, 1.304, 0, 4.167, -3.041, 0, 4.4, 9.51, 0, 4.7, -5.694, 0, 5.017, 1.393, 0, 5.35, -7.137, 0, 5.633, 14.929, 0, 5.983, -13.742, 0, 6.333, 9.182, 0, 6.85, -6.47, 0, 7.217, 4.156, 0, 7.867, -4.927, 0, 8.2, 3.406, 1, 8.25, 3.406, 8.3, 3.379, 8.35, 2.497, 1, 8.4, 1.615, 8.45, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param88", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.15, 0, 0, 0.317, -7.614, 0, 0.533, 13.159, 0, 0.833, -13.001, 0, 1.2, 8.752, 0, 1.65, -3.261, 0, 1.7, -3.243, 0, 1.8, -3.393, 0, 2.283, 7.305, 0, 2.683, -12.691, 0, 3.083, 15.315, 0, 3.467, -17.939, 0, 3.8, 13.114, 0, 4.1, -3.828, 0, 4.3, 1.835, 0, 4.533, -7.309, 0, 4.833, 6.406, 0, 5.117, -2.921, 0, 5.45, 5.725, 0, 5.75, -12.915, 0, 6.067, 14.619, 0, 6.417, -10.586, 0, 6.883, 5.719, 0, 7.3, -4.359, 0, 7.733, 0.605, 0, 7.75, 0.581, 0, 7.983, 3.551, 0, 8.3, -3.449, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.3, 0, 0, 0.467, 5.777, 0, 0.667, -11.068, 0, 0.95, 13.25, 0, 1.267, -9.428, 0, 1.633, 4.108, 0, 2.35, -6.298, 0, 2.783, 11.165, 0, 3.167, -14.603, 0, 3.567, 16.809, 0, 3.9, -14.287, 0, 4.217, 6.303, 0, 4.417, -1.915, 0, 4.65, 5.65, 0, 4.933, -6.616, 0, 5.217, 4.131, 0, 5.533, -5.365, 0, 5.833, 11.435, 0, 6.15, -14.62, 0, 6.5, 11.93, 0, 6.883, -6.175, 0, 7.367, 4.122, 0, 7.717, -1.145, 0, 7.867, -0.748, 0, 8.083, -2.499, 0, 8.4, 3.584, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.233, 1.467, 0, 0.717, -2.187, 0, 1.25, 1.252, 0, 1.683, -0.882, 0, 2.133, 2.692, 0, 2.567, -2.531, 0, 2.983, 2.203, 0, 3.333, -2.534, 0, 3.983, 1.507, 0, 4.35, -1.857, 0, 5.183, 1.189, 0, 5.517, -2.534, 0, 5.95, 1.614, 0, 6.433, -1.4, 0, 6.85, 1.139, 0, 7.4, -1.18, 0, 7.833, 0.551, 0, 8.317, -0.604, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.183, -9.455, 0, 0.5, 11.883, 0, 0.917, -9.699, 0, 1.45, 7.08, 0, 1.917, -10.436, 0, 2.367, 16.693, 0, 2.767, -18.213, 0, 3.183, 20.602, 0, 3.533, -12.757, 0, 4.217, 11.627, 0, 4.533, -7.167, 0, 5.417, 13.648, 0, 5.75, -16.183, 0, 6.15, 11.469, 0, 6.633, -9.015, 0, 7.067, 6.537, 0, 7.633, -5.452, 0, 8, 4.524, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param126", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.167, 7.697, 0, 0.4, -13.522, 0, 0.733, 11.069, 0, 1.1, -6.551, 0, 1.75, 4.65, 0, 2.217, -8.584, 0, 2.617, 14.391, 0, 3.017, -16.024, 0, 3.383, 19.606, 0, 3.7, -11.001, 0, 4, 1.304, 0, 4.167, -3.041, 0, 4.4, 9.51, 0, 4.7, -5.694, 0, 5.017, 1.393, 0, 5.35, -7.137, 0, 5.633, 14.929, 0, 5.983, -13.742, 0, 6.333, 9.182, 0, 6.85, -6.47, 0, 7.217, 4.156, 0, 7.867, -4.927, 0, 8.2, 3.406, 1, 8.25, 3.406, 8.3, 3.379, 8.35, 2.497, 1, 8.4, 1.615, 8.45, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param128", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.15, 0, 0, 0.317, -7.614, 0, 0.533, 13.159, 0, 0.833, -13.001, 0, 1.2, 8.752, 0, 1.65, -3.261, 0, 1.7, -3.243, 0, 1.8, -3.393, 0, 2.283, 7.305, 0, 2.683, -12.691, 0, 3.083, 15.315, 0, 3.467, -17.939, 0, 3.8, 13.114, 0, 4.1, -3.828, 0, 4.3, 1.835, 0, 4.533, -7.309, 0, 4.833, 6.406, 0, 5.117, -2.921, 0, 5.45, 5.725, 0, 5.75, -12.915, 0, 6.067, 14.619, 0, 6.417, -10.586, 0, 6.883, 5.719, 0, 7.3, -4.359, 0, 7.733, 0.605, 0, 7.75, 0.581, 0, 7.983, 3.551, 0, 8.3, -3.449, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param129", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.3, 0, 0, 0.467, 5.777, 0, 0.667, -11.068, 0, 0.95, 13.25, 0, 1.267, -9.428, 0, 1.633, 4.108, 0, 2.35, -6.298, 0, 2.783, 11.165, 0, 3.167, -14.603, 0, 3.567, 16.809, 0, 3.9, -14.287, 0, 4.217, 6.303, 0, 4.417, -1.915, 0, 4.65, 5.65, 0, 4.933, -6.616, 0, 5.217, 4.131, 0, 5.533, -5.365, 0, 5.833, 11.435, 0, 6.15, -14.62, 0, 6.5, 11.93, 0, 6.883, -6.175, 0, 7.367, 4.122, 0, 7.717, -1.145, 0, 7.867, -0.748, 0, 8.083, -2.499, 0, 8.4, 3.584, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 2.833, 0.142, 5.667, 0.283, 8.5, 0.425]}, {"Target": "Parameter", "Id": "Param142", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.812, 2, 0.017, 0.127, 1, 2.561, 0.305, 5.106, 0.482, 7.65, 0.66, 1, 7.933, 0.692, 8.217, 0.724, 8.5, 0.756]}, {"Target": "Parameter", "Id": "Param143", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.76, 1, 0.422, 0.82, 0.845, 0.88, 1.267, 0.94, 2, 1.283, 0.2, 1, 3.689, 0.375, 6.094, 0.549, 8.5, 0.724]}, {"Target": "Parameter", "Id": "Param18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 0, 2, 0.35, 1, 2, 0.4, 2, 2, 0.45, 3, 2, 0.5, 4, 2, 0.55, 5, 2, 0.6, 0, 2, 0.65, 1, 2, 0.7, 2, 2, 0.75, 3, 2, 0.8, 4, 2, 0.85, 5, 2, 0.9, 0, 2, 0.95, 1, 2, 1, 2, 2, 1.05, 3, 2, 1.1, 4, 2, 1.15, 5, 2, 1.2, 0, 2, 1.25, 1, 2, 1.3, 2, 2, 1.35, 3, 2, 1.4, 4, 2, 1.45, 5, 2, 1.5, 0, 2, 1.55, 1, 2, 1.6, 2, 2, 1.65, 3, 2, 1.7, 4, 2, 1.75, 5, 2, 1.8, 0, 2, 1.85, 1, 2, 1.9, 2, 2, 1.95, 3, 2, 2, 4, 2, 2.05, 5, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 0, 2, 2.45, 1, 2, 2.5, 2, 2, 2.55, 3, 2, 2.6, 4, 2, 2.65, 5, 2, 2.7, 0, 2, 2.75, 1, 2, 2.8, 2, 2, 2.85, 3, 2, 2.9, 4, 2, 2.95, 5, 2, 3, 0, 2, 3.05, 1, 2, 3.1, 2, 2, 3.15, 3, 2, 3.2, 4, 2, 3.25, 5, 2, 3.3, 0, 2, 3.35, 1, 2, 3.4, 2, 2, 3.45, 3, 2, 3.5, 4, 2, 3.55, 5, 2, 3.6, 0, 2, 3.65, 1, 2, 3.7, 2, 2, 3.75, 3, 2, 3.8, 4, 2, 3.85, 5, 2, 3.9, 0, 2, 3.95, 1, 2, 4, 2, 2, 4.05, 3, 2, 4.1, 4, 2, 4.15, 5, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 0, 2, 4.55, 1, 2, 4.6, 2, 2, 4.65, 3, 2, 4.7, 4, 2, 4.75, 5, 2, 4.8, 0, 2, 4.85, 1, 2, 4.9, 2, 2, 4.95, 3, 2, 5, 4, 2, 5.05, 5, 2, 5.1, 0, 2, 5.15, 1, 2, 5.2, 2, 2, 5.25, 3, 2, 5.3, 4, 2, 5.35, 5, 2, 5.4, 0, 2, 5.45, 1, 2, 5.5, 2, 2, 5.55, 3, 2, 5.6, 4, 2, 5.65, 5, 2, 5.7, 0, 2, 5.75, 1, 2, 5.8, 2, 2, 5.85, 3, 2, 5.9, 4, 2, 5.95, 5, 2, 6, 0, 2, 6.05, 1, 2, 6.1, 2, 2, 6.15, 3, 2, 6.2, 4, 2, 6.25, 5, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 0, 2, 6.65, 1, 2, 6.7, 2, 2, 6.75, 3, 2, 6.8, 4, 2, 6.85, 5, 2, 6.9, 0, 2, 6.95, 1, 2, 7, 2, 2, 7.05, 3, 2, 7.1, 4, 2, 7.15, 5, 2, 7.2, 0, 2, 7.25, 1, 2, 7.3, 2, 2, 7.35, 3, 2, 7.4, 4, 2, 7.45, 5, 2, 7.5, 0, 2, 7.55, 1, 2, 7.6, 2, 2, 7.65, 3, 2, 7.7, 4, 2, 7.75, 5, 2, 7.8, 0, 2, 7.85, 1, 2, 7.9, 2, 2, 7.95, 3, 2, 8, 4, 2, 8.05, 5, 2, 8.1, 0, 2, 8.15, 1, 2, 8.2, 2, 2, 8.25, 3, 2, 8.3, 4, 2, 8.35, 5, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 6, 2, 0.35, 0, 2, 0.4, 1, 2, 0.45, 2, 2, 0.5, 3, 2, 0.55, 4, 2, 0.6, 5, 2, 0.65, 6, 2, 0.7, 0, 2, 0.75, 1, 2, 0.8, 2, 2, 0.85, 3, 2, 0.9, 4, 2, 0.95, 5, 2, 1, 6, 2, 1.05, 0, 2, 1.1, 1, 2, 1.15, 2, 2, 1.2, 3, 2, 1.25, 4, 2, 1.3, 5, 2, 1.35, 6, 2, 1.4, 0, 2, 1.45, 1, 2, 1.5, 2, 2, 1.55, 3, 2, 1.6, 4, 2, 1.65, 5, 2, 1.7, 6, 2, 1.75, 0, 2, 1.8, 1, 2, 1.85, 2, 2, 1.9, 3, 2, 1.95, 4, 2, 2, 5, 2, 2.05, 6, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 6, 2, 2.45, 0, 2, 2.5, 1, 2, 2.55, 2, 2, 2.6, 3, 2, 2.65, 4, 2, 2.7, 5, 2, 2.75, 6, 2, 2.8, 0, 2, 2.85, 1, 2, 2.9, 2, 2, 2.95, 3, 2, 3, 4, 2, 3.05, 5, 2, 3.1, 6, 2, 3.15, 0, 2, 3.2, 1, 2, 3.25, 2, 2, 3.3, 3, 2, 3.35, 4, 2, 3.4, 5, 2, 3.45, 6, 2, 3.5, 0, 2, 3.55, 1, 2, 3.6, 2, 2, 3.65, 3, 2, 3.7, 4, 2, 3.75, 5, 2, 3.8, 6, 2, 3.85, 0, 2, 3.9, 1, 2, 3.95, 2, 2, 4, 3, 2, 4.05, 4, 2, 4.1, 5, 2, 4.15, 6, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 6, 2, 4.55, 0, 2, 4.6, 1, 2, 4.65, 2, 2, 4.7, 3, 2, 4.75, 4, 2, 4.8, 5, 2, 4.85, 6, 2, 4.9, 0, 2, 4.95, 1, 2, 5, 2, 2, 5.05, 3, 2, 5.1, 4, 2, 5.15, 5, 2, 5.2, 6, 2, 5.25, 0, 2, 5.3, 1, 2, 5.35, 2, 2, 5.4, 3, 2, 5.45, 4, 2, 5.5, 5, 2, 5.55, 6, 2, 5.6, 0, 2, 5.65, 1, 2, 5.7, 2, 2, 5.75, 3, 2, 5.8, 4, 2, 5.85, 5, 2, 5.9, 6, 2, 5.95, 0, 2, 6, 1, 2, 6.05, 2, 2, 6.1, 3, 2, 6.15, 4, 2, 6.2, 5, 2, 6.25, 6, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 6, 2, 6.65, 0, 2, 6.7, 1, 2, 6.75, 2, 2, 6.8, 3, 2, 6.85, 4, 2, 6.9, 5, 2, 6.95, 6, 2, 7, 0, 2, 7.05, 1, 2, 7.1, 2, 2, 7.15, 3, 2, 7.2, 4, 2, 7.25, 5, 2, 7.3, 6, 2, 7.35, 0, 2, 7.4, 1, 2, 7.45, 2, 2, 7.5, 3, 2, 7.55, 4, 2, 7.6, 5, 2, 7.65, 6, 2, 7.7, 0, 2, 7.75, 1, 2, 7.8, 2, 2, 7.85, 3, 2, 7.9, 4, 2, 7.95, 5, 2, 8, 6, 2, 8.05, 0, 2, 8.1, 1, 2, 8.15, 2, 2, 8.2, 3, 2, 8.25, 4, 2, 8.3, 5, 2, 8.35, 6, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2]}, {"Target": "Parameter", "Id": "Param51", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 6, 2, 0.35, 0, 2, 0.4, 1, 2, 0.45, 2, 2, 0.5, 3, 2, 0.55, 4, 2, 0.6, 5, 2, 0.65, 6, 2, 0.7, 0, 2, 0.75, 1, 2, 0.8, 2, 2, 0.85, 3, 2, 0.9, 4, 2, 0.95, 5, 2, 1, 6, 2, 1.05, 0, 2, 1.1, 1, 2, 1.15, 2, 2, 1.2, 3, 2, 1.25, 4, 2, 1.3, 5, 2, 1.35, 6, 2, 1.4, 0, 2, 1.45, 1, 2, 1.5, 2, 2, 1.55, 3, 2, 1.6, 4, 2, 1.65, 5, 2, 1.7, 6, 2, 1.75, 0, 2, 1.8, 1, 2, 1.85, 2, 2, 1.9, 3, 2, 1.95, 4, 2, 2, 5, 2, 2.05, 6, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 6, 2, 2.45, 0, 2, 2.5, 1, 2, 2.55, 2, 2, 2.6, 3, 2, 2.65, 4, 2, 2.7, 5, 2, 2.75, 6, 2, 2.8, 0, 2, 2.85, 1, 2, 2.9, 2, 2, 2.95, 3, 2, 3, 4, 2, 3.05, 5, 2, 3.1, 6, 2, 3.15, 0, 2, 3.2, 1, 2, 3.25, 2, 2, 3.3, 3, 2, 3.35, 4, 2, 3.4, 5, 2, 3.45, 6, 2, 3.5, 0, 2, 3.55, 1, 2, 3.6, 2, 2, 3.65, 3, 2, 3.7, 4, 2, 3.75, 5, 2, 3.8, 6, 2, 3.85, 0, 2, 3.9, 1, 2, 3.95, 2, 2, 4, 3, 2, 4.05, 4, 2, 4.1, 5, 2, 4.15, 6, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 6, 2, 4.55, 0, 2, 4.6, 1, 2, 4.65, 2, 2, 4.7, 3, 2, 4.75, 4, 2, 4.8, 5, 2, 4.85, 6, 2, 4.9, 0, 2, 4.95, 1, 2, 5, 2, 2, 5.05, 3, 2, 5.1, 4, 2, 5.15, 5, 2, 5.2, 6, 2, 5.25, 0, 2, 5.3, 1, 2, 5.35, 2, 2, 5.4, 3, 2, 5.45, 4, 2, 5.5, 5, 2, 5.55, 6, 2, 5.6, 0, 2, 5.65, 1, 2, 5.7, 2, 2, 5.75, 3, 2, 5.8, 4, 2, 5.85, 5, 2, 5.9, 6, 2, 5.95, 0, 2, 6, 1, 2, 6.05, 2, 2, 6.1, 3, 2, 6.15, 4, 2, 6.2, 5, 2, 6.25, 6, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 6, 2, 6.65, 0, 2, 6.7, 1, 2, 6.75, 2, 2, 6.8, 3, 2, 6.85, 4, 2, 6.9, 5, 2, 6.95, 6, 2, 7, 0, 2, 7.05, 1, 2, 7.1, 2, 2, 7.15, 3, 2, 7.2, 4, 2, 7.25, 5, 2, 7.3, 6, 2, 7.35, 0, 2, 7.4, 1, 2, 7.45, 2, 2, 7.5, 3, 2, 7.55, 4, 2, 7.6, 5, 2, 7.65, 6, 2, 7.7, 0, 2, 7.75, 1, 2, 7.8, 2, 2, 7.85, 3, 2, 7.9, 4, 2, 7.95, 5, 2, 8, 6, 2, 8.05, 0, 2, 8.1, 1, 2, 8.15, 2, 2, 8.2, 3, 2, 8.25, 4, 2, 8.3, 5, 2, 8.35, 6, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2]}, {"Target": "Parameter", "Id": "Param42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 2.833, 0.031, 5.667, 0.063, 8.5, 0.094]}, {"Target": "Parameter", "Id": "Param45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.4, 1, 2.833, -0.369, 5.667, -0.337, 8.5, -0.306]}, {"Target": "Parameter", "Id": "Param46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.3, 1, 2.833, 0.331, 5.667, 0.363, 8.5, 0.394]}, {"Target": "Parameter", "Id": "Param50", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 2.833, 0.567, 5.667, 1.133, 8.5, 1.7]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2.5, 10, 0, 5.017, -10, 0, 7.5, 10, 0, 8.5, 2.959]}, {"Target": "Parameter", "Id": "Param17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.76, 0, 2.5, 0.66, 0, 5.017, -5.76, 0, 7.5, 0.66, 0, 8.5, -1.6]}, {"Target": "Parameter", "Id": "Param49", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.35, 0, 1.25, 0, 0, 3.75, 0.7, 0, 6.267, 0, 0, 8.5, 0.678]}, {"Target": "Parameter", "Id": "Param40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.25, 0, 0.917, 10, 0, 2.917, -10, 0, 4.917, 10, 0, 6.917, -10, 0, 8.5, 7.759]}, {"Target": "Parameter", "Id": "Param41", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2, 10, 0, 4, -10, 0, 6, 10, 0, 8, -10, 0, 8.5, -6.875]}, {"Target": "Parameter", "Id": "Param32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2, 10, 0, 4, -10, 0, 6, 10, 0, 8, -10, 0, 8.5, -6.875]}, {"Target": "Parameter", "Id": "Param33", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.886, 0, 0.583, 10, 0, 2.583, -10, 0, 4.583, 10, 0, 6.583, -10, 0, 8.5, 9.898]}, {"Target": "Parameter", "Id": "Param34", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -14.451, 0, 1.333, 30, 0, 3.333, -30, 0, 5.333, 30, 0, 7.333, -30, 0, 8.5, 7.423]}, {"Target": "Parameter", "Id": "Param28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -15, 0, 2, 15, 0, 4, -15, 0, 6, 15, 0, 8, -15, 0, 8.5, -10.312]}, {"Target": "Parameter", "Id": "Param29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.886, 0, 0.583, 10, 0, 2.583, -10, 0, 4.583, 10, 0, 6.583, -10, 0, 8.5, 9.898]}, {"Target": "Parameter", "Id": "Param35", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -14.451, 0, 1.333, 30, 0, 3.333, -30, 0, 5.333, 30, 0, 7.333, -30, 0, 8.5, 7.423]}, {"Target": "Parameter", "Id": "Param30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.959, 0, 0.8, 10, 0, 2.8, -10, 0, 4.8, 10, 0, 6.8, -10, 0, 8.5, 8.785]}, {"Target": "Parameter", "Id": "Param31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 9.44, 0, 1.8, -10, 0, 3.8, 10, 0, 5.8, -10, 0, 7.8, 10, 0, 8.5, 4.366]}, {"Target": "Parameter", "Id": "Param36", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 13.773, 0, 0.683, 30, 0, 2.683, -30, 0, 4.683, 30, 0, 6.683, -30, 0, 8.5, 28.58]}, {"Target": "Parameter", "Id": "GL1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 2.833, 0.126, 5.667, 0.252, 8.5, 0.378]}, {"Target": "Parameter", "Id": "GL2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.39, 1, 2.833, 0.516, 5.667, 0.642, 8.5, 0.768]}, {"Target": "Parameter", "Id": "GL3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.72, 1, 2.833, 0.846, 5.667, 0.972, 8.5, 1.098]}, {"Target": "Parameter", "Id": "GL4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.21, 1, 2.833, 0.336, 5.667, 0.462, 8.5, 0.588]}, {"Target": "Parameter", "Id": "GL5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.53, 1, 2.833, 0.656, 5.667, 0.782, 8.5, 0.908]}, {"Target": "Parameter", "Id": "GL6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.89, 1, 2.833, 1.016, 5.667, 1.142, 8.5, 1.268]}, {"Target": "Parameter", "Id": "GL7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.77, 1, 2.833, 0.896, 5.667, 1.022, 8.5, 1.148]}, {"Target": "Parameter", "Id": "GL8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.09, 1, 2.833, 0.216, 5.667, 0.342, 8.5, 0.468]}, {"Target": "Parameter", "Id": "GL9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.301, 1, 2.833, 0.427, 5.667, 0.553, 8.5, 0.679]}, {"Target": "Parameter", "Id": "GL10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.45, 1, 2.833, 0.576, 5.667, 0.702, 8.5, 0.828]}, {"Target": "Parameter", "Id": "GL11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.64, 1, 2.833, 0.766, 5.667, 0.892, 8.5, 1.018]}, {"Target": "Parameter", "Id": "GL12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.15, 1, 2.833, 0.276, 5.667, 0.402, 8.5, 0.528]}, {"Target": "Parameter", "Id": "GL13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.585, 1, 2.833, 0.711, 5.667, 0.837, 8.5, 0.963]}, {"Target": "Parameter", "Id": "GL14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 2.833, 0.126, 5.667, 0.252, 8.5, 0.378]}, {"Target": "Parameter", "Id": "Param161", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.51, 1, 2.833, 0.668, 5.667, 0.825, 8.5, 0.983]}, {"Target": "Parameter", "Id": "Param162", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.31, 1, 2.833, 0.468, 5.667, 0.625, 8.5, 0.783]}, {"Target": "Parameter", "Id": "Param163", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.72, 1, 2.833, 0.878, 5.667, 1.035, 8.5, 1.193]}, {"Target": "Parameter", "Id": "Param164", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 2.833, 0.158, 5.667, 0.315, 8.5, 0.473]}, {"Target": "Parameter", "Id": "Param165", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.17, 1, 2.833, 0.328, 5.667, 0.485, 8.5, 0.643]}, {"Target": "Parameter", "Id": "touch_drag21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param54", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param118", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "man_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "man_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param78", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param79", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "man_<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "man_<PERSON>ze", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param189", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "shangguang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param186", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param96", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param97", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param98", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param111", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "shangguang2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param53", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param99", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param76", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param77", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "MB_xingfengTMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param127", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param103", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param104", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param106", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param105", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param110", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param130", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param107", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param108", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param109", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Mouthfunnel", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param293", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param72", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param74", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param75", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param73", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "MB_fenweiTMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "MB_fenweiliangdu", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 8.5, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiandu", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 8.5, 0.6]}, {"Target": "Parameter", "Id": "MB_fenweiqiu1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 8.5, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 8.5, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 8.5, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 8.5, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 8.5, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 8.5, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiushangxia", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "MB_fenweiqiudaxiao", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param102", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param101", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param100", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param167", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param169", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param196", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param56", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param59", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10, 0, 8.5, 10]}, {"Target": "Parameter", "Id": "Param57", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param124", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param80", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param60", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param47", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param81", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param95", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param125", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param171", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param166", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param123", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 8.5, 1]}, {"Target": "Parameter", "Id": "Param13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param39", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param61", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 8.5, 1]}, {"Target": "Parameter", "Id": "Param71", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 8.5, 1]}, {"Target": "Parameter", "Id": "Param173", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param174", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param175", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param176", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param190", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param191", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param192", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param193", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param65", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 8.5, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamShoulderStretch3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamShoulderStretch4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamChestZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamWaistZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamButtZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param82", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param83", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param84", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param85", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param87", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param94", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.2, 0, 8.5, 0.2]}, {"Target": "Parameter", "Id": "Param20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param52", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param70", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param208", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param112", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param115", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param113", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param116", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param114", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param117", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "Param23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "touch_drag23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "touch_drag24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "touch_drag25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "touch_drag26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "touch_drag27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "touch_drag28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "touch_drag29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "touch_drag30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "touch_drag31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "touch_drag32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "empty1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "empty2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "empty3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "empty4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "empty5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "empty6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "empty7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "empty8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "empty9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "empty10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "empty11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "empty12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "empty13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "empty14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "empty15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "empty16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "empty17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "empty18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "empty19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "empty20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "empty21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "empty22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}, {"Target": "Parameter", "Id": "empty23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.5, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 8.0, "Value": ""}]}