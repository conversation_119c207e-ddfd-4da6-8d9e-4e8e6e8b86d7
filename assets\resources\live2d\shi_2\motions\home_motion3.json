{"Version": 3, "Meta": {"Duration": 11.667, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 260, "TotalSegmentCount": 2237, "TotalPointCount": 2633, "UserDataCount": 2, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 13.08, 1, 1.05, 13.08, 2.1, 3.377, 3.15, 1.08, 1, 3.628, 0.035, 4.105, -0.047, 4.583, -0.143, 1, 6.25, -0.479, 7.916, -0.658, 9.583, -0.97, 1, 9.816, -1.014, 10.05, -2.17, 10.283, -2.17, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 19.44, 1, 1.05, 19.44, 2.1, 15.088, 3.15, 8.69, 1, 3.628, 5.779, 4.105, 2.245, 4.583, 1.62, 1, 6.25, -0.559, 7.916, -1.08, 9.583, -1.08, 1, 9.816, -1.08, 10.05, -0.831, 10.283, -0.6, 1, 10.744, -0.144, 11.206, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "All_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 4.583, 0, 0, 5.167, -9.609, 0, 5.75, 9.609, 0, 6.333, -9.609, 0, 6.917, 9.609, 0, 7.5, -9.609, 0, 8.083, 9.609, 0, 8.667, -9.609, 0, 9.25, 9.609, 0, 9.833, -9.609, 0, 10.417, 9.609, 0, 11.017, -9.609, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -17.04, 1, 1.05, -17.04, 2.1, -17.905, 3.15, -20.24, 1, 3.628, -21.302, 4.105, -22.25, 4.583, -22.25, 1, 6.25, -22.25, 7.916, -21.315, 9.583, -18.62, 1, 9.816, -18.243, 10.05, -15.641, 10.283, -12.55, 1, 10.744, -6.442, 11.206, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 36.02, 0, 3.15, 24.22, 0, 3.883, 39.258, 1, 4.116, 39.258, 4.35, 35.492, 4.583, 33.643, 1, 6.25, 20.439, 7.916, 15.14, 9.583, 15.14, 0, 10.283, 23.14, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param78", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.52, 0, 1.417, -0.549, 1, 1.734, -0.549, 2.05, -0.499, 2.367, -0.425, 1, 2.622, -0.366, 2.878, -0.332, 3.133, -0.268, 1, 3.422, -0.196, 3.711, -0.007, 4, 0, 1, 4.878, 0.021, 5.755, 0.023, 6.633, 0.048, 1, 7.644, 0.077, 8.656, 0.136, 9.667, 0.136, 1, 9.906, 0.136, 10.144, 0.14, 10.383, 0.116, 1, 10.755, 0.079, 11.128, 0, 11.5, 0, 2, 11.667, 0]}, {"Target": "Parameter", "Id": "Param79", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.28, 0, 1.417, 0.41, 1, 1.734, 0.41, 2.05, 0.383, 2.367, 0.319, 1, 2.622, 0.267, 2.878, 0.201, 3.133, 0.152, 1, 3.422, 0.096, 3.711, 0.01, 4, 0, 1, 4.878, -0.031, 5.755, -0.052, 6.633, -0.074, 1, 7.644, -0.099, 8.656, -0.12, 9.667, -0.144, 1, 9.906, -0.15, 10.144, -0.155, 10.383, -0.155, 0, 11.5, 0, 2, 11.667, 0]}, {"Target": "Parameter", "Id": "man_<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 11.94, 0, 3.133, 16.38, 0, 4, 14.891, 0, 6.633, 17.831, 1, 7.644, 17.831, 8.656, 17.417, 9.667, 15.791, 1, 9.906, 15.407, 10.144, 12.123, 10.383, 9.112, 1, 10.755, 4.421, 11.128, 0, 11.5, 0, 2, 11.667, 0]}, {"Target": "Parameter", "Id": "man_<PERSON>ze", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.555, 2, 3.133, -0.555, 0, 4, -0.58, 1, 4.878, -0.58, 5.755, -0.472, 6.633, -0.395, 1, 7.644, -0.307, 8.656, -0.295, 9.667, -0.295, 0, 10.383, -0.487, 0, 11.5, 0, 2, 11.667, 0]}, {"Target": "Parameter", "Id": "Param77", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 10.117, 1, 0, 10.283, 0, 2, 11.5, 0, 2, 11.667, 0]}, {"Target": "Parameter", "Id": "Param26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 9.667, 0, 0, 11.267, 30, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "MB_fenweiTMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.386, 0, 10.5, 0.385, 0, 11.5, 0, 2, 11.667, 0]}, {"Target": "Parameter", "Id": "MB_fenweiliangdu", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.863, 1, 0.8, 0.863, 1.6, 0.813, 2.4, 0.8, 1, 4.822, 0.759, 7.245, 0.737, 9.667, 0.7, 1, 9.945, 0.696, 10.222, 0.435, 10.5, 0.435, 0, 11.5, 0.7, 2, 11.667, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiandu", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.169, 0, 2.4, 0.6, 2, 9.667, 0.6, 0, 10.5, 0.584, 0, 11.5, 0.6, 2, 11.667, 0.6]}, {"Target": "Parameter", "Id": "MB_fenweiqiu1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.417, 0.7, 0, 2.867, 0, 0, 4.217, 0.7, 0, 5.667, 0, 0, 9.667, 0.7, 2, 11.5, 0.7, 2, 11.667, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 1.583, 0, 0, 3, 1, 0, 4.333, 0, 0, 5.667, 1, 0, 9.667, 0.7, 2, 11.5, 0.7, 2, 11.667, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.1, 0, 0.75, 0, 0, 3.233, 0.7, 0, 5.667, 0.1, 0, 9.667, 0.7, 2, 11.5, 0.7, 2, 11.667, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 3, 0, 0, 5.667, 1, 0, 9.667, 0.7, 2, 11.5, 0.7, 2, 11.667, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.3, 0, 0.633, 0, 0, 2.05, 0.7, 0, 3.5, 0, 0, 4.85, 0.7, 0, 5.667, 0.3, 0, 9.667, 0.7, 2, 11.5, 0.7, 2, 11.667, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.417, 0.7, 0, 2.867, 0, 0, 4.217, 0.7, 0, 5.667, 0, 0, 9.667, 0.7, 2, 11.5, 0.7, 2, 11.667, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiudaxiao", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.667, 1, 0, 9.667, 0, 2, 11.5, 0, 2, 11.667, 0]}, {"Target": "Parameter", "Id": "ParamAngleX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.05, 0, 0, 3.533, 2.734, 0, 4.117, -2.748, 0, 4.7, 2.734, 0, 5.283, -2.748, 0, 5.867, 2.734, 0, 6.45, -2.748, 0, 7.033, 2.734, 0, 7.617, -2.748, 0, 8.2, 2.734, 0, 8.783, -2.748, 0, 9.517, 10.073, 0, 10.133, -18, 0, 11, 30, 0, 11.667, -1.304]}, {"Target": "Parameter", "Id": "ParamAngleY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.05, 0, 0, 3.267, -5, 0, 3.833, 5, 0, 4.433, -5, 0, 5.017, 5, 0, 5.6, -5, 0, 6.183, 5, 0, 6.767, -5, 0, 7.35, 5, 0, 7.933, -5, 0, 8.517, 5, 1, 8.711, 5, 8.906, 2.232, 9.1, -5, 1, 9.267, -11.199, 9.433, -15.339, 9.6, -15.339, 0, 10.233, 0, 0, 11.033, -25, 0, 11.667, -0.124]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.633, -29.924, 2, 9.667, -29.924, 0, 10.133, -30, 1, 10.3, -30, 10.466, -29.637, 10.633, -26.16, 1, 10.811, -22.452, 10.989, -15.38, 11.167, -9.18, 1, 11.334, -3.368, 11.5, 0.543, 11.667, 0.543]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 2.211, 0, 4.422, 25.125, 6.633, 29.025, 1, 7.644, 30, 8.656, 30, 9.667, 30, 0, 10.633, 28.01, 0, 11.167, 28.02, 0, 11.667, -4.357]}, {"Target": "Parameter", "Id": "ParamBodyAngleX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.633, -22.881, 1, 7.594, -22.881, 8.556, -22.063, 9.517, -18.747, 1, 9.722, -18.037, 9.928, 19, 10.133, 19, 0, 11, -30, 0, 11.667, -8.417]}, {"Target": "Parameter", "Id": "ParamBodyAngleY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.05, 0, 0, 3.5, -8.723, 0, 4.067, 8.696, 0, 4.667, -8.723, 0, 5.25, 8.696, 0, 5.833, -8.723, 0, 6.417, 8.696, 0, 7, -8.723, 0, 7.583, 8.696, 0, 8.167, -8.723, 0, 8.75, 8.696, 0, 9.333, -8.723, 1, 9.528, -8.723, 9.722, -7.544, 9.917, -2, 1, 10.111, 3.544, 10.306, 13.835, 10.5, 20, 1, 10.694, 26.165, 10.889, 28, 11.083, 28, 0, 11.667, -9.558]}, {"Target": "Parameter", "Id": "ParamWaistZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.633, -1.646, 2, 9.567, -1.646, 0, 10.4, -18.049, 0, 11.05, -4.534, 0, 11.667, -13.805]}, {"Target": "Parameter", "Id": "ParamShoulderStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 6.633, 0, 2, 9.667, 0, 1, 9.961, 0, 10.256, 0.578, 10.55, 5.545, 1, 10.678, 7.701, 10.805, 30, 10.933, 30, 0, 11.667, -2.893]}, {"Target": "Parameter", "Id": "ParamShoulderStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.633, -13.86, 2, 9.667, -13.86, 1, 9.889, -13.86, 10.111, -11.217, 10.333, -7.302, 1, 10.516, -4.072, 10.7, -3, 10.883, -3, 0, 11.667, -3.371]}, {"Target": "Parameter", "Id": "ParamButtXA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.05, 0, 0, 3.633, 3.977, 0, 4.217, -3.977, 0, 4.8, 3.977, 0, 5.383, -3.977, 0, 5.967, 3.977, 0, 6.55, -3.977, 0, 7.133, 3.977, 0, 7.717, -3.977, 0, 8.3, 3.977, 0, 8.883, -3.977, 0, 9.467, 3.977, 1, 9.667, 3.977, 9.867, 1.129, 10.067, -3.977, 1, 10.284, -9.508, 10.5, -12, 10.717, -12, 0, 11.15, 15.627, 0, 11.667, -6.39]}, {"Target": "Parameter", "Id": "Param43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.05, 0, 0, 3.183, 3.64, 0, 3.767, -3.658, 0, 4.35, 3.64, 0, 4.933, -3.658, 0, 5.517, 3.64, 0, 6.1, -3.658, 0, 6.683, 3.64, 0, 7.267, -3.658, 0, 7.85, 3.64, 0, 8.433, -3.658, 0, 9.017, 3.64, 0, 9.617, -3.658, 0, 10.183, 3.64, 0, 10.783, -3.658, 0, 11.15, 13.674, 0, 11.667, -1.915]}, {"Target": "Parameter", "Id": "ParamButtZA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 6.633, 29.992, 2, 9.667, 29.992, 0, 10.933, 30, 0, 11.667, -5.239]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10, 2, 6.633, 10, 0, 11.5, 15, 2, 11.667, 15]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.002, 0, 3.583, -0.032, 0, 4.15, 0.004, 0, 4.783, -0.039, 0, 5.317, -0.003, 0, 5.933, -0.045, 0, 6.5, -0.006, 0, 6.983, -0.03, 0, 10.533, 2.672, 1, 10.722, 2.672, 10.911, 2.387, 11.1, 1.512, 1, 11.289, 0.637, 11.478, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.056, 0, 3.167, -0.041, 0, 3.7, -0.191, 0, 4.283, 0.036, 0, 4.867, -0.202, 0, 5.45, 0.023, 0, 6.033, -0.211, 0, 6.617, 0.018, 0, 7.117, -0.121, 0, 10.5, 5.527, 1, 10.7, 5.527, 10.9, 4.982, 11.1, 3.138, 1, 11.289, 1.396, 11.478, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 15.344, 1, 1.056, 15.344, 2.111, 15.336, 3.167, 15.308, 1, 3.339, 15.303, 3.511, 14.812, 3.683, 14.812, 0, 4.25, 15.617, 0, 4.85, 14.744, 0, 5.417, 15.548, 0, 6.017, 14.693, 0, 6.6, 15.52, 0, 7.183, 14.663, 0, 7.75, 15.437, 0, 8.367, 14.5, 0, 8.917, 15.223, 0, 9.533, 14.253, 0, 10.35, 18.61, 1, 10.6, 18.61, 10.85, 17.449, 11.1, 11.824, 1, 11.289, 7.574, 11.478, 2.296, 11.667, 2.296]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.146, 0, 3.2, -1.487, 0, 3.783, -0.871, 0, 4.35, -1.649, 0, 4.95, -0.924, 0, 5.533, -1.679, 0, 6.117, -0.954, 0, 6.683, -1.692, 0, 7.283, -0.948, 0, 7.85, -1.653, 0, 8.45, -0.885, 0, 9.017, -1.56, 0, 9.617, -0.854, 0, 10.283, -3.08, 0, 10.917, 1.4, 0, 11.667, 0.842]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.048, 0, 3.183, -0.002, 0, 3.767, 0.101, 0, 4.35, -0.033, 0, 4.933, 0.095, 0, 5.517, -0.035, 0, 6.1, 0.092, 0, 6.683, -0.037, 0, 7.233, 0.07, 0, 7.9, -0.117, 0, 8.35, -0.059, 0, 9.1, -0.295, 2, 9.117, -0.295, 2, 9.133, -0.295, 0, 9.45, -0.266, 0, 10.4, -3.836, 0, 11, 3, 0, 11.667, 0.113]}, {"Target": "Parameter", "Id": "Param55", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 6.633, 0, 2, 9.667, 0, 2, 10.35, 0, 0, 10.867, 15.99, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 22, 2, 6.633, 22, 2, 9.667, 22, 0, 10.35, 30, 2, 11.133, 30, 2, 11.15, 0, 2, 11.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 6.633, 0, 2, 9.667, 0, 0, 10.283, 2.16, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 18.41, 0, 6.633, 10.15, 0, 9.583, 12.04, 1, 9.816, 12.04, 10.05, 11.918, 10.283, 11.161, 1, 10.511, 10.422, 10.739, 9.763, 10.967, 8, 1, 11.2, 6.194, 11.434, -0.089, 11.667, -0.089]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.38, 1, 2.211, -1.38, 4.422, -0.075, 6.633, 1.38, 1, 7.616, 2.027, 8.6, 2.04, 9.583, 2.04, 0, 10.283, -1.988, 0, 11.133, 7, 0, 11.667, 0.769]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -6.9, 0, 6.633, 10.56, 0, 9.583, -6.9, 1, 9.816, -6.9, 10.05, -5.389, 10.283, -3.02, 1, 10.433, -1.497, 10.583, -1, 10.733, -1, 0, 11.267, -11, 0, 11.667, -3.105]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 6.633, 0, 2, 9.667, 0, 2, 10.35, 0, 0, 10.8, 2, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 6.633, 0, 2, 9.667, 0, 2, 10.35, 0, 0, 10.8, -30, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param56", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 30, 2, 2.75, 30, 2, 3.333, 30, 2, 6.633, 30, 2, 9.667, 30, 0, 10.167, 0, 2, 10.583, 0, 2, 11.5, 0, 2, 11.667, 0]}, {"Target": "Parameter", "Id": "Param59", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 30, 2, 2.75, 30, 2, 3.333, 30, 2, 6.633, 30, 2, 9.667, 30, 2, 10.167, 30, 0, 10.5, 10, 2, 11.2, 10, 2, 11.667, 10]}, {"Target": "Parameter", "Id": "Param3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.75, 0, 2, 3.333, 0, 2, 6.633, 0, 2, 9.667, 0, 0, 10.167, 2.746, 1, 10.189, 2.746, 10.211, 2.603, 10.233, 2.252, 1, 10.416, -0.642, 10.6, -2.124, 10.783, -2.124, 0, 11.5, 4.502, 2, 11.667, 4.502]}, {"Target": "Parameter", "Id": "Param2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.62, 2, 2.75, 1.62, 2, 3.333, 1.62, 2, 6.633, 1.62, 2, 9.667, 1.62, 1, 9.834, 1.62, 10, 20.061, 10.167, 23.98, 1, 10.184, 24.372, 10.2, 24.058, 10.217, 24.058, 1, 10.406, 24.058, 10.594, 24.569, 10.783, 22.415, 1, 11.022, 19.69, 11.261, 2.204, 11.5, 2.204, 2, 11.667, 2.204]}, {"Target": "Parameter", "Id": "Param", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -22.905, 0, 2.95, -12.064, 2, 2.967, -12.064, 0, 3.333, -8.666, 1, 3.472, -8.666, 3.611, -24.94, 3.75, -27, 1, 3.956, -30, 4.161, -30, 4.367, -30, 2, 9.667, -30, 1, 10.172, -30, 10.678, -25.207, 11.183, -9, 1, 11.344, -3.835, 11.506, 7.526, 11.667, 7.526]}, {"Target": "Parameter", "Id": "Param44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 30, 2, 9.667, 30, 0, 10.5, 0, 2, 11.35, 0, 2, 11.667, 0]}, {"Target": "Parameter", "Id": "Param57", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.75, 0, 2, 3.333, 0, 2, 6.633, 0, 2, 9.667, 0, 2, 10.167, 0, 2, 10.583, 0, 0, 11.033, -17.82, 0, 11.5, 0, 2, 11.667, 0]}, {"Target": "Parameter", "Id": "Param124", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2, 2, 2.75, 2, 2, 3.333, 2, 2, 6.633, 2, 2, 9.667, 2, 0, 10.167, 0, 2, 11.5, 0, 2, 11.667, 0]}, {"Target": "Parameter", "Id": "Param80", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 2.75, 1, 2, 3.333, 1, 2, 6.633, 1, 2, 9.667, 1, 0, 10.167, 0, 2, 11.5, 0, 2, 11.667, 0]}, {"Target": "Parameter", "Id": "Param6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.75, 0, 2, 3.333, 0, 2, 6.633, 0, 2, 9.667, 0, 0, 10.167, 0.256, 0, 10.783, -7.362, 0, 11.5, -5.323, 2, 11.667, -5.323]}, {"Target": "Parameter", "Id": "Param48", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 30, 2, 2.75, 30, 2, 3.333, 30, 2, 6.633, 30, 2, 9.667, 30, 1, 9.834, 30, 10, 29.171, 10.167, 25.565, 1, 10.372, 21.117, 10.578, 16.436, 10.783, 9.276, 1, 11.022, 0.955, 11.261, -8.246, 11.5, -8.246, 2, 11.667, -8.246]}, {"Target": "Parameter", "Id": "Param37", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10.96, 0, 2.95, -2.138, 2, 2.967, -2.138, 1, 3.089, -2.138, 3.211, -2.229, 3.333, -1.887, 1, 3.472, -1.498, 3.611, 11.753, 3.75, 14, 1, 3.956, 17.326, 4.161, 17.625, 4.367, 17.625, 2, 9.667, 17.625, 0, 11.083, 20, 0, 11.667, 8.94]}, {"Target": "Parameter", "Id": "Param38", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 30, 2, 9.667, 30, 0, 10.5, -3.915, 1, 10.728, -3.915, 10.955, -3.474, 11.183, -2, 1, 11.344, -0.958, 11.506, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param60", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.18, 2, 2.75, -3.18, 2, 3.333, -3.18, 2, 6.633, -3.18, 2, 9.667, -3.18, 0, 10.167, 0, 2, 10.583, 0, 0, 11.033, -17.22, 0, 11.5, 0, 2, 11.667, 0]}, {"Target": "Parameter", "Id": "Param121", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 4.867, 1, 2, 10.6, 1, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param81", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.267, 0, 1, 6.067, 1.667, 8.867, 3.333, 11.667, 5]}, {"Target": "Parameter", "Id": "Param95", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.55, 0, 1, 5.35, 1.667, 8.15, 3.333, 10.95, 5, 2, 11.667, 5]}, {"Target": "Parameter", "Id": "Param123", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 5.367, 1, 2, 11.067, 1, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.9, 2, 1.283, 0.9, 0, 1.633, 0, 1, 1.766, 0, 1.9, 0.861, 2.033, 0.9, 1, 2.344, 0.991, 2.656, 0.998, 2.967, 0.998, 0, 3.5, 0.727, 0, 4.067, 0.998, 0, 4.667, 0.727, 0, 5.233, 0.998, 2, 6.4, 0.998, 0, 6.967, 0.727, 0, 7.567, 0.998, 0, 8.167, 0.727, 1, 8.367, 0.727, 8.567, 0.813, 8.767, 0.9, 1, 9.039, 1.018, 9.311, 1.05, 9.583, 1.05, 0, 10, 0.852, 0, 10.417, 0.999, 0, 11.117, 0, 0, 11.667, 0.9]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.467, 0, 0, 1.767, 1, 0, 2.267, 0, 2, 2.367, 0, 0, 2.667, 1, 0, 2.967, 0, 2, 4.35, 0, 0, 4.6, 0.9, 0, 5.033, 0, 2, 8.967, 0, 1, 9.172, 0, 9.378, 0.016, 9.583, 0.102, 1, 9.722, 0.16, 9.861, 0.243, 10, 0.243, 0, 10.183, 0.2, 2, 11.667, 0.2]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.9, 2, 1.283, 0.9, 0, 1.633, 0, 1, 1.766, 0, 1.9, 0.861, 2.033, 0.9, 1, 2.344, 0.991, 2.656, 0.998, 2.967, 0.998, 0, 3.5, 0.727, 0, 4.067, 0.998, 0, 4.667, 0.727, 0, 5.233, 0.998, 2, 6.4, 0.998, 0, 6.967, 0.727, 0, 7.567, 0.998, 0, 8.167, 0.727, 1, 8.367, 0.727, 8.567, 0.816, 8.767, 0.9, 1, 9.039, 1.015, 9.311, 1.044, 9.583, 1.044, 0, 10, 0.838, 2, 11.667, 0.838]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.467, 0, 0, 1.767, 1, 0, 2.267, 0, 2, 2.367, 0, 0, 2.667, 1, 0, 2.967, 0, 2, 4.35, 0, 0, 4.6, 0.9, 0, 5.033, 0, 2, 8.967, 0, 0, 9.583, 0.099, 0, 10, 0, 0, 10.183, 0.2, 2, 11.667, 0.2]}, {"Target": "Parameter", "Id": "ParamEyeBallX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.15, 0, 1, 3.756, 0, 6.361, -0.36, 8.967, -0.4, 1, 9.189, -0.403, 9.411, -0.3, 9.633, -0.3, 2, 11.667, -0.3]}, {"Target": "Parameter", "Id": "ParamEyeBallY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.15, 0, 1, 3.756, 0, 6.361, 0.22, 8.967, 0.3, 1, 9.189, 0.307, 9.411, 0.6, 9.633, 0.6, 2, 11.667, 0.6]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.15, 0, 0, 1.833, 0.07, 2, 2.433, 0.07, 0, 2.967, 0, 2, 4.183, 0, 0, 4.483, 0.07, 0, 4.9, 0, 2, 11.667, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.15, 0, 0, 1.833, 0.364, 2, 2.433, 0.364, 0, 2.967, 0, 2, 4.183, 0, 0, 4.483, 0.364, 0, 4.9, 0, 2, 11.667, 0]}, {"Target": "Parameter", "Id": "Param4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.15, 0, 0, 1.833, -0.586, 2, 2.433, -0.586, 0, 2.967, 0, 2, 4.183, 0, 0, 4.483, -0.586, 0, 4.9, 0, 2, 11.667, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.15, 0, 0, 1.833, 0.262, 2, 2.433, 0.262, 0, 2.967, 0, 2, 4.183, 0, 0, 4.483, 0.262, 0, 4.9, 0, 2, 11.667, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.15, 0, 0, 1.833, 0.366, 2, 2.433, 0.366, 0, 2.967, 0, 2, 4.183, 0, 0, 4.483, 0.366, 0, 4.9, 0, 2, 11.667, 0]}, {"Target": "Parameter", "Id": "Param11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.15, 0, 0, 1.967, -0.572, 2, 2.433, -0.572, 0, 2.967, 0, 2, 4.183, 0, 0, 4.483, -0.572, 0, 4.9, 0, 2, 9.7, 0, 2, 10.45, 0, 2, 11.667, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.15, 0, 0, 9.167, 1, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.15, 0, 2, 7.083, 0, 0, 7.367, 0.494, 1, 7.556, 0.494, 7.744, 0.08, 7.933, 0, 2, 9.733, 0, 0, 10.2, 1, 2, 10.767, 1, 0, 11.3, 0, 2, 11.667, 0]}, {"Target": "Parameter", "Id": "Param14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.15, 0, 2, 7.083, 0, 2, 7.417, 0, 0, 7.733, 0.3, 1, 7.894, 0.3, 8.056, 0.033, 8.217, 0, 2, 10.2, 0, 1, 10.389, 0.052, 10.578, 1, 10.767, 1, 0, 11.3, 0, 2, 11.667, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.15, 0, 0, 1.3, -0.244, 0, 1.483, -0.01, 0, 1.6, -0.319, 2, 1.783, -0.319, 2, 1.967, -0.319, 0, 2.05, 0.656, 0, 2.1, -0.319, 2, 2.283, -0.319, 1, 2.339, -0.319, 2.394, 0.691, 2.45, 0.7, 1, 2.939, 0.78, 3.428, 0.808, 3.917, 0.808, 0, 4.117, 0, 0, 4.383, 1, 0, 4.467, 0.656, 0, 4.517, 1, 0, 4.7, -0.319, 0, 4.9, 0.224, 2, 5.033, 0.224, 0, 5.117, 0, 0, 5.2, 0.34, 0, 6.4, -0.846, 0, 6.55, -0.319, 2, 6.583, -0.319, 0, 6.683, 0.808, 0, 6.75, -0.319, 0, 6.883, 0, 0, 6.9, -0.319, 1, 6.922, -0.319, 6.945, -0.028, 6.967, 0, 1, 7.2, 0.289, 7.434, 0.4, 7.667, 0.4, 1, 7.7, 0.4, 7.734, 0.174, 7.767, 0, 1, 7.817, -0.262, 7.867, -0.319, 7.917, -0.319, 2, 7.95, -0.319, 0, 8.05, 0.808, 0, 8.117, -0.319, 0, 8.25, 0, 0, 8.267, -0.319, 1, 8.289, -0.319, 8.311, 0.148, 8.333, 0.34, 1, 8.394, 0.869, 8.456, 1, 8.517, 1, 1, 8.522, 1, 8.528, 0.829, 8.533, 0.8, 1, 8.555, 0.685, 8.578, 0.656, 8.6, 0.656, 0, 8.65, 1, 0, 8.833, -0.319, 0, 9.033, 0.224, 2, 9.167, 0.224, 0, 9.25, -0.2, 1, 9.328, -0.2, 9.405, -0.059, 9.483, 0.23, 1, 9.489, 0.251, 9.494, 0.277, 9.5, 0.3, 1, 9.611, 0.767, 9.722, 1, 9.833, 1, 0, 10.367, 0.8, 0, 11.083, 1, 0, 11.333, 0.5, 2, 11.667, 0.5]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.15, 0, 1, 1.2, 0, 1.25, 0.185, 1.3, 0.5, 1, 1.356, 0.85, 1.411, 1, 1.467, 1, 0, 1.65, 0.102, 0, 1.833, 0.969, 0, 2.05, 0.012, 0, 2.25, 0.663, 0, 2.333, 0, 2, 3.917, 0, 0, 4.117, 0.5, 0, 4.4, 0.323, 0, 4.517, 0.9, 0, 4.6, 0.016, 0, 4.667, 0.663, 0, 4.75, 0.024, 0, 4.9, 0.871, 2, 5.033, 0.871, 0, 5.117, 0, 1, 5.545, 0, 5.972, 0.078, 6.4, 0.3, 1, 6.45, 0.326, 6.5, 0.871, 6.55, 0.871, 2, 6.583, 0.871, 0, 6.7, 0.4, 2, 6.867, 0.4, 0, 6.933, 0.5, 2, 7.767, 0.5, 0, 7.917, 0.871, 2, 7.95, 0.871, 0, 8.05, 0, 0, 8.3, 0.5, 0, 8.533, 0.323, 0, 8.65, 0.9, 0, 8.733, 0.016, 0, 8.8, 0.663, 0, 8.883, 0.024, 0, 9.033, 0.871, 2, 9.167, 0.871, 1, 9.195, 0.871, 9.222, 0.431, 9.25, 0.4, 1, 9.328, 0.312, 9.405, 0.3, 9.483, 0.3, 2, 9.5, 0.3, 1, 9.611, 0.3, 9.722, 0.588, 9.833, 0.7, 1, 10.083, 0.952, 10.333, 1, 10.583, 1, 2, 11.083, 1, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Hair_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.033, 0, 0, 3.617, 8.401, 0, 4.2, -8.401, 0, 4.783, 8.401, 0, 5.367, -8.401, 0, 5.95, 8.401, 0, 6.533, -8.401, 0, 7.117, 8.401, 0, 7.7, -8.401, 0, 8.283, 8.401, 0, 8.867, -8.401, 0, 9.45, 15.084, 0, 10.05, -15.084, 0, 10.7, 0, 2, 11.667, 0]}, {"Target": "Parameter", "Id": "Hair_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.333, 0, 0, 3.75, 10.341, 0, 4.2, -15.275, 0, 4.783, 11.809, 0, 5.367, -11.809, 0, 5.95, 11.809, 0, 6.533, -11.809, 0, 7.117, 11.809, 0, 7.7, -11.809, 0, 8.283, 11.809, 0, 8.867, -11.809, 0, 9.45, 11.809, 0, 10.05, -11.809, 0, 10.7, 0, 2, 11.667, 0]}, {"Target": "Parameter", "Id": "Param69", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.033, 0, 0, 3.617, 15.084, 0, 4.2, -15.084, 0, 4.783, 15.084, 0, 5.367, -15.084, 0, 5.95, 15.084, 0, 6.533, -15.084, 0, 7.117, 15.084, 0, 7.7, -15.084, 0, 8.283, 15.084, 0, 8.867, -15.084, 0, 9.45, 15.084, 0, 10.05, -15.084, 0, 10.7, 0, 2, 11.667, 0]}, {"Target": "Parameter", "Id": "Param153", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.033, 0, 0, 3.383, -6.159, 0, 3.933, 9.063, 0, 4.483, -9.901, 0, 5.067, 9.874, 0, 5.65, -9.841, 0, 6.233, 9.835, 0, 6.817, -9.836, 0, 7.4, 9.836, 0, 7.983, -9.836, 0, 8.567, 9.836, 0, 9.167, -12.628, 0, 9.75, 16.006, 0, 10.283, -10.555, 0, 10.817, 3.518, 0, 11.267, -0.726, 1, 11.4, -0.726, 11.534, 0.378, 11.667, 0.466]}, {"Target": "Parameter", "Id": "Param154", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.033, 0, 0, 3.333, 3.745, 0, 3.733, -5.659, 0, 4.25, 6.29, 0, 4.783, -6.125, 0, 5.35, 5.844, 0, 5.933, -5.758, 0, 6.533, 5.753, 0, 7.117, -5.756, 0, 7.7, 5.757, 0, 8.283, -5.757, 0, 8.867, 5.757, 0, 9.5, -7.781, 0, 10.05, 9.357, 0, 10.5, -6.611, 0, 11.017, 3.628, 0, 11.45, -1.546, 0, 11.5, -1.432, 0, 11.517, -1.434, 1, 11.567, -1.434, 11.617, -1.192, 11.667, -0.864]}, {"Target": "Parameter", "Id": "Param155", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.3, 0, 0, 3.55, 3.205, 0, 3.917, -5.724, 0, 4.383, 6.806, 0, 4.917, -6.869, 0, 5.483, 6.436, 0, 6.067, -6.201, 0, 6.65, 6.163, 0, 7.233, -6.169, 0, 7.817, 6.173, 0, 8.4, -6.173, 0, 9, 6.225, 0, 9.617, -7.724, 0, 10.183, 9.47, 0, 10.683, -7.839, 0, 11.167, 4.837, 0, 11.633, -2.361, 1, 11.644, -2.361, 11.656, -2.346, 11.667, -2.318]}, {"Target": "Parameter", "Id": "Param156", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.483, 0, 0, 3.717, 2.692, 0, 4.083, -5.493, 0, 4.55, 7.235, 0, 5.067, -7.636, 0, 5.617, 7.14, 0, 6.2, -6.675, 0, 6.783, 6.546, 0, 7.367, -6.548, 0, 7.95, 6.558, 0, 8.533, -6.561, 0, 9.117, 6.62, 0, 9.75, -7.705, 0, 10.317, 9.547, 0, 10.833, -8.847, 0, 11.317, 6.098, 1, 11.434, 6.098, 11.55, 1.061, 11.667, -1.718]}, {"Target": "Parameter", "Id": "Param82", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.033, 0, 0, 3.383, -5.742, 0, 3.933, 8.449, 0, 4.483, -9.23, 0, 5.067, 9.205, 0, 5.65, -9.174, 0, 6.233, 9.169, 0, 6.817, -9.169, 0, 7.4, 9.169, 0, 7.983, -9.169, 0, 8.567, 9.169, 0, 9.167, -11.772, 0, 9.75, 14.921, 0, 10.283, -9.84, 0, 10.817, 3.28, 0, 11.267, -0.677, 1, 11.4, -0.677, 11.534, 0.352, 11.667, 0.434]}, {"Target": "Parameter", "Id": "Param83", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.033, 0, 0, 3.333, 3.588, 0, 3.733, -5.421, 0, 4.25, 6.026, 0, 4.783, -5.868, 0, 5.35, 5.599, 0, 5.933, -5.516, 0, 6.533, 5.512, 0, 7.117, -5.515, 0, 7.7, 5.516, 0, 8.283, -5.516, 0, 8.867, 5.516, 0, 9.5, -7.455, 0, 10.05, 8.965, 0, 10.5, -6.334, 0, 11.017, 3.476, 0, 11.45, -1.481, 0, 11.5, -1.372, 0, 11.517, -1.374, 1, 11.567, -1.374, 11.617, -1.142, 11.667, -0.828]}, {"Target": "Parameter", "Id": "Param84", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.3, 0, 0, 3.55, 3.277, 0, 3.917, -5.852, 0, 4.383, 6.958, 0, 4.917, -7.022, 0, 5.483, 6.579, 0, 6.067, -6.339, 0, 6.65, 6.3, 0, 7.233, -6.306, 0, 7.817, 6.31, 0, 8.4, -6.31, 0, 9, 6.363, 0, 9.617, -7.896, 0, 10.183, 9.681, 0, 10.683, -8.013, 0, 11.167, 4.944, 0, 11.633, -2.413, 1, 11.644, -2.413, 11.656, -2.398, 11.667, -2.369]}, {"Target": "Parameter", "Id": "Param85", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.483, 0, 0, 3.717, 2.745, 0, 4.083, -5.601, 0, 4.55, 7.377, 0, 5.067, -7.785, 0, 5.617, 7.279, 0, 6.2, -6.805, 0, 6.783, 6.674, 0, 7.367, -6.676, 0, 7.95, 6.687, 0, 8.533, -6.69, 0, 9.117, 6.75, 0, 9.75, -7.856, 0, 10.317, 9.734, 0, 10.833, -9.02, 0, 11.317, 6.217, 1, 11.434, 6.217, 11.55, 1.082, 11.667, -1.752]}, {"Target": "Parameter", "Id": "Param66", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.133, 0, 0, 3.333, 8.991, 0, 3.717, -12.71, 0, 4.25, 18.974, 0, 4.75, -18.408, 0, 5.35, 16.657, 0, 5.95, -16.114, 0, 6.517, 16.707, 0, 7.1, -16.304, 0, 7.683, 16.545, 0, 8.267, -16.403, 0, 8.85, 16.487, 0, 9.433, -16.437, 0, 10, 15.778, 0, 10.483, -11.811, 0, 11.067, 3.433, 0, 11.083, 3.423, 0, 11.117, 3.583, 0, 11.5, -3.459, 1, 11.556, -3.459, 11.611, -1.602, 11.667, 0.049]}, {"Target": "Parameter", "Id": "Param67", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.033, 0, 0, 3.25, -17.297, 0, 3.817, 28.119, 0, 4.317, -30, 2, 4.35, -30, 0, 4.917, 30, 2, 4.95, 30, 0, 5.5, -30, 2, 5.533, -30, 0, 6.083, 30, 2, 6.117, 30, 0, 6.667, -30, 2, 6.7, -30, 0, 7.25, 30, 2, 7.283, 30, 0, 7.833, -30, 2, 7.867, -30, 0, 8.417, 30, 2, 8.45, 30, 0, 9, -30, 2, 9.033, -30, 0, 9.6, 29.286, 0, 10.117, -20.607, 0, 10.45, -0.697, 0, 10.533, -0.906, 0, 10.767, 7.416, 0, 11.033, -2.823, 0, 11.3, 1.298, 0, 11.55, -1.257, 1, 11.589, -1.257, 11.628, -0.718, 11.667, -0.143]}, {"Target": "Parameter", "Id": "Param68", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.033, 0, 0, 3.183, 5.597, 0, 3.433, -10.887, 0, 3.667, 0.587, 0, 3.75, -0.064, 0, 4.017, 13.11, 0, 4.517, -11.7, 0, 5.117, 12.751, 0, 5.7, -12.247, 0, 6.283, 12.367, 0, 6.867, -12.35, 0, 7.45, 12.35, 0, 8.033, -12.351, 0, 8.617, 12.351, 0, 9.2, -12.351, 0, 9.783, 11.451, 0, 10.283, -8.352, 0, 10.533, 1.932, 0, 10.733, -3.908, 0, 10.933, 4.064, 0, 11.183, -3.064, 0, 11.483, 2.093, 1, 11.544, 2.093, 11.606, 0.278, 11.667, -0.856]}, {"Target": "Parameter", "Id": "Param62", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.133, 0, 0, 3.333, -8.991, 0, 3.717, 12.71, 0, 4.25, -18.974, 0, 4.75, 18.408, 0, 5.35, -16.657, 0, 5.95, 16.114, 0, 6.517, -16.707, 0, 7.1, 16.304, 0, 7.683, -16.545, 0, 8.267, 16.403, 0, 8.85, -16.487, 0, 9.433, 16.437, 0, 10, -15.778, 0, 10.483, 11.811, 0, 11.067, -3.433, 0, 11.083, -3.423, 0, 11.117, -3.583, 0, 11.5, 3.459, 1, 11.556, 3.459, 11.611, 1.602, 11.667, -0.049]}, {"Target": "Parameter", "Id": "Param63", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.033, 0, 0, 3.25, 17.297, 0, 3.817, -28.119, 0, 4.317, 30, 2, 4.35, 30, 0, 4.917, -30, 2, 4.95, -30, 0, 5.5, 30, 2, 5.533, 30, 0, 6.083, -30, 2, 6.117, -30, 0, 6.667, 30, 2, 6.7, 30, 0, 7.25, -30, 2, 7.283, -30, 0, 7.833, 30, 2, 7.867, 30, 0, 8.417, -30, 2, 8.45, -30, 0, 9, 30, 2, 9.033, 30, 0, 9.6, -29.286, 0, 10.117, 20.607, 0, 10.45, 0.697, 0, 10.533, 0.906, 0, 10.767, -7.416, 0, 11.033, 2.823, 0, 11.3, -1.298, 0, 11.55, 1.257, 1, 11.589, 1.257, 11.628, 0.718, 11.667, 0.143]}, {"Target": "Parameter", "Id": "Param64", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.033, 0, 0, 3.183, -5.597, 0, 3.433, 10.887, 0, 3.667, -0.587, 0, 3.75, 0.064, 0, 4.017, -13.11, 0, 4.517, 11.7, 0, 5.117, -12.751, 0, 5.7, 12.247, 0, 6.283, -12.367, 0, 6.867, 12.35, 0, 7.45, -12.35, 0, 8.033, 12.351, 0, 8.617, -12.351, 0, 9.2, 12.351, 0, 9.783, -11.451, 0, 10.283, 8.352, 0, 10.533, -1.932, 0, 10.733, 3.908, 0, 10.933, -4.064, 0, 11.183, 3.064, 0, 11.483, -2.093, 1, 11.544, -2.093, 11.606, -0.278, 11.667, 0.856]}, {"Target": "Parameter", "Id": "Param86", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.333, 0, 0, 3.75, -4.524, 0, 4.133, 5.748, 0, 4.4, -8.37, 0, 4.75, 5.854, 0, 5.167, -2.457, 0, 5.95, 1.835, 0, 6.5, -1.933, 0, 7.083, 1.921, 0, 7.667, -1.92, 0, 8.25, 1.92, 0, 8.833, -1.92, 0, 9.417, 1.92, 0, 10.067, -1.644, 0, 10.45, 1.142, 0, 10.65, 0.472, 0, 10.733, 0.623, 0, 11.067, -1.282, 0, 11.333, 1.088, 0, 11.617, -0.824, 1, 11.634, -0.824, 11.65, -0.78, 11.667, -0.713]}, {"Target": "Parameter", "Id": "Param89", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.333, 0, 0, 3.583, -15.27, 0, 3.933, 30, 2, 4.067, 30, 0, 4.383, -30, 2, 4.483, -30, 0, 5.017, 23.856, 0, 5.617, -23.434, 0, 6.2, 23.55, 0, 6.783, -23.543, 0, 7.367, 23.543, 0, 7.95, -23.543, 0, 8.533, 23.543, 0, 9.117, -23.543, 0, 9.7, 22.699, 0, 10.233, -12.881, 0, 10.8, 3.423, 0, 11.15, -1.184, 0, 11.467, 1.035, 1, 11.534, 1.035, 11.6, 0.261, 11.667, -0.308]}, {"Target": "Parameter", "Id": "Param87", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 4.117, 0, 0, 4.333, 4.442, 0, 4.6, -7.293, 0, 4.933, 6.4, 0, 5.317, -3.561, 0, 6.017, 1.513, 0, 6.617, -1.899, 0, 7.183, 1.906, 0, 7.767, -1.896, 0, 8.35, 1.897, 0, 8.933, -1.897, 0, 9.517, 1.896, 0, 10.1, -1.568, 0, 10.583, 1.225, 0, 10.983, -0.359, 0, 11.05, -0.234, 0, 11.217, -1.288, 0, 11.55, 1.554, 1, 11.589, 1.554, 11.628, 1.166, 11.667, 0.71]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.333, 0, 0, 3.517, 9.372, 0, 3.883, -20.92, 0, 4.233, 30, 2, 4.267, 30, 0, 4.617, -20.67, 0, 5.233, 10.017, 0, 5.883, -11.046, 0, 6.45, 11.157, 0, 7.033, -11.114, 0, 7.617, 11.117, 0, 8.2, -11.117, 0, 8.783, 11.117, 0, 9.367, -11.117, 0, 9.933, 10.124, 0, 10.383, -6.896, 0, 10.667, -1.168, 0, 10.733, -1.562, 0, 10.983, 2.803, 0, 11, 2.798, 0, 11.017, 2.873, 0, 11.383, -2.507, 1, 11.478, -2.507, 11.572, 1.463, 11.667, 2.298]}, {"Target": "Parameter", "Id": "Param88", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.45, 0, 0, 3.633, -8.591, 0, 3.967, 18.47, 0, 4.333, -28.384, 0, 4.717, 23.095, 0, 5.267, -8.104, 0, 5.95, 9.907, 0, 6.517, -10.041, 0, 7.1, 9.95, 0, 7.683, -9.959, 0, 8.267, 9.959, 0, 8.85, -9.959, 0, 9.433, 9.959, 0, 10, -9.177, 0, 10.483, 6.933, 0, 10.817, -2.015, 0, 10.967, -1.214, 0, 11, -1.253, 0, 11.017, -1.237, 0, 11.133, -1.494, 0, 11.533, 3.144, 1, 11.578, 3.144, 11.622, 2.039, 11.667, 0.76]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.567, 0, 0, 3.733, 7.021, 0, 4.067, -16.609, 0, 4.433, 26.269, 0, 4.8, -24.367, 0, 5.2, 9.354, 0, 6.05, -8.674, 0, 6.6, 8.741, 0, 7.183, -8.569, 0, 7.767, 8.596, 0, 8.35, -8.595, 0, 8.933, 8.594, 0, 9.517, -8.594, 0, 10.083, 8.021, 0, 10.567, -6.538, 0, 10.95, 2.994, 0, 11.167, 0.793, 0, 11.217, 0.806, 0, 11.433, -0.471, 0, 11.45, -0.465, 0, 11.517, -0.848, 0, 11.533, -0.817, 0, 11.667, -1.372]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.333, 0, 0, 3.75, 4.524, 0, 4.133, -5.748, 0, 4.4, 8.37, 0, 4.75, -5.854, 0, 5.167, 2.457, 0, 5.95, -1.835, 0, 6.5, 1.933, 0, 7.083, -1.921, 0, 7.667, 1.92, 0, 8.25, -1.92, 0, 8.833, 1.92, 0, 9.417, -1.92, 0, 10.067, 1.644, 0, 10.45, -1.142, 0, 10.65, -0.472, 0, 10.733, -0.623, 0, 11.067, 1.282, 0, 11.333, -1.088, 0, 11.617, 0.824, 1, 11.634, 0.824, 11.65, 0.78, 11.667, 0.713]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.333, 0, 0, 3.583, -15.27, 0, 3.933, 30, 2, 4.067, 30, 0, 4.383, -30, 2, 4.483, -30, 0, 5.017, 23.856, 0, 5.617, -23.434, 0, 6.2, 23.55, 0, 6.783, -23.543, 0, 7.367, 23.543, 0, 7.95, -23.543, 0, 8.533, 23.543, 0, 9.117, -23.543, 0, 9.7, 22.699, 0, 10.233, -12.881, 0, 10.8, 3.423, 0, 11.15, -1.184, 0, 11.467, 1.035, 1, 11.534, 1.035, 11.6, 0.261, 11.667, -0.308]}, {"Target": "Parameter", "Id": "Param94", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 4.117, 0, 0, 4.333, -4.442, 0, 4.6, 7.293, 0, 4.933, -6.4, 0, 5.317, 3.561, 0, 6.017, -1.513, 0, 6.617, 1.899, 0, 7.183, -1.906, 0, 7.767, 1.896, 0, 8.35, -1.897, 0, 8.933, 1.897, 0, 9.517, -1.896, 0, 10.1, 1.568, 0, 10.583, -1.225, 0, 10.983, 0.359, 0, 11.05, 0.234, 0, 11.217, 1.288, 0, 11.55, -1.554, 1, 11.589, -1.554, 11.628, -1.166, 11.667, -0.71]}, {"Target": "Parameter", "Id": "Param126", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.333, 0, 0, 3.517, 9.372, 0, 3.883, -20.92, 0, 4.233, 30, 2, 4.267, 30, 0, 4.617, -20.67, 0, 5.233, 10.017, 0, 5.883, -11.046, 0, 6.45, 11.157, 0, 7.033, -11.114, 0, 7.617, 11.117, 0, 8.2, -11.117, 0, 8.783, 11.117, 0, 9.367, -11.117, 0, 9.933, 10.124, 0, 10.383, -6.896, 0, 10.667, -1.168, 0, 10.733, -1.562, 0, 10.983, 2.803, 0, 11, 2.798, 0, 11.017, 2.873, 0, 11.383, -2.507, 1, 11.478, -2.507, 11.572, 1.463, 11.667, 2.298]}, {"Target": "Parameter", "Id": "Param128", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.45, 0, 0, 3.633, -8.591, 0, 3.967, 18.47, 0, 4.333, -28.384, 0, 4.717, 23.095, 0, 5.267, -8.104, 0, 5.95, 9.907, 0, 6.517, -10.041, 0, 7.1, 9.95, 0, 7.683, -9.959, 0, 8.267, 9.959, 0, 8.85, -9.959, 0, 9.433, 9.959, 0, 10, -9.177, 0, 10.483, 6.933, 0, 10.817, -2.015, 0, 10.967, -1.214, 0, 11, -1.253, 0, 11.017, -1.237, 0, 11.133, -1.494, 0, 11.533, 3.144, 1, 11.578, 3.144, 11.622, 2.039, 11.667, 0.76]}, {"Target": "Parameter", "Id": "Param129", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.567, 0, 0, 3.733, 7.021, 0, 4.067, -16.609, 0, 4.433, 26.269, 0, 4.8, -24.367, 0, 5.2, 9.354, 0, 6.05, -8.674, 0, 6.6, 8.741, 0, 7.183, -8.569, 0, 7.767, 8.596, 0, 8.35, -8.595, 0, 8.933, 8.594, 0, 9.517, -8.594, 0, 10.083, 8.021, 0, 10.567, -6.538, 0, 10.95, 2.994, 0, 11.167, 0.793, 0, 11.217, 0.806, 0, 11.433, -0.471, 0, 11.45, -0.465, 0, 11.517, -0.848, 0, 11.533, -0.817, 0, 11.667, -1.372]}, {"Target": "Parameter", "Id": "Param16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.267, 0.223, 0, 6.6, -0.46, 0, 10.317, 0.033, 1, 10.767, 0.033, 11.217, -0.102, 11.667, -0.239]}, {"Target": "Parameter", "Id": "Param19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 3.889, 0.194, 7.778, 0.389, 11.667, 0.583]}, {"Target": "Parameter", "Id": "Param142", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.812, 2, 0.017, 0.127, 1, 2.561, 0.305, 5.106, 0.482, 7.65, 0.66, 1, 8.1, 0.711, 8.55, 0.761, 9, 0.812, 2, 9.017, 0.127, 1, 9.9, 0.189, 10.784, 0.25, 11.667, 0.312]}, {"Target": "Parameter", "Id": "Param143", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.76, 1, 0.422, 0.82, 0.845, 0.88, 1.267, 0.94, 2, 1.283, 0.2, 1, 3.855, 0.387, 6.428, 0.573, 9, 0.76, 1, 9.422, 0.82, 9.845, 0.88, 10.267, 0.94, 2, 10.283, 0.2, 1, 10.744, 0.233, 11.206, 0.267, 11.667, 0.3]}, {"Target": "Parameter", "Id": "Param18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 0, 2, 0.35, 1, 2, 0.4, 2, 2, 0.45, 3, 2, 0.5, 4, 2, 0.55, 5, 2, 0.6, 0, 2, 0.65, 1, 2, 0.7, 2, 2, 0.75, 3, 2, 0.8, 4, 2, 0.85, 5, 2, 0.9, 0, 2, 0.95, 1, 2, 1, 2, 2, 1.05, 3, 2, 1.1, 4, 2, 1.15, 5, 2, 1.2, 0, 2, 1.25, 1, 2, 1.3, 2, 2, 1.35, 3, 2, 1.4, 4, 2, 1.45, 5, 2, 1.5, 0, 2, 1.55, 1, 2, 1.6, 2, 2, 1.65, 3, 2, 1.7, 4, 2, 1.75, 5, 2, 1.8, 0, 2, 1.85, 1, 2, 1.9, 2, 2, 1.95, 3, 2, 2, 4, 2, 2.05, 5, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 0, 2, 2.45, 1, 2, 2.5, 2, 2, 2.55, 3, 2, 2.6, 4, 2, 2.65, 5, 2, 2.7, 0, 2, 2.75, 1, 2, 2.8, 2, 2, 2.85, 3, 2, 2.9, 4, 2, 2.95, 5, 2, 3, 0, 2, 3.05, 1, 2, 3.1, 2, 2, 3.15, 3, 2, 3.2, 4, 2, 3.25, 5, 2, 3.3, 0, 2, 3.35, 1, 2, 3.4, 2, 2, 3.45, 3, 2, 3.5, 4, 2, 3.55, 5, 2, 3.6, 0, 2, 3.65, 1, 2, 3.7, 2, 2, 3.75, 3, 2, 3.8, 4, 2, 3.85, 5, 2, 3.9, 0, 2, 3.95, 1, 2, 4, 2, 2, 4.05, 3, 2, 4.1, 4, 2, 4.15, 5, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 0, 2, 4.55, 1, 2, 4.6, 2, 2, 4.65, 3, 2, 4.7, 4, 2, 4.75, 5, 2, 4.8, 0, 2, 4.85, 1, 2, 4.9, 2, 2, 4.95, 3, 2, 5, 4, 2, 5.05, 5, 2, 5.1, 0, 2, 5.15, 1, 2, 5.2, 2, 2, 5.25, 3, 2, 5.3, 4, 2, 5.35, 5, 2, 5.4, 0, 2, 5.45, 1, 2, 5.5, 2, 2, 5.55, 3, 2, 5.6, 4, 2, 5.65, 5, 2, 5.7, 0, 2, 5.75, 1, 2, 5.8, 2, 2, 5.85, 3, 2, 5.9, 4, 2, 5.95, 5, 2, 6, 0, 2, 6.05, 1, 2, 6.1, 2, 2, 6.15, 3, 2, 6.2, 4, 2, 6.25, 5, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 0, 2, 6.65, 1, 2, 6.7, 2, 2, 6.75, 3, 2, 6.8, 4, 2, 6.85, 5, 2, 6.9, 0, 2, 6.95, 1, 2, 7, 2, 2, 7.05, 3, 2, 7.1, 4, 2, 7.15, 5, 2, 7.2, 0, 2, 7.25, 1, 2, 7.3, 2, 2, 7.35, 3, 2, 7.4, 4, 2, 7.45, 5, 2, 7.5, 0, 2, 7.55, 1, 2, 7.6, 2, 2, 7.65, 3, 2, 7.7, 4, 2, 7.75, 5, 2, 7.8, 0, 2, 7.85, 1, 2, 7.9, 2, 2, 7.95, 3, 2, 8, 4, 2, 8.05, 5, 2, 8.1, 0, 2, 8.15, 1, 2, 8.2, 2, 2, 8.25, 3, 2, 8.3, 4, 2, 8.35, 5, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 0, 2, 8.75, 1, 2, 8.8, 2, 2, 8.85, 3, 2, 8.9, 4, 2, 8.95, 5, 2, 9, 0, 2, 9.05, 1, 2, 9.1, 2, 2, 9.15, 3, 2, 9.2, 4, 2, 9.25, 5, 2, 9.3, 0, 2, 9.35, 1, 2, 9.4, 2, 2, 9.45, 3, 2, 9.5, 4, 2, 9.55, 5, 2, 9.6, 0, 2, 9.65, 1, 2, 9.7, 2, 2, 9.75, 3, 2, 9.8, 4, 2, 9.85, 5, 2, 9.9, 0, 2, 9.95, 1, 2, 10, 2, 2, 10.05, 3, 2, 10.1, 4, 2, 10.15, 5, 2, 10.2, 0, 2, 10.25, 1, 2, 10.3, 2, 2, 10.35, 3, 2, 10.4, 4, 2, 10.45, 5, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 0, 2, 10.85, 1, 2, 10.9, 2, 2, 10.95, 3, 2, 11, 4, 2, 11.05, 5, 2, 11.1, 0, 2, 11.15, 1, 2, 11.2, 2, 2, 11.25, 3, 2, 11.3, 4, 2, 11.35, 5, 2, 11.4, 0, 2, 11.45, 1, 2, 11.5, 2, 2, 11.55, 3, 2, 11.6, 4, 2, 11.65, 5, 2, 11.667, 5]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 6, 2, 0.35, 0, 2, 0.4, 1, 2, 0.45, 2, 2, 0.5, 3, 2, 0.55, 4, 2, 0.6, 5, 2, 0.65, 6, 2, 0.7, 0, 2, 0.75, 1, 2, 0.8, 2, 2, 0.85, 3, 2, 0.9, 4, 2, 0.95, 5, 2, 1, 6, 2, 1.05, 0, 2, 1.1, 1, 2, 1.15, 2, 2, 1.2, 3, 2, 1.25, 4, 2, 1.3, 5, 2, 1.35, 6, 2, 1.4, 0, 2, 1.45, 1, 2, 1.5, 2, 2, 1.55, 3, 2, 1.6, 4, 2, 1.65, 5, 2, 1.7, 6, 2, 1.75, 0, 2, 1.8, 1, 2, 1.85, 2, 2, 1.9, 3, 2, 1.95, 4, 2, 2, 5, 2, 2.05, 6, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 6, 2, 2.45, 0, 2, 2.5, 1, 2, 2.55, 2, 2, 2.6, 3, 2, 2.65, 4, 2, 2.7, 5, 2, 2.75, 6, 2, 2.8, 0, 2, 2.85, 1, 2, 2.9, 2, 2, 2.95, 3, 2, 3, 4, 2, 3.05, 5, 2, 3.1, 6, 2, 3.15, 0, 2, 3.2, 1, 2, 3.25, 2, 2, 3.3, 3, 2, 3.35, 4, 2, 3.4, 5, 2, 3.45, 6, 2, 3.5, 0, 2, 3.55, 1, 2, 3.6, 2, 2, 3.65, 3, 2, 3.7, 4, 2, 3.75, 5, 2, 3.8, 6, 2, 3.85, 0, 2, 3.9, 1, 2, 3.95, 2, 2, 4, 3, 2, 4.05, 4, 2, 4.1, 5, 2, 4.15, 6, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 6, 2, 4.55, 0, 2, 4.6, 1, 2, 4.65, 2, 2, 4.7, 3, 2, 4.75, 4, 2, 4.8, 5, 2, 4.85, 6, 2, 4.9, 0, 2, 4.95, 1, 2, 5, 2, 2, 5.05, 3, 2, 5.1, 4, 2, 5.15, 5, 2, 5.2, 6, 2, 5.25, 0, 2, 5.3, 1, 2, 5.35, 2, 2, 5.4, 3, 2, 5.45, 4, 2, 5.5, 5, 2, 5.55, 6, 2, 5.6, 0, 2, 5.65, 1, 2, 5.7, 2, 2, 5.75, 3, 2, 5.8, 4, 2, 5.85, 5, 2, 5.9, 6, 2, 5.95, 0, 2, 6, 1, 2, 6.05, 2, 2, 6.1, 3, 2, 6.15, 4, 2, 6.2, 5, 2, 6.25, 6, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 6, 2, 6.65, 0, 2, 6.7, 1, 2, 6.75, 2, 2, 6.8, 3, 2, 6.85, 4, 2, 6.9, 5, 2, 6.95, 6, 2, 7, 0, 2, 7.05, 1, 2, 7.1, 2, 2, 7.15, 3, 2, 7.2, 4, 2, 7.25, 5, 2, 7.3, 6, 2, 7.35, 0, 2, 7.4, 1, 2, 7.45, 2, 2, 7.5, 3, 2, 7.55, 4, 2, 7.6, 5, 2, 7.65, 6, 2, 7.7, 0, 2, 7.75, 1, 2, 7.8, 2, 2, 7.85, 3, 2, 7.9, 4, 2, 7.95, 5, 2, 8, 6, 2, 8.05, 0, 2, 8.1, 1, 2, 8.15, 2, 2, 8.2, 3, 2, 8.25, 4, 2, 8.3, 5, 2, 8.35, 6, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 6, 2, 8.75, 0, 2, 8.8, 1, 2, 8.85, 2, 2, 8.9, 3, 2, 8.95, 4, 2, 9, 5, 2, 9.05, 6, 2, 9.1, 0, 2, 9.15, 1, 2, 9.2, 2, 2, 9.25, 3, 2, 9.3, 4, 2, 9.35, 5, 2, 9.4, 6, 2, 9.45, 0, 2, 9.5, 1, 2, 9.55, 2, 2, 9.6, 3, 2, 9.65, 4, 2, 9.7, 5, 2, 9.75, 6, 2, 9.8, 0, 2, 9.85, 1, 2, 9.9, 2, 2, 9.95, 3, 2, 10, 4, 2, 10.05, 5, 2, 10.1, 6, 2, 10.15, 0, 2, 10.2, 1, 2, 10.25, 2, 2, 10.3, 3, 2, 10.35, 4, 2, 10.4, 5, 2, 10.45, 6, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 6, 2, 10.85, 0, 2, 10.9, 1, 2, 10.95, 2, 2, 11, 3, 2, 11.05, 4, 2, 11.1, 5, 2, 11.15, 6, 2, 11.2, 0, 2, 11.25, 1, 2, 11.3, 2, 2, 11.35, 3, 2, 11.4, 4, 2, 11.45, 5, 2, 11.5, 6, 2, 11.55, 0, 2, 11.6, 1, 2, 11.65, 2, 2, 11.667, 2]}, {"Target": "Parameter", "Id": "Param51", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 6, 2, 0.35, 0, 2, 0.4, 1, 2, 0.45, 2, 2, 0.5, 3, 2, 0.55, 4, 2, 0.6, 5, 2, 0.65, 6, 2, 0.7, 0, 2, 0.75, 1, 2, 0.8, 2, 2, 0.85, 3, 2, 0.9, 4, 2, 0.95, 5, 2, 1, 6, 2, 1.05, 0, 2, 1.1, 1, 2, 1.15, 2, 2, 1.2, 3, 2, 1.25, 4, 2, 1.3, 5, 2, 1.35, 6, 2, 1.4, 0, 2, 1.45, 1, 2, 1.5, 2, 2, 1.55, 3, 2, 1.6, 4, 2, 1.65, 5, 2, 1.7, 6, 2, 1.75, 0, 2, 1.8, 1, 2, 1.85, 2, 2, 1.9, 3, 2, 1.95, 4, 2, 2, 5, 2, 2.05, 6, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 6, 2, 2.45, 0, 2, 2.5, 1, 2, 2.55, 2, 2, 2.6, 3, 2, 2.65, 4, 2, 2.7, 5, 2, 2.75, 6, 2, 2.8, 0, 2, 2.85, 1, 2, 2.9, 2, 2, 2.95, 3, 2, 3, 4, 2, 3.05, 5, 2, 3.1, 6, 2, 3.15, 0, 2, 3.2, 1, 2, 3.25, 2, 2, 3.3, 3, 2, 3.35, 4, 2, 3.4, 5, 2, 3.45, 6, 2, 3.5, 0, 2, 3.55, 1, 2, 3.6, 2, 2, 3.65, 3, 2, 3.7, 4, 2, 3.75, 5, 2, 3.8, 6, 2, 3.85, 0, 2, 3.9, 1, 2, 3.95, 2, 2, 4, 3, 2, 4.05, 4, 2, 4.1, 5, 2, 4.15, 6, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 6, 2, 4.55, 0, 2, 4.6, 1, 2, 4.65, 2, 2, 4.7, 3, 2, 4.75, 4, 2, 4.8, 5, 2, 4.85, 6, 2, 4.9, 0, 2, 4.95, 1, 2, 5, 2, 2, 5.05, 3, 2, 5.1, 4, 2, 5.15, 5, 2, 5.2, 6, 2, 5.25, 0, 2, 5.3, 1, 2, 5.35, 2, 2, 5.4, 3, 2, 5.45, 4, 2, 5.5, 5, 2, 5.55, 6, 2, 5.6, 0, 2, 5.65, 1, 2, 5.7, 2, 2, 5.75, 3, 2, 5.8, 4, 2, 5.85, 5, 2, 5.9, 6, 2, 5.95, 0, 2, 6, 1, 2, 6.05, 2, 2, 6.1, 3, 2, 6.15, 4, 2, 6.2, 5, 2, 6.25, 6, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 6, 2, 6.65, 0, 2, 6.7, 1, 2, 6.75, 2, 2, 6.8, 3, 2, 6.85, 4, 2, 6.9, 5, 2, 6.95, 6, 2, 7, 0, 2, 7.05, 1, 2, 7.1, 2, 2, 7.15, 3, 2, 7.2, 4, 2, 7.25, 5, 2, 7.3, 6, 2, 7.35, 0, 2, 7.4, 1, 2, 7.45, 2, 2, 7.5, 3, 2, 7.55, 4, 2, 7.6, 5, 2, 7.65, 6, 2, 7.7, 0, 2, 7.75, 1, 2, 7.8, 2, 2, 7.85, 3, 2, 7.9, 4, 2, 7.95, 5, 2, 8, 6, 2, 8.05, 0, 2, 8.1, 1, 2, 8.15, 2, 2, 8.2, 3, 2, 8.25, 4, 2, 8.3, 5, 2, 8.35, 6, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 6, 2, 8.75, 0, 2, 8.8, 1, 2, 8.85, 2, 2, 8.9, 3, 2, 8.95, 4, 2, 9, 5, 2, 9.05, 6, 2, 9.1, 0, 2, 9.15, 1, 2, 9.2, 2, 2, 9.25, 3, 2, 9.3, 4, 2, 9.35, 5, 2, 9.4, 6, 2, 9.45, 0, 2, 9.5, 1, 2, 9.55, 2, 2, 9.6, 3, 2, 9.65, 4, 2, 9.7, 5, 2, 9.75, 6, 2, 9.8, 0, 2, 9.85, 1, 2, 9.9, 2, 2, 9.95, 3, 2, 10, 4, 2, 10.05, 5, 2, 10.1, 6, 2, 10.15, 0, 2, 10.2, 1, 2, 10.25, 2, 2, 10.3, 3, 2, 10.35, 4, 2, 10.4, 5, 2, 10.45, 6, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 6, 2, 10.85, 0, 2, 10.9, 1, 2, 10.95, 2, 2, 11, 3, 2, 11.05, 4, 2, 11.1, 5, 2, 11.15, 6, 2, 11.2, 0, 2, 11.25, 1, 2, 11.3, 2, 2, 11.35, 3, 2, 11.4, 4, 2, 11.45, 5, 2, 11.5, 6, 2, 11.55, 0, 2, 11.6, 1, 2, 11.65, 2, 2, 11.667, 2]}, {"Target": "Parameter", "Id": "Param42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 3.889, 0.043, 7.778, 0.087, 11.667, 0.13]}, {"Target": "Parameter", "Id": "Param45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.4, 1, 3.889, -0.357, 7.778, -0.313, 11.667, -0.27]}, {"Target": "Parameter", "Id": "Param46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.3, 1, 3.889, 0.343, 7.778, 0.386, 11.667, 0.429]}, {"Target": "Parameter", "Id": "Param50", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 3.889, 0.778, 7.778, 1.555, 11.667, 2.333]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2.5, 10, 0, 5.017, -10, 0, 7.5, 10, 0, 10, -10, 1, 10.556, -10, 11.111, 6.409, 11.667, 17.348]}, {"Target": "Parameter", "Id": "Param17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.76, 0, 2.5, 0.66, 0, 5.017, -5.76, 0, 7.5, 0.66, 0, 10, -5.76, 1, 10.556, -5.76, 11.111, 6.16, 11.667, 14.107]}, {"Target": "Parameter", "Id": "Param49", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.35, 0, 1.25, 0, 0, 3.75, 0.7, 0, 6.267, 0, 0, 8.767, 0.7, 0, 11.283, 0, 1, 11.411, 0, 11.539, 0.016, 11.667, 0.044]}, {"Target": "Parameter", "Id": "Param40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.25, 0, 0.917, 10, 0, 2.917, -10, 0, 4.917, 10, 0, 6.917, -10, 0, 8.917, 10, 0, 10.917, -10, 1, 11.167, -10, 11.417, -7.187, 11.667, -3.672]}, {"Target": "Parameter", "Id": "Param41", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2, 10, 0, 4, -10, 0, 6, 10, 0, 8, -10, 0, 10, 10, 1, 10.556, 10, 11.111, -3.889, 11.667, -8.519]}, {"Target": "Parameter", "Id": "Param32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2, 10, 0, 4, -10, 0, 6, 10, 0, 8, -10, 0, 10, 10, 1, 10.556, 10, 11.111, -3.889, 11.667, -8.519]}, {"Target": "Parameter", "Id": "Param33", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.886, 0, 0.583, 10, 0, 2.583, -10, 0, 4.583, 10, 0, 6.583, -10, 0, 8.583, 10, 0, 10.583, -10, 1, 10.944, -10, 11.306, -4.132, 11.667, 1.247]}, {"Target": "Parameter", "Id": "Param34", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -14.451, 0, 1.333, 30, 0, 3.333, -30, 0, 5.333, 30, 0, 7.333, -30, 0, 9.333, 30, 0, 11.333, -30, 1, 11.444, -30, 11.556, -28.333, 11.667, -25.556]}, {"Target": "Parameter", "Id": "Param28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -15, 0, 2, 15, 0, 4, -15, 0, 6, 15, 0, 8, -15, 0, 10, 15, 1, 10.556, 15, 11.111, -5.833, 11.667, -12.778]}, {"Target": "Parameter", "Id": "Param29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.886, 0, 0.583, 10, 0, 2.583, -10, 0, 4.583, 10, 0, 6.583, -10, 0, 8.583, 10, 0, 10.583, -10, 1, 10.944, -10, 11.306, -4.132, 11.667, 1.247]}, {"Target": "Parameter", "Id": "Param35", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -14.451, 0, 1.333, 30, 0, 3.333, -30, 0, 5.333, 30, 0, 7.333, -30, 0, 9.333, 30, 0, 11.333, -30, 1, 11.444, -30, 11.556, -28.333, 11.667, -25.556]}, {"Target": "Parameter", "Id": "Param30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.959, 0, 0.8, 10, 0, 2.8, -10, 0, 4.8, 10, 0, 6.8, -10, 0, 8.8, 10, 0, 10.8, -10, 1, 11.089, -10, 11.378, -6.244, 11.667, -1.988]}, {"Target": "Parameter", "Id": "Param31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 9.44, 0, 1.8, -10, 0, 3.8, 10, 0, 5.8, -10, 0, 7.8, 10, 0, 9.8, -10, 1, 10.422, -10, 11.045, 7.422, 11.667, 9.745]}, {"Target": "Parameter", "Id": "Param36", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 13.773, 0, 0.683, 30, 0, 2.683, -30, 0, 4.683, 30, 0, 6.683, -30, 0, 8.683, 30, 0, 10.683, -30, 1, 11.011, -30, 11.339, -15.496, 11.667, -0.75]}, {"Target": "Parameter", "Id": "GL1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 3.889, 0.173, 7.778, 0.346, 11.667, 0.519]}, {"Target": "Parameter", "Id": "GL2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.39, 1, 3.889, 0.563, 7.778, 0.736, 11.667, 0.909]}, {"Target": "Parameter", "Id": "GL3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.72, 1, 3.889, 0.893, 7.778, 1.066, 11.667, 1.239]}, {"Target": "Parameter", "Id": "GL4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.21, 1, 3.889, 0.383, 7.778, 0.556, 11.667, 0.729]}, {"Target": "Parameter", "Id": "GL5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.53, 1, 3.889, 0.703, 7.778, 0.876, 11.667, 1.049]}, {"Target": "Parameter", "Id": "GL6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.89, 1, 3.889, 1.063, 7.778, 1.236, 11.667, 1.409]}, {"Target": "Parameter", "Id": "GL7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.77, 1, 3.889, 0.943, 7.778, 1.116, 11.667, 1.289]}, {"Target": "Parameter", "Id": "GL8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.09, 1, 3.889, 0.263, 7.778, 0.436, 11.667, 0.609]}, {"Target": "Parameter", "Id": "GL9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.301, 1, 3.889, 0.474, 7.778, 0.647, 11.667, 0.82]}, {"Target": "Parameter", "Id": "GL10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.45, 1, 3.889, 0.623, 7.778, 0.796, 11.667, 0.969]}, {"Target": "Parameter", "Id": "GL11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.64, 1, 3.889, 0.813, 7.778, 0.986, 11.667, 1.159]}, {"Target": "Parameter", "Id": "GL12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.15, 1, 3.889, 0.323, 7.778, 0.496, 11.667, 0.669]}, {"Target": "Parameter", "Id": "GL13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.585, 1, 3.889, 0.758, 7.778, 0.931, 11.667, 1.104]}, {"Target": "Parameter", "Id": "GL14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 3.889, 0.173, 7.778, 0.346, 11.667, 0.519]}, {"Target": "Parameter", "Id": "Param161", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.51, 1, 3.889, 0.726, 7.778, 0.943, 11.667, 1.159]}, {"Target": "Parameter", "Id": "Param162", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.31, 1, 3.889, 0.526, 7.778, 0.743, 11.667, 0.959]}, {"Target": "Parameter", "Id": "Param163", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.72, 1, 3.889, 0.936, 7.778, 1.153, 11.667, 1.369]}, {"Target": "Parameter", "Id": "Param164", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 3.889, 0.216, 7.778, 0.433, 11.667, 0.649]}, {"Target": "Parameter", "Id": "Param165", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.17, 1, 3.889, 0.386, 7.778, 0.603, 11.667, 0.819]}, {"Target": "Parameter", "Id": "touch_drag21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param54", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "All_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "man_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "man_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param189", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "shangguang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param186", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "shangguang2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param53", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param76", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "MB_xingfengTMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param127", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Mouthfunnel", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param293", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param72", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param74", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param75", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param73", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "MB_fenweiqiushangxia", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param58", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "ParamHandRREDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "ParamHandLREDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 11.667, 1]}, {"Target": "Parameter", "Id": "Param167", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param169", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param196", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param47", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param125", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param171", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param166", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 11.667, 1]}, {"Target": "Parameter", "Id": "Param39", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param61", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 11.667, 1]}, {"Target": "Parameter", "Id": "Param71", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 11.667, 1]}, {"Target": "Parameter", "Id": "Param173", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param174", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param175", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param176", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param190", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param191", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param192", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param193", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param65", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 11.667, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "ParamShoulderStretch3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "ParamShoulderStretch4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "ParamChestZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "ParamWaistZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "ParamButtZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param52", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param70", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param208", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "Param23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "touch_drag23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "touch_drag24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "touch_drag25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "touch_drag26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "touch_drag27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "touch_drag28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "touch_drag29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "touch_drag30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "touch_drag31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "touch_drag32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "empty1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "empty2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "empty3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "empty4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "empty5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "empty6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "empty7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "empty8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "empty9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "empty10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "empty11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "empty12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "empty13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "empty14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "empty15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "empty16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "empty17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "empty18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "empty19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "empty20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "empty21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "empty22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}, {"Target": "Parameter", "Id": "empty23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11.667, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 11.167, "Value": ""}]}