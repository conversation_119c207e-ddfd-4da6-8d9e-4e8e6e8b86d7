{"Version": 3, "Meta": {"Duration": 20.0, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 263, "TotalSegmentCount": 1773, "TotalPointCount": 1853, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 7.272, 0, 0.7, 10.68, 0, 3.233, 6.423, 0, 5.667, 11.142, 0, 8.217, 6.638, 0, 10.6, 11.334, 0, 13.117, 7.085, 0, 15.567, 11.188, 0, 18.017, 7.168, 0, 20, 7.272]}, {"Target": "Parameter", "Id": "ParamAngleY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -7.609, 0, 1.467, -9.984, 0, 3.967, -1.665, 0, 6.383, -9.778, 0, 8.8, -0.22, 0, 11.4, -8.828, 0, 13.817, -0.693, 0, 16.35, -9.171, 0, 18.783, -1.098, 0, 20, -7.609]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -19.357, 0, 1.133, -15.906, 0, 3.6, -23.894, 0, 6.083, -15.906, 0, 8.567, -23.894, 0, 11.033, -15.906, 0, 13.517, -23.894, 0, 16, -15.906, 0, 18.467, -23.894, 0, 20, -19.357]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.843, 0, 2.167, 11.993, 0, 4.633, 2.407, 0, 7.117, 11.993, 0, 9.6, 2.407, 0, 12.067, 11.993, 0, 14.55, 2.407, 0, 17.033, 11.993, 0, 19.5, 2.407, 0, 20, 2.843]}, {"Target": "Parameter", "Id": "ParamBodyAngleX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -8.417, 0, 1.8, 13.258, 0, 4.283, -13.258, 0, 6.767, 13.258, 0, 9.233, -13.258, 0, 11.717, 13.258, 0, 14.2, -13.258, 0, 16.667, 13.258, 0, 19.15, -13.258, 0, 20, -8.417]}, {"Target": "Parameter", "Id": "ParamBodyAngleY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -9.558, 0, 0.483, -11.982, 0, 2.967, 11.982, 0, 5.45, -11.982, 0, 7.917, 11.982, 0, 10.4, -11.982, 0, 12.883, 11.982, 0, 15.35, -11.982, 0, 17.833, 11.982, 0, 20, -9.558]}, {"Target": "Parameter", "Id": "ParamWaistZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.336, 0, 0.8, 1.258, 0, 3.617, -2.897, 0, 5.733, 2.331, 0, 8.6, -3.963, 0, 10.75, 1.442, 0, 13.367, -4.141, 0, 15.75, 1.903, 0, 18.317, -3.304, 0, 20, -0.336]}, {"Target": "Parameter", "Id": "ParamShoulderStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.893, 0, 2.1, 13.854, 0, 4.583, -1.866, 0, 7.067, 13.854, 0, 9.533, -1.866, 0, 12.017, 13.854, 0, 14.5, -1.866, 0, 16.967, 13.854, 0, 19.45, -1.866, 0, 20, -0.893]}, {"Target": "Parameter", "Id": "ParamShoulderStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -8.071, 0, 0.267, -8.566, 0, 2.733, 7.154, 0, 5.217, -8.566, 0, 7.7, 7.154, 0, 10.183, -8.566, 0, 12.65, 7.154, 0, 15.133, -8.566, 0, 17.617, 7.154, 0, 20, -8.071]}, {"Target": "Parameter", "Id": "ParamButtXA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -4.29, 0, 2.483, 8.49, 0, 4.95, -4.29, 0, 7.433, 8.49, 0, 9.917, -4.29, 0, 12.4, 8.49, 0, 14.867, -4.29, 0, 17.35, 8.49, 0, 20, -4.29]}, {"Target": "Parameter", "Id": "Param43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.616, 0, 0.9, -6.502, 0, 3.383, 3.102, 0, 5.85, -6.502, 0, 8.333, 3.102, 0, 10.817, -6.502, 0, 13.3, 3.102, 0, 15.767, -6.502, 0, 18.25, 3.102, 0, 20, -3.616]}, {"Target": "Parameter", "Id": "ParamButtZA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 15.72, 0, 1.833, 24.314, 0, 4.267, 10.705, 0, 6.733, 27.695, 0, 9.217, 10.705, 0, 11.7, 27.695, 0, 14.183, 10.705, 0, 16.65, 27.695, 0, 19.133, 10.705, 0, 20, 15.72]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.396, 0, 1.583, 1.329, 0, 4.1, 1.549, 0, 6.433, 1.328, 0, 9.05, 1.547, 0, 12.083, 1.329, 0, 14.6, 1.549, 0, 16.95, 1.328, 0, 20, 1.396]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -4.962, 0, 1.333, -5.079, 0, 3.95, -4.609, 0, 6.25, -5.073, 0, 8.917, -4.599, 0, 11.85, -5.079, 0, 14.45, -4.609, 0, 16.767, -5.073, 0, 20, -4.962]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10.704, 0, 1.483, 10.423, 0, 4.017, 11.469, 0, 6.333, 10.43, 0, 8.917, 11.476, 0, 12, 10.423, 0, 14.533, 11.469, 0, 16.833, 10.43, 0, 20, 10.704]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.602, 0, 2.433, -0.079, 0, 5.017, 0.788, 0, 7.367, -0.191, 0, 10.083, 0.695, 0, 12.95, -0.079, 0, 15.533, 0.788, 0, 17.867, -0.191, 0, 20, 0.602]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.145, 0, 0.467, 0.194, 0, 2.5, 0.03, 0, 5.317, 0.181, 0, 7.483, 0.007, 0, 10.983, 0.194, 0, 13.017, 0.03, 0, 15.833, 0.181, 0, 18, 0.007, 0, 20, 0.145]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.189, 0, 0.417, -0.217, 0, 2.883, 1.517, 0, 5.367, -0.217, 0, 7.85, 1.517, 0, 10.333, -0.217, 0, 12.8, 1.517, 0, 15.283, -0.217, 0, 17.767, 1.517, 0, 20, -0.189]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.283, 0, 1.317, 0, 0, 3.8, 1.41, 0, 6.267, 0, 0, 8.75, 1.41, 0, 11.833, 0, 0, 14.3, 1.41, 0, 16.783, 0, 0, 19.267, 1.41, 0, 20, 0.283]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.291, 0, 2.167, 3.416, 0, 4.633, -3.416, 0, 7.117, 3.416, 0, 9.6, -3.416, 0, 12.667, 3.416, 0, 15.15, -3.416, 0, 17.633, 3.416, 0, 20, -1.291]}, {"Target": "Parameter", "Id": "Param16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.267, 0.223, 0, 6.6, -0.46, 0, 10.317, 0.033, 0, 13.05, -0.522, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 6.667, 0.333, 13.333, 0.667, 20, 1]}, {"Target": "Parameter", "Id": "Param142", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.812, 2, 0.017, 0.127, 1, 2.561, 0.305, 5.106, 0.482, 7.65, 0.66, 1, 8.1, 0.711, 8.55, 0.761, 9, 0.812, 2, 9.017, 0.127, 1, 11.561, 0.305, 14.106, 0.482, 16.65, 0.66, 1, 17.1, 0.711, 17.55, 0.761, 18, 0.812, 2, 18.017, 0.127, 1, 18.678, 0.173, 19.339, 0.219, 20, 0.265]}, {"Target": "Parameter", "Id": "Param143", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.76, 1, 0.422, 0.82, 0.845, 0.88, 1.267, 0.94, 2, 1.283, 0.2, 1, 3.855, 0.387, 6.428, 0.573, 9, 0.76, 1, 9.422, 0.82, 9.845, 0.88, 10.267, 0.94, 2, 10.283, 0.2, 1, 12.855, 0.387, 15.428, 0.573, 18, 0.76, 1, 18.422, 0.82, 18.845, 0.88, 19.267, 0.94, 2, 19.283, 0.2, 1, 19.522, 0.217, 19.761, 0.235, 20, 0.252]}, {"Target": "Parameter", "Id": "Param18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 0, 2, 0.35, 1, 2, 0.4, 2, 2, 0.45, 3, 2, 0.5, 4, 2, 0.55, 5, 2, 0.6, 0, 2, 0.65, 1, 2, 0.7, 2, 2, 0.75, 3, 2, 0.8, 4, 2, 0.85, 5, 2, 0.9, 0, 2, 0.95, 1, 2, 1, 2, 2, 1.05, 3, 2, 1.1, 4, 2, 1.15, 5, 2, 1.2, 0, 2, 1.25, 1, 2, 1.3, 2, 2, 1.35, 3, 2, 1.4, 4, 2, 1.45, 5, 2, 1.5, 0, 2, 1.55, 1, 2, 1.6, 2, 2, 1.65, 3, 2, 1.7, 4, 2, 1.75, 5, 2, 1.8, 0, 2, 1.85, 1, 2, 1.9, 2, 2, 1.95, 3, 2, 2, 4, 2, 2.05, 5, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 0, 2, 2.45, 1, 2, 2.5, 2, 2, 2.55, 3, 2, 2.6, 4, 2, 2.65, 5, 2, 2.7, 0, 2, 2.75, 1, 2, 2.8, 2, 2, 2.85, 3, 2, 2.9, 4, 2, 2.95, 5, 2, 3, 0, 2, 3.05, 1, 2, 3.1, 2, 2, 3.15, 3, 2, 3.2, 4, 2, 3.25, 5, 2, 3.3, 0, 2, 3.35, 1, 2, 3.4, 2, 2, 3.45, 3, 2, 3.5, 4, 2, 3.55, 5, 2, 3.6, 0, 2, 3.65, 1, 2, 3.7, 2, 2, 3.75, 3, 2, 3.8, 4, 2, 3.85, 5, 2, 3.9, 0, 2, 3.95, 1, 2, 4, 2, 2, 4.05, 3, 2, 4.1, 4, 2, 4.15, 5, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 0, 2, 4.55, 1, 2, 4.6, 2, 2, 4.65, 3, 2, 4.7, 4, 2, 4.75, 5, 2, 4.8, 0, 2, 4.85, 1, 2, 4.9, 2, 2, 4.95, 3, 2, 5, 4, 2, 5.05, 5, 2, 5.1, 0, 2, 5.15, 1, 2, 5.2, 2, 2, 5.25, 3, 2, 5.3, 4, 2, 5.35, 5, 2, 5.4, 0, 2, 5.45, 1, 2, 5.5, 2, 2, 5.55, 3, 2, 5.6, 4, 2, 5.65, 5, 2, 5.7, 0, 2, 5.75, 1, 2, 5.8, 2, 2, 5.85, 3, 2, 5.9, 4, 2, 5.95, 5, 2, 6, 0, 2, 6.05, 1, 2, 6.1, 2, 2, 6.15, 3, 2, 6.2, 4, 2, 6.25, 5, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 0, 2, 6.65, 1, 2, 6.7, 2, 2, 6.75, 3, 2, 6.8, 4, 2, 6.85, 5, 2, 6.9, 0, 2, 6.95, 1, 2, 7, 2, 2, 7.05, 3, 2, 7.1, 4, 2, 7.15, 5, 2, 7.2, 0, 2, 7.25, 1, 2, 7.3, 2, 2, 7.35, 3, 2, 7.4, 4, 2, 7.45, 5, 2, 7.5, 0, 2, 7.55, 1, 2, 7.6, 2, 2, 7.65, 3, 2, 7.7, 4, 2, 7.75, 5, 2, 7.8, 0, 2, 7.85, 1, 2, 7.9, 2, 2, 7.95, 3, 2, 8, 4, 2, 8.05, 5, 2, 8.1, 0, 2, 8.15, 1, 2, 8.2, 2, 2, 8.25, 3, 2, 8.3, 4, 2, 8.35, 5, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 0, 2, 8.75, 1, 2, 8.8, 2, 2, 8.85, 3, 2, 8.9, 4, 2, 8.95, 5, 2, 9, 0, 2, 9.05, 1, 2, 9.1, 2, 2, 9.15, 3, 2, 9.2, 4, 2, 9.25, 5, 2, 9.3, 0, 2, 9.35, 1, 2, 9.4, 2, 2, 9.45, 3, 2, 9.5, 4, 2, 9.55, 5, 2, 9.6, 0, 2, 9.65, 1, 2, 9.7, 2, 2, 9.75, 3, 2, 9.8, 4, 2, 9.85, 5, 2, 9.9, 0, 2, 9.95, 1, 2, 10, 2, 2, 10.05, 3, 2, 10.1, 4, 2, 10.15, 5, 2, 10.2, 0, 2, 10.25, 1, 2, 10.3, 2, 2, 10.35, 3, 2, 10.4, 4, 2, 10.45, 5, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 0, 2, 10.85, 1, 2, 10.9, 2, 2, 10.95, 3, 2, 11, 4, 2, 11.05, 5, 2, 11.1, 0, 2, 11.15, 1, 2, 11.2, 2, 2, 11.25, 3, 2, 11.3, 4, 2, 11.35, 5, 2, 11.4, 0, 2, 11.45, 1, 2, 11.5, 2, 2, 11.55, 3, 2, 11.6, 4, 2, 11.65, 5, 2, 11.7, 0, 2, 11.75, 1, 2, 11.8, 2, 2, 11.85, 3, 2, 11.9, 4, 2, 11.95, 5, 2, 12, 0, 2, 12.05, 1, 2, 12.1, 2, 2, 12.15, 3, 2, 12.2, 4, 2, 12.25, 5, 2, 12.3, 0, 2, 12.35, 1, 2, 12.4, 2, 2, 12.45, 3, 2, 12.5, 4, 2, 12.55, 5, 2, 12.6, 0, 2, 12.65, 1, 2, 12.7, 2, 2, 12.75, 3, 2, 12.8, 4, 2, 12.85, 5, 2, 12.9, 0, 2, 12.95, 1, 2, 13, 2, 2, 13.05, 3, 2, 13.1, 4, 2, 13.15, 5, 2, 13.2, 0, 2, 13.25, 1, 2, 13.3, 2, 2, 13.35, 3, 2, 13.4, 4, 2, 13.45, 5, 2, 13.5, 0, 2, 13.55, 1, 2, 13.6, 2, 2, 13.65, 3, 2, 13.7, 4, 2, 13.75, 5, 2, 13.8, 0, 2, 13.85, 1, 2, 13.9, 2, 2, 13.95, 3, 2, 14, 4, 2, 14.05, 5, 2, 14.1, 0, 2, 14.15, 1, 2, 14.2, 2, 2, 14.25, 3, 2, 14.3, 4, 2, 14.35, 5, 2, 14.4, 0, 2, 14.45, 1, 2, 14.5, 2, 2, 14.55, 3, 2, 14.6, 4, 2, 14.65, 5, 2, 14.7, 0, 2, 14.75, 1, 2, 14.8, 2, 2, 14.85, 3, 2, 14.9, 4, 2, 14.95, 5, 2, 15, 0, 2, 15.05, 1, 2, 15.1, 2, 2, 15.15, 3, 2, 15.2, 4, 2, 15.25, 5, 2, 15.3, 0, 2, 15.35, 1, 2, 15.4, 2, 2, 15.45, 3, 2, 15.5, 4, 2, 15.55, 5, 2, 15.6, 0, 2, 15.65, 1, 2, 15.7, 2, 2, 15.75, 3, 2, 15.8, 4, 2, 15.85, 5, 2, 15.9, 0, 2, 15.95, 1, 2, 16, 2, 2, 16.05, 3, 2, 16.1, 4, 2, 16.15, 5, 2, 16.2, 0, 2, 16.25, 1, 2, 16.3, 2, 2, 16.35, 3, 2, 16.4, 4, 2, 16.45, 5, 2, 16.5, 0, 2, 16.55, 1, 2, 16.6, 2, 2, 16.65, 3, 2, 16.7, 4, 2, 16.75, 5, 2, 16.8, 0, 2, 16.85, 1, 2, 16.9, 2, 2, 16.95, 3, 2, 17, 4, 2, 17.05, 5, 2, 17.1, 0, 2, 17.15, 1, 2, 17.2, 2, 2, 17.25, 3, 2, 17.3, 4, 2, 17.35, 5, 2, 17.4, 0, 2, 17.45, 1, 2, 17.5, 2, 2, 17.55, 3, 2, 17.6, 4, 2, 17.65, 5, 2, 17.7, 0, 2, 17.75, 1, 2, 17.8, 2, 2, 17.85, 3, 2, 17.9, 4, 2, 17.95, 5, 2, 18, 0, 2, 18.05, 1, 2, 18.1, 2, 2, 18.15, 3, 2, 18.2, 4, 2, 18.25, 5, 2, 18.3, 0, 2, 18.35, 1, 2, 18.4, 2, 2, 18.45, 3, 2, 18.5, 4, 2, 18.55, 5, 2, 18.6, 0, 2, 18.65, 1, 2, 18.7, 2, 2, 18.75, 3, 2, 18.8, 4, 2, 18.85, 5, 2, 18.9, 0, 2, 18.95, 1, 2, 19, 2, 2, 19.05, 3, 2, 19.1, 4, 2, 19.15, 5, 2, 19.2, 0, 2, 19.25, 1, 2, 19.3, 2, 2, 19.35, 3, 2, 19.4, 4, 2, 19.45, 5, 2, 19.5, 0, 2, 19.55, 1, 2, 19.6, 2, 2, 19.65, 3, 2, 19.7, 4, 2, 19.75, 5, 2, 19.8, 0, 2, 19.85, 1, 2, 19.9, 2, 2, 19.95, 3, 2, 20, 4]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 6, 2, 0.35, 0, 2, 0.4, 1, 2, 0.45, 2, 2, 0.5, 3, 2, 0.55, 4, 2, 0.6, 5, 2, 0.65, 6, 2, 0.7, 0, 2, 0.75, 1, 2, 0.8, 2, 2, 0.85, 3, 2, 0.9, 4, 2, 0.95, 5, 2, 1, 6, 2, 1.05, 0, 2, 1.1, 1, 2, 1.15, 2, 2, 1.2, 3, 2, 1.25, 4, 2, 1.3, 5, 2, 1.35, 6, 2, 1.4, 0, 2, 1.45, 1, 2, 1.5, 2, 2, 1.55, 3, 2, 1.6, 4, 2, 1.65, 5, 2, 1.7, 6, 2, 1.75, 0, 2, 1.8, 1, 2, 1.85, 2, 2, 1.9, 3, 2, 1.95, 4, 2, 2, 5, 2, 2.05, 6, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 6, 2, 2.45, 0, 2, 2.5, 1, 2, 2.55, 2, 2, 2.6, 3, 2, 2.65, 4, 2, 2.7, 5, 2, 2.75, 6, 2, 2.8, 0, 2, 2.85, 1, 2, 2.9, 2, 2, 2.95, 3, 2, 3, 4, 2, 3.05, 5, 2, 3.1, 6, 2, 3.15, 0, 2, 3.2, 1, 2, 3.25, 2, 2, 3.3, 3, 2, 3.35, 4, 2, 3.4, 5, 2, 3.45, 6, 2, 3.5, 0, 2, 3.55, 1, 2, 3.6, 2, 2, 3.65, 3, 2, 3.7, 4, 2, 3.75, 5, 2, 3.8, 6, 2, 3.85, 0, 2, 3.9, 1, 2, 3.95, 2, 2, 4, 3, 2, 4.05, 4, 2, 4.1, 5, 2, 4.15, 6, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 6, 2, 4.55, 0, 2, 4.6, 1, 2, 4.65, 2, 2, 4.7, 3, 2, 4.75, 4, 2, 4.8, 5, 2, 4.85, 6, 2, 4.9, 0, 2, 4.95, 1, 2, 5, 2, 2, 5.05, 3, 2, 5.1, 4, 2, 5.15, 5, 2, 5.2, 6, 2, 5.25, 0, 2, 5.3, 1, 2, 5.35, 2, 2, 5.4, 3, 2, 5.45, 4, 2, 5.5, 5, 2, 5.55, 6, 2, 5.6, 0, 2, 5.65, 1, 2, 5.7, 2, 2, 5.75, 3, 2, 5.8, 4, 2, 5.85, 5, 2, 5.9, 6, 2, 5.95, 0, 2, 6, 1, 2, 6.05, 2, 2, 6.1, 3, 2, 6.15, 4, 2, 6.2, 5, 2, 6.25, 6, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 6, 2, 6.65, 0, 2, 6.7, 1, 2, 6.75, 2, 2, 6.8, 3, 2, 6.85, 4, 2, 6.9, 5, 2, 6.95, 6, 2, 7, 0, 2, 7.05, 1, 2, 7.1, 2, 2, 7.15, 3, 2, 7.2, 4, 2, 7.25, 5, 2, 7.3, 6, 2, 7.35, 0, 2, 7.4, 1, 2, 7.45, 2, 2, 7.5, 3, 2, 7.55, 4, 2, 7.6, 5, 2, 7.65, 6, 2, 7.7, 0, 2, 7.75, 1, 2, 7.8, 2, 2, 7.85, 3, 2, 7.9, 4, 2, 7.95, 5, 2, 8, 6, 2, 8.05, 0, 2, 8.1, 1, 2, 8.15, 2, 2, 8.2, 3, 2, 8.25, 4, 2, 8.3, 5, 2, 8.35, 6, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 6, 2, 8.75, 0, 2, 8.8, 1, 2, 8.85, 2, 2, 8.9, 3, 2, 8.95, 4, 2, 9, 5, 2, 9.05, 6, 2, 9.1, 0, 2, 9.15, 1, 2, 9.2, 2, 2, 9.25, 3, 2, 9.3, 4, 2, 9.35, 5, 2, 9.4, 6, 2, 9.45, 0, 2, 9.5, 1, 2, 9.55, 2, 2, 9.6, 3, 2, 9.65, 4, 2, 9.7, 5, 2, 9.75, 6, 2, 9.8, 0, 2, 9.85, 1, 2, 9.9, 2, 2, 9.95, 3, 2, 10, 4, 2, 10.05, 5, 2, 10.1, 6, 2, 10.15, 0, 2, 10.2, 1, 2, 10.25, 2, 2, 10.3, 3, 2, 10.35, 4, 2, 10.4, 5, 2, 10.45, 6, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 6, 2, 10.85, 0, 2, 10.9, 1, 2, 10.95, 2, 2, 11, 3, 2, 11.05, 4, 2, 11.1, 5, 2, 11.15, 6, 2, 11.2, 0, 2, 11.25, 1, 2, 11.3, 2, 2, 11.35, 3, 2, 11.4, 4, 2, 11.45, 5, 2, 11.5, 6, 2, 11.55, 0, 2, 11.6, 1, 2, 11.65, 2, 2, 11.7, 3, 2, 11.75, 4, 2, 11.8, 5, 2, 11.85, 6, 2, 11.9, 0, 2, 11.95, 1, 2, 12, 2, 2, 12.05, 3, 2, 12.1, 4, 2, 12.15, 5, 2, 12.2, 6, 2, 12.25, 0, 2, 12.3, 1, 2, 12.35, 2, 2, 12.4, 3, 2, 12.45, 4, 2, 12.5, 5, 2, 12.55, 6, 2, 12.6, 0, 2, 12.65, 1, 2, 12.7, 2, 2, 12.75, 3, 2, 12.8, 4, 2, 12.85, 5, 2, 12.9, 6, 2, 12.95, 0, 2, 13, 1, 2, 13.05, 2, 2, 13.1, 3, 2, 13.15, 4, 2, 13.2, 5, 2, 13.25, 6, 2, 13.3, 0, 2, 13.35, 1, 2, 13.4, 2, 2, 13.45, 3, 2, 13.5, 4, 2, 13.55, 5, 2, 13.6, 6, 2, 13.65, 0, 2, 13.7, 1, 2, 13.75, 2, 2, 13.8, 3, 2, 13.85, 4, 2, 13.9, 5, 2, 13.95, 6, 2, 14, 0, 2, 14.05, 1, 2, 14.1, 2, 2, 14.15, 3, 2, 14.2, 4, 2, 14.25, 5, 2, 14.3, 6, 2, 14.35, 0, 2, 14.4, 1, 2, 14.45, 2, 2, 14.5, 3, 2, 14.55, 4, 2, 14.6, 5, 2, 14.65, 6, 2, 14.7, 0, 2, 14.75, 1, 2, 14.8, 2, 2, 14.85, 3, 2, 14.9, 4, 2, 14.95, 5, 2, 15, 6, 2, 15.05, 0, 2, 15.1, 1, 2, 15.15, 2, 2, 15.2, 3, 2, 15.25, 4, 2, 15.3, 5, 2, 15.35, 6, 2, 15.4, 0, 2, 15.45, 1, 2, 15.5, 2, 2, 15.55, 3, 2, 15.6, 4, 2, 15.65, 5, 2, 15.7, 6, 2, 15.75, 0, 2, 15.8, 1, 2, 15.85, 2, 2, 15.9, 3, 2, 15.95, 4, 2, 16, 5, 2, 16.05, 6, 2, 16.1, 0, 2, 16.15, 1, 2, 16.2, 2, 2, 16.25, 3, 2, 16.3, 4, 2, 16.35, 5, 2, 16.4, 6, 2, 16.45, 0, 2, 16.5, 1, 2, 16.55, 2, 2, 16.6, 3, 2, 16.65, 4, 2, 16.7, 5, 2, 16.75, 6, 2, 16.8, 0, 2, 16.85, 1, 2, 16.9, 2, 2, 16.95, 3, 2, 17, 4, 2, 17.05, 5, 2, 17.1, 6, 2, 17.15, 0, 2, 17.2, 1, 2, 17.25, 2, 2, 17.3, 3, 2, 17.35, 4, 2, 17.4, 5, 2, 17.45, 6, 2, 17.5, 0, 2, 17.55, 1, 2, 17.6, 2, 2, 17.65, 3, 2, 17.7, 4, 2, 17.75, 5, 2, 17.8, 6, 2, 17.85, 0, 2, 17.9, 1, 2, 17.95, 2, 2, 18, 3, 2, 18.05, 4, 2, 18.1, 5, 2, 18.15, 6, 2, 18.2, 0, 2, 18.25, 1, 2, 18.3, 2, 2, 18.35, 3, 2, 18.4, 4, 2, 18.45, 5, 2, 18.5, 6, 2, 18.55, 0, 2, 18.6, 1, 2, 18.65, 2, 2, 18.7, 3, 2, 18.75, 4, 2, 18.8, 5, 2, 18.85, 6, 2, 18.9, 0, 2, 18.95, 1, 2, 19, 2, 2, 19.05, 3, 2, 19.1, 4, 2, 19.15, 5, 2, 19.2, 6, 2, 19.25, 0, 2, 19.3, 1, 2, 19.35, 2, 2, 19.4, 3, 2, 19.45, 4, 2, 19.5, 5, 2, 19.55, 6, 2, 19.6, 0, 2, 19.65, 1, 2, 19.7, 2, 2, 19.75, 3, 2, 19.8, 4, 2, 19.85, 5, 2, 19.9, 6, 2, 19.95, 0, 2, 20, 1]}, {"Target": "Parameter", "Id": "Param51", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 6, 2, 0.35, 0, 2, 0.4, 1, 2, 0.45, 2, 2, 0.5, 3, 2, 0.55, 4, 2, 0.6, 5, 2, 0.65, 6, 2, 0.7, 0, 2, 0.75, 1, 2, 0.8, 2, 2, 0.85, 3, 2, 0.9, 4, 2, 0.95, 5, 2, 1, 6, 2, 1.05, 0, 2, 1.1, 1, 2, 1.15, 2, 2, 1.2, 3, 2, 1.25, 4, 2, 1.3, 5, 2, 1.35, 6, 2, 1.4, 0, 2, 1.45, 1, 2, 1.5, 2, 2, 1.55, 3, 2, 1.6, 4, 2, 1.65, 5, 2, 1.7, 6, 2, 1.75, 0, 2, 1.8, 1, 2, 1.85, 2, 2, 1.9, 3, 2, 1.95, 4, 2, 2, 5, 2, 2.05, 6, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 6, 2, 2.45, 0, 2, 2.5, 1, 2, 2.55, 2, 2, 2.6, 3, 2, 2.65, 4, 2, 2.7, 5, 2, 2.75, 6, 2, 2.8, 0, 2, 2.85, 1, 2, 2.9, 2, 2, 2.95, 3, 2, 3, 4, 2, 3.05, 5, 2, 3.1, 6, 2, 3.15, 0, 2, 3.2, 1, 2, 3.25, 2, 2, 3.3, 3, 2, 3.35, 4, 2, 3.4, 5, 2, 3.45, 6, 2, 3.5, 0, 2, 3.55, 1, 2, 3.6, 2, 2, 3.65, 3, 2, 3.7, 4, 2, 3.75, 5, 2, 3.8, 6, 2, 3.85, 0, 2, 3.9, 1, 2, 3.95, 2, 2, 4, 3, 2, 4.05, 4, 2, 4.1, 5, 2, 4.15, 6, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 6, 2, 4.55, 0, 2, 4.6, 1, 2, 4.65, 2, 2, 4.7, 3, 2, 4.75, 4, 2, 4.8, 5, 2, 4.85, 6, 2, 4.9, 0, 2, 4.95, 1, 2, 5, 2, 2, 5.05, 3, 2, 5.1, 4, 2, 5.15, 5, 2, 5.2, 6, 2, 5.25, 0, 2, 5.3, 1, 2, 5.35, 2, 2, 5.4, 3, 2, 5.45, 4, 2, 5.5, 5, 2, 5.55, 6, 2, 5.6, 0, 2, 5.65, 1, 2, 5.7, 2, 2, 5.75, 3, 2, 5.8, 4, 2, 5.85, 5, 2, 5.9, 6, 2, 5.95, 0, 2, 6, 1, 2, 6.05, 2, 2, 6.1, 3, 2, 6.15, 4, 2, 6.2, 5, 2, 6.25, 6, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 6, 2, 6.65, 0, 2, 6.7, 1, 2, 6.75, 2, 2, 6.8, 3, 2, 6.85, 4, 2, 6.9, 5, 2, 6.95, 6, 2, 7, 0, 2, 7.05, 1, 2, 7.1, 2, 2, 7.15, 3, 2, 7.2, 4, 2, 7.25, 5, 2, 7.3, 6, 2, 7.35, 0, 2, 7.4, 1, 2, 7.45, 2, 2, 7.5, 3, 2, 7.55, 4, 2, 7.6, 5, 2, 7.65, 6, 2, 7.7, 0, 2, 7.75, 1, 2, 7.8, 2, 2, 7.85, 3, 2, 7.9, 4, 2, 7.95, 5, 2, 8, 6, 2, 8.05, 0, 2, 8.1, 1, 2, 8.15, 2, 2, 8.2, 3, 2, 8.25, 4, 2, 8.3, 5, 2, 8.35, 6, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 6, 2, 8.75, 0, 2, 8.8, 1, 2, 8.85, 2, 2, 8.9, 3, 2, 8.95, 4, 2, 9, 5, 2, 9.05, 6, 2, 9.1, 0, 2, 9.15, 1, 2, 9.2, 2, 2, 9.25, 3, 2, 9.3, 4, 2, 9.35, 5, 2, 9.4, 6, 2, 9.45, 0, 2, 9.5, 1, 2, 9.55, 2, 2, 9.6, 3, 2, 9.65, 4, 2, 9.7, 5, 2, 9.75, 6, 2, 9.8, 0, 2, 9.85, 1, 2, 9.9, 2, 2, 9.95, 3, 2, 10, 4, 2, 10.05, 5, 2, 10.1, 6, 2, 10.15, 0, 2, 10.2, 1, 2, 10.25, 2, 2, 10.3, 3, 2, 10.35, 4, 2, 10.4, 5, 2, 10.45, 6, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 6, 2, 10.85, 0, 2, 10.9, 1, 2, 10.95, 2, 2, 11, 3, 2, 11.05, 4, 2, 11.1, 5, 2, 11.15, 6, 2, 11.2, 0, 2, 11.25, 1, 2, 11.3, 2, 2, 11.35, 3, 2, 11.4, 4, 2, 11.45, 5, 2, 11.5, 6, 2, 11.55, 0, 2, 11.6, 1, 2, 11.65, 2, 2, 11.7, 3, 2, 11.75, 4, 2, 11.8, 5, 2, 11.85, 6, 2, 11.9, 0, 2, 11.95, 1, 2, 12, 2, 2, 12.05, 3, 2, 12.1, 4, 2, 12.15, 5, 2, 12.2, 6, 2, 12.25, 0, 2, 12.3, 1, 2, 12.35, 2, 2, 12.4, 3, 2, 12.45, 4, 2, 12.5, 5, 2, 12.55, 6, 2, 12.6, 0, 2, 12.65, 1, 2, 12.7, 2, 2, 12.75, 3, 2, 12.8, 4, 2, 12.85, 5, 2, 12.9, 6, 2, 12.95, 0, 2, 13, 1, 2, 13.05, 2, 2, 13.1, 3, 2, 13.15, 4, 2, 13.2, 5, 2, 13.25, 6, 2, 13.3, 0, 2, 13.35, 1, 2, 13.4, 2, 2, 13.45, 3, 2, 13.5, 4, 2, 13.55, 5, 2, 13.6, 6, 2, 13.65, 0, 2, 13.7, 1, 2, 13.75, 2, 2, 13.8, 3, 2, 13.85, 4, 2, 13.9, 5, 2, 13.95, 6, 2, 14, 0, 2, 14.05, 1, 2, 14.1, 2, 2, 14.15, 3, 2, 14.2, 4, 2, 14.25, 5, 2, 14.3, 6, 2, 14.35, 0, 2, 14.4, 1, 2, 14.45, 2, 2, 14.5, 3, 2, 14.55, 4, 2, 14.6, 5, 2, 14.65, 6, 2, 14.7, 0, 2, 14.75, 1, 2, 14.8, 2, 2, 14.85, 3, 2, 14.9, 4, 2, 14.95, 5, 2, 15, 6, 2, 15.05, 0, 2, 15.1, 1, 2, 15.15, 2, 2, 15.2, 3, 2, 15.25, 4, 2, 15.3, 5, 2, 15.35, 6, 2, 15.4, 0, 2, 15.45, 1, 2, 15.5, 2, 2, 15.55, 3, 2, 15.6, 4, 2, 15.65, 5, 2, 15.7, 6, 2, 15.75, 0, 2, 15.8, 1, 2, 15.85, 2, 2, 15.9, 3, 2, 15.95, 4, 2, 16, 5, 2, 16.05, 6, 2, 16.1, 0, 2, 16.15, 1, 2, 16.2, 2, 2, 16.25, 3, 2, 16.3, 4, 2, 16.35, 5, 2, 16.4, 6, 2, 16.45, 0, 2, 16.5, 1, 2, 16.55, 2, 2, 16.6, 3, 2, 16.65, 4, 2, 16.7, 5, 2, 16.75, 6, 2, 16.8, 0, 2, 16.85, 1, 2, 16.9, 2, 2, 16.95, 3, 2, 17, 4, 2, 17.05, 5, 2, 17.1, 6, 2, 17.15, 0, 2, 17.2, 1, 2, 17.25, 2, 2, 17.3, 3, 2, 17.35, 4, 2, 17.4, 5, 2, 17.45, 6, 2, 17.5, 0, 2, 17.55, 1, 2, 17.6, 2, 2, 17.65, 3, 2, 17.7, 4, 2, 17.75, 5, 2, 17.8, 6, 2, 17.85, 0, 2, 17.9, 1, 2, 17.95, 2, 2, 18, 3, 2, 18.05, 4, 2, 18.1, 5, 2, 18.15, 6, 2, 18.2, 0, 2, 18.25, 1, 2, 18.3, 2, 2, 18.35, 3, 2, 18.4, 4, 2, 18.45, 5, 2, 18.5, 6, 2, 18.55, 0, 2, 18.6, 1, 2, 18.65, 2, 2, 18.7, 3, 2, 18.75, 4, 2, 18.8, 5, 2, 18.85, 6, 2, 18.9, 0, 2, 18.95, 1, 2, 19, 2, 2, 19.05, 3, 2, 19.1, 4, 2, 19.15, 5, 2, 19.2, 6, 2, 19.25, 0, 2, 19.3, 1, 2, 19.35, 2, 2, 19.4, 3, 2, 19.45, 4, 2, 19.5, 5, 2, 19.55, 6, 2, 19.6, 0, 2, 19.65, 1, 2, 19.7, 2, 2, 19.75, 3, 2, 19.8, 4, 2, 19.85, 5, 2, 19.9, 6, 2, 19.95, 0, 2, 20, 1]}, {"Target": "Parameter", "Id": "Param42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 6.667, 0.074, 13.333, 0.148, 20, 0.222]}, {"Target": "Parameter", "Id": "Param45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.4, 1, 6.667, -0.326, 13.333, -0.252, 20, -0.178]}, {"Target": "Parameter", "Id": "Param46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.3, 1, 6.667, 0.374, 13.333, 0.448, 20, 0.522]}, {"Target": "Parameter", "Id": "Param50", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 6.667, 1.333, 13.333, 2.667, 20, 4]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2.5, 10, 0, 5.017, -10, 0, 7.5, 10, 0, 10, -10, 0, 12.5, 26.92, 0, 15.017, -10, 0, 17.5, 10, 0, 20, -10]}, {"Target": "Parameter", "Id": "Param17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.76, 0, 2.5, 0.66, 0, 5.017, -5.76, 0, 7.5, 0.66, 0, 10, -5.76, 0, 12.5, 21.06, 0, 15.017, -5.76, 0, 17.5, 0.66, 0, 20, -5.76]}, {"Target": "Parameter", "Id": "Param49", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.35, 0, 1.25, 0, 0, 3.75, 0.7, 0, 6.267, 0, 0, 8.767, 0.7, 0, 11.283, 0, 0, 13.783, 0.7, 0, 16.3, 0, 0, 18.8, 0.7, 0, 20, 0.374]}, {"Target": "Parameter", "Id": "Param40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.25, 0, 0.917, 10, 0, 2.917, -10, 0, 4.917, 10, 0, 6.917, -10, 0, 8.917, 10, 0, 10.917, -10, 0, 12.917, 10, 0, 14.917, -10, 0, 16.917, 10, 0, 18.917, -10, 0, 20, 1.25]}, {"Target": "Parameter", "Id": "Param41", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2, 10, 0, 4, -10, 0, 6, 10, 0, 8, -10, 0, 10, 10, 0, 12, -10, 0, 14, 10, 0, 16, -10, 0, 18, 10, 0, 20, -10]}, {"Target": "Parameter", "Id": "Param32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2, 10, 0, 4, -10, 0, 6, 10, 0, 8, -10, 0, 10, 10, 0, 12, -10, 0, 14, 10, 0, 16, -10, 0, 18, 10, 0, 20, -10]}, {"Target": "Parameter", "Id": "Param33", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.886, 0, 0.583, 10, 0, 2.583, -10, 0, 4.583, 10, 0, 6.583, -10, 0, 8.583, 10, 0, 10.583, -10, 0, 12.583, 10, 0, 14.583, -10, 0, 16.583, 10, 0, 18.583, -10, 0, 20, 5.886]}, {"Target": "Parameter", "Id": "Param34", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -14.451, 0, 1.333, 30, 0, 3.333, -30, 0, 5.333, 30, 0, 7.333, -30, 0, 9.333, 30, 0, 11.333, -30, 0, 13.333, 30, 0, 15.333, -30, 0, 17.333, 30, 0, 19.333, -30, 0, 20, -14.451]}, {"Target": "Parameter", "Id": "Param28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -15, 0, 2, 15, 0, 4, -15, 0, 6, 15, 0, 8, -15, 0, 10, 15, 0, 12, -15, 0, 14, 15, 0, 16, -15, 0, 18, 15, 0, 20, -15]}, {"Target": "Parameter", "Id": "Param29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.886, 0, 0.583, 10, 0, 2.583, -10, 0, 4.583, 10, 0, 6.583, -10, 0, 8.583, 10, 0, 10.583, -10, 0, 12.583, 10, 0, 14.583, -10, 0, 16.583, 10, 0, 18.583, -10, 0, 20, 5.886]}, {"Target": "Parameter", "Id": "Param35", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -14.451, 0, 1.333, 30, 0, 3.333, -30, 0, 5.333, 30, 0, 7.333, -30, 0, 9.333, 30, 0, 11.333, -30, 0, 13.333, 30, 0, 15.333, -30, 0, 17.333, 30, 0, 19.333, -30, 0, 20, -14.451]}, {"Target": "Parameter", "Id": "Param30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.959, 0, 0.8, 10, 0, 2.8, -10, 0, 4.8, 10, 0, 6.8, -10, 0, 8.8, 10, 0, 10.8, -10, 0, 12.8, 10, 0, 14.8, -10, 0, 16.8, 10, 0, 18.8, -10, 0, 20, 2.959]}, {"Target": "Parameter", "Id": "Param31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 9.44, 0, 1.8, -10, 0, 3.8, 10, 0, 5.8, -10, 0, 7.8, 10, 0, 9.8, -10, 0, 11.8, 10, 0, 13.8, -10, 0, 15.8, 10, 0, 17.8, -10, 0, 19.8, 10, 0, 20, 9.44]}, {"Target": "Parameter", "Id": "Param36", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 13.773, 0, 0.683, 30, 0, 2.683, -30, 0, 4.683, 30, 0, 6.683, -30, 0, 8.683, 30, 0, 10.683, -30, 0, 12.683, 30, 0, 14.683, -30, 0, 16.683, 30, 0, 18.683, -30, 0, 20, 13.773]}, {"Target": "Parameter", "Id": "GL1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 6.667, 0.297, 13.333, 0.593, 20, 0.89]}, {"Target": "Parameter", "Id": "GL2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.39, 1, 6.667, 0.687, 13.333, 0.983, 20, 1.28]}, {"Target": "Parameter", "Id": "GL3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.72, 1, 6.667, 1.017, 13.333, 1.313, 20, 1.61]}, {"Target": "Parameter", "Id": "GL4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.21, 1, 6.667, 0.507, 13.333, 0.803, 20, 1.1]}, {"Target": "Parameter", "Id": "GL5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.53, 1, 6.667, 0.827, 13.333, 1.123, 20, 1.42]}, {"Target": "Parameter", "Id": "GL6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.89, 1, 6.667, 1.187, 13.333, 1.483, 20, 1.78]}, {"Target": "Parameter", "Id": "GL7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.77, 1, 6.667, 1.067, 13.333, 1.363, 20, 1.66]}, {"Target": "Parameter", "Id": "GL8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.09, 1, 6.667, 0.387, 13.333, 0.683, 20, 0.98]}, {"Target": "Parameter", "Id": "GL9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.301, 1, 6.667, 0.598, 13.333, 0.894, 20, 1.191]}, {"Target": "Parameter", "Id": "GL10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.45, 1, 6.667, 0.747, 13.333, 1.043, 20, 1.34]}, {"Target": "Parameter", "Id": "GL11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.64, 1, 6.667, 0.937, 13.333, 1.233, 20, 1.53]}, {"Target": "Parameter", "Id": "GL12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.15, 1, 6.667, 0.447, 13.333, 0.743, 20, 1.04]}, {"Target": "Parameter", "Id": "GL13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.585, 1, 6.667, 0.882, 13.333, 1.178, 20, 1.475]}, {"Target": "Parameter", "Id": "GL14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 6.667, 0.297, 13.333, 0.593, 20, 0.89]}, {"Target": "Parameter", "Id": "Param161", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.51, 1, 5.994, 0.843, 11.989, 1.177, 17.983, 1.51, 2, 18, 0.51, 1, 18.667, 0.547, 19.333, 0.584, 20, 0.621]}, {"Target": "Parameter", "Id": "Param162", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.31, 1, 5.994, 0.643, 11.989, 0.977, 17.983, 1.31, 2, 18, 0.31, 1, 18.667, 0.347, 19.333, 0.384, 20, 0.421]}, {"Target": "Parameter", "Id": "Param163", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.72, 1, 5.994, 1.053, 11.989, 1.387, 17.983, 1.72, 2, 18, 0.72, 1, 18.667, 0.757, 19.333, 0.794, 20, 0.831]}, {"Target": "Parameter", "Id": "Param164", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 5.994, 0.333, 11.989, 0.667, 17.983, 1, 2, 18, 0, 1, 18.667, 0.037, 19.333, 0.074, 20, 0.111]}, {"Target": "Parameter", "Id": "Param165", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.17, 1, 5.994, 0.503, 11.989, 0.837, 17.983, 1.17, 2, 18, 0.17, 1, 18.667, 0.207, 19.333, 0.244, 20, 0.281]}, {"Target": "Parameter", "Id": "touch_drag21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param118", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 20, 1]}, {"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.615, 0, 20, -0.615]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.12, 0, 20, 0.12]}, {"Target": "Parameter", "Id": "All_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 3.72, 0, 20, 3.72]}, {"Target": "Parameter", "Id": "All_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 9.17, 0, 20, 9.17]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -17.329, 0, 20, -17.329]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 8.47, 0, 20, 8.47]}, {"Target": "Parameter", "Id": "man_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "man_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param78", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param79", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "man_<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -13.2, 0, 20, -13.2]}, {"Target": "Parameter", "Id": "man_<PERSON>ze", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.065, 0, 20, -0.065]}, {"Target": "Parameter", "Id": "Param189", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "shangguang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param186", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param96", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param97", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param98", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 20, 1]}, {"Target": "Parameter", "Id": "shangguang2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param99", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 20, 1]}, {"Target": "Parameter", "Id": "Param22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param76", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param77", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "MB_xingfengTMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param127", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Mouthfunnel", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param293", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param72", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param74", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param75", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param73", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "MB_fenweiTMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "MB_fenweiliangdu", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.701, 0, 20, 0.701]}, {"Target": "Parameter", "Id": "MB_fenweiandu", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 20, 0.6]}, {"Target": "Parameter", "Id": "MB_fenweiqiu1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.699, 0, 20, 0.699]}, {"Target": "Parameter", "Id": "MB_fenweiqiu4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 20, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 20, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 20, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 20, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.699, 0, 20, 0.699]}, {"Target": "Parameter", "Id": "MB_fenweiqiushangxia", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "MB_fenweiqiudaxiao", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.001, 0, 20, 0.001]}, {"Target": "Parameter", "Id": "Param25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 20, 1]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 15, 0, 20, 15]}, {"Target": "Parameter", "Id": "Param58", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "ParamHandRREDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param55", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "ParamHandLREDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 20, 1]}, {"Target": "Parameter", "Id": "Param167", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param169", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param196", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param56", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param59", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10, 0, 20, 10]}, {"Target": "Parameter", "Id": "Param3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param57", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param124", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param80", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param48", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param37", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param38", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param60", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param47", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param81", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param95", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param125", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param171", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param166", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param123", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 20, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 20, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.8, 0, 20, 0.8]}, {"Target": "Parameter", "Id": "Param14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param39", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.5, 0, 20, 0.5]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 20, 1]}, {"Target": "Parameter", "Id": "Param61", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 20, 1]}, {"Target": "Parameter", "Id": "Param71", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 20, 1]}, {"Target": "Parameter", "Id": "Hair_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Hair_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param69", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param173", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param174", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param175", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param176", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param190", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param191", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param192", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param193", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param65", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 20, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "ParamShoulderStretch3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "ParamShoulderStretch4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "ParamChestZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "ParamWaistZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "ParamButtZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param153", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param154", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param155", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param156", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param82", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param83", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param84", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param85", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param66", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param67", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param68", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param62", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param63", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param64", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param86", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param89", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param87", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param88", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param94", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param126", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param128", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param129", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param52", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param70", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param208", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "Param23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "touch_drag23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "touch_drag24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "touch_drag25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "touch_drag26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "touch_drag27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "touch_drag28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "touch_drag29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "touch_drag30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "touch_drag31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "touch_drag32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "empty1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "empty2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "empty3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "empty4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "empty5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "empty6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "empty7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "empty8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "empty9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "empty10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "empty11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "empty12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "empty13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "empty14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "empty15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "empty16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "empty17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "empty18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "empty19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "empty20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "empty21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "empty22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "Parameter", "Id": "empty23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 20, 0]}, {"Target": "PartOpacity", "Id": "Part7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 20, 1]}], "UserData": []}