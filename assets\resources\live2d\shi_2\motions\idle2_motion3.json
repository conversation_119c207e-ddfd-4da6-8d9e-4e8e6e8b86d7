{"Version": 3, "Meta": {"Duration": 60.0, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 288, "TotalSegmentCount": 7860, "TotalPointCount": 8290, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.365, 0, 0.567, 1.834, 0, 1.567, -1.834, 0, 2.567, 1.834, 0, 3.567, -1.834, 0, 4.567, 1.834, 0, 5.567, -1.834, 0, 6.567, 1.834, 0, 7.567, -1.834, 0, 8.567, 1.834, 0, 9.567, -1.834, 0, 10.567, 1.834, 0, 11.567, -1.834, 0, 12.567, 1.834, 0, 13.567, -1.834, 0, 14.567, 1.834, 0, 15.567, -1.834, 0, 16.567, 1.834, 0, 17.567, -1.834, 0, 18.567, 1.834, 0, 19.567, -1.834, 1, 19.711, -1.834, 19.856, -1.42, 20, -0.365, 1, 20.189, 1.014, 20.378, 1.834, 20.567, 1.834, 0, 21.567, -1.834, 0, 22.567, 1.834, 0, 23.567, -1.834, 0, 24.567, 1.834, 0, 25.567, -1.834, 0, 26.567, 1.834, 0, 27.567, -1.834, 0, 28.567, 1.834, 0, 29.567, -1.834, 0, 30.567, 1.834, 0, 31.567, -1.834, 0, 32.567, 1.834, 0, 33.567, -1.834, 0, 34.567, 1.834, 0, 35.567, -1.834, 0, 36.567, 1.834, 0, 37.567, -1.834, 0, 38.567, 1.834, 0, 39.567, -1.834, 1, 39.711, -1.834, 39.856, -1.42, 40, -0.365, 1, 40.189, 1.014, 40.378, 1.834, 40.567, 1.834, 0, 41.567, -1.834, 0, 42.567, 1.834, 0, 43.567, -1.834, 0, 44.567, 1.834, 0, 45.567, -1.834, 0, 46.567, 1.834, 0, 47.567, -1.834, 0, 48.567, 1.834, 0, 49.567, -1.834, 0, 50.567, 1.834, 0, 51.567, -1.834, 0, 52.567, 1.834, 0, 53.567, -1.834, 0, 54.567, 1.834, 0, 55.567, -1.834, 0, 56.567, 1.834, 0, 57.567, -1.834, 0, 58.567, 1.834, 0, 59.567, -1.834, 0, 60, -0.365]}, {"Target": "Parameter", "Id": "ParamAngleY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.903, 0, 0.25, -1.313, 0, 1.25, 1.313, 0, 2.25, -1.313, 0, 3.25, 1.313, 0, 4.25, -1.313, 0, 5.25, 1.313, 0, 6.25, -1.313, 0, 7.25, 1.313, 0, 8.25, -1.313, 0, 9.25, 1.313, 0, 10.25, -1.313, 0, 11.25, 1.313, 0, 12.25, -1.313, 0, 13.25, 1.313, 0, 14.25, -1.313, 0, 15.25, 1.313, 0, 16.25, -1.313, 0, 17.25, 1.313, 0, 18.25, -1.313, 0, 19.25, 1.313, 1, 19.5, 1.313, 19.75, 0.328, 20, -0.903, 1, 20.083, -1.313, 20.167, -1.313, 20.25, -1.313, 0, 21.25, 1.313, 0, 22.25, -1.313, 0, 23.25, 1.313, 0, 24.25, -1.313, 0, 25.25, 1.313, 0, 26.25, -1.313, 0, 27.25, 1.313, 0, 28.25, -1.313, 0, 29.25, 1.313, 0, 30.25, -1.313, 0, 31.25, 1.313, 0, 32.25, -1.313, 0, 33.25, 1.313, 0, 34.25, -1.313, 0, 35.25, 1.313, 0, 36.25, -1.313, 0, 37.25, 1.313, 0, 38.25, -1.313, 0, 39.25, 1.313, 1, 39.5, 1.313, 39.75, 0.328, 40, -0.903, 1, 40.083, -1.313, 40.167, -1.313, 40.25, -1.313, 0, 41.25, 1.313, 0, 42.25, -1.313, 0, 43.25, 1.313, 0, 44.25, -1.313, 0, 45.25, 1.313, 0, 46.25, -1.313, 0, 47.25, 1.313, 0, 48.25, -1.313, 0, 49.25, 1.313, 0, 50.25, -1.313, 0, 51.25, 1.313, 0, 52.25, -1.313, 0, 53.25, 1.313, 0, 54.25, -1.313, 0, 55.25, 1.313, 0, 56.25, -1.313, 0, 57.25, 1.313, 0, 58.25, -1.313, 0, 59.25, 1.313, 0, 60, -0.903]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -29.943, 0, 0.9, -27.979, 0, 1.9, -30, 0, 2.9, -27.979, 0, 3.9, -30, 0, 4.9, -27.979, 0, 5.9, -30, 0, 6.9, -27.979, 0, 7.9, -30, 0, 8.9, -27.979, 0, 9.9, -30, 0, 10.9, -27.979, 0, 11.9, -30, 0, 12.9, -27.979, 0, 13.9, -30, 0, 14.9, -27.979, 0, 15.9, -30, 0, 16.9, -27.979, 0, 17.9, -30, 0, 18.9, -27.979, 0, 19.9, -30, 1, 19.933, -30, 19.967, -30, 20, -29.943, 1, 20.3, -28.923, 20.6, -27.979, 20.9, -27.979, 0, 21.9, -30, 0, 22.9, -27.979, 0, 23.9, -30, 0, 24.9, -27.979, 0, 25.9, -30, 0, 26.9, -27.979, 0, 27.9, -30, 0, 28.9, -27.979, 0, 29.9, -30, 0, 30.9, -27.979, 0, 31.9, -30, 0, 32.9, -27.979, 0, 33.9, -30, 0, 34.9, -27.979, 0, 35.9, -30, 0, 36.9, -27.979, 0, 37.9, -30, 0, 38.9, -27.979, 0, 39.9, -30, 1, 39.933, -30, 39.967, -30, 40, -29.943, 1, 40.3, -28.923, 40.6, -27.979, 40.9, -27.979, 0, 41.9, -30, 0, 42.9, -27.979, 0, 43.9, -30, 0, 44.9, -27.979, 0, 45.9, -30, 0, 46.9, -27.979, 0, 47.9, -30, 0, 48.9, -27.979, 0, 49.9, -30, 0, 50.9, -27.979, 0, 51.9, -30, 0, 52.9, -27.979, 0, 53.9, -30, 0, 54.9, -27.979, 0, 55.9, -30, 0, 56.9, -27.979, 0, 57.9, -30, 0, 58.9, -27.979, 0, 59.9, -30, 0, 60, -29.943]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 28.994, 0, 0.667, 30, 0, 1.667, 28.642, 0, 2.667, 30, 0, 3.667, 28.642, 0, 4.667, 30, 0, 5.667, 28.642, 0, 6.667, 30, 0, 7.667, 28.642, 0, 8.667, 30, 0, 9.667, 28.642, 0, 10.667, 30, 0, 11.667, 28.642, 0, 12.667, 30, 0, 13.667, 28.642, 0, 14.667, 30, 0, 15.667, 28.642, 0, 16.667, 30, 0, 17.667, 28.642, 0, 18.667, 30, 0, 19.667, 28.642, 1, 19.778, 28.642, 19.889, 28.701, 20, 28.994, 1, 20.222, 29.581, 20.445, 30, 20.667, 30, 0, 21.667, 28.642, 0, 22.667, 30, 0, 23.667, 28.642, 0, 24.667, 30, 0, 25.667, 28.642, 0, 26.667, 30, 0, 27.667, 28.642, 0, 28.667, 30, 0, 29.667, 28.642, 0, 30.667, 30, 0, 31.667, 28.642, 0, 32.667, 30, 0, 33.667, 28.642, 0, 34.667, 30, 0, 35.667, 28.642, 0, 36.667, 30, 0, 37.667, 28.642, 0, 38.667, 30, 0, 39.667, 28.642, 1, 39.778, 28.642, 39.889, 28.701, 40, 28.994, 1, 40.222, 29.581, 40.445, 30, 40.667, 30, 0, 41.667, 28.642, 0, 42.667, 30, 0, 43.667, 28.642, 0, 44.667, 30, 0, 45.667, 28.642, 0, 46.667, 30, 0, 47.667, 28.642, 0, 48.667, 30, 0, 49.667, 28.642, 0, 50.667, 30, 0, 51.667, 28.642, 0, 52.667, 30, 0, 53.667, 28.642, 0, 54.667, 30, 0, 55.667, 28.642, 0, 56.667, 30, 0, 57.667, 28.642, 0, 58.667, 30, 0, 59.667, 28.642, 0, 60, 28.994]}, {"Target": "Parameter", "Id": "ParamBodyAngleX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -23.202, 0, 1.383, 0, 0, 3.383, -30, 0, 5.383, 0, 0, 7.383, -30, 0, 9.383, 0, 0, 11.383, -30, 0, 13.383, 0, 0, 15.383, -30, 0, 17.383, 0, 0, 19.383, -30, 1, 19.589, -30, 19.794, -29.129, 20, -23.202, 1, 20.461, -9.907, 20.922, 0, 21.383, 0, 0, 23.383, -30, 0, 25.383, 0, 0, 27.383, -30, 0, 29.383, 0, 0, 31.383, -30, 0, 33.383, 0, 0, 35.383, -30, 0, 37.383, 0, 0, 39.383, -30, 1, 39.589, -30, 39.794, -29.129, 40, -23.202, 1, 40.461, -9.907, 40.922, 0, 41.383, 0, 0, 43.383, -30, 0, 45.383, 0, 0, 47.383, -30, 0, 49.383, 0, 0, 51.383, -30, 0, 53.383, 0, 0, 55.383, -30, 0, 57.383, 0, 0, 59.383, -30, 0, 60, -23.202]}, {"Target": "Parameter", "Id": "ParamBodyAngleY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 22.67, 0, 0.4, 18.691, 0, 1.4, 30, 0, 2.4, 18.691, 0, 3.4, 30, 0, 4.4, 18.691, 0, 5.4, 30, 0, 6.4, 18.691, 0, 7.4, 30, 0, 8.4, 18.691, 0, 9.4, 30, 0, 10.4, 18.691, 0, 11.4, 30, 0, 12.4, 18.691, 0, 13.4, 30, 0, 14.4, 18.691, 0, 15.4, 30, 0, 16.4, 18.691, 0, 17.4, 30, 0, 18.4, 18.691, 0, 19.4, 30, 1, 19.6, 30, 19.8, 27.146, 20, 22.67, 1, 20.133, 19.686, 20.267, 18.691, 20.4, 18.691, 0, 21.4, 30, 0, 22.4, 18.691, 0, 23.4, 30, 0, 24.4, 18.691, 0, 25.4, 30, 0, 26.4, 18.691, 0, 27.4, 30, 0, 28.4, 18.691, 0, 29.4, 30, 0, 30.4, 18.691, 0, 31.4, 30, 0, 32.4, 18.691, 0, 33.4, 30, 0, 34.4, 18.691, 0, 35.4, 30, 0, 36.4, 18.691, 0, 37.4, 30, 0, 38.4, 18.691, 0, 39.4, 30, 1, 39.6, 30, 39.8, 27.146, 40, 22.67, 1, 40.133, 19.686, 40.267, 18.691, 40.4, 18.691, 0, 41.4, 30, 0, 42.4, 18.691, 0, 43.4, 30, 0, 44.4, 18.691, 0, 45.4, 30, 0, 46.4, 18.691, 0, 47.4, 30, 0, 48.4, 18.691, 0, 49.4, 30, 0, 50.4, 18.691, 0, 51.4, 30, 0, 52.4, 18.691, 0, 53.4, 30, 0, 54.4, 18.691, 0, 55.4, 30, 0, 56.4, 18.691, 0, 57.4, 30, 0, 58.4, 18.691, 0, 59.4, 30, 0, 60, 22.67]}, {"Target": "Parameter", "Id": "ParamWaistZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.584, 0, 0.2, -2, 0, 1.2, 2, 0, 2.2, -2, 0, 3.2, 2, 0, 4.2, -2, 0, 5.2, 2, 0, 6.2, -2, 0, 7.2, 2, 0, 8.2, -2, 0, 9.2, 2, 0, 10.2, -2, 0, 11.2, 2, 0, 12.2, -2, 0, 13.2, 2, 0, 14.2, -2, 0, 15.2, 2, 0, 16.2, -2, 0, 17.2, 2, 0, 18.2, -2, 0, 19.2, 2, 1, 19.467, 2, 19.733, 0.356, 20, -1.584, 1, 20.067, -2.069, 20.133, -2, 20.2, -2, 0, 21.2, 2, 0, 22.2, -2, 0, 23.2, 2, 0, 24.2, -2, 0, 25.2, 2, 0, 26.2, -2, 0, 27.2, 2, 0, 28.2, -2, 0, 29.2, 2, 0, 30.2, -2, 0, 31.2, 2, 0, 32.2, -2, 0, 33.2, 2, 0, 34.2, -2, 0, 35.2, 2, 0, 36.2, -2, 0, 37.2, 2, 0, 38.2, -2, 0, 39.2, 2, 1, 39.467, 2, 39.733, 0.356, 40, -1.584, 1, 40.067, -2.069, 40.133, -2, 40.2, -2, 0, 41.2, 2, 0, 42.2, -2, 0, 43.2, 2, 0, 44.2, -2, 0, 45.2, 2, 0, 46.2, -2, 0, 47.2, 2, 0, 48.2, -2, 0, 49.2, 2, 0, 50.2, -2, 0, 51.2, 2, 0, 52.2, -2, 0, 53.2, 2, 0, 54.2, -2, 0, 55.2, 2, 0, 56.2, -2, 0, 57.2, 2, 0, 58.2, -2, 0, 59.2, 2, 0, 60, -1.584]}, {"Target": "Parameter", "Id": "ParamShoulderStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.149, 0, 0.6, -10, 0, 1.6, -2.515, 0, 2.6, -10, 0, 3.6, -2.515, 0, 4.6, -10, 0, 5.6, -2.515, 0, 6.6, -10, 0, 7.6, -2.515, 0, 8.6, -10, 0, 9.6, -2.515, 0, 10.6, -10, 0, 11.6, -2.515, 0, 12.6, -10, 0, 13.6, -2.515, 0, 14.6, -10, 0, 15.6, -2.515, 0, 16.6, -10, 0, 17.6, -2.515, 0, 18.6, -10, 0, 19.6, -2.515, 1, 19.733, -2.515, 19.867, -3.173, 20, -5.149, 1, 20.2, -8.112, 20.4, -10, 20.6, -10, 0, 21.6, -2.515, 0, 22.6, -10, 0, 23.6, -2.515, 0, 24.6, -10, 0, 25.6, -2.515, 0, 26.6, -10, 0, 27.6, -2.515, 0, 28.6, -10, 0, 29.6, -2.515, 0, 30.6, -10, 0, 31.6, -2.515, 0, 32.6, -10, 0, 33.6, -2.515, 0, 34.6, -10, 0, 35.6, -2.515, 0, 36.6, -10, 0, 37.6, -2.515, 0, 38.6, -10, 0, 39.6, -2.515, 1, 39.733, -2.515, 39.867, -3.173, 40, -5.149, 1, 40.2, -8.112, 40.4, -10, 40.6, -10, 0, 41.6, -2.515, 0, 42.6, -10, 0, 43.6, -2.515, 0, 44.6, -10, 0, 45.6, -2.515, 0, 46.6, -10, 0, 47.6, -2.515, 0, 48.6, -10, 0, 49.6, -2.515, 0, 50.6, -10, 0, 51.6, -2.515, 0, 52.6, -10, 0, 53.6, -2.515, 0, 54.6, -10, 0, 55.6, -2.515, 0, 56.6, -10, 0, 57.6, -2.515, 0, 58.6, -10, 0, 59.6, -2.515, 0, 60, -5.149]}, {"Target": "Parameter", "Id": "ParamShoulderStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -25.012, 0, 0.6, -30, 0, 1.6, -22.305, 0, 2.6, -30, 0, 3.6, -22.305, 0, 4.6, -30, 0, 5.6, -22.305, 0, 6.6, -30, 0, 7.6, -22.305, 0, 8.6, -30, 0, 9.6, -22.305, 0, 10.6, -30, 0, 11.6, -22.305, 0, 12.6, -30, 0, 13.6, -22.305, 0, 14.6, -30, 0, 15.6, -22.305, 0, 16.6, -30, 0, 17.6, -22.305, 0, 18.6, -30, 0, 19.6, -22.305, 1, 19.733, -22.305, 19.867, -22.981, 20, -25.012, 1, 20.2, -28.058, 20.4, -30, 20.6, -30, 0, 21.6, -22.305, 0, 22.6, -30, 0, 23.6, -22.305, 0, 24.6, -30, 0, 25.6, -22.305, 0, 26.6, -30, 0, 27.6, -22.305, 0, 28.6, -30, 0, 29.6, -22.305, 0, 30.6, -30, 0, 31.6, -22.305, 0, 32.6, -30, 0, 33.6, -22.305, 0, 34.6, -30, 0, 35.6, -22.305, 0, 36.6, -30, 0, 37.6, -22.305, 0, 38.6, -30, 0, 39.6, -22.305, 1, 39.733, -22.305, 39.867, -22.981, 40, -25.012, 1, 40.2, -28.058, 40.4, -30, 40.6, -30, 0, 41.6, -22.305, 0, 42.6, -30, 0, 43.6, -22.305, 0, 44.6, -30, 0, 45.6, -22.305, 0, 46.6, -30, 0, 47.6, -22.305, 0, 48.6, -30, 0, 49.6, -22.305, 0, 50.6, -30, 0, 51.6, -22.305, 0, 52.6, -30, 0, 53.6, -22.305, 0, 54.6, -30, 0, 55.6, -22.305, 0, 56.6, -30, 0, 57.6, -22.305, 0, 58.6, -30, 0, 59.6, -22.305, 0, 60, -25.012]}, {"Target": "Parameter", "Id": "ParamButtXA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1, 7.235, 0, 2, 0, 0, 3, 7.235, 0, 4, 0, 0, 5, 7.235, 0, 6, 0, 0, 7, 7.235, 0, 8, 0, 0, 9, 7.235, 0, 10, 0, 0, 11, 7.235, 0, 12, 0, 0, 13, 7.235, 0, 14, 0, 0, 15, 7.235, 0, 16, 0, 0, 17, 7.235, 0, 18, 0, 0, 19, 7.235, 0, 20, 0, 0, 21, 7.235, 0, 22, 0, 0, 23, 7.235, 0, 24, 0, 0, 25, 7.235, 0, 26, 0, 0, 27, 7.235, 0, 28, 0, 0, 29, 7.235, 0, 30, 0, 0, 31, 7.235, 0, 32, 0, 0, 33, 7.235, 0, 34, 0, 0, 35, 7.235, 0, 36, 0, 0, 37, 7.235, 0, 38, 0, 0, 39, 7.235, 0, 40, 0, 0, 41, 7.235, 0, 42, 0, 0, 43, 7.235, 0, 44, 0, 0, 45, 7.235, 0, 46, 0, 0, 47, 7.235, 0, 48, 0, 0, 49, 7.235, 0, 50, 0, 0, 51, 7.235, 0, 52, 0, 0, 53, 7.235, 0, 54, 0, 0, 55, 7.235, 0, 56, 0, 0, 57, 7.235, 0, 58, 0, 0, 59, 7.235, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.361, 0, 0.333, -7.236, 0, 1.333, 0, 0, 2.333, -7.236, 0, 3.333, 0, 0, 4.333, -7.236, 0, 5.333, 0, 0, 6.333, -7.236, 0, 7.333, 0, 0, 8.333, -7.236, 0, 9.333, 0, 0, 10.333, -7.236, 0, 11.333, 0, 0, 12.333, -7.236, 0, 13.333, 0, 0, 14.333, -7.236, 0, 15.333, 0, 0, 16.333, -7.236, 0, 17.333, 0, 0, 18.333, -7.236, 0, 19.333, 0, 1, 19.555, 0, 19.778, -2.236, 20, -5.361, 1, 20.111, -6.923, 20.222, -7.236, 20.333, -7.236, 0, 21.333, 0, 0, 22.333, -7.236, 0, 23.333, 0, 0, 24.333, -7.236, 0, 25.333, 0, 0, 26.333, -7.236, 0, 27.333, 0, 0, 28.333, -7.236, 0, 29.333, 0, 0, 30.333, -7.236, 0, 31.333, 0, 0, 32.333, -7.236, 0, 33.333, 0, 0, 34.333, -7.236, 0, 35.333, 0, 0, 36.333, -7.236, 0, 37.333, 0, 0, 38.333, -7.236, 0, 39.333, 0, 1, 39.555, 0, 39.778, -2.236, 40, -5.361, 1, 40.111, -6.923, 40.222, -7.236, 40.333, -7.236, 0, 41.333, 0, 0, 42.333, -7.236, 0, 43.333, 0, 0, 44.333, -7.236, 0, 45.333, 0, 0, 46.333, -7.236, 0, 47.333, 0, 0, 48.333, -7.236, 0, 49.333, 0, 0, 50.333, -7.236, 0, 51.333, 0, 0, 52.333, -7.236, 0, 53.333, 0, 0, 54.333, -7.236, 0, 55.333, 0, 0, 56.333, -7.236, 0, 57.333, 0, 0, 58.333, -7.236, 0, 59.333, 0, 0, 60, -5.358]}, {"Target": "Parameter", "Id": "ParamButtZA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 29.968, 0, 0.033, 30, 0, 1.033, 20, 0, 2.033, 30, 0, 3.033, 20, 0, 4.033, 30, 0, 5.033, 20, 0, 6.033, 30, 0, 7.033, 20, 0, 8.033, 30, 0, 9.033, 20, 0, 10.033, 30, 0, 11.033, 20, 0, 12.033, 30, 0, 13.033, 20, 0, 14.033, 30, 0, 15.033, 20, 0, 16.033, 30, 0, 17.033, 20, 0, 18.033, 30, 0, 19.033, 20, 1, 19.355, 20, 19.678, 24.982, 20, 29.968, 1, 20.011, 30, 20.022, 30, 20.033, 30, 0, 21.033, 20, 0, 22.033, 30, 0, 23.033, 20, 0, 24.033, 30, 0, 25.033, 20, 0, 26.033, 30, 0, 27.033, 20, 0, 28.033, 30, 0, 29.033, 20, 0, 30.033, 30, 0, 31.033, 20, 0, 32.033, 30, 0, 33.033, 20, 0, 34.033, 30, 0, 35.033, 20, 0, 36.033, 30, 0, 37.033, 20, 0, 38.033, 30, 0, 39.033, 20, 1, 39.355, 20, 39.678, 24.982, 40, 29.968, 1, 40.011, 30, 40.022, 30, 40.033, 30, 0, 41.033, 20, 0, 42.033, 30, 0, 43.033, 20, 0, 44.033, 30, 0, 45.033, 20, 0, 46.033, 30, 0, 47.033, 20, 0, 48.033, 30, 0, 49.033, 20, 0, 50.033, 30, 0, 51.033, 20, 0, 52.033, 30, 0, 53.033, 20, 0, 54.033, 30, 0, 55.033, 20, 0, 56.033, 30, 0, 57.033, 20, 0, 58.033, 30, 0, 59.033, 20, 0, 60, 29.968]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.473, 0, 1.417, -0.703, 0, 3.417, 0.778, 0, 5.417, -0.703, 0, 7.417, 0.778, 0, 9.417, -0.703, 0, 11.417, 0.778, 0, 13.417, -0.703, 0, 15.417, 0.778, 0, 17.417, -0.703, 0, 19.417, 0.778, 1, 19.611, 0.778, 19.806, 0.749, 20, 0.473, 1, 20.472, -0.196, 20.945, -0.703, 21.417, -0.703, 0, 23.417, 0.778, 0, 25.417, -0.703, 0, 27.417, 0.778, 0, 29.417, -0.703, 0, 31.417, 0.778, 0, 33.417, -0.703, 0, 35.417, 0.778, 0, 37.417, -0.703, 0, 39.417, 0.778, 1, 39.611, 0.778, 39.806, 0.749, 40, 0.473, 1, 40.472, -0.196, 40.945, -0.703, 41.417, -0.703, 0, 43.417, 0.778, 0, 45.417, -0.703, 0, 47.417, 0.778, 0, 49.417, -0.703, 0, 51.417, 0.778, 0, 53.417, -0.703, 0, 55.417, 0.778, 0, 57.417, -0.703, 0, 59.417, 0.778, 0, 60, 0.473]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.616, 0, 1.333, -0.481, 0, 3.333, 1, 0, 5.333, -0.481, 0, 7.333, 1, 0, 9.333, -0.481, 0, 11.333, 1, 0, 13.333, -0.481, 0, 15.333, 1, 0, 17.333, -0.481, 0, 19.333, 1, 1, 19.555, 1, 19.778, 0.936, 20, 0.616, 1, 20.444, -0.024, 20.889, -0.481, 21.333, -0.481, 0, 23.333, 1, 0, 25.333, -0.481, 0, 27.333, 1, 0, 29.333, -0.481, 0, 31.333, 1, 0, 33.333, -0.481, 0, 35.333, 1, 0, 37.333, -0.481, 0, 39.333, 1, 1, 39.555, 1, 39.778, 0.936, 40, 0.616, 1, 40.444, -0.024, 40.889, -0.481, 41.333, -0.481, 0, 43.333, 1, 0, 45.333, -0.481, 0, 47.333, 1, 0, 49.333, -0.481, 0, 51.333, 1, 0, 53.333, -0.481, 0, 55.333, 1, 0, 57.333, -0.481, 0, 59.333, 1, 0, 60, 0.616]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.65, 1, 0, 1.033, 0, 0, 1.35, 1, 2, 4.283, 1, 0, 4.717, 0, 0, 5.083, 1, 2, 20, 1, 2, 20.65, 1, 0, 21.033, 0, 0, 21.35, 1, 2, 24.283, 1, 0, 24.717, 0, 0, 25.083, 1, 2, 40, 1, 2, 40.65, 1, 0, 41.033, 0, 0, 41.35, 1, 2, 44.283, 1, 0, 44.717, 0, 0, 45.083, 1, 2, 60, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.717, 0, 0, 1.083, 0.6, 0, 1.417, 0, 2, 4.333, 0, 0, 4.783, 0.6, 0, 5.133, 0, 2, 20, 0, 2, 20.717, 0, 0, 21.083, 0.6, 0, 21.417, 0, 2, 24.333, 0, 0, 24.783, 0.6, 0, 25.133, 0, 2, 40, 0, 2, 40.717, 0, 0, 41.083, 0.6, 0, 41.417, 0, 2, 44.333, 0, 0, 44.783, 0.6, 0, 45.133, 0, 2, 60, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.65, 1, 0, 1.033, 0, 0, 1.35, 1, 2, 4.283, 1, 0, 4.717, 0, 0, 5.083, 1, 2, 20, 1, 2, 20.65, 1, 0, 21.033, 0, 0, 21.35, 1, 2, 24.283, 1, 0, 24.717, 0, 0, 25.083, 1, 2, 40, 1, 2, 40.65, 1, 0, 41.033, 0, 0, 41.35, 1, 2, 44.283, 1, 0, 44.717, 0, 0, 45.083, 1, 2, 60, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.717, 0, 0, 1.083, 0.6, 0, 1.417, 0, 2, 4.333, 0, 0, 4.783, 0.6, 0, 5.133, 0, 2, 20, 0, 2, 20.717, 0, 0, 21.083, 0.6, 0, 21.417, 0, 2, 24.333, 0, 0, 24.783, 0.6, 0, 25.133, 0, 2, 40, 0, 2, 40.717, 0, 0, 41.083, 0.6, 0, 41.417, 0, 2, 44.333, 0, 0, 44.783, 0.6, 0, 45.133, 0, 2, 60, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.65, 0, 0, 1.033, 0.022, 0, 1.433, 0, 2, 4.283, 0, 0, 4.667, 0.022, 0, 5.067, 0, 2, 20, 0, 2, 20.65, 0, 0, 21.033, 0.022, 0, 21.433, 0, 2, 24.283, 0, 0, 24.667, 0.022, 0, 25.067, 0, 2, 40, 0, 2, 40.65, 0, 0, 41.033, 0.022, 0, 41.433, 0, 2, 44.283, 0, 0, 44.667, 0.022, 0, 45.067, 0, 2, 60, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.65, 0, 0, 1.033, 0.238, 0, 1.433, 0, 2, 4.283, 0, 0, 4.667, 0.238, 0, 5.067, 0, 2, 20, 0, 2, 20.65, 0, 0, 21.033, 0.238, 0, 21.433, 0, 2, 24.283, 0, 0, 24.667, 0.238, 0, 25.067, 0, 2, 40, 0, 2, 40.65, 0, 0, 41.033, 0.238, 0, 41.433, 0, 2, 44.283, 0, 0, 44.667, 0.238, 0, 45.067, 0, 2, 60, 0]}, {"Target": "Parameter", "Id": "Param4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.65, 0, 0, 1.033, -0.459, 0, 1.433, 0, 2, 4.283, 0, 0, 4.667, -0.459, 0, 5.067, 0, 2, 20, 0, 2, 20.65, 0, 0, 21.033, -0.459, 0, 21.433, 0, 2, 24.283, 0, 0, 24.667, -0.459, 0, 25.067, 0, 2, 40, 0, 2, 40.65, 0, 0, 41.033, -0.459, 0, 41.433, 0, 2, 44.283, 0, 0, 44.667, -0.459, 0, 45.067, 0, 2, 60, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.65, 0, 0, 1.033, 0.163, 0, 1.433, 0, 2, 4.283, 0, 0, 4.667, 0.163, 0, 5.067, 0, 2, 20, 0, 2, 20.65, 0, 0, 21.033, 0.163, 0, 21.433, 0, 2, 24.283, 0, 0, 24.667, 0.163, 0, 25.067, 0, 2, 40, 0, 2, 40.65, 0, 0, 41.033, 0.163, 0, 41.433, 0, 2, 44.283, 0, 0, 44.667, 0.163, 0, 45.067, 0, 2, 60, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.65, 0, 0, 1.033, 0.239, 0, 1.433, 0, 2, 4.283, 0, 0, 4.667, 0.239, 0, 5.067, 0, 2, 20, 0, 2, 20.65, 0, 0, 21.033, 0.239, 0, 21.433, 0, 2, 24.283, 0, 0, 24.667, 0.239, 0, 25.067, 0, 2, 40, 0, 2, 40.65, 0, 0, 41.033, 0.239, 0, 41.433, 0, 2, 44.283, 0, 0, 44.667, 0.239, 0, 45.067, 0, 2, 60, 0]}, {"Target": "Parameter", "Id": "Param11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.65, 0, 0, 1.033, -0.449, 0, 1.433, 0, 2, 4.283, 0, 0, 4.667, -0.449, 0, 5.067, 0, 2, 20, 0, 2, 20.65, 0, 0, 21.033, -0.449, 0, 21.433, 0, 2, 24.283, 0, 0, 24.667, -0.449, 0, 25.067, 0, 2, 40, 0, 2, 40.65, 0, 0, 41.033, -0.449, 0, 41.433, 0, 2, 44.283, 0, 0, 44.667, -0.449, 0, 45.067, 0, 2, 60, 0]}, {"Target": "Parameter", "Id": "Hair_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -16, 0, 1, 16, 0, 2, -16, 0, 3, 16, 0, 4, -16, 0, 5, 16, 0, 6, -16, 0, 7, 16, 0, 8, -16, 0, 9, 16, 0, 10, -16, 0, 11, 16, 0, 12, -16, 0, 13, 16, 0, 14, -16, 0, 15, 16, 0, 16, -16, 0, 17, 16, 0, 18, -16, 0, 19, 16, 0, 20, -16, 0, 21, 16, 0, 22, -16, 0, 23, 16, 0, 24, -16, 0, 25, 16, 0, 26, -16, 0, 27, 16, 0, 28, -16, 0, 29, 16, 0, 30, -16, 0, 31, 16, 0, 32, -16, 0, 33, 16, 0, 34, -16, 0, 35, 16, 0, 36, -16, 0, 37, 16, 0, 38, -16, 0, 39, 16, 0, 40, -16, 0, 41, 16, 0, 42, -16, 0, 43, 16, 0, 44, -16, 0, 45, 16, 0, 46, -16, 0, 47, 16, 0, 48, -16, 0, 49, 16, 0, 50, -16, 0, 51, 16, 0, 52, -16, 0, 53, 16, 0, 54, -16, 0, 55, 16, 0, 56, -16, 0, 57, 16, 0, 58, -16, 0, 59, 16, 0, 60, -16]}, {"Target": "Parameter", "Id": "Hair_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 1, 10, 0, 2, -10, 0, 3, 10, 0, 4, -10, 0, 5, 10, 0, 6, -10, 0, 7, 10, 0, 8, -10, 0, 9, 10, 0, 10, -10, 0, 11, 10, 0, 12, -10, 0, 13, 10, 0, 14, -10, 0, 15, 10, 0, 16, -10, 0, 17, 10, 0, 18, -10, 0, 19, 10, 0, 20, -10, 0, 21, 10, 0, 22, -10, 0, 23, 10, 0, 24, -10, 0, 25, 10, 0, 26, -10, 0, 27, 10, 0, 28, -10, 0, 29, 10, 0, 30, -10, 0, 31, 10, 0, 32, -10, 0, 33, 10, 0, 34, -10, 0, 35, 10, 0, 36, -10, 0, 37, 10, 0, 38, -10, 0, 39, 10, 0, 40, -10, 0, 41, 10, 0, 42, -10, 0, 43, 10, 0, 44, -10, 0, 45, 10, 0, 46, -10, 0, 47, 10, 0, 48, -10, 0, 49, 10, 0, 50, -10, 0, 51, 10, 0, 52, -10, 0, 53, 10, 0, 54, -10, 0, 55, 10, 0, 56, -10, 0, 57, 10, 0, 58, -10, 0, 59, 10, 0, 60, -10]}, {"Target": "Parameter", "Id": "Param153", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.467, -7.631, 0, 1.383, 7.345, 0, 2.383, -7.394, 0, 3.383, 7.391, 0, 4.383, -7.391, 0, 5.383, 7.391, 0, 6.383, -7.391, 0, 7.383, 7.391, 0, 8.383, -7.391, 0, 9.383, 7.391, 0, 10.383, -7.391, 0, 11.383, 7.391, 0, 12.383, -7.391, 0, 13.383, 7.391, 0, 14.383, -7.391, 0, 15.383, 7.391, 0, 16.383, -7.391, 0, 17.383, 7.391, 0, 18.383, -7.391, 0, 19.383, 7.391, 1, 19.589, 7.391, 19.794, 5.561, 20, 0, 1, 20.156, -4.208, 20.311, -7.631, 20.467, -7.631, 0, 21.383, 7.345, 0, 22.383, -7.394, 0, 23.383, 7.391, 0, 24.383, -7.391, 0, 25.383, 7.391, 0, 26.383, -7.391, 0, 27.383, 7.391, 0, 28.383, -7.391, 0, 29.383, 7.391, 0, 30.383, -7.391, 0, 31.383, 7.391, 0, 32.383, -7.391, 0, 33.383, 7.391, 0, 34.383, -7.391, 0, 35.383, 7.391, 0, 36.383, -7.391, 0, 37.383, 7.391, 0, 38.383, -7.391, 0, 39.383, 7.391, 1, 39.589, 7.391, 39.794, 5.561, 40, 0, 1, 40.156, -4.208, 40.311, -7.631, 40.467, -7.631, 0, 41.383, 7.345, 0, 42.383, -7.394, 0, 43.383, 7.391, 0, 44.383, -7.391, 0, 45.383, 7.391, 0, 46.383, -7.391, 0, 47.383, 7.391, 0, 48.383, -7.391, 0, 49.383, 7.391, 0, 50.383, -7.391, 0, 51.383, 7.391, 0, 52.383, -7.391, 0, 53.383, 7.391, 0, 54.383, -7.391, 0, 55.383, 7.391, 0, 56.383, -7.391, 0, 57.383, 7.391, 0, 58.383, -7.391, 0, 59.383, 7.391, 0, 60, -1.75]}, {"Target": "Parameter", "Id": "Param154", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.283, 2.299, 0, 0.75, -2.358, 0, 1.65, 1.399, 0, 1.85, 1.248, 0, 2.117, 1.622, 0, 2.65, -1.546, 0, 2.9, -1.267, 0, 3.117, -1.574, 0, 3.65, 1.537, 0, 3.9, 1.271, 0, 4.117, 1.577, 0, 4.65, -1.537, 0, 4.9, -1.271, 0, 5.117, -1.577, 0, 5.65, 1.537, 0, 5.9, 1.271, 0, 6.117, 1.577, 0, 6.65, -1.537, 0, 6.9, -1.271, 0, 7.117, -1.577, 0, 7.65, 1.537, 0, 7.9, 1.271, 0, 8.117, 1.577, 0, 8.65, -1.537, 0, 8.9, -1.271, 0, 9.117, -1.577, 0, 9.65, 1.537, 0, 9.9, 1.271, 0, 10.117, 1.577, 0, 10.65, -1.537, 0, 10.9, -1.271, 0, 11.117, -1.577, 0, 11.65, 1.537, 0, 11.9, 1.271, 0, 12.117, 1.577, 0, 12.65, -1.537, 0, 12.9, -1.271, 0, 13.117, -1.577, 0, 13.65, 1.537, 0, 13.9, 1.271, 0, 14.117, 1.577, 0, 14.65, -1.537, 0, 14.9, -1.271, 0, 15.117, -1.577, 0, 15.65, 1.537, 0, 15.9, 1.271, 0, 16.117, 1.577, 0, 16.65, -1.537, 0, 16.9, -1.271, 0, 17.117, -1.577, 0, 17.65, 1.537, 0, 17.9, 1.271, 0, 18.117, 1.577, 0, 18.65, -1.537, 0, 18.9, -1.271, 0, 19.117, -1.577, 0, 19.65, 1.537, 1, 19.733, 1.537, 19.817, 1.511, 19.9, 1.271, 1, 19.933, 1.175, 19.967, 0, 20, 0, 0, 20.283, 2.299, 0, 20.75, -2.358, 0, 21.65, 1.399, 0, 21.85, 1.248, 0, 22.117, 1.622, 0, 22.65, -1.546, 0, 22.9, -1.267, 0, 23.117, -1.574, 0, 23.65, 1.537, 0, 23.9, 1.271, 0, 24.117, 1.577, 0, 24.65, -1.537, 0, 24.9, -1.271, 0, 25.117, -1.577, 0, 25.65, 1.537, 0, 25.9, 1.271, 0, 26.117, 1.577, 0, 26.65, -1.537, 0, 26.9, -1.271, 0, 27.117, -1.577, 0, 27.65, 1.537, 0, 27.9, 1.271, 0, 28.117, 1.577, 0, 28.65, -1.537, 0, 28.9, -1.271, 0, 29.117, -1.577, 0, 29.65, 1.537, 0, 29.9, 1.271, 0, 30.117, 1.577, 0, 30.65, -1.537, 0, 30.9, -1.271, 0, 31.117, -1.577, 0, 31.65, 1.537, 0, 31.9, 1.271, 0, 32.117, 1.577, 0, 32.65, -1.537, 0, 32.9, -1.271, 0, 33.117, -1.577, 0, 33.65, 1.537, 0, 33.9, 1.271, 0, 34.117, 1.577, 0, 34.65, -1.537, 0, 34.9, -1.271, 0, 35.117, -1.577, 0, 35.65, 1.537, 0, 35.9, 1.271, 0, 36.117, 1.577, 0, 36.65, -1.537, 0, 36.9, -1.271, 0, 37.117, -1.577, 0, 37.65, 1.537, 0, 37.9, 1.271, 0, 38.117, 1.577, 0, 38.65, -1.537, 0, 38.9, -1.271, 0, 39.117, -1.577, 0, 39.65, 1.537, 1, 39.733, 1.537, 39.817, 1.511, 39.9, 1.271, 1, 39.933, 1.175, 39.967, 0, 40, 0, 0, 40.283, 2.299, 0, 40.75, -2.358, 0, 41.65, 1.399, 0, 41.85, 1.248, 0, 42.117, 1.622, 0, 42.65, -1.546, 0, 42.9, -1.267, 0, 43.117, -1.574, 0, 43.65, 1.537, 0, 43.9, 1.271, 0, 44.117, 1.577, 0, 44.65, -1.537, 0, 44.9, -1.271, 0, 45.117, -1.577, 0, 45.65, 1.537, 0, 45.9, 1.271, 0, 46.117, 1.577, 0, 46.65, -1.537, 0, 46.9, -1.271, 0, 47.117, -1.577, 0, 47.65, 1.537, 0, 47.9, 1.271, 0, 48.117, 1.577, 0, 48.65, -1.537, 0, 48.9, -1.271, 0, 49.117, -1.577, 0, 49.65, 1.537, 0, 49.9, 1.271, 0, 50.117, 1.577, 0, 50.65, -1.537, 0, 50.9, -1.271, 0, 51.117, -1.577, 0, 51.65, 1.537, 0, 51.9, 1.271, 0, 52.117, 1.577, 0, 52.65, -1.537, 0, 52.9, -1.271, 0, 53.117, -1.577, 0, 53.65, 1.537, 0, 53.9, 1.271, 0, 54.117, 1.577, 0, 54.65, -1.537, 0, 54.9, -1.271, 0, 55.117, -1.577, 0, 55.65, 1.537, 0, 55.9, 1.271, 0, 56.117, 1.577, 0, 56.65, -1.537, 0, 56.9, -1.271, 0, 57.117, -1.577, 0, 57.65, 1.537, 0, 57.9, 1.271, 0, 58.117, 1.577, 0, 58.65, -1.537, 0, 58.9, -1.271, 0, 59.117, -1.577, 0, 59.65, 1.537, 0, 59.9, 1.271, 0, 60, 1.388]}, {"Target": "Parameter", "Id": "Param155", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.45, 1.809, 0, 0.933, -2.557, 0, 1.783, 1.337, 0, 2.083, 1.029, 0, 2.25, 1.102, 0, 2.817, -1.628, 0, 3.183, -0.987, 0, 3.217, -0.988, 0, 3.817, 1.595, 0, 4.183, 1.002, 2, 4.217, 1.002, 0, 4.817, -1.595, 0, 5.183, -1.001, 0, 5.217, -1.002, 0, 5.817, 1.596, 0, 6.183, 1.001, 0, 6.217, 1.002, 0, 6.817, -1.596, 0, 7.183, -1.001, 0, 7.217, -1.002, 0, 7.817, 1.596, 0, 8.183, 1.001, 0, 8.217, 1.002, 0, 8.817, -1.596, 0, 9.183, -1.001, 0, 9.217, -1.002, 0, 9.817, 1.596, 0, 10.183, 1.001, 0, 10.217, 1.002, 0, 10.817, -1.596, 0, 11.183, -1.001, 0, 11.217, -1.002, 0, 11.817, 1.596, 0, 12.183, 1.001, 0, 12.217, 1.002, 0, 12.817, -1.596, 0, 13.183, -1.001, 0, 13.217, -1.002, 0, 13.817, 1.596, 0, 14.183, 1.001, 0, 14.217, 1.002, 0, 14.817, -1.596, 0, 15.183, -1.001, 0, 15.217, -1.002, 0, 15.817, 1.596, 0, 16.183, 1.001, 0, 16.217, 1.002, 0, 16.817, -1.596, 0, 17.183, -1.001, 0, 17.217, -1.002, 0, 17.817, 1.596, 0, 18.183, 1.001, 0, 18.217, 1.002, 0, 18.817, -1.596, 0, 19.183, -1.001, 0, 19.217, -1.002, 0, 19.817, 1.596, 0, 20, 0, 0, 20.45, 1.809, 0, 20.933, -2.557, 0, 21.783, 1.337, 0, 22.083, 1.029, 0, 22.25, 1.102, 0, 22.817, -1.628, 0, 23.183, -0.987, 0, 23.217, -0.988, 0, 23.817, 1.595, 0, 24.183, 1.002, 2, 24.217, 1.002, 0, 24.817, -1.595, 0, 25.183, -1.001, 0, 25.217, -1.002, 0, 25.817, 1.596, 0, 26.183, 1.001, 0, 26.217, 1.002, 0, 26.817, -1.596, 0, 27.183, -1.001, 0, 27.217, -1.002, 0, 27.817, 1.596, 0, 28.183, 1.001, 0, 28.217, 1.002, 0, 28.817, -1.596, 0, 29.183, -1.001, 0, 29.217, -1.002, 0, 29.817, 1.596, 0, 30.183, 1.001, 0, 30.217, 1.002, 0, 30.817, -1.596, 0, 31.183, -1.001, 0, 31.217, -1.002, 0, 31.817, 1.596, 0, 32.183, 1.001, 0, 32.217, 1.002, 0, 32.817, -1.596, 0, 33.183, -1.001, 0, 33.217, -1.002, 0, 33.817, 1.596, 0, 34.183, 1.001, 0, 34.217, 1.002, 0, 34.817, -1.596, 0, 35.183, -1.001, 0, 35.217, -1.002, 0, 35.817, 1.596, 0, 36.183, 1.001, 0, 36.217, 1.002, 0, 36.817, -1.596, 0, 37.183, -1.001, 0, 37.217, -1.002, 0, 37.817, 1.596, 0, 38.183, 1.001, 0, 38.217, 1.002, 0, 38.817, -1.596, 0, 39.183, -1.001, 0, 39.217, -1.002, 0, 39.817, 1.596, 0, 40, 0, 0, 40.45, 1.809, 0, 40.933, -2.557, 0, 41.783, 1.337, 0, 42.083, 1.029, 0, 42.25, 1.102, 0, 42.817, -1.628, 0, 43.183, -0.987, 0, 43.217, -0.988, 0, 43.817, 1.595, 0, 44.183, 1.002, 2, 44.217, 1.002, 0, 44.817, -1.595, 0, 45.183, -1.001, 0, 45.217, -1.002, 0, 45.817, 1.596, 0, 46.183, 1.001, 0, 46.217, 1.002, 0, 46.817, -1.596, 0, 47.183, -1.001, 0, 47.217, -1.002, 0, 47.817, 1.596, 0, 48.183, 1.001, 0, 48.217, 1.002, 0, 48.817, -1.596, 0, 49.183, -1.001, 0, 49.217, -1.002, 0, 49.817, 1.596, 0, 50.183, 1.001, 0, 50.217, 1.002, 0, 50.817, -1.596, 0, 51.183, -1.001, 0, 51.217, -1.002, 0, 51.817, 1.596, 0, 52.183, 1.001, 0, 52.217, 1.002, 0, 52.817, -1.596, 0, 53.183, -1.001, 0, 53.217, -1.002, 0, 53.817, 1.596, 0, 54.183, 1.001, 0, 54.217, 1.002, 0, 54.817, -1.596, 0, 55.183, -1.001, 0, 55.217, -1.002, 0, 55.817, 1.596, 0, 56.183, 1.001, 0, 56.217, 1.002, 0, 56.817, -1.596, 0, 57.183, -1.001, 0, 57.217, -1.002, 0, 57.817, 1.596, 0, 58.183, 1.001, 0, 58.217, 1.002, 0, 58.817, -1.596, 0, 59.183, -1.001, 0, 59.217, -1.002, 0, 59.817, 1.596, 0, 60, 1.057]}, {"Target": "Parameter", "Id": "Param156", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.033, 0, 0, 0.6, 1.49, 0, 1.1, -2.632, 0, 1.85, 1.217, 0, 2.333, 0.746, 2, 2.35, 0.746, 0, 2.967, -1.613, 0, 3.983, 1.532, 0, 4.983, -1.536, 0, 5.983, 1.536, 0, 6.983, -1.536, 0, 7.983, 1.536, 0, 8.983, -1.536, 0, 9.983, 1.536, 0, 10.983, -1.536, 0, 11.983, 1.536, 0, 12.983, -1.536, 0, 13.983, 1.536, 0, 14.983, -1.536, 0, 15.983, 1.536, 0, 16.983, -1.536, 0, 17.983, 1.536, 0, 18.983, -1.536, 0, 19.983, 1.536, 0, 20, 0, 2, 20.033, 0, 0, 20.6, 1.49, 0, 21.1, -2.632, 0, 21.85, 1.217, 0, 22.333, 0.746, 2, 22.35, 0.746, 0, 22.967, -1.613, 0, 23.983, 1.532, 0, 24.983, -1.536, 0, 25.983, 1.536, 0, 26.983, -1.536, 0, 27.983, 1.536, 0, 28.983, -1.536, 0, 29.983, 1.536, 0, 30.983, -1.536, 0, 31.983, 1.536, 0, 32.983, -1.536, 0, 33.983, 1.536, 0, 34.983, -1.536, 0, 35.983, 1.536, 0, 36.983, -1.536, 0, 37.983, 1.536, 0, 38.983, -1.536, 0, 39.983, 1.536, 0, 40, 0, 2, 40.033, 0, 0, 40.6, 1.49, 0, 41.1, -2.632, 0, 41.85, 1.217, 0, 42.333, 0.746, 2, 42.35, 0.746, 0, 42.967, -1.613, 0, 43.983, 1.532, 0, 44.983, -1.536, 0, 45.983, 1.536, 0, 46.983, -1.536, 0, 47.983, 1.536, 0, 48.983, -1.536, 0, 49.983, 1.536, 0, 50.983, -1.536, 0, 51.983, 1.536, 0, 52.983, -1.536, 0, 53.983, 1.536, 0, 54.983, -1.536, 0, 55.983, 1.536, 0, 56.983, -1.536, 0, 57.983, 1.536, 0, 58.983, -1.536, 0, 59.983, 1.536, 0, 60, 1.531]}, {"Target": "Parameter", "Id": "Param82", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.467, -7.114, 0, 1.383, 6.847, 0, 2.383, -6.893, 0, 3.383, 6.89, 0, 4.383, -6.89, 0, 5.383, 6.89, 0, 6.383, -6.89, 0, 7.383, 6.89, 0, 8.383, -6.89, 0, 9.383, 6.89, 0, 10.383, -6.89, 0, 11.383, 6.89, 0, 12.383, -6.89, 0, 13.383, 6.89, 0, 14.383, -6.89, 0, 15.383, 6.89, 0, 16.383, -6.89, 0, 17.383, 6.89, 0, 18.383, -6.89, 0, 19.383, 6.89, 1, 19.589, 6.89, 19.794, 5.185, 20, 0, 1, 20.156, -3.924, 20.311, -7.114, 20.467, -7.114, 0, 21.383, 6.847, 0, 22.383, -6.893, 0, 23.383, 6.89, 0, 24.383, -6.89, 0, 25.383, 6.89, 0, 26.383, -6.89, 0, 27.383, 6.89, 0, 28.383, -6.89, 0, 29.383, 6.89, 0, 30.383, -6.89, 0, 31.383, 6.89, 0, 32.383, -6.89, 0, 33.383, 6.89, 0, 34.383, -6.89, 0, 35.383, 6.89, 0, 36.383, -6.89, 0, 37.383, 6.89, 0, 38.383, -6.89, 0, 39.383, 6.89, 1, 39.589, 6.89, 39.794, 5.185, 40, 0, 1, 40.156, -3.924, 40.311, -7.114, 40.467, -7.114, 0, 41.383, 6.847, 0, 42.383, -6.893, 0, 43.383, 6.89, 0, 44.383, -6.89, 0, 45.383, 6.89, 0, 46.383, -6.89, 0, 47.383, 6.89, 0, 48.383, -6.89, 0, 49.383, 6.89, 0, 50.383, -6.89, 0, 51.383, 6.89, 0, 52.383, -6.89, 0, 53.383, 6.89, 0, 54.383, -6.89, 0, 55.383, 6.89, 0, 56.383, -6.89, 0, 57.383, 6.89, 0, 58.383, -6.89, 0, 59.383, 6.89, 0, 60, -1.631]}, {"Target": "Parameter", "Id": "Param83", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.283, 2.202, 0, 0.75, -2.26, 0, 1.65, 1.34, 0, 1.85, 1.196, 0, 2.117, 1.554, 0, 2.65, -1.482, 0, 2.9, -1.214, 0, 3.117, -1.508, 0, 3.65, 1.472, 0, 3.9, 1.218, 0, 4.117, 1.51, 0, 4.65, -1.472, 0, 4.9, -1.218, 0, 5.117, -1.511, 0, 5.65, 1.472, 0, 5.9, 1.218, 0, 6.117, 1.511, 0, 6.65, -1.472, 0, 6.9, -1.218, 0, 7.117, -1.511, 0, 7.65, 1.472, 0, 7.9, 1.218, 0, 8.117, 1.511, 0, 8.65, -1.472, 0, 8.9, -1.218, 0, 9.117, -1.511, 0, 9.65, 1.472, 0, 9.9, 1.218, 0, 10.117, 1.511, 0, 10.65, -1.472, 0, 10.9, -1.218, 0, 11.117, -1.511, 0, 11.65, 1.472, 0, 11.9, 1.218, 0, 12.117, 1.511, 0, 12.65, -1.472, 0, 12.9, -1.218, 0, 13.117, -1.511, 0, 13.65, 1.472, 0, 13.9, 1.218, 0, 14.117, 1.511, 0, 14.65, -1.472, 0, 14.9, -1.218, 0, 15.117, -1.511, 0, 15.65, 1.472, 0, 15.9, 1.218, 0, 16.117, 1.511, 0, 16.65, -1.472, 0, 16.9, -1.218, 0, 17.117, -1.511, 0, 17.65, 1.472, 0, 17.9, 1.218, 0, 18.117, 1.511, 0, 18.65, -1.472, 0, 18.9, -1.218, 0, 19.117, -1.511, 0, 19.65, 1.472, 1, 19.733, 1.472, 19.817, 1.447, 19.9, 1.218, 1, 19.933, 1.126, 19.967, 0, 20, 0, 0, 20.283, 2.202, 0, 20.75, -2.26, 0, 21.65, 1.34, 0, 21.85, 1.196, 0, 22.117, 1.554, 0, 22.65, -1.482, 0, 22.9, -1.214, 0, 23.117, -1.508, 0, 23.65, 1.472, 0, 23.9, 1.218, 0, 24.117, 1.51, 0, 24.65, -1.472, 0, 24.9, -1.218, 0, 25.117, -1.511, 0, 25.65, 1.472, 0, 25.9, 1.218, 0, 26.117, 1.511, 0, 26.65, -1.472, 0, 26.9, -1.218, 0, 27.117, -1.511, 0, 27.65, 1.472, 0, 27.9, 1.218, 0, 28.117, 1.511, 0, 28.65, -1.472, 0, 28.9, -1.218, 0, 29.117, -1.511, 0, 29.65, 1.472, 0, 29.9, 1.218, 0, 30.117, 1.511, 0, 30.65, -1.472, 0, 30.9, -1.218, 0, 31.117, -1.511, 0, 31.65, 1.472, 0, 31.9, 1.218, 0, 32.117, 1.511, 0, 32.65, -1.472, 0, 32.9, -1.218, 0, 33.117, -1.511, 0, 33.65, 1.472, 0, 33.9, 1.218, 0, 34.117, 1.511, 0, 34.65, -1.472, 0, 34.9, -1.218, 0, 35.117, -1.511, 0, 35.65, 1.472, 0, 35.9, 1.218, 0, 36.117, 1.511, 0, 36.65, -1.472, 0, 36.9, -1.218, 0, 37.117, -1.511, 0, 37.65, 1.472, 0, 37.9, 1.218, 0, 38.117, 1.511, 0, 38.65, -1.472, 0, 38.9, -1.218, 0, 39.117, -1.511, 0, 39.65, 1.472, 1, 39.733, 1.472, 39.817, 1.447, 39.9, 1.218, 1, 39.933, 1.126, 39.967, 0, 40, 0, 0, 40.283, 2.202, 0, 40.75, -2.26, 0, 41.65, 1.34, 0, 41.85, 1.196, 0, 42.117, 1.554, 0, 42.65, -1.482, 0, 42.9, -1.214, 0, 43.117, -1.508, 0, 43.65, 1.472, 0, 43.9, 1.218, 0, 44.117, 1.51, 0, 44.65, -1.472, 0, 44.9, -1.218, 0, 45.117, -1.511, 0, 45.65, 1.472, 0, 45.9, 1.218, 0, 46.117, 1.511, 0, 46.65, -1.472, 0, 46.9, -1.218, 0, 47.117, -1.511, 0, 47.65, 1.472, 0, 47.9, 1.218, 0, 48.117, 1.511, 0, 48.65, -1.472, 0, 48.9, -1.218, 0, 49.117, -1.511, 0, 49.65, 1.472, 0, 49.9, 1.218, 0, 50.117, 1.511, 0, 50.65, -1.472, 0, 50.9, -1.218, 0, 51.117, -1.511, 0, 51.65, 1.472, 0, 51.9, 1.218, 0, 52.117, 1.511, 0, 52.65, -1.472, 0, 52.9, -1.218, 0, 53.117, -1.511, 0, 53.65, 1.472, 0, 53.9, 1.218, 0, 54.117, 1.511, 0, 54.65, -1.472, 0, 54.9, -1.218, 0, 55.117, -1.511, 0, 55.65, 1.472, 0, 55.9, 1.218, 0, 56.117, 1.511, 0, 56.65, -1.472, 0, 56.9, -1.218, 0, 57.117, -1.511, 0, 57.65, 1.472, 0, 57.9, 1.218, 0, 58.117, 1.511, 0, 58.65, -1.472, 0, 58.9, -1.218, 0, 59.117, -1.511, 0, 59.65, 1.472, 0, 59.9, 1.218, 0, 60, 1.33]}, {"Target": "Parameter", "Id": "Param84", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.45, 1.849, 0, 0.933, -2.614, 0, 1.783, 1.366, 0, 2.083, 1.052, 0, 2.25, 1.127, 0, 2.817, -1.664, 0, 3.183, -1.009, 0, 3.217, -1.01, 0, 3.817, 1.63, 0, 4.183, 1.024, 0, 4.217, 1.025, 0, 4.817, -1.631, 0, 5.183, -1.024, 2, 5.217, -1.024, 0, 5.817, 1.631, 0, 6.183, 1.023, 0, 6.217, 1.024, 0, 6.817, -1.631, 0, 7.183, -1.023, 0, 7.217, -1.024, 0, 7.817, 1.631, 0, 8.183, 1.023, 0, 8.217, 1.024, 0, 8.817, -1.631, 0, 9.183, -1.023, 0, 9.217, -1.024, 0, 9.817, 1.631, 0, 10.183, 1.023, 0, 10.217, 1.024, 0, 10.817, -1.631, 0, 11.183, -1.023, 0, 11.217, -1.024, 0, 11.817, 1.631, 0, 12.183, 1.023, 0, 12.217, 1.024, 0, 12.817, -1.631, 0, 13.183, -1.023, 0, 13.217, -1.024, 0, 13.817, 1.631, 0, 14.183, 1.023, 0, 14.217, 1.024, 0, 14.817, -1.631, 0, 15.183, -1.023, 0, 15.217, -1.024, 0, 15.817, 1.631, 0, 16.183, 1.023, 0, 16.217, 1.024, 0, 16.817, -1.631, 0, 17.183, -1.023, 0, 17.217, -1.024, 0, 17.817, 1.631, 0, 18.183, 1.023, 0, 18.217, 1.024, 0, 18.817, -1.631, 0, 19.183, -1.023, 0, 19.217, -1.024, 0, 19.817, 1.631, 0, 20, 0, 0, 20.45, 1.849, 0, 20.933, -2.614, 0, 21.783, 1.366, 0, 22.083, 1.052, 0, 22.25, 1.127, 0, 22.817, -1.664, 0, 23.183, -1.009, 0, 23.217, -1.01, 0, 23.817, 1.63, 0, 24.183, 1.024, 0, 24.217, 1.025, 0, 24.817, -1.631, 0, 25.183, -1.024, 2, 25.217, -1.024, 0, 25.817, 1.631, 0, 26.183, 1.023, 0, 26.217, 1.024, 0, 26.817, -1.631, 0, 27.183, -1.023, 0, 27.217, -1.024, 0, 27.817, 1.631, 0, 28.183, 1.023, 0, 28.217, 1.024, 0, 28.817, -1.631, 0, 29.183, -1.023, 0, 29.217, -1.024, 0, 29.817, 1.631, 0, 30.183, 1.023, 0, 30.217, 1.024, 0, 30.817, -1.631, 0, 31.183, -1.023, 0, 31.217, -1.024, 0, 31.817, 1.631, 0, 32.183, 1.023, 0, 32.217, 1.024, 0, 32.817, -1.631, 0, 33.183, -1.023, 0, 33.217, -1.024, 0, 33.817, 1.631, 0, 34.183, 1.023, 0, 34.217, 1.024, 0, 34.817, -1.631, 0, 35.183, -1.023, 0, 35.217, -1.024, 0, 35.817, 1.631, 0, 36.183, 1.023, 0, 36.217, 1.024, 0, 36.817, -1.631, 0, 37.183, -1.023, 0, 37.217, -1.024, 0, 37.817, 1.631, 0, 38.183, 1.023, 0, 38.217, 1.024, 0, 38.817, -1.631, 0, 39.183, -1.023, 0, 39.217, -1.024, 0, 39.817, 1.631, 0, 40, 0, 0, 40.45, 1.849, 0, 40.933, -2.614, 0, 41.783, 1.366, 0, 42.083, 1.052, 0, 42.25, 1.127, 0, 42.817, -1.664, 0, 43.183, -1.009, 0, 43.217, -1.01, 0, 43.817, 1.63, 0, 44.183, 1.024, 0, 44.217, 1.025, 0, 44.817, -1.631, 0, 45.183, -1.024, 2, 45.217, -1.024, 0, 45.817, 1.631, 0, 46.183, 1.023, 0, 46.217, 1.024, 0, 46.817, -1.631, 0, 47.183, -1.023, 0, 47.217, -1.024, 0, 47.817, 1.631, 0, 48.183, 1.023, 0, 48.217, 1.024, 0, 48.817, -1.631, 0, 49.183, -1.023, 0, 49.217, -1.024, 0, 49.817, 1.631, 0, 50.183, 1.023, 0, 50.217, 1.024, 0, 50.817, -1.631, 0, 51.183, -1.023, 0, 51.217, -1.024, 0, 51.817, 1.631, 0, 52.183, 1.023, 0, 52.217, 1.024, 0, 52.817, -1.631, 0, 53.183, -1.023, 0, 53.217, -1.024, 0, 53.817, 1.631, 0, 54.183, 1.023, 0, 54.217, 1.024, 0, 54.817, -1.631, 0, 55.183, -1.023, 0, 55.217, -1.024, 0, 55.817, 1.631, 0, 56.183, 1.023, 0, 56.217, 1.024, 0, 56.817, -1.631, 0, 57.183, -1.023, 0, 57.217, -1.024, 0, 57.817, 1.631, 0, 58.183, 1.023, 0, 58.217, 1.024, 0, 58.817, -1.631, 0, 59.183, -1.023, 0, 59.217, -1.024, 0, 59.817, 1.631, 0, 60, 1.08]}, {"Target": "Parameter", "Id": "Param85", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.033, 0, 0, 0.6, 1.519, 0, 1.1, -2.683, 0, 1.85, 1.24, 0, 2.333, 0.76, 2, 2.35, 0.76, 0, 2.967, -1.645, 0, 3.983, 1.562, 0, 4.983, -1.566, 0, 5.983, 1.566, 0, 6.983, -1.566, 0, 7.983, 1.566, 0, 8.983, -1.566, 0, 9.983, 1.566, 0, 10.983, -1.566, 0, 11.983, 1.566, 0, 12.983, -1.566, 0, 13.983, 1.566, 0, 14.983, -1.566, 0, 15.983, 1.566, 0, 16.983, -1.566, 0, 17.983, 1.566, 0, 18.983, -1.566, 0, 19.983, 1.566, 0, 20, 0, 2, 20.033, 0, 0, 20.6, 1.519, 0, 21.1, -2.683, 0, 21.85, 1.24, 0, 22.333, 0.76, 2, 22.35, 0.76, 0, 22.967, -1.645, 0, 23.983, 1.562, 0, 24.983, -1.566, 0, 25.983, 1.566, 0, 26.983, -1.566, 0, 27.983, 1.566, 0, 28.983, -1.566, 0, 29.983, 1.566, 0, 30.983, -1.566, 0, 31.983, 1.566, 0, 32.983, -1.566, 0, 33.983, 1.566, 0, 34.983, -1.566, 0, 35.983, 1.566, 0, 36.983, -1.566, 0, 37.983, 1.566, 0, 38.983, -1.566, 0, 39.983, 1.566, 0, 40, 0, 2, 40.033, 0, 0, 40.6, 1.519, 0, 41.1, -2.683, 0, 41.85, 1.24, 0, 42.333, 0.76, 2, 42.35, 0.76, 0, 42.967, -1.645, 0, 43.983, 1.562, 0, 44.983, -1.566, 0, 45.983, 1.566, 0, 46.983, -1.566, 0, 47.983, 1.566, 0, 48.983, -1.566, 0, 49.983, 1.566, 0, 50.983, -1.566, 0, 51.983, 1.566, 0, 52.983, -1.566, 0, 53.983, 1.566, 0, 54.983, -1.566, 0, 55.983, 1.566, 0, 56.983, -1.566, 0, 57.983, 1.566, 0, 58.983, -1.566, 0, 59.983, 1.566, 0, 60, 1.561]}, {"Target": "Parameter", "Id": "Param86", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 4.375, 0, 1, -4.375, 0, 2, 4.375, 0, 3, -4.375, 0, 4, 4.375, 0, 5, -4.375, 0, 6, 4.375, 0, 7, -4.375, 0, 8, 4.375, 0, 9, -4.375, 0, 10, 4.375, 0, 11, -4.375, 0, 12, 4.375, 0, 13, -4.375, 0, 14, 4.375, 0, 15, -4.375, 0, 16, 4.375, 0, 17, -4.375, 0, 18, 4.375, 0, 19, -4.375, 0, 20, 4.375, 0, 21, -4.375, 0, 22, 4.375, 0, 23, -4.375, 0, 24, 4.375, 0, 25, -4.375, 0, 26, 4.375, 0, 27, -4.375, 0, 28, 4.375, 0, 29, -4.375, 0, 30, 4.375, 0, 31, -4.375, 0, 32, 4.375, 0, 33, -4.375, 0, 34, 4.375, 0, 35, -4.375, 0, 36, 4.375, 0, 37, -4.375, 0, 38, 4.375, 0, 39, -4.375, 0, 40, 4.375, 0, 41, -4.375, 0, 42, 4.375, 0, 43, -4.375, 0, 44, 4.375, 0, 45, -4.375, 0, 46, 4.375, 0, 47, -4.375, 0, 48, 4.375, 0, 49, -4.375, 0, 50, 4.375, 0, 51, -4.375, 0, 52, 4.375, 0, 53, -4.375, 0, 54, 4.375, 0, 55, -4.375, 0, 56, 4.375, 0, 57, -4.375, 0, 58, 4.375, 0, 59, -4.375, 0, 60, 4.375]}, {"Target": "Parameter", "Id": "Param89", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.417, -9.075, 0, 1.367, 8.515, 0, 2.367, -8.517, 0, 3.367, 8.517, 0, 4.367, -8.517, 0, 5.367, 8.517, 0, 6.367, -8.517, 0, 7.367, 8.517, 0, 8.367, -8.517, 0, 9.367, 8.517, 0, 10.367, -8.517, 0, 11.367, 8.517, 0, 12.367, -8.517, 0, 13.367, 8.517, 0, 14.367, -8.517, 0, 15.367, 8.517, 0, 16.367, -8.517, 0, 17.367, 8.517, 0, 18.367, -8.517, 0, 19.367, 8.517, 1, 19.578, 8.517, 19.789, 6.67, 20, 0, 1, 20.139, -4.388, 20.278, -9.075, 20.417, -9.075, 0, 21.367, 8.515, 0, 22.367, -8.517, 0, 23.367, 8.517, 0, 24.367, -8.517, 0, 25.367, 8.517, 0, 26.367, -8.517, 0, 27.367, 8.517, 0, 28.367, -8.517, 0, 29.367, 8.517, 0, 30.367, -8.517, 0, 31.367, 8.517, 0, 32.367, -8.517, 0, 33.367, 8.517, 0, 34.367, -8.517, 0, 35.367, 8.517, 0, 36.367, -8.517, 0, 37.367, 8.517, 0, 38.367, -8.517, 0, 39.367, 8.517, 1, 39.578, 8.517, 39.789, 6.67, 40, 0, 1, 40.139, -4.388, 40.278, -9.075, 40.417, -9.075, 0, 41.367, 8.515, 0, 42.367, -8.517, 0, 43.367, 8.517, 0, 44.367, -8.517, 0, 45.367, 8.517, 0, 46.367, -8.517, 0, 47.367, 8.517, 0, 48.367, -8.517, 0, 49.367, 8.517, 0, 50.367, -8.517, 0, 51.367, 8.517, 0, 52.367, -8.517, 0, 53.367, 8.517, 0, 54.367, -8.517, 0, 55.367, 8.517, 0, 56.367, -8.517, 0, 57.367, 8.517, 0, 58.367, -8.517, 0, 59.367, 8.517, 0, 60, -2.108]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.233, 3.249, 0, 0.617, -2.325, 0, 0.833, -0.683, 0, 1.1, -2.351, 0, 1.533, 1.359, 0, 1.717, 1.065, 0, 2.083, 2.393, 0, 2.517, -1.324, 0, 2.717, -1.028, 0, 3.083, -2.403, 0, 3.517, 1.325, 0, 3.717, 1.027, 0, 4.083, 2.404, 0, 4.517, -1.325, 0, 4.717, -1.027, 0, 5.083, -2.404, 0, 5.517, 1.325, 0, 5.717, 1.027, 0, 6.083, 2.404, 0, 6.517, -1.325, 0, 6.717, -1.027, 0, 7.083, -2.404, 0, 7.517, 1.325, 0, 7.717, 1.027, 0, 8.083, 2.404, 0, 8.517, -1.325, 0, 8.717, -1.027, 0, 9.083, -2.404, 0, 9.517, 1.325, 0, 9.717, 1.027, 0, 10.083, 2.404, 0, 10.517, -1.325, 0, 10.717, -1.027, 0, 11.083, -2.404, 0, 11.517, 1.325, 0, 11.717, 1.027, 0, 12.083, 2.404, 0, 12.517, -1.325, 0, 12.717, -1.027, 0, 13.083, -2.404, 0, 13.517, 1.325, 0, 13.717, 1.027, 0, 14.083, 2.404, 0, 14.517, -1.325, 0, 14.717, -1.027, 0, 15.083, -2.404, 0, 15.517, 1.325, 0, 15.717, 1.027, 0, 16.083, 2.404, 0, 16.517, -1.325, 0, 16.717, -1.027, 0, 17.083, -2.404, 0, 17.517, 1.325, 0, 17.717, 1.027, 0, 18.083, 2.404, 0, 18.517, -1.325, 0, 18.717, -1.027, 0, 19.083, -2.404, 0, 19.517, 1.325, 1, 19.584, 1.325, 19.65, 1.33, 19.717, 1.027, 1, 19.811, 0.597, 19.906, 0, 20, 0, 0, 20.233, 3.249, 0, 20.617, -2.325, 0, 20.833, -0.683, 0, 21.1, -2.351, 0, 21.533, 1.359, 0, 21.717, 1.065, 0, 22.083, 2.393, 0, 22.517, -1.324, 0, 22.717, -1.028, 0, 23.083, -2.403, 0, 23.517, 1.325, 0, 23.717, 1.027, 0, 24.083, 2.404, 0, 24.517, -1.325, 0, 24.717, -1.027, 0, 25.083, -2.404, 0, 25.517, 1.325, 0, 25.717, 1.027, 0, 26.083, 2.404, 0, 26.517, -1.325, 0, 26.717, -1.027, 0, 27.083, -2.404, 0, 27.517, 1.325, 0, 27.717, 1.027, 0, 28.083, 2.404, 0, 28.517, -1.325, 0, 28.717, -1.027, 0, 29.083, -2.404, 0, 29.517, 1.325, 0, 29.717, 1.027, 0, 30.083, 2.404, 0, 30.517, -1.325, 0, 30.717, -1.027, 0, 31.083, -2.404, 0, 31.517, 1.325, 0, 31.717, 1.027, 0, 32.083, 2.404, 0, 32.517, -1.325, 0, 32.717, -1.027, 0, 33.083, -2.404, 0, 33.517, 1.325, 0, 33.717, 1.027, 0, 34.083, 2.404, 0, 34.517, -1.325, 0, 34.717, -1.027, 0, 35.083, -2.404, 0, 35.517, 1.325, 0, 35.717, 1.027, 0, 36.083, 2.404, 0, 36.517, -1.325, 0, 36.717, -1.027, 0, 37.083, -2.404, 0, 37.517, 1.325, 0, 37.717, 1.027, 0, 38.083, 2.404, 0, 38.517, -1.325, 0, 38.717, -1.027, 0, 39.083, -2.404, 0, 39.517, 1.325, 1, 39.584, 1.325, 39.65, 1.33, 39.717, 1.027, 1, 39.811, 0.597, 39.906, 0, 40, 0, 0, 40.233, 3.249, 0, 40.617, -2.325, 0, 40.833, -0.683, 0, 41.1, -2.351, 0, 41.533, 1.359, 0, 41.717, 1.065, 0, 42.083, 2.393, 0, 42.517, -1.324, 0, 42.717, -1.028, 0, 43.083, -2.403, 0, 43.517, 1.325, 0, 43.717, 1.027, 0, 44.083, 2.404, 0, 44.517, -1.325, 0, 44.717, -1.027, 0, 45.083, -2.404, 0, 45.517, 1.325, 0, 45.717, 1.027, 0, 46.083, 2.404, 0, 46.517, -1.325, 0, 46.717, -1.027, 0, 47.083, -2.404, 0, 47.517, 1.325, 0, 47.717, 1.027, 0, 48.083, 2.404, 0, 48.517, -1.325, 0, 48.717, -1.027, 0, 49.083, -2.404, 0, 49.517, 1.325, 0, 49.717, 1.027, 0, 50.083, 2.404, 0, 50.517, -1.325, 0, 50.717, -1.027, 0, 51.083, -2.404, 0, 51.517, 1.325, 0, 51.717, 1.027, 0, 52.083, 2.404, 0, 52.517, -1.325, 0, 52.717, -1.027, 0, 53.083, -2.404, 0, 53.517, 1.325, 0, 53.717, 1.027, 0, 54.083, 2.404, 0, 54.517, -1.325, 0, 54.717, -1.027, 0, 55.083, -2.404, 0, 55.517, 1.325, 0, 55.717, 1.027, 0, 56.083, 2.404, 0, 56.517, -1.325, 0, 56.717, -1.027, 0, 57.083, -2.404, 0, 57.517, 1.325, 0, 57.717, 1.027, 0, 58.083, 2.404, 0, 58.517, -1.325, 0, 58.717, -1.027, 0, 59.083, -2.404, 0, 59.517, 1.325, 0, 59.717, 1.027, 0, 60, 2.153]}, {"Target": "Parameter", "Id": "Param88", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.35, -2.541, 0, 0.717, 3.794, 0, 1, -0.142, 0, 1.25, 1.883, 0, 1.617, -1.548, 0, 1.867, -0.645, 0, 2.183, -1.849, 0, 2.583, 1.426, 2, 2.6, 1.426, 0, 2.85, 0.636, 0, 3.183, 1.857, 0, 3.583, -1.425, 0, 3.85, -0.635, 0, 4.183, -1.857, 0, 4.583, 1.425, 0, 4.85, 0.635, 0, 5.183, 1.857, 0, 5.583, -1.425, 0, 5.85, -0.635, 0, 6.183, -1.857, 0, 6.583, 1.425, 0, 6.85, 0.635, 0, 7.183, 1.857, 0, 7.583, -1.425, 0, 7.85, -0.635, 0, 8.183, -1.857, 0, 8.583, 1.425, 0, 8.85, 0.635, 0, 9.183, 1.857, 0, 9.583, -1.425, 0, 9.85, -0.635, 0, 10.183, -1.857, 0, 10.583, 1.425, 0, 10.85, 0.635, 0, 11.183, 1.857, 0, 11.583, -1.425, 0, 11.85, -0.635, 0, 12.183, -1.857, 0, 12.583, 1.425, 0, 12.85, 0.635, 0, 13.183, 1.857, 0, 13.583, -1.425, 0, 13.85, -0.635, 0, 14.183, -1.857, 0, 14.583, 1.425, 0, 14.85, 0.635, 0, 15.183, 1.857, 0, 15.583, -1.425, 0, 15.85, -0.635, 0, 16.183, -1.857, 0, 16.583, 1.425, 0, 16.85, 0.635, 0, 17.183, 1.857, 0, 17.583, -1.425, 0, 17.85, -0.635, 0, 18.183, -1.857, 0, 18.583, 1.425, 0, 18.85, 0.635, 0, 19.183, 1.857, 0, 19.583, -1.425, 1, 19.672, -1.425, 19.761, -1.225, 19.85, -0.635, 1, 19.9, -0.303, 19.95, 0, 20, 0, 0, 20.35, -2.541, 0, 20.717, 3.794, 0, 21, -0.142, 0, 21.25, 1.883, 0, 21.617, -1.548, 0, 21.867, -0.645, 0, 22.183, -1.849, 0, 22.583, 1.426, 2, 22.6, 1.426, 0, 22.85, 0.636, 0, 23.183, 1.857, 0, 23.583, -1.425, 0, 23.85, -0.635, 0, 24.183, -1.857, 0, 24.583, 1.425, 0, 24.85, 0.635, 0, 25.183, 1.857, 0, 25.583, -1.425, 0, 25.85, -0.635, 0, 26.183, -1.857, 0, 26.583, 1.425, 0, 26.85, 0.635, 0, 27.183, 1.857, 0, 27.583, -1.425, 0, 27.85, -0.635, 0, 28.183, -1.857, 0, 28.583, 1.425, 0, 28.85, 0.635, 0, 29.183, 1.857, 0, 29.583, -1.425, 0, 29.85, -0.635, 0, 30.183, -1.857, 0, 30.583, 1.425, 0, 30.85, 0.635, 0, 31.183, 1.857, 0, 31.583, -1.425, 0, 31.85, -0.635, 0, 32.183, -1.857, 0, 32.583, 1.425, 0, 32.85, 0.635, 0, 33.183, 1.857, 0, 33.583, -1.425, 0, 33.85, -0.635, 0, 34.183, -1.857, 0, 34.583, 1.425, 0, 34.85, 0.635, 0, 35.183, 1.857, 0, 35.583, -1.425, 0, 35.85, -0.635, 0, 36.183, -1.857, 0, 36.583, 1.425, 0, 36.85, 0.635, 0, 37.183, 1.857, 0, 37.583, -1.425, 0, 37.85, -0.635, 0, 38.183, -1.857, 0, 38.583, 1.425, 0, 38.85, 0.635, 0, 39.183, 1.857, 0, 39.583, -1.425, 1, 39.672, -1.425, 39.761, -1.225, 39.85, -0.635, 1, 39.9, -0.303, 39.95, 0, 40, 0, 0, 40.35, -2.541, 0, 40.717, 3.794, 0, 41, -0.142, 0, 41.25, 1.883, 0, 41.617, -1.548, 0, 41.867, -0.645, 0, 42.183, -1.849, 0, 42.583, 1.426, 2, 42.6, 1.426, 0, 42.85, 0.636, 0, 43.183, 1.857, 0, 43.583, -1.425, 0, 43.85, -0.635, 0, 44.183, -1.857, 0, 44.583, 1.425, 0, 44.85, 0.635, 0, 45.183, 1.857, 0, 45.583, -1.425, 0, 45.85, -0.635, 0, 46.183, -1.857, 0, 46.583, 1.425, 0, 46.85, 0.635, 0, 47.183, 1.857, 0, 47.583, -1.425, 0, 47.85, -0.635, 0, 48.183, -1.857, 0, 48.583, 1.425, 0, 48.85, 0.635, 0, 49.183, 1.857, 0, 49.583, -1.425, 0, 49.85, -0.635, 0, 50.183, -1.857, 0, 50.583, 1.425, 0, 50.85, 0.635, 0, 51.183, 1.857, 0, 51.583, -1.425, 0, 51.85, -0.635, 0, 52.183, -1.857, 0, 52.583, 1.425, 0, 52.85, 0.635, 0, 53.183, 1.857, 0, 53.583, -1.425, 0, 53.85, -0.635, 0, 54.183, -1.857, 0, 54.583, 1.425, 0, 54.85, 0.635, 0, 55.183, 1.857, 0, 55.583, -1.425, 0, 55.85, -0.635, 0, 56.183, -1.857, 0, 56.583, 1.425, 0, 56.85, 0.635, 0, 57.183, 1.857, 0, 57.583, -1.425, 0, 57.85, -0.635, 0, 58.183, -1.857, 0, 58.583, 1.425, 0, 58.85, 0.635, 0, 59.183, 1.857, 0, 59.583, -1.425, 0, 59.85, -0.635, 0, 60, -1.186]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.017, 0, 0, 0.45, 2.01, 0, 0.85, -2.914, 0, 1.133, 0.628, 0, 1.383, -1.583, 0, 1.717, 1.695, 0, 2, 0.233, 0, 2.3, 1.5, 0, 2.683, -1.48, 0, 2.967, -0.258, 0, 3.283, -1.51, 0, 3.683, 1.467, 0, 3.967, 0.26, 0, 4.283, 1.51, 0, 4.683, -1.467, 0, 4.967, -0.26, 0, 5.283, -1.51, 0, 5.683, 1.467, 0, 5.967, 0.26, 0, 6.283, 1.51, 0, 6.683, -1.467, 0, 6.967, -0.26, 0, 7.283, -1.51, 0, 7.683, 1.467, 0, 7.967, 0.26, 0, 8.283, 1.51, 0, 8.683, -1.467, 0, 8.967, -0.26, 0, 9.283, -1.51, 0, 9.683, 1.467, 0, 9.967, 0.26, 0, 10.283, 1.51, 0, 10.683, -1.467, 0, 10.967, -0.26, 0, 11.283, -1.51, 0, 11.683, 1.467, 0, 11.967, 0.26, 0, 12.283, 1.51, 0, 12.683, -1.467, 0, 12.967, -0.26, 0, 13.283, -1.51, 0, 13.683, 1.467, 0, 13.967, 0.26, 0, 14.283, 1.51, 0, 14.683, -1.467, 0, 14.967, -0.26, 0, 15.283, -1.51, 0, 15.683, 1.467, 0, 15.967, 0.26, 0, 16.283, 1.51, 0, 16.683, -1.467, 0, 16.967, -0.26, 0, 17.283, -1.51, 0, 17.683, 1.467, 0, 17.967, 0.26, 0, 18.283, 1.51, 0, 18.683, -1.467, 0, 18.967, -0.26, 0, 19.283, -1.51, 0, 19.683, 1.467, 1, 19.778, 1.467, 19.872, 1.105, 19.967, 0.26, 1, 19.978, 0.161, 19.989, 0, 20, 0, 2, 20.017, 0, 0, 20.45, 2.01, 0, 20.85, -2.914, 0, 21.133, 0.628, 0, 21.383, -1.583, 0, 21.717, 1.695, 0, 22, 0.233, 0, 22.3, 1.5, 0, 22.683, -1.48, 0, 22.967, -0.258, 0, 23.283, -1.51, 0, 23.683, 1.467, 0, 23.967, 0.26, 0, 24.283, 1.51, 0, 24.683, -1.467, 0, 24.967, -0.26, 0, 25.283, -1.51, 0, 25.683, 1.467, 0, 25.967, 0.26, 0, 26.283, 1.51, 0, 26.683, -1.467, 0, 26.967, -0.26, 0, 27.283, -1.51, 0, 27.683, 1.467, 0, 27.967, 0.26, 0, 28.283, 1.51, 0, 28.683, -1.467, 0, 28.967, -0.26, 0, 29.283, -1.51, 0, 29.683, 1.467, 0, 29.967, 0.26, 0, 30.283, 1.51, 0, 30.683, -1.467, 0, 30.967, -0.26, 0, 31.283, -1.51, 0, 31.683, 1.467, 0, 31.967, 0.26, 0, 32.283, 1.51, 0, 32.683, -1.467, 0, 32.967, -0.26, 0, 33.283, -1.51, 0, 33.683, 1.467, 0, 33.967, 0.26, 0, 34.283, 1.51, 0, 34.683, -1.467, 0, 34.967, -0.26, 0, 35.283, -1.51, 0, 35.683, 1.467, 0, 35.967, 0.26, 0, 36.283, 1.51, 0, 36.683, -1.467, 0, 36.967, -0.26, 0, 37.283, -1.51, 0, 37.683, 1.467, 0, 37.967, 0.26, 0, 38.283, 1.51, 0, 38.683, -1.467, 0, 38.967, -0.26, 0, 39.283, -1.51, 0, 39.683, 1.467, 1, 39.778, 1.467, 39.872, 1.105, 39.967, 0.26, 1, 39.978, 0.161, 39.989, 0, 40, 0, 2, 40.017, 0, 0, 40.45, 2.01, 0, 40.85, -2.914, 0, 41.133, 0.628, 0, 41.383, -1.583, 0, 41.717, 1.695, 0, 42, 0.233, 0, 42.3, 1.5, 0, 42.683, -1.48, 0, 42.967, -0.258, 0, 43.283, -1.51, 0, 43.683, 1.467, 0, 43.967, 0.26, 0, 44.283, 1.51, 0, 44.683, -1.467, 0, 44.967, -0.26, 0, 45.283, -1.51, 0, 45.683, 1.467, 0, 45.967, 0.26, 0, 46.283, 1.51, 0, 46.683, -1.467, 0, 46.967, -0.26, 0, 47.283, -1.51, 0, 47.683, 1.467, 0, 47.967, 0.26, 0, 48.283, 1.51, 0, 48.683, -1.467, 0, 48.967, -0.26, 0, 49.283, -1.51, 0, 49.683, 1.467, 0, 49.967, 0.26, 0, 50.283, 1.51, 0, 50.683, -1.467, 0, 50.967, -0.26, 0, 51.283, -1.51, 0, 51.683, 1.467, 0, 51.967, 0.26, 0, 52.283, 1.51, 0, 52.683, -1.467, 0, 52.967, -0.26, 0, 53.283, -1.51, 0, 53.683, 1.467, 0, 53.967, 0.26, 0, 54.283, 1.51, 0, 54.683, -1.467, 0, 54.967, -0.26, 0, 55.283, -1.51, 0, 55.683, 1.467, 0, 55.967, 0.26, 0, 56.283, 1.51, 0, 56.683, -1.467, 0, 56.967, -0.26, 0, 57.283, -1.51, 0, 57.683, 1.467, 0, 57.967, 0.26, 0, 58.283, 1.51, 0, 58.683, -1.467, 0, 58.967, -0.26, 0, 59.283, -1.51, 0, 59.683, 1.467, 0, 59.967, 0.26, 0, 60, 0.304]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -4.375, 0, 1, 4.375, 0, 2, -4.375, 0, 3, 4.375, 0, 4, -4.375, 0, 5, 4.375, 0, 6, -4.375, 0, 7, 4.375, 0, 8, -4.375, 0, 9, 4.375, 0, 10, -4.375, 0, 11, 4.375, 0, 12, -4.375, 0, 13, 4.375, 0, 14, -4.375, 0, 15, 4.375, 0, 16, -4.375, 0, 17, 4.375, 0, 18, -4.375, 0, 19, 4.375, 0, 20, -4.375, 0, 21, 4.375, 0, 22, -4.375, 0, 23, 4.375, 0, 24, -4.375, 0, 25, 4.375, 0, 26, -4.375, 0, 27, 4.375, 0, 28, -4.375, 0, 29, 4.375, 0, 30, -4.375, 0, 31, 4.375, 0, 32, -4.375, 0, 33, 4.375, 0, 34, -4.375, 0, 35, 4.375, 0, 36, -4.375, 0, 37, 4.375, 0, 38, -4.375, 0, 39, 4.375, 0, 40, -4.375, 0, 41, 4.375, 0, 42, -4.375, 0, 43, 4.375, 0, 44, -4.375, 0, 45, 4.375, 0, 46, -4.375, 0, 47, 4.375, 0, 48, -4.375, 0, 49, 4.375, 0, 50, -4.375, 0, 51, 4.375, 0, 52, -4.375, 0, 53, 4.375, 0, 54, -4.375, 0, 55, 4.375, 0, 56, -4.375, 0, 57, 4.375, 0, 58, -4.375, 0, 59, 4.375, 0, 60, -4.375]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.417, -9.075, 0, 1.367, 8.515, 0, 2.367, -8.517, 0, 3.367, 8.517, 0, 4.367, -8.517, 0, 5.367, 8.517, 0, 6.367, -8.517, 0, 7.367, 8.517, 0, 8.367, -8.517, 0, 9.367, 8.517, 0, 10.367, -8.517, 0, 11.367, 8.517, 0, 12.367, -8.517, 0, 13.367, 8.517, 0, 14.367, -8.517, 0, 15.367, 8.517, 0, 16.367, -8.517, 0, 17.367, 8.517, 0, 18.367, -8.517, 0, 19.367, 8.517, 1, 19.578, 8.517, 19.789, 6.67, 20, 0, 1, 20.139, -4.388, 20.278, -9.075, 20.417, -9.075, 0, 21.367, 8.515, 0, 22.367, -8.517, 0, 23.367, 8.517, 0, 24.367, -8.517, 0, 25.367, 8.517, 0, 26.367, -8.517, 0, 27.367, 8.517, 0, 28.367, -8.517, 0, 29.367, 8.517, 0, 30.367, -8.517, 0, 31.367, 8.517, 0, 32.367, -8.517, 0, 33.367, 8.517, 0, 34.367, -8.517, 0, 35.367, 8.517, 0, 36.367, -8.517, 0, 37.367, 8.517, 0, 38.367, -8.517, 0, 39.367, 8.517, 1, 39.578, 8.517, 39.789, 6.67, 40, 0, 1, 40.139, -4.388, 40.278, -9.075, 40.417, -9.075, 0, 41.367, 8.515, 0, 42.367, -8.517, 0, 43.367, 8.517, 0, 44.367, -8.517, 0, 45.367, 8.517, 0, 46.367, -8.517, 0, 47.367, 8.517, 0, 48.367, -8.517, 0, 49.367, 8.517, 0, 50.367, -8.517, 0, 51.367, 8.517, 0, 52.367, -8.517, 0, 53.367, 8.517, 0, 54.367, -8.517, 0, 55.367, 8.517, 0, 56.367, -8.517, 0, 57.367, 8.517, 0, 58.367, -8.517, 0, 59.367, 8.517, 0, 60, -2.108]}, {"Target": "Parameter", "Id": "Param126", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.233, 3.249, 0, 0.617, -2.325, 0, 0.833, -0.683, 0, 1.1, -2.351, 0, 1.533, 1.359, 0, 1.717, 1.065, 0, 2.083, 2.393, 0, 2.517, -1.324, 0, 2.717, -1.028, 0, 3.083, -2.403, 0, 3.517, 1.325, 0, 3.717, 1.027, 0, 4.083, 2.404, 0, 4.517, -1.325, 0, 4.717, -1.027, 0, 5.083, -2.404, 0, 5.517, 1.325, 0, 5.717, 1.027, 0, 6.083, 2.404, 0, 6.517, -1.325, 0, 6.717, -1.027, 0, 7.083, -2.404, 0, 7.517, 1.325, 0, 7.717, 1.027, 0, 8.083, 2.404, 0, 8.517, -1.325, 0, 8.717, -1.027, 0, 9.083, -2.404, 0, 9.517, 1.325, 0, 9.717, 1.027, 0, 10.083, 2.404, 0, 10.517, -1.325, 0, 10.717, -1.027, 0, 11.083, -2.404, 0, 11.517, 1.325, 0, 11.717, 1.027, 0, 12.083, 2.404, 0, 12.517, -1.325, 0, 12.717, -1.027, 0, 13.083, -2.404, 0, 13.517, 1.325, 0, 13.717, 1.027, 0, 14.083, 2.404, 0, 14.517, -1.325, 0, 14.717, -1.027, 0, 15.083, -2.404, 0, 15.517, 1.325, 0, 15.717, 1.027, 0, 16.083, 2.404, 0, 16.517, -1.325, 0, 16.717, -1.027, 0, 17.083, -2.404, 0, 17.517, 1.325, 0, 17.717, 1.027, 0, 18.083, 2.404, 0, 18.517, -1.325, 0, 18.717, -1.027, 0, 19.083, -2.404, 0, 19.517, 1.325, 1, 19.584, 1.325, 19.65, 1.33, 19.717, 1.027, 1, 19.811, 0.597, 19.906, 0, 20, 0, 0, 20.233, 3.249, 0, 20.617, -2.325, 0, 20.833, -0.683, 0, 21.1, -2.351, 0, 21.533, 1.359, 0, 21.717, 1.065, 0, 22.083, 2.393, 0, 22.517, -1.324, 0, 22.717, -1.028, 0, 23.083, -2.403, 0, 23.517, 1.325, 0, 23.717, 1.027, 0, 24.083, 2.404, 0, 24.517, -1.325, 0, 24.717, -1.027, 0, 25.083, -2.404, 0, 25.517, 1.325, 0, 25.717, 1.027, 0, 26.083, 2.404, 0, 26.517, -1.325, 0, 26.717, -1.027, 0, 27.083, -2.404, 0, 27.517, 1.325, 0, 27.717, 1.027, 0, 28.083, 2.404, 0, 28.517, -1.325, 0, 28.717, -1.027, 0, 29.083, -2.404, 0, 29.517, 1.325, 0, 29.717, 1.027, 0, 30.083, 2.404, 0, 30.517, -1.325, 0, 30.717, -1.027, 0, 31.083, -2.404, 0, 31.517, 1.325, 0, 31.717, 1.027, 0, 32.083, 2.404, 0, 32.517, -1.325, 0, 32.717, -1.027, 0, 33.083, -2.404, 0, 33.517, 1.325, 0, 33.717, 1.027, 0, 34.083, 2.404, 0, 34.517, -1.325, 0, 34.717, -1.027, 0, 35.083, -2.404, 0, 35.517, 1.325, 0, 35.717, 1.027, 0, 36.083, 2.404, 0, 36.517, -1.325, 0, 36.717, -1.027, 0, 37.083, -2.404, 0, 37.517, 1.325, 0, 37.717, 1.027, 0, 38.083, 2.404, 0, 38.517, -1.325, 0, 38.717, -1.027, 0, 39.083, -2.404, 0, 39.517, 1.325, 1, 39.584, 1.325, 39.65, 1.33, 39.717, 1.027, 1, 39.811, 0.597, 39.906, 0, 40, 0, 0, 40.233, 3.249, 0, 40.617, -2.325, 0, 40.833, -0.683, 0, 41.1, -2.351, 0, 41.533, 1.359, 0, 41.717, 1.065, 0, 42.083, 2.393, 0, 42.517, -1.324, 0, 42.717, -1.028, 0, 43.083, -2.403, 0, 43.517, 1.325, 0, 43.717, 1.027, 0, 44.083, 2.404, 0, 44.517, -1.325, 0, 44.717, -1.027, 0, 45.083, -2.404, 0, 45.517, 1.325, 0, 45.717, 1.027, 0, 46.083, 2.404, 0, 46.517, -1.325, 0, 46.717, -1.027, 0, 47.083, -2.404, 0, 47.517, 1.325, 0, 47.717, 1.027, 0, 48.083, 2.404, 0, 48.517, -1.325, 0, 48.717, -1.027, 0, 49.083, -2.404, 0, 49.517, 1.325, 0, 49.717, 1.027, 0, 50.083, 2.404, 0, 50.517, -1.325, 0, 50.717, -1.027, 0, 51.083, -2.404, 0, 51.517, 1.325, 0, 51.717, 1.027, 0, 52.083, 2.404, 0, 52.517, -1.325, 0, 52.717, -1.027, 0, 53.083, -2.404, 0, 53.517, 1.325, 0, 53.717, 1.027, 0, 54.083, 2.404, 0, 54.517, -1.325, 0, 54.717, -1.027, 0, 55.083, -2.404, 0, 55.517, 1.325, 0, 55.717, 1.027, 0, 56.083, 2.404, 0, 56.517, -1.325, 0, 56.717, -1.027, 0, 57.083, -2.404, 0, 57.517, 1.325, 0, 57.717, 1.027, 0, 58.083, 2.404, 0, 58.517, -1.325, 0, 58.717, -1.027, 0, 59.083, -2.404, 0, 59.517, 1.325, 0, 59.717, 1.027, 0, 60, 2.153]}, {"Target": "Parameter", "Id": "Param128", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.35, -2.541, 0, 0.717, 3.794, 0, 1, -0.142, 0, 1.25, 1.883, 0, 1.617, -1.548, 0, 1.867, -0.645, 0, 2.183, -1.849, 0, 2.583, 1.426, 2, 2.6, 1.426, 0, 2.85, 0.636, 0, 3.183, 1.857, 0, 3.583, -1.425, 0, 3.85, -0.635, 0, 4.183, -1.857, 0, 4.583, 1.425, 0, 4.85, 0.635, 0, 5.183, 1.857, 0, 5.583, -1.425, 0, 5.85, -0.635, 0, 6.183, -1.857, 0, 6.583, 1.425, 0, 6.85, 0.635, 0, 7.183, 1.857, 0, 7.583, -1.425, 0, 7.85, -0.635, 0, 8.183, -1.857, 0, 8.583, 1.425, 0, 8.85, 0.635, 0, 9.183, 1.857, 0, 9.583, -1.425, 0, 9.85, -0.635, 0, 10.183, -1.857, 0, 10.583, 1.425, 0, 10.85, 0.635, 0, 11.183, 1.857, 0, 11.583, -1.425, 0, 11.85, -0.635, 0, 12.183, -1.857, 0, 12.583, 1.425, 0, 12.85, 0.635, 0, 13.183, 1.857, 0, 13.583, -1.425, 0, 13.85, -0.635, 0, 14.183, -1.857, 0, 14.583, 1.425, 0, 14.85, 0.635, 0, 15.183, 1.857, 0, 15.583, -1.425, 0, 15.85, -0.635, 0, 16.183, -1.857, 0, 16.583, 1.425, 0, 16.85, 0.635, 0, 17.183, 1.857, 0, 17.583, -1.425, 0, 17.85, -0.635, 0, 18.183, -1.857, 0, 18.583, 1.425, 0, 18.85, 0.635, 0, 19.183, 1.857, 0, 19.583, -1.425, 1, 19.672, -1.425, 19.761, -1.225, 19.85, -0.635, 1, 19.9, -0.303, 19.95, 0, 20, 0, 0, 20.35, -2.541, 0, 20.717, 3.794, 0, 21, -0.142, 0, 21.25, 1.883, 0, 21.617, -1.548, 0, 21.867, -0.645, 0, 22.183, -1.849, 0, 22.583, 1.426, 2, 22.6, 1.426, 0, 22.85, 0.636, 0, 23.183, 1.857, 0, 23.583, -1.425, 0, 23.85, -0.635, 0, 24.183, -1.857, 0, 24.583, 1.425, 0, 24.85, 0.635, 0, 25.183, 1.857, 0, 25.583, -1.425, 0, 25.85, -0.635, 0, 26.183, -1.857, 0, 26.583, 1.425, 0, 26.85, 0.635, 0, 27.183, 1.857, 0, 27.583, -1.425, 0, 27.85, -0.635, 0, 28.183, -1.857, 0, 28.583, 1.425, 0, 28.85, 0.635, 0, 29.183, 1.857, 0, 29.583, -1.425, 0, 29.85, -0.635, 0, 30.183, -1.857, 0, 30.583, 1.425, 0, 30.85, 0.635, 0, 31.183, 1.857, 0, 31.583, -1.425, 0, 31.85, -0.635, 0, 32.183, -1.857, 0, 32.583, 1.425, 0, 32.85, 0.635, 0, 33.183, 1.857, 0, 33.583, -1.425, 0, 33.85, -0.635, 0, 34.183, -1.857, 0, 34.583, 1.425, 0, 34.85, 0.635, 0, 35.183, 1.857, 0, 35.583, -1.425, 0, 35.85, -0.635, 0, 36.183, -1.857, 0, 36.583, 1.425, 0, 36.85, 0.635, 0, 37.183, 1.857, 0, 37.583, -1.425, 0, 37.85, -0.635, 0, 38.183, -1.857, 0, 38.583, 1.425, 0, 38.85, 0.635, 0, 39.183, 1.857, 0, 39.583, -1.425, 1, 39.672, -1.425, 39.761, -1.225, 39.85, -0.635, 1, 39.9, -0.303, 39.95, 0, 40, 0, 0, 40.35, -2.541, 0, 40.717, 3.794, 0, 41, -0.142, 0, 41.25, 1.883, 0, 41.617, -1.548, 0, 41.867, -0.645, 0, 42.183, -1.849, 0, 42.583, 1.426, 2, 42.6, 1.426, 0, 42.85, 0.636, 0, 43.183, 1.857, 0, 43.583, -1.425, 0, 43.85, -0.635, 0, 44.183, -1.857, 0, 44.583, 1.425, 0, 44.85, 0.635, 0, 45.183, 1.857, 0, 45.583, -1.425, 0, 45.85, -0.635, 0, 46.183, -1.857, 0, 46.583, 1.425, 0, 46.85, 0.635, 0, 47.183, 1.857, 0, 47.583, -1.425, 0, 47.85, -0.635, 0, 48.183, -1.857, 0, 48.583, 1.425, 0, 48.85, 0.635, 0, 49.183, 1.857, 0, 49.583, -1.425, 0, 49.85, -0.635, 0, 50.183, -1.857, 0, 50.583, 1.425, 0, 50.85, 0.635, 0, 51.183, 1.857, 0, 51.583, -1.425, 0, 51.85, -0.635, 0, 52.183, -1.857, 0, 52.583, 1.425, 0, 52.85, 0.635, 0, 53.183, 1.857, 0, 53.583, -1.425, 0, 53.85, -0.635, 0, 54.183, -1.857, 0, 54.583, 1.425, 0, 54.85, 0.635, 0, 55.183, 1.857, 0, 55.583, -1.425, 0, 55.85, -0.635, 0, 56.183, -1.857, 0, 56.583, 1.425, 0, 56.85, 0.635, 0, 57.183, 1.857, 0, 57.583, -1.425, 0, 57.85, -0.635, 0, 58.183, -1.857, 0, 58.583, 1.425, 0, 58.85, 0.635, 0, 59.183, 1.857, 0, 59.583, -1.425, 0, 59.85, -0.635, 0, 60, -1.186]}, {"Target": "Parameter", "Id": "Param129", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.017, 0, 0, 0.45, 2.01, 0, 0.85, -2.914, 0, 1.133, 0.628, 0, 1.383, -1.583, 0, 1.717, 1.695, 0, 2, 0.233, 0, 2.3, 1.5, 0, 2.683, -1.48, 0, 2.967, -0.258, 0, 3.283, -1.51, 0, 3.683, 1.467, 0, 3.967, 0.26, 0, 4.283, 1.51, 0, 4.683, -1.467, 0, 4.967, -0.26, 0, 5.283, -1.51, 0, 5.683, 1.467, 0, 5.967, 0.26, 0, 6.283, 1.51, 0, 6.683, -1.467, 0, 6.967, -0.26, 0, 7.283, -1.51, 0, 7.683, 1.467, 0, 7.967, 0.26, 0, 8.283, 1.51, 0, 8.683, -1.467, 0, 8.967, -0.26, 0, 9.283, -1.51, 0, 9.683, 1.467, 0, 9.967, 0.26, 0, 10.283, 1.51, 0, 10.683, -1.467, 0, 10.967, -0.26, 0, 11.283, -1.51, 0, 11.683, 1.467, 0, 11.967, 0.26, 0, 12.283, 1.51, 0, 12.683, -1.467, 0, 12.967, -0.26, 0, 13.283, -1.51, 0, 13.683, 1.467, 0, 13.967, 0.26, 0, 14.283, 1.51, 0, 14.683, -1.467, 0, 14.967, -0.26, 0, 15.283, -1.51, 0, 15.683, 1.467, 0, 15.967, 0.26, 0, 16.283, 1.51, 0, 16.683, -1.467, 0, 16.967, -0.26, 0, 17.283, -1.51, 0, 17.683, 1.467, 0, 17.967, 0.26, 0, 18.283, 1.51, 0, 18.683, -1.467, 0, 18.967, -0.26, 0, 19.283, -1.51, 0, 19.683, 1.467, 1, 19.778, 1.467, 19.872, 1.105, 19.967, 0.26, 1, 19.978, 0.161, 19.989, 0, 20, 0, 2, 20.017, 0, 0, 20.45, 2.01, 0, 20.85, -2.914, 0, 21.133, 0.628, 0, 21.383, -1.583, 0, 21.717, 1.695, 0, 22, 0.233, 0, 22.3, 1.5, 0, 22.683, -1.48, 0, 22.967, -0.258, 0, 23.283, -1.51, 0, 23.683, 1.467, 0, 23.967, 0.26, 0, 24.283, 1.51, 0, 24.683, -1.467, 0, 24.967, -0.26, 0, 25.283, -1.51, 0, 25.683, 1.467, 0, 25.967, 0.26, 0, 26.283, 1.51, 0, 26.683, -1.467, 0, 26.967, -0.26, 0, 27.283, -1.51, 0, 27.683, 1.467, 0, 27.967, 0.26, 0, 28.283, 1.51, 0, 28.683, -1.467, 0, 28.967, -0.26, 0, 29.283, -1.51, 0, 29.683, 1.467, 0, 29.967, 0.26, 0, 30.283, 1.51, 0, 30.683, -1.467, 0, 30.967, -0.26, 0, 31.283, -1.51, 0, 31.683, 1.467, 0, 31.967, 0.26, 0, 32.283, 1.51, 0, 32.683, -1.467, 0, 32.967, -0.26, 0, 33.283, -1.51, 0, 33.683, 1.467, 0, 33.967, 0.26, 0, 34.283, 1.51, 0, 34.683, -1.467, 0, 34.967, -0.26, 0, 35.283, -1.51, 0, 35.683, 1.467, 0, 35.967, 0.26, 0, 36.283, 1.51, 0, 36.683, -1.467, 0, 36.967, -0.26, 0, 37.283, -1.51, 0, 37.683, 1.467, 0, 37.967, 0.26, 0, 38.283, 1.51, 0, 38.683, -1.467, 0, 38.967, -0.26, 0, 39.283, -1.51, 0, 39.683, 1.467, 1, 39.778, 1.467, 39.872, 1.105, 39.967, 0.26, 1, 39.978, 0.161, 39.989, 0, 40, 0, 2, 40.017, 0, 0, 40.45, 2.01, 0, 40.85, -2.914, 0, 41.133, 0.628, 0, 41.383, -1.583, 0, 41.717, 1.695, 0, 42, 0.233, 0, 42.3, 1.5, 0, 42.683, -1.48, 0, 42.967, -0.258, 0, 43.283, -1.51, 0, 43.683, 1.467, 0, 43.967, 0.26, 0, 44.283, 1.51, 0, 44.683, -1.467, 0, 44.967, -0.26, 0, 45.283, -1.51, 0, 45.683, 1.467, 0, 45.967, 0.26, 0, 46.283, 1.51, 0, 46.683, -1.467, 0, 46.967, -0.26, 0, 47.283, -1.51, 0, 47.683, 1.467, 0, 47.967, 0.26, 0, 48.283, 1.51, 0, 48.683, -1.467, 0, 48.967, -0.26, 0, 49.283, -1.51, 0, 49.683, 1.467, 0, 49.967, 0.26, 0, 50.283, 1.51, 0, 50.683, -1.467, 0, 50.967, -0.26, 0, 51.283, -1.51, 0, 51.683, 1.467, 0, 51.967, 0.26, 0, 52.283, 1.51, 0, 52.683, -1.467, 0, 52.967, -0.26, 0, 53.283, -1.51, 0, 53.683, 1.467, 0, 53.967, 0.26, 0, 54.283, 1.51, 0, 54.683, -1.467, 0, 54.967, -0.26, 0, 55.283, -1.51, 0, 55.683, 1.467, 0, 55.967, 0.26, 0, 56.283, 1.51, 0, 56.683, -1.467, 0, 56.967, -0.26, 0, 57.283, -1.51, 0, 57.683, 1.467, 0, 57.967, 0.26, 0, 58.283, 1.51, 0, 58.683, -1.467, 0, 58.967, -0.26, 0, 59.283, -1.51, 0, 59.683, 1.467, 0, 59.967, 0.26, 0, 60, 0.304]}, {"Target": "Parameter", "Id": "Param16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.267, 0.223, 0, 6.6, -0.46, 0, 10.317, 0.033, 0, 13.05, -0.522, 1, 15.367, -0.522, 17.683, -0.261, 20, 0, 1, 21.756, 0.198, 23.511, 0.223, 25.267, 0.223, 0, 26.6, -0.46, 0, 30.317, 0.033, 0, 33.05, -0.522, 1, 35.367, -0.522, 37.683, -0.261, 40, 0, 1, 41.756, 0.198, 43.511, 0.223, 45.267, 0.223, 0, 46.6, -0.46, 0, 50.317, 0.033, 0, 53.05, -0.522, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 20, 0, 2, 40, 0, 1, 46.667, 0.333, 53.333, 0.667, 60, 1]}, {"Target": "Parameter", "Id": "Param142", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.812, 2, 0.017, 0.127, 1, 2.561, 0.305, 5.106, 0.482, 7.65, 0.66, 1, 8.1, 0.711, 8.55, 0.761, 9, 0.812, 2, 9.017, 0.127, 1, 11.561, 0.305, 14.106, 0.482, 16.65, 0.66, 1, 17.1, 0.711, 17.55, 0.761, 18, 0.812, 2, 18.017, 0.127, 1, 18.678, 0.355, 19.339, 0.584, 20, 0.812, 2, 20.017, 0.127, 1, 22.561, 0.305, 25.106, 0.482, 27.65, 0.66, 1, 28.1, 0.711, 28.55, 0.761, 29, 0.812, 2, 29.017, 0.127, 1, 31.561, 0.305, 34.106, 0.482, 36.65, 0.66, 1, 37.1, 0.711, 37.55, 0.761, 38, 0.812, 2, 38.017, 0.127, 1, 38.678, 0.355, 39.339, 0.584, 40, 0.812, 2, 40.017, 0.127, 1, 42.561, 0.305, 45.106, 0.482, 47.65, 0.66, 1, 48.1, 0.711, 48.55, 0.761, 49, 0.812, 2, 49.017, 0.127, 1, 51.561, 0.305, 54.106, 0.482, 56.65, 0.66, 1, 57.1, 0.711, 57.55, 0.761, 58, 0.812, 2, 58.017, 0.127, 1, 58.678, 0.173, 59.339, 0.219, 60, 0.265]}, {"Target": "Parameter", "Id": "Param143", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.76, 1, 0.422, 0.82, 0.845, 0.88, 1.267, 0.94, 2, 1.283, 0.2, 1, 3.855, 0.387, 6.428, 0.573, 9, 0.76, 1, 9.422, 0.82, 9.845, 0.88, 10.267, 0.94, 2, 10.283, 0.2, 1, 12.855, 0.387, 15.428, 0.573, 18, 0.76, 1, 18.422, 0.82, 18.845, 0.88, 19.267, 0.94, 2, 19.283, 0.2, 1, 19.522, 0.387, 19.761, 0.573, 20, 0.76, 1, 20.422, 0.82, 20.845, 0.88, 21.267, 0.94, 2, 21.283, 0.2, 1, 23.855, 0.387, 26.428, 0.573, 29, 0.76, 1, 29.422, 0.82, 29.845, 0.88, 30.267, 0.94, 2, 30.283, 0.2, 1, 32.855, 0.387, 35.428, 0.573, 38, 0.76, 1, 38.422, 0.82, 38.845, 0.88, 39.267, 0.94, 2, 39.283, 0.2, 1, 39.522, 0.387, 39.761, 0.573, 40, 0.76, 1, 40.422, 0.82, 40.845, 0.88, 41.267, 0.94, 2, 41.283, 0.2, 1, 43.855, 0.387, 46.428, 0.573, 49, 0.76, 1, 49.422, 0.82, 49.845, 0.88, 50.267, 0.94, 2, 50.283, 0.2, 1, 52.855, 0.387, 55.428, 0.573, 58, 0.76, 1, 58.422, 0.82, 58.845, 0.88, 59.267, 0.94, 2, 59.283, 0.2, 1, 59.522, 0.217, 59.761, 0.235, 60, 0.252]}, {"Target": "Parameter", "Id": "Param18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 0, 2, 0.35, 1, 2, 0.4, 2, 2, 0.45, 3, 2, 0.5, 4, 2, 0.55, 5, 2, 0.6, 0, 2, 0.65, 1, 2, 0.7, 2, 2, 0.75, 3, 2, 0.8, 4, 2, 0.85, 5, 2, 0.9, 0, 2, 0.95, 1, 2, 1, 2, 2, 1.05, 3, 2, 1.1, 4, 2, 1.15, 5, 2, 1.2, 0, 2, 1.25, 1, 2, 1.3, 2, 2, 1.35, 3, 2, 1.4, 4, 2, 1.45, 5, 2, 1.5, 0, 2, 1.55, 1, 2, 1.6, 2, 2, 1.65, 3, 2, 1.7, 4, 2, 1.75, 5, 2, 1.8, 0, 2, 1.85, 1, 2, 1.9, 2, 2, 1.95, 3, 2, 2, 4, 2, 2.05, 5, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 0, 2, 2.45, 1, 2, 2.5, 2, 2, 2.55, 3, 2, 2.6, 4, 2, 2.65, 5, 2, 2.7, 0, 2, 2.75, 1, 2, 2.8, 2, 2, 2.85, 3, 2, 2.9, 4, 2, 2.95, 5, 2, 3, 0, 2, 3.05, 1, 2, 3.1, 2, 2, 3.15, 3, 2, 3.2, 4, 2, 3.25, 5, 2, 3.3, 0, 2, 3.35, 1, 2, 3.4, 2, 2, 3.45, 3, 2, 3.5, 4, 2, 3.55, 5, 2, 3.6, 0, 2, 3.65, 1, 2, 3.7, 2, 2, 3.75, 3, 2, 3.8, 4, 2, 3.85, 5, 2, 3.9, 0, 2, 3.95, 1, 2, 4, 2, 2, 4.05, 3, 2, 4.1, 4, 2, 4.15, 5, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 0, 2, 4.55, 1, 2, 4.6, 2, 2, 4.65, 3, 2, 4.7, 4, 2, 4.75, 5, 2, 4.8, 0, 2, 4.85, 1, 2, 4.9, 2, 2, 4.95, 3, 2, 5, 4, 2, 5.05, 5, 2, 5.1, 0, 2, 5.15, 1, 2, 5.2, 2, 2, 5.25, 3, 2, 5.3, 4, 2, 5.35, 5, 2, 5.4, 0, 2, 5.45, 1, 2, 5.5, 2, 2, 5.55, 3, 2, 5.6, 4, 2, 5.65, 5, 2, 5.7, 0, 2, 5.75, 1, 2, 5.8, 2, 2, 5.85, 3, 2, 5.9, 4, 2, 5.95, 5, 2, 6, 0, 2, 6.05, 1, 2, 6.1, 2, 2, 6.15, 3, 2, 6.2, 4, 2, 6.25, 5, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 0, 2, 6.65, 1, 2, 6.7, 2, 2, 6.75, 3, 2, 6.8, 4, 2, 6.85, 5, 2, 6.9, 0, 2, 6.95, 1, 2, 7, 2, 2, 7.05, 3, 2, 7.1, 4, 2, 7.15, 5, 2, 7.2, 0, 2, 7.25, 1, 2, 7.3, 2, 2, 7.35, 3, 2, 7.4, 4, 2, 7.45, 5, 2, 7.5, 0, 2, 7.55, 1, 2, 7.6, 2, 2, 7.65, 3, 2, 7.7, 4, 2, 7.75, 5, 2, 7.8, 0, 2, 7.85, 1, 2, 7.9, 2, 2, 7.95, 3, 2, 8, 4, 2, 8.05, 5, 2, 8.1, 0, 2, 8.15, 1, 2, 8.2, 2, 2, 8.25, 3, 2, 8.3, 4, 2, 8.35, 5, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 0, 2, 8.75, 1, 2, 8.8, 2, 2, 8.85, 3, 2, 8.9, 4, 2, 8.95, 5, 2, 9, 0, 2, 9.05, 1, 2, 9.1, 2, 2, 9.15, 3, 2, 9.2, 4, 2, 9.25, 5, 2, 9.3, 0, 2, 9.35, 1, 2, 9.4, 2, 2, 9.45, 3, 2, 9.5, 4, 2, 9.55, 5, 2, 9.6, 0, 2, 9.65, 1, 2, 9.7, 2, 2, 9.75, 3, 2, 9.8, 4, 2, 9.85, 5, 2, 9.9, 0, 2, 9.95, 1, 2, 10, 2, 2, 10.05, 3, 2, 10.1, 4, 2, 10.15, 5, 2, 10.2, 0, 2, 10.25, 1, 2, 10.3, 2, 2, 10.35, 3, 2, 10.4, 4, 2, 10.45, 5, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 0, 2, 10.85, 1, 2, 10.9, 2, 2, 10.95, 3, 2, 11, 4, 2, 11.05, 5, 2, 11.1, 0, 2, 11.15, 1, 2, 11.2, 2, 2, 11.25, 3, 2, 11.3, 4, 2, 11.35, 5, 2, 11.4, 0, 2, 11.45, 1, 2, 11.5, 2, 2, 11.55, 3, 2, 11.6, 4, 2, 11.65, 5, 2, 11.7, 0, 2, 11.75, 1, 2, 11.8, 2, 2, 11.85, 3, 2, 11.9, 4, 2, 11.95, 5, 2, 12, 0, 2, 12.05, 1, 2, 12.1, 2, 2, 12.15, 3, 2, 12.2, 4, 2, 12.25, 5, 2, 12.3, 0, 2, 12.35, 1, 2, 12.4, 2, 2, 12.45, 3, 2, 12.5, 4, 2, 12.55, 5, 2, 12.6, 0, 2, 12.65, 1, 2, 12.7, 2, 2, 12.75, 3, 2, 12.8, 4, 2, 12.85, 5, 2, 12.9, 0, 2, 12.95, 1, 2, 13, 2, 2, 13.05, 3, 2, 13.1, 4, 2, 13.15, 5, 2, 13.2, 0, 2, 13.25, 1, 2, 13.3, 2, 2, 13.35, 3, 2, 13.4, 4, 2, 13.45, 5, 2, 13.5, 0, 2, 13.55, 1, 2, 13.6, 2, 2, 13.65, 3, 2, 13.7, 4, 2, 13.75, 5, 2, 13.8, 0, 2, 13.85, 1, 2, 13.9, 2, 2, 13.95, 3, 2, 14, 4, 2, 14.05, 5, 2, 14.1, 0, 2, 14.15, 1, 2, 14.2, 2, 2, 14.25, 3, 2, 14.3, 4, 2, 14.35, 5, 2, 14.4, 0, 2, 14.45, 1, 2, 14.5, 2, 2, 14.55, 3, 2, 14.6, 4, 2, 14.65, 5, 2, 14.7, 0, 2, 14.75, 1, 2, 14.8, 2, 2, 14.85, 3, 2, 14.9, 4, 2, 14.95, 5, 2, 15, 0, 2, 15.05, 1, 2, 15.1, 2, 2, 15.15, 3, 2, 15.2, 4, 2, 15.25, 5, 2, 15.3, 0, 2, 15.35, 1, 2, 15.4, 2, 2, 15.45, 3, 2, 15.5, 4, 2, 15.55, 5, 2, 15.6, 0, 2, 15.65, 1, 2, 15.7, 2, 2, 15.75, 3, 2, 15.8, 4, 2, 15.85, 5, 2, 15.9, 0, 2, 15.95, 1, 2, 16, 2, 2, 16.05, 3, 2, 16.1, 4, 2, 16.15, 5, 2, 16.2, 0, 2, 16.25, 1, 2, 16.3, 2, 2, 16.35, 3, 2, 16.4, 4, 2, 16.45, 5, 2, 16.5, 0, 2, 16.55, 1, 2, 16.6, 2, 2, 16.65, 3, 2, 16.7, 4, 2, 16.75, 5, 2, 16.8, 0, 2, 16.85, 1, 2, 16.9, 2, 2, 16.95, 3, 2, 17, 4, 2, 17.05, 5, 2, 17.1, 0, 2, 17.15, 1, 2, 17.2, 2, 2, 17.25, 3, 2, 17.3, 4, 2, 17.35, 5, 2, 17.4, 0, 2, 17.45, 1, 2, 17.5, 2, 2, 17.55, 3, 2, 17.6, 4, 2, 17.65, 5, 2, 17.7, 0, 2, 17.75, 1, 2, 17.8, 2, 2, 17.85, 3, 2, 17.9, 4, 2, 17.95, 5, 2, 18, 0, 2, 18.05, 1, 2, 18.1, 2, 2, 18.15, 3, 2, 18.2, 4, 2, 18.25, 5, 2, 18.3, 0, 2, 18.35, 1, 2, 18.4, 2, 2, 18.45, 3, 2, 18.5, 4, 2, 18.55, 5, 2, 18.6, 0, 2, 18.65, 1, 2, 18.7, 2, 2, 18.75, 3, 2, 18.8, 4, 2, 18.85, 5, 2, 18.9, 0, 2, 18.95, 1, 2, 19, 2, 2, 19.05, 3, 2, 19.1, 4, 2, 19.15, 5, 2, 19.2, 0, 2, 19.25, 1, 2, 19.3, 2, 2, 19.35, 3, 2, 19.4, 4, 2, 19.45, 5, 2, 19.5, 0, 2, 19.55, 1, 2, 19.6, 2, 2, 19.65, 3, 2, 19.7, 4, 2, 19.75, 5, 2, 19.8, 0, 2, 19.85, 1, 2, 19.9, 2, 2, 19.95, 3, 2, 20, 0, 2, 20.05, 1, 2, 20.1, 2, 2, 20.15, 3, 2, 20.2, 4, 2, 20.25, 5, 2, 20.3, 0, 2, 20.35, 1, 2, 20.4, 2, 2, 20.45, 3, 2, 20.5, 4, 2, 20.55, 5, 2, 20.6, 0, 2, 20.65, 1, 2, 20.7, 2, 2, 20.75, 3, 2, 20.8, 4, 2, 20.85, 5, 2, 20.9, 0, 2, 20.95, 1, 2, 21, 2, 2, 21.05, 3, 2, 21.1, 4, 2, 21.15, 5, 2, 21.2, 0, 2, 21.25, 1, 2, 21.3, 2, 2, 21.35, 3, 2, 21.4, 4, 2, 21.45, 5, 2, 21.5, 0, 2, 21.55, 1, 2, 21.6, 2, 2, 21.65, 3, 2, 21.7, 4, 2, 21.75, 5, 2, 21.8, 0, 2, 21.85, 1, 2, 21.9, 2, 2, 21.95, 3, 2, 22, 4, 2, 22.05, 5, 2, 22.1, 0, 2, 22.15, 1, 2, 22.2, 2, 2, 22.25, 3, 2, 22.3, 4, 2, 22.35, 5, 2, 22.4, 0, 2, 22.45, 1, 2, 22.5, 2, 2, 22.55, 3, 2, 22.6, 4, 2, 22.65, 5, 2, 22.7, 0, 2, 22.75, 1, 2, 22.8, 2, 2, 22.85, 3, 2, 22.9, 4, 2, 22.95, 5, 2, 23, 0, 2, 23.05, 1, 2, 23.1, 2, 2, 23.15, 3, 2, 23.2, 4, 2, 23.25, 5, 2, 23.3, 0, 2, 23.35, 1, 2, 23.4, 2, 2, 23.45, 3, 2, 23.5, 4, 2, 23.55, 5, 2, 23.6, 0, 2, 23.65, 1, 2, 23.7, 2, 2, 23.75, 3, 2, 23.8, 4, 2, 23.85, 5, 2, 23.9, 0, 2, 23.95, 1, 2, 24, 2, 2, 24.05, 3, 2, 24.1, 4, 2, 24.15, 5, 2, 24.2, 0, 2, 24.25, 1, 2, 24.3, 2, 2, 24.35, 3, 2, 24.4, 4, 2, 24.45, 5, 2, 24.5, 0, 2, 24.55, 1, 2, 24.6, 2, 2, 24.65, 3, 2, 24.7, 4, 2, 24.75, 5, 2, 24.8, 0, 2, 24.85, 1, 2, 24.9, 2, 2, 24.95, 3, 2, 25, 4, 2, 25.05, 5, 2, 25.1, 0, 2, 25.15, 1, 2, 25.2, 2, 2, 25.25, 3, 2, 25.3, 4, 2, 25.35, 5, 2, 25.4, 0, 2, 25.45, 1, 2, 25.5, 2, 2, 25.55, 3, 2, 25.6, 4, 2, 25.65, 5, 2, 25.7, 0, 2, 25.75, 1, 2, 25.8, 2, 2, 25.85, 3, 2, 25.9, 4, 2, 25.95, 5, 2, 26, 0, 2, 26.05, 1, 2, 26.1, 2, 2, 26.15, 3, 2, 26.2, 4, 2, 26.25, 5, 2, 26.3, 0, 2, 26.35, 1, 2, 26.4, 2, 2, 26.45, 3, 2, 26.5, 4, 2, 26.55, 5, 2, 26.6, 0, 2, 26.65, 1, 2, 26.7, 2, 2, 26.75, 3, 2, 26.8, 4, 2, 26.85, 5, 2, 26.9, 0, 2, 26.95, 1, 2, 27, 2, 2, 27.05, 3, 2, 27.1, 4, 2, 27.15, 5, 2, 27.2, 0, 2, 27.25, 1, 2, 27.3, 2, 2, 27.35, 3, 2, 27.4, 4, 2, 27.45, 5, 2, 27.5, 0, 2, 27.55, 1, 2, 27.6, 2, 2, 27.65, 3, 2, 27.7, 4, 2, 27.75, 5, 2, 27.8, 0, 2, 27.85, 1, 2, 27.9, 2, 2, 27.95, 3, 2, 28, 4, 2, 28.05, 5, 2, 28.1, 0, 2, 28.15, 1, 2, 28.2, 2, 2, 28.25, 3, 2, 28.3, 4, 2, 28.35, 5, 2, 28.4, 0, 2, 28.45, 1, 2, 28.5, 2, 2, 28.55, 3, 2, 28.6, 4, 2, 28.65, 5, 2, 28.7, 0, 2, 28.75, 1, 2, 28.8, 2, 2, 28.85, 3, 2, 28.9, 4, 2, 28.95, 5, 2, 29, 0, 2, 29.05, 1, 2, 29.1, 2, 2, 29.15, 3, 2, 29.2, 4, 2, 29.25, 5, 2, 29.3, 0, 2, 29.35, 1, 2, 29.4, 2, 2, 29.45, 3, 2, 29.5, 4, 2, 29.55, 5, 2, 29.6, 0, 2, 29.65, 1, 2, 29.7, 2, 2, 29.75, 3, 2, 29.8, 4, 2, 29.85, 5, 2, 29.9, 0, 2, 29.95, 1, 2, 30, 2, 2, 30.05, 3, 2, 30.1, 4, 2, 30.15, 5, 2, 30.2, 0, 2, 30.25, 1, 2, 30.3, 2, 2, 30.35, 3, 2, 30.4, 4, 2, 30.45, 5, 2, 30.5, 0, 2, 30.55, 1, 2, 30.6, 2, 2, 30.65, 3, 2, 30.7, 4, 2, 30.75, 5, 2, 30.8, 0, 2, 30.85, 1, 2, 30.9, 2, 2, 30.95, 3, 2, 31, 4, 2, 31.05, 5, 2, 31.1, 0, 2, 31.15, 1, 2, 31.2, 2, 2, 31.25, 3, 2, 31.3, 4, 2, 31.35, 5, 2, 31.4, 0, 2, 31.45, 1, 2, 31.5, 2, 2, 31.55, 3, 2, 31.6, 4, 2, 31.65, 5, 2, 31.7, 0, 2, 31.75, 1, 2, 31.8, 2, 2, 31.85, 3, 2, 31.9, 4, 2, 31.95, 5, 2, 32, 0, 2, 32.05, 1, 2, 32.1, 2, 2, 32.15, 3, 2, 32.2, 4, 2, 32.25, 5, 2, 32.3, 0, 2, 32.35, 1, 2, 32.4, 2, 2, 32.45, 3, 2, 32.5, 4, 2, 32.55, 5, 2, 32.6, 0, 2, 32.65, 1, 2, 32.7, 2, 2, 32.75, 3, 2, 32.8, 4, 2, 32.85, 5, 2, 32.9, 0, 2, 32.95, 1, 2, 33, 2, 2, 33.05, 3, 2, 33.1, 4, 2, 33.15, 5, 2, 33.2, 0, 2, 33.25, 1, 2, 33.3, 2, 2, 33.35, 3, 2, 33.4, 4, 2, 33.45, 5, 2, 33.5, 0, 2, 33.55, 1, 2, 33.6, 2, 2, 33.65, 3, 2, 33.7, 4, 2, 33.75, 5, 2, 33.8, 0, 2, 33.85, 1, 2, 33.9, 2, 2, 33.95, 3, 2, 34, 4, 2, 34.05, 5, 2, 34.1, 0, 2, 34.15, 1, 2, 34.2, 2, 2, 34.25, 3, 2, 34.3, 4, 2, 34.35, 5, 2, 34.4, 0, 2, 34.45, 1, 2, 34.5, 2, 2, 34.55, 3, 2, 34.6, 4, 2, 34.65, 5, 2, 34.7, 0, 2, 34.75, 1, 2, 34.8, 2, 2, 34.85, 3, 2, 34.9, 4, 2, 34.95, 5, 2, 35, 0, 2, 35.05, 1, 2, 35.1, 2, 2, 35.15, 3, 2, 35.2, 4, 2, 35.25, 5, 2, 35.3, 0, 2, 35.35, 1, 2, 35.4, 2, 2, 35.45, 3, 2, 35.5, 4, 2, 35.55, 5, 2, 35.6, 0, 2, 35.65, 1, 2, 35.7, 2, 2, 35.75, 3, 2, 35.8, 4, 2, 35.85, 5, 2, 35.9, 0, 2, 35.95, 1, 2, 36, 2, 2, 36.05, 3, 2, 36.1, 4, 2, 36.15, 5, 2, 36.2, 0, 2, 36.25, 1, 2, 36.3, 2, 2, 36.35, 3, 2, 36.4, 4, 2, 36.45, 5, 2, 36.5, 0, 2, 36.55, 1, 2, 36.6, 2, 2, 36.65, 3, 2, 36.7, 4, 2, 36.75, 5, 2, 36.8, 0, 2, 36.85, 1, 2, 36.9, 2, 2, 36.95, 3, 2, 37, 4, 2, 37.05, 5, 2, 37.1, 0, 2, 37.15, 1, 2, 37.2, 2, 2, 37.25, 3, 2, 37.3, 4, 2, 37.35, 5, 2, 37.4, 0, 2, 37.45, 1, 2, 37.5, 2, 2, 37.55, 3, 2, 37.6, 4, 2, 37.65, 5, 2, 37.7, 0, 2, 37.75, 1, 2, 37.8, 2, 2, 37.85, 3, 2, 37.9, 4, 2, 37.95, 5, 2, 38, 0, 2, 38.05, 1, 2, 38.1, 2, 2, 38.15, 3, 2, 38.2, 4, 2, 38.25, 5, 2, 38.3, 0, 2, 38.35, 1, 2, 38.4, 2, 2, 38.45, 3, 2, 38.5, 4, 2, 38.55, 5, 2, 38.6, 0, 2, 38.65, 1, 2, 38.7, 2, 2, 38.75, 3, 2, 38.8, 4, 2, 38.85, 5, 2, 38.9, 0, 2, 38.95, 1, 2, 39, 2, 2, 39.05, 3, 2, 39.1, 4, 2, 39.15, 5, 2, 39.2, 0, 2, 39.25, 1, 2, 39.3, 2, 2, 39.35, 3, 2, 39.4, 4, 2, 39.45, 5, 2, 39.5, 0, 2, 39.55, 1, 2, 39.6, 2, 2, 39.65, 3, 2, 39.7, 4, 2, 39.75, 5, 2, 39.8, 0, 2, 39.85, 1, 2, 39.9, 2, 2, 39.95, 3, 2, 40, 0, 2, 40.05, 1, 2, 40.1, 2, 2, 40.15, 3, 2, 40.2, 4, 2, 40.25, 5, 2, 40.3, 0, 2, 40.35, 1, 2, 40.4, 2, 2, 40.45, 3, 2, 40.5, 4, 2, 40.55, 5, 2, 40.6, 0, 2, 40.65, 1, 2, 40.7, 2, 2, 40.75, 3, 2, 40.8, 4, 2, 40.85, 5, 2, 40.9, 0, 2, 40.95, 1, 2, 41, 2, 2, 41.05, 3, 2, 41.1, 4, 2, 41.15, 5, 2, 41.2, 0, 2, 41.25, 1, 2, 41.3, 2, 2, 41.35, 3, 2, 41.4, 4, 2, 41.45, 5, 2, 41.5, 0, 2, 41.55, 1, 2, 41.6, 2, 2, 41.65, 3, 2, 41.7, 4, 2, 41.75, 5, 2, 41.8, 0, 2, 41.85, 1, 2, 41.9, 2, 2, 41.95, 3, 2, 42, 4, 2, 42.05, 5, 2, 42.1, 0, 2, 42.15, 1, 2, 42.2, 2, 2, 42.25, 3, 2, 42.3, 4, 2, 42.35, 5, 2, 42.4, 0, 2, 42.45, 1, 2, 42.5, 2, 2, 42.55, 3, 2, 42.6, 4, 2, 42.65, 5, 2, 42.7, 0, 2, 42.75, 1, 2, 42.8, 2, 2, 42.85, 3, 2, 42.9, 4, 2, 42.95, 5, 2, 43, 0, 2, 43.05, 1, 2, 43.1, 2, 2, 43.15, 3, 2, 43.2, 4, 2, 43.25, 5, 2, 43.3, 0, 2, 43.35, 1, 2, 43.4, 2, 2, 43.45, 3, 2, 43.5, 4, 2, 43.55, 5, 2, 43.6, 0, 2, 43.65, 1, 2, 43.7, 2, 2, 43.75, 3, 2, 43.8, 4, 2, 43.85, 5, 2, 43.9, 0, 2, 43.95, 1, 2, 44, 2, 2, 44.05, 3, 2, 44.1, 4, 2, 44.15, 5, 2, 44.2, 0, 2, 44.25, 1, 2, 44.3, 2, 2, 44.35, 3, 2, 44.4, 4, 2, 44.45, 5, 2, 44.5, 0, 2, 44.55, 1, 2, 44.6, 2, 2, 44.65, 3, 2, 44.7, 4, 2, 44.75, 5, 2, 44.8, 0, 2, 44.85, 1, 2, 44.9, 2, 2, 44.95, 3, 2, 45, 4, 2, 45.05, 5, 2, 45.1, 0, 2, 45.15, 1, 2, 45.2, 2, 2, 45.25, 3, 2, 45.3, 4, 2, 45.35, 5, 2, 45.4, 0, 2, 45.45, 1, 2, 45.5, 2, 2, 45.55, 3, 2, 45.6, 4, 2, 45.65, 5, 2, 45.7, 0, 2, 45.75, 1, 2, 45.8, 2, 2, 45.85, 3, 2, 45.9, 4, 2, 45.95, 5, 2, 46, 0, 2, 46.05, 1, 2, 46.1, 2, 2, 46.15, 3, 2, 46.2, 4, 2, 46.25, 5, 2, 46.3, 0, 2, 46.35, 1, 2, 46.4, 2, 2, 46.45, 3, 2, 46.5, 4, 2, 46.55, 5, 2, 46.6, 0, 2, 46.65, 1, 2, 46.7, 2, 2, 46.75, 3, 2, 46.8, 4, 2, 46.85, 5, 2, 46.9, 0, 2, 46.95, 1, 2, 47, 2, 2, 47.05, 3, 2, 47.1, 4, 2, 47.15, 5, 2, 47.2, 0, 2, 47.25, 1, 2, 47.3, 2, 2, 47.35, 3, 2, 47.4, 4, 2, 47.45, 5, 2, 47.5, 0, 2, 47.55, 1, 2, 47.6, 2, 2, 47.65, 3, 2, 47.7, 4, 2, 47.75, 5, 2, 47.8, 0, 2, 47.85, 1, 2, 47.9, 2, 2, 47.95, 3, 2, 48, 4, 2, 48.05, 5, 2, 48.1, 0, 2, 48.15, 1, 2, 48.2, 2, 2, 48.25, 3, 2, 48.3, 4, 2, 48.35, 5, 2, 48.4, 0, 2, 48.45, 1, 2, 48.5, 2, 2, 48.55, 3, 2, 48.6, 4, 2, 48.65, 5, 2, 48.7, 0, 2, 48.75, 1, 2, 48.8, 2, 2, 48.85, 3, 2, 48.9, 4, 2, 48.95, 5, 2, 49, 0, 2, 49.05, 1, 2, 49.1, 2, 2, 49.15, 3, 2, 49.2, 4, 2, 49.25, 5, 2, 49.3, 0, 2, 49.35, 1, 2, 49.4, 2, 2, 49.45, 3, 2, 49.5, 4, 2, 49.55, 5, 2, 49.6, 0, 2, 49.65, 1, 2, 49.7, 2, 2, 49.75, 3, 2, 49.8, 4, 2, 49.85, 5, 2, 49.9, 0, 2, 49.95, 1, 2, 50, 2, 2, 50.05, 3, 2, 50.1, 4, 2, 50.15, 5, 2, 50.2, 0, 2, 50.25, 1, 2, 50.3, 2, 2, 50.35, 3, 2, 50.4, 4, 2, 50.45, 5, 2, 50.5, 0, 2, 50.55, 1, 2, 50.6, 2, 2, 50.65, 3, 2, 50.7, 4, 2, 50.75, 5, 2, 50.8, 0, 2, 50.85, 1, 2, 50.9, 2, 2, 50.95, 3, 2, 51, 4, 2, 51.05, 5, 2, 51.1, 0, 2, 51.15, 1, 2, 51.2, 2, 2, 51.25, 3, 2, 51.3, 4, 2, 51.35, 5, 2, 51.4, 0, 2, 51.45, 1, 2, 51.5, 2, 2, 51.55, 3, 2, 51.6, 4, 2, 51.65, 5, 2, 51.7, 0, 2, 51.75, 1, 2, 51.8, 2, 2, 51.85, 3, 2, 51.9, 4, 2, 51.95, 5, 2, 52, 0, 2, 52.05, 1, 2, 52.1, 2, 2, 52.15, 3, 2, 52.2, 4, 2, 52.25, 5, 2, 52.3, 0, 2, 52.35, 1, 2, 52.4, 2, 2, 52.45, 3, 2, 52.5, 4, 2, 52.55, 5, 2, 52.6, 0, 2, 52.65, 1, 2, 52.7, 2, 2, 52.75, 3, 2, 52.8, 4, 2, 52.85, 5, 2, 52.9, 0, 2, 52.95, 1, 2, 53, 2, 2, 53.05, 3, 2, 53.1, 4, 2, 53.15, 5, 2, 53.2, 0, 2, 53.25, 1, 2, 53.3, 2, 2, 53.35, 3, 2, 53.4, 4, 2, 53.45, 5, 2, 53.5, 0, 2, 53.55, 1, 2, 53.6, 2, 2, 53.65, 3, 2, 53.7, 4, 2, 53.75, 5, 2, 53.8, 0, 2, 53.85, 1, 2, 53.9, 2, 2, 53.95, 3, 2, 54, 4, 2, 54.05, 5, 2, 54.1, 0, 2, 54.15, 1, 2, 54.2, 2, 2, 54.25, 3, 2, 54.3, 4, 2, 54.35, 5, 2, 54.4, 0, 2, 54.45, 1, 2, 54.5, 2, 2, 54.55, 3, 2, 54.6, 4, 2, 54.65, 5, 2, 54.7, 0, 2, 54.75, 1, 2, 54.8, 2, 2, 54.85, 3, 2, 54.9, 4, 2, 54.95, 5, 2, 55, 0, 2, 55.05, 1, 2, 55.1, 2, 2, 55.15, 3, 2, 55.2, 4, 2, 55.25, 5, 2, 55.3, 0, 2, 55.35, 1, 2, 55.4, 2, 2, 55.45, 3, 2, 55.5, 4, 2, 55.55, 5, 2, 55.6, 0, 2, 55.65, 1, 2, 55.7, 2, 2, 55.75, 3, 2, 55.8, 4, 2, 55.85, 5, 2, 55.9, 0, 2, 55.95, 1, 2, 56, 2, 2, 56.05, 3, 2, 56.1, 4, 2, 56.15, 5, 2, 56.2, 0, 2, 56.25, 1, 2, 56.3, 2, 2, 56.35, 3, 2, 56.4, 4, 2, 56.45, 5, 2, 56.5, 0, 2, 56.55, 1, 2, 56.6, 2, 2, 56.65, 3, 2, 56.7, 4, 2, 56.75, 5, 2, 56.8, 0, 2, 56.85, 1, 2, 56.9, 2, 2, 56.95, 3, 2, 57, 4, 2, 57.05, 5, 2, 57.1, 0, 2, 57.15, 1, 2, 57.2, 2, 2, 57.25, 3, 2, 57.3, 4, 2, 57.35, 5, 2, 57.4, 0, 2, 57.45, 1, 2, 57.5, 2, 2, 57.55, 3, 2, 57.6, 4, 2, 57.65, 5, 2, 57.7, 0, 2, 57.75, 1, 2, 57.8, 2, 2, 57.85, 3, 2, 57.9, 4, 2, 57.95, 5, 2, 58, 0, 2, 58.05, 1, 2, 58.1, 2, 2, 58.15, 3, 2, 58.2, 4, 2, 58.25, 5, 2, 58.3, 0, 2, 58.35, 1, 2, 58.4, 2, 2, 58.45, 3, 2, 58.5, 4, 2, 58.55, 5, 2, 58.6, 0, 2, 58.65, 1, 2, 58.7, 2, 2, 58.75, 3, 2, 58.8, 4, 2, 58.85, 5, 2, 58.9, 0, 2, 58.95, 1, 2, 59, 2, 2, 59.05, 3, 2, 59.1, 4, 2, 59.15, 5, 2, 59.2, 0, 2, 59.25, 1, 2, 59.3, 2, 2, 59.35, 3, 2, 59.4, 4, 2, 59.45, 5, 2, 59.5, 0, 2, 59.55, 1, 2, 59.6, 2, 2, 59.65, 3, 2, 59.7, 4, 2, 59.75, 5, 2, 59.8, 0, 2, 59.85, 1, 2, 59.9, 2, 2, 59.95, 3, 2, 60, 4]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 6, 2, 0.35, 0, 2, 0.4, 1, 2, 0.45, 2, 2, 0.5, 3, 2, 0.55, 4, 2, 0.6, 5, 2, 0.65, 6, 2, 0.7, 0, 2, 0.75, 1, 2, 0.8, 2, 2, 0.85, 3, 2, 0.9, 4, 2, 0.95, 5, 2, 1, 6, 2, 1.05, 0, 2, 1.1, 1, 2, 1.15, 2, 2, 1.2, 3, 2, 1.25, 4, 2, 1.3, 5, 2, 1.35, 6, 2, 1.4, 0, 2, 1.45, 1, 2, 1.5, 2, 2, 1.55, 3, 2, 1.6, 4, 2, 1.65, 5, 2, 1.7, 6, 2, 1.75, 0, 2, 1.8, 1, 2, 1.85, 2, 2, 1.9, 3, 2, 1.95, 4, 2, 2, 5, 2, 2.05, 6, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 6, 2, 2.45, 0, 2, 2.5, 1, 2, 2.55, 2, 2, 2.6, 3, 2, 2.65, 4, 2, 2.7, 5, 2, 2.75, 6, 2, 2.8, 0, 2, 2.85, 1, 2, 2.9, 2, 2, 2.95, 3, 2, 3, 4, 2, 3.05, 5, 2, 3.1, 6, 2, 3.15, 0, 2, 3.2, 1, 2, 3.25, 2, 2, 3.3, 3, 2, 3.35, 4, 2, 3.4, 5, 2, 3.45, 6, 2, 3.5, 0, 2, 3.55, 1, 2, 3.6, 2, 2, 3.65, 3, 2, 3.7, 4, 2, 3.75, 5, 2, 3.8, 6, 2, 3.85, 0, 2, 3.9, 1, 2, 3.95, 2, 2, 4, 3, 2, 4.05, 4, 2, 4.1, 5, 2, 4.15, 6, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 6, 2, 4.55, 0, 2, 4.6, 1, 2, 4.65, 2, 2, 4.7, 3, 2, 4.75, 4, 2, 4.8, 5, 2, 4.85, 6, 2, 4.9, 0, 2, 4.95, 1, 2, 5, 2, 2, 5.05, 3, 2, 5.1, 4, 2, 5.15, 5, 2, 5.2, 6, 2, 5.25, 0, 2, 5.3, 1, 2, 5.35, 2, 2, 5.4, 3, 2, 5.45, 4, 2, 5.5, 5, 2, 5.55, 6, 2, 5.6, 0, 2, 5.65, 1, 2, 5.7, 2, 2, 5.75, 3, 2, 5.8, 4, 2, 5.85, 5, 2, 5.9, 6, 2, 5.95, 0, 2, 6, 1, 2, 6.05, 2, 2, 6.1, 3, 2, 6.15, 4, 2, 6.2, 5, 2, 6.25, 6, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 6, 2, 6.65, 0, 2, 6.7, 1, 2, 6.75, 2, 2, 6.8, 3, 2, 6.85, 4, 2, 6.9, 5, 2, 6.95, 6, 2, 7, 0, 2, 7.05, 1, 2, 7.1, 2, 2, 7.15, 3, 2, 7.2, 4, 2, 7.25, 5, 2, 7.3, 6, 2, 7.35, 0, 2, 7.4, 1, 2, 7.45, 2, 2, 7.5, 3, 2, 7.55, 4, 2, 7.6, 5, 2, 7.65, 6, 2, 7.7, 0, 2, 7.75, 1, 2, 7.8, 2, 2, 7.85, 3, 2, 7.9, 4, 2, 7.95, 5, 2, 8, 6, 2, 8.05, 0, 2, 8.1, 1, 2, 8.15, 2, 2, 8.2, 3, 2, 8.25, 4, 2, 8.3, 5, 2, 8.35, 6, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 6, 2, 8.75, 0, 2, 8.8, 1, 2, 8.85, 2, 2, 8.9, 3, 2, 8.95, 4, 2, 9, 5, 2, 9.05, 6, 2, 9.1, 0, 2, 9.15, 1, 2, 9.2, 2, 2, 9.25, 3, 2, 9.3, 4, 2, 9.35, 5, 2, 9.4, 6, 2, 9.45, 0, 2, 9.5, 1, 2, 9.55, 2, 2, 9.6, 3, 2, 9.65, 4, 2, 9.7, 5, 2, 9.75, 6, 2, 9.8, 0, 2, 9.85, 1, 2, 9.9, 2, 2, 9.95, 3, 2, 10, 4, 2, 10.05, 5, 2, 10.1, 6, 2, 10.15, 0, 2, 10.2, 1, 2, 10.25, 2, 2, 10.3, 3, 2, 10.35, 4, 2, 10.4, 5, 2, 10.45, 6, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 6, 2, 10.85, 0, 2, 10.9, 1, 2, 10.95, 2, 2, 11, 3, 2, 11.05, 4, 2, 11.1, 5, 2, 11.15, 6, 2, 11.2, 0, 2, 11.25, 1, 2, 11.3, 2, 2, 11.35, 3, 2, 11.4, 4, 2, 11.45, 5, 2, 11.5, 6, 2, 11.55, 0, 2, 11.6, 1, 2, 11.65, 2, 2, 11.7, 3, 2, 11.75, 4, 2, 11.8, 5, 2, 11.85, 6, 2, 11.9, 0, 2, 11.95, 1, 2, 12, 2, 2, 12.05, 3, 2, 12.1, 4, 2, 12.15, 5, 2, 12.2, 6, 2, 12.25, 0, 2, 12.3, 1, 2, 12.35, 2, 2, 12.4, 3, 2, 12.45, 4, 2, 12.5, 5, 2, 12.55, 6, 2, 12.6, 0, 2, 12.65, 1, 2, 12.7, 2, 2, 12.75, 3, 2, 12.8, 4, 2, 12.85, 5, 2, 12.9, 6, 2, 12.95, 0, 2, 13, 1, 2, 13.05, 2, 2, 13.1, 3, 2, 13.15, 4, 2, 13.2, 5, 2, 13.25, 6, 2, 13.3, 0, 2, 13.35, 1, 2, 13.4, 2, 2, 13.45, 3, 2, 13.5, 4, 2, 13.55, 5, 2, 13.6, 6, 2, 13.65, 0, 2, 13.7, 1, 2, 13.75, 2, 2, 13.8, 3, 2, 13.85, 4, 2, 13.9, 5, 2, 13.95, 6, 2, 14, 0, 2, 14.05, 1, 2, 14.1, 2, 2, 14.15, 3, 2, 14.2, 4, 2, 14.25, 5, 2, 14.3, 6, 2, 14.35, 0, 2, 14.4, 1, 2, 14.45, 2, 2, 14.5, 3, 2, 14.55, 4, 2, 14.6, 5, 2, 14.65, 6, 2, 14.7, 0, 2, 14.75, 1, 2, 14.8, 2, 2, 14.85, 3, 2, 14.9, 4, 2, 14.95, 5, 2, 15, 6, 2, 15.05, 0, 2, 15.1, 1, 2, 15.15, 2, 2, 15.2, 3, 2, 15.25, 4, 2, 15.3, 5, 2, 15.35, 6, 2, 15.4, 0, 2, 15.45, 1, 2, 15.5, 2, 2, 15.55, 3, 2, 15.6, 4, 2, 15.65, 5, 2, 15.7, 6, 2, 15.75, 0, 2, 15.8, 1, 2, 15.85, 2, 2, 15.9, 3, 2, 15.95, 4, 2, 16, 5, 2, 16.05, 6, 2, 16.1, 0, 2, 16.15, 1, 2, 16.2, 2, 2, 16.25, 3, 2, 16.3, 4, 2, 16.35, 5, 2, 16.4, 6, 2, 16.45, 0, 2, 16.5, 1, 2, 16.55, 2, 2, 16.6, 3, 2, 16.65, 4, 2, 16.7, 5, 2, 16.75, 6, 2, 16.8, 0, 2, 16.85, 1, 2, 16.9, 2, 2, 16.95, 3, 2, 17, 4, 2, 17.05, 5, 2, 17.1, 6, 2, 17.15, 0, 2, 17.2, 1, 2, 17.25, 2, 2, 17.3, 3, 2, 17.35, 4, 2, 17.4, 5, 2, 17.45, 6, 2, 17.5, 0, 2, 17.55, 1, 2, 17.6, 2, 2, 17.65, 3, 2, 17.7, 4, 2, 17.75, 5, 2, 17.8, 6, 2, 17.85, 0, 2, 17.9, 1, 2, 17.95, 2, 2, 18, 3, 2, 18.05, 4, 2, 18.1, 5, 2, 18.15, 6, 2, 18.2, 0, 2, 18.25, 1, 2, 18.3, 2, 2, 18.35, 3, 2, 18.4, 4, 2, 18.45, 5, 2, 18.5, 6, 2, 18.55, 0, 2, 18.6, 1, 2, 18.65, 2, 2, 18.7, 3, 2, 18.75, 4, 2, 18.8, 5, 2, 18.85, 6, 2, 18.9, 0, 2, 18.95, 1, 2, 19, 2, 2, 19.05, 3, 2, 19.1, 4, 2, 19.15, 5, 2, 19.2, 6, 2, 19.25, 0, 2, 19.3, 1, 2, 19.35, 2, 2, 19.4, 3, 2, 19.45, 4, 2, 19.5, 5, 2, 19.55, 6, 2, 19.6, 0, 2, 19.65, 1, 2, 19.7, 2, 2, 19.75, 3, 2, 19.8, 4, 2, 19.85, 5, 2, 19.9, 6, 2, 19.95, 0, 2, 20, 0, 2, 20.05, 1, 2, 20.1, 2, 2, 20.15, 3, 2, 20.2, 4, 2, 20.25, 5, 2, 20.3, 6, 2, 20.35, 0, 2, 20.4, 1, 2, 20.45, 2, 2, 20.5, 3, 2, 20.55, 4, 2, 20.6, 5, 2, 20.65, 6, 2, 20.7, 0, 2, 20.75, 1, 2, 20.8, 2, 2, 20.85, 3, 2, 20.9, 4, 2, 20.95, 5, 2, 21, 6, 2, 21.05, 0, 2, 21.1, 1, 2, 21.15, 2, 2, 21.2, 3, 2, 21.25, 4, 2, 21.3, 5, 2, 21.35, 6, 2, 21.4, 0, 2, 21.45, 1, 2, 21.5, 2, 2, 21.55, 3, 2, 21.6, 4, 2, 21.65, 5, 2, 21.7, 6, 2, 21.75, 0, 2, 21.8, 1, 2, 21.85, 2, 2, 21.9, 3, 2, 21.95, 4, 2, 22, 5, 2, 22.05, 6, 2, 22.1, 0, 2, 22.15, 1, 2, 22.2, 2, 2, 22.25, 3, 2, 22.3, 4, 2, 22.35, 5, 2, 22.4, 6, 2, 22.45, 0, 2, 22.5, 1, 2, 22.55, 2, 2, 22.6, 3, 2, 22.65, 4, 2, 22.7, 5, 2, 22.75, 6, 2, 22.8, 0, 2, 22.85, 1, 2, 22.9, 2, 2, 22.95, 3, 2, 23, 4, 2, 23.05, 5, 2, 23.1, 6, 2, 23.15, 0, 2, 23.2, 1, 2, 23.25, 2, 2, 23.3, 3, 2, 23.35, 4, 2, 23.4, 5, 2, 23.45, 6, 2, 23.5, 0, 2, 23.55, 1, 2, 23.6, 2, 2, 23.65, 3, 2, 23.7, 4, 2, 23.75, 5, 2, 23.8, 6, 2, 23.85, 0, 2, 23.9, 1, 2, 23.95, 2, 2, 24, 3, 2, 24.05, 4, 2, 24.1, 5, 2, 24.15, 6, 2, 24.2, 0, 2, 24.25, 1, 2, 24.3, 2, 2, 24.35, 3, 2, 24.4, 4, 2, 24.45, 5, 2, 24.5, 6, 2, 24.55, 0, 2, 24.6, 1, 2, 24.65, 2, 2, 24.7, 3, 2, 24.75, 4, 2, 24.8, 5, 2, 24.85, 6, 2, 24.9, 0, 2, 24.95, 1, 2, 25, 2, 2, 25.05, 3, 2, 25.1, 4, 2, 25.15, 5, 2, 25.2, 6, 2, 25.25, 0, 2, 25.3, 1, 2, 25.35, 2, 2, 25.4, 3, 2, 25.45, 4, 2, 25.5, 5, 2, 25.55, 6, 2, 25.6, 0, 2, 25.65, 1, 2, 25.7, 2, 2, 25.75, 3, 2, 25.8, 4, 2, 25.85, 5, 2, 25.9, 6, 2, 25.95, 0, 2, 26, 1, 2, 26.05, 2, 2, 26.1, 3, 2, 26.15, 4, 2, 26.2, 5, 2, 26.25, 6, 2, 26.3, 0, 2, 26.35, 1, 2, 26.4, 2, 2, 26.45, 3, 2, 26.5, 4, 2, 26.55, 5, 2, 26.6, 6, 2, 26.65, 0, 2, 26.7, 1, 2, 26.75, 2, 2, 26.8, 3, 2, 26.85, 4, 2, 26.9, 5, 2, 26.95, 6, 2, 27, 0, 2, 27.05, 1, 2, 27.1, 2, 2, 27.15, 3, 2, 27.2, 4, 2, 27.25, 5, 2, 27.3, 6, 2, 27.35, 0, 2, 27.4, 1, 2, 27.45, 2, 2, 27.5, 3, 2, 27.55, 4, 2, 27.6, 5, 2, 27.65, 6, 2, 27.7, 0, 2, 27.75, 1, 2, 27.8, 2, 2, 27.85, 3, 2, 27.9, 4, 2, 27.95, 5, 2, 28, 6, 2, 28.05, 0, 2, 28.1, 1, 2, 28.15, 2, 2, 28.2, 3, 2, 28.25, 4, 2, 28.3, 5, 2, 28.35, 6, 2, 28.4, 0, 2, 28.45, 1, 2, 28.5, 2, 2, 28.55, 3, 2, 28.6, 4, 2, 28.65, 5, 2, 28.7, 6, 2, 28.75, 0, 2, 28.8, 1, 2, 28.85, 2, 2, 28.9, 3, 2, 28.95, 4, 2, 29, 5, 2, 29.05, 6, 2, 29.1, 0, 2, 29.15, 1, 2, 29.2, 2, 2, 29.25, 3, 2, 29.3, 4, 2, 29.35, 5, 2, 29.4, 6, 2, 29.45, 0, 2, 29.5, 1, 2, 29.55, 2, 2, 29.6, 3, 2, 29.65, 4, 2, 29.7, 5, 2, 29.75, 6, 2, 29.8, 0, 2, 29.85, 1, 2, 29.9, 2, 2, 29.95, 3, 2, 30, 4, 2, 30.05, 5, 2, 30.1, 6, 2, 30.15, 0, 2, 30.2, 1, 2, 30.25, 2, 2, 30.3, 3, 2, 30.35, 4, 2, 30.4, 5, 2, 30.45, 6, 2, 30.5, 0, 2, 30.55, 1, 2, 30.6, 2, 2, 30.65, 3, 2, 30.7, 4, 2, 30.75, 5, 2, 30.8, 6, 2, 30.85, 0, 2, 30.9, 1, 2, 30.95, 2, 2, 31, 3, 2, 31.05, 4, 2, 31.1, 5, 2, 31.15, 6, 2, 31.2, 0, 2, 31.25, 1, 2, 31.3, 2, 2, 31.35, 3, 2, 31.4, 4, 2, 31.45, 5, 2, 31.5, 6, 2, 31.55, 0, 2, 31.6, 1, 2, 31.65, 2, 2, 31.7, 3, 2, 31.75, 4, 2, 31.8, 5, 2, 31.85, 6, 2, 31.9, 0, 2, 31.95, 1, 2, 32, 2, 2, 32.05, 3, 2, 32.1, 4, 2, 32.15, 5, 2, 32.2, 6, 2, 32.25, 0, 2, 32.3, 1, 2, 32.35, 2, 2, 32.4, 3, 2, 32.45, 4, 2, 32.5, 5, 2, 32.55, 6, 2, 32.6, 0, 2, 32.65, 1, 2, 32.7, 2, 2, 32.75, 3, 2, 32.8, 4, 2, 32.85, 5, 2, 32.9, 6, 2, 32.95, 0, 2, 33, 1, 2, 33.05, 2, 2, 33.1, 3, 2, 33.15, 4, 2, 33.2, 5, 2, 33.25, 6, 2, 33.3, 0, 2, 33.35, 1, 2, 33.4, 2, 2, 33.45, 3, 2, 33.5, 4, 2, 33.55, 5, 2, 33.6, 6, 2, 33.65, 0, 2, 33.7, 1, 2, 33.75, 2, 2, 33.8, 3, 2, 33.85, 4, 2, 33.9, 5, 2, 33.95, 6, 2, 34, 0, 2, 34.05, 1, 2, 34.1, 2, 2, 34.15, 3, 2, 34.2, 4, 2, 34.25, 5, 2, 34.3, 6, 2, 34.35, 0, 2, 34.4, 1, 2, 34.45, 2, 2, 34.5, 3, 2, 34.55, 4, 2, 34.6, 5, 2, 34.65, 6, 2, 34.7, 0, 2, 34.75, 1, 2, 34.8, 2, 2, 34.85, 3, 2, 34.9, 4, 2, 34.95, 5, 2, 35, 6, 2, 35.05, 0, 2, 35.1, 1, 2, 35.15, 2, 2, 35.2, 3, 2, 35.25, 4, 2, 35.3, 5, 2, 35.35, 6, 2, 35.4, 0, 2, 35.45, 1, 2, 35.5, 2, 2, 35.55, 3, 2, 35.6, 4, 2, 35.65, 5, 2, 35.7, 6, 2, 35.75, 0, 2, 35.8, 1, 2, 35.85, 2, 2, 35.9, 3, 2, 35.95, 4, 2, 36, 5, 2, 36.05, 6, 2, 36.1, 0, 2, 36.15, 1, 2, 36.2, 2, 2, 36.25, 3, 2, 36.3, 4, 2, 36.35, 5, 2, 36.4, 6, 2, 36.45, 0, 2, 36.5, 1, 2, 36.55, 2, 2, 36.6, 3, 2, 36.65, 4, 2, 36.7, 5, 2, 36.75, 6, 2, 36.8, 0, 2, 36.85, 1, 2, 36.9, 2, 2, 36.95, 3, 2, 37, 4, 2, 37.05, 5, 2, 37.1, 6, 2, 37.15, 0, 2, 37.2, 1, 2, 37.25, 2, 2, 37.3, 3, 2, 37.35, 4, 2, 37.4, 5, 2, 37.45, 6, 2, 37.5, 0, 2, 37.55, 1, 2, 37.6, 2, 2, 37.65, 3, 2, 37.7, 4, 2, 37.75, 5, 2, 37.8, 6, 2, 37.85, 0, 2, 37.9, 1, 2, 37.95, 2, 2, 38, 3, 2, 38.05, 4, 2, 38.1, 5, 2, 38.15, 6, 2, 38.2, 0, 2, 38.25, 1, 2, 38.3, 2, 2, 38.35, 3, 2, 38.4, 4, 2, 38.45, 5, 2, 38.5, 6, 2, 38.55, 0, 2, 38.6, 1, 2, 38.65, 2, 2, 38.7, 3, 2, 38.75, 4, 2, 38.8, 5, 2, 38.85, 6, 2, 38.9, 0, 2, 38.95, 1, 2, 39, 2, 2, 39.05, 3, 2, 39.1, 4, 2, 39.15, 5, 2, 39.2, 6, 2, 39.25, 0, 2, 39.3, 1, 2, 39.35, 2, 2, 39.4, 3, 2, 39.45, 4, 2, 39.5, 5, 2, 39.55, 6, 2, 39.6, 0, 2, 39.65, 1, 2, 39.7, 2, 2, 39.75, 3, 2, 39.8, 4, 2, 39.85, 5, 2, 39.9, 6, 2, 39.95, 0, 2, 40, 0, 2, 40.05, 1, 2, 40.1, 2, 2, 40.15, 3, 2, 40.2, 4, 2, 40.25, 5, 2, 40.3, 6, 2, 40.35, 0, 2, 40.4, 1, 2, 40.45, 2, 2, 40.5, 3, 2, 40.55, 4, 2, 40.6, 5, 2, 40.65, 6, 2, 40.7, 0, 2, 40.75, 1, 2, 40.8, 2, 2, 40.85, 3, 2, 40.9, 4, 2, 40.95, 5, 2, 41, 6, 2, 41.05, 0, 2, 41.1, 1, 2, 41.15, 2, 2, 41.2, 3, 2, 41.25, 4, 2, 41.3, 5, 2, 41.35, 6, 2, 41.4, 0, 2, 41.45, 1, 2, 41.5, 2, 2, 41.55, 3, 2, 41.6, 4, 2, 41.65, 5, 2, 41.7, 6, 2, 41.75, 0, 2, 41.8, 1, 2, 41.85, 2, 2, 41.9, 3, 2, 41.95, 4, 2, 42, 5, 2, 42.05, 6, 2, 42.1, 0, 2, 42.15, 1, 2, 42.2, 2, 2, 42.25, 3, 2, 42.3, 4, 2, 42.35, 5, 2, 42.4, 6, 2, 42.45, 0, 2, 42.5, 1, 2, 42.55, 2, 2, 42.6, 3, 2, 42.65, 4, 2, 42.7, 5, 2, 42.75, 6, 2, 42.8, 0, 2, 42.85, 1, 2, 42.9, 2, 2, 42.95, 3, 2, 43, 4, 2, 43.05, 5, 2, 43.1, 6, 2, 43.15, 0, 2, 43.2, 1, 2, 43.25, 2, 2, 43.3, 3, 2, 43.35, 4, 2, 43.4, 5, 2, 43.45, 6, 2, 43.5, 0, 2, 43.55, 1, 2, 43.6, 2, 2, 43.65, 3, 2, 43.7, 4, 2, 43.75, 5, 2, 43.8, 6, 2, 43.85, 0, 2, 43.9, 1, 2, 43.95, 2, 2, 44, 3, 2, 44.05, 4, 2, 44.1, 5, 2, 44.15, 6, 2, 44.2, 0, 2, 44.25, 1, 2, 44.3, 2, 2, 44.35, 3, 2, 44.4, 4, 2, 44.45, 5, 2, 44.5, 6, 2, 44.55, 0, 2, 44.6, 1, 2, 44.65, 2, 2, 44.7, 3, 2, 44.75, 4, 2, 44.8, 5, 2, 44.85, 6, 2, 44.9, 0, 2, 44.95, 1, 2, 45, 2, 2, 45.05, 3, 2, 45.1, 4, 2, 45.15, 5, 2, 45.2, 6, 2, 45.25, 0, 2, 45.3, 1, 2, 45.35, 2, 2, 45.4, 3, 2, 45.45, 4, 2, 45.5, 5, 2, 45.55, 6, 2, 45.6, 0, 2, 45.65, 1, 2, 45.7, 2, 2, 45.75, 3, 2, 45.8, 4, 2, 45.85, 5, 2, 45.9, 6, 2, 45.95, 0, 2, 46, 1, 2, 46.05, 2, 2, 46.1, 3, 2, 46.15, 4, 2, 46.2, 5, 2, 46.25, 6, 2, 46.3, 0, 2, 46.35, 1, 2, 46.4, 2, 2, 46.45, 3, 2, 46.5, 4, 2, 46.55, 5, 2, 46.6, 6, 2, 46.65, 0, 2, 46.7, 1, 2, 46.75, 2, 2, 46.8, 3, 2, 46.85, 4, 2, 46.9, 5, 2, 46.95, 6, 2, 47, 0, 2, 47.05, 1, 2, 47.1, 2, 2, 47.15, 3, 2, 47.2, 4, 2, 47.25, 5, 2, 47.3, 6, 2, 47.35, 0, 2, 47.4, 1, 2, 47.45, 2, 2, 47.5, 3, 2, 47.55, 4, 2, 47.6, 5, 2, 47.65, 6, 2, 47.7, 0, 2, 47.75, 1, 2, 47.8, 2, 2, 47.85, 3, 2, 47.9, 4, 2, 47.95, 5, 2, 48, 6, 2, 48.05, 0, 2, 48.1, 1, 2, 48.15, 2, 2, 48.2, 3, 2, 48.25, 4, 2, 48.3, 5, 2, 48.35, 6, 2, 48.4, 0, 2, 48.45, 1, 2, 48.5, 2, 2, 48.55, 3, 2, 48.6, 4, 2, 48.65, 5, 2, 48.7, 6, 2, 48.75, 0, 2, 48.8, 1, 2, 48.85, 2, 2, 48.9, 3, 2, 48.95, 4, 2, 49, 5, 2, 49.05, 6, 2, 49.1, 0, 2, 49.15, 1, 2, 49.2, 2, 2, 49.25, 3, 2, 49.3, 4, 2, 49.35, 5, 2, 49.4, 6, 2, 49.45, 0, 2, 49.5, 1, 2, 49.55, 2, 2, 49.6, 3, 2, 49.65, 4, 2, 49.7, 5, 2, 49.75, 6, 2, 49.8, 0, 2, 49.85, 1, 2, 49.9, 2, 2, 49.95, 3, 2, 50, 4, 2, 50.05, 5, 2, 50.1, 6, 2, 50.15, 0, 2, 50.2, 1, 2, 50.25, 2, 2, 50.3, 3, 2, 50.35, 4, 2, 50.4, 5, 2, 50.45, 6, 2, 50.5, 0, 2, 50.55, 1, 2, 50.6, 2, 2, 50.65, 3, 2, 50.7, 4, 2, 50.75, 5, 2, 50.8, 6, 2, 50.85, 0, 2, 50.9, 1, 2, 50.95, 2, 2, 51, 3, 2, 51.05, 4, 2, 51.1, 5, 2, 51.15, 6, 2, 51.2, 0, 2, 51.25, 1, 2, 51.3, 2, 2, 51.35, 3, 2, 51.4, 4, 2, 51.45, 5, 2, 51.5, 6, 2, 51.55, 0, 2, 51.6, 1, 2, 51.65, 2, 2, 51.7, 3, 2, 51.75, 4, 2, 51.8, 5, 2, 51.85, 6, 2, 51.9, 0, 2, 51.95, 1, 2, 52, 2, 2, 52.05, 3, 2, 52.1, 4, 2, 52.15, 5, 2, 52.2, 6, 2, 52.25, 0, 2, 52.3, 1, 2, 52.35, 2, 2, 52.4, 3, 2, 52.45, 4, 2, 52.5, 5, 2, 52.55, 6, 2, 52.6, 0, 2, 52.65, 1, 2, 52.7, 2, 2, 52.75, 3, 2, 52.8, 4, 2, 52.85, 5, 2, 52.9, 6, 2, 52.95, 0, 2, 53, 1, 2, 53.05, 2, 2, 53.1, 3, 2, 53.15, 4, 2, 53.2, 5, 2, 53.25, 6, 2, 53.3, 0, 2, 53.35, 1, 2, 53.4, 2, 2, 53.45, 3, 2, 53.5, 4, 2, 53.55, 5, 2, 53.6, 6, 2, 53.65, 0, 2, 53.7, 1, 2, 53.75, 2, 2, 53.8, 3, 2, 53.85, 4, 2, 53.9, 5, 2, 53.95, 6, 2, 54, 0, 2, 54.05, 1, 2, 54.1, 2, 2, 54.15, 3, 2, 54.2, 4, 2, 54.25, 5, 2, 54.3, 6, 2, 54.35, 0, 2, 54.4, 1, 2, 54.45, 2, 2, 54.5, 3, 2, 54.55, 4, 2, 54.6, 5, 2, 54.65, 6, 2, 54.7, 0, 2, 54.75, 1, 2, 54.8, 2, 2, 54.85, 3, 2, 54.9, 4, 2, 54.95, 5, 2, 55, 6, 2, 55.05, 0, 2, 55.1, 1, 2, 55.15, 2, 2, 55.2, 3, 2, 55.25, 4, 2, 55.3, 5, 2, 55.35, 6, 2, 55.4, 0, 2, 55.45, 1, 2, 55.5, 2, 2, 55.55, 3, 2, 55.6, 4, 2, 55.65, 5, 2, 55.7, 6, 2, 55.75, 0, 2, 55.8, 1, 2, 55.85, 2, 2, 55.9, 3, 2, 55.95, 4, 2, 56, 5, 2, 56.05, 6, 2, 56.1, 0, 2, 56.15, 1, 2, 56.2, 2, 2, 56.25, 3, 2, 56.3, 4, 2, 56.35, 5, 2, 56.4, 6, 2, 56.45, 0, 2, 56.5, 1, 2, 56.55, 2, 2, 56.6, 3, 2, 56.65, 4, 2, 56.7, 5, 2, 56.75, 6, 2, 56.8, 0, 2, 56.85, 1, 2, 56.9, 2, 2, 56.95, 3, 2, 57, 4, 2, 57.05, 5, 2, 57.1, 6, 2, 57.15, 0, 2, 57.2, 1, 2, 57.25, 2, 2, 57.3, 3, 2, 57.35, 4, 2, 57.4, 5, 2, 57.45, 6, 2, 57.5, 0, 2, 57.55, 1, 2, 57.6, 2, 2, 57.65, 3, 2, 57.7, 4, 2, 57.75, 5, 2, 57.8, 6, 2, 57.85, 0, 2, 57.9, 1, 2, 57.95, 2, 2, 58, 3, 2, 58.05, 4, 2, 58.1, 5, 2, 58.15, 6, 2, 58.2, 0, 2, 58.25, 1, 2, 58.3, 2, 2, 58.35, 3, 2, 58.4, 4, 2, 58.45, 5, 2, 58.5, 6, 2, 58.55, 0, 2, 58.6, 1, 2, 58.65, 2, 2, 58.7, 3, 2, 58.75, 4, 2, 58.8, 5, 2, 58.85, 6, 2, 58.9, 0, 2, 58.95, 1, 2, 59, 2, 2, 59.05, 3, 2, 59.1, 4, 2, 59.15, 5, 2, 59.2, 6, 2, 59.25, 0, 2, 59.3, 1, 2, 59.35, 2, 2, 59.4, 3, 2, 59.45, 4, 2, 59.5, 5, 2, 59.55, 6, 2, 59.6, 0, 2, 59.65, 1, 2, 59.7, 2, 2, 59.75, 3, 2, 59.8, 4, 2, 59.85, 5, 2, 59.9, 6, 2, 59.95, 0, 2, 60, 1]}, {"Target": "Parameter", "Id": "Param51", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 6, 2, 0.35, 0, 2, 0.4, 1, 2, 0.45, 2, 2, 0.5, 3, 2, 0.55, 4, 2, 0.6, 5, 2, 0.65, 6, 2, 0.7, 0, 2, 0.75, 1, 2, 0.8, 2, 2, 0.85, 3, 2, 0.9, 4, 2, 0.95, 5, 2, 1, 6, 2, 1.05, 0, 2, 1.1, 1, 2, 1.15, 2, 2, 1.2, 3, 2, 1.25, 4, 2, 1.3, 5, 2, 1.35, 6, 2, 1.4, 0, 2, 1.45, 1, 2, 1.5, 2, 2, 1.55, 3, 2, 1.6, 4, 2, 1.65, 5, 2, 1.7, 6, 2, 1.75, 0, 2, 1.8, 1, 2, 1.85, 2, 2, 1.9, 3, 2, 1.95, 4, 2, 2, 5, 2, 2.05, 6, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 6, 2, 2.45, 0, 2, 2.5, 1, 2, 2.55, 2, 2, 2.6, 3, 2, 2.65, 4, 2, 2.7, 5, 2, 2.75, 6, 2, 2.8, 0, 2, 2.85, 1, 2, 2.9, 2, 2, 2.95, 3, 2, 3, 4, 2, 3.05, 5, 2, 3.1, 6, 2, 3.15, 0, 2, 3.2, 1, 2, 3.25, 2, 2, 3.3, 3, 2, 3.35, 4, 2, 3.4, 5, 2, 3.45, 6, 2, 3.5, 0, 2, 3.55, 1, 2, 3.6, 2, 2, 3.65, 3, 2, 3.7, 4, 2, 3.75, 5, 2, 3.8, 6, 2, 3.85, 0, 2, 3.9, 1, 2, 3.95, 2, 2, 4, 3, 2, 4.05, 4, 2, 4.1, 5, 2, 4.15, 6, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 6, 2, 4.55, 0, 2, 4.6, 1, 2, 4.65, 2, 2, 4.7, 3, 2, 4.75, 4, 2, 4.8, 5, 2, 4.85, 6, 2, 4.9, 0, 2, 4.95, 1, 2, 5, 2, 2, 5.05, 3, 2, 5.1, 4, 2, 5.15, 5, 2, 5.2, 6, 2, 5.25, 0, 2, 5.3, 1, 2, 5.35, 2, 2, 5.4, 3, 2, 5.45, 4, 2, 5.5, 5, 2, 5.55, 6, 2, 5.6, 0, 2, 5.65, 1, 2, 5.7, 2, 2, 5.75, 3, 2, 5.8, 4, 2, 5.85, 5, 2, 5.9, 6, 2, 5.95, 0, 2, 6, 1, 2, 6.05, 2, 2, 6.1, 3, 2, 6.15, 4, 2, 6.2, 5, 2, 6.25, 6, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 6, 2, 6.65, 0, 2, 6.7, 1, 2, 6.75, 2, 2, 6.8, 3, 2, 6.85, 4, 2, 6.9, 5, 2, 6.95, 6, 2, 7, 0, 2, 7.05, 1, 2, 7.1, 2, 2, 7.15, 3, 2, 7.2, 4, 2, 7.25, 5, 2, 7.3, 6, 2, 7.35, 0, 2, 7.4, 1, 2, 7.45, 2, 2, 7.5, 3, 2, 7.55, 4, 2, 7.6, 5, 2, 7.65, 6, 2, 7.7, 0, 2, 7.75, 1, 2, 7.8, 2, 2, 7.85, 3, 2, 7.9, 4, 2, 7.95, 5, 2, 8, 6, 2, 8.05, 0, 2, 8.1, 1, 2, 8.15, 2, 2, 8.2, 3, 2, 8.25, 4, 2, 8.3, 5, 2, 8.35, 6, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 6, 2, 8.75, 0, 2, 8.8, 1, 2, 8.85, 2, 2, 8.9, 3, 2, 8.95, 4, 2, 9, 5, 2, 9.05, 6, 2, 9.1, 0, 2, 9.15, 1, 2, 9.2, 2, 2, 9.25, 3, 2, 9.3, 4, 2, 9.35, 5, 2, 9.4, 6, 2, 9.45, 0, 2, 9.5, 1, 2, 9.55, 2, 2, 9.6, 3, 2, 9.65, 4, 2, 9.7, 5, 2, 9.75, 6, 2, 9.8, 0, 2, 9.85, 1, 2, 9.9, 2, 2, 9.95, 3, 2, 10, 4, 2, 10.05, 5, 2, 10.1, 6, 2, 10.15, 0, 2, 10.2, 1, 2, 10.25, 2, 2, 10.3, 3, 2, 10.35, 4, 2, 10.4, 5, 2, 10.45, 6, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 6, 2, 10.85, 0, 2, 10.9, 1, 2, 10.95, 2, 2, 11, 3, 2, 11.05, 4, 2, 11.1, 5, 2, 11.15, 6, 2, 11.2, 0, 2, 11.25, 1, 2, 11.3, 2, 2, 11.35, 3, 2, 11.4, 4, 2, 11.45, 5, 2, 11.5, 6, 2, 11.55, 0, 2, 11.6, 1, 2, 11.65, 2, 2, 11.7, 3, 2, 11.75, 4, 2, 11.8, 5, 2, 11.85, 6, 2, 11.9, 0, 2, 11.95, 1, 2, 12, 2, 2, 12.05, 3, 2, 12.1, 4, 2, 12.15, 5, 2, 12.2, 6, 2, 12.25, 0, 2, 12.3, 1, 2, 12.35, 2, 2, 12.4, 3, 2, 12.45, 4, 2, 12.5, 5, 2, 12.55, 6, 2, 12.6, 0, 2, 12.65, 1, 2, 12.7, 2, 2, 12.75, 3, 2, 12.8, 4, 2, 12.85, 5, 2, 12.9, 6, 2, 12.95, 0, 2, 13, 1, 2, 13.05, 2, 2, 13.1, 3, 2, 13.15, 4, 2, 13.2, 5, 2, 13.25, 6, 2, 13.3, 0, 2, 13.35, 1, 2, 13.4, 2, 2, 13.45, 3, 2, 13.5, 4, 2, 13.55, 5, 2, 13.6, 6, 2, 13.65, 0, 2, 13.7, 1, 2, 13.75, 2, 2, 13.8, 3, 2, 13.85, 4, 2, 13.9, 5, 2, 13.95, 6, 2, 14, 0, 2, 14.05, 1, 2, 14.1, 2, 2, 14.15, 3, 2, 14.2, 4, 2, 14.25, 5, 2, 14.3, 6, 2, 14.35, 0, 2, 14.4, 1, 2, 14.45, 2, 2, 14.5, 3, 2, 14.55, 4, 2, 14.6, 5, 2, 14.65, 6, 2, 14.7, 0, 2, 14.75, 1, 2, 14.8, 2, 2, 14.85, 3, 2, 14.9, 4, 2, 14.95, 5, 2, 15, 6, 2, 15.05, 0, 2, 15.1, 1, 2, 15.15, 2, 2, 15.2, 3, 2, 15.25, 4, 2, 15.3, 5, 2, 15.35, 6, 2, 15.4, 0, 2, 15.45, 1, 2, 15.5, 2, 2, 15.55, 3, 2, 15.6, 4, 2, 15.65, 5, 2, 15.7, 6, 2, 15.75, 0, 2, 15.8, 1, 2, 15.85, 2, 2, 15.9, 3, 2, 15.95, 4, 2, 16, 5, 2, 16.05, 6, 2, 16.1, 0, 2, 16.15, 1, 2, 16.2, 2, 2, 16.25, 3, 2, 16.3, 4, 2, 16.35, 5, 2, 16.4, 6, 2, 16.45, 0, 2, 16.5, 1, 2, 16.55, 2, 2, 16.6, 3, 2, 16.65, 4, 2, 16.7, 5, 2, 16.75, 6, 2, 16.8, 0, 2, 16.85, 1, 2, 16.9, 2, 2, 16.95, 3, 2, 17, 4, 2, 17.05, 5, 2, 17.1, 6, 2, 17.15, 0, 2, 17.2, 1, 2, 17.25, 2, 2, 17.3, 3, 2, 17.35, 4, 2, 17.4, 5, 2, 17.45, 6, 2, 17.5, 0, 2, 17.55, 1, 2, 17.6, 2, 2, 17.65, 3, 2, 17.7, 4, 2, 17.75, 5, 2, 17.8, 6, 2, 17.85, 0, 2, 17.9, 1, 2, 17.95, 2, 2, 18, 3, 2, 18.05, 4, 2, 18.1, 5, 2, 18.15, 6, 2, 18.2, 0, 2, 18.25, 1, 2, 18.3, 2, 2, 18.35, 3, 2, 18.4, 4, 2, 18.45, 5, 2, 18.5, 6, 2, 18.55, 0, 2, 18.6, 1, 2, 18.65, 2, 2, 18.7, 3, 2, 18.75, 4, 2, 18.8, 5, 2, 18.85, 6, 2, 18.9, 0, 2, 18.95, 1, 2, 19, 2, 2, 19.05, 3, 2, 19.1, 4, 2, 19.15, 5, 2, 19.2, 6, 2, 19.25, 0, 2, 19.3, 1, 2, 19.35, 2, 2, 19.4, 3, 2, 19.45, 4, 2, 19.5, 5, 2, 19.55, 6, 2, 19.6, 0, 2, 19.65, 1, 2, 19.7, 2, 2, 19.75, 3, 2, 19.8, 4, 2, 19.85, 5, 2, 19.9, 6, 2, 19.95, 0, 2, 20, 0, 2, 20.05, 1, 2, 20.1, 2, 2, 20.15, 3, 2, 20.2, 4, 2, 20.25, 5, 2, 20.3, 6, 2, 20.35, 0, 2, 20.4, 1, 2, 20.45, 2, 2, 20.5, 3, 2, 20.55, 4, 2, 20.6, 5, 2, 20.65, 6, 2, 20.7, 0, 2, 20.75, 1, 2, 20.8, 2, 2, 20.85, 3, 2, 20.9, 4, 2, 20.95, 5, 2, 21, 6, 2, 21.05, 0, 2, 21.1, 1, 2, 21.15, 2, 2, 21.2, 3, 2, 21.25, 4, 2, 21.3, 5, 2, 21.35, 6, 2, 21.4, 0, 2, 21.45, 1, 2, 21.5, 2, 2, 21.55, 3, 2, 21.6, 4, 2, 21.65, 5, 2, 21.7, 6, 2, 21.75, 0, 2, 21.8, 1, 2, 21.85, 2, 2, 21.9, 3, 2, 21.95, 4, 2, 22, 5, 2, 22.05, 6, 2, 22.1, 0, 2, 22.15, 1, 2, 22.2, 2, 2, 22.25, 3, 2, 22.3, 4, 2, 22.35, 5, 2, 22.4, 6, 2, 22.45, 0, 2, 22.5, 1, 2, 22.55, 2, 2, 22.6, 3, 2, 22.65, 4, 2, 22.7, 5, 2, 22.75, 6, 2, 22.8, 0, 2, 22.85, 1, 2, 22.9, 2, 2, 22.95, 3, 2, 23, 4, 2, 23.05, 5, 2, 23.1, 6, 2, 23.15, 0, 2, 23.2, 1, 2, 23.25, 2, 2, 23.3, 3, 2, 23.35, 4, 2, 23.4, 5, 2, 23.45, 6, 2, 23.5, 0, 2, 23.55, 1, 2, 23.6, 2, 2, 23.65, 3, 2, 23.7, 4, 2, 23.75, 5, 2, 23.8, 6, 2, 23.85, 0, 2, 23.9, 1, 2, 23.95, 2, 2, 24, 3, 2, 24.05, 4, 2, 24.1, 5, 2, 24.15, 6, 2, 24.2, 0, 2, 24.25, 1, 2, 24.3, 2, 2, 24.35, 3, 2, 24.4, 4, 2, 24.45, 5, 2, 24.5, 6, 2, 24.55, 0, 2, 24.6, 1, 2, 24.65, 2, 2, 24.7, 3, 2, 24.75, 4, 2, 24.8, 5, 2, 24.85, 6, 2, 24.9, 0, 2, 24.95, 1, 2, 25, 2, 2, 25.05, 3, 2, 25.1, 4, 2, 25.15, 5, 2, 25.2, 6, 2, 25.25, 0, 2, 25.3, 1, 2, 25.35, 2, 2, 25.4, 3, 2, 25.45, 4, 2, 25.5, 5, 2, 25.55, 6, 2, 25.6, 0, 2, 25.65, 1, 2, 25.7, 2, 2, 25.75, 3, 2, 25.8, 4, 2, 25.85, 5, 2, 25.9, 6, 2, 25.95, 0, 2, 26, 1, 2, 26.05, 2, 2, 26.1, 3, 2, 26.15, 4, 2, 26.2, 5, 2, 26.25, 6, 2, 26.3, 0, 2, 26.35, 1, 2, 26.4, 2, 2, 26.45, 3, 2, 26.5, 4, 2, 26.55, 5, 2, 26.6, 6, 2, 26.65, 0, 2, 26.7, 1, 2, 26.75, 2, 2, 26.8, 3, 2, 26.85, 4, 2, 26.9, 5, 2, 26.95, 6, 2, 27, 0, 2, 27.05, 1, 2, 27.1, 2, 2, 27.15, 3, 2, 27.2, 4, 2, 27.25, 5, 2, 27.3, 6, 2, 27.35, 0, 2, 27.4, 1, 2, 27.45, 2, 2, 27.5, 3, 2, 27.55, 4, 2, 27.6, 5, 2, 27.65, 6, 2, 27.7, 0, 2, 27.75, 1, 2, 27.8, 2, 2, 27.85, 3, 2, 27.9, 4, 2, 27.95, 5, 2, 28, 6, 2, 28.05, 0, 2, 28.1, 1, 2, 28.15, 2, 2, 28.2, 3, 2, 28.25, 4, 2, 28.3, 5, 2, 28.35, 6, 2, 28.4, 0, 2, 28.45, 1, 2, 28.5, 2, 2, 28.55, 3, 2, 28.6, 4, 2, 28.65, 5, 2, 28.7, 6, 2, 28.75, 0, 2, 28.8, 1, 2, 28.85, 2, 2, 28.9, 3, 2, 28.95, 4, 2, 29, 5, 2, 29.05, 6, 2, 29.1, 0, 2, 29.15, 1, 2, 29.2, 2, 2, 29.25, 3, 2, 29.3, 4, 2, 29.35, 5, 2, 29.4, 6, 2, 29.45, 0, 2, 29.5, 1, 2, 29.55, 2, 2, 29.6, 3, 2, 29.65, 4, 2, 29.7, 5, 2, 29.75, 6, 2, 29.8, 0, 2, 29.85, 1, 2, 29.9, 2, 2, 29.95, 3, 2, 30, 4, 2, 30.05, 5, 2, 30.1, 6, 2, 30.15, 0, 2, 30.2, 1, 2, 30.25, 2, 2, 30.3, 3, 2, 30.35, 4, 2, 30.4, 5, 2, 30.45, 6, 2, 30.5, 0, 2, 30.55, 1, 2, 30.6, 2, 2, 30.65, 3, 2, 30.7, 4, 2, 30.75, 5, 2, 30.8, 6, 2, 30.85, 0, 2, 30.9, 1, 2, 30.95, 2, 2, 31, 3, 2, 31.05, 4, 2, 31.1, 5, 2, 31.15, 6, 2, 31.2, 0, 2, 31.25, 1, 2, 31.3, 2, 2, 31.35, 3, 2, 31.4, 4, 2, 31.45, 5, 2, 31.5, 6, 2, 31.55, 0, 2, 31.6, 1, 2, 31.65, 2, 2, 31.7, 3, 2, 31.75, 4, 2, 31.8, 5, 2, 31.85, 6, 2, 31.9, 0, 2, 31.95, 1, 2, 32, 2, 2, 32.05, 3, 2, 32.1, 4, 2, 32.15, 5, 2, 32.2, 6, 2, 32.25, 0, 2, 32.3, 1, 2, 32.35, 2, 2, 32.4, 3, 2, 32.45, 4, 2, 32.5, 5, 2, 32.55, 6, 2, 32.6, 0, 2, 32.65, 1, 2, 32.7, 2, 2, 32.75, 3, 2, 32.8, 4, 2, 32.85, 5, 2, 32.9, 6, 2, 32.95, 0, 2, 33, 1, 2, 33.05, 2, 2, 33.1, 3, 2, 33.15, 4, 2, 33.2, 5, 2, 33.25, 6, 2, 33.3, 0, 2, 33.35, 1, 2, 33.4, 2, 2, 33.45, 3, 2, 33.5, 4, 2, 33.55, 5, 2, 33.6, 6, 2, 33.65, 0, 2, 33.7, 1, 2, 33.75, 2, 2, 33.8, 3, 2, 33.85, 4, 2, 33.9, 5, 2, 33.95, 6, 2, 34, 0, 2, 34.05, 1, 2, 34.1, 2, 2, 34.15, 3, 2, 34.2, 4, 2, 34.25, 5, 2, 34.3, 6, 2, 34.35, 0, 2, 34.4, 1, 2, 34.45, 2, 2, 34.5, 3, 2, 34.55, 4, 2, 34.6, 5, 2, 34.65, 6, 2, 34.7, 0, 2, 34.75, 1, 2, 34.8, 2, 2, 34.85, 3, 2, 34.9, 4, 2, 34.95, 5, 2, 35, 6, 2, 35.05, 0, 2, 35.1, 1, 2, 35.15, 2, 2, 35.2, 3, 2, 35.25, 4, 2, 35.3, 5, 2, 35.35, 6, 2, 35.4, 0, 2, 35.45, 1, 2, 35.5, 2, 2, 35.55, 3, 2, 35.6, 4, 2, 35.65, 5, 2, 35.7, 6, 2, 35.75, 0, 2, 35.8, 1, 2, 35.85, 2, 2, 35.9, 3, 2, 35.95, 4, 2, 36, 5, 2, 36.05, 6, 2, 36.1, 0, 2, 36.15, 1, 2, 36.2, 2, 2, 36.25, 3, 2, 36.3, 4, 2, 36.35, 5, 2, 36.4, 6, 2, 36.45, 0, 2, 36.5, 1, 2, 36.55, 2, 2, 36.6, 3, 2, 36.65, 4, 2, 36.7, 5, 2, 36.75, 6, 2, 36.8, 0, 2, 36.85, 1, 2, 36.9, 2, 2, 36.95, 3, 2, 37, 4, 2, 37.05, 5, 2, 37.1, 6, 2, 37.15, 0, 2, 37.2, 1, 2, 37.25, 2, 2, 37.3, 3, 2, 37.35, 4, 2, 37.4, 5, 2, 37.45, 6, 2, 37.5, 0, 2, 37.55, 1, 2, 37.6, 2, 2, 37.65, 3, 2, 37.7, 4, 2, 37.75, 5, 2, 37.8, 6, 2, 37.85, 0, 2, 37.9, 1, 2, 37.95, 2, 2, 38, 3, 2, 38.05, 4, 2, 38.1, 5, 2, 38.15, 6, 2, 38.2, 0, 2, 38.25, 1, 2, 38.3, 2, 2, 38.35, 3, 2, 38.4, 4, 2, 38.45, 5, 2, 38.5, 6, 2, 38.55, 0, 2, 38.6, 1, 2, 38.65, 2, 2, 38.7, 3, 2, 38.75, 4, 2, 38.8, 5, 2, 38.85, 6, 2, 38.9, 0, 2, 38.95, 1, 2, 39, 2, 2, 39.05, 3, 2, 39.1, 4, 2, 39.15, 5, 2, 39.2, 6, 2, 39.25, 0, 2, 39.3, 1, 2, 39.35, 2, 2, 39.4, 3, 2, 39.45, 4, 2, 39.5, 5, 2, 39.55, 6, 2, 39.6, 0, 2, 39.65, 1, 2, 39.7, 2, 2, 39.75, 3, 2, 39.8, 4, 2, 39.85, 5, 2, 39.9, 6, 2, 39.95, 0, 2, 40, 0, 2, 40.05, 1, 2, 40.1, 2, 2, 40.15, 3, 2, 40.2, 4, 2, 40.25, 5, 2, 40.3, 6, 2, 40.35, 0, 2, 40.4, 1, 2, 40.45, 2, 2, 40.5, 3, 2, 40.55, 4, 2, 40.6, 5, 2, 40.65, 6, 2, 40.7, 0, 2, 40.75, 1, 2, 40.8, 2, 2, 40.85, 3, 2, 40.9, 4, 2, 40.95, 5, 2, 41, 6, 2, 41.05, 0, 2, 41.1, 1, 2, 41.15, 2, 2, 41.2, 3, 2, 41.25, 4, 2, 41.3, 5, 2, 41.35, 6, 2, 41.4, 0, 2, 41.45, 1, 2, 41.5, 2, 2, 41.55, 3, 2, 41.6, 4, 2, 41.65, 5, 2, 41.7, 6, 2, 41.75, 0, 2, 41.8, 1, 2, 41.85, 2, 2, 41.9, 3, 2, 41.95, 4, 2, 42, 5, 2, 42.05, 6, 2, 42.1, 0, 2, 42.15, 1, 2, 42.2, 2, 2, 42.25, 3, 2, 42.3, 4, 2, 42.35, 5, 2, 42.4, 6, 2, 42.45, 0, 2, 42.5, 1, 2, 42.55, 2, 2, 42.6, 3, 2, 42.65, 4, 2, 42.7, 5, 2, 42.75, 6, 2, 42.8, 0, 2, 42.85, 1, 2, 42.9, 2, 2, 42.95, 3, 2, 43, 4, 2, 43.05, 5, 2, 43.1, 6, 2, 43.15, 0, 2, 43.2, 1, 2, 43.25, 2, 2, 43.3, 3, 2, 43.35, 4, 2, 43.4, 5, 2, 43.45, 6, 2, 43.5, 0, 2, 43.55, 1, 2, 43.6, 2, 2, 43.65, 3, 2, 43.7, 4, 2, 43.75, 5, 2, 43.8, 6, 2, 43.85, 0, 2, 43.9, 1, 2, 43.95, 2, 2, 44, 3, 2, 44.05, 4, 2, 44.1, 5, 2, 44.15, 6, 2, 44.2, 0, 2, 44.25, 1, 2, 44.3, 2, 2, 44.35, 3, 2, 44.4, 4, 2, 44.45, 5, 2, 44.5, 6, 2, 44.55, 0, 2, 44.6, 1, 2, 44.65, 2, 2, 44.7, 3, 2, 44.75, 4, 2, 44.8, 5, 2, 44.85, 6, 2, 44.9, 0, 2, 44.95, 1, 2, 45, 2, 2, 45.05, 3, 2, 45.1, 4, 2, 45.15, 5, 2, 45.2, 6, 2, 45.25, 0, 2, 45.3, 1, 2, 45.35, 2, 2, 45.4, 3, 2, 45.45, 4, 2, 45.5, 5, 2, 45.55, 6, 2, 45.6, 0, 2, 45.65, 1, 2, 45.7, 2, 2, 45.75, 3, 2, 45.8, 4, 2, 45.85, 5, 2, 45.9, 6, 2, 45.95, 0, 2, 46, 1, 2, 46.05, 2, 2, 46.1, 3, 2, 46.15, 4, 2, 46.2, 5, 2, 46.25, 6, 2, 46.3, 0, 2, 46.35, 1, 2, 46.4, 2, 2, 46.45, 3, 2, 46.5, 4, 2, 46.55, 5, 2, 46.6, 6, 2, 46.65, 0, 2, 46.7, 1, 2, 46.75, 2, 2, 46.8, 3, 2, 46.85, 4, 2, 46.9, 5, 2, 46.95, 6, 2, 47, 0, 2, 47.05, 1, 2, 47.1, 2, 2, 47.15, 3, 2, 47.2, 4, 2, 47.25, 5, 2, 47.3, 6, 2, 47.35, 0, 2, 47.4, 1, 2, 47.45, 2, 2, 47.5, 3, 2, 47.55, 4, 2, 47.6, 5, 2, 47.65, 6, 2, 47.7, 0, 2, 47.75, 1, 2, 47.8, 2, 2, 47.85, 3, 2, 47.9, 4, 2, 47.95, 5, 2, 48, 6, 2, 48.05, 0, 2, 48.1, 1, 2, 48.15, 2, 2, 48.2, 3, 2, 48.25, 4, 2, 48.3, 5, 2, 48.35, 6, 2, 48.4, 0, 2, 48.45, 1, 2, 48.5, 2, 2, 48.55, 3, 2, 48.6, 4, 2, 48.65, 5, 2, 48.7, 6, 2, 48.75, 0, 2, 48.8, 1, 2, 48.85, 2, 2, 48.9, 3, 2, 48.95, 4, 2, 49, 5, 2, 49.05, 6, 2, 49.1, 0, 2, 49.15, 1, 2, 49.2, 2, 2, 49.25, 3, 2, 49.3, 4, 2, 49.35, 5, 2, 49.4, 6, 2, 49.45, 0, 2, 49.5, 1, 2, 49.55, 2, 2, 49.6, 3, 2, 49.65, 4, 2, 49.7, 5, 2, 49.75, 6, 2, 49.8, 0, 2, 49.85, 1, 2, 49.9, 2, 2, 49.95, 3, 2, 50, 4, 2, 50.05, 5, 2, 50.1, 6, 2, 50.15, 0, 2, 50.2, 1, 2, 50.25, 2, 2, 50.3, 3, 2, 50.35, 4, 2, 50.4, 5, 2, 50.45, 6, 2, 50.5, 0, 2, 50.55, 1, 2, 50.6, 2, 2, 50.65, 3, 2, 50.7, 4, 2, 50.75, 5, 2, 50.8, 6, 2, 50.85, 0, 2, 50.9, 1, 2, 50.95, 2, 2, 51, 3, 2, 51.05, 4, 2, 51.1, 5, 2, 51.15, 6, 2, 51.2, 0, 2, 51.25, 1, 2, 51.3, 2, 2, 51.35, 3, 2, 51.4, 4, 2, 51.45, 5, 2, 51.5, 6, 2, 51.55, 0, 2, 51.6, 1, 2, 51.65, 2, 2, 51.7, 3, 2, 51.75, 4, 2, 51.8, 5, 2, 51.85, 6, 2, 51.9, 0, 2, 51.95, 1, 2, 52, 2, 2, 52.05, 3, 2, 52.1, 4, 2, 52.15, 5, 2, 52.2, 6, 2, 52.25, 0, 2, 52.3, 1, 2, 52.35, 2, 2, 52.4, 3, 2, 52.45, 4, 2, 52.5, 5, 2, 52.55, 6, 2, 52.6, 0, 2, 52.65, 1, 2, 52.7, 2, 2, 52.75, 3, 2, 52.8, 4, 2, 52.85, 5, 2, 52.9, 6, 2, 52.95, 0, 2, 53, 1, 2, 53.05, 2, 2, 53.1, 3, 2, 53.15, 4, 2, 53.2, 5, 2, 53.25, 6, 2, 53.3, 0, 2, 53.35, 1, 2, 53.4, 2, 2, 53.45, 3, 2, 53.5, 4, 2, 53.55, 5, 2, 53.6, 6, 2, 53.65, 0, 2, 53.7, 1, 2, 53.75, 2, 2, 53.8, 3, 2, 53.85, 4, 2, 53.9, 5, 2, 53.95, 6, 2, 54, 0, 2, 54.05, 1, 2, 54.1, 2, 2, 54.15, 3, 2, 54.2, 4, 2, 54.25, 5, 2, 54.3, 6, 2, 54.35, 0, 2, 54.4, 1, 2, 54.45, 2, 2, 54.5, 3, 2, 54.55, 4, 2, 54.6, 5, 2, 54.65, 6, 2, 54.7, 0, 2, 54.75, 1, 2, 54.8, 2, 2, 54.85, 3, 2, 54.9, 4, 2, 54.95, 5, 2, 55, 6, 2, 55.05, 0, 2, 55.1, 1, 2, 55.15, 2, 2, 55.2, 3, 2, 55.25, 4, 2, 55.3, 5, 2, 55.35, 6, 2, 55.4, 0, 2, 55.45, 1, 2, 55.5, 2, 2, 55.55, 3, 2, 55.6, 4, 2, 55.65, 5, 2, 55.7, 6, 2, 55.75, 0, 2, 55.8, 1, 2, 55.85, 2, 2, 55.9, 3, 2, 55.95, 4, 2, 56, 5, 2, 56.05, 6, 2, 56.1, 0, 2, 56.15, 1, 2, 56.2, 2, 2, 56.25, 3, 2, 56.3, 4, 2, 56.35, 5, 2, 56.4, 6, 2, 56.45, 0, 2, 56.5, 1, 2, 56.55, 2, 2, 56.6, 3, 2, 56.65, 4, 2, 56.7, 5, 2, 56.75, 6, 2, 56.8, 0, 2, 56.85, 1, 2, 56.9, 2, 2, 56.95, 3, 2, 57, 4, 2, 57.05, 5, 2, 57.1, 6, 2, 57.15, 0, 2, 57.2, 1, 2, 57.25, 2, 2, 57.3, 3, 2, 57.35, 4, 2, 57.4, 5, 2, 57.45, 6, 2, 57.5, 0, 2, 57.55, 1, 2, 57.6, 2, 2, 57.65, 3, 2, 57.7, 4, 2, 57.75, 5, 2, 57.8, 6, 2, 57.85, 0, 2, 57.9, 1, 2, 57.95, 2, 2, 58, 3, 2, 58.05, 4, 2, 58.1, 5, 2, 58.15, 6, 2, 58.2, 0, 2, 58.25, 1, 2, 58.3, 2, 2, 58.35, 3, 2, 58.4, 4, 2, 58.45, 5, 2, 58.5, 6, 2, 58.55, 0, 2, 58.6, 1, 2, 58.65, 2, 2, 58.7, 3, 2, 58.75, 4, 2, 58.8, 5, 2, 58.85, 6, 2, 58.9, 0, 2, 58.95, 1, 2, 59, 2, 2, 59.05, 3, 2, 59.1, 4, 2, 59.15, 5, 2, 59.2, 6, 2, 59.25, 0, 2, 59.3, 1, 2, 59.35, 2, 2, 59.4, 3, 2, 59.45, 4, 2, 59.5, 5, 2, 59.55, 6, 2, 59.6, 0, 2, 59.65, 1, 2, 59.7, 2, 2, 59.75, 3, 2, 59.8, 4, 2, 59.85, 5, 2, 59.9, 6, 2, 59.95, 0, 2, 60, 1]}, {"Target": "Parameter", "Id": "Param42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 20, 0, 2, 40, 0, 1, 46.667, 0.074, 53.333, 0.148, 60, 0.222]}, {"Target": "Parameter", "Id": "Param45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.4, 2, 20, -0.4, 2, 40, -0.4, 1, 46.667, -0.326, 53.333, -0.252, 60, -0.178]}, {"Target": "Parameter", "Id": "Param46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.3, 2, 20, 0.3, 2, 40, 0.3, 1, 46.667, 0.374, 53.333, 0.448, 60, 0.522]}, {"Target": "Parameter", "Id": "Param50", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 20, 0, 2, 40, 0, 1, 46.667, 1.333, 53.333, 2.667, 60, 4]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2.5, 10, 0, 5.017, -10, 0, 7.5, 10, 0, 10, -10, 0, 12.5, 26.92, 0, 15.017, -10, 0, 17.5, 10, 0, 20, -10, 0, 22.5, 10, 0, 25.017, -10, 0, 27.5, 10, 0, 30, -10, 0, 32.5, 26.92, 0, 35.017, -10, 0, 37.5, 10, 0, 40, -10, 0, 42.5, 10, 0, 45.017, -10, 0, 47.5, 10, 0, 50, -10, 0, 52.5, 26.92, 0, 55.017, -10, 0, 57.5, 10, 0, 60, -10]}, {"Target": "Parameter", "Id": "Param17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.76, 0, 2.5, 0.66, 0, 5.017, -5.76, 0, 7.5, 0.66, 0, 10, -5.76, 0, 12.5, 21.06, 0, 15.017, -5.76, 0, 17.5, 0.66, 0, 20, -5.76, 0, 22.5, 0.66, 0, 25.017, -5.76, 0, 27.5, 0.66, 0, 30, -5.76, 0, 32.5, 21.06, 0, 35.017, -5.76, 0, 37.5, 0.66, 0, 40, -5.76, 0, 42.5, 0.66, 0, 45.017, -5.76, 0, 47.5, 0.66, 0, 50, -5.76, 0, 52.5, 21.06, 0, 55.017, -5.76, 0, 57.5, 0.66, 0, 60, -5.76]}, {"Target": "Parameter", "Id": "Param49", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.35, 0, 1.25, 0, 0, 3.75, 0.7, 0, 6.267, 0, 0, 8.767, 0.7, 0, 11.283, 0, 0, 13.783, 0.7, 0, 16.3, 0, 0, 18.8, 0.7, 1, 19.2, 0.7, 19.6, 0.578, 20, 0.35, 1, 20.417, 0.112, 20.833, 0, 21.25, 0, 0, 23.75, 0.7, 0, 26.267, 0, 0, 28.767, 0.7, 0, 31.283, 0, 0, 33.783, 0.7, 0, 36.3, 0, 0, 38.8, 0.7, 1, 39.2, 0.7, 39.6, 0.578, 40, 0.35, 1, 40.417, 0.112, 40.833, 0, 41.25, 0, 0, 43.75, 0.7, 0, 46.267, 0, 0, 48.767, 0.7, 0, 51.283, 0, 0, 53.783, 0.7, 0, 56.3, 0, 0, 58.8, 0.7, 0, 60, 0.374]}, {"Target": "Parameter", "Id": "Param40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.25, 0, 0.917, 10, 0, 2.917, -10, 0, 4.917, 10, 0, 6.917, -10, 0, 8.917, 10, 0, 10.917, -10, 0, 12.917, 10, 0, 14.917, -10, 0, 16.917, 10, 0, 18.917, -10, 1, 19.278, -10, 19.639, -5.959, 20, 1.25, 1, 20.306, 7.35, 20.611, 10, 20.917, 10, 0, 22.917, -10, 0, 24.917, 10, 0, 26.917, -10, 0, 28.917, 10, 0, 30.917, -10, 0, 32.917, 10, 0, 34.917, -10, 0, 36.917, 10, 0, 38.917, -10, 1, 39.278, -10, 39.639, -5.959, 40, 1.25, 1, 40.306, 7.35, 40.611, 10, 40.917, 10, 0, 42.917, -10, 0, 44.917, 10, 0, 46.917, -10, 0, 48.917, 10, 0, 50.917, -10, 0, 52.917, 10, 0, 54.917, -10, 0, 56.917, 10, 0, 58.917, -10, 0, 60, 1.25]}, {"Target": "Parameter", "Id": "Param41", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2, 10, 0, 4, -10, 0, 6, 10, 0, 8, -10, 0, 10, 10, 0, 12, -10, 0, 14, 10, 0, 16, -10, 0, 18, 10, 0, 20, -10, 0, 22, 10, 0, 24, -10, 0, 26, 10, 0, 28, -10, 0, 30, 10, 0, 32, -10, 0, 34, 10, 0, 36, -10, 0, 38, 10, 0, 40, -10, 0, 42, 10, 0, 44, -10, 0, 46, 10, 0, 48, -10, 0, 50, 10, 0, 52, -10, 0, 54, 10, 0, 56, -10, 0, 58, 10, 0, 60, -10]}, {"Target": "Parameter", "Id": "Param32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2, 10, 0, 4, -10, 0, 6, 10, 0, 8, -10, 0, 10, 10, 0, 12, -10, 0, 14, 10, 0, 16, -10, 0, 18, 10, 0, 20, -10, 0, 22, 10, 0, 24, -10, 0, 26, 10, 0, 28, -10, 0, 30, 10, 0, 32, -10, 0, 34, 10, 0, 36, -10, 0, 38, 10, 0, 40, -10, 0, 42, 10, 0, 44, -10, 0, 46, 10, 0, 48, -10, 0, 50, 10, 0, 52, -10, 0, 54, 10, 0, 56, -10, 0, 58, 10, 0, 60, -10]}, {"Target": "Parameter", "Id": "Param33", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.886, 0, 0.583, 10, 0, 2.583, -10, 0, 4.583, 10, 0, 6.583, -10, 0, 8.583, 10, 0, 10.583, -10, 0, 12.583, 10, 0, 14.583, -10, 0, 16.583, 10, 0, 18.583, -10, 1, 19.055, -10, 19.528, -3.137, 20, 5.886, 1, 20.194, 9.602, 20.389, 10, 20.583, 10, 0, 22.583, -10, 0, 24.583, 10, 0, 26.583, -10, 0, 28.583, 10, 0, 30.583, -10, 0, 32.583, 10, 0, 34.583, -10, 0, 36.583, 10, 0, 38.583, -10, 1, 39.055, -10, 39.528, -3.137, 40, 5.886, 1, 40.194, 9.602, 40.389, 10, 40.583, 10, 0, 42.583, -10, 0, 44.583, 10, 0, 46.583, -10, 0, 48.583, 10, 0, 50.583, -10, 0, 52.583, 10, 0, 54.583, -10, 0, 56.583, 10, 0, 58.583, -10, 0, 60, 5.886]}, {"Target": "Parameter", "Id": "Param34", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -14.451, 0, 1.333, 30, 0, 3.333, -30, 0, 5.333, 30, 0, 7.333, -30, 0, 9.333, 30, 0, 11.333, -30, 0, 13.333, 30, 0, 15.333, -30, 0, 17.333, 30, 0, 19.333, -30, 1, 19.555, -30, 19.778, -27.362, 20, -14.451, 1, 20.444, 11.37, 20.889, 30, 21.333, 30, 0, 23.333, -30, 0, 25.333, 30, 0, 27.333, -30, 0, 29.333, 30, 0, 31.333, -30, 0, 33.333, 30, 0, 35.333, -30, 0, 37.333, 30, 0, 39.333, -30, 1, 39.555, -30, 39.778, -27.362, 40, -14.451, 1, 40.444, 11.37, 40.889, 30, 41.333, 30, 0, 43.333, -30, 0, 45.333, 30, 0, 47.333, -30, 0, 49.333, 30, 0, 51.333, -30, 0, 53.333, 30, 0, 55.333, -30, 0, 57.333, 30, 0, 59.333, -30, 0, 60, -14.451]}, {"Target": "Parameter", "Id": "Param28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -15, 0, 2, 15, 0, 4, -15, 0, 6, 15, 0, 8, -15, 0, 10, 15, 0, 12, -15, 0, 14, 15, 0, 16, -15, 0, 18, 15, 0, 20, -15, 0, 22, 15, 0, 24, -15, 0, 26, 15, 0, 28, -15, 0, 30, 15, 0, 32, -15, 0, 34, 15, 0, 36, -15, 0, 38, 15, 0, 40, -15, 0, 42, 15, 0, 44, -15, 0, 46, 15, 0, 48, -15, 0, 50, 15, 0, 52, -15, 0, 54, 15, 0, 56, -15, 0, 58, 15, 0, 60, -15]}, {"Target": "Parameter", "Id": "Param29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.886, 0, 0.583, 10, 0, 2.583, -10, 0, 4.583, 10, 0, 6.583, -10, 0, 8.583, 10, 0, 10.583, -10, 0, 12.583, 10, 0, 14.583, -10, 0, 16.583, 10, 0, 18.583, -10, 1, 19.055, -10, 19.528, -3.137, 20, 5.886, 1, 20.194, 9.602, 20.389, 10, 20.583, 10, 0, 22.583, -10, 0, 24.583, 10, 0, 26.583, -10, 0, 28.583, 10, 0, 30.583, -10, 0, 32.583, 10, 0, 34.583, -10, 0, 36.583, 10, 0, 38.583, -10, 1, 39.055, -10, 39.528, -3.137, 40, 5.886, 1, 40.194, 9.602, 40.389, 10, 40.583, 10, 0, 42.583, -10, 0, 44.583, 10, 0, 46.583, -10, 0, 48.583, 10, 0, 50.583, -10, 0, 52.583, 10, 0, 54.583, -10, 0, 56.583, 10, 0, 58.583, -10, 0, 60, 5.886]}, {"Target": "Parameter", "Id": "Param35", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -14.451, 0, 1.333, 30, 0, 3.333, -30, 0, 5.333, 30, 0, 7.333, -30, 0, 9.333, 30, 0, 11.333, -30, 0, 13.333, 30, 0, 15.333, -30, 0, 17.333, 30, 0, 19.333, -30, 1, 19.555, -30, 19.778, -27.362, 20, -14.451, 1, 20.444, 11.37, 20.889, 30, 21.333, 30, 0, 23.333, -30, 0, 25.333, 30, 0, 27.333, -30, 0, 29.333, 30, 0, 31.333, -30, 0, 33.333, 30, 0, 35.333, -30, 0, 37.333, 30, 0, 39.333, -30, 1, 39.555, -30, 39.778, -27.362, 40, -14.451, 1, 40.444, 11.37, 40.889, 30, 41.333, 30, 0, 43.333, -30, 0, 45.333, 30, 0, 47.333, -30, 0, 49.333, 30, 0, 51.333, -30, 0, 53.333, 30, 0, 55.333, -30, 0, 57.333, 30, 0, 59.333, -30, 0, 60, -14.451]}, {"Target": "Parameter", "Id": "Param30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.959, 0, 0.8, 10, 0, 2.8, -10, 0, 4.8, 10, 0, 6.8, -10, 0, 8.8, 10, 0, 10.8, -10, 0, 12.8, 10, 0, 14.8, -10, 0, 16.8, 10, 0, 18.8, -10, 1, 19.2, -10, 19.6, -4.959, 20, 2.959, 1, 20.267, 8.237, 20.533, 10, 20.8, 10, 0, 22.8, -10, 0, 24.8, 10, 0, 26.8, -10, 0, 28.8, 10, 0, 30.8, -10, 0, 32.8, 10, 0, 34.8, -10, 0, 36.8, 10, 0, 38.8, -10, 1, 39.2, -10, 39.6, -4.959, 40, 2.959, 1, 40.267, 8.237, 40.533, 10, 40.8, 10, 0, 42.8, -10, 0, 44.8, 10, 0, 46.8, -10, 0, 48.8, 10, 0, 50.8, -10, 0, 52.8, 10, 0, 54.8, -10, 0, 56.8, 10, 0, 58.8, -10, 0, 60, 2.959]}, {"Target": "Parameter", "Id": "Param31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 9.44, 0, 1.8, -10, 0, 3.8, 10, 0, 5.8, -10, 0, 7.8, 10, 0, 9.8, -10, 0, 11.8, 10, 0, 13.8, -10, 0, 15.8, 10, 0, 17.8, -10, 0, 19.8, 10, 1, 19.867, 10, 19.933, 10.555, 20, 9.44, 1, 20.6, -0.593, 21.2, -10, 21.8, -10, 0, 23.8, 10, 0, 25.8, -10, 0, 27.8, 10, 0, 29.8, -10, 0, 31.8, 10, 0, 33.8, -10, 0, 35.8, 10, 0, 37.8, -10, 0, 39.8, 10, 1, 39.867, 10, 39.933, 10.555, 40, 9.44, 1, 40.6, -0.593, 41.2, -10, 41.8, -10, 0, 43.8, 10, 0, 45.8, -10, 0, 47.8, 10, 0, 49.8, -10, 0, 51.8, 10, 0, 53.8, -10, 0, 55.8, 10, 0, 57.8, -10, 0, 59.8, 10, 0, 60, 9.44]}, {"Target": "Parameter", "Id": "Param36", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 13.773, 0, 0.683, 30, 0, 2.683, -30, 0, 4.683, 30, 0, 6.683, -30, 0, 8.683, 30, 0, 10.683, -30, 0, 12.683, 30, 0, 14.683, -30, 0, 16.683, 30, 0, 18.683, -30, 1, 19.122, -30, 19.561, -11.809, 20, 13.773, 1, 20.228, 27.05, 20.455, 30, 20.683, 30, 0, 22.683, -30, 0, 24.683, 30, 0, 26.683, -30, 0, 28.683, 30, 0, 30.683, -30, 0, 32.683, 30, 0, 34.683, -30, 0, 36.683, 30, 0, 38.683, -30, 1, 39.122, -30, 39.561, -11.809, 40, 13.773, 1, 40.228, 27.05, 40.455, 30, 40.683, 30, 0, 42.683, -30, 0, 44.683, 30, 0, 46.683, -30, 0, 48.683, 30, 0, 50.683, -30, 0, 52.683, 30, 0, 54.683, -30, 0, 56.683, 30, 0, 58.683, -30, 0, 60, 13.773]}, {"Target": "Parameter", "Id": "GL1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 20, 0, 2, 40, 0, 1, 46.667, 0.297, 53.333, 0.593, 60, 0.89]}, {"Target": "Parameter", "Id": "GL2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.39, 2, 20, 0.39, 2, 40, 0.39, 1, 46.667, 0.687, 53.333, 0.983, 60, 1.28]}, {"Target": "Parameter", "Id": "GL3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.72, 2, 20, 0.72, 2, 40, 0.72, 1, 46.667, 1.017, 53.333, 1.313, 60, 1.61]}, {"Target": "Parameter", "Id": "GL4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.21, 2, 20, 0.21, 2, 40, 0.21, 1, 46.667, 0.507, 53.333, 0.803, 60, 1.1]}, {"Target": "Parameter", "Id": "GL5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.53, 2, 20, 0.53, 2, 40, 0.53, 1, 46.667, 0.827, 53.333, 1.123, 60, 1.42]}, {"Target": "Parameter", "Id": "GL6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.89, 2, 20, 0.89, 2, 40, 0.89, 1, 46.667, 1.187, 53.333, 1.483, 60, 1.78]}, {"Target": "Parameter", "Id": "GL7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.77, 2, 20, 0.77, 2, 40, 0.77, 1, 46.667, 1.067, 53.333, 1.363, 60, 1.66]}, {"Target": "Parameter", "Id": "GL8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.09, 2, 20, 0.09, 2, 40, 0.09, 1, 46.667, 0.387, 53.333, 0.683, 60, 0.98]}, {"Target": "Parameter", "Id": "GL9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.301, 2, 20, 0.301, 2, 40, 0.301, 1, 46.667, 0.598, 53.333, 0.894, 60, 1.191]}, {"Target": "Parameter", "Id": "GL10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.45, 2, 20, 0.45, 2, 40, 0.45, 1, 46.667, 0.747, 53.333, 1.043, 60, 1.34]}, {"Target": "Parameter", "Id": "GL11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.64, 2, 20, 0.64, 2, 40, 0.64, 1, 46.667, 0.937, 53.333, 1.233, 60, 1.53]}, {"Target": "Parameter", "Id": "GL12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.15, 2, 20, 0.15, 2, 40, 0.15, 1, 46.667, 0.447, 53.333, 0.743, 60, 1.04]}, {"Target": "Parameter", "Id": "GL13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.585, 2, 20, 0.585, 2, 40, 0.585, 1, 46.667, 0.882, 53.333, 1.178, 60, 1.475]}, {"Target": "Parameter", "Id": "GL14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 20, 0, 2, 40, 0, 1, 46.667, 0.297, 53.333, 0.593, 60, 0.89]}, {"Target": "Parameter", "Id": "Param161", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.51, 1, 5.994, 0.843, 11.989, 1.177, 17.983, 1.51, 2, 18, 0.51, 2, 20, 0.51, 1, 25.994, 0.843, 31.989, 1.177, 37.983, 1.51, 2, 38, 0.51, 2, 40, 0.51, 1, 45.994, 0.843, 51.989, 1.177, 57.983, 1.51, 2, 58, 0.51, 1, 58.667, 0.547, 59.333, 0.584, 60, 0.621]}, {"Target": "Parameter", "Id": "Param162", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.31, 1, 5.994, 0.643, 11.989, 0.977, 17.983, 1.31, 2, 18, 0.31, 2, 20, 0.31, 1, 25.994, 0.643, 31.989, 0.977, 37.983, 1.31, 2, 38, 0.31, 2, 40, 0.31, 1, 45.994, 0.643, 51.989, 0.977, 57.983, 1.31, 2, 58, 0.31, 1, 58.667, 0.347, 59.333, 0.384, 60, 0.421]}, {"Target": "Parameter", "Id": "Param163", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.72, 1, 5.994, 1.053, 11.989, 1.387, 17.983, 1.72, 2, 18, 0.72, 2, 20, 0.72, 1, 25.994, 1.053, 31.989, 1.387, 37.983, 1.72, 2, 38, 0.72, 2, 40, 0.72, 1, 45.994, 1.053, 51.989, 1.387, 57.983, 1.72, 2, 58, 0.72, 1, 58.667, 0.757, 59.333, 0.794, 60, 0.831]}, {"Target": "Parameter", "Id": "Param164", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 5.994, 0.333, 11.989, 0.667, 17.983, 1, 2, 18, 0, 2, 20, 0, 1, 25.994, 0.333, 31.989, 0.667, 37.983, 1, 2, 38, 0, 2, 40, 0, 1, 45.994, 0.333, 51.989, 0.667, 57.983, 1, 2, 58, 0, 1, 58.667, 0.037, 59.333, 0.074, 60, 0.111]}, {"Target": "Parameter", "Id": "Param165", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.17, 1, 5.994, 0.503, 11.989, 0.837, 17.983, 1.17, 2, 18, 0.17, 2, 20, 0.17, 1, 25.994, 0.503, 31.989, 0.837, 37.983, 1.17, 2, 38, 0.17, 2, 40, 0.17, 1, 45.994, 0.503, 51.989, 0.837, 57.983, 1.17, 2, 58, 0.17, 1, 58.667, 0.207, 59.333, 0.244, 60, 0.281]}, {"Target": "Parameter", "Id": "Param119", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 20.967, 0, 0, 24.817, 2, 2, 50.983, 0, 0, 54.833, 2, 2, 56.8, 0, 2, 60, 0]}, {"Target": "Parameter", "Id": "Param23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.238, 1, 7.617, 0.492, 15.233, 0.746, 22.85, 1, 2, 22.867, 0, 1, 32.867, 0.333, 42.867, 0.667, 52.867, 1, 2, 52.883, 0, 1, 55.255, 0.079, 57.628, 0.159, 60, 0.238]}, {"Target": "Parameter", "Id": "touch_drag21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param54", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param118", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 3, 0, 60, 3]}, {"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "All_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "All_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 3.77, 0, 60, 3.77]}, {"Target": "Parameter", "Id": "man_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 15, 0, 60, 15]}, {"Target": "Parameter", "Id": "man_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param78", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param79", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "man_<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "man_<PERSON>ze", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param189", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "shangguang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param186", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param96", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param97", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param98", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param111", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "shangguang2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param53", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param99", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 60, 1]}, {"Target": "Parameter", "Id": "Param76", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 60, 1]}, {"Target": "Parameter", "Id": "Param120", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 60, 1]}, {"Target": "Parameter", "Id": "Param77", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "MB_xingfengTMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param127", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param103", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param104", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param106", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param105", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param110", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param130", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param107", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param108", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param109", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Mouthfunnel", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param293", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param72", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param74", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param75", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param73", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "MB_fenweiTMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "MB_fenweiliangdu", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 60, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiandu", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 60, 0.6]}, {"Target": "Parameter", "Id": "MB_fenweiqiu1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 60, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 60, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 60, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 60, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 60, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 60, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiushangxia", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "MB_fenweiqiudaxiao", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param102", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 15, 0, 60, 15]}, {"Target": "Parameter", "Id": "Param58", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -14, 0, 60, -14]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -23.56, 0, 60, -23.56]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "ParamHandRREDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 4, 0, 60, 4]}, {"Target": "Parameter", "Id": "Param101", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param55", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -30, 0, 60, -30]}, {"Target": "Parameter", "Id": "ParamForearmLChange2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 30, 0, 60, 30]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "ParamHandLREDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 60, 1]}, {"Target": "Parameter", "Id": "Param100", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param167", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param169", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param196", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param56", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param59", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10, 0, 60, 10]}, {"Target": "Parameter", "Id": "Param3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param57", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param124", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param80", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param48", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param37", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param38", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param60", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param47", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param81", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param95", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param125", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param171", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param166", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param123", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param39", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.4, 0, 60, 0.4]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param61", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 60, 1]}, {"Target": "Parameter", "Id": "Param71", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 60, 1]}, {"Target": "Parameter", "Id": "Param69", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param173", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param174", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param175", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param176", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param190", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param191", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param192", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param193", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param65", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 60, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "ParamShoulderStretch3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "ParamShoulderStretch4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "ParamChestZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "ParamWaistZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "ParamButtZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param66", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param67", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param68", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param62", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param63", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param64", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param87", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param94", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param52", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param70", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param208", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param112", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param115", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param113", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param116", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param114", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "Param117", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "touch_drag23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "touch_drag24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "touch_drag25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "touch_drag26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "touch_drag27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "touch_drag28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "touch_drag29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "touch_drag30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "touch_drag31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "touch_drag32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "empty1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "empty2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "empty3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "empty4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "empty5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "empty6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "empty7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "empty8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "empty9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "empty10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "empty11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "empty12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "empty13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "empty14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "empty15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "empty16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "empty17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "empty18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "empty19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "empty20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "empty21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "empty22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "Parameter", "Id": "empty23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 60, 0]}, {"Target": "PartOpacity", "Id": "Part7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 60, 1]}], "UserData": []}