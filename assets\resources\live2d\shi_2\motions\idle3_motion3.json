{"Version": 3, "Meta": {"Duration": 22.0, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 279, "TotalSegmentCount": 2164, "TotalPointCount": 2300, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "All_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.227, 1, 0.617, -1.547, 1.233, 3.734, 1.85, 3.734, 0, 4.05, -3.734, 0, 6.25, 3.734, 0, 8.45, -3.734, 0, 10.65, 3.734, 0, 12.85, -3.734, 0, 15.05, 3.734, 0, 17.25, -3.734, 0, 19.45, 3.734, 0, 21.65, -3.734, 1, 21.767, -3.734, 21.883, -3.545, 22, -3.227]}, {"Target": "Parameter", "Id": "All_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.02, 1, 0.228, 3.013, 0.455, 3.734, 0.683, 3.734, 0, 2.883, -3.734, 0, 5.083, 3.734, 0, 7.283, -3.734, 0, 9.483, 3.734, 0, 11.683, -3.734, 0, 13.883, 3.734, 0, 16.083, -3.734, 0, 18.283, 3.734, 0, 20.483, -3.734, 1, 20.989, -3.734, 21.494, -0.185, 22, 2.02]}, {"Target": "Parameter", "Id": "Param26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -6.39, 0, 2.2, 6.39, 0, 4.4, -6.39, 0, 6.6, 6.39, 0, 8.8, -6.39, 0, 11, 6.39, 0, 13.2, -6.39, 0, 15.4, 6.39, 0, 17.6, -6.39, 0, 19.8, 6.39, 0, 22, -6.39]}, {"Target": "Parameter", "Id": "ParamAngleX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.928, 0, 0.617, -0.52, 0, 2.867, -4.777, 0, 5.033, -0.058, 0, 7.3, -4.562, 0, 9.417, 0.134, 0, 11.65, -4.115, 0, 13.817, -0.012, 0, 16, -4.032, 0, 18.217, -0.361, 0, 20.467, -4.843, 0, 22, -1.928]}, {"Target": "Parameter", "Id": "ParamAngleY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.509, 0, 1.3, -2.884, 0, 3.517, 5.435, 0, 5.667, -2.678, 0, 7.817, 6.88, 0, 10.117, -1.728, 0, 12.267, 6.407, 0, 14.517, -2.071, 0, 16.667, 6.002, 0, 19.017, -1.528, 0, 22, -0.509]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -25.457, 0, 1, -22.006, 0, 3.2, -29.994, 0, 5.4, -22.006, 0, 7.6, -29.994, 0, 9.8, -22.006, 0, 12, -29.994, 0, 14.2, -22.006, 0, 16.4, -29.994, 0, 18.6, -22.006, 0, 20.8, -29.994, 0, 22, -25.457]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 18.643, 0, 1.917, 27.793, 0, 4.117, 18.207, 0, 6.317, 27.793, 0, 8.517, 18.207, 0, 10.717, 27.793, 0, 12.917, 18.207, 0, 15.117, 27.793, 0, 17.317, 18.207, 0, 19.517, 27.793, 0, 21.717, 18.207, 0, 22, 18.643]}, {"Target": "Parameter", "Id": "ParamBodyAngleX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.969, 0, 1.6, 3.101, 0, 3.8, -3.101, 0, 6, 3.101, 0, 8.2, -3.101, 0, 10.4, 3.101, 0, 12.6, -3.101, 0, 14.8, 3.101, 0, 17, -3.101, 0, 19.2, 3.101, 0, 21.4, -3.101, 0, 22, -1.969]}, {"Target": "Parameter", "Id": "ParamBodyAngleY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 8.442, 0, 0.433, 6.018, 0, 2.633, 29.982, 0, 4.833, 6.018, 0, 7.033, 29.982, 0, 9.233, 6.018, 0, 11.433, 29.982, 0, 13.633, 6.018, 0, 15.833, 29.982, 0, 18.033, 6.018, 0, 20.233, 29.982, 0, 22, 8.442]}, {"Target": "Parameter", "Id": "ParamWaistZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 15.464, 0, 0.717, 17.058, 0, 0.733, 17.048, 0, 0.75, 17.051, 0, 3.033, 10.932, 2, 3.05, 10.932, 0, 3.117, 10.899, 0, 3.15, 10.901, 0, 3.183, 10.895, 0, 3.2, 10.91, 0, 3.217, 10.903, 0, 5.083, 16.131, 0, 5.1, 16.113, 0, 5.15, 16.119, 0, 7.45, 9.843, 2, 7.467, 9.843, 0, 7.517, 9.824, 0, 7.533, 9.83, 0, 7.55, 9.821, 0, 7.567, 9.83, 0, 7.583, 9.825, 0, 7.617, 9.84, 0, 7.633, 9.837, 0, 9.55, 15.242, 0, 9.6, 15.231, 0, 9.617, 15.234, 0, 11.867, 9.659, 0, 11.917, 9.672, 0, 11.933, 9.665, 0, 12, 9.702, 0, 12.017, 9.699, 0, 13.983, 15.703, 0, 14, 15.693, 0, 14.017, 15.695, 0, 14.05, 15.692, 0, 14.067, 15.694, 0, 16.183, 10.504, 0, 16.2, 10.512, 0, 16.233, 10.482, 0, 16.267, 10.496, 0, 16.3, 10.488, 0, 16.367, 10.517, 0, 16.383, 10.515, 0, 18.383, 16.675, 0, 20.617, 11.273, 0, 20.633, 11.275, 0, 20.683, 11.258, 0, 20.7, 11.261, 0, 20.717, 11.26, 0, 22, 15.464]}, {"Target": "Parameter", "Id": "ParamShoulderStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -6.842, 1, 0.717, -6.16, 1.433, 8.854, 2.15, 8.854, 0, 4.35, -6.866, 0, 6.55, 8.854, 0, 8.75, -6.866, 0, 10.95, 8.854, 0, 13.15, -6.866, 0, 15.35, 8.854, 0, 17.55, -6.866, 1, 17.661, -6.866, 17.772, -7.285, 17.883, -5.893, 1, 18.505, 1.904, 19.128, 8.854, 19.75, 8.854, 0, 21.95, -6.866, 1, 21.967, -6.866, 21.983, -6.858, 22, -6.842]}, {"Target": "Parameter", "Id": "ParamShoulderStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -25.672, 1, 0.172, -26.999, 0.345, -27.866, 0.517, -27.866, 0, 2.717, -12.146, 0, 4.917, -27.866, 0, 7.117, -12.146, 0, 9.317, -27.866, 0, 11.517, -12.146, 0, 13.717, -27.866, 0, 15.917, -12.146, 1, 16.572, -12.146, 17.228, -19.466, 17.883, -27.371, 1, 17.961, -28.309, 18.039, -27.866, 18.117, -27.866, 0, 20.317, -12.146, 1, 20.878, -12.146, 21.439, -21.349, 22, -25.672]}, {"Target": "Parameter", "Id": "ParamButtXA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -6.39, 0, 2.2, 6.39, 0, 4.4, -6.39, 0, 6.6, 6.39, 0, 8.8, -6.39, 0, 11, 6.39, 0, 13.2, -6.39, 0, 15.4, 6.39, 0, 17.6, -6.39, 0, 19.8, 6.39, 0, 22, -6.39]}, {"Target": "Parameter", "Id": "Param43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.92, 0, 0.8, -4.802, 0, 3, 4.802, 0, 5.2, -4.802, 0, 7.4, 4.802, 0, 9.6, -4.802, 0, 11.8, 4.802, 0, 14, -4.802, 0, 16.2, 4.802, 0, 18.4, -4.802, 0, 20.6, 4.802, 0, 22, -1.92]}, {"Target": "Parameter", "Id": "ParamButtZA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 23.76, 0, 1.617, 30, 0, 3.783, 20.505, 0, 5.983, 30, 0, 8.183, 20.505, 0, 10.383, 30, 0, 12.583, 20.505, 0, 14.783, 30, 0, 16.983, 20.505, 0, 19.183, 30, 0, 21.383, 20.505, 0, 22, 23.76]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.683, 0, 1.267, -1.73, 0, 1.283, -1.729, 0, 1.317, -1.73, 0, 3.567, -0.078, 0, 5.617, -1.719, 0, 5.667, -1.716, 0, 5.683, -1.717, 0, 7.917, -0.068, 2, 7.933, -0.068, 0, 7.967, -0.064, 0, 10.05, -1.688, 0, 10.117, -1.679, 0, 10.133, -1.681, 0, 12.35, -0.065, 0, 12.367, -0.066, 0, 12.383, -0.064, 0, 14.417, -1.711, 0, 14.433, -1.71, 0, 14.467, -1.711, 0, 14.533, -1.701, 0, 14.55, -1.704, 0, 14.583, -1.693, 0, 14.6, -1.694, 0, 16.75, -0.076, 2, 16.783, -0.076, 0, 16.8, -0.075, 0, 18.85, -1.747, 0, 21.15, -0.085, 0, 21.167, -0.086, 0, 21.183, -0.084, 0, 21.2, -0.086, 0, 21.217, -0.085, 0, 22, -0.683]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -23.145, 0, 0.083, -23.14, 2, 0.1, -23.14, 2, 0.117, -23.14, 0, 2.117, -24.078, 2, 2.133, -24.078, 2, 2.167, -24.078, 0, 4.283, -23.229, 2, 4.3, -23.229, 0, 4.45, -23.212, 2, 4.467, -23.212, 2, 4.483, -23.212, 0, 6.533, -24.191, 0, 6.55, -24.19, 0, 6.567, -24.191, 0, 8.683, -23.325, 0, 8.7, -23.326, 0, 8.783, -23.31, 0, 8.8, -23.311, 0, 8.85, -23.305, 0, 8.867, -23.307, 0, 8.883, -23.303, 0, 8.933, -23.307, 0, 8.95, -23.305, 0, 10.917, -24.24, 0, 13.1, -23.302, 0, 13.117, -23.303, 0, 13.15, -23.293, 2, 13.167, -23.293, 0, 13.183, -23.288, 2, 13.2, -23.288, 0, 13.233, -23.282, 0, 13.25, -23.283, 0, 13.3, -23.278, 0, 13.333, -23.281, 2, 13.35, -23.281, 0, 15.3, -24.166, 0, 15.317, -24.165, 0, 15.333, -24.166, 0, 17.733, -23.189, 0, 19.717, -24.074, 0, 22, -23.145]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.183, 0, 0.3, 0.194, 2, 0.317, 0.194, 2, 0.333, 0.194, 2, 0.35, 0.194, 2, 0.367, 0.194, 2, 0.383, 0.194, 2, 0.4, 0.194, 0, 2.167, 0.031, 0, 2.183, 0.032, 0, 2.217, 0.03, 2, 2.233, 0.03, 0, 2.267, 0.029, 2, 2.283, 0.029, 2, 2.317, 0.029, 2, 2.333, 0.029, 2, 2.35, 0.029, 0, 2.4, 0.03, 2, 2.417, 0.03, 0, 4.65, 0.18, 2, 4.667, 0.18, 0, 4.717, 0.181, 2, 4.75, 0.181, 2, 4.783, 0.181, 0, 6.65, 0.007, 0, 6.667, 0.008, 0, 6.683, 0.007, 0, 6.7, 0.008, 0, 6.717, 0.007, 0, 6.917, 0.011, 2, 6.933, 0.011, 0, 9.083, 0.163, 2, 9.1, 0.163, 2, 9.133, 0.163, 0, 11, -0.001, 2, 11.017, -0.001, 2, 11.033, -0.001, 2, 11.05, -0.001, 0, 11.067, -0.002, 0, 11.083, -0.001, 2, 11.117, -0.001, 0, 13.5, 0.17, 0, 13.517, 0.169, 0, 13.583, 0.17, 0, 15.433, 0.014, 2, 15.467, 0.014, 2, 15.483, 0.014, 2, 15.5, 0.014, 0, 15.533, 0.013, 0, 18, 0.188, 0, 18.05, 0.187, 2, 18.067, 0.187, 0, 19.783, 0.032, 2, 19.8, 0.032, 0, 19.833, 0.031, 2, 19.85, 0.031, 2, 19.867, 0.031, 2, 19.883, 0.031, 2, 19.9, 0.031, 2, 19.917, 0.031, 2, 19.933, 0.031, 0, 22, 0.183]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.089, 0, 0.367, -0.217, 0, 2.567, 1.517, 0, 4.767, -0.217, 0, 6.967, 1.517, 0, 9.167, -0.217, 0, 11.367, 1.517, 0, 13.567, -0.217, 0, 15.767, 1.517, 0, 17.967, -0.217, 0, 20.167, 1.517, 1, 20.778, 1.517, 21.389, 0.313, 22, -0.089]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.821, 1, 0.361, 3.168, 0.722, 3.51, 1.083, 3.51, 0, 3.283, 2.1, 0, 5.483, 3.51, 0, 7.683, 2.1, 0, 9.883, 3.51, 0, 12.083, 2.1, 0, 14.283, 3.51, 0, 16.483, 2.1, 0, 18.683, 3.51, 0, 20.883, 2.1, 1, 21.255, 2.1, 21.628, 2.463, 22, 2.821]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 9.49, 1, 0.606, 6.947, 1.211, -0.352, 1.817, -0.352, 0, 4.017, 10.352, 0, 6.217, -0.352, 0, 8.417, 10.352, 0, 10.617, -0.352, 0, 12.817, 10.352, 0, 15.017, -0.352, 0, 17.217, 10.352, 0, 19.417, -0.352, 0, 21.617, 10.352, 1, 21.745, 10.352, 21.872, 10.027, 22, 9.49]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.185, 0, 1.917, 14.948, 0, 4.117, 0.185, 0, 6.317, 14.948, 0, 8.517, 0.185, 0, 10.717, 14.948, 0, 12.917, 0.185, 0, 15.117, 14.948, 0, 17.317, 0.185, 0, 19.517, 14.948, 0, 21.717, 0.185, 1, 21.811, 0.185, 21.906, 0.43, 22, 0.857]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 1, 0.878, 1, 1.755, 0.975, 2.633, 0.9, 1, 2.739, 0.891, 2.844, 0, 2.95, 0, 0, 3.267, 0.9, 2, 6.817, 0.9, 0, 7.133, 0, 0, 7.45, 0.9, 2, 12.333, 0.9, 0, 12.65, 0, 0, 12.967, 0.9, 2, 17.633, 0.9, 0, 17.95, 0, 1, 18.056, 0, 18.161, 0.894, 18.267, 0.9, 1, 19.511, 0.972, 20.756, 1, 22, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.8, 0, 0, 3.05, 0.466, 0, 3.483, 0, 2, 6.983, 0, 0, 7.233, 0.466, 0, 7.667, 0, 2, 12.5, 0, 0, 12.75, 0.466, 0, 13.183, 0, 2, 17.8, 0, 0, 18.05, 0.466, 0, 18.483, 0, 2, 22, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 1, 0.878, 1, 1.755, 0.975, 2.633, 0.9, 1, 2.739, 0.891, 2.844, 0, 2.95, 0, 0, 3.267, 0.9, 2, 6.817, 0.9, 0, 7.133, 0, 0, 7.45, 0.9, 2, 12.333, 0.9, 0, 12.65, 0, 0, 12.967, 0.9, 2, 17.633, 0.9, 0, 17.95, 0, 1, 18.056, 0, 18.161, 0.894, 18.267, 0.9, 1, 19.511, 0.972, 20.756, 1, 22, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.8, 0, 0, 3.05, 0.466, 0, 3.483, 0, 2, 6.983, 0, 0, 7.233, 0.466, 0, 7.667, 0, 2, 12.5, 0, 0, 12.75, 0.466, 0, 13.183, 0, 2, 17.8, 0, 0, 18.05, 0.466, 0, 18.483, 0, 2, 22, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.633, 0, 0, 2.933, 0.045, 0, 3.35, 0, 2, 6.817, 0, 0, 7.117, 0.045, 0, 7.533, 0, 2, 12.333, 0, 0, 12.633, 0.045, 0, 13.05, 0, 2, 17.633, 0, 0, 17.933, 0.045, 0, 18.35, 0, 2, 22, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.633, 0, 0, 2.933, 0.235, 0, 3.35, 0, 2, 6.817, 0, 0, 7.117, 0.235, 0, 7.533, 0, 2, 12.333, 0, 0, 12.633, 0.235, 0, 13.05, 0, 2, 17.633, 0, 0, 17.933, 0.235, 0, 18.35, 0, 2, 22, 0]}, {"Target": "Parameter", "Id": "Param4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.633, 0, 0, 2.933, -0.342, 0, 3.35, 0, 2, 6.817, 0, 0, 7.117, -0.342, 0, 7.533, 0, 2, 12.333, 0, 0, 12.633, -0.342, 0, 13.05, 0, 2, 17.633, 0, 0, 17.933, -0.342, 0, 18.35, 0, 2, 22, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.633, 0, 0, 2.933, 0.17, 0, 3.35, 0, 2, 6.817, 0, 0, 7.117, 0.17, 0, 7.533, 0, 2, 12.333, 0, 0, 12.633, 0.17, 0, 13.05, 0, 2, 17.633, 0, 0, 17.933, 0.17, 0, 18.35, 0, 2, 22, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.633, 0, 0, 2.933, 0.237, 0, 3.35, 0, 2, 6.817, 0, 0, 7.117, 0.237, 0, 7.533, 0, 2, 12.333, 0, 0, 12.633, 0.237, 0, 13.05, 0, 2, 17.633, 0, 0, 17.933, 0.237, 0, 18.35, 0, 2, 22, 0]}, {"Target": "Parameter", "Id": "Param11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.633, 0, 0, 2.933, -0.334, 0, 3.35, 0, 2, 6.817, 0, 0, 7.117, -0.334, 0, 7.533, 0, 2, 12.333, 0, 0, 12.633, -0.334, 0, 13.05, 0, 2, 17.633, 0, 0, 17.933, -0.334, 0, 18.35, 0, 2, 22, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.2, 0, 3.517, 1, 0, 7.1, 0.2, 0, 15.583, 1, 1, 17.722, 1, 19.861, 0.208, 22, 0.2]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.3, 0, 3.517, 0, 0, 7.1, 0.3, 0, 15.583, 0, 1, 17.722, 0, 19.861, 0.297, 22, 0.3]}, {"Target": "Parameter", "Id": "Param16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.267, 0.223, 0, 6.6, -0.46, 0, 10.317, 0.033, 0, 13.05, -0.522, 0, 20, 0, 2, 22, 0]}, {"Target": "Parameter", "Id": "Param19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 6.667, 0.333, 13.333, 0.667, 20, 1, 2, 22, 1]}, {"Target": "Parameter", "Id": "Param142", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.812, 2, 0.017, 0.127, 1, 2.561, 0.305, 5.106, 0.482, 7.65, 0.66, 1, 8.1, 0.711, 8.55, 0.761, 9, 0.812, 2, 9.017, 0.127, 1, 11.561, 0.305, 14.106, 0.482, 16.65, 0.66, 1, 17.1, 0.711, 17.55, 0.761, 18, 0.812, 2, 18.017, 0.127, 1, 18.678, 0.173, 19.339, 0.219, 20, 0.265, 2, 22, 0.265]}, {"Target": "Parameter", "Id": "Param143", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.76, 1, 0.422, 0.82, 0.845, 0.88, 1.267, 0.94, 2, 1.283, 0.2, 1, 3.855, 0.387, 6.428, 0.573, 9, 0.76, 1, 9.422, 0.82, 9.845, 0.88, 10.267, 0.94, 2, 10.283, 0.2, 1, 12.855, 0.387, 15.428, 0.573, 18, 0.76, 1, 18.422, 0.82, 18.845, 0.88, 19.267, 0.94, 2, 19.283, 0.2, 1, 19.522, 0.217, 19.761, 0.235, 20, 0.252, 2, 22, 0.252]}, {"Target": "Parameter", "Id": "Param18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 0, 2, 0.35, 1, 2, 0.4, 2, 2, 0.45, 3, 2, 0.5, 4, 2, 0.55, 5, 2, 0.6, 0, 2, 0.65, 1, 2, 0.7, 2, 2, 0.75, 3, 2, 0.8, 4, 2, 0.85, 5, 2, 0.9, 0, 2, 0.95, 1, 2, 1, 2, 2, 1.05, 3, 2, 1.1, 4, 2, 1.15, 5, 2, 1.2, 0, 2, 1.25, 1, 2, 1.3, 2, 2, 1.35, 3, 2, 1.4, 4, 2, 1.45, 5, 2, 1.5, 0, 2, 1.55, 1, 2, 1.6, 2, 2, 1.65, 3, 2, 1.7, 4, 2, 1.75, 5, 2, 1.8, 0, 2, 1.85, 1, 2, 1.9, 2, 2, 1.95, 3, 2, 2, 4, 2, 2.05, 5, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 0, 2, 2.45, 1, 2, 2.5, 2, 2, 2.55, 3, 2, 2.6, 4, 2, 2.65, 5, 2, 2.7, 0, 2, 2.75, 1, 2, 2.8, 2, 2, 2.85, 3, 2, 2.9, 4, 2, 2.95, 5, 2, 3, 0, 2, 3.05, 1, 2, 3.1, 2, 2, 3.15, 3, 2, 3.2, 4, 2, 3.25, 5, 2, 3.3, 0, 2, 3.35, 1, 2, 3.4, 2, 2, 3.45, 3, 2, 3.5, 4, 2, 3.55, 5, 2, 3.6, 0, 2, 3.65, 1, 2, 3.7, 2, 2, 3.75, 3, 2, 3.8, 4, 2, 3.85, 5, 2, 3.9, 0, 2, 3.95, 1, 2, 4, 2, 2, 4.05, 3, 2, 4.1, 4, 2, 4.15, 5, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 0, 2, 4.55, 1, 2, 4.6, 2, 2, 4.65, 3, 2, 4.7, 4, 2, 4.75, 5, 2, 4.8, 0, 2, 4.85, 1, 2, 4.9, 2, 2, 4.95, 3, 2, 5, 4, 2, 5.05, 5, 2, 5.1, 0, 2, 5.15, 1, 2, 5.2, 2, 2, 5.25, 3, 2, 5.3, 4, 2, 5.35, 5, 2, 5.4, 0, 2, 5.45, 1, 2, 5.5, 2, 2, 5.55, 3, 2, 5.6, 4, 2, 5.65, 5, 2, 5.7, 0, 2, 5.75, 1, 2, 5.8, 2, 2, 5.85, 3, 2, 5.9, 4, 2, 5.95, 5, 2, 6, 0, 2, 6.05, 1, 2, 6.1, 2, 2, 6.15, 3, 2, 6.2, 4, 2, 6.25, 5, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 0, 2, 6.65, 1, 2, 6.7, 2, 2, 6.75, 3, 2, 6.8, 4, 2, 6.85, 5, 2, 6.9, 0, 2, 6.95, 1, 2, 7, 2, 2, 7.05, 3, 2, 7.1, 4, 2, 7.15, 5, 2, 7.2, 0, 2, 7.25, 1, 2, 7.3, 2, 2, 7.35, 3, 2, 7.4, 4, 2, 7.45, 5, 2, 7.5, 0, 2, 7.55, 1, 2, 7.6, 2, 2, 7.65, 3, 2, 7.7, 4, 2, 7.75, 5, 2, 7.8, 0, 2, 7.85, 1, 2, 7.9, 2, 2, 7.95, 3, 2, 8, 4, 2, 8.05, 5, 2, 8.1, 0, 2, 8.15, 1, 2, 8.2, 2, 2, 8.25, 3, 2, 8.3, 4, 2, 8.35, 5, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 0, 2, 8.75, 1, 2, 8.8, 2, 2, 8.85, 3, 2, 8.9, 4, 2, 8.95, 5, 2, 9, 0, 2, 9.05, 1, 2, 9.1, 2, 2, 9.15, 3, 2, 9.2, 4, 2, 9.25, 5, 2, 9.3, 0, 2, 9.35, 1, 2, 9.4, 2, 2, 9.45, 3, 2, 9.5, 4, 2, 9.55, 5, 2, 9.6, 0, 2, 9.65, 1, 2, 9.7, 2, 2, 9.75, 3, 2, 9.8, 4, 2, 9.85, 5, 2, 9.9, 0, 2, 9.95, 1, 2, 10, 2, 2, 10.05, 3, 2, 10.1, 4, 2, 10.15, 5, 2, 10.2, 0, 2, 10.25, 1, 2, 10.3, 2, 2, 10.35, 3, 2, 10.4, 4, 2, 10.45, 5, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 0, 2, 10.85, 1, 2, 10.9, 2, 2, 10.95, 3, 2, 11, 4, 2, 11.05, 5, 2, 11.1, 0, 2, 11.15, 1, 2, 11.2, 2, 2, 11.25, 3, 2, 11.3, 4, 2, 11.35, 5, 2, 11.4, 0, 2, 11.45, 1, 2, 11.5, 2, 2, 11.55, 3, 2, 11.6, 4, 2, 11.65, 5, 2, 11.7, 0, 2, 11.75, 1, 2, 11.8, 2, 2, 11.85, 3, 2, 11.9, 4, 2, 11.95, 5, 2, 12, 0, 2, 12.05, 1, 2, 12.1, 2, 2, 12.15, 3, 2, 12.2, 4, 2, 12.25, 5, 2, 12.3, 0, 2, 12.35, 1, 2, 12.4, 2, 2, 12.45, 3, 2, 12.5, 4, 2, 12.55, 5, 2, 12.6, 0, 2, 12.65, 1, 2, 12.7, 2, 2, 12.75, 3, 2, 12.8, 4, 2, 12.85, 5, 2, 12.9, 0, 2, 12.95, 1, 2, 13, 2, 2, 13.05, 3, 2, 13.1, 4, 2, 13.15, 5, 2, 13.2, 0, 2, 13.25, 1, 2, 13.3, 2, 2, 13.35, 3, 2, 13.4, 4, 2, 13.45, 5, 2, 13.5, 0, 2, 13.55, 1, 2, 13.6, 2, 2, 13.65, 3, 2, 13.7, 4, 2, 13.75, 5, 2, 13.8, 0, 2, 13.85, 1, 2, 13.9, 2, 2, 13.95, 3, 2, 14, 4, 2, 14.05, 5, 2, 14.1, 0, 2, 14.15, 1, 2, 14.2, 2, 2, 14.25, 3, 2, 14.3, 4, 2, 14.35, 5, 2, 14.4, 0, 2, 14.45, 1, 2, 14.5, 2, 2, 14.55, 3, 2, 14.6, 4, 2, 14.65, 5, 2, 14.7, 0, 2, 14.75, 1, 2, 14.8, 2, 2, 14.85, 3, 2, 14.9, 4, 2, 14.95, 5, 2, 15, 0, 2, 15.05, 1, 2, 15.1, 2, 2, 15.15, 3, 2, 15.2, 4, 2, 15.25, 5, 2, 15.3, 0, 2, 15.35, 1, 2, 15.4, 2, 2, 15.45, 3, 2, 15.5, 4, 2, 15.55, 5, 2, 15.6, 0, 2, 15.65, 1, 2, 15.7, 2, 2, 15.75, 3, 2, 15.8, 4, 2, 15.85, 5, 2, 15.9, 0, 2, 15.95, 1, 2, 16, 2, 2, 16.05, 3, 2, 16.1, 4, 2, 16.15, 5, 2, 16.2, 0, 2, 16.25, 1, 2, 16.3, 2, 2, 16.35, 3, 2, 16.4, 4, 2, 16.45, 5, 2, 16.5, 0, 2, 16.55, 1, 2, 16.6, 2, 2, 16.65, 3, 2, 16.7, 4, 2, 16.75, 5, 2, 16.8, 0, 2, 16.85, 1, 2, 16.9, 2, 2, 16.95, 3, 2, 17, 4, 2, 17.05, 5, 2, 17.1, 0, 2, 17.15, 1, 2, 17.2, 2, 2, 17.25, 3, 2, 17.3, 4, 2, 17.35, 5, 2, 17.4, 0, 2, 17.45, 1, 2, 17.5, 2, 2, 17.55, 3, 2, 17.6, 4, 2, 17.65, 5, 2, 17.7, 0, 2, 17.75, 1, 2, 17.8, 2, 2, 17.85, 3, 2, 17.9, 4, 2, 17.95, 5, 2, 18, 0, 2, 18.05, 1, 2, 18.1, 2, 2, 18.15, 3, 2, 18.2, 4, 2, 18.25, 5, 2, 18.3, 0, 2, 18.35, 1, 2, 18.4, 2, 2, 18.45, 3, 2, 18.5, 4, 2, 18.55, 5, 2, 18.6, 0, 2, 18.65, 1, 2, 18.7, 2, 2, 18.75, 3, 2, 18.8, 4, 2, 18.85, 5, 2, 18.9, 0, 2, 18.95, 1, 2, 19, 2, 2, 19.05, 3, 2, 19.1, 4, 2, 19.15, 5, 2, 19.2, 0, 2, 19.25, 1, 2, 19.3, 2, 2, 19.35, 3, 2, 19.4, 4, 2, 19.45, 5, 2, 19.5, 0, 2, 19.55, 1, 2, 19.6, 2, 2, 19.65, 3, 2, 19.7, 4, 2, 19.75, 5, 2, 19.8, 0, 2, 19.85, 1, 2, 19.9, 2, 2, 19.95, 3, 2, 20, 4, 2, 22, 4]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 6, 2, 0.35, 0, 2, 0.4, 1, 2, 0.45, 2, 2, 0.5, 3, 2, 0.55, 4, 2, 0.6, 5, 2, 0.65, 6, 2, 0.7, 0, 2, 0.75, 1, 2, 0.8, 2, 2, 0.85, 3, 2, 0.9, 4, 2, 0.95, 5, 2, 1, 6, 2, 1.05, 0, 2, 1.1, 1, 2, 1.15, 2, 2, 1.2, 3, 2, 1.25, 4, 2, 1.3, 5, 2, 1.35, 6, 2, 1.4, 0, 2, 1.45, 1, 2, 1.5, 2, 2, 1.55, 3, 2, 1.6, 4, 2, 1.65, 5, 2, 1.7, 6, 2, 1.75, 0, 2, 1.8, 1, 2, 1.85, 2, 2, 1.9, 3, 2, 1.95, 4, 2, 2, 5, 2, 2.05, 6, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 6, 2, 2.45, 0, 2, 2.5, 1, 2, 2.55, 2, 2, 2.6, 3, 2, 2.65, 4, 2, 2.7, 5, 2, 2.75, 6, 2, 2.8, 0, 2, 2.85, 1, 2, 2.9, 2, 2, 2.95, 3, 2, 3, 4, 2, 3.05, 5, 2, 3.1, 6, 2, 3.15, 0, 2, 3.2, 1, 2, 3.25, 2, 2, 3.3, 3, 2, 3.35, 4, 2, 3.4, 5, 2, 3.45, 6, 2, 3.5, 0, 2, 3.55, 1, 2, 3.6, 2, 2, 3.65, 3, 2, 3.7, 4, 2, 3.75, 5, 2, 3.8, 6, 2, 3.85, 0, 2, 3.9, 1, 2, 3.95, 2, 2, 4, 3, 2, 4.05, 4, 2, 4.1, 5, 2, 4.15, 6, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 6, 2, 4.55, 0, 2, 4.6, 1, 2, 4.65, 2, 2, 4.7, 3, 2, 4.75, 4, 2, 4.8, 5, 2, 4.85, 6, 2, 4.9, 0, 2, 4.95, 1, 2, 5, 2, 2, 5.05, 3, 2, 5.1, 4, 2, 5.15, 5, 2, 5.2, 6, 2, 5.25, 0, 2, 5.3, 1, 2, 5.35, 2, 2, 5.4, 3, 2, 5.45, 4, 2, 5.5, 5, 2, 5.55, 6, 2, 5.6, 0, 2, 5.65, 1, 2, 5.7, 2, 2, 5.75, 3, 2, 5.8, 4, 2, 5.85, 5, 2, 5.9, 6, 2, 5.95, 0, 2, 6, 1, 2, 6.05, 2, 2, 6.1, 3, 2, 6.15, 4, 2, 6.2, 5, 2, 6.25, 6, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 6, 2, 6.65, 0, 2, 6.7, 1, 2, 6.75, 2, 2, 6.8, 3, 2, 6.85, 4, 2, 6.9, 5, 2, 6.95, 6, 2, 7, 0, 2, 7.05, 1, 2, 7.1, 2, 2, 7.15, 3, 2, 7.2, 4, 2, 7.25, 5, 2, 7.3, 6, 2, 7.35, 0, 2, 7.4, 1, 2, 7.45, 2, 2, 7.5, 3, 2, 7.55, 4, 2, 7.6, 5, 2, 7.65, 6, 2, 7.7, 0, 2, 7.75, 1, 2, 7.8, 2, 2, 7.85, 3, 2, 7.9, 4, 2, 7.95, 5, 2, 8, 6, 2, 8.05, 0, 2, 8.1, 1, 2, 8.15, 2, 2, 8.2, 3, 2, 8.25, 4, 2, 8.3, 5, 2, 8.35, 6, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 6, 2, 8.75, 0, 2, 8.8, 1, 2, 8.85, 2, 2, 8.9, 3, 2, 8.95, 4, 2, 9, 5, 2, 9.05, 6, 2, 9.1, 0, 2, 9.15, 1, 2, 9.2, 2, 2, 9.25, 3, 2, 9.3, 4, 2, 9.35, 5, 2, 9.4, 6, 2, 9.45, 0, 2, 9.5, 1, 2, 9.55, 2, 2, 9.6, 3, 2, 9.65, 4, 2, 9.7, 5, 2, 9.75, 6, 2, 9.8, 0, 2, 9.85, 1, 2, 9.9, 2, 2, 9.95, 3, 2, 10, 4, 2, 10.05, 5, 2, 10.1, 6, 2, 10.15, 0, 2, 10.2, 1, 2, 10.25, 2, 2, 10.3, 3, 2, 10.35, 4, 2, 10.4, 5, 2, 10.45, 6, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 6, 2, 10.85, 0, 2, 10.9, 1, 2, 10.95, 2, 2, 11, 3, 2, 11.05, 4, 2, 11.1, 5, 2, 11.15, 6, 2, 11.2, 0, 2, 11.25, 1, 2, 11.3, 2, 2, 11.35, 3, 2, 11.4, 4, 2, 11.45, 5, 2, 11.5, 6, 2, 11.55, 0, 2, 11.6, 1, 2, 11.65, 2, 2, 11.7, 3, 2, 11.75, 4, 2, 11.8, 5, 2, 11.85, 6, 2, 11.9, 0, 2, 11.95, 1, 2, 12, 2, 2, 12.05, 3, 2, 12.1, 4, 2, 12.15, 5, 2, 12.2, 6, 2, 12.25, 0, 2, 12.3, 1, 2, 12.35, 2, 2, 12.4, 3, 2, 12.45, 4, 2, 12.5, 5, 2, 12.55, 6, 2, 12.6, 0, 2, 12.65, 1, 2, 12.7, 2, 2, 12.75, 3, 2, 12.8, 4, 2, 12.85, 5, 2, 12.9, 6, 2, 12.95, 0, 2, 13, 1, 2, 13.05, 2, 2, 13.1, 3, 2, 13.15, 4, 2, 13.2, 5, 2, 13.25, 6, 2, 13.3, 0, 2, 13.35, 1, 2, 13.4, 2, 2, 13.45, 3, 2, 13.5, 4, 2, 13.55, 5, 2, 13.6, 6, 2, 13.65, 0, 2, 13.7, 1, 2, 13.75, 2, 2, 13.8, 3, 2, 13.85, 4, 2, 13.9, 5, 2, 13.95, 6, 2, 14, 0, 2, 14.05, 1, 2, 14.1, 2, 2, 14.15, 3, 2, 14.2, 4, 2, 14.25, 5, 2, 14.3, 6, 2, 14.35, 0, 2, 14.4, 1, 2, 14.45, 2, 2, 14.5, 3, 2, 14.55, 4, 2, 14.6, 5, 2, 14.65, 6, 2, 14.7, 0, 2, 14.75, 1, 2, 14.8, 2, 2, 14.85, 3, 2, 14.9, 4, 2, 14.95, 5, 2, 15, 6, 2, 15.05, 0, 2, 15.1, 1, 2, 15.15, 2, 2, 15.2, 3, 2, 15.25, 4, 2, 15.3, 5, 2, 15.35, 6, 2, 15.4, 0, 2, 15.45, 1, 2, 15.5, 2, 2, 15.55, 3, 2, 15.6, 4, 2, 15.65, 5, 2, 15.7, 6, 2, 15.75, 0, 2, 15.8, 1, 2, 15.85, 2, 2, 15.9, 3, 2, 15.95, 4, 2, 16, 5, 2, 16.05, 6, 2, 16.1, 0, 2, 16.15, 1, 2, 16.2, 2, 2, 16.25, 3, 2, 16.3, 4, 2, 16.35, 5, 2, 16.4, 6, 2, 16.45, 0, 2, 16.5, 1, 2, 16.55, 2, 2, 16.6, 3, 2, 16.65, 4, 2, 16.7, 5, 2, 16.75, 6, 2, 16.8, 0, 2, 16.85, 1, 2, 16.9, 2, 2, 16.95, 3, 2, 17, 4, 2, 17.05, 5, 2, 17.1, 6, 2, 17.15, 0, 2, 17.2, 1, 2, 17.25, 2, 2, 17.3, 3, 2, 17.35, 4, 2, 17.4, 5, 2, 17.45, 6, 2, 17.5, 0, 2, 17.55, 1, 2, 17.6, 2, 2, 17.65, 3, 2, 17.7, 4, 2, 17.75, 5, 2, 17.8, 6, 2, 17.85, 0, 2, 17.9, 1, 2, 17.95, 2, 2, 18, 3, 2, 18.05, 4, 2, 18.1, 5, 2, 18.15, 6, 2, 18.2, 0, 2, 18.25, 1, 2, 18.3, 2, 2, 18.35, 3, 2, 18.4, 4, 2, 18.45, 5, 2, 18.5, 6, 2, 18.55, 0, 2, 18.6, 1, 2, 18.65, 2, 2, 18.7, 3, 2, 18.75, 4, 2, 18.8, 5, 2, 18.85, 6, 2, 18.9, 0, 2, 18.95, 1, 2, 19, 2, 2, 19.05, 3, 2, 19.1, 4, 2, 19.15, 5, 2, 19.2, 6, 2, 19.25, 0, 2, 19.3, 1, 2, 19.35, 2, 2, 19.4, 3, 2, 19.45, 4, 2, 19.5, 5, 2, 19.55, 6, 2, 19.6, 0, 2, 19.65, 1, 2, 19.7, 2, 2, 19.75, 3, 2, 19.8, 4, 2, 19.85, 5, 2, 19.9, 6, 2, 19.95, 0, 2, 20, 1, 2, 22, 1]}, {"Target": "Parameter", "Id": "Param51", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 6, 2, 0.35, 0, 2, 0.4, 1, 2, 0.45, 2, 2, 0.5, 3, 2, 0.55, 4, 2, 0.6, 5, 2, 0.65, 6, 2, 0.7, 0, 2, 0.75, 1, 2, 0.8, 2, 2, 0.85, 3, 2, 0.9, 4, 2, 0.95, 5, 2, 1, 6, 2, 1.05, 0, 2, 1.1, 1, 2, 1.15, 2, 2, 1.2, 3, 2, 1.25, 4, 2, 1.3, 5, 2, 1.35, 6, 2, 1.4, 0, 2, 1.45, 1, 2, 1.5, 2, 2, 1.55, 3, 2, 1.6, 4, 2, 1.65, 5, 2, 1.7, 6, 2, 1.75, 0, 2, 1.8, 1, 2, 1.85, 2, 2, 1.9, 3, 2, 1.95, 4, 2, 2, 5, 2, 2.05, 6, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 6, 2, 2.45, 0, 2, 2.5, 1, 2, 2.55, 2, 2, 2.6, 3, 2, 2.65, 4, 2, 2.7, 5, 2, 2.75, 6, 2, 2.8, 0, 2, 2.85, 1, 2, 2.9, 2, 2, 2.95, 3, 2, 3, 4, 2, 3.05, 5, 2, 3.1, 6, 2, 3.15, 0, 2, 3.2, 1, 2, 3.25, 2, 2, 3.3, 3, 2, 3.35, 4, 2, 3.4, 5, 2, 3.45, 6, 2, 3.5, 0, 2, 3.55, 1, 2, 3.6, 2, 2, 3.65, 3, 2, 3.7, 4, 2, 3.75, 5, 2, 3.8, 6, 2, 3.85, 0, 2, 3.9, 1, 2, 3.95, 2, 2, 4, 3, 2, 4.05, 4, 2, 4.1, 5, 2, 4.15, 6, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 6, 2, 4.55, 0, 2, 4.6, 1, 2, 4.65, 2, 2, 4.7, 3, 2, 4.75, 4, 2, 4.8, 5, 2, 4.85, 6, 2, 4.9, 0, 2, 4.95, 1, 2, 5, 2, 2, 5.05, 3, 2, 5.1, 4, 2, 5.15, 5, 2, 5.2, 6, 2, 5.25, 0, 2, 5.3, 1, 2, 5.35, 2, 2, 5.4, 3, 2, 5.45, 4, 2, 5.5, 5, 2, 5.55, 6, 2, 5.6, 0, 2, 5.65, 1, 2, 5.7, 2, 2, 5.75, 3, 2, 5.8, 4, 2, 5.85, 5, 2, 5.9, 6, 2, 5.95, 0, 2, 6, 1, 2, 6.05, 2, 2, 6.1, 3, 2, 6.15, 4, 2, 6.2, 5, 2, 6.25, 6, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 6, 2, 6.65, 0, 2, 6.7, 1, 2, 6.75, 2, 2, 6.8, 3, 2, 6.85, 4, 2, 6.9, 5, 2, 6.95, 6, 2, 7, 0, 2, 7.05, 1, 2, 7.1, 2, 2, 7.15, 3, 2, 7.2, 4, 2, 7.25, 5, 2, 7.3, 6, 2, 7.35, 0, 2, 7.4, 1, 2, 7.45, 2, 2, 7.5, 3, 2, 7.55, 4, 2, 7.6, 5, 2, 7.65, 6, 2, 7.7, 0, 2, 7.75, 1, 2, 7.8, 2, 2, 7.85, 3, 2, 7.9, 4, 2, 7.95, 5, 2, 8, 6, 2, 8.05, 0, 2, 8.1, 1, 2, 8.15, 2, 2, 8.2, 3, 2, 8.25, 4, 2, 8.3, 5, 2, 8.35, 6, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 6, 2, 8.75, 0, 2, 8.8, 1, 2, 8.85, 2, 2, 8.9, 3, 2, 8.95, 4, 2, 9, 5, 2, 9.05, 6, 2, 9.1, 0, 2, 9.15, 1, 2, 9.2, 2, 2, 9.25, 3, 2, 9.3, 4, 2, 9.35, 5, 2, 9.4, 6, 2, 9.45, 0, 2, 9.5, 1, 2, 9.55, 2, 2, 9.6, 3, 2, 9.65, 4, 2, 9.7, 5, 2, 9.75, 6, 2, 9.8, 0, 2, 9.85, 1, 2, 9.9, 2, 2, 9.95, 3, 2, 10, 4, 2, 10.05, 5, 2, 10.1, 6, 2, 10.15, 0, 2, 10.2, 1, 2, 10.25, 2, 2, 10.3, 3, 2, 10.35, 4, 2, 10.4, 5, 2, 10.45, 6, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 6, 2, 10.85, 0, 2, 10.9, 1, 2, 10.95, 2, 2, 11, 3, 2, 11.05, 4, 2, 11.1, 5, 2, 11.15, 6, 2, 11.2, 0, 2, 11.25, 1, 2, 11.3, 2, 2, 11.35, 3, 2, 11.4, 4, 2, 11.45, 5, 2, 11.5, 6, 2, 11.55, 0, 2, 11.6, 1, 2, 11.65, 2, 2, 11.7, 3, 2, 11.75, 4, 2, 11.8, 5, 2, 11.85, 6, 2, 11.9, 0, 2, 11.95, 1, 2, 12, 2, 2, 12.05, 3, 2, 12.1, 4, 2, 12.15, 5, 2, 12.2, 6, 2, 12.25, 0, 2, 12.3, 1, 2, 12.35, 2, 2, 12.4, 3, 2, 12.45, 4, 2, 12.5, 5, 2, 12.55, 6, 2, 12.6, 0, 2, 12.65, 1, 2, 12.7, 2, 2, 12.75, 3, 2, 12.8, 4, 2, 12.85, 5, 2, 12.9, 6, 2, 12.95, 0, 2, 13, 1, 2, 13.05, 2, 2, 13.1, 3, 2, 13.15, 4, 2, 13.2, 5, 2, 13.25, 6, 2, 13.3, 0, 2, 13.35, 1, 2, 13.4, 2, 2, 13.45, 3, 2, 13.5, 4, 2, 13.55, 5, 2, 13.6, 6, 2, 13.65, 0, 2, 13.7, 1, 2, 13.75, 2, 2, 13.8, 3, 2, 13.85, 4, 2, 13.9, 5, 2, 13.95, 6, 2, 14, 0, 2, 14.05, 1, 2, 14.1, 2, 2, 14.15, 3, 2, 14.2, 4, 2, 14.25, 5, 2, 14.3, 6, 2, 14.35, 0, 2, 14.4, 1, 2, 14.45, 2, 2, 14.5, 3, 2, 14.55, 4, 2, 14.6, 5, 2, 14.65, 6, 2, 14.7, 0, 2, 14.75, 1, 2, 14.8, 2, 2, 14.85, 3, 2, 14.9, 4, 2, 14.95, 5, 2, 15, 6, 2, 15.05, 0, 2, 15.1, 1, 2, 15.15, 2, 2, 15.2, 3, 2, 15.25, 4, 2, 15.3, 5, 2, 15.35, 6, 2, 15.4, 0, 2, 15.45, 1, 2, 15.5, 2, 2, 15.55, 3, 2, 15.6, 4, 2, 15.65, 5, 2, 15.7, 6, 2, 15.75, 0, 2, 15.8, 1, 2, 15.85, 2, 2, 15.9, 3, 2, 15.95, 4, 2, 16, 5, 2, 16.05, 6, 2, 16.1, 0, 2, 16.15, 1, 2, 16.2, 2, 2, 16.25, 3, 2, 16.3, 4, 2, 16.35, 5, 2, 16.4, 6, 2, 16.45, 0, 2, 16.5, 1, 2, 16.55, 2, 2, 16.6, 3, 2, 16.65, 4, 2, 16.7, 5, 2, 16.75, 6, 2, 16.8, 0, 2, 16.85, 1, 2, 16.9, 2, 2, 16.95, 3, 2, 17, 4, 2, 17.05, 5, 2, 17.1, 6, 2, 17.15, 0, 2, 17.2, 1, 2, 17.25, 2, 2, 17.3, 3, 2, 17.35, 4, 2, 17.4, 5, 2, 17.45, 6, 2, 17.5, 0, 2, 17.55, 1, 2, 17.6, 2, 2, 17.65, 3, 2, 17.7, 4, 2, 17.75, 5, 2, 17.8, 6, 2, 17.85, 0, 2, 17.9, 1, 2, 17.95, 2, 2, 18, 3, 2, 18.05, 4, 2, 18.1, 5, 2, 18.15, 6, 2, 18.2, 0, 2, 18.25, 1, 2, 18.3, 2, 2, 18.35, 3, 2, 18.4, 4, 2, 18.45, 5, 2, 18.5, 6, 2, 18.55, 0, 2, 18.6, 1, 2, 18.65, 2, 2, 18.7, 3, 2, 18.75, 4, 2, 18.8, 5, 2, 18.85, 6, 2, 18.9, 0, 2, 18.95, 1, 2, 19, 2, 2, 19.05, 3, 2, 19.1, 4, 2, 19.15, 5, 2, 19.2, 6, 2, 19.25, 0, 2, 19.3, 1, 2, 19.35, 2, 2, 19.4, 3, 2, 19.45, 4, 2, 19.5, 5, 2, 19.55, 6, 2, 19.6, 0, 2, 19.65, 1, 2, 19.7, 2, 2, 19.75, 3, 2, 19.8, 4, 2, 19.85, 5, 2, 19.9, 6, 2, 19.95, 0, 2, 20, 1, 2, 22, 1]}, {"Target": "Parameter", "Id": "Param42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 6.667, 0.074, 13.333, 0.148, 20, 0.222, 2, 22, 0.222]}, {"Target": "Parameter", "Id": "Param45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.4, 1, 6.667, -0.326, 13.333, -0.252, 20, -0.178, 2, 22, -0.178]}, {"Target": "Parameter", "Id": "Param46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.3, 1, 6.667, 0.374, 13.333, 0.448, 20, 0.522, 2, 22, 0.522]}, {"Target": "Parameter", "Id": "Param50", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 6.667, 1.333, 13.333, 2.667, 20, 4, 2, 22, 4]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2.5, 10, 0, 5.017, -10, 0, 7.5, 10, 0, 10, -10, 0, 12.5, 26.92, 0, 15.017, -10, 0, 17.5, 10, 0, 20, -10, 2, 22, -10]}, {"Target": "Parameter", "Id": "Param17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.76, 0, 2.5, 0.66, 0, 5.017, -5.76, 0, 7.5, 0.66, 0, 10, -5.76, 0, 12.5, 21.06, 0, 15.017, -5.76, 0, 17.5, 0.66, 0, 20, -5.76, 2, 22, -5.76]}, {"Target": "Parameter", "Id": "Param49", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.35, 0, 1.25, 0, 0, 3.75, 0.7, 0, 6.267, 0, 0, 8.767, 0.7, 0, 11.283, 0, 0, 13.783, 0.7, 0, 16.3, 0, 0, 18.8, 0.7, 0, 20, 0.374, 2, 22, 0.374]}, {"Target": "Parameter", "Id": "Param40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.25, 0, 0.917, 10, 0, 2.917, -10, 0, 4.917, 10, 0, 6.917, -10, 0, 8.917, 10, 0, 10.917, -10, 0, 12.917, 10, 0, 14.917, -10, 0, 16.917, 10, 0, 18.917, -10, 0, 20, 1.25, 2, 22, 1.25]}, {"Target": "Parameter", "Id": "Param41", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2, 10, 0, 4, -10, 0, 6, 10, 0, 8, -10, 0, 10, 10, 0, 12, -10, 0, 14, 10, 0, 16, -10, 0, 18, 10, 0, 20, -10, 2, 22, -10]}, {"Target": "Parameter", "Id": "Param32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2, 10, 0, 4, -10, 0, 6, 10, 0, 8, -10, 0, 10, 10, 0, 12, -10, 0, 14, 10, 0, 16, -10, 0, 18, 10, 0, 20, -10, 2, 22, -10]}, {"Target": "Parameter", "Id": "Param33", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.886, 0, 0.583, 10, 0, 2.583, -10, 0, 4.583, 10, 0, 6.583, -10, 0, 8.583, 10, 0, 10.583, -10, 0, 12.583, 10, 0, 14.583, -10, 0, 16.583, 10, 0, 18.583, -10, 0, 20, 5.886, 2, 22, 5.886]}, {"Target": "Parameter", "Id": "Param34", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -14.451, 0, 1.333, 30, 0, 3.333, -30, 0, 5.333, 30, 0, 7.333, -30, 0, 9.333, 30, 0, 11.333, -30, 0, 13.333, 30, 0, 15.333, -30, 0, 17.333, 30, 0, 19.333, -30, 0, 20, -14.451, 2, 22, -14.451]}, {"Target": "Parameter", "Id": "Param28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -15, 0, 2, 15, 0, 4, -15, 0, 6, 15, 0, 8, -15, 0, 10, 15, 0, 12, -15, 0, 14, 15, 0, 16, -15, 0, 18, 15, 0, 20, -15, 2, 22, -15]}, {"Target": "Parameter", "Id": "Param29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.886, 0, 0.583, 10, 0, 2.583, -10, 0, 4.583, 10, 0, 6.583, -10, 0, 8.583, 10, 0, 10.583, -10, 0, 12.583, 10, 0, 14.583, -10, 0, 16.583, 10, 0, 18.583, -10, 0, 20, 5.886, 2, 22, 5.886]}, {"Target": "Parameter", "Id": "Param35", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -14.451, 0, 1.333, 30, 0, 3.333, -30, 0, 5.333, 30, 0, 7.333, -30, 0, 9.333, 30, 0, 11.333, -30, 0, 13.333, 30, 0, 15.333, -30, 0, 17.333, 30, 0, 19.333, -30, 0, 20, -14.451, 2, 22, -14.451]}, {"Target": "Parameter", "Id": "Param30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.959, 0, 0.8, 10, 0, 2.8, -10, 0, 4.8, 10, 0, 6.8, -10, 0, 8.8, 10, 0, 10.8, -10, 0, 12.8, 10, 0, 14.8, -10, 0, 16.8, 10, 0, 18.8, -10, 0, 20, 2.959, 2, 22, 2.959]}, {"Target": "Parameter", "Id": "Param31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 9.44, 0, 1.8, -10, 0, 3.8, 10, 0, 5.8, -10, 0, 7.8, 10, 0, 9.8, -10, 0, 11.8, 10, 0, 13.8, -10, 0, 15.8, 10, 0, 17.8, -10, 0, 19.8, 10, 0, 20, 9.44, 2, 22, 9.44]}, {"Target": "Parameter", "Id": "Param36", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 13.773, 0, 0.683, 30, 0, 2.683, -30, 0, 4.683, 30, 0, 6.683, -30, 0, 8.683, 30, 0, 10.683, -30, 0, 12.683, 30, 0, 14.683, -30, 0, 16.683, 30, 0, 18.683, -30, 0, 20, 13.773, 2, 22, 13.773]}, {"Target": "Parameter", "Id": "GL1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 6.667, 0.297, 13.333, 0.593, 20, 0.89, 2, 22, 0.89]}, {"Target": "Parameter", "Id": "GL2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.39, 1, 6.667, 0.687, 13.333, 0.983, 20, 1.28, 2, 22, 1.28]}, {"Target": "Parameter", "Id": "GL3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.72, 1, 6.667, 1.017, 13.333, 1.313, 20, 1.61, 2, 22, 1.61]}, {"Target": "Parameter", "Id": "GL4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.21, 1, 6.667, 0.507, 13.333, 0.803, 20, 1.1, 2, 22, 1.1]}, {"Target": "Parameter", "Id": "GL5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.53, 1, 6.667, 0.827, 13.333, 1.123, 20, 1.42, 2, 22, 1.42]}, {"Target": "Parameter", "Id": "GL6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.89, 1, 6.667, 1.187, 13.333, 1.483, 20, 1.78, 2, 22, 1.78]}, {"Target": "Parameter", "Id": "GL7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.77, 1, 6.667, 1.067, 13.333, 1.363, 20, 1.66, 2, 22, 1.66]}, {"Target": "Parameter", "Id": "GL8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.09, 1, 6.667, 0.387, 13.333, 0.683, 20, 0.98, 2, 22, 0.98]}, {"Target": "Parameter", "Id": "GL9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.301, 1, 6.667, 0.598, 13.333, 0.894, 20, 1.191, 2, 22, 1.191]}, {"Target": "Parameter", "Id": "GL10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.45, 1, 6.667, 0.747, 13.333, 1.043, 20, 1.34, 2, 22, 1.34]}, {"Target": "Parameter", "Id": "GL11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.64, 1, 6.667, 0.937, 13.333, 1.233, 20, 1.53, 2, 22, 1.53]}, {"Target": "Parameter", "Id": "GL12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.15, 1, 6.667, 0.447, 13.333, 0.743, 20, 1.04, 2, 22, 1.04]}, {"Target": "Parameter", "Id": "GL13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.585, 1, 6.667, 0.882, 13.333, 1.178, 20, 1.475, 2, 22, 1.475]}, {"Target": "Parameter", "Id": "GL14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 6.667, 0.297, 13.333, 0.593, 20, 0.89, 2, 22, 0.89]}, {"Target": "Parameter", "Id": "Param161", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.51, 1, 5.994, 0.843, 11.989, 1.177, 17.983, 1.51, 2, 18, 0.51, 1, 18.667, 0.547, 19.333, 0.584, 20, 0.621, 2, 22, 0.621]}, {"Target": "Parameter", "Id": "Param162", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.31, 1, 5.994, 0.643, 11.989, 0.977, 17.983, 1.31, 2, 18, 0.31, 1, 18.667, 0.347, 19.333, 0.384, 20, 0.421, 2, 22, 0.421]}, {"Target": "Parameter", "Id": "Param163", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.72, 1, 5.994, 1.053, 11.989, 1.387, 17.983, 1.72, 2, 18, 0.72, 1, 18.667, 0.757, 19.333, 0.794, 20, 0.831, 2, 22, 0.831]}, {"Target": "Parameter", "Id": "Param164", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 5.994, 0.333, 11.989, 0.667, 17.983, 1, 2, 18, 0, 1, 18.667, 0.037, 19.333, 0.074, 20, 0.111, 2, 22, 0.111]}, {"Target": "Parameter", "Id": "Param165", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.17, 1, 5.994, 0.503, 11.989, 0.837, 17.983, 1.17, 2, 18, 0.17, 1, 18.667, 0.207, 19.333, 0.244, 20, 0.281, 2, 22, 0.281]}, {"Target": "Parameter", "Id": "touch_drag21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param54", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param118", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 3, 0, 22, 3]}, {"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.136, 0, 22, 1.136]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.816, 0, 22, -0.816]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.447, 0, 22, 0.447]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.19, 0, 22, 5.19]}, {"Target": "Parameter", "Id": "man_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "man_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 21.78, 0, 22, 21.78]}, {"Target": "Parameter", "Id": "Param78", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.16, 0, 22, -0.16]}, {"Target": "Parameter", "Id": "Param79", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.372, 0, 22, 0.372]}, {"Target": "Parameter", "Id": "man_<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.48, 0, 22, -0.48]}, {"Target": "Parameter", "Id": "man_<PERSON>ze", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.42, 0, 22, 0.42]}, {"Target": "Parameter", "Id": "Param189", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "shangguang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param186", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param96", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param97", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param98", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "shangguang2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param53", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param99", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 22, 1]}, {"Target": "Parameter", "Id": "Param76", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 22, 1]}, {"Target": "Parameter", "Id": "Param77", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "MB_xingfengTMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param127", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param103", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param104", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param106", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param105", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param110", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param130", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param107", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param108", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param109", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Mouthfunnel", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param293", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param72", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param74", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param75", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param73", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "MB_fenweiTMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "MB_fenweiliangdu", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 22, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiandu", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 22, 0.6]}, {"Target": "Parameter", "Id": "MB_fenweiqiu1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 22, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 22, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 22, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 22, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 22, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 22, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiushangxia", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "MB_fenweiqiudaxiao", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param102", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param58", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -14, 0, 22, -14]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamHandRREDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param101", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param55", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.04, 0, 22, 0.04]}, {"Target": "Parameter", "Id": "ParamForearmLChange2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 11.04, 0, 22, 11.04]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 4.8, 0, 22, 4.8]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamHandLREDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 7, 0, 22, 7]}, {"Target": "Parameter", "Id": "Param100", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param167", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param169", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param196", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param56", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param59", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10, 0, 22, 10]}, {"Target": "Parameter", "Id": "Param3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param57", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param124", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param80", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param48", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param37", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param38", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param60", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param47", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param81", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param95", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param125", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param171", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param166", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param123", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param39", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param61", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 22, 1]}, {"Target": "Parameter", "Id": "Param71", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 22, 1]}, {"Target": "Parameter", "Id": "Hair_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.937, 0, 22, -1.937]}, {"Target": "Parameter", "Id": "Hair_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.012, 0, 22, -1.012]}, {"Target": "Parameter", "Id": "Param69", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.07, 0, 22, 1.07]}, {"Target": "Parameter", "Id": "Param173", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param174", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param175", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param176", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param190", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param191", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param192", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param193", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param65", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 22, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamShoulderStretch3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamShoulderStretch4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamChestZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamWaistZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamButtZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param153", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.71, 0, 22, 5.71]}, {"Target": "Parameter", "Id": "Param154", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 3.577, 0, 22, 3.577]}, {"Target": "Parameter", "Id": "Param155", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -7.623, 0, 22, -7.623]}, {"Target": "Parameter", "Id": "Param156", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -12.702, 0, 22, -12.702]}, {"Target": "Parameter", "Id": "Param82", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.323, 0, 22, 5.323]}, {"Target": "Parameter", "Id": "Param83", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 3.427, 0, 22, 3.427]}, {"Target": "Parameter", "Id": "Param84", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -7.792, 0, 22, -7.792]}, {"Target": "Parameter", "Id": "Param85", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -12.95, 0, 22, -12.95]}, {"Target": "Parameter", "Id": "Param66", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param67", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.77, 0, 22, -2.77]}, {"Target": "Parameter", "Id": "Param68", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.889, 0, 22, -1.889]}, {"Target": "Parameter", "Id": "Param62", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param63", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.77, 0, 22, 2.77]}, {"Target": "Parameter", "Id": "Param64", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.889, 0, 22, 1.889]}, {"Target": "Parameter", "Id": "Param86", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.045, 0, 22, -2.045]}, {"Target": "Parameter", "Id": "Param89", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.908, 0, 22, 5.908]}, {"Target": "Parameter", "Id": "Param87", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.338, 0, 22, 0.338]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10.391, 0, 22, 10.391]}, {"Target": "Parameter", "Id": "Param88", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -7.272, 0, 22, -7.272]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 3.257, 0, 22, 3.257]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.045, 0, 22, 2.045]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.908, 0, 22, 5.908]}, {"Target": "Parameter", "Id": "Param94", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.338, 0, 22, -0.338]}, {"Target": "Parameter", "Id": "Param126", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10.391, 0, 22, 10.391]}, {"Target": "Parameter", "Id": "Param128", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -7.272, 0, 22, -7.272]}, {"Target": "Parameter", "Id": "Param129", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 3.257, 0, 22, 3.257]}, {"Target": "Parameter", "Id": "Param20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param52", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param70", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param208", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "touch_drag23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "touch_drag24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "touch_drag25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "touch_drag26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "touch_drag27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "touch_drag28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "touch_drag29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "touch_drag30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "touch_drag31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "touch_drag32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "PartOpacity", "Id": "Part7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 22, 1]}], "UserData": []}