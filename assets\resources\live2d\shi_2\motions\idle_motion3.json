{"Version": 3, "Meta": {"Duration": 22.0, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 265, "TotalSegmentCount": 2270, "TotalPointCount": 2484, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "All_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 2.2, 0.935, 0, 4.4, 0, 0, 6.6, 0.935, 0, 8.8, 0, 0, 11, 0.935, 0, 13.2, 0, 0, 15.4, 0.935, 0, 17.6, 0, 0, 19.8, 0.935, 0, 22, 0]}, {"Target": "Parameter", "Id": "All_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 2.2, 0.935, 0, 4.4, 0, 0, 6.6, 0.935, 0, 8.8, 0, 0, 11, 0.935, 0, 13.2, 0, 0, 15.4, 0.935, 0, 17.6, 0, 0, 19.8, 0.935, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 10.033, 5.456, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamAngleX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.928, 0, 0.617, 1.48, 0, 2.867, -2.777, 0, 5.033, 1.942, 0, 7.3, -2.562, 0, 9.417, 2.134, 0, 11.65, -2.115, 0, 13.817, 1.988, 0, 16, -2.032, 0, 18.217, 1.639, 0, 20.467, -2.843, 0, 22, -1.928]}, {"Target": "Parameter", "Id": "ParamAngleY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 3.491, 0, 1.3, 1.116, 0, 3.517, 9.435, 0, 5.667, 1.322, 0, 7.817, 10.88, 0, 10.117, 2.272, 0, 12.267, 10.407, 0, 14.517, 1.929, 0, 16.667, 10.002, 0, 19.017, 2.472, 0, 22, 3.491]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.543, 0, 1, 3.994, 0, 3.2, -3.994, 0, 5.4, 3.994, 0, 7.6, -3.994, 0, 9.8, 3.994, 0, 12, -3.994, 0, 14.2, 3.994, 0, 16.4, -3.994, 0, 18.6, 3.994, 0, 20.8, -3.994, 0, 22, 0.543]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -4.357, 0, 1.917, 4.793, 0, 4.117, -4.793, 0, 6.317, 4.793, 0, 8.517, -4.793, 0, 10.717, 4.793, 0, 12.917, -4.793, 0, 15.117, 4.793, 0, 17.317, -4.793, 0, 19.517, 4.793, 0, 21.717, -4.793, 0, 22, -4.357]}, {"Target": "Parameter", "Id": "ParamBodyAngleX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -8.417, 0, 1.6, 13.258, 0, 3.8, -13.258, 0, 6, 13.258, 0, 8.2, -13.258, 0, 10.4, 13.258, 0, 12.6, -13.258, 0, 14.8, 13.258, 0, 17, -13.258, 0, 19.2, 13.258, 0, 21.4, -13.258, 0, 22, -8.417]}, {"Target": "Parameter", "Id": "ParamBodyAngleY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -9.558, 0, 0.433, -11.982, 0, 2.633, 11.982, 0, 4.833, -11.982, 0, 7.033, 11.982, 0, 9.233, -11.982, 0, 11.433, 11.982, 0, 13.633, -11.982, 0, 15.833, 11.982, 0, 18.033, -11.982, 0, 20.233, 11.982, 0, 22, -9.558]}, {"Target": "Parameter", "Id": "ParamWaistZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -13.536, 0, 0.717, -11.942, 0, 3.217, -16.097, 0, 5.083, -10.869, 0, 7.633, -17.163, 0, 9.55, -11.758, 0, 11.867, -17.341, 0, 13.983, -11.297, 0, 16.267, -16.504, 0, 18.383, -10.325, 0, 20.7, -15.739, 0, 22, -13.536]}, {"Target": "Parameter", "Id": "ParamShoulderStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.893, 0, 1.867, 11.854, 0, 4.067, -3.866, 0, 6.267, 11.854, 0, 8.467, -3.866, 0, 10.667, 11.854, 0, 12.867, -3.866, 0, 15.067, 11.854, 0, 17.267, -3.866, 0, 19.467, 11.854, 0, 21.667, -3.866, 0, 22, -2.893]}, {"Target": "Parameter", "Id": "ParamShoulderStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.371, 0, 0.233, -3.866, 0, 2.433, 11.854, 0, 4.633, -3.866, 0, 6.833, 11.854, 0, 9.033, -3.866, 0, 11.233, 11.854, 0, 13.433, -3.866, 0, 15.633, 11.854, 0, 17.833, -3.866, 0, 20.033, 11.854, 0, 22, -3.371]}, {"Target": "Parameter", "Id": "ParamButtXA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -6.39, 0, 2.2, 6.39, 0, 4.4, -6.39, 0, 6.6, 6.39, 0, 8.8, -6.39, 0, 11, 6.39, 0, 13.2, -6.39, 0, 15.4, 6.39, 0, 17.6, -6.39, 0, 19.8, 6.39, 0, 22, -6.39]}, {"Target": "Parameter", "Id": "Param43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.916, 0, 0.8, -4.802, 0, 3, 4.802, 0, 5.2, -4.802, 0, 7.4, 4.802, 0, 9.6, -4.802, 0, 11.8, 4.802, 0, 14, -4.802, 0, 16.2, 4.802, 0, 18.4, -4.802, 0, 20.183, 1.797, 0, 22, -1.916]}, {"Target": "Parameter", "Id": "ParamButtZA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.48, 0, 1.633, 5.114, 0, 3.783, -8.495, 0, 5.983, 8.495, 0, 8.183, -8.495, 0, 10.383, 8.495, 0, 12.583, -8.495, 0, 14.783, 8.495, 0, 16.983, -8.495, 0, 19.183, 8.495, 0, 21.183, -5.878, 0, 22, -3.48]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 9.483, 0, 1.4, 9.329, 0, 3.633, 9.549, 0, 5.717, 9.328, 0, 8.033, 9.547, 0, 10.083, 9.33, 0, 12.433, 9.547, 0, 14.517, 9.328, 0, 16.833, 9.549, 0, 18.867, 9.329, 0, 21.217, 9.55, 0, 22, 9.483]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -4.806, 0, 1.183, -5.079, 0, 3.5, -4.609, 0, 5.55, -5.073, 0, 7.917, -4.599, 0, 9.933, -5.06, 0, 12.267, -4.599, 0, 14.35, -5.069, 0, 16.733, -4.607, 0, 18.783, -5.084, 0, 21.15, -4.613, 0, 22, -4.806]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.416, 0, 1.317, -0.63, 0, 3.567, 1.022, 0, 5.617, -0.619, 0, 7.917, 1.032, 0, 10.017, -0.588, 0, 12.35, 1.035, 0, 14.433, -0.61, 0, 16.75, 1.024, 0, 18.85, -0.647, 0, 21.15, 1.014, 0, 22, 0.416]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.855, 0, 0.1, 0.86, 0, 2.167, -0.079, 0, 4.45, 0.788, 0, 6.533, -0.191, 0, 8.95, 0.695, 0, 10.917, -0.24, 0, 13.35, 0.719, 0, 15.3, -0.166, 0, 17.7, 0.811, 0, 19.717, -0.074, 0, 22, 0.855]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.184, 0, 0.417, 0.194, 0, 2.217, 0.03, 0, 4.717, 0.181, 0, 6.65, 0.007, 0, 8.95, 0.16, 0, 11, -0.001, 0, 13.5, 0.169, 0, 15.383, 0.014, 0, 17.967, 0.188, 0, 19.783, 0.032, 0, 22, 0.184]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.089, 0, 0.367, -0.217, 0, 2.567, 1.517, 0, 4.767, -0.217, 0, 6.967, 1.517, 0, 9.167, -0.217, 0, 11.367, 1.517, 0, 13.567, -0.217, 0, 15.767, 1.517, 0, 17.967, -0.217, 0, 20.167, 1.517, 0, 22, -0.089]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.769, 0, 1.167, 0, 0, 3.367, 1.41, 0, 5.567, 0, 0, 7.767, 1.41, 0, 9.967, 0, 0, 12.167, 1.41, 0, 14.367, 0, 0, 16.567, 1.41, 0, 18.767, 0, 0, 20.967, 1.41, 0, 22, 0.769]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.105, 0, 1.917, 3.416, 0, 4.117, -3.416, 0, 6.317, 3.416, 0, 8.517, -3.416, 0, 10.717, 3.416, 0, 12.917, -3.416, 0, 15.117, 3.416, 0, 17.317, -3.416, 0, 19.517, 3.416, 0, 21.717, -3.416, 0, 22, -3.105]}, {"Target": "Parameter", "Id": "Param3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 7.166, 0, 2.1, -5.781, 0, 4.433, 7.095, 0, 6.5, -5.736, 0, 8.833, 7.215, 0, 10.9, -5.718, 0, 13.233, 7.185, 0, 15.3, -5.746, 0, 17.533, 7.516, 0, 19.7, -5.782, 0, 22, 7.166]}, {"Target": "Parameter", "Id": "Param2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.323, 0, 1.533, -4.36, 0, 3.383, -6.748, 0, 5.9, -4.727, 0, 7.133, -6.926, 0, 10.383, -4.937, 0, 11.533, -6.986, 0, 14.75, -4.728, 0, 15.933, -6.809, 0, 18.733, -4.967, 0, 19.833, -6, 0, 22, -5.323]}, {"Target": "Parameter", "Id": "Param", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 3.264, 0, 1.1, 6.173, 0, 1.167, 6.134, 0, 1.233, 6.147, 0, 3.35, 0.304, 0, 5.5, 6.046, 0, 5.567, 6.013, 0, 5.617, 6.022, 0, 7.75, 0.16, 0, 9.9, 5.942, 0, 9.967, 5.917, 0, 10.033, 5.93, 0, 10.05, 5.925, 0, 10.067, 5.926, 0, 12.033, 0.175, 0, 12.05, 0.176, 0, 12.15, 0.148, 0, 14.3, 6.012, 0, 14.367, 5.983, 0, 14.417, 5.999, 0, 16.433, 0.289, 0, 16.45, 0.29, 0, 16.533, 0.264, 0, 18.7, 6.135, 0, 18.767, 6.103, 0, 18.833, 6.117, 0, 20.833, 0.389, 0, 20.85, 0.39, 0, 20.933, 0.363, 0, 22, 3.264]}, {"Target": "Parameter", "Id": "Param6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.291, 0, 2.283, 0.848, 0, 4.483, -5.303, 0, 6.65, 0.851, 0, 8.85, -5.3, 0, 11.05, 0.846, 0, 13.267, -5.3, 0, 15.45, 0.853, 0, 17.683, -5.306, 0, 19.883, 0.846, 0, 22, -5.291]}, {"Target": "Parameter", "Id": "Param48", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -6.856, 0, 2.5, -8.067, 1, 3.189, -8.067, 3.878, -7.268, 4.567, -7.115, 1, 5.295, -6.953, 6.022, -6.974, 6.75, -6.974, 1, 7.489, -6.974, 8.228, -6.968, 8.967, -7.218, 1, 9.745, -7.481, 10.522, -8.018, 11.3, -8.018, 0, 13.367, -7.187, 0, 15.7, -8.046, 0, 17.767, -7.084, 0, 19.967, -7.13, 0, 22, -6.856]}, {"Target": "Parameter", "Id": "Param37", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 4.26, 0, 1.267, 6.607, 0, 3.433, 2.098, 0, 5.667, 6.439, 0, 5.683, 6.438, 0, 5.7, 6.44, 0, 7.833, 1.87, 0, 10.067, 6.322, 0, 12.233, 1.855, 0, 14.467, 6.413, 0, 16.583, 2.041, 0, 18.867, 6.567, 0, 21, 2.193, 0, 22, 4.26]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 1, 0.883, 1, 1.767, 0.975, 2.65, 0.9, 1, 2.756, 0.891, 2.861, 0, 2.967, 0, 0, 3.283, 0.9, 2, 6.833, 0.9, 0, 7.15, 0, 0, 7.467, 0.9, 2, 12.35, 0.9, 0, 12.667, 0, 0, 12.983, 0.9, 2, 17.65, 0.9, 0, 17.967, 0, 1, 18.072, 0, 18.178, 0.894, 18.283, 0.9, 1, 19.522, 0.972, 20.761, 1, 22, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.817, 0, 0, 3.067, 0.466, 0, 3.5, 0, 2, 7, 0, 0, 7.25, 0.466, 0, 7.683, 0, 2, 12.517, 0, 0, 12.767, 0.466, 0, 13.2, 0, 2, 17.817, 0, 0, 18.067, 0.466, 0, 18.5, 0, 2, 22, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 1, 0.883, 1, 1.767, 0.975, 2.65, 0.9, 1, 2.756, 0.891, 2.861, 0, 2.967, 0, 0, 3.283, 0.9, 2, 6.833, 0.9, 0, 7.15, 0, 0, 7.467, 0.9, 2, 12.35, 0.9, 0, 12.667, 0, 0, 12.983, 0.9, 2, 17.65, 0.9, 0, 17.967, 0, 1, 18.072, 0, 18.178, 0.894, 18.283, 0.9, 1, 19.522, 0.972, 20.761, 1, 22, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.817, 0, 0, 3.067, 0.466, 0, 3.5, 0, 2, 7, 0, 0, 7.25, 0.466, 0, 7.683, 0, 2, 12.517, 0, 0, 12.767, 0.466, 0, 13.2, 0, 2, 17.817, 0, 0, 18.067, 0.466, 0, 18.5, 0, 2, 22, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.65, 0, 0, 2.95, 0.045, 0, 3.367, 0, 2, 6.833, 0, 0, 7.133, 0.045, 0, 7.55, 0, 2, 12.35, 0, 0, 12.65, 0.045, 0, 13.067, 0, 2, 17.65, 0, 0, 17.95, 0.045, 0, 18.367, 0, 2, 22, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.65, 0, 0, 2.95, 0.235, 0, 3.367, 0, 2, 6.833, 0, 0, 7.133, 0.235, 0, 7.55, 0, 2, 12.35, 0, 0, 12.65, 0.235, 0, 13.067, 0, 2, 17.65, 0, 0, 17.95, 0.235, 0, 18.367, 0, 2, 22, 0]}, {"Target": "Parameter", "Id": "Param4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.65, 0, 0, 2.95, -0.342, 0, 3.367, 0, 2, 6.833, 0, 0, 7.133, -0.342, 0, 7.55, 0, 2, 12.35, 0, 0, 12.65, -0.342, 0, 13.067, 0, 2, 17.65, 0, 0, 17.95, -0.342, 0, 18.367, 0, 2, 22, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.65, 0, 0, 2.95, 0.17, 0, 3.367, 0, 2, 6.833, 0, 0, 7.133, 0.17, 0, 7.55, 0, 2, 12.35, 0, 0, 12.65, 0.17, 0, 13.067, 0, 2, 17.65, 0, 0, 17.95, 0.17, 0, 18.367, 0, 2, 22, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.65, 0, 0, 2.95, 0.237, 0, 3.367, 0, 2, 6.833, 0, 0, 7.133, 0.237, 0, 7.55, 0, 2, 12.35, 0, 0, 12.65, 0.237, 0, 13.067, 0, 2, 17.65, 0, 0, 17.95, 0.237, 0, 18.367, 0, 2, 22, 0]}, {"Target": "Parameter", "Id": "Param11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.65, 0, 0, 2.95, -0.334, 0, 3.367, 0, 2, 6.833, 0, 0, 7.133, -0.334, 0, 7.55, 0, 2, 12.35, 0, 0, 12.65, -0.334, 0, 13.067, 0, 2, 17.65, 0, 0, 17.95, -0.334, 0, 18.367, 0, 2, 22, 0]}, {"Target": "Parameter", "Id": "Hair_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -19.132, 0, 2.2, 19.132, 0, 4.4, -19.132, 0, 6.6, 19.132, 0, 8.8, -19.132, 0, 11, 19.132, 0, 13.2, -19.132, 0, 15.4, 19.132, 0, 17.6, -19.132, 0, 19.8, 19.132, 0, 22, -19.132]}, {"Target": "Parameter", "Id": "Param69", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -19.132, 0, 2.2, 19.132, 0, 4.4, -19.132, 0, 6.6, 19.132, 0, 8.8, -19.132, 0, 11, 19.132, 0, 13.2, -19.132, 0, 15.4, 19.132, 0, 17.6, -19.132, 0, 19.8, 19.132, 0, 22, -19.132]}, {"Target": "Parameter", "Id": "Param16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.267, 0.223, 0, 6.6, -0.46, 0, 10.317, 0.033, 0, 13.05, -0.522, 0, 20, 0, 2, 20.017, 0, 0, 22, 0.071]}, {"Target": "Parameter", "Id": "Param19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 6.667, 0.333, 13.333, 0.667, 20, 1, 2, 20.017, 0, 1, 20.678, 0.033, 21.339, 0.066, 22, 0.099]}, {"Target": "Parameter", "Id": "Param142", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.812, 2, 0.017, 0.127, 1, 2.561, 0.305, 5.106, 0.482, 7.65, 0.66, 1, 8.1, 0.711, 8.55, 0.761, 9, 0.812, 2, 9.017, 0.127, 1, 11.561, 0.305, 14.106, 0.482, 16.65, 0.66, 1, 17.1, 0.711, 17.55, 0.761, 18, 0.812, 2, 18.017, 0.127, 1, 18.678, 0.173, 19.339, 0.219, 20, 0.265, 1, 20.006, 0.447, 20.011, 0.63, 20.017, 0.812, 2, 20.033, 0.127, 1, 20.689, 0.173, 21.344, 0.218, 22, 0.264]}, {"Target": "Parameter", "Id": "Param143", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.76, 1, 0.422, 0.82, 0.845, 0.88, 1.267, 0.94, 2, 1.283, 0.2, 1, 3.855, 0.387, 6.428, 0.573, 9, 0.76, 1, 9.422, 0.82, 9.845, 0.88, 10.267, 0.94, 2, 10.283, 0.2, 1, 12.855, 0.387, 15.428, 0.573, 18, 0.76, 1, 18.422, 0.82, 18.845, 0.88, 19.267, 0.94, 2, 19.283, 0.2, 1, 19.522, 0.217, 19.761, 0.235, 20, 0.252, 1, 20.006, 0.421, 20.011, 0.591, 20.017, 0.76, 1, 20.439, 0.82, 20.861, 0.88, 21.283, 0.94, 2, 21.3, 0.2, 1, 21.533, 0.217, 21.767, 0.234, 22, 0.251]}, {"Target": "Parameter", "Id": "Param18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 0, 2, 0.35, 1, 2, 0.4, 2, 2, 0.45, 3, 2, 0.5, 4, 2, 0.55, 5, 2, 0.6, 0, 2, 0.65, 1, 2, 0.7, 2, 2, 0.75, 3, 2, 0.8, 4, 2, 0.85, 5, 2, 0.9, 0, 2, 0.95, 1, 2, 1, 2, 2, 1.05, 3, 2, 1.1, 4, 2, 1.15, 5, 2, 1.2, 0, 2, 1.25, 1, 2, 1.3, 2, 2, 1.35, 3, 2, 1.4, 4, 2, 1.45, 5, 2, 1.5, 0, 2, 1.55, 1, 2, 1.6, 2, 2, 1.65, 3, 2, 1.7, 4, 2, 1.75, 5, 2, 1.8, 0, 2, 1.85, 1, 2, 1.9, 2, 2, 1.95, 3, 2, 2, 4, 2, 2.05, 5, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 0, 2, 2.45, 1, 2, 2.5, 2, 2, 2.55, 3, 2, 2.6, 4, 2, 2.65, 5, 2, 2.7, 0, 2, 2.75, 1, 2, 2.8, 2, 2, 2.85, 3, 2, 2.9, 4, 2, 2.95, 5, 2, 3, 0, 2, 3.05, 1, 2, 3.1, 2, 2, 3.15, 3, 2, 3.2, 4, 2, 3.25, 5, 2, 3.3, 0, 2, 3.35, 1, 2, 3.4, 2, 2, 3.45, 3, 2, 3.5, 4, 2, 3.55, 5, 2, 3.6, 0, 2, 3.65, 1, 2, 3.7, 2, 2, 3.75, 3, 2, 3.8, 4, 2, 3.85, 5, 2, 3.9, 0, 2, 3.95, 1, 2, 4, 2, 2, 4.05, 3, 2, 4.1, 4, 2, 4.15, 5, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 0, 2, 4.55, 1, 2, 4.6, 2, 2, 4.65, 3, 2, 4.7, 4, 2, 4.75, 5, 2, 4.8, 0, 2, 4.85, 1, 2, 4.9, 2, 2, 4.95, 3, 2, 5, 4, 2, 5.05, 5, 2, 5.1, 0, 2, 5.15, 1, 2, 5.2, 2, 2, 5.25, 3, 2, 5.3, 4, 2, 5.35, 5, 2, 5.4, 0, 2, 5.45, 1, 2, 5.5, 2, 2, 5.55, 3, 2, 5.6, 4, 2, 5.65, 5, 2, 5.7, 0, 2, 5.75, 1, 2, 5.8, 2, 2, 5.85, 3, 2, 5.9, 4, 2, 5.95, 5, 2, 6, 0, 2, 6.05, 1, 2, 6.1, 2, 2, 6.15, 3, 2, 6.2, 4, 2, 6.25, 5, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 0, 2, 6.65, 1, 2, 6.7, 2, 2, 6.75, 3, 2, 6.8, 4, 2, 6.85, 5, 2, 6.9, 0, 2, 6.95, 1, 2, 7, 2, 2, 7.05, 3, 2, 7.1, 4, 2, 7.15, 5, 2, 7.2, 0, 2, 7.25, 1, 2, 7.3, 2, 2, 7.35, 3, 2, 7.4, 4, 2, 7.45, 5, 2, 7.5, 0, 2, 7.55, 1, 2, 7.6, 2, 2, 7.65, 3, 2, 7.7, 4, 2, 7.75, 5, 2, 7.8, 0, 2, 7.85, 1, 2, 7.9, 2, 2, 7.95, 3, 2, 8, 4, 2, 8.05, 5, 2, 8.1, 0, 2, 8.15, 1, 2, 8.2, 2, 2, 8.25, 3, 2, 8.3, 4, 2, 8.35, 5, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 0, 2, 8.75, 1, 2, 8.8, 2, 2, 8.85, 3, 2, 8.9, 4, 2, 8.95, 5, 2, 9, 0, 2, 9.05, 1, 2, 9.1, 2, 2, 9.15, 3, 2, 9.2, 4, 2, 9.25, 5, 2, 9.3, 0, 2, 9.35, 1, 2, 9.4, 2, 2, 9.45, 3, 2, 9.5, 4, 2, 9.55, 5, 2, 9.6, 0, 2, 9.65, 1, 2, 9.7, 2, 2, 9.75, 3, 2, 9.8, 4, 2, 9.85, 5, 2, 9.9, 0, 2, 9.95, 1, 2, 10, 2, 2, 10.05, 3, 2, 10.1, 4, 2, 10.15, 5, 2, 10.2, 0, 2, 10.25, 1, 2, 10.3, 2, 2, 10.35, 3, 2, 10.4, 4, 2, 10.45, 5, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 0, 2, 10.85, 1, 2, 10.9, 2, 2, 10.95, 3, 2, 11, 4, 2, 11.05, 5, 2, 11.1, 0, 2, 11.15, 1, 2, 11.2, 2, 2, 11.25, 3, 2, 11.3, 4, 2, 11.35, 5, 2, 11.4, 0, 2, 11.45, 1, 2, 11.5, 2, 2, 11.55, 3, 2, 11.6, 4, 2, 11.65, 5, 2, 11.7, 0, 2, 11.75, 1, 2, 11.8, 2, 2, 11.85, 3, 2, 11.9, 4, 2, 11.95, 5, 2, 12, 0, 2, 12.05, 1, 2, 12.1, 2, 2, 12.15, 3, 2, 12.2, 4, 2, 12.25, 5, 2, 12.3, 0, 2, 12.35, 1, 2, 12.4, 2, 2, 12.45, 3, 2, 12.5, 4, 2, 12.55, 5, 2, 12.6, 0, 2, 12.65, 1, 2, 12.7, 2, 2, 12.75, 3, 2, 12.8, 4, 2, 12.85, 5, 2, 12.9, 0, 2, 12.95, 1, 2, 13, 2, 2, 13.05, 3, 2, 13.1, 4, 2, 13.15, 5, 2, 13.2, 0, 2, 13.25, 1, 2, 13.3, 2, 2, 13.35, 3, 2, 13.4, 4, 2, 13.45, 5, 2, 13.5, 0, 2, 13.55, 1, 2, 13.6, 2, 2, 13.65, 3, 2, 13.7, 4, 2, 13.75, 5, 2, 13.8, 0, 2, 13.85, 1, 2, 13.9, 2, 2, 13.95, 3, 2, 14, 4, 2, 14.05, 5, 2, 14.1, 0, 2, 14.15, 1, 2, 14.2, 2, 2, 14.25, 3, 2, 14.3, 4, 2, 14.35, 5, 2, 14.4, 0, 2, 14.45, 1, 2, 14.5, 2, 2, 14.55, 3, 2, 14.6, 4, 2, 14.65, 5, 2, 14.7, 0, 2, 14.75, 1, 2, 14.8, 2, 2, 14.85, 3, 2, 14.9, 4, 2, 14.95, 5, 2, 15, 0, 2, 15.05, 1, 2, 15.1, 2, 2, 15.15, 3, 2, 15.2, 4, 2, 15.25, 5, 2, 15.3, 0, 2, 15.35, 1, 2, 15.4, 2, 2, 15.45, 3, 2, 15.5, 4, 2, 15.55, 5, 2, 15.6, 0, 2, 15.65, 1, 2, 15.7, 2, 2, 15.75, 3, 2, 15.8, 4, 2, 15.85, 5, 2, 15.9, 0, 2, 15.95, 1, 2, 16, 2, 2, 16.05, 3, 2, 16.1, 4, 2, 16.15, 5, 2, 16.2, 0, 2, 16.25, 1, 2, 16.3, 2, 2, 16.35, 3, 2, 16.4, 4, 2, 16.45, 5, 2, 16.5, 0, 2, 16.55, 1, 2, 16.6, 2, 2, 16.65, 3, 2, 16.7, 4, 2, 16.75, 5, 2, 16.8, 0, 2, 16.85, 1, 2, 16.9, 2, 2, 16.95, 3, 2, 17, 4, 2, 17.05, 5, 2, 17.1, 0, 2, 17.15, 1, 2, 17.2, 2, 2, 17.25, 3, 2, 17.3, 4, 2, 17.35, 5, 2, 17.4, 0, 2, 17.45, 1, 2, 17.5, 2, 2, 17.55, 3, 2, 17.6, 4, 2, 17.65, 5, 2, 17.7, 0, 2, 17.75, 1, 2, 17.8, 2, 2, 17.85, 3, 2, 17.9, 4, 2, 17.95, 5, 2, 18, 0, 2, 18.05, 1, 2, 18.1, 2, 2, 18.15, 3, 2, 18.2, 4, 2, 18.25, 5, 2, 18.3, 0, 2, 18.35, 1, 2, 18.4, 2, 2, 18.45, 3, 2, 18.5, 4, 2, 18.55, 5, 2, 18.6, 0, 2, 18.65, 1, 2, 18.7, 2, 2, 18.75, 3, 2, 18.8, 4, 2, 18.85, 5, 2, 18.9, 0, 2, 18.95, 1, 2, 19, 2, 2, 19.05, 3, 2, 19.1, 4, 2, 19.15, 5, 2, 19.2, 0, 2, 19.25, 1, 2, 19.3, 2, 2, 19.35, 3, 2, 19.4, 4, 2, 19.45, 5, 2, 19.5, 0, 2, 19.55, 1, 2, 19.6, 2, 2, 19.65, 3, 2, 19.7, 4, 2, 19.75, 5, 2, 19.8, 0, 2, 19.85, 1, 2, 19.9, 2, 2, 19.95, 3, 2, 20, 4, 2, 20.017, 0, 2, 20.067, 1, 2, 20.117, 2, 2, 20.167, 3, 2, 20.217, 4, 2, 20.267, 5, 2, 20.317, 0, 2, 20.367, 1, 2, 20.417, 2, 2, 20.467, 3, 2, 20.517, 4, 2, 20.567, 5, 2, 20.617, 0, 2, 20.667, 1, 2, 20.717, 2, 2, 20.767, 3, 2, 20.817, 4, 2, 20.867, 5, 2, 20.917, 0, 2, 20.967, 1, 2, 21.017, 2, 2, 21.067, 3, 2, 21.117, 4, 2, 21.167, 5, 2, 21.217, 0, 2, 21.267, 1, 2, 21.317, 2, 2, 21.367, 3, 2, 21.417, 4, 2, 21.467, 5, 2, 21.517, 0, 2, 21.567, 1, 2, 21.617, 2, 2, 21.667, 3, 2, 21.717, 4, 2, 21.767, 5, 2, 21.817, 0, 2, 21.867, 1, 2, 21.917, 2, 2, 21.967, 3, 2, 22, 3]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 6, 2, 0.35, 0, 2, 0.4, 1, 2, 0.45, 2, 2, 0.5, 3, 2, 0.55, 4, 2, 0.6, 5, 2, 0.65, 6, 2, 0.7, 0, 2, 0.75, 1, 2, 0.8, 2, 2, 0.85, 3, 2, 0.9, 4, 2, 0.95, 5, 2, 1, 6, 2, 1.05, 0, 2, 1.1, 1, 2, 1.15, 2, 2, 1.2, 3, 2, 1.25, 4, 2, 1.3, 5, 2, 1.35, 6, 2, 1.4, 0, 2, 1.45, 1, 2, 1.5, 2, 2, 1.55, 3, 2, 1.6, 4, 2, 1.65, 5, 2, 1.7, 6, 2, 1.75, 0, 2, 1.8, 1, 2, 1.85, 2, 2, 1.9, 3, 2, 1.95, 4, 2, 2, 5, 2, 2.05, 6, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 6, 2, 2.45, 0, 2, 2.5, 1, 2, 2.55, 2, 2, 2.6, 3, 2, 2.65, 4, 2, 2.7, 5, 2, 2.75, 6, 2, 2.8, 0, 2, 2.85, 1, 2, 2.9, 2, 2, 2.95, 3, 2, 3, 4, 2, 3.05, 5, 2, 3.1, 6, 2, 3.15, 0, 2, 3.2, 1, 2, 3.25, 2, 2, 3.3, 3, 2, 3.35, 4, 2, 3.4, 5, 2, 3.45, 6, 2, 3.5, 0, 2, 3.55, 1, 2, 3.6, 2, 2, 3.65, 3, 2, 3.7, 4, 2, 3.75, 5, 2, 3.8, 6, 2, 3.85, 0, 2, 3.9, 1, 2, 3.95, 2, 2, 4, 3, 2, 4.05, 4, 2, 4.1, 5, 2, 4.15, 6, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 6, 2, 4.55, 0, 2, 4.6, 1, 2, 4.65, 2, 2, 4.7, 3, 2, 4.75, 4, 2, 4.8, 5, 2, 4.85, 6, 2, 4.9, 0, 2, 4.95, 1, 2, 5, 2, 2, 5.05, 3, 2, 5.1, 4, 2, 5.15, 5, 2, 5.2, 6, 2, 5.25, 0, 2, 5.3, 1, 2, 5.35, 2, 2, 5.4, 3, 2, 5.45, 4, 2, 5.5, 5, 2, 5.55, 6, 2, 5.6, 0, 2, 5.65, 1, 2, 5.7, 2, 2, 5.75, 3, 2, 5.8, 4, 2, 5.85, 5, 2, 5.9, 6, 2, 5.95, 0, 2, 6, 1, 2, 6.05, 2, 2, 6.1, 3, 2, 6.15, 4, 2, 6.2, 5, 2, 6.25, 6, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 6, 2, 6.65, 0, 2, 6.7, 1, 2, 6.75, 2, 2, 6.8, 3, 2, 6.85, 4, 2, 6.9, 5, 2, 6.95, 6, 2, 7, 0, 2, 7.05, 1, 2, 7.1, 2, 2, 7.15, 3, 2, 7.2, 4, 2, 7.25, 5, 2, 7.3, 6, 2, 7.35, 0, 2, 7.4, 1, 2, 7.45, 2, 2, 7.5, 3, 2, 7.55, 4, 2, 7.6, 5, 2, 7.65, 6, 2, 7.7, 0, 2, 7.75, 1, 2, 7.8, 2, 2, 7.85, 3, 2, 7.9, 4, 2, 7.95, 5, 2, 8, 6, 2, 8.05, 0, 2, 8.1, 1, 2, 8.15, 2, 2, 8.2, 3, 2, 8.25, 4, 2, 8.3, 5, 2, 8.35, 6, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 6, 2, 8.75, 0, 2, 8.8, 1, 2, 8.85, 2, 2, 8.9, 3, 2, 8.95, 4, 2, 9, 5, 2, 9.05, 6, 2, 9.1, 0, 2, 9.15, 1, 2, 9.2, 2, 2, 9.25, 3, 2, 9.3, 4, 2, 9.35, 5, 2, 9.4, 6, 2, 9.45, 0, 2, 9.5, 1, 2, 9.55, 2, 2, 9.6, 3, 2, 9.65, 4, 2, 9.7, 5, 2, 9.75, 6, 2, 9.8, 0, 2, 9.85, 1, 2, 9.9, 2, 2, 9.95, 3, 2, 10, 4, 2, 10.05, 5, 2, 10.1, 6, 2, 10.15, 0, 2, 10.2, 1, 2, 10.25, 2, 2, 10.3, 3, 2, 10.35, 4, 2, 10.4, 5, 2, 10.45, 6, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 6, 2, 10.85, 0, 2, 10.9, 1, 2, 10.95, 2, 2, 11, 3, 2, 11.05, 4, 2, 11.1, 5, 2, 11.15, 6, 2, 11.2, 0, 2, 11.25, 1, 2, 11.3, 2, 2, 11.35, 3, 2, 11.4, 4, 2, 11.45, 5, 2, 11.5, 6, 2, 11.55, 0, 2, 11.6, 1, 2, 11.65, 2, 2, 11.7, 3, 2, 11.75, 4, 2, 11.8, 5, 2, 11.85, 6, 2, 11.9, 0, 2, 11.95, 1, 2, 12, 2, 2, 12.05, 3, 2, 12.1, 4, 2, 12.15, 5, 2, 12.2, 6, 2, 12.25, 0, 2, 12.3, 1, 2, 12.35, 2, 2, 12.4, 3, 2, 12.45, 4, 2, 12.5, 5, 2, 12.55, 6, 2, 12.6, 0, 2, 12.65, 1, 2, 12.7, 2, 2, 12.75, 3, 2, 12.8, 4, 2, 12.85, 5, 2, 12.9, 6, 2, 12.95, 0, 2, 13, 1, 2, 13.05, 2, 2, 13.1, 3, 2, 13.15, 4, 2, 13.2, 5, 2, 13.25, 6, 2, 13.3, 0, 2, 13.35, 1, 2, 13.4, 2, 2, 13.45, 3, 2, 13.5, 4, 2, 13.55, 5, 2, 13.6, 6, 2, 13.65, 0, 2, 13.7, 1, 2, 13.75, 2, 2, 13.8, 3, 2, 13.85, 4, 2, 13.9, 5, 2, 13.95, 6, 2, 14, 0, 2, 14.05, 1, 2, 14.1, 2, 2, 14.15, 3, 2, 14.2, 4, 2, 14.25, 5, 2, 14.3, 6, 2, 14.35, 0, 2, 14.4, 1, 2, 14.45, 2, 2, 14.5, 3, 2, 14.55, 4, 2, 14.6, 5, 2, 14.65, 6, 2, 14.7, 0, 2, 14.75, 1, 2, 14.8, 2, 2, 14.85, 3, 2, 14.9, 4, 2, 14.95, 5, 2, 15, 6, 2, 15.05, 0, 2, 15.1, 1, 2, 15.15, 2, 2, 15.2, 3, 2, 15.25, 4, 2, 15.3, 5, 2, 15.35, 6, 2, 15.4, 0, 2, 15.45, 1, 2, 15.5, 2, 2, 15.55, 3, 2, 15.6, 4, 2, 15.65, 5, 2, 15.7, 6, 2, 15.75, 0, 2, 15.8, 1, 2, 15.85, 2, 2, 15.9, 3, 2, 15.95, 4, 2, 16, 5, 2, 16.05, 6, 2, 16.1, 0, 2, 16.15, 1, 2, 16.2, 2, 2, 16.25, 3, 2, 16.3, 4, 2, 16.35, 5, 2, 16.4, 6, 2, 16.45, 0, 2, 16.5, 1, 2, 16.55, 2, 2, 16.6, 3, 2, 16.65, 4, 2, 16.7, 5, 2, 16.75, 6, 2, 16.8, 0, 2, 16.85, 1, 2, 16.9, 2, 2, 16.95, 3, 2, 17, 4, 2, 17.05, 5, 2, 17.1, 6, 2, 17.15, 0, 2, 17.2, 1, 2, 17.25, 2, 2, 17.3, 3, 2, 17.35, 4, 2, 17.4, 5, 2, 17.45, 6, 2, 17.5, 0, 2, 17.55, 1, 2, 17.6, 2, 2, 17.65, 3, 2, 17.7, 4, 2, 17.75, 5, 2, 17.8, 6, 2, 17.85, 0, 2, 17.9, 1, 2, 17.95, 2, 2, 18, 3, 2, 18.05, 4, 2, 18.1, 5, 2, 18.15, 6, 2, 18.2, 0, 2, 18.25, 1, 2, 18.3, 2, 2, 18.35, 3, 2, 18.4, 4, 2, 18.45, 5, 2, 18.5, 6, 2, 18.55, 0, 2, 18.6, 1, 2, 18.65, 2, 2, 18.7, 3, 2, 18.75, 4, 2, 18.8, 5, 2, 18.85, 6, 2, 18.9, 0, 2, 18.95, 1, 2, 19, 2, 2, 19.05, 3, 2, 19.1, 4, 2, 19.15, 5, 2, 19.2, 6, 2, 19.25, 0, 2, 19.3, 1, 2, 19.35, 2, 2, 19.4, 3, 2, 19.45, 4, 2, 19.5, 5, 2, 19.55, 6, 2, 19.6, 0, 2, 19.65, 1, 2, 19.7, 2, 2, 19.75, 3, 2, 19.8, 4, 2, 19.85, 5, 2, 19.9, 6, 2, 19.95, 0, 2, 20, 1, 2, 20.017, 0, 2, 20.067, 1, 2, 20.117, 2, 2, 20.167, 3, 2, 20.217, 4, 2, 20.267, 5, 2, 20.317, 6, 2, 20.367, 0, 2, 20.417, 1, 2, 20.467, 2, 2, 20.517, 3, 2, 20.567, 4, 2, 20.617, 5, 2, 20.667, 6, 2, 20.717, 0, 2, 20.767, 1, 2, 20.817, 2, 2, 20.867, 3, 2, 20.917, 4, 2, 20.967, 5, 2, 21.017, 6, 2, 21.067, 0, 2, 21.117, 1, 2, 21.167, 2, 2, 21.217, 3, 2, 21.267, 4, 2, 21.317, 5, 2, 21.367, 6, 2, 21.417, 0, 2, 21.467, 1, 2, 21.517, 2, 2, 21.567, 3, 2, 21.617, 4, 2, 21.667, 5, 2, 21.717, 6, 2, 21.767, 0, 2, 21.817, 1, 2, 21.867, 2, 2, 21.917, 3, 2, 21.967, 4, 2, 22, 4]}, {"Target": "Parameter", "Id": "Param51", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 6, 2, 0.35, 0, 2, 0.4, 1, 2, 0.45, 2, 2, 0.5, 3, 2, 0.55, 4, 2, 0.6, 5, 2, 0.65, 6, 2, 0.7, 0, 2, 0.75, 1, 2, 0.8, 2, 2, 0.85, 3, 2, 0.9, 4, 2, 0.95, 5, 2, 1, 6, 2, 1.05, 0, 2, 1.1, 1, 2, 1.15, 2, 2, 1.2, 3, 2, 1.25, 4, 2, 1.3, 5, 2, 1.35, 6, 2, 1.4, 0, 2, 1.45, 1, 2, 1.5, 2, 2, 1.55, 3, 2, 1.6, 4, 2, 1.65, 5, 2, 1.7, 6, 2, 1.75, 0, 2, 1.8, 1, 2, 1.85, 2, 2, 1.9, 3, 2, 1.95, 4, 2, 2, 5, 2, 2.05, 6, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 6, 2, 2.45, 0, 2, 2.5, 1, 2, 2.55, 2, 2, 2.6, 3, 2, 2.65, 4, 2, 2.7, 5, 2, 2.75, 6, 2, 2.8, 0, 2, 2.85, 1, 2, 2.9, 2, 2, 2.95, 3, 2, 3, 4, 2, 3.05, 5, 2, 3.1, 6, 2, 3.15, 0, 2, 3.2, 1, 2, 3.25, 2, 2, 3.3, 3, 2, 3.35, 4, 2, 3.4, 5, 2, 3.45, 6, 2, 3.5, 0, 2, 3.55, 1, 2, 3.6, 2, 2, 3.65, 3, 2, 3.7, 4, 2, 3.75, 5, 2, 3.8, 6, 2, 3.85, 0, 2, 3.9, 1, 2, 3.95, 2, 2, 4, 3, 2, 4.05, 4, 2, 4.1, 5, 2, 4.15, 6, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 6, 2, 4.55, 0, 2, 4.6, 1, 2, 4.65, 2, 2, 4.7, 3, 2, 4.75, 4, 2, 4.8, 5, 2, 4.85, 6, 2, 4.9, 0, 2, 4.95, 1, 2, 5, 2, 2, 5.05, 3, 2, 5.1, 4, 2, 5.15, 5, 2, 5.2, 6, 2, 5.25, 0, 2, 5.3, 1, 2, 5.35, 2, 2, 5.4, 3, 2, 5.45, 4, 2, 5.5, 5, 2, 5.55, 6, 2, 5.6, 0, 2, 5.65, 1, 2, 5.7, 2, 2, 5.75, 3, 2, 5.8, 4, 2, 5.85, 5, 2, 5.9, 6, 2, 5.95, 0, 2, 6, 1, 2, 6.05, 2, 2, 6.1, 3, 2, 6.15, 4, 2, 6.2, 5, 2, 6.25, 6, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 6, 2, 6.65, 0, 2, 6.7, 1, 2, 6.75, 2, 2, 6.8, 3, 2, 6.85, 4, 2, 6.9, 5, 2, 6.95, 6, 2, 7, 0, 2, 7.05, 1, 2, 7.1, 2, 2, 7.15, 3, 2, 7.2, 4, 2, 7.25, 5, 2, 7.3, 6, 2, 7.35, 0, 2, 7.4, 1, 2, 7.45, 2, 2, 7.5, 3, 2, 7.55, 4, 2, 7.6, 5, 2, 7.65, 6, 2, 7.7, 0, 2, 7.75, 1, 2, 7.8, 2, 2, 7.85, 3, 2, 7.9, 4, 2, 7.95, 5, 2, 8, 6, 2, 8.05, 0, 2, 8.1, 1, 2, 8.15, 2, 2, 8.2, 3, 2, 8.25, 4, 2, 8.3, 5, 2, 8.35, 6, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 6, 2, 8.75, 0, 2, 8.8, 1, 2, 8.85, 2, 2, 8.9, 3, 2, 8.95, 4, 2, 9, 5, 2, 9.05, 6, 2, 9.1, 0, 2, 9.15, 1, 2, 9.2, 2, 2, 9.25, 3, 2, 9.3, 4, 2, 9.35, 5, 2, 9.4, 6, 2, 9.45, 0, 2, 9.5, 1, 2, 9.55, 2, 2, 9.6, 3, 2, 9.65, 4, 2, 9.7, 5, 2, 9.75, 6, 2, 9.8, 0, 2, 9.85, 1, 2, 9.9, 2, 2, 9.95, 3, 2, 10, 4, 2, 10.05, 5, 2, 10.1, 6, 2, 10.15, 0, 2, 10.2, 1, 2, 10.25, 2, 2, 10.3, 3, 2, 10.35, 4, 2, 10.4, 5, 2, 10.45, 6, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 6, 2, 10.85, 0, 2, 10.9, 1, 2, 10.95, 2, 2, 11, 3, 2, 11.05, 4, 2, 11.1, 5, 2, 11.15, 6, 2, 11.2, 0, 2, 11.25, 1, 2, 11.3, 2, 2, 11.35, 3, 2, 11.4, 4, 2, 11.45, 5, 2, 11.5, 6, 2, 11.55, 0, 2, 11.6, 1, 2, 11.65, 2, 2, 11.7, 3, 2, 11.75, 4, 2, 11.8, 5, 2, 11.85, 6, 2, 11.9, 0, 2, 11.95, 1, 2, 12, 2, 2, 12.05, 3, 2, 12.1, 4, 2, 12.15, 5, 2, 12.2, 6, 2, 12.25, 0, 2, 12.3, 1, 2, 12.35, 2, 2, 12.4, 3, 2, 12.45, 4, 2, 12.5, 5, 2, 12.55, 6, 2, 12.6, 0, 2, 12.65, 1, 2, 12.7, 2, 2, 12.75, 3, 2, 12.8, 4, 2, 12.85, 5, 2, 12.9, 6, 2, 12.95, 0, 2, 13, 1, 2, 13.05, 2, 2, 13.1, 3, 2, 13.15, 4, 2, 13.2, 5, 2, 13.25, 6, 2, 13.3, 0, 2, 13.35, 1, 2, 13.4, 2, 2, 13.45, 3, 2, 13.5, 4, 2, 13.55, 5, 2, 13.6, 6, 2, 13.65, 0, 2, 13.7, 1, 2, 13.75, 2, 2, 13.8, 3, 2, 13.85, 4, 2, 13.9, 5, 2, 13.95, 6, 2, 14, 0, 2, 14.05, 1, 2, 14.1, 2, 2, 14.15, 3, 2, 14.2, 4, 2, 14.25, 5, 2, 14.3, 6, 2, 14.35, 0, 2, 14.4, 1, 2, 14.45, 2, 2, 14.5, 3, 2, 14.55, 4, 2, 14.6, 5, 2, 14.65, 6, 2, 14.7, 0, 2, 14.75, 1, 2, 14.8, 2, 2, 14.85, 3, 2, 14.9, 4, 2, 14.95, 5, 2, 15, 6, 2, 15.05, 0, 2, 15.1, 1, 2, 15.15, 2, 2, 15.2, 3, 2, 15.25, 4, 2, 15.3, 5, 2, 15.35, 6, 2, 15.4, 0, 2, 15.45, 1, 2, 15.5, 2, 2, 15.55, 3, 2, 15.6, 4, 2, 15.65, 5, 2, 15.7, 6, 2, 15.75, 0, 2, 15.8, 1, 2, 15.85, 2, 2, 15.9, 3, 2, 15.95, 4, 2, 16, 5, 2, 16.05, 6, 2, 16.1, 0, 2, 16.15, 1, 2, 16.2, 2, 2, 16.25, 3, 2, 16.3, 4, 2, 16.35, 5, 2, 16.4, 6, 2, 16.45, 0, 2, 16.5, 1, 2, 16.55, 2, 2, 16.6, 3, 2, 16.65, 4, 2, 16.7, 5, 2, 16.75, 6, 2, 16.8, 0, 2, 16.85, 1, 2, 16.9, 2, 2, 16.95, 3, 2, 17, 4, 2, 17.05, 5, 2, 17.1, 6, 2, 17.15, 0, 2, 17.2, 1, 2, 17.25, 2, 2, 17.3, 3, 2, 17.35, 4, 2, 17.4, 5, 2, 17.45, 6, 2, 17.5, 0, 2, 17.55, 1, 2, 17.6, 2, 2, 17.65, 3, 2, 17.7, 4, 2, 17.75, 5, 2, 17.8, 6, 2, 17.85, 0, 2, 17.9, 1, 2, 17.95, 2, 2, 18, 3, 2, 18.05, 4, 2, 18.1, 5, 2, 18.15, 6, 2, 18.2, 0, 2, 18.25, 1, 2, 18.3, 2, 2, 18.35, 3, 2, 18.4, 4, 2, 18.45, 5, 2, 18.5, 6, 2, 18.55, 0, 2, 18.6, 1, 2, 18.65, 2, 2, 18.7, 3, 2, 18.75, 4, 2, 18.8, 5, 2, 18.85, 6, 2, 18.9, 0, 2, 18.95, 1, 2, 19, 2, 2, 19.05, 3, 2, 19.1, 4, 2, 19.15, 5, 2, 19.2, 6, 2, 19.25, 0, 2, 19.3, 1, 2, 19.35, 2, 2, 19.4, 3, 2, 19.45, 4, 2, 19.5, 5, 2, 19.55, 6, 2, 19.6, 0, 2, 19.65, 1, 2, 19.7, 2, 2, 19.75, 3, 2, 19.8, 4, 2, 19.85, 5, 2, 19.9, 6, 2, 19.95, 0, 2, 20, 1, 2, 20.017, 0, 2, 20.067, 1, 2, 20.117, 2, 2, 20.167, 3, 2, 20.217, 4, 2, 20.267, 5, 2, 20.317, 6, 2, 20.367, 0, 2, 20.417, 1, 2, 20.467, 2, 2, 20.517, 3, 2, 20.567, 4, 2, 20.617, 5, 2, 20.667, 6, 2, 20.717, 0, 2, 20.767, 1, 2, 20.817, 2, 2, 20.867, 3, 2, 20.917, 4, 2, 20.967, 5, 2, 21.017, 6, 2, 21.067, 0, 2, 21.117, 1, 2, 21.167, 2, 2, 21.217, 3, 2, 21.267, 4, 2, 21.317, 5, 2, 21.367, 6, 2, 21.417, 0, 2, 21.467, 1, 2, 21.517, 2, 2, 21.567, 3, 2, 21.617, 4, 2, 21.667, 5, 2, 21.717, 6, 2, 21.767, 0, 2, 21.817, 1, 2, 21.867, 2, 2, 21.917, 3, 2, 21.967, 4, 2, 22, 4]}, {"Target": "Parameter", "Id": "Param42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 6.667, 0.074, 13.333, 0.148, 20, 0.222, 1, 20.006, 0.148, 20.011, 0.074, 20.017, 0, 1, 20.678, 0.007, 21.339, 0.015, 22, 0.022]}, {"Target": "Parameter", "Id": "Param45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.4, 1, 6.667, -0.326, 13.333, -0.252, 20, -0.178, 1, 20.006, -0.252, 20.011, -0.326, 20.017, -0.4, 1, 20.678, -0.393, 21.339, -0.385, 22, -0.378]}, {"Target": "Parameter", "Id": "Param46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.3, 1, 6.667, 0.374, 13.333, 0.448, 20, 0.522, 1, 20.006, 0.448, 20.011, 0.374, 20.017, 0.3, 1, 20.678, 0.307, 21.339, 0.315, 22, 0.322]}, {"Target": "Parameter", "Id": "Param50", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 6.667, 1.333, 13.333, 2.667, 20, 4, 1, 20.006, 2.667, 20.011, 1.333, 20.017, 0, 1, 20.678, 0.132, 21.339, 0.265, 22, 0.397]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2.5, 10, 0, 5.017, -10, 0, 7.5, 10, 0, 10, -10, 0, 12.5, 26.92, 0, 15.017, -10, 0, 17.5, 10, 0, 20, -10, 2, 20.017, -10, 0, 22, 7.79]}, {"Target": "Parameter", "Id": "Param17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.76, 0, 2.5, 0.66, 0, 5.017, -5.76, 0, 7.5, 0.66, 0, 10, -5.76, 0, 12.5, 21.06, 0, 15.017, -5.76, 0, 17.5, 0.66, 0, 20, -5.76, 2, 20.017, -5.76, 0, 22, -0.049]}, {"Target": "Parameter", "Id": "Param49", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.35, 0, 1.25, 0, 0, 3.75, 0.7, 0, 6.267, 0, 0, 8.767, 0.7, 0, 11.283, 0, 0, 13.783, 0.7, 0, 16.3, 0, 0, 18.8, 0.7, 1, 19.2, 0.7, 19.6, 0.593, 20, 0.374, 1, 20.006, 0.371, 20.011, 0.353, 20.017, 0.35, 1, 20.434, 0.115, 20.85, 0, 21.267, 0, 0, 22, 0.145]}, {"Target": "Parameter", "Id": "Param40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.25, 0, 0.917, 10, 0, 2.917, -10, 0, 4.917, 10, 0, 6.917, -10, 0, 8.917, 10, 0, 10.917, -10, 0, 12.917, 10, 0, 14.917, -10, 0, 16.917, 10, 0, 18.917, -10, 0, 20, 1.25, 2, 20.017, 1.25, 0, 20.933, 10, 0, 22, -0.998]}, {"Target": "Parameter", "Id": "Param41", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2, 10, 0, 4, -10, 0, 6, 10, 0, 8, -10, 0, 10, 10, 0, 12, -10, 0, 14, 10, 0, 16, -10, 0, 18, 10, 0, 20, -10, 2, 20.017, -10, 0, 22, 9.996]}, {"Target": "Parameter", "Id": "Param32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2, 10, 0, 4, -10, 0, 6, 10, 0, 8, -10, 0, 10, 10, 0, 12, -10, 0, 14, 10, 0, 16, -10, 0, 18, 10, 0, 20, -10, 2, 20.017, -10, 0, 22, 9.996]}, {"Target": "Parameter", "Id": "Param33", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.886, 0, 0.583, 10, 0, 2.583, -10, 0, 4.583, 10, 0, 6.583, -10, 0, 8.583, 10, 0, 10.583, -10, 0, 12.583, 10, 0, 14.583, -10, 0, 16.583, 10, 0, 18.583, -10, 0, 20, 5.886, 2, 20.017, 5.886, 0, 20.6, 10, 0, 22, -5.679]}, {"Target": "Parameter", "Id": "Param34", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -14.451, 0, 1.333, 30, 0, 3.333, -30, 0, 5.333, 30, 0, 7.333, -30, 0, 9.333, 30, 0, 11.333, -30, 0, 13.333, 30, 0, 15.333, -30, 0, 17.333, 30, 0, 19.333, -30, 0, 20, -14.451, 2, 20.017, -14.451, 0, 21.35, 30, 0, 22, 15.111]}, {"Target": "Parameter", "Id": "Param28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -15, 0, 2, 15, 0, 4, -15, 0, 6, 15, 0, 8, -15, 0, 10, 15, 0, 12, -15, 0, 14, 15, 0, 16, -15, 0, 18, 15, 0, 20, -15, 2, 20.017, -15, 0, 22, 14.994]}, {"Target": "Parameter", "Id": "Param29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.886, 0, 0.583, 10, 0, 2.583, -10, 0, 4.583, 10, 0, 6.583, -10, 0, 8.583, 10, 0, 10.583, -10, 0, 12.583, 10, 0, 14.583, -10, 0, 16.583, 10, 0, 18.583, -10, 0, 20, 5.886, 2, 20.017, 5.886, 0, 20.6, 10, 0, 22, -5.679]}, {"Target": "Parameter", "Id": "Param35", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -14.451, 0, 1.333, 30, 0, 3.333, -30, 0, 5.333, 30, 0, 7.333, -30, 0, 9.333, 30, 0, 11.333, -30, 0, 13.333, 30, 0, 15.333, -30, 0, 17.333, 30, 0, 19.333, -30, 0, 20, -14.451, 2, 20.017, -14.451, 0, 21.35, 30, 0, 22, 15.111]}, {"Target": "Parameter", "Id": "Param30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.959, 0, 0.8, 10, 0, 2.8, -10, 0, 4.8, 10, 0, 6.8, -10, 0, 8.8, 10, 0, 10.8, -10, 0, 12.8, 10, 0, 14.8, -10, 0, 16.8, 10, 0, 18.8, -10, 0, 20, 2.959, 2, 20.017, 2.959, 0, 20.817, 10, 0, 22, -2.719]}, {"Target": "Parameter", "Id": "Param31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 9.44, 0, 1.8, -10, 0, 3.8, 10, 0, 5.8, -10, 0, 7.8, 10, 0, 9.8, -10, 0, 11.8, 10, 0, 13.8, -10, 0, 15.8, 10, 0, 17.8, -10, 0, 19.8, 10, 0, 20, 9.44, 2, 20.017, 9.44, 0, 21.817, -10, 0, 22, -9.527]}, {"Target": "Parameter", "Id": "Param36", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 13.773, 0, 0.683, 30, 0, 2.683, -30, 0, 4.683, 30, 0, 6.683, -30, 0, 8.683, 30, 0, 10.683, -30, 0, 12.683, 30, 0, 14.683, -30, 0, 16.683, 30, 0, 18.683, -30, 0, 20, 13.773, 2, 20.017, 13.773, 0, 20.7, 30, 0, 22, -13.097]}, {"Target": "Parameter", "Id": "GL1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 6.667, 0.297, 13.333, 0.593, 20, 0.89, 1, 20.006, 0.593, 20.011, 0.297, 20.017, 0, 1, 20.678, 0.029, 21.339, 0.059, 22, 0.088]}, {"Target": "Parameter", "Id": "GL2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.39, 1, 6.667, 0.687, 13.333, 0.983, 20, 1.28, 1, 20.006, 0.983, 20.011, 0.687, 20.017, 0.39, 1, 20.678, 0.419, 21.339, 0.449, 22, 0.478]}, {"Target": "Parameter", "Id": "GL3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.72, 1, 6.667, 1.017, 13.333, 1.313, 20, 1.61, 1, 20.006, 1.313, 20.011, 1.017, 20.017, 0.72, 1, 20.678, 0.749, 21.339, 0.779, 22, 0.808]}, {"Target": "Parameter", "Id": "GL4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.21, 1, 6.667, 0.507, 13.333, 0.803, 20, 1.1, 1, 20.006, 0.803, 20.011, 0.507, 20.017, 0.21, 1, 20.678, 0.239, 21.339, 0.269, 22, 0.298]}, {"Target": "Parameter", "Id": "GL5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.53, 1, 6.667, 0.827, 13.333, 1.123, 20, 1.42, 1, 20.006, 1.123, 20.011, 0.827, 20.017, 0.53, 1, 20.678, 0.559, 21.339, 0.589, 22, 0.618]}, {"Target": "Parameter", "Id": "GL6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.89, 1, 6.667, 1.187, 13.333, 1.483, 20, 1.78, 1, 20.006, 1.483, 20.011, 1.187, 20.017, 0.89, 1, 20.678, 0.919, 21.339, 0.949, 22, 0.978]}, {"Target": "Parameter", "Id": "GL7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.77, 1, 6.667, 1.067, 13.333, 1.363, 20, 1.66, 1, 20.006, 1.363, 20.011, 1.067, 20.017, 0.77, 1, 20.678, 0.799, 21.339, 0.829, 22, 0.858]}, {"Target": "Parameter", "Id": "GL8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.09, 1, 6.667, 0.387, 13.333, 0.683, 20, 0.98, 1, 20.006, 0.683, 20.011, 0.387, 20.017, 0.09, 1, 20.678, 0.119, 21.339, 0.149, 22, 0.178]}, {"Target": "Parameter", "Id": "GL9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.301, 1, 6.667, 0.598, 13.333, 0.894, 20, 1.191, 1, 20.006, 0.894, 20.011, 0.598, 20.017, 0.301, 1, 20.678, 0.33, 21.339, 0.36, 22, 0.389]}, {"Target": "Parameter", "Id": "GL10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.45, 1, 6.667, 0.747, 13.333, 1.043, 20, 1.34, 1, 20.006, 1.043, 20.011, 0.747, 20.017, 0.45, 1, 20.678, 0.479, 21.339, 0.509, 22, 0.538]}, {"Target": "Parameter", "Id": "GL11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.64, 1, 6.667, 0.937, 13.333, 1.233, 20, 1.53, 1, 20.006, 1.233, 20.011, 0.937, 20.017, 0.64, 1, 20.678, 0.669, 21.339, 0.699, 22, 0.728]}, {"Target": "Parameter", "Id": "GL12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.15, 1, 6.667, 0.447, 13.333, 0.743, 20, 1.04, 1, 20.006, 0.743, 20.011, 0.447, 20.017, 0.15, 1, 20.678, 0.179, 21.339, 0.209, 22, 0.238]}, {"Target": "Parameter", "Id": "GL13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.585, 1, 6.667, 0.882, 13.333, 1.178, 20, 1.475, 1, 20.006, 1.178, 20.011, 0.882, 20.017, 0.585, 1, 20.678, 0.614, 21.339, 0.644, 22, 0.673]}, {"Target": "Parameter", "Id": "GL14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 6.667, 0.297, 13.333, 0.593, 20, 0.89, 1, 20.006, 0.593, 20.011, 0.297, 20.017, 0, 1, 20.678, 0.029, 21.339, 0.059, 22, 0.088]}, {"Target": "Parameter", "Id": "Param161", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.51, 1, 5.994, 0.843, 11.989, 1.177, 17.983, 1.51, 2, 18, 0.51, 1, 18.667, 0.547, 19.333, 0.584, 20, 0.621, 1, 20.006, 0.584, 20.011, 0.547, 20.017, 0.51, 1, 20.678, 0.547, 21.339, 0.583, 22, 0.62]}, {"Target": "Parameter", "Id": "Param162", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.31, 1, 5.994, 0.643, 11.989, 0.977, 17.983, 1.31, 2, 18, 0.31, 1, 18.667, 0.347, 19.333, 0.384, 20, 0.421, 1, 20.006, 0.384, 20.011, 0.347, 20.017, 0.31, 1, 20.678, 0.347, 21.339, 0.383, 22, 0.42]}, {"Target": "Parameter", "Id": "Param163", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.72, 1, 5.994, 1.053, 11.989, 1.387, 17.983, 1.72, 2, 18, 0.72, 1, 18.667, 0.757, 19.333, 0.794, 20, 0.831, 1, 20.006, 0.794, 20.011, 0.757, 20.017, 0.72, 1, 20.678, 0.757, 21.339, 0.793, 22, 0.83]}, {"Target": "Parameter", "Id": "Param164", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 5.994, 0.333, 11.989, 0.667, 17.983, 1, 2, 18, 0, 1, 18.667, 0.037, 19.333, 0.074, 20, 0.111, 1, 20.006, 0.074, 20.011, 0.037, 20.017, 0, 1, 20.678, 0.037, 21.339, 0.073, 22, 0.11]}, {"Target": "Parameter", "Id": "Param165", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.17, 1, 5.994, 0.503, 11.989, 0.837, 17.983, 1.17, 2, 18, 0.17, 1, 18.667, 0.207, 19.333, 0.244, 20, 0.281, 1, 20.006, 0.244, 20.011, 0.207, 20.017, 0.17, 1, 20.678, 0.207, 21.339, 0.243, 22, 0.28]}, {"Target": "Parameter", "Id": "touch_drag21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param54", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param118", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "man_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "man_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param78", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param79", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "man_<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "man_<PERSON>ze", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param189", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "shangguang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param186", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param96", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param97", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param98", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "shangguang2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param53", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param76", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param77", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "MB_xingfengTMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param127", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Mouthfunnel", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param293", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param72", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param74", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param75", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param73", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "MB_fenweiTMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "MB_fenweiliangdu", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 22, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiandu", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 22, 0.6]}, {"Target": "Parameter", "Id": "MB_fenweiqiu1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 22, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 22, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 22, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 22, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 22, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 22, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiushangxia", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "MB_fenweiqiudaxiao", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param58", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamHandRREDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param55", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamHandLREDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 22, 1]}, {"Target": "Parameter", "Id": "Param167", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param169", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param196", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param56", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param59", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10, 0, 22, 10]}, {"Target": "Parameter", "Id": "Param44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param57", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param124", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param80", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param38", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param60", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param47", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param121", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param81", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param95", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param125", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param171", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param166", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param123", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param39", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.5, 0, 22, 0.5]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param61", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 22, 1]}, {"Target": "Parameter", "Id": "Param71", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 22, 1]}, {"Target": "Parameter", "Id": "Hair_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param173", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param174", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param175", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param176", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param190", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param191", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param192", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param193", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param65", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 22, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamShoulderStretch3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamShoulderStretch4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamChestZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamWaistZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamButtZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param153", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param154", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param155", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param156", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param82", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param83", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param84", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param85", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param66", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param67", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param68", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param62", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param63", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param64", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param86", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param89", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param87", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param88", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param94", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param126", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param128", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param129", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param52", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param70", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param208", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "Param23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "touch_drag23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "touch_drag24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "touch_drag25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "touch_drag26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "touch_drag27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "touch_drag28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "touch_drag29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "touch_drag30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "touch_drag31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "touch_drag32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "Parameter", "Id": "empty23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 22, 0]}, {"Target": "PartOpacity", "Id": "Part7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 22, 1]}], "UserData": []}