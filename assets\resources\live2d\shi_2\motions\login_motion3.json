{"Version": 3, "Meta": {"Duration": 13.0, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 238, "TotalSegmentCount": 3130, "TotalPointCount": 3856, "UserDataCount": 2, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 4.2, 0, 2.7, 1, 0, 4.483, 1.273, 2, 4.5, -0.043, 0, 6.583, 0.232, 0, 8.333, -0.662, 0, 13, 0]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.2, 0, 2.7, -1.659, 0, 4.483, 3.84, 2, 4.5, 3.408, 1, 5.194, 3.166, 5.889, 3.048, 6.583, 2.686, 1, 7.166, 2.382, 7.75, 0.522, 8.333, 0.393, 1, 9.889, 0.049, 11.444, 0, 13, 0]}, {"Target": "Parameter", "Id": "All_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 4.483, 0, 1, 4.489, 0, 4.494, -0.132, 4.5, -0.134, 1, 4.639, -0.176, 4.778, -0.194, 4.917, -0.194, 0, 5.233, 0.189, 0, 5.617, -0.282, 0, 6.25, 0.371, 0, 6.683, 0.286, 0, 7.15, 0.485, 0, 7.767, -0.331, 0, 8.35, 0.117, 0, 8.967, -0.098, 0, 9.35, 0.152, 0, 9.65, 0.056, 0, 10.033, 0.227, 0, 10.55, 0.134, 0, 10.95, 0.516, 0, 11.283, -0.033, 0, 11.833, 0.158, 0, 12.15, 0.114, 0, 12.817, 0.507, 0, 13, -0.134]}, {"Target": "Parameter", "Id": "All_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 4.483, 0, 1, 4.489, 0, 4.494, -0.241, 4.5, -0.244, 1, 4.65, -0.314, 4.8, -0.346, 4.95, -0.346, 0, 5.467, 0.419, 0, 6.867, -0.586, 0, 7.3, 0.51, 0, 8.067, -0.397, 0, 8.683, -0.203, 0, 9.017, -0.306, 1, 9.228, -0.306, 9.439, -0.318, 9.65, -0.258, 1, 9.872, -0.195, 10.095, 0.266, 10.317, 0.266, 0, 10.983, -0.469, 0, 11.7, 0.008, 0, 12.017, -0.026, 0, 12.217, 0.018, 0, 12.883, -0.498, 0, 13, -0.244]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 4.483, 2.34, 2, 4.5, -20.88, 1, 5.194, -20.88, 5.889, -20.502, 6.583, -18, 1, 7.166, -15.898, 7.75, -9.062, 8.333, -8.132, 1, 9.328, -6.546, 10.322, -6.353, 11.317, -4.782, 1, 11.878, -3.895, 12.439, 0, 13, 0]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 8.71, 0, 2.7, 7.277, 1, 3.294, 7.277, 3.889, 10.985, 4.483, 18.59, 2, 4.5, 33.93, 1, 5.194, 33.93, 5.889, 17.858, 6.583, 15.252, 1, 7.166, 13.063, 7.75, 12.937, 8.333, 11.861, 1, 9.328, 10.027, 10.322, 8.977, 11.317, 6.511, 1, 11.878, 5.12, 12.439, 0, 13, 0]}, {"Target": "Parameter", "Id": "man_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 21.78, 2, 4.483, 21.78, 2, 4.5, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Param78", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.017, 0, 0, 3.983, 0.032, 2, 13, 0.032]}, {"Target": "Parameter", "Id": "Param79", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.017, 0, 0, 3.983, -0.114, 2, 13, -0.114]}, {"Target": "Parameter", "Id": "Param22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 4.483, 1, 2, 4.5, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Param76", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 4.033, 1, 1, 4.183, 1, 4.333, 0.517, 4.483, 0.004, 1, 4.489, 0, 4.494, 0, 4.5, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Param26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 30, 0, 4.483, 0, 0, 5.283, 5.951, 0, 7.683, -4.62, 0, 11.317, 30, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamAngleX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.365, 0, 0.567, 1.834, 0, 1.567, -1.834, 0, 2.567, 1.834, 0, 3.75, -1.696, 0, 4.5, -1.304, 0, 4.8, -8.126, 1, 4.983, -8.126, 5.167, 3.008, 5.35, 4, 1, 5.839, 6.647, 6.328, 7.241, 6.817, 10, 1, 7.084, 11.505, 7.35, 17.554, 7.617, 17.554, 0, 8.15, 3.597, 0, 8.75, 8.107, 0, 9.583, 3.052, 0, 10, 9.326, 0, 10.7, -11.927, 0, 11.417, -0.316, 0, 11.717, -3.699, 0, 12.017, 4, 0, 12.533, -7, 0, 13, -1.304]}, {"Target": "Parameter", "Id": "ParamAngleY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.903, 0, 0.25, -1.313, 0, 1.25, 1.313, 0, 2.25, -1.313, 0, 3.25, 1.313, 1, 3.667, 1.313, 4.083, 0.997, 4.5, -0.124, 1, 4.578, -0.333, 4.655, -3.912, 4.733, -3.912, 0, 5.083, 9.215, 0, 5.583, -8.809, 0, 6.2, 1, 0, 6.65, -1, 0, 7.117, 17.114, 0, 7.783, -6.412, 0, 8.383, 6.259, 0, 9.083, 0.154, 0, 9.683, 11.593, 0, 10.267, 5.059, 0, 10.667, 16.445, 0, 11.033, 2.492, 0, 11.567, 6.421, 0, 11.883, 4.744, 0, 12.633, 10, 0, 13, -0.124]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -12, 0, 1.383, -30, 2, 1.9, -30, 0, 2.9, -27.979, 0, 3.75, -29.141, 0, 4.5, 0.543, 0, 4.8, -3.35, 0, 5.2, -1.899, 0, 5.583, -5.918, 1, 5.8, -5.918, 6.016, -3.749, 6.233, -3.342, 1, 6.561, -2.726, 6.889, -2.728, 7.217, -2.728, 1, 7.361, -2.728, 7.506, -4.586, 7.65, -5.621, 1, 7.772, -6.497, 7.895, -6.472, 8.017, -6.472, 0, 8.467, -4.851, 0, 9.1, -5.506, 0, 9.717, 0, 0, 10.133, -4.66, 0, 10.7, 2, 1, 10.85, 2, 11, -5.574, 11.15, -7.411, 1, 11.411, -10.608, 11.672, -10.852, 11.933, -10.852, 0, 12.65, 6, 0, 13, 0.543]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 28.994, 0, 0.433, 30, 0, 1.517, 18.323, 0, 2.667, 30, 2, 2.683, 30, 1, 3.039, 30, 3.394, 30, 3.75, 28.806, 1, 4, 27.877, 4.25, -4.357, 4.5, -4.357, 0, 5.017, -0.087, 0, 5.4, -10.506, 1, 5.617, -10.506, 5.833, 3.165, 6.05, 4.273, 1, 6.378, 5.948, 6.705, 5.867, 7.033, 5.867, 1, 7.178, 5.867, 7.322, -6.66, 7.467, -9.735, 1, 7.589, -12.337, 7.711, -11.942, 7.833, -11.942, 0, 8.283, -7.739, 0, 8.917, -9.438, 1, 9.261, -9.438, 9.606, -9.453, 9.95, -8, 1, 10.172, -7.062, 10.395, -0.775, 10.617, -0.775, 1, 10.772, -0.775, 10.928, -5.577, 11.083, -7.411, 1, 11.344, -10.49, 11.606, -10.852, 11.867, -10.852, 0, 12.517, 2, 0, 13, -4.357]}, {"Target": "Parameter", "Id": "ParamBodyAngleX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -23.202, 0, 1.383, 0, 0, 3.383, -30, 1, 3.505, -30, 3.628, -30, 3.75, -29.938, 1, 4, -29.684, 4.25, -8.417, 4.5, -8.417, 0, 4.683, -13, 0, 5.133, 8.898, 1, 5.322, 8.898, 5.511, -1.611, 5.7, -3.294, 1, 6.022, -6.165, 6.345, -6.258, 6.667, -6.258, 1, 6.806, -6.258, 6.944, 5.672, 7.083, 7.377, 1, 7.194, 8.741, 7.306, 8.418, 7.417, 8.418, 0, 7.917, 1.913, 0, 8.583, 3.784, 0, 9.367, 2.496, 0, 9.783, 6.328, 0, 10.5, -7.606, 0, 10.833, 1.013, 0, 11.317, -19, 0, 11.767, 0, 0, 12.35, -14.891, 0, 12.817, 1.013, 0, 13, -8.417]}, {"Target": "Parameter", "Id": "ParamBodyAngleY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 22.67, 0, 0.4, 18.691, 0, 1.4, 30, 0, 2.4, 18.691, 0, 3.4, 30, 1, 3.517, 30, 3.633, 30, 3.75, 29.918, 1, 4, 29.556, 4.25, -9.558, 4.5, -9.558, 0, 4.917, 8.992, 0, 5.417, -24.296, 0, 5.983, 23.968, 0, 6.433, 20.304, 0, 6.933, 28.939, 1, 7.083, 28.939, 7.233, -3.87, 7.383, -18.303, 1, 7.483, -27.925, 7.583, -26.947, 7.683, -26.947, 0, 8.2, 1.602, 0, 8.883, -7.125, 0, 9.267, 0.512, 0, 9.583, -4.082, 1, 9.75, -4.082, 9.916, 3.387, 10.083, 9, 1, 10.222, 13.678, 10.361, 14, 10.5, 14, 0, 10.9, -2, 0, 11.583, 13.762, 0, 12.05, -1, 0, 12.467, 14, 0, 13, -9.558]}, {"Target": "Parameter", "Id": "ParamWaistZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 0.433, -12.763, 0, 1.133, 14.457, 1, 1.505, 14.457, 1.878, 14.534, 2.25, 14, 1, 2.567, 13.545, 2.883, 2.848, 3.2, 2, 1, 3.383, 1.509, 3.567, 1.831, 3.75, 1.375, 1, 4, 0.753, 4.25, -9.355, 4.5, -13.805, 1, 4.561, -14.893, 4.622, -14.902, 4.683, -14.902, 0, 5.1, -8.143, 0, 5.667, -19.479, 0, 6.1, -17.429, 0, 6.633, -19.592, 0, 7.183, -7.929, 0, 7.983, -14.873, 1, 8.089, -14.873, 8.194, -14.182, 8.3, -14.09, 1, 8.35, -14.046, 8.4, -14.066, 8.45, -14.066, 1, 8.622, -14.066, 8.795, -15.913, 8.967, -15.955, 1, 9.106, -15.989, 9.244, -15.978, 9.383, -15.978, 0, 9.867, -11.925, 0, 10.4, -17.722, 0, 10.683, -16.878, 0, 10.983, -18.103, 0, 11.75, -11.376, 0, 12.267, -17.55, 0, 12.85, -12.564, 0, 12.983, -13.81, 0, 13, -13.805]}, {"Target": "Parameter", "Id": "ParamShoulderStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.149, 0, 0.6, -10, 0, 1.6, -2.515, 0, 2.6, -10, 1, 2.983, -10, 3.367, -3.066, 3.75, -2.969, 1, 4, -2.906, 4.25, -2.928, 4.5, -2.893, 1, 4.594, -2.88, 4.689, 8.992, 4.783, 8.992, 0, 5.283, -24.296, 0, 5.85, 16.791, 0, 6.3, 14.271, 0, 6.8, 20.208, 1, 6.95, 20.208, 7.1, -2.299, 7.25, -12.273, 1, 7.35, -18.922, 7.45, -18.216, 7.55, -18.216, 0, 8.067, 1.602, 0, 8.75, -7.125, 0, 9.133, -0.487, 1, 9.239, -0.487, 9.344, -2.722, 9.45, -5.082, 1, 9.617, -8.808, 9.783, -9.971, 9.95, -9.971, 0, 10.383, 2.28, 1, 10.572, 2.28, 10.761, -5.821, 10.95, -6.765, 1, 11.267, -8.347, 11.583, -8.35, 11.9, -8.35, 0, 12.45, -1.991, 0, 13, -2.893]}, {"Target": "Parameter", "Id": "ParamShoulderStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -25.012, 0, 0.6, -30, 0, 1.6, -22.305, 0, 2.6, -30, 1, 2.983, -30, 3.367, -28.801, 3.75, -22.772, 1, 4, -18.84, 4.25, -11.334, 4.5, -3.371, 1, 4.661, 1.761, 4.822, 8.992, 4.983, 8.992, 0, 5.483, -24.296, 0, 6.05, 16.791, 0, 6.5, 14.271, 0, 7, 20.208, 1, 7.15, 20.208, 7.3, -2.299, 7.45, -12.273, 1, 7.55, -18.922, 7.65, -18.216, 7.75, -18.216, 0, 8.267, 1.602, 0, 8.95, -7.125, 0, 9.333, -0.487, 1, 9.439, -0.487, 9.544, -1.097, 9.65, -3.573, 1, 9.817, -7.482, 9.983, -10.317, 10.15, -10.317, 0, 10.583, 0.605, 1, 10.739, 0.605, 10.894, -6.536, 11.05, -7.306, 1, 11.372, -8.901, 11.695, -9.006, 12.017, -9.006, 0, 12.55, -2.187, 0, 13, -3.371]}, {"Target": "Parameter", "Id": "ParamButtXA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -30, 0, 1, 19.235, 0, 2, 0, 0, 3, 19.235, 0, 4.5, -6.39, 0, 4.683, -5.227, 1, 4.828, -5.233, 4.972, -9.995, 5.117, -10, 1, 5.306, -9.996, 5.494, 8.557, 5.683, 8.566, 1, 5.844, 8.565, 6.006, 7.473, 6.167, 7.472, 1, 6.328, 7.476, 6.489, 11.074, 6.65, 11.077, 1, 6.894, 11.065, 7.139, -6.102, 7.383, -6.102, 1, 7.922, -6.127, 8.461, 2.981, 9, 2.978, 1, 9.267, 2.977, 9.533, -2, 9.8, -2, 1, 9.933, -1.996, 10.067, 5.996, 10.2, 6, 1, 10.378, 5.992, 10.555, -1.531, 10.733, -1.539, 1, 10.833, -1.541, 10.933, -0.388, 11.033, -0.39, 1, 11.272, -0.388, 11.511, -4.002, 11.75, -4, 1, 11.889, -3.997, 12.028, -0.003, 12.167, 0, 1, 12.35, 0.01, 12.534, -8.519, 12.717, -8.518, 1, 12.811, -8.509, 12.906, -6.39, 13, -6.39]}, {"Target": "Parameter", "Id": "Param43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -30, 0, 1.333, 14, 0, 2.333, -7.236, 0, 3.333, 14, 1, 3.722, 14, 4.111, 7.609, 4.5, -1.915, 1, 4.583, -3.956, 4.667, -4, 4.75, -4, 1, 4.917, -4.012, 5.083, 6.612, 5.25, 6.613, 1, 5.439, 6.607, 5.628, -4.913, 5.817, -4.911, 1, 5.967, -4.913, 6.117, -4.034, 6.267, -4.036, 1, 6.434, -4.032, 6.6, -6.102, 6.767, -6.098, 1, 6.995, -6.102, 7.222, 7.258, 7.45, 7.261, 1, 8, 7.248, 8.55, -0.593, 9.1, -0.59, 1, 9.206, -0.597, 9.311, 1.199, 9.417, 1.192, 1, 9.578, 1.188, 9.739, -2.996, 9.9, -3, 1, 10.05, -3.006, 10.2, 0.136, 10.35, 0.13, 1, 10.511, 0.129, 10.672, -1.252, 10.833, -1.253, 1, 10.916, -1.249, 11, -0.092, 11.083, -0.088, 1, 11.344, -0.091, 11.606, -5.997, 11.867, -6, 1, 12.017, -5.998, 12.167, 3.435, 12.317, 3.44, 1, 12.484, 3.447, 12.65, -3.147, 12.817, -3.148, 1, 12.878, -3.145, 12.939, -1.915, 13, -1.915]}, {"Target": "Parameter", "Id": "ParamButtZA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 29.968, 0, 0.033, 30, 0, 1.033, 20, 0, 2.033, 30, 0, 3.033, 20, 0, 3.75, 23.759, 1, 4, 23.759, 4.25, -0.933, 4.5, -5.239, 1, 4.65, -7.823, 4.8, -7, 4.95, -7, 1, 5.106, -7.009, 5.261, 4.207, 5.417, 4.198, 1, 5.6, 4.197, 5.784, 3.624, 5.967, 3.623, 1, 6.117, 3.619, 6.267, 5.522, 6.417, 5.518, 1, 6.717, 5.523, 7.017, -3.511, 7.317, -3.51, 1, 7.822, -3.538, 8.328, 1.411, 8.833, 1.404, 1, 8.95, 1.408, 9.066, -0.643, 9.183, -0.639, 1, 9.328, -0.641, 9.472, 0.67, 9.617, 0.674, 1, 9.917, 0.675, 10.217, -4.995, 10.517, -5, 1, 10.661, -4.999, 10.806, -4.45, 10.95, -4.449, 1, 11.083, -4.44, 11.217, -9.009, 11.35, -9, 1, 11.517, -9.014, 11.683, 2.998, 11.85, 3, 1, 12.061, 2.995, 12.272, -8.218, 12.483, -8.218, 1, 12.655, -8.224, 12.828, -5.239, 13, -5.239]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 15, 2, 3.75, 15, 0, 4.267, 0, 2, 4.5, 0, 2, 7.2, 0, 2, 7.217, 15, 2, 7.55, 15, 2, 7.567, 0, 2, 11.9, 0, 0, 12.017, 15, 2, 12.5, 15, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param58", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.75, 0, 2, 4.5, 0, 2, 6.083, 0, 2, 6.967, 0, 0, 7.983, 15, 0, 11.333, 14.102, 0, 12.25, 30, 0, 12.5, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.75, 0, 2, 4.5, 0, 2, 6.083, 0, 2, 6.967, 0, 0, 7.55, 16, 1, 8.811, 16, 10.072, 15.84, 11.333, 15.111, 1, 11.639, 14.934, 11.944, 8.465, 12.25, 4.278, 1, 12.333, 3.136, 12.417, 0, 12.5, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -14, 2, 3.75, -14, 0, 4.5, 24.978, 1, 4.856, 24.978, 5.211, 24.947, 5.567, 23.768, 1, 5.739, 23.197, 5.911, -12, 6.083, -12, 2, 6.967, -12, 0, 7.267, 21.32, 0, 7.567, -0.52, 1, 8.822, -0.52, 10.078, 1.114, 11.333, 5.214, 1, 11.511, 5.794, 11.689, 7, 11.867, 7, 0, 12.25, 0, 1, 12.333, 0, 12.417, 24.95, 12.5, 24.959, 1, 12.667, 24.978, 12.833, 24.978, 13, 24.978]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.75, 0, 0, 4.5, -9.422, 0, 6.083, -2, 2, 6.967, -2, 0, 7.267, -6.962, 0, 7.567, 1.027, 0, 7.983, -7.94, 1, 9.278, -7.94, 10.572, -6.56, 11.867, -3, 1, 11.995, -2.649, 12.122, 0, 12.25, 0, 0, 12.5, -9.471, 0, 13, -9.422]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.473, 0, 1.417, -0.703, 0, 3.417, 0.778, 0, 4.5, -1.112, 1, 4.661, -1.112, 4.822, -1.441, 4.983, -0.727, 1, 5.35, 0.898, 5.716, 20.005, 6.083, 22.42, 1, 6.378, 24.359, 6.672, 23.995, 6.967, 23.995, 1, 7.167, 23.995, 7.367, 12.619, 7.567, 4.19, 1, 7.706, -1.663, 7.844, -1.744, 7.983, -1.744, 1, 9.1, -1.744, 10.216, -0.942, 11.333, 1.07, 1, 11.489, 1.35, 11.644, 1.95, 11.8, 1.95, 0, 12.183, -1.166, 0, 12.433, 3.828, 0, 13, 2.038]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -23.56, 2, 3.75, -23.56, 0, 4.5, 0.871, 0, 4.767, 0.696, 0, 5.15, 1.863, 0, 6.083, -22.2, 1, 6.378, -22.2, 6.672, -22.246, 6.967, -18.84, 1, 7.167, -16.526, 7.367, 5.565, 7.567, 15.73, 1, 7.706, 22.789, 7.844, 24.824, 7.983, 24.824, 0, 11.333, 22.014, 0, 11.933, 26.86, 0, 12.567, -3.413, 0, 13, 0.871]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 1.5, 0, 3, 0.041, 4.5, 0.131, 1, 4.55, 0.134, 4.6, 0.879, 4.65, 0.879, 0, 4.917, -5, 0, 5.433, 7.587, 0, 6.083, -5.498, 2, 6.967, -5.498, 1, 7.167, -5.498, 7.367, 2.67, 7.567, 6.22, 1, 7.706, 8.685, 7.844, 8.327, 7.983, 8.327, 0, 8.65, 7.917, 0, 9.1, 9.976, 0, 9.917, 7.066, 0, 10.383, 12.019, 1, 10.7, 12.019, 11.016, 11.238, 11.333, 7.24, 1, 11.511, 4.996, 11.689, 1, 11.867, 1, 0, 12.25, 18.58, 0, 12.5, -8.674, 0, 13, 0.131]}, {"Target": "Parameter", "Id": "Param5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.75, 0, 2, 4.5, 0, 2, 6.083, 0, 2, 6.967, 0, 0, 7.55, -24.6, 0, 11.333, -24.528, 0, 12.25, -24.84, 0, 12.5, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.75, 0, 2, 4.5, 0, 2, 6.083, 0, 2, 6.967, 0, 2, 7.383, 0, 0, 7.567, -30, 0, 9.917, -12.48, 0, 10.383, -18.571, 0, 11.333, -4.32, 0, 11.7, -30, 0, 12.083, 0, 0, 12.25, -8.033, 0, 12.483, 0, 2, 12.5, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.75, 0, 2, 4.5, 0, 2, 6.083, 0, 2, 6.967, 0, 0, 7.383, -30, 1, 7.444, -30, 7.506, 20.475, 7.567, 20.7, 1, 8.35, 23.588, 9.134, 26.786, 9.917, 28.76, 1, 10.389, 29.95, 10.861, 30, 11.333, 30, 1, 11.455, 30, 11.578, 30, 11.7, 19, 1, 11.828, 7.159, 11.955, -30, 12.083, -30, 0, 12.25, 0, 0, 12.483, -30, 1, 12.489, -30, 12.494, 0, 12.5, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamHandRREDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.75, 0, 2, 4.5, 0, 2, 6.083, 0, 2, 7.183, 0, 2, 7.2, 4, 2, 11.333, 4, 2, 12.25, 4, 2, 12.45, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Param55", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.75, 0, 2, 4.5, 0, 2, 4.917, 0, 2, 5.417, 0, 2, 6.083, 0, 2, 6.967, 0, 0, 7.55, 18, 2, 11.75, 18, 0, 12.5, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -9, 2, 0.583, -9, 2, 0.6, 0.04, 2, 2.333, 0.04, 2, 3.283, 0.04, 1, 3.289, 0.04, 3.294, -30, 3.3, -30, 2, 4.5, -30, 0, 4.917, 0, 2, 5.417, 0, 2, 6.083, 0, 2, 7.3, 0, 2, 7.317, 20, 2, 11.75, 20, 2, 12.5, 20, 2, 12.517, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.75, 0, 2, 4.5, 0, 2, 4.917, 0, 2, 5.417, 0, 2, 6.083, 0, 2, 6.967, 0, 0, 7.55, -30, 2, 11.75, -30, 0, 12.5, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.75, 0, 2, 4.5, 0, 2, 4.917, 0, 2, 5.417, 0, 2, 6.083, 0, 2, 6.967, 0, 0, 7.55, 6.6, 2, 11.75, 6.6, 0, 12.5, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.75, 0, 2, 4.5, 0, 2, 4.917, 0, 0, 5.417, 4.38, 2, 6.083, 4.38, 2, 6.967, 4.38, 0, 7.55, 4.68, 2, 11.75, 4.68, 0, 12.5, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.616, 0, 1.1, 7, 0, 2.05, 0.978, 0, 2.817, 5.307, 1, 2.989, 5.307, 3.161, 1.72, 3.333, 1, 1, 3.472, 0.419, 3.611, 0.284, 3.75, 0.094, 1, 4, -0.247, 4.25, -0.339, 4.5, -0.754, 1, 4.639, -0.985, 4.778, -3, 4.917, -3, 1, 5.222, -3, 5.528, 17.912, 5.833, 24, 1, 5.961, 26.546, 6.089, 25.543, 6.217, 25.543, 1, 6.467, 25.543, 6.717, 25.358, 6.967, 22.358, 1, 7.117, 20.558, 7.267, 5.686, 7.417, 3.72, 1, 7.678, 0.298, 7.939, 0, 8.2, 0, 0, 9.817, 1.41, 0, 11.383, 1.128, 0, 11.6, 1.45, 0, 11.917, 0.085, 0, 12.433, 2.921, 0, 13, -0.089]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 29, 0, 1.217, 4.74, 0, 2.167, 6.246, 0, 2.717, 3.804, 0, 4.067, 26, 1, 4.211, 26, 4.356, 25.745, 4.5, 21.243, 1, 4.661, 16.222, 4.822, 8.709, 4.983, 8.709, 1, 5.239, 8.709, 5.494, 15.03, 5.75, 21, 1, 5.878, 23.985, 6.005, 23.933, 6.133, 23.933, 1, 6.411, 23.933, 6.689, 23.275, 6.967, 18.173, 1, 7.117, 15.418, 7.267, -2.419, 7.417, -4.7, 1, 7.678, -8.671, 7.939, -9, 8.2, -9, 1, 8.739, -9, 9.278, -8.149, 9.817, -7.66, 1, 10.417, -7.116, 11.017, -7.08, 11.617, -7.08, 0, 11.983, -8.168, 0, 12.5, 5.449, 0, 13, 0.769]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.467, 0, 0, 1.333, 12.12, 0, 2.283, 0.044, 0, 2.9, 14.949, 1, 3.183, 14.949, 3.467, 3.913, 3.75, 0, 1, 4, -3.453, 4.25, -3.105, 4.5, -3.105, 0, 5.017, 9, 1, 5.322, 9, 5.628, 6.214, 5.933, -0.32, 1, 6.061, -3.052, 6.189, -5, 6.317, -5, 0, 6.967, -0.32, 0, 7.417, -20.11, 1, 7.678, -20.11, 7.939, -12.488, 8.2, -11.58, 1, 8.739, -9.707, 9.278, -9.58, 9.817, -9.58, 0, 11.683, -10.08, 0, 12.05, -3, 0, 12.567, -16.305, 0, 13, -3.105]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.75, 0, 2, 4.5, 0, 2, 4.917, 0, 2, 5.417, 0, 2, 6.083, 0, 2, 6.967, 0, 0, 7.55, -8.1, 2, 11.75, -8.1, 0, 12.5, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 1.05, 0, 2, 3.75, 0, 2, 4.5, 0, 2, 4.917, 0, 2, 5.417, 0, 2, 6.083, 0, 2, 6.967, 0, 0, 7.583, 30, 0, 8.2, 1, 2, 11.75, 1, 0, 12.5, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.5, 30, 0, 1.05, 0, 2, 3.75, 0, 2, 4.5, 0, 2, 4.917, 0, 2, 5.417, 0, 2, 6.083, 0, 2, 6.967, 0, 0, 7.583, -2, 0, 8.2, 7, 2, 11.75, 7, 0, 12.5, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamHandLREDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 7, 2, 3.217, 7, 2, 3.233, 1, 2, 7.317, 1, 2, 7.333, 5, 2, 12.283, 5, 2, 12.3, 1, 2, 13, 1]}, {"Target": "Parameter", "Id": "Param3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.75, 0, 0, 4.5, -0.499, 1, 4.6, -0.612, 4.7, -1.422, 4.8, -1.408, 1, 4.944, -1.41, 5.089, 0.301, 5.233, 0.298, 1, 5.255, 0.299, 5.278, 0.294, 5.3, 0.253, 1, 5.306, 0.25, 5.311, 0.348, 5.317, 0.345, 1, 5.372, 0.278, 5.428, 0.153, 5.483, 0.033, 1, 5.494, -0.02, 5.506, 0.471, 5.517, 0.41, 1, 5.822, 0.348, 6.128, -1.455, 6.433, -1.517, 1, 6.522, -1.551, 6.611, -0.993, 6.7, -1, 1, 6.722, -1.081, 6.745, -1.129, 6.767, -1.125, 1, 6.778, -1.117, 6.789, -1.108, 6.8, -1.1, 1, 6.806, -1.111, 6.811, -1.122, 6.817, -1.133, 1, 6.956, -1.074, 7.094, 2.36, 7.233, 2.36, 1, 7.244, 2.402, 7.256, 2.034, 7.267, 2.077, 1, 7.289, 2.217, 7.311, 2.188, 7.333, 2.205, 1, 7.639, 2.203, 7.944, -0.971, 8.25, -0.971, 1, 8.628, -0.968, 9.005, 1.289, 9.383, 1.297, 1, 9.522, 1.297, 9.661, 0.701, 9.8, 0.7, 1, 9.956, 0.69, 10.111, 3.378, 10.267, 3.382, 1, 10.484, 3.388, 10.7, -1.095, 10.917, -1.075, 1, 11.067, -1.076, 11.217, 0.477, 11.367, 0.493, 1, 11.489, 0.496, 11.611, -1.145, 11.733, -1.139, 1, 11.916, -1.143, 12.1, 2.37, 12.283, 2.362, 1, 12.489, 2.359, 12.694, -0.69, 12.9, -0.71, 1, 12.933, -0.639, 12.967, -0.499, 13, -0.499]}, {"Target": "Parameter", "Id": "Param2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.75, 0, 0, 4.5, 0.661, 1, 4.606, 1.232, 4.711, 3.608, 4.817, 3.593, 1, 4.95, 3.562, 5.084, -1.035, 5.217, -1.061, 1, 5.245, -1.062, 5.272, -0.957, 5.3, -0.909, 1, 5.306, -0.906, 5.311, -1.038, 5.317, -1.034, 1, 5.372, -1.007, 5.428, -0.371, 5.483, -0.076, 1, 5.494, 0.036, 5.506, -1.058, 5.517, -0.917, 1, 5.845, -0.771, 6.172, 5.434, 6.5, 5.425, 1, 6.744, 5.421, 6.989, -5.276, 7.233, -5.281, 1, 7.244, -5.394, 7.256, -4.541, 7.267, -4.625, 1, 7.289, -4.739, 7.311, -4.776, 7.333, -4.77, 1, 7.644, -4.76, 7.956, 1.636, 8.267, 1.64, 1, 8.639, 1.635, 9.011, -3.132, 9.383, -3.154, 1, 9.522, -3.154, 9.661, -2.084, 9.8, -2.085, 1, 9.956, -2.084, 10.111, -7.35, 10.267, -7.346, 1, 10.484, -7.378, 10.7, 2.528, 10.917, 2.52, 1, 11.072, 2.513, 11.228, -1.269, 11.383, -1.223, 1, 11.5, -1.225, 11.616, 1.961, 11.733, 2.017, 1, 11.739, 2.017, 11.744, 2.016, 11.75, 2.016, 1, 11.767, 1.975, 11.783, 2.227, 11.8, 2.154, 1, 11.956, 2.132, 12.111, -5.437, 12.267, -5.435, 1, 12.456, -5.455, 12.644, 1.264, 12.833, 1.267, 1, 12.889, 1.256, 12.944, 0.661, 13, 0.661]}, {"Target": "Parameter", "Id": "Param", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.75, 0, 0, 4.5, 7.32, 1, 4.611, 8.067, 4.722, 9.272, 4.833, 9.274, 1, 5.472, 9.274, 6.111, -10.704, 6.75, -10.692, 1, 7.372, -10.673, 7.995, 8.654, 8.617, 8.658, 1, 8.784, 8.651, 8.95, 7.491, 9.117, 7.474, 1, 9.372, 7.452, 9.628, 10.349, 9.883, 10.347, 1, 10.222, 10.345, 10.561, 1.786, 10.9, 1.784, 1, 10.939, 1.772, 10.978, 1.88, 11.017, 1.859, 1, 11.061, 1.865, 11.106, 1.815, 11.15, 1.82, 1, 11.378, 1.826, 11.605, 9.329, 11.833, 9.323, 1, 12.033, 9.312, 12.233, 3.467, 12.433, 3.482, 1, 12.622, 3.489, 12.811, 7.32, 13, 7.32]}, {"Target": "Parameter", "Id": "Param44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.75, 0, 2, 4.5, 0, 1, 5.278, 0.001, 6.055, 2.399, 6.833, 2.4, 1, 7.322, 2.401, 7.811, -0.001, 8.3, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Param6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.75, 0, 1, 4, 0, 4.25, 0.767, 4.5, 1.948, 1, 4.678, 2.788, 4.855, 3.066, 5.033, 3.066, 0, 5.65, 1.673, 0, 6.467, 3.167, 0, 7.783, 1.488, 0, 9.483, 3.341, 0, 10.017, 2.537, 0, 10.4, 3.016, 0, 10.85, 1.513, 0, 11.483, 2.529, 0, 11.85, 2.336, 0, 12.267, 3.366, 0, 13, 1.948]}, {"Target": "Parameter", "Id": "Param48", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.75, 0, 1, 4, 0, 4.25, -3.142, 4.5, -7.628, 1, 4.683, -10.917, 4.867, -11.883, 5.05, -11.883, 0, 5.65, -6.598, 0, 6.483, -12.411, 0, 7.783, -5.905, 0, 9.483, -12.873, 1, 9.789, -12.873, 10.094, -12.799, 10.4, -11.622, 1, 10.55, -11.044, 10.7, -6.035, 10.85, -6.035, 0, 11.483, -9.84, 0, 11.85, -9.112, 0, 12.267, -13.004, 0, 13, -7.628]}, {"Target": "Parameter", "Id": "Param37", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.75, 0, 1, 4, 0, 4.25, 5.393, 4.5, 9.009, 1, 4.717, 12.143, 4.933, 12.248, 5.15, 12.248, 0, 5.617, 7.972, 0, 6.533, 20.035, 0, 9.15, 7.201, 0, 9.567, 8.386, 0, 10.233, 4.843, 0, 10.933, 15.801, 0, 11.483, 11.467, 0, 11.733, 11.993, 0, 12.083, 10.286, 0, 12.567, 12.204, 0, 13, 9.009]}, {"Target": "Parameter", "Id": "Param38", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.75, 0, 2, 4.5, 0, 0, 6.833, -2.445, 0, 8.3, 0, 2, 9.733, 0, 0, 10.75, -2.97, 0, 11.55, 0, 0, 12.133, -1.779, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.65, 1, 0, 1.033, 0, 0, 1.35, 1, 2, 2.017, 1, 0, 2.3, 0, 0, 2.767, 0.9, 2, 3.75, 0.9, 2, 4.5, 0.9, 0, 4.833, 1, 0, 5.317, 0, 2, 5.933, 0, 0, 6.617, 0.9, 2, 7.533, 0.9, 0, 7.85, 0, 0, 8.167, 0.9, 2, 9.017, 0.9, 0, 9.217, 0, 0, 9.433, 0.9, 2, 10.3, 0.9, 0, 10.55, 1.056, 0, 10.8, 0.9, 2, 11.767, 0.9, 0, 11.967, 0, 0, 12.183, 0.9, 2, 13, 0.9]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.717, 0, 0, 1.083, 0.6, 0, 1.417, 0, 2, 2.017, 0, 0, 2.767, 0.6, 0, 3.75, 0, 2, 4.5, 0, 2, 4.833, 0, 0, 5.017, 1, 0, 5.317, 0.4, 2, 5.85, 0.4, 0, 6.017, 0, 0, 6.317, 1, 0, 6.617, 0, 2, 7.7, 0, 0, 7.95, 0.9, 0, 8.383, 0, 2, 9.117, 0, 0, 9.283, 1, 0, 9.567, 0, 2, 10.4, 0, 0, 10.65, 0.084, 0, 10.9, 0, 2, 11.867, 0, 0, 12.033, 1, 0, 12.317, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.65, 1, 0, 1.033, 0, 0, 1.35, 1, 2, 2.017, 1, 0, 2.3, 0, 0, 2.767, 0.9, 2, 3.75, 0.9, 2, 4.5, 0.9, 0, 4.833, 1, 0, 5.317, 0, 2, 5.933, 0, 0, 6.617, 0.9, 2, 7.533, 0.9, 0, 7.85, 0, 0, 8.167, 0.9, 2, 9.017, 0.9, 0, 9.217, 0, 0, 9.433, 0.9, 2, 10.3, 0.9, 0, 10.55, 1.047, 0, 10.8, 0.9, 2, 11.767, 0.9, 0, 11.967, 0, 0, 12.183, 0.9, 2, 13, 0.9]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.717, 0, 0, 1.083, 0.6, 0, 1.417, 0, 2, 2.017, 0, 0, 2.767, 0.5, 0, 3.75, 0, 2, 4.5, 0, 2, 4.833, 0, 0, 5.017, 1, 0, 5.317, 0.4, 2, 5.85, 0.4, 0, 6.017, 0, 0, 6.317, 1, 0, 6.617, 0, 2, 7.7, 0, 0, 7.95, 0.9, 0, 8.383, 0, 2, 9.117, 0, 0, 9.283, 1, 0, 9.567, 0, 2, 10.4, 0, 0, 10.65, 0.091, 0, 10.9, 0, 2, 11.867, 0, 0, 12.033, 1, 0, 12.317, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.65, 0, 0, 1.033, 0.022, 0, 1.433, 0, 2, 2, 0, 0, 2.767, -0.2, 0, 3.75, 0, 2, 4.5, 0, 0, 5.317, 0.07, 2, 5.933, 0.07, 0, 6.617, 0, 2, 7.533, 0, 0, 7.833, 0.07, 0, 8.25, 0, 2, 9.017, 0, 0, 9.217, 0.07, 0, 9.433, 0, 2, 11.767, 0, 0, 11.967, 0.07, 0, 12.183, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.65, 0, 0, 1.033, 0.238, 0, 1.433, 0, 2, 2, 0, 0, 2.767, 1, 0, 3.75, 0, 2, 4.5, 0, 0, 5.317, 0.364, 2, 5.933, 0.364, 0, 6.617, 0, 2, 7.533, 0, 0, 7.833, 0.364, 0, 8.25, 0, 2, 9.017, 0, 0, 9.217, 0.364, 0, 9.433, 0, 2, 11.767, 0, 0, 11.967, 0.364, 0, 12.183, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Param4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.65, 0, 0, 1.033, -0.459, 0, 1.433, 0, 2, 2, 0, 2, 3.75, 0, 2, 4.5, 0, 0, 5.317, -0.586, 2, 5.933, -0.586, 0, 6.617, 0, 2, 7.533, 0, 0, 7.833, -0.586, 0, 8.25, 0, 2, 9.017, 0, 0, 9.217, -0.586, 0, 9.433, 0, 2, 11.767, 0, 0, 11.967, -0.586, 0, 12.183, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.65, 0, 0, 1.033, 0.163, 0, 1.433, 0, 2, 2, 0, 0, 2.767, -0.1, 0, 3.75, 0, 2, 4.5, 0, 0, 5.317, 0.262, 2, 5.933, 0.262, 0, 6.617, 0, 2, 7.533, 0, 0, 7.833, 0.262, 0, 8.25, 0, 2, 9.017, 0, 0, 9.217, 0.262, 0, 9.433, 0, 2, 11.767, 0, 0, 11.967, 0.262, 0, 12.183, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.65, 0, 0, 1.033, 0.239, 0, 1.433, 0, 2, 2, 0, 0, 2.767, 0.956, 0, 3.75, 0, 2, 4.5, 0, 0, 5.317, 0.366, 2, 5.933, 0.366, 0, 6.617, 0, 2, 7.533, 0, 0, 7.833, 0.366, 0, 8.25, 0, 2, 9.017, 0, 0, 9.217, 0.366, 0, 9.433, 0, 2, 11.767, 0, 0, 11.967, 0.366, 0, 12.183, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Param11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.65, 0, 0, 1.033, -0.449, 0, 1.433, 0, 2, 2, 0, 0, 2.767, 0.262, 0, 3.75, 0, 2, 4.5, 0, 0, 5.317, -0.572, 2, 5.933, -0.572, 0, 6.617, 0, 2, 7.533, 0, 0, 7.833, -0.572, 0, 8.25, 0, 2, 9.017, 0, 0, 9.217, -0.572, 0, 9.433, 0, 2, 11.767, 0, 0, 11.967, -0.572, 0, 12.183, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Param15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 3.75, 1, 2, 4.5, 1, 2, 13, 1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.4, 1, 0.028, 0.4, 0.055, 0.118, 0.083, 0, 1, 0.133, -0.213, 0.183, -0.244, 0.233, -0.244, 0, 0.417, -0.01, 0, 0.533, -0.319, 2, 0.717, -0.319, 2, 0.9, -0.319, 1, 0.928, -0.319, 0.955, 0.541, 0.983, 0.656, 1, 1, 0.725, 1.016, 0.7, 1.033, 0.7, 1, 1.255, 0.7, 1.478, 0.62, 1.7, 0.4, 1, 1.728, 0.373, 1.755, 0.102, 1.783, 0, 1, 1.833, -0.184, 1.883, -0.244, 1.933, -0.244, 0, 2.117, -0.01, 0, 2.233, -0.319, 2, 2.417, -0.319, 2, 2.6, -0.319, 0, 2.683, 0.656, 0, 2.733, -0.319, 2, 2.917, -0.319, 0, 3.05, 0.6, 1, 3.083, 0.6, 3.117, 0.544, 3.15, 0.4, 1, 3.178, 0.28, 3.205, 0.102, 3.233, 0, 1, 3.283, -0.184, 3.333, -0.244, 3.383, -0.244, 0, 3.567, -0.01, 0, 3.683, -0.319, 2, 3.867, -0.319, 2, 4.05, -0.319, 0, 4.133, 0.656, 0, 4.183, -0.319, 0, 4.233, 0.5, 1, 4.55, 0.5, 4.866, 0.367, 5.183, 0, 1, 5.233, -0.058, 5.283, -0.244, 5.333, -0.244, 0, 5.517, -0.01, 0, 5.633, -0.319, 2, 5.817, -0.319, 2, 6, -0.319, 0, 6.083, 0.656, 0, 6.133, -0.319, 2, 6.317, -0.319, 2, 6.517, -0.319, 2, 6.55, -0.319, 2, 6.717, -0.319, 2, 6.867, -0.319, 0, 6.933, 0.34, 1, 7, 0.34, 7.066, 0.257, 7.133, -0.01, 1, 7.172, -0.166, 7.211, -0.319, 7.25, -0.319, 0, 7.6, 1, 1, 7.917, 1, 8.233, 0.7, 8.55, 0, 1, 8.6, -0.111, 8.65, -0.244, 8.7, -0.244, 0, 8.883, -0.01, 0, 9, -0.319, 2, 9.183, -0.319, 2, 9.367, -0.319, 0, 9.45, 0.656, 0, 9.5, -0.319, 0, 10.233, 0.8, 0, 10.35, -0.319, 2, 10.55, -0.319, 2, 10.583, -0.319, 2, 10.75, -0.319, 2, 10.9, -0.319, 0, 10.967, 0.34, 1, 11.034, 0.34, 11.1, 0.257, 11.167, -0.01, 1, 11.206, -0.166, 11.244, -0.319, 11.283, -0.319, 0, 11.633, 1, 0, 11.7, -0.319, 2, 11.9, -0.319, 2, 11.933, -0.319, 2, 12.1, -0.319, 2, 12.25, -0.319, 0, 12.317, 0.34, 0, 12.517, -0.01, 0, 12.667, 0.7, 2, 13, 0.7]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.083, 0, 1, 0.133, 0, 0.183, 0.185, 0.233, 0.5, 1, 0.289, 0.85, 0.344, 1, 0.4, 1, 0, 0.583, 0.102, 0, 0.767, 0.969, 1, 0.839, 0.969, 0.911, 0.162, 0.983, 0.012, 1, 1, 0, 1.016, 0, 1.033, 0, 2, 1.7, 0, 2, 1.783, 0, 1, 1.833, 0, 1.883, 0.185, 1.933, 0.5, 1, 1.989, 0.85, 2.044, 1, 2.1, 1, 0, 2.283, 0.102, 0, 2.467, 0.969, 0, 2.683, 0.012, 0, 2.883, 0.663, 1, 2.911, 0.663, 2.939, 0.055, 2.967, 0.024, 1, 2.995, 0, 3.022, 0, 3.05, 0, 2, 3.15, 0, 2, 3.233, 0, 1, 3.283, 0, 3.333, 0.185, 3.383, 0.5, 1, 3.439, 0.85, 3.494, 1, 3.55, 1, 0, 3.733, 0.102, 0, 3.917, 0.969, 0, 4.133, 0.012, 0, 4.233, 0.1, 0, 5.183, 0, 1, 5.233, 0, 5.283, 0.185, 5.333, 0.5, 1, 5.389, 0.85, 5.444, 1, 5.5, 1, 0, 5.683, 0.102, 0, 5.867, 0.969, 0, 6.083, 0.012, 0, 6.283, 0.663, 0, 6.367, 0.024, 0, 6.517, 0.871, 2, 6.55, 0.871, 0, 6.833, 0, 0, 7.117, 1, 1, 7.172, 1, 7.228, 0.21, 7.283, 0.102, 1, 7.344, 0, 7.406, 0, 7.467, 0, 2, 7.6, 0, 2, 8.55, 0, 1, 8.6, 0, 8.65, 0.185, 8.7, 0.5, 1, 8.756, 0.85, 8.811, 1, 8.867, 1, 0, 9.05, 0.102, 0, 9.233, 0.969, 1, 9.305, 0.969, 9.378, 0.015, 9.45, 0.012, 1, 9.711, 0.002, 9.972, 0, 10.233, 0, 0, 10.317, 0.663, 0, 10.4, 0.024, 0, 10.55, 0.871, 2, 10.583, 0.871, 0, 10.867, 0, 0, 11.15, 1, 1, 11.206, 1, 11.261, 0.21, 11.317, 0.102, 1, 11.378, 0, 11.439, 0, 11.5, 0, 2, 11.633, 0, 0, 11.667, 0.663, 0, 11.75, 0.024, 0, 11.9, 0.871, 2, 11.933, 0.871, 0, 12.217, 0, 0, 12.5, 1, 0, 12.667, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Hair_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -16, 0, 0.433, 9.925, 0, 1, -9.363, 0, 1.383, 3.565, 0, 1.883, -2.577, 0, 2.283, 2.011, 0, 2.7, -0.943, 0, 3.35, 8.402, 0, 4, -16, 0, 4.733, 3.354, 0, 5.217, -5, 0, 5.75, 2.861, 0, 6.183, -2.017, 0, 6.633, 6.153, 0, 7.067, -3.961, 0, 7.483, 2.6, 0, 7.833, -3.965, 0, 8.483, 1.635, 0, 8.833, -1.438, 0, 9.683, 2.718, 0, 10.017, -5.793, 0, 10.45, 3.691, 0, 10.933, -3.202, 0, 11.35, 2.604, 0, 11.9, -2.699, 0, 12.333, 1.26, 0, 12.817, -1.381, 0, 13, 0]}, {"Target": "Parameter", "Id": "Hair_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 0.433, 14.624, 0, 1, -4.663, 0, 1.383, 4.97, 0, 1.883, -1.708, 0, 2.283, 1.554, 0, 2.7, -1.803, 0, 3.35, 6.756, 0, 4, -10, 0, 4.733, 3.354, 0, 5.217, -5, 0, 5.75, 2.861, 0, 6.183, -2.017, 0, 6.633, 6.153, 0, 7.067, -3.961, 0, 7.483, 2.6, 0, 7.833, -3.965, 0, 8.483, 1.635, 0, 8.833, -1.438, 0, 9.683, 2.718, 0, 10.017, -5.793, 0, 10.45, 3.691, 0, 10.933, -3.202, 0, 11.35, 2.604, 0, 11.9, -2.699, 0, 12.333, 1.26, 0, 12.817, -1.381, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param69", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 4.733, 3.354, 0, 5.217, -8.454, 0, 5.75, 6.288, 0, 6.183, -5.444, 0, 6.633, 7.707, 0, 7.067, -4.903, 0, 7.483, 2.6, 0, 7.833, -3.965, 0, 8.483, 1.635, 0, 8.833, -1.438, 0, 9.683, 2.718, 0, 10.017, -5.793, 0, 10.45, 3.691, 0, 10.933, -3.202, 0, 11.35, 2.604, 0, 11.9, -2.699, 0, 12.333, 4.171, 0, 12.817, -1.381, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param153", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.1, 0, 0.3, -15.606, 0, 0.717, 14.944, 0, 1.233, -13.454, 0, 1.65, 8.305, 0, 2.133, -5.623, 0, 2.55, 3.903, 0, 3.033, -4.846, 0, 3.7, 10.4, 0, 4.267, -8.877, 0, 4.983, 6.155, 0, 5.483, -5.859, 0, 6, 4.898, 0, 6.467, -6.386, 0, 6.917, 8.531, 0, 7.333, -7.433, 0, 7.75, 7.275, 0, 8.15, -4.697, 0, 8.7, 3.474, 0, 9.15, -2.193, 0, 9.933, 6.577, 0, 10.3, -8.437, 0, 10.733, 6.844, 0, 11.2, -6.009, 0, 11.633, 4.619, 0, 12.133, -3.896, 0, 12.583, 2.841, 0, 12.983, -2.468, 1, 12.989, -2.468, 12.994, -2.457, 13, -2.437]}, {"Target": "Parameter", "Id": "Param154", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.526, 0, 0.233, 6.58, 0, 0.567, -12.685, 0, 1.017, 11.135, 0, 1.483, -11.875, 0, 1.917, 8.081, 0, 2.367, -5.938, 0, 2.817, 4.557, 0, 3.3, -3.33, 0, 3.4, -3.141, 0, 3.467, -3.203, 0, 4.017, 5.755, 0, 4.5, -4.187, 0, 5.283, 3.935, 0, 5.783, -4.133, 0, 6.3, 4.374, 0, 6.767, -6.312, 0, 7.183, 7.784, 0, 7.617, -7.529, 0, 7.983, 6.991, 0, 8.417, -4.155, 0, 8.933, 2.977, 0, 9.367, -1.715, 0, 9.7, 0.089, 0, 9.883, -2.268, 0, 10.2, 6.209, 0, 10.583, -7.433, 0, 11.017, 6.407, 0, 11.45, -5.805, 0, 11.9, 4.474, 0, 12.4, -3.538, 0, 12.9, 2.824, 1, 12.933, 2.824, 12.967, 2.329, 13, 1.651]}, {"Target": "Parameter", "Id": "Param155", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.411, 0, 0.05, -1.619, 0, 0.383, 4.675, 0, 0.75, -10.464, 0, 1.2, 12.196, 0, 1.65, -12.288, 0, 2.1, 9.773, 0, 2.55, -7.249, 0, 3, 5.589, 0, 3.483, -4.406, 0, 4.15, 5.431, 0, 4.667, -4.86, 0, 5.4, 3.424, 0, 5.917, -4.297, 0, 6.45, 4.75, 0, 6.917, -6.15, 0, 7.35, 7.636, 0, 7.783, -7.949, 0, 8.183, 7.273, 0, 8.617, -5.323, 0, 9.083, 3.835, 0, 9.55, -2.357, 0, 9.867, -0.144, 0, 10.033, -0.666, 0, 10.367, 4.586, 0, 10.767, -6.791, 0, 11.2, 6.928, 0, 11.633, -6.098, 0, 12.083, 5.27, 0, 12.55, -4.229, 1, 12.7, -4.229, 12.85, 2.858, 13, 3.364]}, {"Target": "Parameter", "Id": "Param156", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.168, 0, 0.233, -2.052, 0, 0.55, 3.359, 0, 0.933, -8.891, 0, 1.367, 12.43, 0, 1.817, -13.103, 0, 2.267, 11.321, 0, 2.717, -8.8, 0, 3.167, 6.867, 0, 3.65, -5.641, 0, 4.267, 5.414, 0, 4.817, -5.332, 0, 5.467, 3.362, 0, 6.067, -4.28, 0, 6.583, 4.757, 0, 7.067, -6.396, 0, 7.517, 7.781, 0, 7.95, -8.305, 0, 8.367, 7.839, 0, 8.8, -6.413, 0, 9.25, 4.837, 0, 9.717, -3.207, 0, 10.517, 3.429, 0, 10.933, -6.228, 0, 11.367, 7.219, 0, 11.8, -6.904, 0, 12.267, 6.091, 0, 12.717, -4.906, 1, 12.811, -4.906, 12.906, -1.617, 13, 0.968]}, {"Target": "Parameter", "Id": "Param82", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.093, 0, 0.3, -14.549, 0, 0.717, 13.932, 0, 1.233, -12.542, 0, 1.65, 7.742, 0, 2.133, -5.242, 0, 2.55, 3.639, 0, 3.033, -4.517, 0, 3.7, 9.695, 0, 4.267, -8.275, 0, 4.983, 5.738, 0, 5.483, -5.462, 0, 6, 4.566, 0, 6.467, -5.954, 0, 6.917, 7.952, 0, 7.333, -6.93, 0, 7.75, 6.782, 0, 8.15, -4.378, 0, 8.7, 3.239, 0, 9.15, -2.045, 0, 9.933, 6.131, 0, 10.3, -7.865, 0, 10.733, 6.38, 0, 11.2, -5.602, 0, 11.633, 4.306, 0, 12.133, -3.632, 0, 12.583, 2.648, 0, 12.983, -2.301, 1, 12.989, -2.301, 12.994, -2.291, 13, -2.272]}, {"Target": "Parameter", "Id": "Param83", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.504, 0, 0.233, 6.304, 0, 0.567, -12.153, 0, 1.017, 10.668, 0, 1.483, -11.377, 0, 1.917, 7.742, 0, 2.367, -5.689, 0, 2.817, 4.365, 0, 3.3, -3.19, 0, 3.4, -3.009, 0, 3.467, -3.068, 0, 4.017, 5.514, 0, 4.5, -4.011, 0, 5.283, 3.77, 0, 5.783, -3.959, 0, 6.3, 4.191, 0, 6.767, -6.047, 0, 7.183, 7.458, 0, 7.617, -7.213, 0, 7.983, 6.698, 0, 8.417, -3.981, 0, 8.933, 2.852, 0, 9.367, -1.643, 0, 9.7, 0.085, 0, 9.883, -2.173, 0, 10.2, 5.949, 0, 10.583, -7.121, 0, 11.017, 6.138, 0, 11.45, -5.561, 0, 11.9, 4.287, 0, 12.4, -3.39, 0, 12.9, 2.706, 1, 12.933, 2.706, 12.967, 2.231, 13, 1.581]}, {"Target": "Parameter", "Id": "Param84", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.442, 0, 0.05, -1.655, 0, 0.383, 4.779, 0, 0.75, -10.696, 0, 1.2, 12.467, 0, 1.65, -12.561, 0, 2.1, 9.99, 0, 2.55, -7.41, 0, 3, 5.714, 0, 3.483, -4.504, 0, 4.15, 5.551, 0, 4.667, -4.968, 0, 5.4, 3.5, 0, 5.917, -4.393, 0, 6.45, 4.855, 0, 6.917, -6.287, 0, 7.35, 7.806, 0, 7.783, -8.126, 0, 8.183, 7.434, 0, 8.617, -5.441, 0, 9.083, 3.92, 0, 9.55, -2.409, 0, 9.867, -0.147, 0, 10.033, -0.681, 0, 10.367, 4.688, 0, 10.767, -6.942, 0, 11.2, 7.082, 0, 11.633, -6.234, 0, 12.083, 5.388, 0, 12.55, -4.323, 1, 12.7, -4.323, 12.85, 2.921, 13, 3.439]}, {"Target": "Parameter", "Id": "Param85", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.211, 0, 0.233, -2.092, 0, 0.55, 3.425, 0, 0.933, -9.065, 0, 1.367, 12.674, 0, 1.817, -13.359, 0, 2.267, 11.543, 0, 2.717, -8.972, 0, 3.167, 7.002, 0, 3.65, -5.751, 0, 4.267, 5.52, 0, 4.817, -5.436, 0, 5.467, 3.428, 0, 6.067, -4.364, 0, 6.583, 4.85, 0, 7.067, -6.521, 0, 7.517, 7.933, 0, 7.95, -8.467, 0, 8.367, 7.992, 0, 8.8, -6.539, 0, 9.25, 4.932, 0, 9.717, -3.269, 0, 10.517, 3.496, 0, 10.933, -6.35, 0, 11.367, 7.36, 0, 11.8, -7.039, 0, 12.267, 6.21, 0, 12.717, -5.002, 1, 12.811, -5.002, 12.906, -1.648, 13, 0.987]}, {"Target": "Parameter", "Id": "Param66", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.2, 0, 0, 2.417, 6.418, 0, 2.633, -7.653, 0, 2.933, 4.335, 0, 3.233, -2.04, 0, 3.55, 0.82, 0, 3.883, -0.309, 0, 4.2, 0.092, 0, 4.533, -0.044, 0, 4.733, -0.014, 0, 5.033, -6.031, 0, 5.4, 14.584, 0, 5.783, -16.094, 0, 6.3, 17.302, 0, 6.733, -21.275, 0, 7.167, 22.855, 0, 7.567, -20.762, 0, 7.983, 19.033, 0, 8.333, -13.27, 0, 8.65, 3.812, 0, 8.883, 0.954, 0, 9.05, 3.29, 0, 9.367, -3.659, 0, 9.7, 1.576, 0, 9.967, -7.172, 0, 10.25, 18.076, 0, 10.583, -22.018, 0, 10.95, 16.88, 0, 11.417, -10.253, 0, 11.85, 7.863, 0, 12.483, -8.322, 0, 12.867, 9.394, 1, 12.911, 9.394, 12.956, 7.144, 13, 4.208]}, {"Target": "Parameter", "Id": "Param67", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.667, -6.747, 0, 1.933, 3.033, 0, 2.2, -1.7, 0, 2.467, 0.593, 0, 2.733, -0.507, 0, 3, 0.041, 0, 3.25, -0.205, 0, 3.533, -0.058, 0, 3.767, -0.098, 0, 4.95, 16.171, 0, 5.333, -20.575, 0, 5.9, 18.962, 0, 6.3, -24.54, 0, 6.75, 23.732, 0, 7.15, -21.229, 0, 7.583, 16.335, 0, 7.917, -16.055, 0, 8.2, 3.692, 0, 8.4, -1.305, 0, 8.65, 7.752, 0, 8.933, -8.134, 0, 9.2, 1.746, 0, 9.45, -1.923, 0, 9.867, 18.674, 0, 10.15, -28.083, 0, 10.5, 18.817, 0, 11.083, -9.204, 0, 11.45, 10.844, 0, 12.1, -10.438, 0, 12.433, 12.999, 0, 12.917, -7.985, 1, 12.945, -7.985, 12.972, -5.833, 13, -3.185]}, {"Target": "Parameter", "Id": "Param68", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.667, 3.863, 0, 1.867, -5.344, 0, 2.117, 3.436, 0, 2.367, -2.168, 0, 2.633, 1.135, 0, 2.883, -0.697, 0, 3.15, 0.288, 0, 3.417, -0.219, 0, 3.683, 0.047, 0, 3.95, -0.078, 0, 4.217, -0.004, 0, 4.45, -0.028, 0, 4.733, -0.001, 0, 4.883, -4.37, 0, 5.133, 11.49, 0, 5.483, -10.716, 0, 5.767, 0.41, 0, 5.833, -0.071, 0, 6.117, 9.904, 0, 6.483, -13.344, 0, 6.917, 11.336, 0, 7.3, -11.349, 0, 7.783, 8.535, 0, 8.067, -10.603, 0, 8.333, 5.819, 0, 8.583, -5.175, 0, 8.85, 7.391, 0, 9.1, -6.528, 0, 9.367, 3.206, 0, 9.617, -2.688, 0, 9.7, -1.987, 0, 9.8, -4.307, 0, 10.05, 16.735, 0, 10.317, -20.062, 0, 10.633, 13.348, 0, 10.9, -4.484, 0, 11.1, 1.107, 0, 11.333, -5.565, 0, 11.6, 6.563, 0, 11.867, -2.164, 0, 12.05, 2.204, 0, 12.3, -7.73, 0, 12.6, 8.309, 0, 12.85, -2.25, 0, 12.917, -2.038, 1, 12.945, -2.038, 12.972, -3.251, 13, -4.162]}, {"Target": "Parameter", "Id": "Param62", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.2, 0, 0, 2.417, -6.418, 0, 2.633, 7.653, 0, 2.933, -4.335, 0, 3.233, 2.04, 0, 3.55, -0.82, 0, 3.883, 0.309, 0, 4.2, -0.092, 0, 4.533, 0.044, 0, 4.733, 0.014, 0, 5.033, 6.031, 0, 5.4, -14.584, 0, 5.783, 16.094, 0, 6.3, -17.302, 0, 6.733, 21.275, 0, 7.167, -22.855, 0, 7.567, 20.762, 0, 7.983, -19.033, 0, 8.333, 13.27, 0, 8.65, -3.812, 0, 8.883, -0.954, 0, 9.05, -3.29, 0, 9.367, 3.659, 0, 9.7, -1.576, 0, 9.967, 7.172, 0, 10.25, -18.076, 0, 10.583, 22.018, 0, 10.95, -16.88, 0, 11.417, 10.253, 0, 11.85, -7.863, 0, 12.483, 8.322, 0, 12.867, -9.394, 1, 12.911, -9.394, 12.956, -7.144, 13, -4.208]}, {"Target": "Parameter", "Id": "Param63", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.667, 6.747, 0, 1.933, -3.033, 0, 2.2, 1.7, 0, 2.467, -0.593, 0, 2.733, 0.507, 0, 3, -0.041, 0, 3.25, 0.205, 0, 3.533, 0.058, 0, 3.767, 0.098, 0, 4.95, -16.171, 0, 5.333, 20.575, 0, 5.9, -18.962, 0, 6.3, 24.54, 0, 6.75, -23.732, 0, 7.15, 21.229, 0, 7.583, -16.335, 0, 7.917, 16.055, 0, 8.2, -3.692, 0, 8.4, 1.305, 0, 8.65, -7.752, 0, 8.933, 8.134, 0, 9.2, -1.746, 0, 9.45, 1.923, 0, 9.867, -18.674, 0, 10.15, 28.083, 0, 10.5, -18.817, 0, 11.083, 9.204, 0, 11.45, -10.844, 0, 12.1, 10.438, 0, 12.433, -12.999, 0, 12.917, 7.985, 1, 12.945, 7.985, 12.972, 5.833, 13, 3.185]}, {"Target": "Parameter", "Id": "Param64", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.667, -3.863, 0, 1.867, 5.344, 0, 2.117, -3.436, 0, 2.367, 2.168, 0, 2.633, -1.135, 0, 2.883, 0.697, 0, 3.15, -0.288, 0, 3.417, 0.219, 0, 3.683, -0.047, 0, 3.95, 0.078, 0, 4.217, 0.004, 0, 4.45, 0.028, 0, 4.733, 0.001, 0, 4.883, 4.37, 0, 5.133, -11.49, 0, 5.483, 10.716, 0, 5.767, -0.41, 0, 5.833, 0.071, 0, 6.117, -9.904, 0, 6.483, 13.344, 0, 6.917, -11.336, 0, 7.3, 11.349, 0, 7.783, -8.535, 0, 8.067, 10.603, 0, 8.333, -5.819, 0, 8.583, 5.175, 0, 8.85, -7.391, 0, 9.1, 6.528, 0, 9.367, -3.206, 0, 9.617, 2.688, 0, 9.7, 1.987, 0, 9.8, 4.307, 0, 10.05, -16.735, 0, 10.317, 20.062, 0, 10.633, -13.348, 0, 10.9, 4.484, 0, 11.1, -1.107, 0, 11.333, 5.565, 0, 11.6, -6.563, 0, 11.867, 2.164, 0, 12.05, -2.204, 0, 12.3, 7.73, 0, 12.6, -8.309, 0, 12.85, 2.25, 0, 12.917, 2.038, 1, 12.945, 2.038, 12.972, 3.251, 13, 4.162]}, {"Target": "Parameter", "Id": "Param86", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 4.375, 0, 0.383, -5.736, 0, 0.65, 8.201, 0, 1, -4.582, 0, 1.417, 3.804, 0, 1.833, -2.287, 0, 2.283, 1.822, 0, 2.683, -1.356, 0, 3, 0.52, 0, 3.217, 0.201, 0, 3.517, 0.685, 0, 3.883, -1.058, 0, 4.433, 0.524, 0, 4.533, 0.436, 0, 4.617, 1.089, 0, 5.3, -0.9, 0, 5.617, 0.768, 0, 5.767, 0.637, 0, 5.783, 0.638, 0, 6.25, -1.917, 0, 6.733, 1.547, 0, 7.133, -2.008, 0, 7.567, 1.827, 0, 7.967, -1.895, 0, 8.383, 1.746, 0, 8.867, -1.113, 0, 9.2, 0.56, 0, 9.217, 0.549, 0, 9.3, 1.022, 0, 9.583, -0.491, 0, 9.867, 1.215, 0, 10.133, -2.404, 0, 10.5, 2.952, 0, 10.883, -1.913, 0, 11.367, 1.964, 0, 11.783, -1.503, 0, 12.35, 1.373, 0, 12.733, -1.071, 1, 12.822, -1.07, 12.911, -0.092, 13, 0.66]}, {"Target": "Parameter", "Id": "Param89", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.027, 0, 0.25, -30, 2, 0.3, -30, 0, 0.65, 26.89, 0, 1.2, -18.167, 0, 1.6, 12.659, 0, 2.067, -7.119, 0, 2.5, 6.256, 0, 2.983, -7.694, 0, 3.667, 13.431, 0, 4.233, -9.992, 0, 4.983, 10.194, 0, 5.45, -10.031, 0, 5.967, 8.108, 0, 6.433, -11.71, 0, 6.867, 15.591, 0, 7.283, -13.289, 0, 7.7, 14.112, 0, 8.05, -8.743, 0, 8.7, 5.42, 0, 9.05, -3.795, 0, 9.917, 14.142, 0, 10.267, -16.959, 0, 10.65, 12.423, 0, 11.15, -10.061, 0, 11.567, 7.663, 0, 12.117, -5.96, 0, 12.533, 4.629, 0, 12.967, -4.937, 1, 12.978, -4.937, 12.989, -4.859, 13, -4.72]}, {"Target": "Parameter", "Id": "Param87", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.367, 0, 0, 0.6, -4.487, 0, 0.867, 7.077, 0, 1.2, -5.006, 0, 1.567, 4.189, 0, 1.967, -2.653, 0, 2.417, 2.246, 0, 2.817, -2.136, 0, 3.217, 1.089, 0, 3.25, 1.03, 0, 3.267, 1.038, 0, 3.567, 0.274, 0, 3.65, 0.293, 0, 3.717, 0.258, 0, 3.733, 0.264, 0, 4.05, -1.213, 0, 4.533, 0.639, 0, 4.6, 0.345, 0, 4.667, 0.596, 0, 5.367, -1.047, 0, 5.817, 1.035, 0, 6.083, -0.088, 0, 6.1, -0.074, 0, 6.2, -0.376, 0, 6.217, -0.36, 0, 6.3, -0.607, 0, 6.333, -0.59, 0, 6.417, -0.654, 0, 6.817, 1.56, 0, 7.267, -1.931, 0, 7.667, 1.985, 0, 8.1, -1.35, 0, 8.417, 0.765, 0, 8.45, 0.738, 0, 8.533, 0.8, 0, 9, -1.201, 0, 9.433, 0.942, 0, 9.75, -0.512, 0, 10, 0.888, 0, 10.3, -1.834, 0, 10.667, 2.664, 0, 11.05, -2.135, 0, 11.5, 1.886, 0, 11.917, -1.206, 0, 12.233, 0.479, 0, 12.283, 0.422, 0, 12.517, 0.88, 0, 12.883, -1.165, 1, 12.922, -1.165, 12.961, -1.008, 13, -0.779]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.329, 0, 0.2, 14.55, 0, 0.5, -28.401, 0, 0.85, 19.287, 0, 1.417, -14.229, 0, 1.783, 10.39, 0, 2.25, -5.992, 0, 2.767, 5.786, 0, 3.167, -3.929, 0, 3.367, -2.421, 0, 3.517, -3.536, 0, 3.95, 6.471, 0, 4.383, -3.787, 0, 4.667, -1.859, 0, 4.867, -3.162, 0, 5.267, 7.413, 0, 5.633, -5.794, 0, 6.25, 6.566, 0, 6.683, -9.613, 0, 7.117, 12.822, 0, 7.517, -12.28, 0, 7.9, 14.404, 0, 8.233, -8.013, 0, 8.533, 0.614, 0, 8.65, 0.019, 0, 8.917, 4.44, 0, 9.267, -2.784, 0, 9.617, 0.33, 0, 9.85, -7.115, 0, 10.15, 14.897, 0, 10.483, -16.029, 0, 10.867, 10.643, 0, 11.383, -8.485, 0, 11.767, 6.078, 0, 12.383, -4.666, 0, 12.75, 3.597, 0, 12.85, 2.68, 0, 12.883, 2.681, 1, 12.922, 2.681, 12.961, 1.117, 13, -0.551]}, {"Target": "Parameter", "Id": "Param88", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.159, 0, 0.05, -1.036, 0, 0.3, -10.166, 0, 0.617, 24.488, 0, 0.95, -21.863, 0, 1.483, 11.459, 0, 1.883, -10.741, 0, 2.3, 6.37, 0, 2.7, -4.097, 0, 3.267, 3.938, 0, 3.517, 1.578, 0, 3.633, 1.836, 0, 4.05, -5.447, 0, 4.467, 4.114, 0, 4.767, 0.577, 0, 4.983, 2.166, 0, 5.35, -6.756, 0, 5.733, 6.538, 0, 6.317, -5.459, 0, 6.75, 9.745, 0, 7.183, -11.767, 0, 7.583, 12.233, 0, 8, -13.384, 0, 8.333, 9.68, 0, 8.667, -2.814, 0, 8.883, -0.744, 0, 8.9, -0.807, 0, 8.933, -0.714, 0, 9.067, -1.704, 0, 9.333, 2.292, 0, 9.7, -0.435, 0, 9.967, 4.667, 0, 10.25, -12.831, 0, 10.583, 15.893, 0, 10.95, -12.379, 0, 11.367, 6.978, 0, 11.85, -5.711, 0, 12.333, 2.97, 0, 12.45, 2, 0, 12.483, 2.022, 0, 12.867, -4.405, 1, 12.911, -4.405, 12.956, -3.345, 13, -1.932]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.614, 0, 0.083, 2.291, 0, 0.167, 2.106, 0, 0.4, 6.96, 0, 0.717, -20.83, 0, 1.067, 22.728, 0, 1.467, -12.183, 0, 1.967, 10.026, 0, 2.367, -7.013, 0, 2.817, 4.731, 0, 3.35, -3.588, 0, 3.667, -0.591, 0, 3.75, -0.685, 0, 4.133, 4.616, 0, 4.55, -4.161, 0, 4.85, 0.742, 0, 5.1, -1.746, 0, 5.45, 5.777, 0, 5.833, -6.611, 0, 6.35, 4.571, 0, 6.85, -8.651, 0, 7.267, 10.966, 0, 7.683, -11.949, 0, 8.083, 12.77, 0, 8.433, -10.691, 0, 8.767, 4.868, 0, 9.117, 0.565, 0, 9.167, 0.586, 0, 9.433, -2.015, 0, 9.467, -1.963, 0, 9.483, -1.973, 0, 9.783, 0.789, 0, 10.067, -3.856, 0, 10.367, 10.965, 0, 10.7, -15.424, 0, 11.05, 13.699, 0, 11.45, -8.401, 0, 11.933, 5.397, 0, 12.4, -3.295, 0, 12.983, 3.378, 1, 12.989, 3.378, 12.994, 3.364, 13, 3.337]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -4.375, 0, 0.383, 5.736, 0, 0.65, -8.201, 0, 1, 4.582, 0, 1.417, -3.804, 0, 1.833, 2.287, 0, 2.283, -1.822, 0, 2.683, 1.356, 0, 3, -0.52, 0, 3.217, -0.201, 0, 3.517, -0.685, 0, 3.883, 1.058, 0, 4.433, -0.524, 0, 4.533, -0.436, 0, 4.617, -1.089, 0, 5.3, 0.9, 0, 5.617, -0.768, 0, 5.767, -0.637, 0, 5.783, -0.638, 0, 6.25, 1.917, 0, 6.733, -1.547, 0, 7.133, 2.008, 0, 7.567, -1.827, 0, 7.967, 1.895, 0, 8.383, -1.746, 0, 8.867, 1.113, 0, 9.2, -0.56, 0, 9.217, -0.549, 0, 9.3, -1.022, 0, 9.583, 0.491, 0, 9.867, -1.215, 0, 10.133, 2.404, 0, 10.5, -2.952, 0, 10.883, 1.913, 0, 11.367, -1.964, 0, 11.783, 1.503, 0, 12.35, -1.373, 0, 12.733, 1.071, 1, 12.822, 1.07, 12.911, 0.092, 13, -0.66]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.027, 0, 0.25, -30, 2, 0.3, -30, 0, 0.65, 26.89, 0, 1.2, -18.167, 0, 1.6, 12.659, 0, 2.067, -7.119, 0, 2.5, 6.256, 0, 2.983, -7.694, 0, 3.667, 13.431, 0, 4.233, -9.992, 0, 4.983, 10.194, 0, 5.45, -10.031, 0, 5.967, 8.108, 0, 6.433, -11.71, 0, 6.867, 15.591, 0, 7.283, -13.289, 0, 7.7, 14.112, 0, 8.05, -8.743, 0, 8.7, 5.42, 0, 9.05, -3.795, 0, 9.917, 14.142, 0, 10.267, -16.959, 0, 10.65, 12.423, 0, 11.15, -10.061, 0, 11.567, 7.663, 0, 12.117, -5.96, 0, 12.533, 4.629, 0, 12.967, -4.937, 1, 12.978, -4.937, 12.989, -4.859, 13, -4.72]}, {"Target": "Parameter", "Id": "Param94", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.367, 0, 0, 0.6, 4.487, 0, 0.867, -7.077, 0, 1.2, 5.006, 0, 1.567, -4.189, 0, 1.967, 2.653, 0, 2.417, -2.246, 0, 2.817, 2.136, 0, 3.217, -1.089, 0, 3.25, -1.03, 0, 3.267, -1.038, 0, 3.567, -0.274, 0, 3.65, -0.293, 0, 3.717, -0.258, 0, 3.733, -0.264, 0, 4.05, 1.213, 0, 4.533, -0.639, 0, 4.6, -0.345, 0, 4.667, -0.596, 0, 5.367, 1.047, 0, 5.817, -1.035, 0, 6.083, 0.088, 0, 6.1, 0.074, 0, 6.2, 0.376, 0, 6.217, 0.36, 0, 6.3, 0.607, 0, 6.333, 0.59, 0, 6.417, 0.654, 0, 6.817, -1.56, 0, 7.267, 1.931, 0, 7.667, -1.985, 0, 8.1, 1.35, 0, 8.417, -0.765, 0, 8.45, -0.738, 0, 8.533, -0.8, 0, 9, 1.201, 0, 9.433, -0.942, 0, 9.75, 0.512, 0, 10, -0.888, 0, 10.3, 1.834, 0, 10.667, -2.664, 0, 11.05, 2.135, 0, 11.5, -1.886, 0, 11.917, 1.206, 0, 12.233, -0.479, 0, 12.283, -0.422, 0, 12.517, -0.88, 0, 12.883, 1.165, 1, 12.922, 1.165, 12.961, 1.008, 13, 0.779]}, {"Target": "Parameter", "Id": "Param126", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.329, 0, 0.2, 14.55, 0, 0.5, -28.401, 0, 0.85, 19.287, 0, 1.417, -14.229, 0, 1.783, 10.39, 0, 2.25, -5.992, 0, 2.767, 5.786, 0, 3.167, -3.929, 0, 3.367, -2.421, 0, 3.517, -3.536, 0, 3.95, 6.471, 0, 4.383, -3.787, 0, 4.667, -1.859, 0, 4.867, -3.162, 0, 5.267, 7.413, 0, 5.633, -5.794, 0, 6.25, 6.566, 0, 6.683, -9.613, 0, 7.117, 12.822, 0, 7.517, -12.28, 0, 7.9, 14.404, 0, 8.233, -8.013, 0, 8.533, 0.614, 0, 8.65, 0.019, 0, 8.917, 4.44, 0, 9.267, -2.784, 0, 9.617, 0.33, 0, 9.85, -7.115, 0, 10.15, 14.897, 0, 10.483, -16.029, 0, 10.867, 10.643, 0, 11.383, -8.485, 0, 11.767, 6.078, 0, 12.383, -4.666, 0, 12.75, 3.597, 0, 12.85, 2.68, 0, 12.883, 2.681, 1, 12.922, 2.681, 12.961, 1.117, 13, -0.551]}, {"Target": "Parameter", "Id": "Param128", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.159, 0, 0.05, -1.036, 0, 0.3, -10.166, 0, 0.617, 24.488, 0, 0.95, -21.863, 0, 1.483, 11.459, 0, 1.883, -10.741, 0, 2.3, 6.37, 0, 2.7, -4.097, 0, 3.267, 3.938, 0, 3.517, 1.578, 0, 3.633, 1.836, 0, 4.05, -5.447, 0, 4.467, 4.114, 0, 4.767, 0.577, 0, 4.983, 2.166, 0, 5.35, -6.756, 0, 5.733, 6.538, 0, 6.317, -5.459, 0, 6.75, 9.745, 0, 7.183, -11.767, 0, 7.583, 12.233, 0, 8, -13.384, 0, 8.333, 9.68, 0, 8.667, -2.814, 0, 8.883, -0.744, 0, 8.9, -0.807, 0, 8.933, -0.714, 0, 9.067, -1.704, 0, 9.333, 2.292, 0, 9.7, -0.435, 0, 9.967, 4.667, 0, 10.25, -12.831, 0, 10.583, 15.893, 0, 10.95, -12.379, 0, 11.367, 6.978, 0, 11.85, -5.711, 0, 12.333, 2.97, 0, 12.45, 2, 0, 12.483, 2.022, 0, 12.867, -4.405, 1, 12.911, -4.405, 12.956, -3.345, 13, -1.932]}, {"Target": "Parameter", "Id": "Param129", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.614, 0, 0.083, 2.291, 0, 0.167, 2.106, 0, 0.4, 6.96, 0, 0.717, -20.83, 0, 1.067, 22.728, 0, 1.467, -12.183, 0, 1.967, 10.026, 0, 2.367, -7.013, 0, 2.817, 4.731, 0, 3.35, -3.588, 0, 3.667, -0.591, 0, 3.75, -0.685, 0, 4.133, 4.616, 0, 4.55, -4.161, 0, 4.85, 0.742, 0, 5.1, -1.746, 0, 5.45, 5.777, 0, 5.833, -6.611, 0, 6.35, 4.571, 0, 6.85, -8.651, 0, 7.267, 10.966, 0, 7.683, -11.949, 0, 8.083, 12.77, 0, 8.433, -10.691, 0, 8.767, 4.868, 0, 9.117, 0.565, 0, 9.167, 0.586, 0, 9.433, -2.015, 0, 9.467, -1.963, 0, 9.483, -1.973, 0, 9.783, 0.789, 0, 10.067, -3.856, 0, 10.367, 10.965, 0, 10.7, -15.424, 0, 11.05, 13.699, 0, 11.45, -8.401, 0, 11.933, 5.397, 0, 12.4, -3.295, 0, 12.983, 3.378, 1, 12.989, 3.378, 12.994, 3.364, 13, 3.337]}, {"Target": "Parameter", "Id": "Param16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.267, 0.223, 0, 6.6, -0.46, 0, 10.317, 0.033, 1, 11.211, 0.033, 12.106, -0.502, 13, -0.521]}, {"Target": "Parameter", "Id": "Param19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 4.333, 0.217, 8.667, 0.433, 13, 0.65]}, {"Target": "Parameter", "Id": "Param142", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.812, 2, 0.017, 0.127, 1, 2.561, 0.305, 5.106, 0.482, 7.65, 0.66, 1, 8.1, 0.711, 8.55, 0.761, 9, 0.812, 2, 9.017, 0.127, 1, 10.345, 0.22, 11.672, 0.312, 13, 0.405]}, {"Target": "Parameter", "Id": "Param143", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.76, 1, 0.422, 0.82, 0.845, 0.88, 1.267, 0.94, 2, 1.283, 0.2, 1, 3.855, 0.387, 6.428, 0.573, 9, 0.76, 1, 9.422, 0.82, 9.845, 0.88, 10.267, 0.94, 2, 10.283, 0.2, 1, 11.189, 0.266, 12.094, 0.331, 13, 0.397]}, {"Target": "Parameter", "Id": "Param18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 0, 2, 0.35, 1, 2, 0.4, 2, 2, 0.45, 3, 2, 0.5, 4, 2, 0.55, 5, 2, 0.6, 0, 2, 0.65, 1, 2, 0.7, 2, 2, 0.75, 3, 2, 0.8, 4, 2, 0.85, 5, 2, 0.9, 0, 2, 0.95, 1, 2, 1, 2, 2, 1.05, 3, 2, 1.1, 4, 2, 1.15, 5, 2, 1.2, 0, 2, 1.25, 1, 2, 1.3, 2, 2, 1.35, 3, 2, 1.4, 4, 2, 1.45, 5, 2, 1.5, 0, 2, 1.55, 1, 2, 1.6, 2, 2, 1.65, 3, 2, 1.7, 4, 2, 1.75, 5, 2, 1.8, 0, 2, 1.85, 1, 2, 1.9, 2, 2, 1.95, 3, 2, 2, 4, 2, 2.05, 5, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 0, 2, 2.45, 1, 2, 2.5, 2, 2, 2.55, 3, 2, 2.6, 4, 2, 2.65, 5, 2, 2.7, 0, 2, 2.75, 1, 2, 2.8, 2, 2, 2.85, 3, 2, 2.9, 4, 2, 2.95, 5, 2, 3, 0, 2, 3.05, 1, 2, 3.1, 2, 2, 3.15, 3, 2, 3.2, 4, 2, 3.25, 5, 2, 3.3, 0, 2, 3.35, 1, 2, 3.4, 2, 2, 3.45, 3, 2, 3.5, 4, 2, 3.55, 5, 2, 3.6, 0, 2, 3.65, 1, 2, 3.7, 2, 2, 3.75, 3, 2, 3.8, 4, 2, 3.85, 5, 2, 3.9, 0, 2, 3.95, 1, 2, 4, 2, 2, 4.05, 3, 2, 4.1, 4, 2, 4.15, 5, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 0, 2, 4.55, 1, 2, 4.6, 2, 2, 4.65, 3, 2, 4.7, 4, 2, 4.75, 5, 2, 4.8, 0, 2, 4.85, 1, 2, 4.9, 2, 2, 4.95, 3, 2, 5, 4, 2, 5.05, 5, 2, 5.1, 0, 2, 5.15, 1, 2, 5.2, 2, 2, 5.25, 3, 2, 5.3, 4, 2, 5.35, 5, 2, 5.4, 0, 2, 5.45, 1, 2, 5.5, 2, 2, 5.55, 3, 2, 5.6, 4, 2, 5.65, 5, 2, 5.7, 0, 2, 5.75, 1, 2, 5.8, 2, 2, 5.85, 3, 2, 5.9, 4, 2, 5.95, 5, 2, 6, 0, 2, 6.05, 1, 2, 6.1, 2, 2, 6.15, 3, 2, 6.2, 4, 2, 6.25, 5, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 0, 2, 6.65, 1, 2, 6.7, 2, 2, 6.75, 3, 2, 6.8, 4, 2, 6.85, 5, 2, 6.9, 0, 2, 6.95, 1, 2, 7, 2, 2, 7.05, 3, 2, 7.1, 4, 2, 7.15, 5, 2, 7.2, 0, 2, 7.25, 1, 2, 7.3, 2, 2, 7.35, 3, 2, 7.4, 4, 2, 7.45, 5, 2, 7.5, 0, 2, 7.55, 1, 2, 7.6, 2, 2, 7.65, 3, 2, 7.7, 4, 2, 7.75, 5, 2, 7.8, 0, 2, 7.85, 1, 2, 7.9, 2, 2, 7.95, 3, 2, 8, 4, 2, 8.05, 5, 2, 8.1, 0, 2, 8.15, 1, 2, 8.2, 2, 2, 8.25, 3, 2, 8.3, 4, 2, 8.35, 5, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 0, 2, 8.75, 1, 2, 8.8, 2, 2, 8.85, 3, 2, 8.9, 4, 2, 8.95, 5, 2, 9, 0, 2, 9.05, 1, 2, 9.1, 2, 2, 9.15, 3, 2, 9.2, 4, 2, 9.25, 5, 2, 9.3, 0, 2, 9.35, 1, 2, 9.4, 2, 2, 9.45, 3, 2, 9.5, 4, 2, 9.55, 5, 2, 9.6, 0, 2, 9.65, 1, 2, 9.7, 2, 2, 9.75, 3, 2, 9.8, 4, 2, 9.85, 5, 2, 9.9, 0, 2, 9.95, 1, 2, 10, 2, 2, 10.05, 3, 2, 10.1, 4, 2, 10.15, 5, 2, 10.2, 0, 2, 10.25, 1, 2, 10.3, 2, 2, 10.35, 3, 2, 10.4, 4, 2, 10.45, 5, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 0, 2, 10.85, 1, 2, 10.9, 2, 2, 10.95, 3, 2, 11, 4, 2, 11.05, 5, 2, 11.1, 0, 2, 11.15, 1, 2, 11.2, 2, 2, 11.25, 3, 2, 11.3, 4, 2, 11.35, 5, 2, 11.4, 0, 2, 11.45, 1, 2, 11.5, 2, 2, 11.55, 3, 2, 11.6, 4, 2, 11.65, 5, 2, 11.7, 0, 2, 11.75, 1, 2, 11.8, 2, 2, 11.85, 3, 2, 11.9, 4, 2, 11.95, 5, 2, 12, 0, 2, 12.05, 1, 2, 12.1, 2, 2, 12.15, 3, 2, 12.2, 4, 2, 12.25, 5, 2, 12.3, 0, 2, 12.35, 1, 2, 12.4, 2, 2, 12.45, 3, 2, 12.5, 4, 2, 12.55, 5, 2, 12.6, 0, 2, 12.65, 1, 2, 12.7, 2, 2, 12.75, 3, 2, 12.8, 4, 2, 12.85, 5, 2, 12.9, 0, 2, 12.95, 1, 2, 13, 2]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 6, 2, 0.35, 0, 2, 0.4, 1, 2, 0.45, 2, 2, 0.5, 3, 2, 0.55, 4, 2, 0.6, 5, 2, 0.65, 6, 2, 0.7, 0, 2, 0.75, 1, 2, 0.8, 2, 2, 0.85, 3, 2, 0.9, 4, 2, 0.95, 5, 2, 1, 6, 2, 1.05, 0, 2, 1.1, 1, 2, 1.15, 2, 2, 1.2, 3, 2, 1.25, 4, 2, 1.3, 5, 2, 1.35, 6, 2, 1.4, 0, 2, 1.45, 1, 2, 1.5, 2, 2, 1.55, 3, 2, 1.6, 4, 2, 1.65, 5, 2, 1.7, 6, 2, 1.75, 0, 2, 1.8, 1, 2, 1.85, 2, 2, 1.9, 3, 2, 1.95, 4, 2, 2, 5, 2, 2.05, 6, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 6, 2, 2.45, 0, 2, 2.5, 1, 2, 2.55, 2, 2, 2.6, 3, 2, 2.65, 4, 2, 2.7, 5, 2, 2.75, 6, 2, 2.8, 0, 2, 2.85, 1, 2, 2.9, 2, 2, 2.95, 3, 2, 3, 4, 2, 3.05, 5, 2, 3.1, 6, 2, 3.15, 0, 2, 3.2, 1, 2, 3.25, 2, 2, 3.3, 3, 2, 3.35, 4, 2, 3.4, 5, 2, 3.45, 6, 2, 3.5, 0, 2, 3.55, 1, 2, 3.6, 2, 2, 3.65, 3, 2, 3.7, 4, 2, 3.75, 5, 2, 3.8, 6, 2, 3.85, 0, 2, 3.9, 1, 2, 3.95, 2, 2, 4, 3, 2, 4.05, 4, 2, 4.1, 5, 2, 4.15, 6, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 6, 2, 4.55, 0, 2, 4.6, 1, 2, 4.65, 2, 2, 4.7, 3, 2, 4.75, 4, 2, 4.8, 5, 2, 4.85, 6, 2, 4.9, 0, 2, 4.95, 1, 2, 5, 2, 2, 5.05, 3, 2, 5.1, 4, 2, 5.15, 5, 2, 5.2, 6, 2, 5.25, 0, 2, 5.3, 1, 2, 5.35, 2, 2, 5.4, 3, 2, 5.45, 4, 2, 5.5, 5, 2, 5.55, 6, 2, 5.6, 0, 2, 5.65, 1, 2, 5.7, 2, 2, 5.75, 3, 2, 5.8, 4, 2, 5.85, 5, 2, 5.9, 6, 2, 5.95, 0, 2, 6, 1, 2, 6.05, 2, 2, 6.1, 3, 2, 6.15, 4, 2, 6.2, 5, 2, 6.25, 6, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 6, 2, 6.65, 0, 2, 6.7, 1, 2, 6.75, 2, 2, 6.8, 3, 2, 6.85, 4, 2, 6.9, 5, 2, 6.95, 6, 2, 7, 0, 2, 7.05, 1, 2, 7.1, 2, 2, 7.15, 3, 2, 7.2, 4, 2, 7.25, 5, 2, 7.3, 6, 2, 7.35, 0, 2, 7.4, 1, 2, 7.45, 2, 2, 7.5, 3, 2, 7.55, 4, 2, 7.6, 5, 2, 7.65, 6, 2, 7.7, 0, 2, 7.75, 1, 2, 7.8, 2, 2, 7.85, 3, 2, 7.9, 4, 2, 7.95, 5, 2, 8, 6, 2, 8.05, 0, 2, 8.1, 1, 2, 8.15, 2, 2, 8.2, 3, 2, 8.25, 4, 2, 8.3, 5, 2, 8.35, 6, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 6, 2, 8.75, 0, 2, 8.8, 1, 2, 8.85, 2, 2, 8.9, 3, 2, 8.95, 4, 2, 9, 5, 2, 9.05, 6, 2, 9.1, 0, 2, 9.15, 1, 2, 9.2, 2, 2, 9.25, 3, 2, 9.3, 4, 2, 9.35, 5, 2, 9.4, 6, 2, 9.45, 0, 2, 9.5, 1, 2, 9.55, 2, 2, 9.6, 3, 2, 9.65, 4, 2, 9.7, 5, 2, 9.75, 6, 2, 9.8, 0, 2, 9.85, 1, 2, 9.9, 2, 2, 9.95, 3, 2, 10, 4, 2, 10.05, 5, 2, 10.1, 6, 2, 10.15, 0, 2, 10.2, 1, 2, 10.25, 2, 2, 10.3, 3, 2, 10.35, 4, 2, 10.4, 5, 2, 10.45, 6, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 6, 2, 10.85, 0, 2, 10.9, 1, 2, 10.95, 2, 2, 11, 3, 2, 11.05, 4, 2, 11.1, 5, 2, 11.15, 6, 2, 11.2, 0, 2, 11.25, 1, 2, 11.3, 2, 2, 11.35, 3, 2, 11.4, 4, 2, 11.45, 5, 2, 11.5, 6, 2, 11.55, 0, 2, 11.6, 1, 2, 11.65, 2, 2, 11.7, 3, 2, 11.75, 4, 2, 11.8, 5, 2, 11.85, 6, 2, 11.9, 0, 2, 11.95, 1, 2, 12, 2, 2, 12.05, 3, 2, 12.1, 4, 2, 12.15, 5, 2, 12.2, 6, 2, 12.25, 0, 2, 12.3, 1, 2, 12.35, 2, 2, 12.4, 3, 2, 12.45, 4, 2, 12.5, 5, 2, 12.55, 6, 2, 12.6, 0, 2, 12.65, 1, 2, 12.7, 2, 2, 12.75, 3, 2, 12.8, 4, 2, 12.85, 5, 2, 12.9, 6, 2, 12.95, 0, 2, 13, 1]}, {"Target": "Parameter", "Id": "Param51", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 6, 2, 0.35, 0, 2, 0.4, 1, 2, 0.45, 2, 2, 0.5, 3, 2, 0.55, 4, 2, 0.6, 5, 2, 0.65, 6, 2, 0.7, 0, 2, 0.75, 1, 2, 0.8, 2, 2, 0.85, 3, 2, 0.9, 4, 2, 0.95, 5, 2, 1, 6, 2, 1.05, 0, 2, 1.1, 1, 2, 1.15, 2, 2, 1.2, 3, 2, 1.25, 4, 2, 1.3, 5, 2, 1.35, 6, 2, 1.4, 0, 2, 1.45, 1, 2, 1.5, 2, 2, 1.55, 3, 2, 1.6, 4, 2, 1.65, 5, 2, 1.7, 6, 2, 1.75, 0, 2, 1.8, 1, 2, 1.85, 2, 2, 1.9, 3, 2, 1.95, 4, 2, 2, 5, 2, 2.05, 6, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 6, 2, 2.45, 0, 2, 2.5, 1, 2, 2.55, 2, 2, 2.6, 3, 2, 2.65, 4, 2, 2.7, 5, 2, 2.75, 6, 2, 2.8, 0, 2, 2.85, 1, 2, 2.9, 2, 2, 2.95, 3, 2, 3, 4, 2, 3.05, 5, 2, 3.1, 6, 2, 3.15, 0, 2, 3.2, 1, 2, 3.25, 2, 2, 3.3, 3, 2, 3.35, 4, 2, 3.4, 5, 2, 3.45, 6, 2, 3.5, 0, 2, 3.55, 1, 2, 3.6, 2, 2, 3.65, 3, 2, 3.7, 4, 2, 3.75, 5, 2, 3.8, 6, 2, 3.85, 0, 2, 3.9, 1, 2, 3.95, 2, 2, 4, 3, 2, 4.05, 4, 2, 4.1, 5, 2, 4.15, 6, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 6, 2, 4.55, 0, 2, 4.6, 1, 2, 4.65, 2, 2, 4.7, 3, 2, 4.75, 4, 2, 4.8, 5, 2, 4.85, 6, 2, 4.9, 0, 2, 4.95, 1, 2, 5, 2, 2, 5.05, 3, 2, 5.1, 4, 2, 5.15, 5, 2, 5.2, 6, 2, 5.25, 0, 2, 5.3, 1, 2, 5.35, 2, 2, 5.4, 3, 2, 5.45, 4, 2, 5.5, 5, 2, 5.55, 6, 2, 5.6, 0, 2, 5.65, 1, 2, 5.7, 2, 2, 5.75, 3, 2, 5.8, 4, 2, 5.85, 5, 2, 5.9, 6, 2, 5.95, 0, 2, 6, 1, 2, 6.05, 2, 2, 6.1, 3, 2, 6.15, 4, 2, 6.2, 5, 2, 6.25, 6, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 6, 2, 6.65, 0, 2, 6.7, 1, 2, 6.75, 2, 2, 6.8, 3, 2, 6.85, 4, 2, 6.9, 5, 2, 6.95, 6, 2, 7, 0, 2, 7.05, 1, 2, 7.1, 2, 2, 7.15, 3, 2, 7.2, 4, 2, 7.25, 5, 2, 7.3, 6, 2, 7.35, 0, 2, 7.4, 1, 2, 7.45, 2, 2, 7.5, 3, 2, 7.55, 4, 2, 7.6, 5, 2, 7.65, 6, 2, 7.7, 0, 2, 7.75, 1, 2, 7.8, 2, 2, 7.85, 3, 2, 7.9, 4, 2, 7.95, 5, 2, 8, 6, 2, 8.05, 0, 2, 8.1, 1, 2, 8.15, 2, 2, 8.2, 3, 2, 8.25, 4, 2, 8.3, 5, 2, 8.35, 6, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 6, 2, 8.75, 0, 2, 8.8, 1, 2, 8.85, 2, 2, 8.9, 3, 2, 8.95, 4, 2, 9, 5, 2, 9.05, 6, 2, 9.1, 0, 2, 9.15, 1, 2, 9.2, 2, 2, 9.25, 3, 2, 9.3, 4, 2, 9.35, 5, 2, 9.4, 6, 2, 9.45, 0, 2, 9.5, 1, 2, 9.55, 2, 2, 9.6, 3, 2, 9.65, 4, 2, 9.7, 5, 2, 9.75, 6, 2, 9.8, 0, 2, 9.85, 1, 2, 9.9, 2, 2, 9.95, 3, 2, 10, 4, 2, 10.05, 5, 2, 10.1, 6, 2, 10.15, 0, 2, 10.2, 1, 2, 10.25, 2, 2, 10.3, 3, 2, 10.35, 4, 2, 10.4, 5, 2, 10.45, 6, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 6, 2, 10.85, 0, 2, 10.9, 1, 2, 10.95, 2, 2, 11, 3, 2, 11.05, 4, 2, 11.1, 5, 2, 11.15, 6, 2, 11.2, 0, 2, 11.25, 1, 2, 11.3, 2, 2, 11.35, 3, 2, 11.4, 4, 2, 11.45, 5, 2, 11.5, 6, 2, 11.55, 0, 2, 11.6, 1, 2, 11.65, 2, 2, 11.7, 3, 2, 11.75, 4, 2, 11.8, 5, 2, 11.85, 6, 2, 11.9, 0, 2, 11.95, 1, 2, 12, 2, 2, 12.05, 3, 2, 12.1, 4, 2, 12.15, 5, 2, 12.2, 6, 2, 12.25, 0, 2, 12.3, 1, 2, 12.35, 2, 2, 12.4, 3, 2, 12.45, 4, 2, 12.5, 5, 2, 12.55, 6, 2, 12.6, 0, 2, 12.65, 1, 2, 12.7, 2, 2, 12.75, 3, 2, 12.8, 4, 2, 12.85, 5, 2, 12.9, 6, 2, 12.95, 0, 2, 13, 1]}, {"Target": "Parameter", "Id": "Param42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 4.333, 0.048, 8.667, 0.096, 13, 0.144]}, {"Target": "Parameter", "Id": "Param45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.4, 1, 4.333, -0.352, 8.667, -0.304, 13, -0.256]}, {"Target": "Parameter", "Id": "Param46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.3, 1, 4.333, 0.348, 8.667, 0.396, 13, 0.444]}, {"Target": "Parameter", "Id": "Param50", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 4.333, 0.867, 8.667, 1.733, 13, 2.6]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2.5, 10, 0, 5.017, -10, 0, 7.5, 10, 0, 10, -10, 0, 12.5, 26.92, 1, 12.667, 26.92, 12.833, 25.463, 13, 23.127]}, {"Target": "Parameter", "Id": "Param17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.76, 0, 2.5, 0.66, 0, 5.017, -5.76, 0, 7.5, 0.66, 0, 10, -5.76, 0, 12.5, 21.06, 1, 12.667, 21.06, 12.833, 20.001, 13, 18.305]}, {"Target": "Parameter", "Id": "Param49", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.35, 0, 1.25, 0, 0, 3.75, 0.7, 0, 6.267, 0, 0, 8.767, 0.7, 0, 11.283, 0, 1, 11.855, 0, 12.428, 0.33, 13, 0.537]}, {"Target": "Parameter", "Id": "Param40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.25, 0, 0.917, 10, 0, 2.917, -10, 0, 4.917, 10, 0, 6.917, -10, 0, 8.917, 10, 0, 10.917, -10, 0, 12.917, 10, 1, 12.945, 10, 12.972, 9.965, 13, 9.899]}, {"Target": "Parameter", "Id": "Param41", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2, 10, 0, 4, -10, 0, 6, 10, 0, 8, -10, 0, 10, 10, 0, 12, -10, 1, 12.333, -10, 12.667, -5, 13, 0]}, {"Target": "Parameter", "Id": "Param32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2, 10, 0, 4, -10, 0, 6, 10, 0, 8, -10, 0, 10, 10, 0, 12, -10, 1, 12.333, -10, 12.667, -5, 13, 0]}, {"Target": "Parameter", "Id": "Param33", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.886, 0, 0.583, 10, 0, 2.583, -10, 0, 4.583, 10, 0, 6.583, -10, 0, 8.583, 10, 0, 10.583, -10, 0, 12.583, 10, 1, 12.722, 10, 12.861, 9.132, 13, 7.758]}, {"Target": "Parameter", "Id": "Param34", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -14.451, 0, 1.333, 30, 0, 3.333, -30, 0, 5.333, 30, 0, 7.333, -30, 0, 9.333, 30, 0, 11.333, -30, 1, 11.889, -30, 12.444, 11.667, 13, 25.556]}, {"Target": "Parameter", "Id": "Param28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -15, 0, 2, 15, 0, 4, -15, 0, 6, 15, 0, 8, -15, 0, 10, 15, 0, 12, -15, 1, 12.333, -15, 12.667, -7.5, 13, 0]}, {"Target": "Parameter", "Id": "Param29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.886, 0, 0.583, 10, 0, 2.583, -10, 0, 4.583, 10, 0, 6.583, -10, 0, 8.583, 10, 0, 10.583, -10, 0, 12.583, 10, 1, 12.722, 10, 12.861, 9.132, 13, 7.758]}, {"Target": "Parameter", "Id": "Param35", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -14.451, 0, 1.333, 30, 0, 3.333, -30, 0, 5.333, 30, 0, 7.333, -30, 0, 9.333, 30, 0, 11.333, -30, 1, 11.889, -30, 12.444, 11.667, 13, 25.556]}, {"Target": "Parameter", "Id": "Param30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.959, 0, 0.8, 10, 0, 2.8, -10, 0, 4.8, 10, 0, 6.8, -10, 0, 8.8, 10, 0, 10.8, -10, 0, 12.8, 10, 1, 12.867, 10, 12.933, 9.8, 13, 9.44]}, {"Target": "Parameter", "Id": "Param31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 9.44, 0, 1.8, -10, 0, 3.8, 10, 0, 5.8, -10, 0, 7.8, 10, 0, 9.8, -10, 0, 11.8, 10, 1, 12.2, 10, 12.6, 2.8, 13, -2.96]}, {"Target": "Parameter", "Id": "Param36", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 13.773, 0, 0.683, 30, 0, 2.683, -30, 0, 4.683, 30, 0, 6.683, -30, 0, 8.683, 30, 0, 10.683, -30, 0, 12.683, 30, 1, 12.789, 30, 12.894, 28.496, 13, 25.964]}, {"Target": "Parameter", "Id": "GL1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 4.333, 0.193, 8.667, 0.386, 13, 0.579]}, {"Target": "Parameter", "Id": "GL2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.39, 1, 4.333, 0.583, 8.667, 0.776, 13, 0.969]}, {"Target": "Parameter", "Id": "GL3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.72, 1, 4.333, 0.913, 8.667, 1.106, 13, 1.299]}, {"Target": "Parameter", "Id": "GL4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.21, 1, 4.333, 0.403, 8.667, 0.596, 13, 0.789]}, {"Target": "Parameter", "Id": "GL5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.53, 1, 4.333, 0.723, 8.667, 0.915, 13, 1.108]}, {"Target": "Parameter", "Id": "GL6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.89, 1, 4.333, 1.083, 8.667, 1.276, 13, 1.469]}, {"Target": "Parameter", "Id": "GL7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.77, 1, 4.333, 0.963, 8.667, 1.156, 13, 1.349]}, {"Target": "Parameter", "Id": "GL8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.09, 1, 4.333, 0.283, 8.667, 0.475, 13, 0.668]}, {"Target": "Parameter", "Id": "GL9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.301, 1, 4.333, 0.494, 8.667, 0.687, 13, 0.88]}, {"Target": "Parameter", "Id": "GL10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.45, 1, 4.333, 0.643, 8.667, 0.836, 13, 1.029]}, {"Target": "Parameter", "Id": "GL11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.64, 1, 4.333, 0.833, 8.667, 1.026, 13, 1.219]}, {"Target": "Parameter", "Id": "GL12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.15, 1, 4.333, 0.343, 8.667, 0.536, 13, 0.729]}, {"Target": "Parameter", "Id": "GL13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.585, 1, 4.333, 0.778, 8.667, 0.97, 13, 1.163]}, {"Target": "Parameter", "Id": "GL14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 4.333, 0.193, 8.667, 0.386, 13, 0.579]}, {"Target": "Parameter", "Id": "Param161", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.51, 1, 4.333, 0.751, 8.667, 0.992, 13, 1.233]}, {"Target": "Parameter", "Id": "Param162", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.31, 1, 4.333, 0.551, 8.667, 0.792, 13, 1.033]}, {"Target": "Parameter", "Id": "Param163", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.72, 1, 4.333, 0.961, 8.667, 1.202, 13, 1.443]}, {"Target": "Parameter", "Id": "Param164", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 4.333, 0.241, 8.667, 0.482, 13, 0.723]}, {"Target": "Parameter", "Id": "Param165", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.17, 1, 4.333, 0.411, 8.667, 0.652, 13, 0.893]}, {"Target": "Parameter", "Id": "Param23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 1.494, 0.019, 2.989, 0.037, 4.483, 0.056, 1, 4.489, 0.037, 4.494, 0.019, 4.5, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "touch_drag21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param54", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "man_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "man_<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "man_<PERSON>ze", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param189", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "shangguang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param186", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "shangguang2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param53", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "MB_xingfengTMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param127", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Mouthfunnel", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param293", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param72", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param74", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param75", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param73", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "MB_fenweiTMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "MB_fenweiliangdu", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 13, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiandu", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 13, 0.6]}, {"Target": "Parameter", "Id": "MB_fenweiqiu1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 13, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 13, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 13, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 13, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 13, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 13, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiushangxia", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "MB_fenweiqiudaxiao", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param167", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param169", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param196", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param56", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param59", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10, 0, 13, 10]}, {"Target": "Parameter", "Id": "Param57", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param124", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param60", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param47", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param125", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param171", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param166", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param123", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param39", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param61", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 13, 1]}, {"Target": "Parameter", "Id": "Param71", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 13, 1]}, {"Target": "Parameter", "Id": "Param20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param52", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param70", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param208", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "touch_drag23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "touch_drag24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "touch_drag25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "touch_drag26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "touch_drag27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "touch_drag28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "touch_drag29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "touch_drag30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "touch_drag31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "touch_drag32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "PartOpacity", "Id": "Part7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 13, 1]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 12.5, "Value": ""}]}