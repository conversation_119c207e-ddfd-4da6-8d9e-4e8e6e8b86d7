{"Version": 3, "Meta": {"Duration": 9.0, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 230, "TotalSegmentCount": 2281, "TotalPointCount": 2643, "UserDataCount": 2, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.15, 0.12, 0, 2.733, -0.105, 1, 2.966, -0.105, 3.2, 0.124, 3.433, 0.16, 1, 3.716, 0.204, 4, 0.2, 4.283, 0.2, 1, 5.183, 0.2, 6.083, 0.184, 6.983, 0.113, 1, 7.655, 0.06, 8.328, 0, 9, 0]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.15, -0.12, 0, 2.733, 0.254, 1, 2.966, 0.254, 3.2, -0.058, 3.433, -0.11, 1, 3.716, -0.173, 4, -0.168, 4.283, -0.168, 1, 5.183, -0.168, 6.083, -0.16, 6.983, -0.106, 1, 7.655, -0.066, 8.328, 0, 9, 0]}, {"Target": "Parameter", "Id": "All_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.134, 0, 0.433, -0.194, 0, 0.783, 0.189, 0, 1.183, -0.282, 0, 1.85, 0.371, 0, 2.317, 0.286, 0, 2.8, 0.485, 0, 3.45, -0.331, 0, 4.067, 0.117, 0, 4.733, -0.098, 0, 5.133, 0.152, 0, 5.45, 0.056, 0, 5.85, 0.227, 0, 6.4, 0.134, 0, 6.817, 0.516, 0, 7.183, -0.033, 0, 7.767, 0.158, 0, 8.1, 0.114, 0, 8.8, 0.507, 0, 9, -0.134]}, {"Target": "Parameter", "Id": "All_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.244, 0, 0.483, -0.346, 0, 1.017, 0.419, 0, 2.5, -0.586, 0, 2.967, 0.51, 0, 3.767, -0.397, 0, 4.433, -0.203, 0, 4.783, -0.306, 1, 5.005, -0.306, 5.228, -0.318, 5.45, -0.258, 1, 5.683, -0.195, 5.917, 0.266, 6.15, 0.266, 0, 6.867, -0.469, 0, 7.617, 0.008, 0, 7.95, -0.026, 0, 8.167, 0.018, 0, 8.867, -0.498, 0, 9, -0.244]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 0.422, 0, 0.845, 0.884, 1.267, 1.17, 1, 1.756, 1.501, 2.244, 1.517, 2.733, 1.812, 1, 2.966, 1.953, 3.2, 2.848, 3.433, 2.848, 0, 5.117, 2.714, 0, 6.983, 3.201, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param186", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 6.733, 0, 0, 7.867, 30, 2, 7.883, 0, 2, 9, 0]}, {"Target": "Parameter", "Id": "Param26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.833, 5.951, 0, 2.85, -4.62, 0, 4.183, -4.018, 0, 5.683, -8.284, 0, 7.683, 29.92, 0, 9, 0]}, {"Target": "Parameter", "Id": "ParamAngleX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.304, 0, 0.3, -8.156, 0, 0.833, 14.178, 0, 2.317, -15.141, 0, 2.783, 16.853, 0, 3.583, -9.62, 0, 4.25, -3.979, 0, 4.6, -6.976, 1, 4.822, -6.976, 5.045, -7.296, 5.267, -5.573, 1, 5.5, -3.763, 5.734, 9.706, 5.967, 9.706, 0, 6.683, -11.743, 0, 7.433, -0.821, 0, 7.767, -1.81, 0, 7.983, -0.531, 0, 8.683, -12.583, 0, 9, -1.304]}, {"Target": "Parameter", "Id": "ParamAngleY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.124, 0, 0.25, -3.694, 0, 0.6, 7.471, 0, 1, -6.29, 0, 1.667, 12.784, 0, 2.133, 10.302, 0, 2.617, 16.115, 0, 3.267, -7.717, 0, 3.883, 5.358, 0, 4.55, -0.903, 0, 4.95, 6.384, 0, 5.267, 3.584, 0, 5.667, 8.575, 0, 6.217, 5.866, 0, 6.633, 17.023, 0, 7, 0.994, 0, 7.583, 6.564, 0, 7.917, 5.286, 0, 8.617, 16.769, 0, 9, -0.124]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.543, 0, 0.3, -3.35, 0, 0.7, -1.899, 0, 1.083, -5.918, 1, 1.3, -5.918, 1.516, -3.664, 1.733, -3.342, 1, 1.972, -2.987, 2.211, -3.007, 2.45, -2.728, 1, 2.539, -2.624, 2.628, 6, 2.717, 6, 1, 2.861, 6, 3.006, -4.235, 3.15, -7, 1, 3.272, -9.34, 3.395, -9, 3.517, -9, 0, 3.967, -4.851, 0, 4.433, -6.136, 0, 4.817, -4.097, 0, 5.283, -4.655, 0, 5.667, 0, 0, 6.083, -4.66, 0, 6.667, 2, 0, 7, -12, 1, 7.306, -12, 7.611, -12.139, 7.917, -10.852, 1, 8.156, -9.846, 8.394, 1.944, 8.633, 1.944, 0, 9, 0.543]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -4.357, 0, 0.517, -0.087, 0, 0.9, -10.506, 1, 1.117, -10.506, 1.333, 3.398, 1.55, 4.273, 1, 1.789, 5.237, 2.028, 5.164, 2.267, 5.867, 1, 2.356, 6.129, 2.444, 9, 2.533, 9, 1, 2.678, 9, 2.822, -6.595, 2.967, -9.735, 1, 3.089, -12.392, 3.211, -11.942, 3.333, -11.942, 0, 3.783, -7.739, 0, 4.383, -9.438, 0, 4.8, -8.239, 0, 5.1, -9, 0, 5.483, -3.848, 0, 5.9, -8, 0, 6.567, -0.775, 0, 6.933, -12, 1, 7.239, -12, 7.544, -12.081, 7.85, -10.852, 1, 8.067, -9.98, 8.283, -1.315, 8.5, -1.315, 0, 9, -4.357]}, {"Target": "Parameter", "Id": "ParamBodyAngleX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -8.417, 0, 0.183, -13, 0, 0.633, 8.898, 1, 0.822, 8.898, 1.011, -2.416, 1.2, -3.294, 1, 1.522, -4.791, 1.845, -4.877, 2.167, -6.258, 1, 2.306, -6.853, 2.444, -22.203, 2.583, -22.203, 0, 2.95, 28.811, 0, 3.533, 0.722, 0, 4.083, 3.784, 1, 4.344, 3.784, 4.606, 3.695, 4.867, 2.496, 1, 5, 1.884, 5.134, -1.72, 5.267, -1.72, 0, 5.733, 6.328, 0, 6.45, -7.606, 0, 6.8, 1.013, 0, 7.283, -3.002, 0, 7.75, 0, 0, 8.333, -14.891, 0, 8.8, 1.013, 0, 9, -8.417]}, {"Target": "Parameter", "Id": "ParamBodyAngleY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -9.558, 0, 0.417, 8.992, 0, 0.9, -9, 0, 1.483, -0.032, 0, 1.933, -3.696, 0, 2.433, 4.939, 1, 2.583, 4.939, 2.733, -6.69, 2.883, -18.303, 1, 2.983, -26.045, 3.083, -26.947, 3.183, -26.947, 0, 3.7, 1.602, 0, 4.383, -7.125, 0, 4.767, 0.512, 0, 5.083, -4.082, 0, 5.533, 6, 0, 5.75, 2, 1, 5.844, 2, 5.939, 6.159, 6.033, 9, 1, 6.172, 13.178, 6.311, 14, 6.45, 14, 0, 6.867, -2, 0, 7.633, 4, 0, 8.033, -1, 0, 8.45, 14, 0, 9, -9.558]}, {"Target": "Parameter", "Id": "ParamWaistZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -13.805, 0, 0.183, -15.424, 0, 0.683, -9, 0, 1.317, -16.239, 0, 1.567, -15.421, 0, 2.15, -17.556, 0, 2.717, -6.667, 0, 3.083, -15.58, 0, 3.633, -14.441, 0, 4.067, -15.724, 0, 4.467, -15.315, 0, 5.133, -18.972, 0, 5.783, -9.622, 0, 6.3, -18.523, 0, 6.65, -16.691, 0, 6.75, -16.963, 0, 7.65, -12.614, 0, 8.25, -17, 0, 9, -13.805]}, {"Target": "Parameter", "Id": "ParamShoulderStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.893, 0, 0.283, -0.235, 0, 0.783, -9.529, 0, 1.35, 1.943, 0, 1.8, 1.239, 0, 2.3, 2.897, 1, 2.45, 2.897, 2.6, -5.11, 2.75, -6.639, 1, 2.85, -7.658, 2.95, -7.365, 3.05, -7.365, 0, 3.567, -2.298, 0, 4.25, -4.735, 0, 4.633, -2.881, 0, 4.95, -4.164, 0, 5.4, -3.573, 0, 5.9, -9.971, 0, 6.333, 2.28, 1, 6.528, 2.28, 6.722, -5.807, 6.917, -6.765, 1, 7.239, -8.352, 7.561, -8.35, 7.883, -8.35, 0, 8.433, -1.991, 0, 9, -2.893]}, {"Target": "Parameter", "Id": "ParamShoulderStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.371, 0, 0.483, -0.235, 0, 0.983, -9.529, 0, 1.55, 1.943, 0, 2, 1.239, 0, 2.5, 2.897, 1, 2.65, 2.897, 2.8, -5.11, 2.95, -6.639, 1, 3.05, -7.658, 3.15, -7.365, 3.25, -7.365, 0, 3.767, -2.298, 0, 4.45, -4.735, 0, 4.833, -2.881, 0, 5.15, -4.164, 0, 5.6, -3.573, 0, 6.1, -10.317, 0, 6.533, 0.605, 1, 6.694, 0.605, 6.856, -6.518, 7.017, -7.306, 1, 7.345, -8.907, 7.672, -9.006, 8, -9.006, 0, 8.533, -2.187, 0, 9, -3.371]}, {"Target": "Parameter", "Id": "ParamButtXA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -6.39, 0, 0.183, -5.227, 0, 0.617, -13, 0, 1.183, -1.63, 0, 1.667, -2.332, 0, 2.15, -0.243, 0, 2.567, -8.371, 0, 2.917, -0.944, 0, 3.4, -4, 0, 4.067, -1, 0, 4.5, -2, 0, 5.417, 2.065, 0, 5.75, -2, 0, 6.15, 6, 0, 6.7, -1.539, 0, 7, -0.39, 0, 7.733, -2, 0, 8.15, 0, 0, 8.7, -8.518, 0, 9, -6.39]}, {"Target": "Parameter", "Id": "Param43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.915, 0, 0.25, -0.088, 0, 0.75, -10, 0, 1.317, 2.265, 0, 1.783, 1.26, 0, 2.267, 2.726, 0, 2.717, -7, 0, 3.017, -2, 0, 3.533, -6, 0, 4.217, -3.029, 0, 4.6, -5, 0, 5.5, -0.499, 0, 5.85, -3, 0, 6.3, 0.13, 0, 6.8, -1.253, 0, 7.05, -0.088, 0, 7.85, -4, 0, 8.3, 3.44, 0, 8.8, -3.148, 0, 9, -1.915]}, {"Target": "Parameter", "Id": "ParamButtZA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.239, 0, 0.45, -7, 0, 0.917, -1.802, 0, 1.467, -2.377, 0, 1.917, -0.482, 0, 2.35, -2, 0, 2.833, 5, 0, 3.133, -1, 0, 4.333, 1.404, 0, 4.683, -0.029, 0, 5.017, 0.42, 0, 5.483, -0.478, 0, 5.983, 0, 0, 6.467, -5, 0, 6.917, -4.449, 0, 7.317, -5.695, 0, 7.833, -2.782, 0, 8.467, -8.218, 0, 9, -5.239]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.833, 15, 2, 5.733, 15, 0, 6.65, 30, 2, 7.467, 30, 0, 8.067, 15, 2, 9, 15]}, {"Target": "Parameter", "Id": "Param58", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.833, 15, 2, 5.733, 15, 0, 6.65, 21, 2, 7.467, 21, 0, 8.3, 30, 0, 8.55, 0, 2, 9, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.833, 0, 2, 5.733, 0, 0, 6.65, 30, 2, 7.467, 30, 0, 8.55, 0, 2, 9, 0]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 24.978, 0, 0.267, 30, 0, 0.667, 0, 2, 2.617, 0, 0, 3, 28.536, 0, 3.45, -7.74, 2, 5.967, -7.74, 0, 6.3, 25.86, 2, 7.467, 25.86, 0, 8.3, 18, 0, 9, 24.978]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -9.422, 0, 0.667, 16, 2, 2.717, 16, 0, 3.45, -5.58, 2, 5.733, -5.58, 0, 6.65, 8.4, 1, 7.2, 8.4, 7.75, 8.378, 8.3, 8, 1, 8.533, 7.84, 8.767, -9.422, 9, -9.422]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.038, 1, 0.111, 2.038, 0.222, 12.543, 0.333, 16, 1, 0.483, 20.667, 0.633, 20.925, 0.783, 20.925, 0, 1.8, 20.22, 0, 2.617, 20.94, 1, 2.734, 20.94, 2.85, 19.424, 2.967, 13.55, 1, 3.128, 5.439, 3.289, -0.675, 3.45, -0.675, 1, 4.211, -0.675, 4.972, -0.439, 5.733, 0.407, 1, 5.872, 0.561, 6.011, 5, 6.15, 5, 0, 6.65, 0.665, 0, 6.917, 1.295, 2, 7.467, 1.295, 0, 7.983, 0, 1, 8.111, 0, 8.239, -0.049, 8.367, 0.237, 1, 8.484, 0.498, 8.6, 3.707, 8.717, 3.707, 0, 9, 2.038]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.871, 0, 0.267, -6, 0, 0.717, -2.105, 0, 1.717, -2.905, 1, 2.05, -2.905, 2.384, -2.722, 2.717, -2, 1, 2.811, -1.795, 2.906, -1.081, 3, 0, 1, 3.183, 2.098, 3.367, 9.048, 3.55, 9.3, 1, 4.306, 10.34, 5.061, 10.576, 5.817, 10.576, 0, 6.25, 4, 0, 6.75, 10, 0, 7, 9.3, 0, 7.517, 9.84, 0, 7.983, 9.76, 0, 8.367, 10.62, 0, 8.717, -3.413, 0, 9, 0.871]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.131, 0, 0.417, -18.2, 0, 0.883, 4, 0, 1.917, -7, 1, 2.128, -7, 2.339, -7.013, 2.55, -4, 1, 2.689, -2.018, 2.828, 12.445, 2.967, 12.445, 0, 3.2, -2, 0, 3.367, 13.2, 0, 5.633, 10.7, 0, 5.933, 15, 0, 6.317, -5, 0, 6.867, 14, 0, 7.133, 13.48, 1, 7.283, 13.48, 7.433, 13.382, 7.583, 14.138, 1, 7.716, 14.81, 7.85, 19.82, 7.983, 19.82, 1, 8.111, 19.82, 8.239, 20.064, 8.367, 15.726, 1, 8.484, 11.765, 8.6, -7.999, 8.717, -7.999, 0, 9, 0.131]}, {"Target": "Parameter", "Id": "Param5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.633, 0, 0, 2.85, -9.24, 0, 3.1, 0, 2, 5.733, 0, 0, 6.65, -16, 2, 8.367, -16, 0, 9, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.833, 23, 0, 1.083, 0, 0, 2.633, 23, 0, 2.733, 0, 2, 3.1, 0, 2, 7.467, 0, 0, 7.75, -1, 0, 8.133, 0, 0, 8.3, -8.033, 0, 8.533, 0, 2, 9, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.833, 30, 2, 2.733, 30, 0, 3.1, 0, 2, 7.467, 0, 0, 7.75, 3, 0, 8.133, -30, 0, 8.3, 0, 0, 8.533, -30, 0, 9, 0]}, {"Target": "Parameter", "Id": "ParamHandRREDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.583, 7, 2, 3.05, 7, 2, 3.067, 2, 2, 6.117, 2, 2, 6.133, 8, 2, 8.483, 0, 2, 9, 0]}, {"Target": "Parameter", "Id": "Param55", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 2.4, 0, 0, 3.083, 7.14, 2, 5.55, 7.14, 0, 6.633, 15.99, 2, 8.383, 15.99, 0, 9, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.733, 0, 2, 0.75, 30, 2, 1, 30, 2, 2.817, 30, 2, 2.833, 0, 1, 2.972, -1.946, 3.111, -2.82, 3.25, -2.82, 2, 5.833, -2.82, 2, 5.85, 20, 2, 6.333, 20, 2, 6.35, 10, 2, 8.4, 10, 2, 8.417, 0, 2, 9, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 2.4, 0, 2, 3.083, 0, 2, 5.55, 0, 0, 6.633, 30, 2, 8.067, 30, 0, 8.55, 0, 2, 9, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.067, 11.04, 2, 2.1, 11.04, 1, 2.306, 11.04, 2.511, 10.526, 2.717, 5.34, 1, 2.895, 0.855, 3.072, -6.72, 3.25, -6.72, 2, 5.55, -6.72, 0, 6.633, -2.66, 2, 7.167, -2.66, 0, 8.55, 0, 2, 9, 0]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.317, 0, 0, 1.067, -16.98, 2, 2.1, -16.98, 0, 2.467, -6.485, 0, 2.817, -16.56, 0, 3.25, -7.26, 2, 5.55, -7.26, 0, 5.8, -5.108, 0, 6.1, -16.263, 1, 6.211, -16.263, 6.322, -1.121, 6.433, -1, 1, 7.139, -0.23, 7.844, 0, 8.55, 0, 2, 9, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.089, 0, 0.317, -2, 0, 0.7, 3, 0, 1.067, 0.21, 0, 1.6, 0.595, 0, 2.017, -0.106, 0, 2.717, 6, 1, 2.895, 6, 3.072, 0.277, 3.25, 0.04, 1, 4.017, -0.983, 4.783, -1.22, 5.55, -1.22, 0, 6.017, 6.875, 1, 6.156, 6.875, 6.294, 4.395, 6.433, 3.465, 1, 6.611, 2.274, 6.789, 2.24, 6.967, 2.24, 2, 7.55, 2.24, 1, 7.722, 2.24, 7.895, 2.229, 8.067, 3.017, 1, 8.228, 3.755, 8.389, 5.53, 8.55, 5.53, 0, 9, -0.089]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.769, 0, 0.383, -0.688, 0, 0.767, 4, 1, 0.945, 4, 1.122, -1.637, 1.3, -3.277, 1, 1.422, -4.405, 1.545, -4.146, 1.667, -4.146, 0, 2.017, -3.083, 1, 2.15, -3.083, 2.284, -2.32, 2.417, -4.199, 1, 2.695, -8.115, 2.972, -30.3, 3.25, -30.3, 0, 4.217, -29.373, 0, 5.55, -29.94, 1, 5.628, -29.94, 5.705, -30.067, 5.783, -28.442, 1, 5.861, -26.817, 5.939, -19.56, 6.017, -13.52, 1, 6.156, -2.734, 6.294, 5, 6.433, 5, 0, 6.967, -1.5, 0, 7.55, -1.44, 0, 8.067, -2.12, 0, 8.55, 5.449, 0, 9, 0.769]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.105, 0, 0.483, 0.738, 0, 0.867, -8, 1, 1.034, -8, 1.2, 8.458, 1.367, 11.523, 1, 1.472, 13.464, 1.578, 12.9, 1.683, 12.9, 0, 2.35, -6, 1, 2.472, -6, 2.595, 1.711, 2.717, 3.42, 1, 2.895, 5.905, 3.072, 5.94, 3.25, 5.94, 0, 5.55, 3.12, 1, 5.744, 3.12, 5.939, 8.822, 6.133, 14, 1, 6.272, 17.698, 6.411, 19.751, 6.55, 22, 1, 6.728, 24.879, 6.905, 29.498, 7.083, 29.7, 1, 7.35, 30, 7.616, 29.981, 7.883, 29.981, 1, 8.055, 29.981, 8.228, 27.773, 8.4, 23, 2, 8.417, 15.48, 1, 8.5, -6.735, 8.584, -17, 8.667, -17, 0, 9, -3.105]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.5, -11, 2, 8.067, -11, 0, 8.55, 0, 2, 9, 0]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 2, 0.933, 0, 2, 1.683, 0, 2, 2.1, 0, 2, 2.4, 0, 2, 3.083, 0, 2, 5.55, 0, 2, 6.633, 0, 2, 7.167, 0, 0, 7.8, 1, 0, 8.55, 0, 2, 9, 0]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.5, 0, 0, 0.933, -30, 1, 1.083, -30, 1.233, 5.605, 1.383, 9.914, 1, 1.622, 16.777, 1.861, 17, 2.1, 17, 0, 2.4, -29, 0, 3.083, 0, 2, 5.55, 0, 2, 6.633, 0, 2, 7.167, 0, 0, 7.8, 7, 0, 8.55, 0, 2, 9, 0]}, {"Target": "Parameter", "Id": "ParamHandLREDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.883, 4, 2, 2.733, 4, 2, 2.75, 2, 2, 5.55, 2, 2, 5.933, 8, 2, 6.85, 8, 2, 7.55, 8, 2, 8.4, 8, 2, 8.417, 1, 2, 9, 1]}, {"Target": "Parameter", "Id": "Param56", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.4, 0, 2, 2.417, 30, 2, 6.317, 30, 2, 6.333, 0, 2, 9, 0]}, {"Target": "Parameter", "Id": "Param3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 4.501, 0, 0.467, -0.824, 0, 0.483, -0.755, 0, 0.633, -2.562, 0, 0.667, -2.277, 0, 0.717, -2.344, 0, 1.283, 0.696, 0, 1.817, -0.77, 0, 2.417, 2.475, 0, 2.683, 1.694, 0, 2.933, 3.097, 0, 3.75, -2.118, 0, 5.417, 1.674, 0, 5.8, -0.985, 0, 6.35, 2.067, 0, 6.85, -0.153, 0, 8.233, 3.734, 0, 8.617, 2.912, 0, 9, 4.501]}, {"Target": "Parameter", "Id": "Param2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.326, 0, 0.35, 3.906, 0, 0.45, 3.737, 0, 0.467, 3.747, 0, 0.533, 2.851, 0, 0.633, 2.956, 0, 1.283, -3.492, 0, 1.817, -0.44, 0, 2.417, -6.633, 0, 2.683, -5.27, 0, 2.933, -7.783, 0, 3.817, 1.786, 0, 3.833, 1.784, 0, 4.217, 2.394, 0, 4.233, 2.365, 2, 4.25, 2.365, 0, 4.283, 2.362, 0, 4.333, 2.368, 0, 5.417, -5.389, 0, 5.783, -0.206, 0, 6.35, -5.976, 0, 6.85, -2.065, 0, 8.15, -8.461, 0, 8.833, 2.767, 0, 9, 2.326]}, {"Target": "Parameter", "Id": "Param", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 7.478, 0, 0.3, 9.672, 0, 1.567, -2.709, 0, 1.8, -2.515, 0, 2.217, -3.474, 0, 4.35, 8.678, 0, 4.767, 8.021, 0, 5.7, 11.97, 0, 6.35, 1.814, 0, 7.75, 7.289, 0, 8.333, 2.596, 0, 9, 7.478]}, {"Target": "Parameter", "Id": "Param6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.346, 0, 0.717, 0.581, 0, 0.8, 0.532, 0, 2.217, 2.281, 0, 2.683, 1.163, 0, 2.9, 1.454, 0, 2.95, 1.422, 0, 2.967, 1.435, 0, 3.517, 0.695, 0, 5.15, 2.22, 0, 5.783, 1.273, 0, 6.233, 2.731, 0, 6.817, 1.875, 0, 7.017, 1.93, 0, 7.75, 1.425, 0, 8.1, 2.033, 0, 9, -5.346]}, {"Target": "Parameter", "Id": "Param48", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -8.069, 0, 0.267, -12.512, 0, 0.8, -6.714, 0, 2.233, -13.384, 0, 2.683, -9.15, 0, 2.9, -10.238, 0, 2.95, -10.103, 0, 2.967, -10.149, 0, 3.517, -7.305, 0, 5.15, -13.091, 0, 5.783, -9.498, 0, 6.233, -15.101, 0, 6.817, -11.848, 0, 7.017, -12.037, 0, 7.75, -10.08, 0, 8.267, -13.873, 0, 8.867, -8.301, 2, 8.883, -8.301, 2, 8.9, -8.301, 0, 9, -8.069]}, {"Target": "Parameter", "Id": "Param37", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 8.923, 0, 0.583, 12.676, 0, 1.133, 10.585, 0, 2.617, 16.126, 0, 3.95, 8.462, 2, 3.967, 8.462, 0, 3.983, 8.461, 0, 4.35, 8.701, 0, 4.817, 7.463, 0, 4.9, 7.472, 0, 5.433, 6.52, 0, 6.617, 15.698, 0, 8.05, 7.447, 0, 8.617, 12.026, 0, 9, 8.923]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.9, 0, 0.333, 1, 0, 0.817, 0, 2, 1.433, 0, 0, 2.117, 0.9, 2, 3.033, 0.9, 0, 3.35, 0, 0, 3.667, 0.9, 2, 4.517, 0.9, 0, 4.717, 0, 0, 4.933, 0.9, 2, 5.8, 0.9, 0, 6.05, 1.056, 1, 6.133, 1.056, 6.217, 0.935, 6.3, 0.9, 1, 6.456, 0.835, 6.611, 0.829, 6.767, 0.755, 1, 6.856, 0.713, 6.944, 0, 7.033, 0, 2, 8.067, 0, 0, 8.383, 1, 0, 9, 0.9]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 0, 0.517, 0.7, 0, 0.817, 0.4, 2, 1.35, 0.4, 0, 1.517, 0, 0, 1.817, 0.7, 0, 2.117, 0, 2, 3.2, 0, 0, 3.45, 0.6, 0, 3.883, 0, 2, 4.617, 0, 0, 4.783, 0.7, 0, 5.067, 0, 2, 5.8, 0, 0, 6.05, 0.084, 0, 6.3, 0, 1, 6.456, 0, 6.611, 0.206, 6.767, 0.308, 1, 6.856, 0.366, 6.944, 0.355, 7.033, 0.4, 1, 7.383, 0.577, 7.733, 0.7, 8.083, 0.7, 0, 8.383, 0, 2, 9, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.9, 0, 0.333, 1, 0, 0.817, 0, 2, 1.433, 0, 0, 2.117, 0.9, 2, 3.033, 0.9, 0, 3.35, 0, 0, 3.667, 0.9, 2, 4.517, 0.9, 0, 4.717, 0, 0, 4.933, 0.9, 2, 5.8, 0.9, 0, 6.05, 1.047, 1, 6.133, 1.047, 6.217, 0.959, 6.3, 0.9, 1, 6.456, 0.789, 6.611, 0.763, 6.767, 0.763, 0, 7.033, 1, 2, 7.867, 1, 0, 8.067, 0, 0, 8.5, 1, 0, 9, 0.9]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 0, 0.517, 0.7, 0, 0.817, 0.4, 2, 1.35, 0.4, 0, 1.517, 0, 0, 1.817, 0.7, 0, 2.117, 0, 2, 3.2, 0, 0, 3.45, 0.6, 0, 3.883, 0, 2, 4.617, 0, 0, 4.783, 0.7, 0, 5.067, 0, 2, 5.8, 0, 0, 6.05, 0.091, 0, 6.3, 0, 0, 6.767, 0.308, 0, 7.033, 0, 2, 7.867, 0, 0, 8.067, 0.7, 0, 8.5, 0, 2, 9, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.817, 0.07, 2, 1.433, 0.07, 0, 2.117, 0, 2, 3.033, 0, 0, 3.333, 0.07, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, 0.07, 0, 4.933, 0, 2, 6.75, 0, 0, 7.2, -0.5, 2, 8.133, -0.5, 0, 8.417, 0, 2, 9, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.817, 0.364, 2, 1.433, 0.364, 0, 2.117, 0, 2, 3.033, 0, 0, 3.333, 0.364, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, 0.364, 0, 4.933, 0, 2, 6.75, 0, 0, 7.2, 0.6, 2, 8.133, 0.6, 0, 8.417, 0, 2, 9, 0]}, {"Target": "Parameter", "Id": "Param4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.817, -0.586, 2, 1.433, -0.586, 0, 2.117, 0, 2, 3.033, 0, 0, 3.333, -0.586, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, -0.586, 0, 4.933, 0, 2, 6.75, 0, 0, 7.2, -0.3, 2, 8.133, -0.3, 0, 8.417, 0, 2, 9, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.817, 0.262, 2, 1.433, 0.262, 0, 2.117, 0, 2, 3.033, 0, 0, 3.333, 0.262, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, 0.262, 0, 4.933, 0, 2, 7.867, 0, 0, 8.067, -0.1, 0, 8.5, 0, 2, 9, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.817, 0.366, 2, 1.433, 0.366, 0, 2.117, 0, 2, 3.033, 0, 0, 3.333, 0.366, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, 0.366, 0, 4.933, 0, 2, 7.867, 0, 0, 8.067, 0.3, 0, 8.5, 0, 2, 9, 0]}, {"Target": "Parameter", "Id": "Param11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.817, -0.572, 2, 1.433, -0.572, 0, 2.117, 0, 2, 3.033, 0, 0, 3.333, -0.572, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, -0.572, 0, 4.933, 0, 2, 7.867, 0, 0, 8.067, -0.4, 0, 8.5, 0, 2, 9, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.15, -0.244, 0, 0.333, -0.01, 0, 0.45, -0.319, 2, 0.633, -0.319, 2, 0.817, -0.319, 0, 0.9, 0.656, 0, 0.95, -0.319, 2, 1.133, -0.319, 2, 1.333, -0.319, 2, 1.367, -0.319, 2, 1.533, -0.319, 2, 1.683, -0.319, 0, 1.75, 0.34, 1, 1.817, 0.34, 1.883, 0.257, 1.95, -0.01, 1, 1.989, -0.166, 2.028, -0.319, 2.067, -0.319, 0, 2.283, -0.244, 0, 2.4, -0.319, 0, 2.617, 0.808, 2, 4.133, 0.808, 0, 4.383, 1, 0, 4.467, 0.656, 0, 4.517, 1, 0, 4.7, -0.319, 0, 4.9, 0.224, 2, 5.033, 0.224, 0, 5.2, 0.34, 0, 6.083, -0.244, 2, 6.233, -0.244, 0, 6.417, -0.01, 0, 6.533, -0.319, 2, 6.717, -0.319, 2, 6.9, -0.319, 0, 6.983, 0.656, 0, 7.033, -0.319, 2, 7.217, -0.319, 2, 7.417, -0.319, 2, 7.45, -0.319, 2, 7.617, -0.319, 2, 7.767, -0.319, 0, 7.833, 0.34, 1, 7.9, 0.34, 7.966, 0.257, 8.033, -0.01, 1, 8.072, -0.166, 8.111, -0.319, 8.15, -0.319, 0, 8.367, 1, 0, 9, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 0.05, 0, 0.1, 0.185, 0.15, 0.5, 1, 0.206, 0.85, 0.261, 1, 0.317, 1, 0, 0.5, 0.102, 0, 0.683, 0.969, 0, 0.9, 0.012, 0, 1.1, 0.663, 0, 1.183, 0.024, 0, 1.333, 0.871, 2, 1.367, 0.871, 0, 1.65, 0, 0, 1.933, 1, 0, 2.1, 0.102, 0, 2.283, 0.5, 1, 2.333, 0.5, 2.383, 0.188, 2.433, 0.102, 1, 2.494, 0, 2.556, 0, 2.617, 0, 2, 4.133, 0, 1, 4.222, 0, 4.311, 0.044, 4.4, 0.323, 1, 4.439, 0.445, 4.478, 0.9, 4.517, 0.9, 0, 4.6, 0.016, 0, 4.667, 0.663, 0, 4.75, 0.024, 0, 4.9, 0.871, 2, 5.033, 0.871, 0, 5.2, 0, 0, 6.083, 0.5, 2, 6.233, 0.5, 0, 6.4, 1, 0, 6.583, 0.102, 0, 6.767, 0.969, 0, 6.983, 0.012, 0, 7.183, 0.663, 0, 7.267, 0.024, 0, 7.417, 0.871, 2, 7.45, 0.871, 0, 7.733, 0, 0, 8.017, 1, 1, 8.072, 1, 8.128, 0.21, 8.183, 0.102, 1, 8.244, 0, 8.306, 0, 8.367, 0, 2, 9, 0]}, {"Target": "Parameter", "Id": "Hair_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.262, 0, 0.25, 7.973, 0, 0.75, -12.538, 0, 1.317, 6.762, 0, 1.783, -5.214, 0, 2.267, 14.845, 0, 2.717, -9.987, 0, 3.15, 6.122, 0, 3.533, -9.997, 0, 4.217, 3.752, 0, 4.6, -3.792, 0, 5.5, 6.411, 0, 5.85, -14.485, 0, 6.3, 8.8, 0, 6.8, -8.123, 0, 7.25, 6.131, 0, 7.85, -6.888, 0, 8.3, 2.832, 0, 8.8, -3.653, 0, 9, -0.262]}, {"Target": "Parameter", "Id": "Hair_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.25, 3.354, 0, 0.75, -5, 0, 1.317, 2.861, 0, 1.783, -2.017, 0, 2.267, 6.153, 0, 2.717, -3.961, 0, 3.15, 2.6, 0, 3.533, -3.965, 0, 4.217, 1.635, 0, 4.6, -1.438, 0, 5.5, 2.718, 0, 5.85, -5.793, 0, 6.3, 3.691, 0, 6.8, -3.202, 0, 7.25, 2.604, 0, 7.85, -2.699, 0, 8.3, 1.26, 0, 8.8, -1.381, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param69", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.25, 3.354, 0, 0.75, -8.454, 0, 1.317, 6.288, 0, 1.783, -5.444, 0, 2.267, 7.707, 0, 2.717, -4.903, 0, 3.15, 2.6, 0, 3.533, -3.965, 0, 4.217, 1.635, 0, 4.6, -1.438, 0, 5.5, 2.718, 0, 5.85, -5.793, 0, 6.3, 3.691, 0, 6.8, -3.202, 0, 7.25, 2.604, 0, 7.85, -2.699, 0, 8.3, 4.171, 0, 8.8, -1.381, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param153", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.2, -7.245, 0, 0.567, 13.557, 0, 1.033, -13.477, 0, 1.567, 10.835, 0, 2.067, -14.336, 0, 2.55, 19.567, 0, 2.983, -17.224, 0, 3.417, 17.093, 0, 3.833, -10.896, 0, 4.433, 7.295, 0, 4.883, -5.014, 0, 5.75, 15.395, 0, 6.133, -19.809, 0, 6.583, 15.995, 0, 7.067, -13.559, 0, 7.517, 10.043, 0, 8.1, -8.153, 0, 8.567, 6.36, 0, 8.983, -5.818, 1, 8.989, -5.818, 8.994, -5.804, 9, -5.776]}, {"Target": "Parameter", "Id": "Param154", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.167, 3.519, 0, 0.467, -8.551, 0, 0.867, 11.455, 0, 1.317, -9.908, 0, 1.867, 9.269, 0, 2.383, -12.769, 0, 2.817, 16.835, 0, 3.267, -16.909, 0, 3.667, 16.004, 0, 4.083, -9.254, 0, 4.667, 6.103, 0, 5.1, -3.526, 0, 5.5, 0.458, 0, 5.7, -5.812, 0, 6.017, 14.324, 0, 6.417, -17.071, 0, 6.867, 14.198, 0, 7.317, -12.33, 0, 7.767, 7.932, 0, 8.35, -6.455, 0, 8.867, 5.612, 1, 8.911, 5.612, 8.956, 4.08, 9, 2.184]}, {"Target": "Parameter", "Id": "Param155", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.3, 2.094, 0, 0.65, -6.404, 0, 1.05, 10.507, 0, 1.5, -11.085, 0, 2, 10.781, 0, 2.517, -12.888, 0, 2.983, 16.426, 0, 3.417, -17.588, 0, 3.85, 16.64, 0, 4.283, -11.813, 0, 4.783, 7.507, 0, 5.25, -4.554, 0, 5.617, 0.639, 0, 5.867, -2.766, 0, 6.2, 10.886, 0, 6.6, -15.718, 0, 7.033, 15.6, 0, 7.483, -14.054, 0, 7.95, 10.303, 0, 8.483, -7.433, 0, 8.983, 6.491, 1, 8.989, 6.491, 8.994, 6.473, 9, 6.439]}, {"Target": "Parameter", "Id": "Param156", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.017, 0, 0, 0.45, 1.161, 0, 0.8, -5.012, 0, 1.217, 9.673, 0, 1.667, -11.737, 0, 2.15, 11.98, 0, 2.65, -13.549, 0, 3.133, 16.666, 0, 3.583, -18.344, 0, 4.017, 17.743, 0, 4.467, -14.188, 0, 4.95, 9.887, 0, 5.417, -6.338, 0, 5.817, 1.402, 0, 6.033, -0.504, 0, 6.367, 8.142, 0, 6.767, -14.395, 0, 7.2, 16.318, 0, 7.65, -15.485, 0, 8.117, 12.559, 0, 8.617, -9.298, 1, 8.745, -9.298, 8.872, -0.019, 9, 4.771]}, {"Target": "Parameter", "Id": "Param82", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.2, -6.754, 0, 0.567, 12.639, 0, 1.033, -12.564, 0, 1.567, 10.1, 0, 2.067, -13.365, 0, 2.55, 18.241, 0, 2.983, -16.057, 0, 3.417, 15.935, 0, 3.833, -10.157, 0, 4.433, 6.801, 0, 4.883, -4.674, 0, 5.75, 14.352, 0, 6.133, -18.466, 0, 6.583, 14.912, 0, 7.067, -12.64, 0, 7.517, 9.363, 0, 8.1, -7.601, 0, 8.567, 5.929, 0, 8.983, -5.423, 1, 8.989, -5.423, 8.994, -5.41, 9, -5.385]}, {"Target": "Parameter", "Id": "Param83", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.167, 3.372, 0, 0.467, -8.193, 0, 0.867, 10.975, 0, 1.317, -9.492, 0, 1.867, 8.88, 0, 2.383, -12.233, 0, 2.817, 16.129, 0, 3.267, -16.2, 0, 3.667, 15.333, 0, 4.083, -8.866, 0, 4.667, 5.847, 0, 5.1, -3.378, 0, 5.5, 0.439, 0, 5.7, -5.569, 0, 6.017, 13.723, 0, 6.417, -16.355, 0, 6.867, 13.603, 0, 7.317, -11.813, 0, 7.767, 7.6, 0, 8.35, -6.184, 0, 8.867, 5.377, 1, 8.911, 5.376, 8.956, 3.909, 9, 2.092]}, {"Target": "Parameter", "Id": "Param84", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.3, 2.14, 0, 0.65, -6.546, 0, 1.05, 10.741, 0, 1.5, -11.331, 0, 2, 11.021, 0, 2.517, -13.175, 0, 2.983, 16.791, 0, 3.417, -17.979, 0, 3.85, 17.01, 0, 4.283, -12.076, 0, 4.783, 7.674, 0, 5.25, -4.656, 0, 5.617, 0.653, 0, 5.867, -2.828, 0, 6.2, 11.129, 0, 6.6, -16.068, 0, 7.033, 15.947, 0, 7.483, -14.366, 0, 7.95, 10.532, 0, 8.483, -7.598, 0, 8.983, 6.635, 1, 8.989, 6.635, 8.994, 6.617, 9, 6.582]}, {"Target": "Parameter", "Id": "Param85", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.017, 0, 0, 0.45, 1.183, 0, 0.8, -5.11, 0, 1.217, 9.862, 0, 1.667, -11.967, 0, 2.15, 12.214, 0, 2.65, -13.814, 0, 3.133, 16.992, 0, 3.583, -18.703, 0, 4.017, 18.09, 0, 4.467, -14.466, 0, 4.95, 10.08, 0, 5.417, -6.462, 0, 5.817, 1.43, 0, 6.033, -0.514, 0, 6.367, 8.301, 0, 6.767, -14.677, 0, 7.2, 16.638, 0, 7.65, -15.788, 0, 8.117, 12.804, 0, 8.617, -9.48, 1, 8.745, -9.48, 8.872, -0.019, 9, 4.864]}, {"Target": "Parameter", "Id": "Param66", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.1, 0, 0, 0.283, 7.043, 0, 0.533, -14.704, 0, 0.867, 18.511, 0, 1.267, -14.037, 0, 1.9, 12.887, 0, 2.35, -16.441, 0, 2.8, 18.373, 0, 3.217, -17.397, 0, 3.65, 15.846, 0, 4, -11.217, 0, 4.333, 3.127, 0, 4.567, -0.672, 0, 4.8, 4.545, 0, 5.1, -3.768, 0, 5.417, 1.422, 0, 5.767, -6.72, 0, 6.083, 17.04, 0, 6.417, -20.165, 0, 6.783, 14.855, 0, 7.283, -7.642, 0, 7.717, 5.877, 0, 8.017, 0.53, 0, 8.133, 1.141, 0, 8.467, -7.603, 0, 8.833, 8.605, 1, 8.889, 8.605, 8.944, 5.433, 9, 1.847]}, {"Target": "Parameter", "Id": "Param67", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.167, -11.947, 0, 0.433, 22.908, 0, 0.833, -17.264, 0, 1.467, 17.393, 0, 1.9, -20.53, 0, 2.4, 20.516, 0, 2.8, -19.571, 0, 3.267, 14.03, 0, 3.6, -13.264, 0, 3.883, 2.412, 0, 4.1, -1.552, 0, 4.383, 7.013, 0, 4.683, -6.978, 0, 4.933, 1.061, 0, 5.183, -1.895, 0, 5.7, 17.296, 0, 5.983, -26.016, 0, 6.35, 16.246, 0, 6.967, -8.657, 0, 7.333, 9.079, 0, 7.667, -0.739, 0, 7.733, -0.678, 0, 8.05, -10.421, 0, 8.4, 12.163, 0, 8.9, -7, 1, 8.933, -7, 8.967, -4.612, 9, -1.883]}, {"Target": "Parameter", "Id": "Param68", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.133, 5.013, 0, 0.333, -12.848, 0, 0.6, 16.402, 0, 0.933, -8.663, 0, 1.233, -0.8, 0, 1.4, -2.825, 0, 1.667, 9.624, 0, 2.067, -10.137, 0, 2.583, 9.506, 0, 2.967, -10.502, 0, 3.45, 6.386, 0, 3.767, -8.204, 0, 4.017, 4.406, 0, 4.283, -4.203, 0, 4.567, 5.931, 0, 4.833, -5.428, 0, 5.083, 3.021, 0, 5.35, -2.422, 0, 5.517, -0.272, 0, 5.633, -3.631, 0, 5.883, 14.956, 0, 6.15, -18.059, 0, 6.467, 11.005, 0, 6.75, -3.057, 0, 6.933, 1.51, 0, 7.183, -5.081, 0, 7.483, 5.347, 0, 7.75, -1.701, 0, 7.983, 3.093, 0, 8.25, -7.185, 0, 8.55, 7.556, 0, 8.817, -2.092, 0, 8.917, -1.192, 1, 8.945, -1.192, 8.972, -2.397, 9, -3.3]}, {"Target": "Parameter", "Id": "Param62", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.1, 0, 0, 0.283, -7.043, 0, 0.533, 14.704, 0, 0.867, -18.511, 0, 1.267, 14.037, 0, 1.9, -12.887, 0, 2.35, 16.441, 0, 2.8, -18.373, 0, 3.217, 17.397, 0, 3.65, -15.846, 0, 4, 11.217, 0, 4.333, -3.127, 0, 4.567, 0.672, 0, 4.8, -4.545, 0, 5.1, 3.768, 0, 5.417, -1.422, 0, 5.767, 6.72, 0, 6.083, -17.04, 0, 6.417, 20.165, 0, 6.783, -14.855, 0, 7.283, 7.642, 0, 7.717, -5.877, 0, 8.017, -0.53, 0, 8.133, -1.141, 0, 8.467, 7.603, 0, 8.833, -8.605, 1, 8.889, -8.605, 8.944, -5.433, 9, -1.847]}, {"Target": "Parameter", "Id": "Param63", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.167, 11.947, 0, 0.433, -22.908, 0, 0.833, 17.264, 0, 1.467, -17.393, 0, 1.9, 20.53, 0, 2.4, -20.516, 0, 2.8, 19.571, 0, 3.267, -14.03, 0, 3.6, 13.264, 0, 3.883, -2.412, 0, 4.1, 1.552, 0, 4.383, -7.013, 0, 4.683, 6.978, 0, 4.933, -1.061, 0, 5.183, 1.895, 0, 5.7, -17.296, 0, 5.983, 26.016, 0, 6.35, -16.246, 0, 6.967, 8.657, 0, 7.333, -9.079, 0, 7.667, 0.739, 0, 7.733, 0.678, 0, 8.05, 10.421, 0, 8.4, -12.163, 0, 8.9, 7, 1, 8.933, 7, 8.967, 4.612, 9, 1.883]}, {"Target": "Parameter", "Id": "Param64", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.133, -5.013, 0, 0.333, 12.848, 0, 0.6, -16.402, 0, 0.933, 8.663, 0, 1.233, 0.8, 0, 1.4, 2.825, 0, 1.667, -9.624, 0, 2.067, 10.137, 0, 2.583, -9.506, 0, 2.967, 10.502, 0, 3.45, -6.386, 0, 3.767, 8.204, 0, 4.017, -4.406, 0, 4.283, 4.203, 0, 4.567, -5.931, 0, 4.833, 5.428, 0, 5.083, -3.021, 0, 5.35, 2.422, 0, 5.517, 0.272, 0, 5.633, 3.631, 0, 5.883, -14.956, 0, 6.15, 18.059, 0, 6.467, -11.005, 0, 6.75, 3.057, 0, 6.933, -1.51, 0, 7.183, 5.081, 0, 7.483, -5.347, 0, 7.75, 1.701, 0, 7.983, -3.093, 0, 8.25, 7.185, 0, 8.55, -7.556, 0, 8.817, 2.092, 0, 8.917, 1.192, 1, 8.945, 1.192, 8.972, 2.397, 9, 3.3]}, {"Target": "Parameter", "Id": "Param86", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.25, -1.467, 0, 0.75, 2.187, 0, 1.317, -1.252, 0, 1.783, 0.882, 0, 2.267, -2.692, 0, 2.717, 1.732, 0, 3.15, -1.137, 0, 3.533, 1.734, 0, 4.217, -0.715, 0, 4.6, 0.629, 0, 5.5, -1.189, 0, 5.85, 2.534, 0, 6.3, -1.614, 0, 6.8, 1.4, 0, 7.25, -1.139, 0, 7.85, 1.18, 0, 8.3, -0.551, 0, 8.8, 0.604, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param89", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.2, -9.455, 0, 0.533, 11.883, 0, 0.967, -9.699, 0, 1.533, 7.08, 0, 2.033, -10.436, 0, 2.5, 14.557, 0, 2.917, -12.492, 0, 3.367, 12.714, 0, 3.733, -7.783, 0, 4.433, 4.677, 0, 4.8, -3.364, 0, 5.017, -1.868, 0, 5.033, -1.872, 0, 5.733, 13.416, 0, 6.083, -16.124, 0, 6.517, 11.462, 0, 7.017, -9.016, 0, 7.467, 6.538, 0, 8.083, -5.452, 0, 8.483, 4.524, 0, 8.95, -4.568, 1, 8.967, -4.568, 8.983, -4.394, 9, -4.103]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.183, 7.697, 0, 0.417, -13.522, 0, 0.767, 11.069, 0, 1.15, -6.551, 0, 1.867, 4.65, 0, 2.333, -8.179, 0, 2.75, 11.933, 0, 3.15, -11.217, 0, 3.567, 12.058, 0, 3.917, -6.764, 0, 4.25, 0.744, 0, 4.417, -0.2, 0, 4.667, 3.805, 0, 4.983, -2.265, 0, 5.233, 0.805, 0, 5.667, -6.746, 0, 5.967, 14.73, 0, 6.333, -13.674, 0, 6.7, 9.166, 0, 7.25, -6.475, 0, 7.633, 4.158, 0, 8.333, -4.926, 0, 8.683, 3.406, 0, 8.85, 2.497, 0, 8.867, 2.502, 1, 8.911, 2.502, 8.956, 0.778, 9, -0.945]}, {"Target": "Parameter", "Id": "Param88", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.15, 0, 0, 0.333, -7.614, 0, 0.567, 13.159, 0, 0.883, -13.001, 0, 1.267, 8.752, 0, 1.75, -3.261, 0, 1.8, -3.243, 0, 1.917, -3.393, 0, 2.4, 7.109, 0, 2.833, -10.655, 0, 3.233, 11.007, 0, 3.667, -11.19, 0, 4, 7.995, 0, 4.333, -2.573, 0, 4.567, 0.346, 0, 4.8, -2.711, 0, 5.133, 2.847, 0, 5.417, -1.118, 0, 5.767, 4.98, 0, 6.083, -12.565, 0, 6.417, 14.472, 0, 6.783, -10.548, 0, 7.283, 5.732, 0, 7.717, -4.363, 0, 8.2, 0.605, 0, 8.217, 0.58, 0, 8.45, 3.55, 0, 8.783, -3.449, 1, 8.855, -3.449, 8.928, -1.861, 9, -0.109]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.317, 0, 0, 0.483, 5.777, 0, 0.7, -11.068, 0, 1, 13.25, 0, 1.333, -9.428, 0, 1.733, 4.108, 0, 2.5, -6.138, 0, 2.917, 9.556, 0, 3.333, -10.771, 0, 3.75, 10.704, 0, 4.1, -8.635, 0, 4.117, -8.626, 0, 4.133, -8.768, 0, 4.45, 4.292, 0, 4.717, -0.863, 0, 4.933, 1.959, 0, 5.217, -2.018, 0, 5.533, 1.289, 0, 5.867, -4.037, 0, 6.183, 10.792, 0, 6.517, -14.34, 0, 6.883, 11.853, 0, 7.283, -6.181, 0, 7.8, 4.131, 0, 8.183, -1.146, 0, 8.333, -0.747, 0, 8.567, -2.505, 0, 8.9, 3.584, 1, 8.933, 3.584, 8.967, 3.207, 9, 2.621]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.25, 1.467, 0, 0.75, -2.187, 0, 1.317, 1.252, 0, 1.783, -0.882, 0, 2.267, 2.692, 0, 2.717, -1.732, 0, 3.15, 1.137, 0, 3.533, -1.734, 0, 4.217, 0.715, 0, 4.6, -0.629, 0, 5.5, 1.189, 0, 5.85, -2.534, 0, 6.3, 1.614, 0, 6.8, -1.4, 0, 7.25, 1.139, 0, 7.85, -1.18, 0, 8.3, 0.551, 0, 8.8, -0.604, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.2, -9.455, 0, 0.533, 11.883, 0, 0.967, -9.699, 0, 1.533, 7.08, 0, 2.033, -10.436, 0, 2.5, 14.557, 0, 2.917, -12.492, 0, 3.367, 12.714, 0, 3.733, -7.783, 0, 4.433, 4.677, 0, 4.8, -3.364, 0, 5.017, -1.868, 0, 5.033, -1.872, 0, 5.733, 13.416, 0, 6.083, -16.124, 0, 6.517, 11.462, 0, 7.017, -9.016, 0, 7.467, 6.538, 0, 8.083, -5.452, 0, 8.483, 4.524, 0, 8.95, -4.568, 1, 8.967, -4.568, 8.983, -4.394, 9, -4.103]}, {"Target": "Parameter", "Id": "Param126", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.183, 7.697, 0, 0.417, -13.522, 0, 0.767, 11.069, 0, 1.15, -6.551, 0, 1.867, 4.65, 0, 2.333, -8.179, 0, 2.75, 11.933, 0, 3.15, -11.217, 0, 3.567, 12.058, 0, 3.917, -6.764, 0, 4.25, 0.744, 0, 4.417, -0.2, 0, 4.667, 3.805, 0, 4.983, -2.265, 0, 5.233, 0.805, 0, 5.667, -6.746, 0, 5.967, 14.73, 0, 6.333, -13.674, 0, 6.7, 9.166, 0, 7.25, -6.475, 0, 7.633, 4.158, 0, 8.333, -4.926, 0, 8.683, 3.406, 0, 8.85, 2.497, 0, 8.867, 2.502, 1, 8.911, 2.502, 8.956, 0.778, 9, -0.945]}, {"Target": "Parameter", "Id": "Param128", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.15, 0, 0, 0.333, -7.614, 0, 0.567, 13.159, 0, 0.883, -13.001, 0, 1.267, 8.752, 0, 1.75, -3.261, 0, 1.8, -3.243, 0, 1.917, -3.393, 0, 2.4, 7.109, 0, 2.833, -10.655, 0, 3.233, 11.007, 0, 3.667, -11.19, 0, 4, 7.995, 0, 4.333, -2.573, 0, 4.567, 0.346, 0, 4.8, -2.711, 0, 5.133, 2.847, 0, 5.417, -1.118, 0, 5.767, 4.98, 0, 6.083, -12.565, 0, 6.417, 14.472, 0, 6.783, -10.548, 0, 7.283, 5.732, 0, 7.717, -4.363, 0, 8.2, 0.605, 0, 8.217, 0.58, 0, 8.45, 3.55, 0, 8.783, -3.449, 1, 8.855, -3.449, 8.928, -1.861, 9, -0.109]}, {"Target": "Parameter", "Id": "Param129", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.317, 0, 0, 0.483, 5.777, 0, 0.7, -11.068, 0, 1, 13.25, 0, 1.333, -9.428, 0, 1.733, 4.108, 0, 2.5, -6.138, 0, 2.917, 9.556, 0, 3.333, -10.771, 0, 3.75, 10.704, 0, 4.1, -8.635, 0, 4.117, -8.626, 0, 4.133, -8.768, 0, 4.45, 4.292, 0, 4.717, -0.863, 0, 4.933, 1.959, 0, 5.217, -2.018, 0, 5.533, 1.289, 0, 5.867, -4.037, 0, 6.183, 10.792, 0, 6.517, -14.34, 0, 6.883, 11.853, 0, 7.283, -6.181, 0, 7.8, 4.131, 0, 8.183, -1.146, 0, 8.333, -0.747, 0, 8.567, -2.505, 0, 8.9, 3.584, 1, 8.933, 3.584, 8.967, 3.207, 9, 2.621]}, {"Target": "Parameter", "Id": "Param16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.267, 0.223, 0, 6.6, -0.46, 1, 7.4, -0.46, 8.2, -0.254, 9, -0.109]}, {"Target": "Parameter", "Id": "Param19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 3, 0.15, 6, 0.3, 9, 0.45]}, {"Target": "Parameter", "Id": "Param142", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.812, 2, 0.017, 0.127, 1, 2.561, 0.305, 5.106, 0.482, 7.65, 0.66, 1, 8.1, 0.711, 8.55, 0.761, 9, 0.812]}, {"Target": "Parameter", "Id": "Param143", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.76, 1, 0.422, 0.82, 0.845, 0.88, 1.267, 0.94, 2, 1.283, 0.2, 1, 3.855, 0.387, 6.428, 0.573, 9, 0.76]}, {"Target": "Parameter", "Id": "Param18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 0, 2, 0.35, 1, 2, 0.4, 2, 2, 0.45, 3, 2, 0.5, 4, 2, 0.55, 5, 2, 0.6, 0, 2, 0.65, 1, 2, 0.7, 2, 2, 0.75, 3, 2, 0.8, 4, 2, 0.85, 5, 2, 0.9, 0, 2, 0.95, 1, 2, 1, 2, 2, 1.05, 3, 2, 1.1, 4, 2, 1.15, 5, 2, 1.2, 0, 2, 1.25, 1, 2, 1.3, 2, 2, 1.35, 3, 2, 1.4, 4, 2, 1.45, 5, 2, 1.5, 0, 2, 1.55, 1, 2, 1.6, 2, 2, 1.65, 3, 2, 1.7, 4, 2, 1.75, 5, 2, 1.8, 0, 2, 1.85, 1, 2, 1.9, 2, 2, 1.95, 3, 2, 2, 4, 2, 2.05, 5, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 0, 2, 2.45, 1, 2, 2.5, 2, 2, 2.55, 3, 2, 2.6, 4, 2, 2.65, 5, 2, 2.7, 0, 2, 2.75, 1, 2, 2.8, 2, 2, 2.85, 3, 2, 2.9, 4, 2, 2.95, 5, 2, 3, 0, 2, 3.05, 1, 2, 3.1, 2, 2, 3.15, 3, 2, 3.2, 4, 2, 3.25, 5, 2, 3.3, 0, 2, 3.35, 1, 2, 3.4, 2, 2, 3.45, 3, 2, 3.5, 4, 2, 3.55, 5, 2, 3.6, 0, 2, 3.65, 1, 2, 3.7, 2, 2, 3.75, 3, 2, 3.8, 4, 2, 3.85, 5, 2, 3.9, 0, 2, 3.95, 1, 2, 4, 2, 2, 4.05, 3, 2, 4.1, 4, 2, 4.15, 5, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 0, 2, 4.55, 1, 2, 4.6, 2, 2, 4.65, 3, 2, 4.7, 4, 2, 4.75, 5, 2, 4.8, 0, 2, 4.85, 1, 2, 4.9, 2, 2, 4.95, 3, 2, 5, 4, 2, 5.05, 5, 2, 5.1, 0, 2, 5.15, 1, 2, 5.2, 2, 2, 5.25, 3, 2, 5.3, 4, 2, 5.35, 5, 2, 5.4, 0, 2, 5.45, 1, 2, 5.5, 2, 2, 5.55, 3, 2, 5.6, 4, 2, 5.65, 5, 2, 5.7, 0, 2, 5.75, 1, 2, 5.8, 2, 2, 5.85, 3, 2, 5.9, 4, 2, 5.95, 5, 2, 6, 0, 2, 6.05, 1, 2, 6.1, 2, 2, 6.15, 3, 2, 6.2, 4, 2, 6.25, 5, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 0, 2, 6.65, 1, 2, 6.7, 2, 2, 6.75, 3, 2, 6.8, 4, 2, 6.85, 5, 2, 6.9, 0, 2, 6.95, 1, 2, 7, 2, 2, 7.05, 3, 2, 7.1, 4, 2, 7.15, 5, 2, 7.2, 0, 2, 7.25, 1, 2, 7.3, 2, 2, 7.35, 3, 2, 7.4, 4, 2, 7.45, 5, 2, 7.5, 0, 2, 7.55, 1, 2, 7.6, 2, 2, 7.65, 3, 2, 7.7, 4, 2, 7.75, 5, 2, 7.8, 0, 2, 7.85, 1, 2, 7.9, 2, 2, 7.95, 3, 2, 8, 4, 2, 8.05, 5, 2, 8.1, 0, 2, 8.15, 1, 2, 8.2, 2, 2, 8.25, 3, 2, 8.3, 4, 2, 8.35, 5, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 0, 2, 8.75, 1, 2, 8.8, 2, 2, 8.85, 3, 2, 8.9, 4, 2, 8.95, 5, 2, 9, 0]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 6, 2, 0.35, 0, 2, 0.4, 1, 2, 0.45, 2, 2, 0.5, 3, 2, 0.55, 4, 2, 0.6, 5, 2, 0.65, 6, 2, 0.7, 0, 2, 0.75, 1, 2, 0.8, 2, 2, 0.85, 3, 2, 0.9, 4, 2, 0.95, 5, 2, 1, 6, 2, 1.05, 0, 2, 1.1, 1, 2, 1.15, 2, 2, 1.2, 3, 2, 1.25, 4, 2, 1.3, 5, 2, 1.35, 6, 2, 1.4, 0, 2, 1.45, 1, 2, 1.5, 2, 2, 1.55, 3, 2, 1.6, 4, 2, 1.65, 5, 2, 1.7, 6, 2, 1.75, 0, 2, 1.8, 1, 2, 1.85, 2, 2, 1.9, 3, 2, 1.95, 4, 2, 2, 5, 2, 2.05, 6, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 6, 2, 2.45, 0, 2, 2.5, 1, 2, 2.55, 2, 2, 2.6, 3, 2, 2.65, 4, 2, 2.7, 5, 2, 2.75, 6, 2, 2.8, 0, 2, 2.85, 1, 2, 2.9, 2, 2, 2.95, 3, 2, 3, 4, 2, 3.05, 5, 2, 3.1, 6, 2, 3.15, 0, 2, 3.2, 1, 2, 3.25, 2, 2, 3.3, 3, 2, 3.35, 4, 2, 3.4, 5, 2, 3.45, 6, 2, 3.5, 0, 2, 3.55, 1, 2, 3.6, 2, 2, 3.65, 3, 2, 3.7, 4, 2, 3.75, 5, 2, 3.8, 6, 2, 3.85, 0, 2, 3.9, 1, 2, 3.95, 2, 2, 4, 3, 2, 4.05, 4, 2, 4.1, 5, 2, 4.15, 6, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 6, 2, 4.55, 0, 2, 4.6, 1, 2, 4.65, 2, 2, 4.7, 3, 2, 4.75, 4, 2, 4.8, 5, 2, 4.85, 6, 2, 4.9, 0, 2, 4.95, 1, 2, 5, 2, 2, 5.05, 3, 2, 5.1, 4, 2, 5.15, 5, 2, 5.2, 6, 2, 5.25, 0, 2, 5.3, 1, 2, 5.35, 2, 2, 5.4, 3, 2, 5.45, 4, 2, 5.5, 5, 2, 5.55, 6, 2, 5.6, 0, 2, 5.65, 1, 2, 5.7, 2, 2, 5.75, 3, 2, 5.8, 4, 2, 5.85, 5, 2, 5.9, 6, 2, 5.95, 0, 2, 6, 1, 2, 6.05, 2, 2, 6.1, 3, 2, 6.15, 4, 2, 6.2, 5, 2, 6.25, 6, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 6, 2, 6.65, 0, 2, 6.7, 1, 2, 6.75, 2, 2, 6.8, 3, 2, 6.85, 4, 2, 6.9, 5, 2, 6.95, 6, 2, 7, 0, 2, 7.05, 1, 2, 7.1, 2, 2, 7.15, 3, 2, 7.2, 4, 2, 7.25, 5, 2, 7.3, 6, 2, 7.35, 0, 2, 7.4, 1, 2, 7.45, 2, 2, 7.5, 3, 2, 7.55, 4, 2, 7.6, 5, 2, 7.65, 6, 2, 7.7, 0, 2, 7.75, 1, 2, 7.8, 2, 2, 7.85, 3, 2, 7.9, 4, 2, 7.95, 5, 2, 8, 6, 2, 8.05, 0, 2, 8.1, 1, 2, 8.15, 2, 2, 8.2, 3, 2, 8.25, 4, 2, 8.3, 5, 2, 8.35, 6, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 6, 2, 8.75, 0, 2, 8.8, 1, 2, 8.85, 2, 2, 8.9, 3, 2, 8.95, 4, 2, 9, 5]}, {"Target": "Parameter", "Id": "Param51", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 6, 2, 0.35, 0, 2, 0.4, 1, 2, 0.45, 2, 2, 0.5, 3, 2, 0.55, 4, 2, 0.6, 5, 2, 0.65, 6, 2, 0.7, 0, 2, 0.75, 1, 2, 0.8, 2, 2, 0.85, 3, 2, 0.9, 4, 2, 0.95, 5, 2, 1, 6, 2, 1.05, 0, 2, 1.1, 1, 2, 1.15, 2, 2, 1.2, 3, 2, 1.25, 4, 2, 1.3, 5, 2, 1.35, 6, 2, 1.4, 0, 2, 1.45, 1, 2, 1.5, 2, 2, 1.55, 3, 2, 1.6, 4, 2, 1.65, 5, 2, 1.7, 6, 2, 1.75, 0, 2, 1.8, 1, 2, 1.85, 2, 2, 1.9, 3, 2, 1.95, 4, 2, 2, 5, 2, 2.05, 6, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 6, 2, 2.45, 0, 2, 2.5, 1, 2, 2.55, 2, 2, 2.6, 3, 2, 2.65, 4, 2, 2.7, 5, 2, 2.75, 6, 2, 2.8, 0, 2, 2.85, 1, 2, 2.9, 2, 2, 2.95, 3, 2, 3, 4, 2, 3.05, 5, 2, 3.1, 6, 2, 3.15, 0, 2, 3.2, 1, 2, 3.25, 2, 2, 3.3, 3, 2, 3.35, 4, 2, 3.4, 5, 2, 3.45, 6, 2, 3.5, 0, 2, 3.55, 1, 2, 3.6, 2, 2, 3.65, 3, 2, 3.7, 4, 2, 3.75, 5, 2, 3.8, 6, 2, 3.85, 0, 2, 3.9, 1, 2, 3.95, 2, 2, 4, 3, 2, 4.05, 4, 2, 4.1, 5, 2, 4.15, 6, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 6, 2, 4.55, 0, 2, 4.6, 1, 2, 4.65, 2, 2, 4.7, 3, 2, 4.75, 4, 2, 4.8, 5, 2, 4.85, 6, 2, 4.9, 0, 2, 4.95, 1, 2, 5, 2, 2, 5.05, 3, 2, 5.1, 4, 2, 5.15, 5, 2, 5.2, 6, 2, 5.25, 0, 2, 5.3, 1, 2, 5.35, 2, 2, 5.4, 3, 2, 5.45, 4, 2, 5.5, 5, 2, 5.55, 6, 2, 5.6, 0, 2, 5.65, 1, 2, 5.7, 2, 2, 5.75, 3, 2, 5.8, 4, 2, 5.85, 5, 2, 5.9, 6, 2, 5.95, 0, 2, 6, 1, 2, 6.05, 2, 2, 6.1, 3, 2, 6.15, 4, 2, 6.2, 5, 2, 6.25, 6, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 6, 2, 6.65, 0, 2, 6.7, 1, 2, 6.75, 2, 2, 6.8, 3, 2, 6.85, 4, 2, 6.9, 5, 2, 6.95, 6, 2, 7, 0, 2, 7.05, 1, 2, 7.1, 2, 2, 7.15, 3, 2, 7.2, 4, 2, 7.25, 5, 2, 7.3, 6, 2, 7.35, 0, 2, 7.4, 1, 2, 7.45, 2, 2, 7.5, 3, 2, 7.55, 4, 2, 7.6, 5, 2, 7.65, 6, 2, 7.7, 0, 2, 7.75, 1, 2, 7.8, 2, 2, 7.85, 3, 2, 7.9, 4, 2, 7.95, 5, 2, 8, 6, 2, 8.05, 0, 2, 8.1, 1, 2, 8.15, 2, 2, 8.2, 3, 2, 8.25, 4, 2, 8.3, 5, 2, 8.35, 6, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 6, 2, 8.75, 0, 2, 8.8, 1, 2, 8.85, 2, 2, 8.9, 3, 2, 8.95, 4, 2, 9, 5]}, {"Target": "Parameter", "Id": "Param42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 3, 0.033, 6, 0.067, 9, 0.1]}, {"Target": "Parameter", "Id": "Param45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.4, 1, 3, -0.367, 6, -0.333, 9, -0.3]}, {"Target": "Parameter", "Id": "Param46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.3, 1, 3, 0.333, 6, 0.367, 9, 0.4]}, {"Target": "Parameter", "Id": "Param50", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 3, 0.6, 6, 1.2, 9, 1.8]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2.5, 10, 0, 5.017, -10, 0, 7.5, 10, 1, 8, 10, 8.5, 2.8, 9, -2.96]}, {"Target": "Parameter", "Id": "Param17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.76, 0, 2.5, 0.66, 0, 5.017, -5.76, 0, 7.5, 0.66, 1, 8, 0.66, 8.5, -1.651, 9, -3.5]}, {"Target": "Parameter", "Id": "Param49", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.35, 0, 1.25, 0, 0, 3.75, 0.7, 0, 6.267, 0, 0, 8.767, 0.7, 1, 8.845, 0.7, 8.922, 0.694, 9, 0.683]}, {"Target": "Parameter", "Id": "Param40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.25, 0, 0.917, 10, 0, 2.917, -10, 0, 4.917, 10, 0, 6.917, -10, 0, 8.917, 10, 1, 8.945, 10, 8.972, 9.965, 9, 9.899]}, {"Target": "Parameter", "Id": "Param41", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2, 10, 0, 4, -10, 0, 6, 10, 0, 8, -10, 1, 8.333, -10, 8.667, -5, 9, 0]}, {"Target": "Parameter", "Id": "Param32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2, 10, 0, 4, -10, 0, 6, 10, 0, 8, -10, 1, 8.333, -10, 8.667, -5, 9, 0]}, {"Target": "Parameter", "Id": "Param33", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.886, 0, 0.583, 10, 0, 2.583, -10, 0, 4.583, 10, 0, 6.583, -10, 0, 8.583, 10, 1, 8.722, 10, 8.861, 9.132, 9, 7.758]}, {"Target": "Parameter", "Id": "Param34", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -14.451, 0, 1.333, 30, 0, 3.333, -30, 0, 5.333, 30, 0, 7.333, -30, 1, 7.889, -30, 8.444, 11.667, 9, 25.556]}, {"Target": "Parameter", "Id": "Param28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -15, 0, 2, 15, 0, 4, -15, 0, 6, 15, 0, 8, -15, 1, 8.333, -15, 8.667, -7.5, 9, 0]}, {"Target": "Parameter", "Id": "Param29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.886, 0, 0.583, 10, 0, 2.583, -10, 0, 4.583, 10, 0, 6.583, -10, 0, 8.583, 10, 1, 8.722, 10, 8.861, 9.132, 9, 7.758]}, {"Target": "Parameter", "Id": "Param35", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -14.451, 0, 1.333, 30, 0, 3.333, -30, 0, 5.333, 30, 0, 7.333, -30, 1, 7.889, -30, 8.444, 11.667, 9, 25.556]}, {"Target": "Parameter", "Id": "Param30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.959, 0, 0.8, 10, 0, 2.8, -10, 0, 4.8, 10, 0, 6.8, -10, 0, 8.8, 10, 1, 8.867, 10, 8.933, 9.8, 9, 9.44]}, {"Target": "Parameter", "Id": "Param31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 9.44, 0, 1.8, -10, 0, 3.8, 10, 0, 5.8, -10, 0, 7.8, 10, 1, 8.2, 10, 8.6, 2.8, 9, -2.96]}, {"Target": "Parameter", "Id": "Param36", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 13.773, 0, 0.683, 30, 0, 2.683, -30, 0, 4.683, 30, 0, 6.683, -30, 0, 8.683, 30, 1, 8.789, 30, 8.894, 28.496, 9, 25.964]}, {"Target": "Parameter", "Id": "GL1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 3, 0.134, 6, 0.267, 9, 0.401]}, {"Target": "Parameter", "Id": "GL2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.39, 1, 3, 0.523, 6, 0.657, 9, 0.79]}, {"Target": "Parameter", "Id": "GL3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.72, 1, 3, 0.854, 6, 0.987, 9, 1.121]}, {"Target": "Parameter", "Id": "GL4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.21, 1, 3, 0.344, 6, 0.477, 9, 0.611]}, {"Target": "Parameter", "Id": "GL5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.53, 1, 3, 0.663, 6, 0.797, 9, 0.93]}, {"Target": "Parameter", "Id": "GL6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.89, 1, 3, 1.023, 6, 1.157, 9, 1.29]}, {"Target": "Parameter", "Id": "GL7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.77, 1, 3, 0.904, 6, 1.037, 9, 1.171]}, {"Target": "Parameter", "Id": "GL8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.09, 1, 3, 0.224, 6, 0.357, 9, 0.491]}, {"Target": "Parameter", "Id": "GL9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.301, 1, 3, 0.435, 6, 0.568, 9, 0.702]}, {"Target": "Parameter", "Id": "GL10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.45, 1, 3, 0.584, 6, 0.717, 9, 0.851]}, {"Target": "Parameter", "Id": "GL11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.64, 1, 3, 0.773, 6, 0.907, 9, 1.04]}, {"Target": "Parameter", "Id": "GL12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.15, 1, 3, 0.283, 6, 0.417, 9, 0.55]}, {"Target": "Parameter", "Id": "GL13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.585, 1, 3, 0.719, 6, 0.852, 9, 0.986]}, {"Target": "Parameter", "Id": "GL14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 3, 0.134, 6, 0.267, 9, 0.401]}, {"Target": "Parameter", "Id": "Param161", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.51, 1, 3, 0.677, 6, 0.843, 9, 1.01]}, {"Target": "Parameter", "Id": "Param162", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.31, 1, 3, 0.477, 6, 0.643, 9, 0.81]}, {"Target": "Parameter", "Id": "Param163", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.72, 1, 3, 0.887, 6, 1.053, 9, 1.22]}, {"Target": "Parameter", "Id": "Param164", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 3, 0.167, 6, 0.333, 9, 0.5]}, {"Target": "Parameter", "Id": "Param165", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.17, 1, 3, 0.337, 6, 0.503, 9, 0.67]}, {"Target": "Parameter", "Id": "touch_drag21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "man_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "man_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "man_<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "man_<PERSON>ze", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param189", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "shangguang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "MB_xingfengTMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param127", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Mouthfunnel", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param293", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param167", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param169", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param196", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param59", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10, 0, 9, 10]}, {"Target": "Parameter", "Id": "Param44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param57", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param124", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param38", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param60", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param47", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param125", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param171", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param166", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param123", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 9, 1]}, {"Target": "Parameter", "Id": "Param13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param39", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param173", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param174", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param175", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param176", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param190", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param191", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param192", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param193", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param65", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 9, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "ParamShoulderStretch3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "ParamShoulderStretch4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "ParamChestZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "ParamWaistZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "ParamButtZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param87", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param94", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param52", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param70", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param208", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "Param27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "touch_drag23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "touch_drag24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "touch_drag25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "touch_drag26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "touch_drag27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "touch_drag28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "touch_drag29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "touch_drag30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "touch_drag31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "touch_drag32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "empty1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "empty2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "empty3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "empty4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "empty5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "empty6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "empty7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "empty8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "empty9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "empty10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "empty11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "empty12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "empty13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "empty14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "empty15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "empty16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "empty17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "empty18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "empty19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "empty20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "empty21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "empty22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "Parameter", "Id": "empty23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9, 0]}, {"Target": "PartOpacity", "Id": "Part7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 9, 1]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 8.5, "Value": ""}]}