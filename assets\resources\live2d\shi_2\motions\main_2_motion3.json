{"Version": 3, "Meta": {"Duration": 11.0, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 205, "TotalSegmentCount": 2599, "TotalPointCount": 2917, "UserDataCount": 2, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.95, -0.24, 1, 1.311, -0.24, 1.672, -0.242, 2.033, -0.23, 1, 2.361, -0.219, 2.689, -0.061, 3.017, -0.056, 1, 5.678, -0.014, 8.339, 0, 11, 0]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.95, 0, 2, 2.033, 0, 0, 3.017, 0.127, 0, 6.8, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "All_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.45, -0.272, 0, 0.95, 0.447, 0, 2.45, -0.506, 0, 2.917, 0.53, 0, 3.733, -0.332, 0, 4.383, -0.149, 0, 4.733, -0.241, 0, 6.067, 0.285, 0, 6.8, -0.401, 0, 7.133, -0.038, 0, 7.667, -0.316, 0, 8.517, 0.145, 0, 9.317, -0.092, 0, 10.117, 0.176, 0, 10.667, -0.053, 0, 11, 0]}, {"Target": "Parameter", "Id": "All_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.367, -0.123, 0, 0.733, 0.229, 0, 1.133, -0.219, 0, 1.817, 0.401, 0, 2.267, 0.321, 0, 2.75, 0.507, 0, 3.4, -0.265, 0, 4.017, 0.158, 0, 4.683, -0.044, 0, 5.083, 0.194, 0, 5.383, 0.103, 0, 5.8, 0.285, 0, 6.35, 0.146, 0, 6.767, 0.532, 0, 7.133, 0.006, 0, 7.633, 0.294, 0, 8.517, -0.271, 0, 9.1, 0.179, 0, 9.683, 0.011, 0, 10.483, 0.458, 0, 11, 0]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.95, 0.52, 0, 2.033, 0.5, 1, 2.361, 0.5, 2.689, 0.989, 3.017, 1.062, 1, 3.767, 1.228, 4.517, 1.246, 5.267, 1.246, 1, 5.778, 1.246, 6.289, 0.396, 6.8, 0.3, 1, 8.2, 0.038, 9.6, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.833, 3.218, 0, 2.85, -3.23, 0, 4.183, -2.863, 0, 5.683, -5.465, 1, 6.055, -5.465, 6.428, 5.679, 6.8, 8, 1, 7.761, 13.993, 8.722, 15, 9.683, 15, 0, 11, 0]}, {"Target": "Parameter", "Id": "ParamAngleX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.304, 0, 0.317, -7.891, 0, 0.817, 14.216, 0, 2.317, -15.09, 0, 2.783, 16.76, 0, 3.6, -9.741, 0, 4.25, -4.125, 0, 4.6, -6.949, 0, 5.933, 9.223, 0, 6.667, -11.857, 0, 7, -0.706, 0, 7.533, -9.248, 0, 8.383, 4.942, 0, 10.217, -13.869, 0, 11, -1.425]}, {"Target": "Parameter", "Id": "ParamAngleY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.124, 0, 0.233, -3.335, 0, 0.6, 7.514, 0, 1, -6.278, 0, 1.683, 12.795, 0, 2.133, 10.351, 0, 2.617, 16.072, 0, 3.267, -7.69, 0, 3.883, 5.33, 0, 4.55, -0.878, 0, 4.95, 6.44, 0, 5.25, 3.634, 0, 5.667, 9.232, 0, 6.217, 4.949, 0, 6.633, 16.833, 0, 7, 0.64, 0, 7.5, 9.506, 0, 8.383, -7.879, 0, 8.967, 5.989, 0, 9.55, 0.787, 0, 10.35, 19.754, 0, 11, -0.298]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.543, 0, 0.3, -3.35, 0, 0.7, -1.899, 0, 1.083, -5.918, 1, 1.3, -5.918, 1.516, -3.664, 1.733, -3.342, 1, 1.972, -2.987, 2.211, -3.007, 2.45, -2.728, 1, 2.539, -2.624, 2.628, 6, 2.717, 6, 1, 2.861, 6, 3.006, -4.235, 3.15, -7, 1, 3.272, -9.34, 3.395, -9, 3.517, -9, 0, 3.967, -4.851, 0, 4.433, -6.136, 0, 4.817, -4.097, 0, 5.283, -4.655, 0, 5.667, 0, 0, 6.083, -3, 0, 6.733, 2, 0, 7.067, -3.978, 0, 7.75, -2.167, 0, 8.4, -3.978, 0, 9.167, -1, 0, 9.817, -7, 0, 10.483, 2, 0, 11, 0.543]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -4.357, 0, 0.517, -0.087, 0, 0.9, -10.506, 1, 1.117, -10.506, 1.333, 3.398, 1.55, 4.273, 1, 1.789, 5.237, 2.028, 5.164, 2.267, 5.867, 1, 2.356, 6.129, 2.444, 9, 2.533, 9, 1, 2.678, 9, 2.822, -6.595, 2.967, -9.735, 1, 3.089, -12.392, 3.211, -11.942, 3.333, -11.942, 0, 3.783, -7.739, 0, 4.383, -9.438, 0, 4.8, -8.239, 0, 5.1, -9, 0, 5.483, -3.848, 0, 5.9, -7, 0, 6.567, -0.775, 0, 6.933, -6.276, 0, 7.617, -4.465, 0, 8.267, -6.276, 0, 8.867, -3.848, 0, 9.517, -8, 0, 10.333, -0.775, 0, 11, -4.357]}, {"Target": "Parameter", "Id": "ParamBodyAngleX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -8.417, 0, 0.183, -13, 0, 0.633, 8.898, 1, 0.822, 8.898, 1.011, -2.416, 1.2, -3.294, 1, 1.522, -4.791, 1.845, -4.877, 2.167, -6.258, 1, 2.306, -6.853, 2.444, -22.203, 2.583, -22.203, 0, 2.95, 28.811, 0, 3.533, 0.722, 0, 4.083, 3.784, 1, 4.344, 3.784, 4.606, 3.695, 4.867, 2.496, 1, 5, 1.884, 5.134, -1.72, 5.267, -1.72, 0, 5.733, 6.328, 0, 6.45, -7.606, 1, 6.567, -7.606, 6.683, -0.218, 6.8, 1.013, 1, 7.006, 3.183, 7.211, 3.287, 7.417, 3.287, 0, 8.15, -18.19, 1, 8.45, -18.19, 8.75, -11.212, 9.05, 2, 1, 9.211, 9.095, 9.372, 12.292, 9.533, 12.292, 0, 10.283, -18.956, 0, 11, -8.417]}, {"Target": "Parameter", "Id": "ParamBodyAngleY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -9.558, 0, 0.417, 8.992, 0, 0.9, -9, 0, 1.483, -0.032, 0, 1.933, -3.696, 0, 2.433, 4.939, 1, 2.583, 4.939, 2.733, -6.69, 2.883, -18.303, 1, 2.983, -26.045, 3.083, -26.947, 3.183, -26.947, 0, 3.7, 1.602, 0, 4.383, -7.125, 0, 4.767, 0.512, 0, 5.083, -4.082, 0, 5.533, 6, 0, 5.75, 2, 1, 5.844, 2, 5.939, 6.159, 6.033, 9, 1, 6.172, 13.178, 6.311, 14, 6.45, 14, 0, 6.867, -2, 0, 7.733, 16.043, 1, 8.111, 16.043, 8.489, 11.139, 8.867, 4, 1, 9.078, 0.011, 9.289, -1, 9.5, -1, 0, 10.15, 14, 0, 11, -9.558]}, {"Target": "Parameter", "Id": "ParamWaistZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -13.805, 0, 0.2, -15.74, 0, 0.683, -2.856, 0, 1.317, -16.65, 0, 1.617, -15.519, 1, 1.656, -15.519, 1.694, -15.441, 1.733, -15.586, 1, 1.872, -16.103, 2.011, -17.675, 2.15, -17.675, 0, 2.717, -6.257, 0, 3.083, -15.102, 0, 3.6, -14.028, 0, 3.9, -15.383, 0, 4.433, -14.989, 0, 5.133, -19.442, 0, 5.733, -11.353, 0, 6.333, -17.178, 0, 6.617, -16.294, 0, 6.75, -16.673, 0, 7.7, -11.049, 0, 8.133, -14.881, 0, 8.75, -11.87, 0, 9.067, -12.484, 0, 9.517, -10.227, 0, 10.117, -20.392, 0, 11, -13.518]}, {"Target": "Parameter", "Id": "ParamShoulderStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.893, 0, 0.283, -0.235, 0, 0.783, -9.529, 0, 1.35, 1.943, 0, 1.8, 1.239, 0, 2.3, 2.897, 1, 2.45, 2.897, 2.6, -5.111, 2.75, -6.639, 1, 2.85, -7.658, 2.95, -7.365, 3.05, -7.365, 0, 3.567, -2.298, 0, 4.95, -4.164, 0, 5.4, -3.573, 0, 5.9, -9.971, 0, 6.333, 2.28, 0, 6.917, -6.765, 0, 7.917, -5.027, 0, 9.267, -8.35, 0, 10.117, -1.991, 0, 11, -2.893]}, {"Target": "Parameter", "Id": "ParamShoulderStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.371, 0, 0.483, -0.235, 0, 0.983, -9.529, 0, 1.55, 1.943, 0, 2, 1.239, 0, 2.5, 2.897, 1, 2.65, 2.897, 2.8, -5.111, 2.95, -6.639, 1, 3.05, -7.658, 3.15, -7.365, 3.25, -7.365, 0, 3.767, -2.298, 1, 4.122, -2.298, 4.478, -2.396, 4.833, -2.881, 1, 4.939, -3.025, 5.044, -4.164, 5.15, -4.164, 0, 5.6, -3.573, 0, 6.1, -10.317, 0, 6.533, 0.605, 0, 7.017, -7.306, 0, 8.017, -5.568, 0, 9.45, -9.006, 0, 10.283, -2.187, 0, 11, -3.371]}, {"Target": "Parameter", "Id": "ParamButtXA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -6.39, 0, 0.183, -5.227, 0, 0.617, -13, 0, 1.183, -1.63, 0, 1.667, -2.332, 0, 2.15, -0.243, 0, 2.567, -8.371, 0, 2.917, -0.944, 0, 3.4, -4, 0, 4.067, -1, 0, 4.5, -2, 0, 5.417, 2.065, 0, 5.75, -3, 0, 6.15, 4, 0, 6.7, -1.539, 0, 7.033, -0.215, 0, 7.683, -6.778, 0, 8.2, -0.215, 0, 8.7, -3.181, 0, 9.05, -1.677, 0, 9.517, -7, 0, 10.017, 5.304, 0, 10.65, -8.518, 0, 11, -6.39]}, {"Target": "Parameter", "Id": "Param43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.915, 0, 0.25, -0.088, 0, 0.75, -10, 0, 1.317, 2.265, 0, 1.783, 1.26, 0, 2.267, 2.726, 0, 2.717, -7, 0, 3.017, -2, 0, 3.533, -6, 0, 4.217, -3.029, 0, 4.6, -5, 0, 5.5, -0.499, 0, 5.85, -4, 0, 6.3, 0.13, 0, 6.8, -1.253, 0, 7.133, 0.087, 0, 7.783, -6.476, 0, 8.35, -2.127, 0, 8.85, -5.093, 0, 9.2, -3.589, 0, 9.65, -9, 0, 10.183, 7, 0, 10.767, -3.148, 0, 11, -1.915]}, {"Target": "Parameter", "Id": "ParamButtZA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.239, 0, 0.45, -7, 0, 0.917, -1.802, 0, 1.467, -2.377, 0, 1.917, -0.482, 0, 2.35, -2, 0, 2.833, 5, 1, 2.883, 5, 2.933, 4, 2.983, 2, 1, 3.033, 0, 3.083, -1, 3.133, -1, 0, 4.333, 1.404, 0, 4.683, -0.029, 0, 5.017, 0.42, 0, 5.483, -0.478, 0, 5.983, 0, 0, 6.467, -5, 0, 6.917, -4.449, 0, 7.3, -6.913, 0, 7.917, 0.621, 0, 8.383, -5.695, 0, 9.633, 4, 0, 10.367, -13, 0, 11, -5.239]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.65, 15, 2, 2.617, 15, 2, 3.5, 15, 2, 6.483, 15, 2, 6.5, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "Param58", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.65, 15, 2, 2.617, 15, 2, 3.5, 15, 2, 5.533, 15, 0, 6.433, 0, 0, 7.117, 15, 2, 9.183, 15, 0, 11, 0]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 24.978, 2, 3.017, 24.978, 0, 3.5, -13.56, 2, 5.883, -13.56, 0, 7.117, 0, 2, 9.183, 0, 0, 9.833, 30, 0, 10.483, 24.978, 2, 11, 24.978]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -9.422, 2, 2.617, -9.422, 0, 3.5, -7.5, 2, 5.883, -7.5, 0, 7.117, 0, 2, 9.183, 0, 0, 9.833, -12, 0, 10.483, -9.422, 2, 11, -9.422]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.038, 0, 0.683, 0.575, 0, 1.333, 1, 0, 2.617, 0.28, 0, 3.017, 5, 1, 3.178, 5, 3.339, -0.923, 3.5, -1.095, 1, 4.011, -1.641, 4.522, -1.725, 5.033, -1.725, 1, 5.161, -1.725, 5.289, -1.774, 5.417, -1.095, 1, 5.572, -0.269, 5.728, 1.425, 5.883, 1.425, 0, 6.517, -2.812, 0, 7, -1.815, 0, 7.867, -3, 1, 8.306, -3, 8.744, -2.959, 9.183, -1.815, 1, 9.4, -1.25, 9.616, 8, 9.833, 8, 0, 10.483, 0.226, 0, 11, 2.038]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.871, 0, 0.683, 5.551, 1, 0.9, 5.551, 1.116, 2.705, 1.333, 1.8, 1, 1.589, 0.733, 1.844, 0.76, 2.1, 0.76, 0, 2.617, 3.78, 0, 3.017, -1, 1, 3.178, -1, 3.339, 9.054, 3.5, 9.3, 1, 4.011, 10.081, 4.522, 10.2, 5.033, 10.2, 1, 5.2, 10.2, 5.366, 10.194, 5.533, 9.3, 1, 5.689, 8.466, 5.844, 6.66, 6, 6.66, 0, 6.633, 24.634, 1, 6.794, 24.634, 6.956, 20.602, 7.117, 20.04, 1, 7.406, 19.033, 7.694, 19, 7.983, 19, 0, 9.183, 20, 2, 9.217, -40, 1, 9.422, -40, 9.628, -13.633, 9.833, -5.405, 1, 10.05, 3.267, 10.266, 3.091, 10.483, 3.091, 0, 11, 0.871]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.131, 0, 0.683, 8.68, 1, 0.9, 8.68, 1.116, -3.704, 1.333, -10, 1, 1.589, -17.426, 1.844, -18, 2.1, -18, 0, 2.617, -13, 0, 3.017, -23, 1, 3.178, -23, 3.339, 14.267, 3.5, 14.76, 1, 4.011, 16.323, 4.522, 16.56, 5.033, 16.56, 1, 5.239, 16.56, 5.444, 16.688, 5.65, 14.76, 1, 5.806, 13.301, 5.961, 1.92, 6.117, 1.92, 0, 6.75, 14.486, 1, 6.911, 14.486, 7.072, 12.025, 7.233, 11.34, 1, 7.522, 10.112, 7.811, 10, 8.1, 10, 0, 9.183, 11.34, 0, 9.833, -19.925, 0, 10.633, 6.041, 0, 11, 0.131]}, {"Target": "Parameter", "Id": "Param5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.65, 0, 2, 2.617, 0, 2, 3.5, 0, 2, 6.433, 0, 0, 7.117, -19, 2, 9.183, -19, 1, 9.461, -19, 9.739, -19.428, 10.017, -16, 1, 10.345, -11.955, 10.672, 0, 11, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.817, 0, 0, 1.35, -30, 2, 2.617, -30, 0, 3.5, 0, 2, 6.433, 0, 0, 7.117, 10, 2, 9.183, 10, 1, 9.339, 10, 9.494, 7.427, 9.65, 0, 1, 9.739, -4.244, 9.828, -8.033, 9.917, -8.033, 0, 10.283, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.817, 0, 0, 1.35, 30, 2, 2.617, 30, 0, 3.5, 0, 2, 6.433, 0, 0, 7.117, -30, 2, 9.183, -30, 2, 9.65, -30, 0, 9.917, 0, 0, 10.283, -30, 0, 11, 0]}, {"Target": "Parameter", "Id": "ParamHandRREDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1, 6, 2, 2.617, 6, 2, 3.283, 2, 2, 6.35, 2, 2, 6.367, 3, 2, 9.633, 3, 2, 9.65, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "Param101", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.35, 0, 0, 4.033, 1, 2, 4.983, 1, 0, 5.983, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "Param55", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.65, 18, 2, 2.617, 18, 0, 3.5, 16, 2, 5.433, 16, 2, 5.9, 16, 0, 6.433, 0, 2, 9.183, 0, 2, 9.833, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.633, 0, 2, 0.65, 10, 2, 3.017, 10, 2, 3.033, 30, 0, 3.5, 28.461, 2, 6.033, 28.461, 2, 6.05, 0, 2, 6.433, 0, 2, 9.183, 0, 2, 10.583, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.65, 0, 2, 2.617, 0, 2, 3.5, 0, 2, 5.433, 0, 2, 6.433, 0, 2, 9.183, 0, 0, 9.55, 30, 0, 10.3, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.067, 8, 2, 2.617, 8, 0, 3.5, 14.22, 2, 5.433, 14.22, 0, 6.433, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.067, 6, 2, 2.617, 6, 0, 3.5, -14.28, 2, 5.433, -14.28, 0, 6.433, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.089, 0, 0.533, 1.731, 0, 0.833, -0.105, 2, 2.5, -0.105, 0, 2.833, 3.547, 0, 3.383, -0.395, 2, 3.5, -0.395, 0, 5.033, 0.27, 0, 5.433, -1, 0, 5.9, 2.923, 0, 6.433, -1.575, 2, 9.183, -1.575, 0, 9.933, 3, 0, 10.567, -1.019, 0, 11, -0.089]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.769, 0, 0.433, 4, 0, 0.95, -2.22, 2, 2.617, -2.22, 0, 2.95, 2, 0, 3.5, -7.98, 0, 5.033, -7.5, 0, 5.433, -9, 0, 6.15, 3.408, 0, 6.683, -0.723, 0, 7.95, 1.767, 0, 9.033, 0, 0, 9.833, 7, 0, 10.4, -1.18, 0, 11, 0.769]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.105, 0, 0.533, -11, 0, 1.05, 18.18, 2, 2.733, 18.18, 0, 3.067, 3, 1, 3.211, 3, 3.356, 17.057, 3.5, 17.94, 1, 4.011, 21.064, 4.522, 21.687, 5.033, 21.687, 1, 5.166, 21.687, 5.3, 21.765, 5.433, 21.48, 1, 5.589, 21.148, 5.744, 14.107, 5.9, 9.587, 1, 6.078, 4.421, 6.255, -5.837, 6.433, -8.12, 1, 6.928, -14.468, 7.422, -15.955, 7.917, -15.955, 1, 8.361, -15.955, 8.806, -9.293, 9.25, -8.12, 1, 9.506, -7.445, 9.761, -7.742, 10.017, -7.257, 1, 10.206, -6.898, 10.394, 0.786, 10.583, 0.786, 0, 11, -3.105]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.65, -15.36, 2, 2.617, -15.36, 0, 3.5, 0, 2, 5.433, 0, 2, 6.433, 0, 2, 9.183, 0, 0, 9.55, -11, 0, 10.3, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.533, 0, 2, 0.617, 0, 2, 1.05, 0, 2, 2.617, 0, 2, 3.5, 0, 2, 5.433, 0, 0, 6.433, -30, 2, 9.183, -30, 0, 10.3, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.533, 0, 2, 0.617, -30, 0, 1.05, 0, 2, 2.617, 0, 0, 3.083, -30, 0, 3.333, 30, 0, 3.5, 0, 2, 5.433, 0, 0, 6.433, -10, 2, 9.183, -10, 0, 10.3, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "ParamHandLREDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.767, 4, 2, 2.617, 4, 2, 3.333, 2, 2, 3.5, 2, 2, 5.767, 2, 2, 5.783, 5, 2, 10.1, 5, 2, 10.117, 1, 2, 11, 1]}, {"Target": "Parameter", "Id": "Param100", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.35, 0, 0, 4.033, 1, 2, 4.983, 1, 0, 5.983, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "Param3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.499, 0, 0.383, -1.381, 0, 0.617, -0.848, 0, 0.783, -1.179, 0, 1.283, 1.236, 0, 1.817, 0.106, 0, 2.317, 2.367, 0, 2.683, 1.62, 0, 3, 3.046, 0, 4.133, -1.116, 0, 5.417, 2.227, 0, 5.783, 0.404, 0, 6.35, 2.255, 0, 6.917, 0.782, 0, 7.15, 1.062, 0, 7.783, -1.235, 0, 8.567, 1.537, 0, 9.6, -1.931, 0, 10.233, 4.739, 0, 11, -0.499]}, {"Target": "Parameter", "Id": "Param2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.661, 0, 0.45, 3.09, 0, 1.283, -2.761, 0, 1.817, -0.249, 0, 2.317, -5.3, 0, 2.683, -3.515, 0, 3, -6.632, 0, 3.767, 2.396, 0, 5.417, -5.058, 0, 5.8, -1.538, 0, 6.35, -5.019, 0, 6.917, -1.835, 0, 7.15, -2.43, 0, 7.817, 5.674, 0, 8.55, -3.639, 0, 9.583, 8.665, 0, 10.233, -9.852, 0, 11, 0.661]}, {"Target": "Parameter", "Id": "Param", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 7.32, 0, 0.317, 9.593, 0, 1.55, -2.704, 0, 1.8, -2.519, 0, 2.217, -3.496, 0, 4.35, 8.829, 0, 4.733, 7.961, 0, 5.683, 11.445, 0, 6.45, 2.443, 0, 6.783, 3.165, 0, 7.133, 2.487, 0, 7.9, 7.19, 0, 8.317, 5.646, 0, 9.05, 7.393, 0, 9.3, 7.182, 0, 9.533, 7.536, 0, 10.2, 3.044, 0, 11, 7.32]}, {"Target": "Parameter", "Id": "Param6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.948, 0, 0.283, 3.077, 0, 0.783, 1.605, 0, 2.233, 3.414, 0, 2.683, 2.284, 0, 2.95, 2.711, 0, 3.45, 1.97, 0, 5.133, 3.333, 0, 5.833, 2.685, 0, 6.25, 3.687, 0, 6.8, 2.889, 0, 7.1, 3.138, 0, 7.767, 2.03, 0, 8.333, 2.78, 0, 8.833, 2.45, 0, 9.167, 2.658, 0, 9.583, 1.279, 0, 10.2, 2.798, 0, 10.767, 1.825, 0, 11, 1.948]}, {"Target": "Parameter", "Id": "Param48", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -7.628, 0, 0.283, -11.918, 0, 0.8, -6.361, 0, 2.233, -13.216, 0, 2.683, -8.954, 0, 2.95, -10.54, 0, 3.433, -7.711, 0, 5.133, -12.824, 0, 5.833, -10.392, 0, 6.25, -14.26, 0, 6.8, -11.258, 0, 7.1, -12.194, 0, 7.767, -7.994, 0, 8.317, -10.769, 0, 8.833, -9.509, 0, 9.167, -10.336, 0, 9.583, -5.15, 0, 10.217, -10.744, 0, 10.767, -7.157, 0, 11, -7.628]}, {"Target": "Parameter", "Id": "Param37", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 9.009, 0, 0.583, 12.75, 0, 1.133, 10.621, 0, 2.617, 16.154, 0, 4, 8.514, 0, 4.35, 8.726, 0, 5.45, 6.494, 0, 6.783, 17.136, 0, 7.35, 15.687, 0, 7.667, 16.135, 0, 8.717, 8.701, 0, 9.483, 15.797, 0, 10.2, 4.09, 0, 11, 9.009]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.9, 0, 0.333, 1, 0, 0.683, 0, 2, 1.133, 0, 0, 1.817, 0.9, 2, 3.033, 0.9, 0, 3.35, 0, 0, 3.667, 0.9, 2, 4.517, 0.9, 0, 4.717, 0, 0, 4.933, 0.9, 2, 5.8, 0.9, 0, 6.05, 1.056, 0, 6.3, 0.9, 2, 7.267, 0.9, 0, 7.467, 0, 0, 7.683, 0.9, 2, 9.083, 0.9, 0, 9.433, 0, 0, 9.833, 0.9, 2, 11, 0.9]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 0, 0.683, 0.4, 2, 1.05, 0.4, 0, 1.217, 0, 0, 1.517, 0.5, 0, 1.817, 0, 2, 3.2, 0, 0, 3.45, 0.4, 0, 3.883, 0, 2, 4.617, 0, 0, 4.783, 0.5, 0, 5.067, 0, 2, 5.9, 0, 0, 6.15, 0.084, 0, 6.4, 0, 2, 7.367, 0, 0, 7.533, 0.5, 0, 7.817, 0, 2, 9.267, 0, 0, 9.567, 0.5, 0, 10.067, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.9, 0, 0.333, 1, 0, 0.683, 0, 2, 1.133, 0, 0, 1.817, 0.9, 2, 3.033, 0.9, 0, 3.35, 0, 0, 3.667, 0.9, 2, 4.517, 0.9, 0, 4.717, 0, 0, 4.933, 0.9, 2, 5.8, 0.9, 0, 6.05, 1.047, 0, 6.3, 0.9, 2, 7.267, 0.9, 0, 7.467, 0, 0, 7.683, 0.9, 2, 9.083, 0.9, 0, 9.433, 0, 0, 9.833, 0.9, 2, 11, 0.9]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.333, 0, 0, 0.683, 0.4, 2, 1.05, 0.4, 0, 1.217, 0, 0, 1.517, 0.5, 0, 1.817, 0, 2, 3.2, 0, 0, 3.45, 0.4, 0, 3.883, 0, 2, 4.617, 0, 0, 4.783, 0.5, 0, 5.067, 0, 2, 5.9, 0, 0, 6.15, 0.091, 0, 6.4, 0, 2, 7.367, 0, 0, 7.533, 0.5, 0, 7.817, 0, 2, 9.267, 0, 0, 9.567, 0.5, 0, 10.067, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.683, 0.07, 2, 1.283, 0.07, 0, 1.817, 0, 2, 3.033, 0, 0, 3.333, 0.07, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, 0.07, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, 0.07, 0, 7.683, 0, 2, 9.083, 0, 0, 9.433, 0.07, 0, 9.833, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.683, 0.364, 2, 1.283, 0.364, 0, 1.817, 0, 2, 3.033, 0, 0, 3.333, 0.364, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, 0.364, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, 0.364, 0, 7.683, 0, 2, 9.083, 0, 0, 9.433, 0.364, 0, 9.833, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "Param4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.683, -0.586, 2, 1.283, -0.586, 0, 1.817, 0, 2, 3.033, 0, 0, 3.333, -0.586, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, -0.586, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, -0.586, 0, 7.683, 0, 2, 9.083, 0, 0, 9.433, -0.586, 0, 9.833, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.683, 0.262, 2, 1.283, 0.262, 0, 1.817, 0, 2, 3.033, 0, 0, 3.333, 0.262, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, 0.262, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, 0.262, 0, 7.683, 0, 2, 9.083, 0, 0, 9.433, 0.262, 0, 9.833, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.683, 0.366, 2, 1.283, 0.366, 0, 1.817, 0, 2, 3.033, 0, 0, 3.333, 0.366, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, 0.366, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, 0.366, 0, 7.683, 0, 2, 9.083, 0, 0, 9.433, 0.366, 0, 9.833, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "Param11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.817, -0.572, 2, 1.283, -0.572, 0, 1.817, 0, 2, 3.033, 0, 0, 3.333, -0.572, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, -0.572, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, -0.572, 0, 7.683, 0, 2, 9.033, 0, 0, 9.233, -0.572, 0, 9.45, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.15, -0.244, 0, 0.333, -0.01, 0, 0.45, -0.319, 2, 0.633, -0.319, 2, 0.817, -0.319, 0, 0.9, 0.656, 0, 0.95, -0.319, 2, 1.133, -0.319, 2, 1.333, -0.319, 2, 1.367, -0.319, 2, 1.533, -0.319, 2, 1.683, -0.319, 0, 1.75, 0.34, 1, 1.817, 0.34, 1.883, 0.257, 1.95, -0.01, 1, 1.989, -0.166, 2.028, -0.319, 2.067, -0.319, 0, 2.283, -0.244, 0, 2.4, -0.319, 0, 2.617, 0.808, 2, 3.6, 0.808, 0, 3.8, 0, 0, 4.067, 1, 0, 4.15, 0.656, 0, 4.2, 1, 0, 4.383, -0.319, 0, 4.583, 0.224, 2, 4.717, 0.224, 0, 4.8, 0, 0, 4.883, 0.34, 0, 4.95, -0.244, 0, 5.133, -0.01, 0, 5.25, -0.319, 2, 5.433, -0.319, 2, 5.617, -0.319, 0, 5.7, 0.656, 0, 5.75, -0.319, 2, 5.933, -0.319, 2, 6.133, -0.319, 2, 6.167, -0.319, 2, 6.333, -0.319, 2, 6.483, -0.319, 1, 6.505, -0.319, 6.528, 0.274, 6.55, 0.34, 1, 6.617, 0.537, 6.683, 0.799, 6.75, 0.8, 1, 7.117, 0.806, 7.483, 0.808, 7.85, 0.808, 0, 8.05, 0, 0, 8.317, 1, 0, 8.4, 0.656, 0, 8.45, 1, 0, 8.633, -0.319, 0, 8.833, 0.224, 2, 8.967, 0.224, 0, 9.05, 0, 0, 9.133, 0.34, 0, 9.2, -0.244, 0, 9.383, -0.01, 0, 9.5, -0.319, 2, 9.683, -0.319, 2, 9.867, -0.319, 0, 9.95, 0.656, 0, 10, -0.319, 2, 10.183, -0.319, 2, 10.383, -0.319, 2, 10.417, -0.319, 0, 10.567, 0.6, 2, 11, 0.6]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 0.05, 0, 0.1, 0.185, 0.15, 0.5, 1, 0.206, 0.85, 0.261, 1, 0.317, 1, 0, 0.5, 0.102, 0, 0.683, 0.969, 0, 0.9, 0.012, 0, 1.1, 0.663, 0, 1.183, 0.024, 0, 1.333, 0.871, 2, 1.367, 0.871, 0, 1.65, 0, 0, 1.933, 1, 0, 2.1, 0.102, 0, 2.283, 0.5, 1, 2.333, 0.5, 2.383, 0.188, 2.433, 0.102, 1, 2.494, 0, 2.556, 0, 2.617, 0, 2, 3.6, 0, 0, 3.8, 0.5, 0, 4.083, 0.323, 0, 4.2, 0.9, 0, 4.283, 0.016, 0, 4.35, 0.663, 0, 4.433, 0.024, 0, 4.583, 0.871, 2, 4.717, 0.871, 0, 4.8, 0, 2, 4.883, 0, 1, 4.905, 0, 4.928, 0.339, 4.95, 0.5, 1, 5.006, 0.902, 5.061, 1, 5.117, 1, 0, 5.3, 0.102, 0, 5.483, 0.969, 0, 5.7, 0.012, 0, 5.9, 0.663, 0, 5.983, 0.024, 0, 6.133, 0.871, 2, 6.167, 0.871, 0, 6.45, 0, 2, 6.75, 0, 2, 7.85, 0, 0, 8.05, 0.5, 0, 8.333, 0.323, 0, 8.45, 0.9, 0, 8.533, 0.016, 0, 8.6, 0.663, 0, 8.683, 0.024, 0, 8.833, 0.871, 2, 8.967, 0.871, 0, 9.05, 0, 2, 9.133, 0, 1, 9.155, 0, 9.178, 0.339, 9.2, 0.5, 1, 9.256, 0.902, 9.311, 1, 9.367, 1, 0, 9.55, 0.102, 0, 9.733, 0.969, 0, 9.95, 0.012, 0, 10.15, 0.663, 0, 10.233, 0.024, 0, 10.383, 0.871, 2, 10.417, 0.871, 0, 10.567, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "Hair_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.509, 0, 0.183, 7.781, 0, 0.617, -7.33, 0, 1.183, 9.972, 0, 1.667, -9.771, 0, 2.15, 12.818, 0, 2.567, -20.634, 0, 2.917, 4.446, 0, 3.4, -4.248, 0, 4.067, 3.011, 0, 4.5, -3.159, 0, 5.417, 9.583, 0, 5.75, -13.702, 0, 6.15, 21.522, 0, 6.7, -0.314, 0, 7.033, 7.855, 0, 7.683, -1.789, 0, 8.2, 4.992, 0, 8.7, -0.875, 0, 9.05, 2.1, 0, 9.517, -9.255, 0, 10.017, 17.252, 0, 10.65, -12.525, 0, 11, 0.509]}, {"Target": "Parameter", "Id": "Hair_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.482, 0, 0.183, 3.267, 0, 0.617, -2.547, 0, 1.183, 4.11, 0, 1.667, -3.487, 0, 2.15, 5.205, 0, 2.567, -7.667, 0, 2.917, 1.984, 0, 3.4, -1.361, 0, 4.067, 1.432, 0, 4.5, -0.942, 0, 5.417, 3.961, 0, 5.75, -5, 0, 6.15, 8.555, 0, 6.7, 0.152, 0, 7.033, 3.296, 0, 7.683, -0.415, 0, 8.2, 2.194, 0, 8.7, -0.064, 0, 9.05, 1.081, 0, 9.517, -3.288, 0, 10.017, 6.912, 0, 10.65, -4.547, 0, 11, 0.482]}, {"Target": "Parameter", "Id": "Param69", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.482, 0, 0.183, 3.267, 0, 0.617, -2.547, 0, 1.183, 4.11, 0, 1.667, -3.487, 0, 2.15, 5.205, 0, 2.567, -7.667, 0, 2.917, 1.984, 0, 3.4, -1.361, 0, 4.067, 1.432, 0, 4.5, -0.942, 0, 5.417, 3.961, 0, 5.75, -5, 0, 6.15, 8.555, 0, 6.7, 0.152, 0, 7.033, 3.296, 0, 7.683, -0.415, 0, 8.2, 2.194, 0, 8.7, -0.064, 0, 9.05, 1.081, 0, 9.517, -3.288, 0, 10.017, 6.912, 0, 10.65, -4.547, 0, 11, 0.482]}, {"Target": "Parameter", "Id": "Param153", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.167, -7.435, 0, 0.483, 11.555, 0, 0.917, -12.071, 0, 1.45, 14.158, 0, 1.95, -17.124, 0, 2.417, 26.84, 0, 2.817, -28.673, 0, 3.217, 14.318, 0, 3.667, -6.989, 0, 4.317, 4.745, 0, 4.817, -4.476, 0, 5.667, 17.982, 0, 6.033, -29.788, 0, 6.45, 21.06, 0, 6.917, -13.427, 0, 7.35, 8.166, 0, 7.917, -5.069, 0, 8.467, 4.585, 0, 8.933, -3.854, 0, 9.367, 7.623, 0, 9.817, -16.661, 0, 10.317, 17.256, 0, 10.867, -14.801, 1, 10.911, -14.801, 10.956, -12.289, 11, -9.012]}, {"Target": "Parameter", "Id": "Param154", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.15, 4.023, 0, 0.4, -7.885, 0, 0.767, 10.521, 0, 1.233, -9.797, 0, 1.75, 11.85, 0, 2.267, -16.217, 0, 2.7, 25.347, 0, 3.067, -24.496, 0, 3.467, 15.159, 0, 3.9, -7.269, 0, 4.55, 3.576, 0, 5.017, -2.191, 0, 5.4, -0.064, 0, 5.617, -7.18, 0, 5.933, 19.584, 0, 6.283, -24.502, 0, 6.717, 18.039, 0, 7.15, -13.879, 0, 7.583, 7.697, 0, 8.133, -3.736, 0, 8.733, 3.246, 0, 9.217, -4.75, 0, 9.683, 9.199, 0, 10.117, -13.567, 0, 10.583, 10.865, 1, 10.722, 10.865, 10.861, -4.012, 11, -9.77]}, {"Target": "Parameter", "Id": "Param155", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.267, 1.772, 0, 0.583, -5.536, 0, 0.967, 9.172, 0, 1.4, -10.752, 0, 1.9, 12.364, 0, 2.4, -16.106, 0, 2.85, 23.459, 0, 3.25, -23.908, 0, 3.667, 17.248, 0, 4.1, -10.288, 0, 4.617, 5.121, 0, 5.15, -2.878, 0, 5.5, 0.08, 0, 5.767, -3.93, 0, 6.1, 14.935, 0, 6.483, -21.574, 0, 6.9, 19.967, 0, 7.333, -15.816, 0, 7.783, 10.401, 0, 8.267, -5.975, 0, 8.833, 3.841, 0, 9.367, -4.852, 0, 9.833, 8.524, 0, 10.283, -12.994, 0, 10.75, 12.674, 1, 10.833, 12.674, 10.917, 6.91, 11, 0.96]}, {"Target": "Parameter", "Id": "Param156", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.295, 2, 0.017, -0.295, 0, 0.367, 0.618, 0, 0.733, -4.54, 0, 1.133, 8.453, 0, 1.567, -11.151, 0, 2.05, 13.193, 0, 2.55, -16.7, 0, 3, 22.68, 0, 3.433, -23.978, 0, 3.867, 19.5, 0, 4.3, -13.287, 0, 4.767, 7.82, 0, 5.283, -4.165, 0, 5.667, 0.475, 0, 5.9, -1.829, 0, 6.25, 11.256, 0, 6.65, -19.357, 0, 7.083, 20.762, 0, 7.517, -17.794, 0, 7.967, 13.029, 0, 8.433, -8.32, 0, 8.95, 5.119, 0, 9.5, -4.986, 0, 9.983, 8.23, 0, 10.433, -12.746, 0, 10.917, 13.995, 1, 10.945, 13.995, 10.972, 13.243, 11, 11.991]}, {"Target": "Parameter", "Id": "Param82", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.167, -6.931, 0, 0.483, 10.772, 0, 0.917, -11.253, 0, 1.45, 13.199, 0, 1.95, -15.963, 0, 2.417, 25.021, 0, 2.817, -26.73, 0, 3.217, 13.347, 0, 3.667, -6.515, 0, 4.317, 4.424, 0, 4.817, -4.173, 0, 5.667, 16.764, 0, 6.033, -27.769, 0, 6.45, 19.633, 0, 6.917, -12.517, 0, 7.35, 7.613, 0, 7.917, -4.725, 0, 8.467, 4.275, 0, 8.933, -3.593, 0, 9.367, 7.106, 0, 9.817, -15.532, 0, 10.317, 16.087, 0, 10.867, -13.798, 1, 10.911, -13.798, 10.956, -11.456, 11, -8.401]}, {"Target": "Parameter", "Id": "Param83", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.15, 3.855, 0, 0.4, -7.554, 0, 0.767, 10.08, 0, 1.233, -9.386, 0, 1.75, 11.353, 0, 2.267, -15.537, 0, 2.7, 24.284, 0, 3.067, -23.469, 0, 3.467, 14.524, 0, 3.9, -6.964, 0, 4.55, 3.426, 0, 5.017, -2.099, 0, 5.4, -0.061, 0, 5.617, -6.879, 0, 5.933, 18.763, 0, 6.283, -23.475, 0, 6.717, 17.283, 0, 7.15, -13.297, 0, 7.583, 7.375, 0, 8.133, -3.579, 0, 8.733, 3.11, 0, 9.217, -4.55, 0, 9.683, 8.813, 0, 10.117, -12.998, 0, 10.583, 10.409, 1, 10.722, 10.409, 10.861, -3.843, 11, -9.36]}, {"Target": "Parameter", "Id": "Param84", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.267, 1.811, 0, 0.583, -5.659, 0, 0.967, 9.376, 0, 1.4, -10.991, 0, 1.9, 12.639, 0, 2.4, -16.464, 0, 2.85, 23.98, 0, 3.25, -24.439, 0, 3.667, 17.632, 0, 4.1, -10.517, 0, 4.617, 5.235, 0, 5.15, -2.942, 0, 5.5, 0.081, 0, 5.767, -4.018, 0, 6.1, 15.267, 0, 6.483, -22.054, 0, 6.9, 20.411, 0, 7.333, -16.168, 0, 7.783, 10.632, 0, 8.267, -6.108, 0, 8.833, 3.926, 0, 9.367, -4.96, 0, 9.833, 8.714, 0, 10.283, -13.283, 0, 10.75, 12.956, 1, 10.833, 12.956, 10.917, 7.064, 11, 0.981]}, {"Target": "Parameter", "Id": "Param85", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.301, 2, 0.017, -0.301, 0, 0.367, 0.63, 0, 0.733, -4.629, 0, 1.133, 8.619, 0, 1.567, -11.369, 0, 2.05, 13.451, 0, 2.55, -17.027, 0, 3, 23.124, 0, 3.433, -24.448, 0, 3.867, 19.881, 0, 4.3, -13.547, 0, 4.767, 7.973, 0, 5.283, -4.246, 0, 5.667, 0.484, 0, 5.9, -1.865, 0, 6.25, 11.477, 0, 6.65, -19.736, 0, 7.083, 21.168, 0, 7.517, -18.143, 0, 7.967, 13.284, 0, 8.433, -8.483, 0, 8.95, 5.219, 0, 9.5, -5.083, 0, 9.983, 8.391, 0, 10.433, -12.995, 0, 10.917, 14.268, 1, 10.945, 14.268, 10.972, 13.503, 11, 12.226]}, {"Target": "Parameter", "Id": "Param66", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.217, 3.155, 0, 0.483, -8.998, 0, 0.8, 12.713, 0, 1.15, -9.971, 0, 1.8, 7.42, 0, 2.267, -11.027, 0, 2.733, 20.61, 0, 3.083, -27.463, 0, 3.417, 20.73, 0, 3.767, -10.652, 0, 4.1, 3.734, 0, 4.383, -2.122, 0, 4.667, 3.853, 0, 5, -2.438, 0, 5.317, 0.628, 0, 5.683, -7.12, 0, 5.983, 21.346, 0, 6.317, -29.751, 0, 6.65, 22.639, 0, 7.067, -9.911, 0, 7.5, 6.186, 0, 7.867, -1.73, 0, 8.117, 0.206, 0, 8.383, -2.076, 0, 8.733, 2.711, 0, 9.233, -4.006, 0, 9.7, 7.008, 0, 10.133, -11.083, 0, 10.517, 8.873, 1, 10.678, 8.873, 10.839, -0.733, 11, -5.659]}, {"Target": "Parameter", "Id": "Param67", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.133, -10.589, 0, 0.367, 16.942, 0, 0.683, -13.768, 0, 1.017, 1.828, 0, 1.1, 1.63, 0, 1.383, 10.27, 0, 1.8, -13.437, 0, 2.317, 21.388, 0, 2.683, -29.973, 0, 2.983, 25.724, 0, 3.267, -9.895, 0, 3.55, 1.557, 0, 3.783, -2.458, 0, 4.217, 3.704, 0, 4.6, -3.965, 0, 4.883, -0.444, 0, 5.083, -1.321, 0, 5.6, 19.43, 0, 5.85, -30, 2, 5.95, -30, 0, 6.217, 30, 2, 6.233, 30, 0, 6.517, -7.835, 0, 6.683, -1.51, 0, 6.883, -5.966, 0, 7.15, 8.16, 0, 7.433, -1.581, 0, 7.617, 0.402, 0, 7.883, -3.735, 0, 8.3, 3.418, 0, 8.833, -2.8, 0, 9.217, 6.816, 0, 9.7, -13.089, 0, 10.1, 13.172, 0, 10.8, -11.878, 1, 10.867, -11.878, 10.933, -0.224, 11, 6.631]}, {"Target": "Parameter", "Id": "Param68", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.117, 3.917, 0, 0.3, -10.503, 0, 0.55, 13.249, 0, 0.833, -10.265, 0, 1.1, 3.696, 0, 1.317, -3.345, 0, 1.583, 6.862, 0, 1.95, -7.01, 0, 2.183, -0.93, 0, 2.25, -1.139, 0, 2.517, 13.04, 0, 2.867, -19.907, 0, 3.15, 20.06, 0, 3.417, -11.784, 0, 3.683, 5.851, 0, 3.95, -2.932, 0, 4.333, 1.767, 0, 4.383, 1.393, 0, 4.467, 1.493, 0, 4.75, -2.071, 0, 5, 0.899, 0, 5.267, -1.355, 0, 5.417, -0.421, 0, 5.55, -4.852, 0, 5.8, 18.063, 0, 6.067, -25.908, 0, 6.367, 22.83, 0, 6.633, -11.489, 0, 6.867, 4.885, 0, 7.083, -6.076, 0, 7.333, 5.704, 0, 7.583, -2.681, 0, 7.817, 2.124, 0, 8.067, -2.675, 0, 8.433, 1.707, 0, 8.7, -0.093, 0, 8.8, 0.023, 0, 9.033, -1.727, 0, 9.417, 4.194, 0, 9.883, -6.908, 0, 10.25, 6.075, 0, 10.517, -0.456, 0, 10.733, 3.033, 0, 11, -8.869]}, {"Target": "Parameter", "Id": "Param62", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.217, -3.155, 0, 0.483, 8.998, 0, 0.8, -12.713, 0, 1.15, 9.971, 0, 1.8, -7.42, 0, 2.267, 11.027, 0, 2.733, -20.61, 0, 3.083, 27.463, 0, 3.417, -20.73, 0, 3.767, 10.652, 0, 4.1, -3.734, 0, 4.383, 2.122, 0, 4.667, -3.853, 0, 5, 2.438, 0, 5.317, -0.628, 0, 5.683, 7.12, 0, 5.983, -21.346, 0, 6.317, 29.751, 0, 6.65, -22.639, 0, 7.067, 9.911, 0, 7.5, -6.186, 0, 7.867, 1.73, 0, 8.117, -0.206, 0, 8.383, 2.076, 0, 8.733, -2.711, 0, 9.233, 4.006, 0, 9.7, -7.008, 0, 10.133, 11.083, 0, 10.517, -8.873, 1, 10.678, -8.873, 10.839, 0.733, 11, 5.659]}, {"Target": "Parameter", "Id": "Param63", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.133, 10.589, 0, 0.367, -16.942, 0, 0.683, 13.768, 0, 1.017, -1.828, 0, 1.1, -1.63, 0, 1.383, -10.27, 0, 1.8, 13.437, 0, 2.317, -21.388, 0, 2.683, 29.973, 0, 2.983, -25.724, 0, 3.267, 9.895, 0, 3.55, -1.557, 0, 3.783, 2.458, 0, 4.217, -3.704, 0, 4.6, 3.965, 0, 4.883, 0.444, 0, 5.083, 1.321, 0, 5.6, -19.43, 0, 5.85, 30, 2, 5.95, 30, 0, 6.217, -30, 2, 6.233, -30, 0, 6.517, 7.835, 0, 6.683, 1.51, 0, 6.883, 5.966, 0, 7.15, -8.16, 0, 7.433, 1.581, 0, 7.617, -0.402, 0, 7.883, 3.735, 0, 8.3, -3.418, 0, 8.833, 2.8, 0, 9.217, -6.816, 0, 9.7, 13.089, 0, 10.1, -13.172, 0, 10.8, 11.878, 1, 10.867, 11.878, 10.933, 0.224, 11, -6.631]}, {"Target": "Parameter", "Id": "Param64", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.117, -3.917, 0, 0.3, 10.503, 0, 0.55, -13.249, 0, 0.833, 10.265, 0, 1.1, -3.696, 0, 1.317, 3.345, 0, 1.583, -6.862, 0, 1.95, 7.01, 0, 2.183, 0.93, 0, 2.25, 1.139, 0, 2.517, -13.04, 0, 2.867, 19.907, 0, 3.15, -20.06, 0, 3.417, 11.784, 0, 3.683, -5.851, 0, 3.95, 2.932, 0, 4.333, -1.767, 0, 4.383, -1.393, 0, 4.467, -1.493, 0, 4.75, 2.071, 0, 5, -0.899, 0, 5.267, 1.355, 0, 5.417, 0.421, 0, 5.55, 4.852, 0, 5.8, -18.063, 0, 6.067, 25.908, 0, 6.367, -22.83, 0, 6.633, 11.489, 0, 6.867, -4.885, 0, 7.083, 6.076, 0, 7.333, -5.704, 0, 7.583, 2.681, 0, 7.817, -2.124, 0, 8.067, 2.675, 0, 8.433, -1.707, 0, 8.7, 0.093, 0, 8.8, -0.023, 0, 9.033, 1.727, 0, 9.417, -4.194, 0, 9.883, 6.908, 0, 10.25, -6.075, 0, 10.517, 0.456, 0, 10.733, -3.033, 0, 11, 8.869]}, {"Target": "Parameter", "Id": "Param86", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.211, 0, 0.183, -1.429, 0, 0.617, 1.114, 0, 1.183, -1.798, 0, 1.667, 1.525, 0, 2.15, -2.277, 0, 2.567, 3.354, 0, 2.917, -0.868, 0, 3.4, 0.596, 0, 4.067, -0.626, 0, 4.5, 0.412, 0, 5.417, -1.732, 0, 5.75, 2.187, 0, 6.15, -3.742, 0, 6.7, -0.067, 0, 7.033, -1.442, 0, 7.683, 0.182, 0, 8.2, -0.96, 0, 8.7, 0.028, 0, 9.05, -0.473, 0, 9.517, 1.438, 0, 10.017, -3.023, 0, 10.65, 1.989, 0, 11, -0.211]}, {"Target": "Parameter", "Id": "Param89", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.15, -6.672, 0, 0.45, 9.604, 0, 0.85, -8.585, 0, 1.433, 9.427, 0, 1.917, -11.681, 0, 2.383, 19.268, 0, 2.783, -21.672, 0, 3.117, 11.417, 0, 3.533, -4.237, 0, 4.317, 3.037, 0, 4.75, -2.88, 0, 5.65, 14.76, 0, 5.983, -23.815, 0, 6.35, 15.561, 0, 6.867, -8.363, 0, 7.25, 5.221, 0, 7.95, -2.97, 0, 8.433, 3.042, 0, 8.9, -2.584, 0, 9.3, 5.737, 0, 9.783, -11.994, 0, 10.25, 11.453, 0, 10.85, -9.793, 1, 10.9, -9.793, 10.95, -6.513, 11, -3.061]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.133, 4.346, 0, 0.35, -8.367, 0, 0.683, 9.644, 0, 1.05, -6.2, 0, 1.717, 6.926, 0, 2.133, -9.141, 0, 2.167, -9.106, 0, 2.233, -9.317, 0, 2.65, 18.132, 0, 2.983, -21.765, 0, 3.317, 12.916, 0, 3.683, -5.309, 0, 4.067, 1.237, 0, 4.283, -0.979, 0, 4.583, 3.047, 0, 4.917, -1.468, 0, 5.2, 0.142, 0, 5.583, -7.497, 0, 5.883, 18.958, 0, 6.217, -23.257, 0, 6.55, 13.957, 0, 7.067, -7.731, 0, 7.417, 4.418, 0, 7.75, 0.005, 0, 7.85, 0.057, 0, 7.917, -0.05, 0, 7.933, -0.023, 0, 8.267, -1.73, 0, 8.667, 1.993, 0, 9.167, -3.162, 0, 9.617, 5.774, 0, 10.05, -8.886, 0, 10.433, 6.074, 0, 10.683, 2.279, 0, 10.75, 2.384, 1, 10.833, 2.384, 10.917, -4.705, 11, -7.689]}, {"Target": "Parameter", "Id": "Param88", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.217, -2.311, 0, 0.483, 6.602, 0, 0.8, -9.336, 0, 1.15, 7.324, 0, 1.8, -5.481, 0, 2.267, 8.655, 0, 2.733, -15.805, 0, 3.083, 20.337, 0, 3.417, -15.334, 0, 3.767, 7.85, 0, 4.117, -2.383, 0, 4.4, 1.248, 0, 4.483, 0.701, 0, 4.5, 0.766, 0, 4.75, -1.906, 0, 5.083, 2.358, 0, 5.367, -0.671, 0, 5.683, 5.337, 0, 6, -15.789, 0, 6.317, 22.026, 0, 6.65, -16.791, 0, 7.083, 7.48, 0, 7.5, -4.879, 0, 7.8, 1.776, 0, 8.017, -0.041, 0, 8.367, 1.391, 0, 8.733, -1.921, 0, 9.233, 2.97, 0, 9.683, -5.237, 0, 10.133, 7.612, 0, 10.567, -6.973, 1, 10.711, -6.973, 10.856, -0.527, 11, 3.306]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.317, 1.451, 0, 0.6, -5.242, 0, 0.9, 8.715, 0, 1.25, -8.174, 0, 1.9, 3.803, 0, 2.35, -7.757, 0, 2.817, 13.219, 0, 3.183, -18.754, 0, 3.517, 16.688, 0, 3.867, -10.24, 0, 4.2, 4.337, 0, 4.483, -1.904, 0, 4.883, 7.147, 0, 5.25, -4.938, 0, 5.517, 1.685, 0, 5.783, -4.276, 0, 6.1, 13.32, 0, 6.433, -20.996, 0, 6.767, 18.549, 0, 7.117, -9.941, 0, 7.567, 5.04, 0, 7.933, -2.577, 0, 8.2, 0.518, 0, 8.483, -1.272, 0, 8.85, 2.035, 0, 9.333, -3.111, 0, 9.767, 4.558, 0, 10.217, -6.784, 0, 10.633, 6.373, 1, 10.755, 6.373, 10.878, 2.831, 11, -0.256]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.211, 0, 0.183, 1.429, 0, 0.617, -1.114, 0, 1.183, 1.798, 0, 1.667, -1.525, 0, 2.15, 2.277, 0, 2.567, -3.354, 0, 2.917, 0.868, 0, 3.4, -0.596, 0, 4.067, 0.626, 0, 4.5, -0.412, 0, 5.417, 1.732, 0, 5.75, -2.187, 0, 6.15, 3.742, 0, 6.7, 0.067, 0, 7.033, 1.442, 0, 7.683, -0.182, 0, 8.2, 0.96, 0, 8.7, -0.028, 0, 9.05, 0.473, 0, 9.517, -1.438, 0, 10.017, 3.023, 0, 10.65, -1.989, 0, 11, 0.211]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.15, -6.672, 0, 0.45, 9.604, 0, 0.85, -8.585, 0, 1.433, 9.427, 0, 1.917, -11.681, 0, 2.383, 19.268, 0, 2.783, -21.672, 0, 3.117, 11.417, 0, 3.533, -4.237, 0, 4.317, 3.037, 0, 4.75, -2.88, 0, 5.65, 14.76, 0, 5.983, -23.815, 0, 6.35, 15.561, 0, 6.867, -8.363, 0, 7.25, 5.221, 0, 7.95, -2.97, 0, 8.433, 3.042, 0, 8.9, -2.584, 0, 9.3, 5.737, 0, 9.783, -11.994, 0, 10.25, 11.453, 0, 10.85, -9.793, 1, 10.9, -9.793, 10.95, -6.513, 11, -3.061]}, {"Target": "Parameter", "Id": "Param126", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.133, 4.346, 0, 0.35, -8.367, 0, 0.683, 9.644, 0, 1.05, -6.2, 0, 1.717, 6.926, 0, 2.133, -9.141, 0, 2.167, -9.106, 0, 2.233, -9.317, 0, 2.65, 18.132, 0, 2.983, -21.765, 0, 3.317, 12.916, 0, 3.683, -5.309, 0, 4.067, 1.237, 0, 4.283, -0.979, 0, 4.583, 3.047, 0, 4.917, -1.468, 0, 5.2, 0.142, 0, 5.583, -7.497, 0, 5.883, 18.958, 0, 6.217, -23.257, 0, 6.55, 13.957, 0, 7.067, -7.731, 0, 7.417, 4.418, 0, 7.75, 0.005, 0, 7.85, 0.057, 0, 7.917, -0.05, 0, 7.933, -0.023, 0, 8.267, -1.73, 0, 8.667, 1.993, 0, 9.167, -3.162, 0, 9.617, 5.774, 0, 10.05, -8.886, 0, 10.433, 6.074, 0, 10.683, 2.279, 0, 10.75, 2.384, 1, 10.833, 2.384, 10.917, -4.705, 11, -7.689]}, {"Target": "Parameter", "Id": "Param128", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.217, -2.311, 0, 0.483, 6.602, 0, 0.8, -9.336, 0, 1.15, 7.324, 0, 1.8, -5.481, 0, 2.267, 8.655, 0, 2.733, -15.805, 0, 3.083, 20.337, 0, 3.417, -15.334, 0, 3.767, 7.85, 0, 4.117, -2.383, 0, 4.4, 1.248, 0, 4.483, 0.701, 0, 4.5, 0.766, 0, 4.75, -1.906, 0, 5.083, 2.358, 0, 5.367, -0.671, 0, 5.683, 5.337, 0, 6, -15.789, 0, 6.317, 22.026, 0, 6.65, -16.791, 0, 7.083, 7.48, 0, 7.5, -4.879, 0, 7.8, 1.776, 0, 8.017, -0.041, 0, 8.367, 1.391, 0, 8.733, -1.921, 0, 9.233, 2.97, 0, 9.683, -5.237, 0, 10.133, 7.612, 0, 10.567, -6.973, 1, 10.711, -6.973, 10.856, -0.527, 11, 3.306]}, {"Target": "Parameter", "Id": "Param129", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.317, 1.451, 0, 0.6, -5.242, 0, 0.9, 8.715, 0, 1.25, -8.174, 0, 1.9, 3.803, 0, 2.35, -7.757, 0, 2.817, 13.219, 0, 3.183, -18.754, 0, 3.517, 16.688, 0, 3.867, -10.24, 0, 4.2, 4.337, 0, 4.483, -1.904, 0, 4.883, 7.147, 0, 5.25, -4.938, 0, 5.517, 1.685, 0, 5.783, -4.276, 0, 6.1, 13.32, 0, 6.433, -20.996, 0, 6.767, 18.549, 0, 7.117, -9.941, 0, 7.567, 5.04, 0, 7.933, -2.577, 0, 8.2, 0.518, 0, 8.483, -1.272, 0, 8.85, 2.035, 0, 9.333, -3.111, 0, 9.767, 4.558, 0, 10.217, -6.784, 0, 10.633, 6.373, 1, 10.755, 6.373, 10.878, 2.831, 11, -0.256]}, {"Target": "Parameter", "Id": "Param19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 3.667, 0.183, 7.333, 0.367, 11, 0.55]}, {"Target": "Parameter", "Id": "Param142", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.812, 2, 0.017, 0.127, 1, 2.561, 0.305, 5.106, 0.482, 7.65, 0.66, 1, 8.1, 0.711, 8.55, 0.761, 9, 0.812, 2, 9.017, 0.127, 1, 9.678, 0.173, 10.339, 0.219, 11, 0.265]}, {"Target": "Parameter", "Id": "Param143", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.76, 1, 0.422, 0.82, 0.845, 0.88, 1.267, 0.94, 2, 1.283, 0.2, 1, 3.855, 0.387, 6.428, 0.573, 9, 0.76, 1, 9.422, 0.82, 9.845, 0.88, 10.267, 0.94, 2, 10.283, 0.2, 1, 10.522, 0.217, 10.761, 0.235, 11, 0.252]}, {"Target": "Parameter", "Id": "Param18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 0, 2, 0.35, 1, 2, 0.4, 2, 2, 0.45, 3, 2, 0.5, 4, 2, 0.55, 5, 2, 0.6, 0, 2, 0.65, 1, 2, 0.7, 2, 2, 0.75, 3, 2, 0.8, 4, 2, 0.85, 5, 2, 0.9, 0, 2, 0.95, 1, 2, 1, 2, 2, 1.05, 3, 2, 1.1, 4, 2, 1.15, 5, 2, 1.2, 0, 2, 1.25, 1, 2, 1.3, 2, 2, 1.35, 3, 2, 1.4, 4, 2, 1.45, 5, 2, 1.5, 0, 2, 1.55, 1, 2, 1.6, 2, 2, 1.65, 3, 2, 1.7, 4, 2, 1.75, 5, 2, 1.8, 0, 2, 1.85, 1, 2, 1.9, 2, 2, 1.95, 3, 2, 2, 4, 2, 2.05, 5, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 0, 2, 2.45, 1, 2, 2.5, 2, 2, 2.55, 3, 2, 2.6, 4, 2, 2.65, 5, 2, 2.7, 0, 2, 2.75, 1, 2, 2.8, 2, 2, 2.85, 3, 2, 2.9, 4, 2, 2.95, 5, 2, 3, 0, 2, 3.05, 1, 2, 3.1, 2, 2, 3.15, 3, 2, 3.2, 4, 2, 3.25, 5, 2, 3.3, 0, 2, 3.35, 1, 2, 3.4, 2, 2, 3.45, 3, 2, 3.5, 4, 2, 3.55, 5, 2, 3.6, 0, 2, 3.65, 1, 2, 3.7, 2, 2, 3.75, 3, 2, 3.8, 4, 2, 3.85, 5, 2, 3.9, 0, 2, 3.95, 1, 2, 4, 2, 2, 4.05, 3, 2, 4.1, 4, 2, 4.15, 5, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 0, 2, 4.55, 1, 2, 4.6, 2, 2, 4.65, 3, 2, 4.7, 4, 2, 4.75, 5, 2, 4.8, 0, 2, 4.85, 1, 2, 4.9, 2, 2, 4.95, 3, 2, 5, 4, 2, 5.05, 5, 2, 5.1, 0, 2, 5.15, 1, 2, 5.2, 2, 2, 5.25, 3, 2, 5.3, 4, 2, 5.35, 5, 2, 5.4, 0, 2, 5.45, 1, 2, 5.5, 2, 2, 5.55, 3, 2, 5.6, 4, 2, 5.65, 5, 2, 5.7, 0, 2, 5.75, 1, 2, 5.8, 2, 2, 5.85, 3, 2, 5.9, 4, 2, 5.95, 5, 2, 6, 0, 2, 6.05, 1, 2, 6.1, 2, 2, 6.15, 3, 2, 6.2, 4, 2, 6.25, 5, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 0, 2, 6.65, 1, 2, 6.7, 2, 2, 6.75, 3, 2, 6.8, 4, 2, 6.85, 5, 2, 6.9, 0, 2, 6.95, 1, 2, 7, 2, 2, 7.05, 3, 2, 7.1, 4, 2, 7.15, 5, 2, 7.2, 0, 2, 7.25, 1, 2, 7.3, 2, 2, 7.35, 3, 2, 7.4, 4, 2, 7.45, 5, 2, 7.5, 0, 2, 7.55, 1, 2, 7.6, 2, 2, 7.65, 3, 2, 7.7, 4, 2, 7.75, 5, 2, 7.8, 0, 2, 7.85, 1, 2, 7.9, 2, 2, 7.95, 3, 2, 8, 4, 2, 8.05, 5, 2, 8.1, 0, 2, 8.15, 1, 2, 8.2, 2, 2, 8.25, 3, 2, 8.3, 4, 2, 8.35, 5, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 0, 2, 8.75, 1, 2, 8.8, 2, 2, 8.85, 3, 2, 8.9, 4, 2, 8.95, 5, 2, 9, 0, 2, 9.05, 1, 2, 9.1, 2, 2, 9.15, 3, 2, 9.2, 4, 2, 9.25, 5, 2, 9.3, 0, 2, 9.35, 1, 2, 9.4, 2, 2, 9.45, 3, 2, 9.5, 4, 2, 9.55, 5, 2, 9.6, 0, 2, 9.65, 1, 2, 9.7, 2, 2, 9.75, 3, 2, 9.8, 4, 2, 9.85, 5, 2, 9.9, 0, 2, 9.95, 1, 2, 10, 2, 2, 10.05, 3, 2, 10.1, 4, 2, 10.15, 5, 2, 10.2, 0, 2, 10.25, 1, 2, 10.3, 2, 2, 10.35, 3, 2, 10.4, 4, 2, 10.45, 5, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 0, 2, 10.85, 1, 2, 10.9, 2, 2, 10.95, 3, 2, 11, 4]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 6, 2, 0.35, 0, 2, 0.4, 1, 2, 0.45, 2, 2, 0.5, 3, 2, 0.55, 4, 2, 0.6, 5, 2, 0.65, 6, 2, 0.7, 0, 2, 0.75, 1, 2, 0.8, 2, 2, 0.85, 3, 2, 0.9, 4, 2, 0.95, 5, 2, 1, 6, 2, 1.05, 0, 2, 1.1, 1, 2, 1.15, 2, 2, 1.2, 3, 2, 1.25, 4, 2, 1.3, 5, 2, 1.35, 6, 2, 1.4, 0, 2, 1.45, 1, 2, 1.5, 2, 2, 1.55, 3, 2, 1.6, 4, 2, 1.65, 5, 2, 1.7, 6, 2, 1.75, 0, 2, 1.8, 1, 2, 1.85, 2, 2, 1.9, 3, 2, 1.95, 4, 2, 2, 5, 2, 2.05, 6, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 6, 2, 2.45, 0, 2, 2.5, 1, 2, 2.55, 2, 2, 2.6, 3, 2, 2.65, 4, 2, 2.7, 5, 2, 2.75, 6, 2, 2.8, 0, 2, 2.85, 1, 2, 2.9, 2, 2, 2.95, 3, 2, 3, 4, 2, 3.05, 5, 2, 3.1, 6, 2, 3.15, 0, 2, 3.2, 1, 2, 3.25, 2, 2, 3.3, 3, 2, 3.35, 4, 2, 3.4, 5, 2, 3.45, 6, 2, 3.5, 0, 2, 3.55, 1, 2, 3.6, 2, 2, 3.65, 3, 2, 3.7, 4, 2, 3.75, 5, 2, 3.8, 6, 2, 3.85, 0, 2, 3.9, 1, 2, 3.95, 2, 2, 4, 3, 2, 4.05, 4, 2, 4.1, 5, 2, 4.15, 6, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 6, 2, 4.55, 0, 2, 4.6, 1, 2, 4.65, 2, 2, 4.7, 3, 2, 4.75, 4, 2, 4.8, 5, 2, 4.85, 6, 2, 4.9, 0, 2, 4.95, 1, 2, 5, 2, 2, 5.05, 3, 2, 5.1, 4, 2, 5.15, 5, 2, 5.2, 6, 2, 5.25, 0, 2, 5.3, 1, 2, 5.35, 2, 2, 5.4, 3, 2, 5.45, 4, 2, 5.5, 5, 2, 5.55, 6, 2, 5.6, 0, 2, 5.65, 1, 2, 5.7, 2, 2, 5.75, 3, 2, 5.8, 4, 2, 5.85, 5, 2, 5.9, 6, 2, 5.95, 0, 2, 6, 1, 2, 6.05, 2, 2, 6.1, 3, 2, 6.15, 4, 2, 6.2, 5, 2, 6.25, 6, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 6, 2, 6.65, 0, 2, 6.7, 1, 2, 6.75, 2, 2, 6.8, 3, 2, 6.85, 4, 2, 6.9, 5, 2, 6.95, 6, 2, 7, 0, 2, 7.05, 1, 2, 7.1, 2, 2, 7.15, 3, 2, 7.2, 4, 2, 7.25, 5, 2, 7.3, 6, 2, 7.35, 0, 2, 7.4, 1, 2, 7.45, 2, 2, 7.5, 3, 2, 7.55, 4, 2, 7.6, 5, 2, 7.65, 6, 2, 7.7, 0, 2, 7.75, 1, 2, 7.8, 2, 2, 7.85, 3, 2, 7.9, 4, 2, 7.95, 5, 2, 8, 6, 2, 8.05, 0, 2, 8.1, 1, 2, 8.15, 2, 2, 8.2, 3, 2, 8.25, 4, 2, 8.3, 5, 2, 8.35, 6, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 6, 2, 8.75, 0, 2, 8.8, 1, 2, 8.85, 2, 2, 8.9, 3, 2, 8.95, 4, 2, 9, 5, 2, 9.05, 6, 2, 9.1, 0, 2, 9.15, 1, 2, 9.2, 2, 2, 9.25, 3, 2, 9.3, 4, 2, 9.35, 5, 2, 9.4, 6, 2, 9.45, 0, 2, 9.5, 1, 2, 9.55, 2, 2, 9.6, 3, 2, 9.65, 4, 2, 9.7, 5, 2, 9.75, 6, 2, 9.8, 0, 2, 9.85, 1, 2, 9.9, 2, 2, 9.95, 3, 2, 10, 4, 2, 10.05, 5, 2, 10.1, 6, 2, 10.15, 0, 2, 10.2, 1, 2, 10.25, 2, 2, 10.3, 3, 2, 10.35, 4, 2, 10.4, 5, 2, 10.45, 6, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 6, 2, 10.85, 0, 2, 10.9, 1, 2, 10.95, 2, 2, 11, 3]}, {"Target": "Parameter", "Id": "Param51", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 6, 2, 0.35, 0, 2, 0.4, 1, 2, 0.45, 2, 2, 0.5, 3, 2, 0.55, 4, 2, 0.6, 5, 2, 0.65, 6, 2, 0.7, 0, 2, 0.75, 1, 2, 0.8, 2, 2, 0.85, 3, 2, 0.9, 4, 2, 0.95, 5, 2, 1, 6, 2, 1.05, 0, 2, 1.1, 1, 2, 1.15, 2, 2, 1.2, 3, 2, 1.25, 4, 2, 1.3, 5, 2, 1.35, 6, 2, 1.4, 0, 2, 1.45, 1, 2, 1.5, 2, 2, 1.55, 3, 2, 1.6, 4, 2, 1.65, 5, 2, 1.7, 6, 2, 1.75, 0, 2, 1.8, 1, 2, 1.85, 2, 2, 1.9, 3, 2, 1.95, 4, 2, 2, 5, 2, 2.05, 6, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 6, 2, 2.45, 0, 2, 2.5, 1, 2, 2.55, 2, 2, 2.6, 3, 2, 2.65, 4, 2, 2.7, 5, 2, 2.75, 6, 2, 2.8, 0, 2, 2.85, 1, 2, 2.9, 2, 2, 2.95, 3, 2, 3, 4, 2, 3.05, 5, 2, 3.1, 6, 2, 3.15, 0, 2, 3.2, 1, 2, 3.25, 2, 2, 3.3, 3, 2, 3.35, 4, 2, 3.4, 5, 2, 3.45, 6, 2, 3.5, 0, 2, 3.55, 1, 2, 3.6, 2, 2, 3.65, 3, 2, 3.7, 4, 2, 3.75, 5, 2, 3.8, 6, 2, 3.85, 0, 2, 3.9, 1, 2, 3.95, 2, 2, 4, 3, 2, 4.05, 4, 2, 4.1, 5, 2, 4.15, 6, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 6, 2, 4.55, 0, 2, 4.6, 1, 2, 4.65, 2, 2, 4.7, 3, 2, 4.75, 4, 2, 4.8, 5, 2, 4.85, 6, 2, 4.9, 0, 2, 4.95, 1, 2, 5, 2, 2, 5.05, 3, 2, 5.1, 4, 2, 5.15, 5, 2, 5.2, 6, 2, 5.25, 0, 2, 5.3, 1, 2, 5.35, 2, 2, 5.4, 3, 2, 5.45, 4, 2, 5.5, 5, 2, 5.55, 6, 2, 5.6, 0, 2, 5.65, 1, 2, 5.7, 2, 2, 5.75, 3, 2, 5.8, 4, 2, 5.85, 5, 2, 5.9, 6, 2, 5.95, 0, 2, 6, 1, 2, 6.05, 2, 2, 6.1, 3, 2, 6.15, 4, 2, 6.2, 5, 2, 6.25, 6, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 6, 2, 6.65, 0, 2, 6.7, 1, 2, 6.75, 2, 2, 6.8, 3, 2, 6.85, 4, 2, 6.9, 5, 2, 6.95, 6, 2, 7, 0, 2, 7.05, 1, 2, 7.1, 2, 2, 7.15, 3, 2, 7.2, 4, 2, 7.25, 5, 2, 7.3, 6, 2, 7.35, 0, 2, 7.4, 1, 2, 7.45, 2, 2, 7.5, 3, 2, 7.55, 4, 2, 7.6, 5, 2, 7.65, 6, 2, 7.7, 0, 2, 7.75, 1, 2, 7.8, 2, 2, 7.85, 3, 2, 7.9, 4, 2, 7.95, 5, 2, 8, 6, 2, 8.05, 0, 2, 8.1, 1, 2, 8.15, 2, 2, 8.2, 3, 2, 8.25, 4, 2, 8.3, 5, 2, 8.35, 6, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 6, 2, 8.75, 0, 2, 8.8, 1, 2, 8.85, 2, 2, 8.9, 3, 2, 8.95, 4, 2, 9, 5, 2, 9.05, 6, 2, 9.1, 0, 2, 9.15, 1, 2, 9.2, 2, 2, 9.25, 3, 2, 9.3, 4, 2, 9.35, 5, 2, 9.4, 6, 2, 9.45, 0, 2, 9.5, 1, 2, 9.55, 2, 2, 9.6, 3, 2, 9.65, 4, 2, 9.7, 5, 2, 9.75, 6, 2, 9.8, 0, 2, 9.85, 1, 2, 9.9, 2, 2, 9.95, 3, 2, 10, 4, 2, 10.05, 5, 2, 10.1, 6, 2, 10.15, 0, 2, 10.2, 1, 2, 10.25, 2, 2, 10.3, 3, 2, 10.35, 4, 2, 10.4, 5, 2, 10.45, 6, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 6, 2, 10.85, 0, 2, 10.9, 1, 2, 10.95, 2, 2, 11, 3]}, {"Target": "Parameter", "Id": "Param42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 3.667, 0.041, 7.333, 0.081, 11, 0.122]}, {"Target": "Parameter", "Id": "Param45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.4, 1, 3.667, -0.359, 7.333, -0.319, 11, -0.278]}, {"Target": "Parameter", "Id": "Param46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.3, 1, 3.667, 0.341, 7.333, 0.381, 11, 0.422]}, {"Target": "Parameter", "Id": "Param50", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 3.667, 0.733, 7.333, 1.467, 11, 2.2]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2.5, 10, 0, 5.017, -10, 0, 7.5, 10, 0, 10, -10, 1, 10.333, -10, 10.667, -4.093, 11, 2.996]}, {"Target": "Parameter", "Id": "Param17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.76, 0, 2.5, 0.66, 0, 5.017, -5.76, 0, 7.5, 0.66, 0, 10, -5.76, 1, 10.333, -5.76, 10.667, -1.469, 11, 3.681]}, {"Target": "Parameter", "Id": "Param49", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.35, 0, 1.25, 0, 0, 3.75, 0.7, 0, 6.267, 0, 0, 8.767, 0.7, 1, 9.511, 0.7, 10.256, 0.149, 11, 0.025]}, {"Target": "Parameter", "Id": "Param40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.25, 0, 0.917, 10, 0, 2.917, -10, 0, 4.917, 10, 0, 6.917, -10, 0, 8.917, 10, 0, 10.917, -10, 1, 10.945, -10, 10.972, -9.965, 11, -9.899]}, {"Target": "Parameter", "Id": "Param41", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2, 10, 0, 4, -10, 0, 6, 10, 0, 8, -10, 0, 10, 10, 1, 10.333, 10, 10.667, 5, 11, 0]}, {"Target": "Parameter", "Id": "Param32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2, 10, 0, 4, -10, 0, 6, 10, 0, 8, -10, 0, 10, 10, 1, 10.333, 10, 10.667, 5, 11, 0]}, {"Target": "Parameter", "Id": "Param33", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.886, 0, 0.583, 10, 0, 2.583, -10, 0, 4.583, 10, 0, 6.583, -10, 0, 8.583, 10, 0, 10.583, -10, 1, 10.722, -10, 10.861, -9.132, 11, -7.758]}, {"Target": "Parameter", "Id": "Param34", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -14.451, 0, 1.333, 30, 0, 3.333, -30, 0, 5.333, 30, 0, 7.333, -30, 0, 9.333, 30, 1, 9.889, 30, 10.444, -11.667, 11, -25.556]}, {"Target": "Parameter", "Id": "Param28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -15, 0, 2, 15, 0, 4, -15, 0, 6, 15, 0, 8, -15, 0, 10, 15, 1, 10.333, 15, 10.667, 7.5, 11, 0]}, {"Target": "Parameter", "Id": "Param29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.886, 0, 0.583, 10, 0, 2.583, -10, 0, 4.583, 10, 0, 6.583, -10, 0, 8.583, 10, 0, 10.583, -10, 1, 10.722, -10, 10.861, -9.132, 11, -7.758]}, {"Target": "Parameter", "Id": "Param35", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -14.451, 0, 1.333, 30, 0, 3.333, -30, 0, 5.333, 30, 0, 7.333, -30, 0, 9.333, 30, 1, 9.889, 30, 10.444, -11.667, 11, -25.556]}, {"Target": "Parameter", "Id": "Param30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.959, 0, 0.8, 10, 0, 2.8, -10, 0, 4.8, 10, 0, 6.8, -10, 0, 8.8, 10, 0, 10.8, -10, 1, 10.867, -10, 10.933, -9.8, 11, -9.44]}, {"Target": "Parameter", "Id": "Param31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 9.44, 0, 1.8, -10, 0, 3.8, 10, 0, 5.8, -10, 0, 7.8, 10, 0, 9.8, -10, 1, 10.2, -10, 10.6, -2.8, 11, 2.96]}, {"Target": "Parameter", "Id": "Param36", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 13.773, 0, 0.683, 30, 0, 2.683, -30, 0, 4.683, 30, 0, 6.683, -30, 0, 8.683, 30, 0, 10.683, -30, 1, 10.789, -30, 10.894, -28.496, 11, -25.964]}, {"Target": "Parameter", "Id": "GL1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 3.667, 0.163, 7.333, 0.327, 11, 0.49]}, {"Target": "Parameter", "Id": "GL2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.39, 1, 3.667, 0.553, 7.333, 0.717, 11, 0.88]}, {"Target": "Parameter", "Id": "GL3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.72, 1, 3.667, 0.883, 7.333, 1.047, 11, 1.21]}, {"Target": "Parameter", "Id": "GL4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.21, 1, 3.667, 0.373, 7.333, 0.537, 11, 0.7]}, {"Target": "Parameter", "Id": "GL5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.53, 1, 3.667, 0.693, 7.333, 0.857, 11, 1.02]}, {"Target": "Parameter", "Id": "GL6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.89, 1, 3.667, 1.053, 7.333, 1.217, 11, 1.38]}, {"Target": "Parameter", "Id": "GL7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.77, 1, 3.667, 0.933, 7.333, 1.097, 11, 1.26]}, {"Target": "Parameter", "Id": "GL8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.09, 1, 3.667, 0.253, 7.333, 0.417, 11, 0.58]}, {"Target": "Parameter", "Id": "GL9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.301, 1, 3.667, 0.464, 7.333, 0.628, 11, 0.791]}, {"Target": "Parameter", "Id": "GL10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.45, 1, 3.667, 0.613, 7.333, 0.777, 11, 0.94]}, {"Target": "Parameter", "Id": "GL11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.64, 1, 3.667, 0.803, 7.333, 0.967, 11, 1.13]}, {"Target": "Parameter", "Id": "GL12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.15, 1, 3.667, 0.313, 7.333, 0.477, 11, 0.64]}, {"Target": "Parameter", "Id": "GL13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.585, 1, 3.667, 0.748, 7.333, 0.912, 11, 1.075]}, {"Target": "Parameter", "Id": "GL14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 3.667, 0.163, 7.333, 0.327, 11, 0.49]}, {"Target": "Parameter", "Id": "Param161", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.51, 1, 3.667, 0.714, 7.333, 0.918, 11, 1.122]}, {"Target": "Parameter", "Id": "Param162", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.31, 1, 3.667, 0.514, 7.333, 0.718, 11, 0.922]}, {"Target": "Parameter", "Id": "Param163", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.72, 1, 3.667, 0.924, 7.333, 1.128, 11, 1.332]}, {"Target": "Parameter", "Id": "Param164", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 3.667, 0.204, 7.333, 0.408, 11, 0.612]}, {"Target": "Parameter", "Id": "Param165", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.17, 1, 3.667, 0.374, 7.333, 0.578, 11, 0.782]}, {"Target": "Parameter", "Id": "touch_drag21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "man_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "man_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "man_<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "man_<PERSON>ze", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param189", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "shangguang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param186", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "MB_xingfengTMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param127", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Mouthfunnel", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param293", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param38", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param125", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param171", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param166", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param123", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 11, 1]}, {"Target": "Parameter", "Id": "Param13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param39", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param87", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param94", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.5, 0, 11, 0.5]}, {"Target": "Parameter", "Id": "Param20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param52", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param70", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param208", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "touch_drag23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "touch_drag24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "touch_drag25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "touch_drag26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "touch_drag27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "touch_drag28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "touch_drag29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "touch_drag30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "touch_drag31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "touch_drag32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "PartOpacity", "Id": "Part7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 11, 1]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 10.5, "Value": ""}]}