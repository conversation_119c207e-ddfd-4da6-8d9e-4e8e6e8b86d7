{"Version": 3, "Meta": {"Duration": 13.0, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 286, "TotalSegmentCount": 3014, "TotalPointCount": 3462, "UserDataCount": 2, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.883, 0.2, 0, 2.067, 0, 2, 5.267, 0, 0, 6.133, -0.156, 1, 6.466, -0.156, 6.8, -0.046, 7.133, -0.041, 1, 9.089, -0.009, 11.044, 0, 13, 0]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.883, -0.145, 0, 2.067, 0, 2, 5.267, 0, 1, 5.556, 0, 5.844, 0.163, 6.133, 0.166, 1, 6.466, 0.171, 6.8, 0.17, 7.133, 0.17, 0, 10.667, -0.19, 2, 11.75, -0.19, 0, 13, 0]}, {"Target": "Parameter", "Id": "All_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.45, -0.272, 0, 0.95, 0.447, 0, 2.45, -0.506, 0, 2.917, 0.53, 0, 3.733, -0.332, 0, 4.383, -0.149, 0, 4.733, -0.241, 0, 6.067, 0.285, 0, 6.8, -0.401, 0, 7.133, -0.038, 0, 7.667, -0.316, 0, 10.517, 0.145, 0, 11.317, -0.092, 0, 12.117, 0.176, 0, 12.667, -0.053, 0, 13, 0]}, {"Target": "Parameter", "Id": "All_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.367, -0.123, 0, 0.733, 0.229, 0, 1.133, -0.219, 0, 1.817, 0.401, 0, 2.267, 0.321, 0, 2.75, 0.507, 0, 3.4, -0.265, 0, 4.017, 0.158, 0, 4.683, -0.044, 0, 5.083, 0.194, 0, 5.383, 0.103, 0, 5.8, 0.285, 0, 6.35, 0.146, 0, 6.767, 0.532, 0, 7.133, 0.006, 0, 7.633, 0.294, 0, 10.517, -0.271, 0, 11.1, 0.179, 0, 11.683, 0.011, 0, 12.483, 0.458, 0, 13, 0]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.883, 0.435, 0, 2.067, 0, 0, 3.333, 3.38, 1, 3.978, 3.38, 4.622, 3.327, 5.267, 3.03, 1, 5.556, 2.897, 5.844, 2.077, 6.133, 2, 1, 7.644, 1.595, 9.156, 1.406, 10.667, 0.941, 1, 11.445, 0.701, 12.222, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.883, 5, 0, 2.933, -3.23, 0, 4.183, -2.863, 0, 5.267, -5.303, 1, 5.556, -5.303, 5.844, 12.914, 6.133, 16.227, 1, 6.466, 20.05, 6.8, 19.719, 7.133, 19.719, 0, 9.25, 15.877, 0, 10.667, 21.196, 1, 11.028, 21.196, 11.389, 21.575, 11.75, 20.034, 1, 12.167, 18.256, 12.583, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamAngleX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.304, 0, 0.317, -7.891, 0, 0.817, 14.216, 0, 2.317, -15.09, 0, 2.783, 16.76, 0, 3.583, -9.76, 0, 4.25, -4.125, 0, 4.6, -6.93, 0, 4.867, -4.735, 0, 5.15, -6.907, 0, 5.933, 19.748, 0, 6.683, -11.521, 0, 7.017, -0.544, 0, 8.05, -3.776, 0, 9.367, 4.907, 0, 10.183, -3.079, 0, 11.283, 2.221, 0, 11.8, 0.196, 0, 11.983, 1.384, 0, 12.683, -23.17, 0, 13, -1.425]}, {"Target": "Parameter", "Id": "ParamAngleY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.124, 0, 0.233, -3.335, 0, 0.6, 7.514, 0, 1, -6.278, 0, 1.7, 12.806, 0, 2.133, 10.351, 0, 2.617, 16.072, 0, 3.25, -7.702, 0, 3.883, 5.33, 0, 4.55, -0.867, 0, 4.917, 6.042, 0, 5.233, 2.121, 0, 5.6, 7.744, 0, 6.167, 3.891, 0, 6.617, 17.398, 1, 6.745, 17.398, 6.872, 2.189, 7, 1.035, 1, 7.211, -0.872, 7.422, -0.85, 7.633, -0.85, 0, 8.667, 2.503, 0, 9.55, -8.069, 0, 11.283, 7.863, 0, 11.867, 3.724, 0, 12.617, 16.664, 0, 13, -0.298]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.543, 0, 0.3, -3.35, 0, 0.7, -1.899, 0, 1.083, -5.918, 1, 1.3, -5.918, 1.516, -3.664, 1.733, -3.342, 1, 1.972, -2.987, 2.211, -3.007, 2.45, -2.728, 1, 2.539, -2.624, 2.628, 6, 2.717, 6, 1, 2.861, 6, 3.006, -4.235, 3.15, -7, 1, 3.272, -9.34, 3.395, -9, 3.517, -9, 0, 3.967, -4.851, 0, 4.433, -6.136, 0, 4.817, -4.097, 0, 5.417, -4.855, 0, 5.8, 2.8, 0, 6.217, -6.2, 1, 6.378, -6.2, 6.539, -2.03, 6.7, -1.2, 1, 6.956, 0.117, 7.211, 0.144, 7.467, 0.144, 0, 8.683, -7.427, 0, 9.417, -3.112, 1, 9.578, -3.112, 9.739, -7.59, 9.9, -9.007, 1, 10.183, -11.498, 10.467, -11.757, 10.75, -11.757, 1, 10.889, -11.757, 11.028, -5.723, 11.167, -3.912, 1, 11.378, -1.16, 11.589, -1, 11.8, -1, 0, 12.233, -2, 0, 12.667, 2, 0, 13, 0.543]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -4.357, 0, 0.517, -0.087, 0, 0.9, -10.506, 1, 1.117, -10.506, 1.333, 3.398, 1.55, 4.273, 1, 1.789, 5.237, 2.028, 5.164, 2.267, 5.867, 1, 2.356, 6.129, 2.444, 9, 2.533, 9, 1, 2.678, 9, 2.822, -6.595, 2.967, -9.735, 1, 3.089, -12.392, 3.211, -11.942, 3.333, -11.942, 0, 3.783, -7.739, 0, 4.383, -9.438, 0, 4.8, -8.239, 0, 5.233, -9.2, 0, 5.617, -1.048, 0, 6.033, -9.2, 1, 6.216, -9.2, 6.4, -5.45, 6.583, -4.2, 1, 6.844, -2.419, 7.106, -2.339, 7.367, -2.339, 0, 8.55, -10.378, 0, 9.283, -6.063, 1, 9.444, -6.063, 9.606, -10.6, 9.767, -11.958, 1, 10.061, -14.439, 10.356, -14.708, 10.65, -14.708, 1, 10.789, -14.708, 10.928, -8.948, 11.067, -6.863, 1, 11.25, -4.112, 11.434, -4, 11.617, -4, 0, 12.05, -8, 0, 12.567, -0.775, 0, 13, -4.357]}, {"Target": "Parameter", "Id": "ParamBodyAngleX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -8.417, 0, 0.183, -13, 0, 0.633, 8.898, 1, 0.822, 8.898, 1.011, -2.416, 1.2, -3.294, 1, 1.522, -4.791, 1.845, -4.877, 2.167, -6.258, 1, 2.306, -6.853, 2.444, -22.203, 2.583, -22.203, 0, 2.95, 28.811, 0, 3.533, 0.722, 0, 4.083, 3.784, 1, 4.344, 3.784, 4.606, 3.695, 4.867, 2.496, 1, 5, 1.884, 5.134, -1.72, 5.267, -1.72, 0, 5.733, 20, 0, 6.45, -7.606, 0, 6.8, 1.013, 0, 11.283, -3.002, 0, 11.75, 0, 0, 12.333, -14.891, 0, 12.8, 1.013, 0, 13, -8.417]}, {"Target": "Parameter", "Id": "ParamBodyAngleY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -9.558, 0, 0.417, 8.992, 0, 0.9, -9, 0, 1.483, -0.032, 0, 1.933, -3.696, 0, 2.433, 4.939, 1, 2.583, 4.939, 2.733, -6.69, 2.883, -18.303, 1, 2.983, -26.045, 3.083, -26.947, 3.183, -26.947, 0, 3.7, 1.602, 0, 4.383, -7.125, 0, 4.767, 0.512, 0, 5.083, -4.082, 0, 5.533, 6, 0, 5.75, 2, 1, 5.844, 2, 5.939, 6.159, 6.033, 9, 1, 6.172, 13.178, 6.311, 14, 6.45, 14, 0, 6.867, -2, 0, 9.133, -1.879, 0, 9.65, -9.071, 1, 9.756, -9.071, 9.861, 2.459, 9.967, 3.289, 1, 10.267, 5.647, 10.567, 6, 10.867, 6, 0, 11.283, -8, 0, 11.633, 4, 0, 12.033, -1, 0, 12.45, 14, 0, 13, -9.558]}, {"Target": "Parameter", "Id": "ParamWaistZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -13.805, 0, 0.2, -15.74, 0, 0.683, -2.856, 0, 1.317, -16.65, 0, 1.617, -15.519, 0, 2.15, -17.675, 0, 2.717, -6.257, 0, 3.083, -15.102, 0, 3.6, -14.028, 0, 4.833, -17.441, 0, 5.183, -16.532, 0, 5.333, -17.024, 0, 5.8, -4.965, 0, 6.4, -19.613, 0, 7.683, -11.504, 0, 8.833, -15.251, 0, 9.917, -5.976, 0, 11.267, -14.259, 0, 11.683, -10.531, 0, 12.283, -20.693, 0, 13, -13.518]}, {"Target": "Parameter", "Id": "ParamShoulderStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.893, 0, 0.283, -0.235, 0, 0.783, -9.529, 0, 1.35, 1.943, 0, 1.8, 1.239, 0, 2.3, 2.897, 1, 2.45, 2.897, 2.6, -5.111, 2.75, -6.639, 1, 2.85, -7.658, 2.95, -7.365, 3.05, -7.365, 0, 3.567, -2.298, 0, 4.25, -4.735, 0, 4.633, -2.881, 0, 4.95, -4.164, 0, 5.4, -3.573, 0, 5.9, -9.971, 0, 6.333, 2.28, 0, 6.917, -6.765, 0, 8.817, -5.668, 0, 9.367, -8.35, 0, 9.85, -4.623, 0, 10.567, -15.764, 0, 11.55, -6.232, 0, 11.9, -11.507, 0, 12.417, 1.9, 0, 13, -2.893]}, {"Target": "Parameter", "Id": "ParamShoulderStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.371, 0, 0.483, -0.235, 0, 0.983, -9.529, 0, 1.55, 1.943, 0, 2, 1.239, 0, 2.5, 2.897, 1, 2.65, 2.897, 2.8, -5.111, 2.95, -6.639, 1, 3.05, -7.658, 3.15, -7.365, 3.25, -7.365, 0, 3.767, -2.298, 0, 4.45, -4.735, 0, 4.833, -2.881, 0, 5.15, -4.164, 0, 5.6, -3.573, 0, 6.1, -10.317, 0, 6.533, 0.605, 0, 7.017, -7.306, 0, 8.9, -6.324, 0, 9.433, -9.006, 0, 9.9, -5.279, 0, 10.633, -16.42, 0, 11.633, -6.888, 0, 12.017, -12.163, 0, 12.517, 1.704, 0, 13, -3.371]}, {"Target": "Parameter", "Id": "ParamButtXA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -6.39, 0, 0.183, -5.227, 0, 0.617, -13, 0, 1.183, -8.63, 0, 1.667, -9.332, 0, 2.15, -7.243, 0, 2.567, -15.371, 0, 2.917, -7.944, 0, 3.4, -11, 0, 4.067, -8, 0, 4.5, -9, 0, 4.817, -5.725, 0, 5.167, -7.701, 0, 5.417, -4.935, 0, 5.75, -13, 0, 6.333, 1.33, 0, 6.7, -3.539, 0, 7.033, -2.215, 0, 7.7, -13.778, 0, 9.083, -7.104, 0, 9.733, -13.137, 0, 11.2, -1.683, 0, 11.733, -6.285, 0, 12.217, 2, 0, 12.7, -8.518, 0, 13, -6.39]}, {"Target": "Parameter", "Id": "Param43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.915, 0, 0.25, -0.088, 0, 0.75, -10, 0, 1.317, 2.265, 0, 1.783, 1.26, 0, 2.267, 2.726, 0, 2.717, -7, 0, 3.017, -2, 0, 3.533, -6, 0, 4.217, -3.029, 0, 4.6, -5, 0, 4.917, -2.104, 0, 5.25, -6, 0, 5.5, -0.499, 0, 5.85, -6, 0, 6.417, 0.272, 0, 6.8, -1.253, 0, 7.133, 0.087, 0, 7.9, -6.476, 0, 9.283, -0.015, 0, 10.633, -11.047, 0, 11.3, -2.127, 0, 11.85, -6.247, 0, 12.3, 3.44, 0, 12.8, -3.148, 0, 13, -1.915]}, {"Target": "Parameter", "Id": "ParamButtZA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.239, 0, 0.45, -7, 0, 0.917, -1.802, 0, 1.467, -2.377, 0, 1.917, -0.482, 0, 2.35, -2, 0, 2.833, 5, 0, 3.133, -1, 0, 4.333, 1.404, 0, 4.683, -0.029, 0, 5.017, 0.42, 0, 5.317, -5, 0, 5.717, 8.117, 0, 6.317, -10.914, 0, 6.917, -4.449, 0, 7.3, -6.913, 0, 7.933, 0.621, 0, 9.133, -3.464, 0, 10, -0.678, 0, 10.6, -7.869, 0, 11.833, -2.782, 0, 12.467, -12, 0, 13, -5.239]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.833, 15, 2, 1.433, 15, 2, 2.95, 15, 2, 2.967, 0, 2, 6.167, 0, 2, 6.183, 30, 2, 12.483, 30, 2, 12.5, 15, 2, 13, 15]}, {"Target": "Parameter", "Id": "Param58", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.833, 15, 0, 1.433, 0, 2, 2.667, 0, 0, 3.25, 7.65, 2, 6.167, 7.65, 0, 6.75, 30, 2, 11.833, 30, 1, 11.939, 30, 12.044, 17.017, 12.15, 10, 1, 12.283, 1.136, 12.417, 0, 12.55, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.833, 0, 2, 1.433, 0, 2, 2.667, 0, 0, 3.25, -7, 2, 6.167, -7, 0, 6.75, 0, 2, 11.833, 0, 2, 12.55, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 24.978, 0, 0.267, 30, 0, 0.667, 0, 0, 1.433, 12.72, 2, 2.667, 12.72, 0, 3.25, 10.9, 2, 6.167, 10.9, 0, 6.75, 23, 2, 11.783, 23, 2, 12.15, 23, 0, 13, 24.978]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -9.422, 1, 0.222, -9.422, 0.445, 7.772, 0.667, 9, 1, 0.922, 10.412, 1.178, 10.2, 1.433, 10.2, 2, 2.667, 10.2, 1, 2.761, 10.2, 2.856, 1.566, 2.95, -3.48, 1, 3.05, -8.823, 3.15, -9.3, 3.25, -9.3, 2, 5.95, -9.3, 0, 6.367, -13.443, 1, 6.428, -13.443, 6.489, -2.189, 6.55, -1.621, 1, 6.644, -0.743, 6.739, -0.78, 6.833, -0.78, 2, 11.783, -0.78, 2, 12.15, -0.78, 0, 13, -9.422]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.038, 0, 0.783, 20.925, 1, 0.972, 20.925, 1.161, 20.477, 1.35, 19.355, 1, 1.789, 16.749, 2.228, 14.775, 2.667, 11.287, 1, 2.861, 9.742, 3.056, -3.125, 3.25, -3.145, 1, 4.1, -3.231, 4.95, -3.25, 5.8, -3.25, 0, 6.533, 2.278, 0, 9.733, 1.249, 0, 11.7, 2.285, 0, 12.067, 1, 0, 12.633, 3.707, 0, 13, 2.038]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.871, 0, 0.267, -6, 1, 0.678, -6, 1.089, 0.567, 1.5, 2.075, 1, 1.889, 3.5, 2.278, 3.291, 2.667, 3.291, 2, 2.683, -57.142, 1, 2.772, -57.142, 2.861, -43.161, 2.95, -39.174, 1, 3.05, -34.688, 3.15, -30.34, 3.25, -30.24, 1, 4.033, -29.454, 4.817, -28.917, 5.6, -28.14, 1, 5.7, -28.041, 5.8, -27.041, 5.9, -27.041, 1, 6, -27.041, 6.1, -26.546, 6.2, -30.76, 1, 6.317, -35.677, 6.433, -56.455, 6.55, -56.455, 1, 6.7, -56.455, 6.85, -53.653, 7, -47.526, 2, 7.017, 12.8, 0, 9.733, 9.7, 1, 10.416, 9.7, 11.1, 9.974, 11.783, 10.9, 1, 11.905, 11.066, 12.028, 13, 12.15, 13, 0, 12.717, -3.413, 0, 13, 0.871]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.131, 0, 0.417, -18.2, 1, 0.728, -18.2, 1.039, -2.89, 1.35, 0.31, 1, 1.561, 2.481, 1.772, 1.941, 1.983, 3.471, 1, 2.211, 5.121, 2.439, 7.744, 2.667, 7.744, 0, 2.95, -13.649, 0, 3.25, -9.12, 0, 5.55, -16.02, 1, 5.7, -16.02, 5.85, -15.803, 6, -11, 1, 6.1, -7.798, 6.2, 8, 6.3, 8, 0, 6.55, -2, 1, 6.722, -2, 6.895, 22.652, 7.067, 23.116, 1, 7.956, 25.506, 8.844, 26.14, 9.733, 26.14, 0, 11.867, 23.5, 0, 12.233, 26.8, 0, 12.8, -7.999, 0, 13, 0.131]}, {"Target": "Parameter", "Id": "Param5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.433, -2.52, 2, 2.667, -2.52, 0, 3.25, -19.56, 2, 6.167, -19.56, 0, 6.75, -19.6, 2, 11.833, -19.6, 1, 12.011, -19.6, 12.189, -19.917, 12.367, -16, 1, 12.578, -11.348, 12.789, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.85, 0, 0, 1.2, -30, 2, 2.667, -30, 2, 2.95, -30, 0, 3.25, 30, 2, 6.167, 30, 0, 6.75, 0, 2, 11.833, 0, 2, 12.133, 0, 0, 12.3, -8.033, 0, 12.533, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.85, 0, 0, 1.2, -22.26, 0, 2.667, 0, 0, 2.95, -30, 2, 3.25, -30, 2, 6.167, -30, 0, 6.75, 0, 2, 11.833, 0, 0, 12.133, -30, 0, 12.3, 0, 0, 12.533, -30, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamHandRREDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.583, 7, 2, 1.433, 7, 2, 2.95, 7, 2, 2.967, 3, 2, 6.167, 3, 2, 6.75, 2, 2, 12.367, 2, 2, 12.383, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Param101", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 6.75, 0, 0, 7.05, 1, 2, 11.933, 1, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param55", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.667, 0, 0, 3.25, 20.39, 1, 4.222, 20.39, 5.195, 18.246, 6.167, 11, 1, 6.361, 9.551, 6.556, 0, 6.75, 0, 2, 12.15, 0, 0, 12.383, 15.99, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 3.233, 0, 2, 3.25, 5.2, 2, 6.5, 5.2, 2, 6.517, 20, 2, 12.483, 20, 2, 12.5, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.667, 0, 0, 2.95, 6.54, 0, 3.25, 0, 2, 6.167, 0, 1, 6.361, 0, 6.556, 4.125, 6.75, 4.38, 1, 6.889, 4.562, 7.028, 4.5, 7.167, 4.5, 1, 8.828, 4.5, 10.489, 4.466, 12.15, 4.38, 1, 12.283, 4.373, 12.417, 0, 12.55, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.667, 0, 0, 2.95, 1, 0, 3.25, -2.94, 2, 6.167, -2.94, 1, 6.361, -2.94, 6.556, -7.196, 6.75, -11.58, 1, 6.889, -14.712, 7.028, -15.12, 7.167, -15.12, 1, 8.828, -15.12, 10.489, -14.123, 12.15, -11.58, 1, 12.283, -11.376, 12.417, 0, 12.55, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.089, 0, 0.4, -2, 0, 1.1, -0.168, 1, 1.506, -0.168, 1.911, -0.368, 2.317, -1, 1, 2.378, -1.095, 2.439, -3, 2.5, -3, 0, 2.933, 2, 0, 3.333, 0.945, 2, 5.667, 0.945, 1, 5.945, 0.945, 6.222, 1.621, 6.5, 4, 1, 6.567, 4.571, 6.633, 6.974, 6.7, 6.974, 1, 6.844, 6.974, 6.989, 3.895, 7.133, 3.815, 1, 8.45, 3.085, 9.766, 2.825, 11.083, 2.825, 1, 11.294, 2.825, 11.506, 2.868, 11.717, 3.185, 1, 11.839, 3.368, 11.961, 4.015, 12.083, 4.415, 1, 12.216, 4.852, 12.35, 5.056, 12.483, 5.056, 0, 13, -0.089]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.769, 0, 0.4, -0.251, 0, 1.1, 2.44, 1, 1.528, 2.44, 1.955, 2.015, 2.383, 0.769, 1, 2.444, 0.591, 2.506, -0.851, 2.567, -0.851, 0, 3.017, 5.82, 1, 3.15, 5.82, 3.284, -0.842, 3.417, -1.14, 1, 4.167, -2.818, 4.917, -3.3, 5.667, -3.3, 1, 5.945, -3.3, 6.222, -1.925, 6.5, 1.8, 1, 6.6, 3.141, 6.7, 4.544, 6.8, 4.544, 0, 7.233, -5.88, 0, 11.083, -1.785, 1, 11.322, -1.785, 11.561, -1.932, 11.8, -2.88, 1, 11.917, -3.343, 12.033, -4.68, 12.15, -4.68, 0, 12.65, 5.449, 0, 13, 0.769]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.105, 0, 0.4, 8.475, 0, 1.1, -5, 0, 1.783, 0.651, 0, 2.483, -3.105, 0, 2.783, 7.816, 0, 3.133, -0.58, 1, 3.266, -0.58, 3.4, 20.969, 3.533, 22.601, 1, 3.733, 25.048, 3.933, 24.96, 4.133, 24.96, 0, 5.667, 20.82, 0, 6.5, 27.66, 0, 6.917, 3.84, 1, 7.061, 3.84, 7.206, 24.57, 7.35, 25, 1, 8.594, 28.702, 9.839, 30, 11.083, 30, 2, 11.867, 30, 1, 11.995, 30, 12.122, 30, 12.25, 26, 1, 12.428, 19.14, 12.605, -11, 12.783, -11, 0, 13, -3.105]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.667, 0, 0, 3.25, -3.96, 2, 6.167, -3.96, 0, 6.75, -13.44, 2, 11.783, -13.44, 2, 12.15, -13.44, 0, 12.55, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.667, 0, 0, 2.783, 29, 1, 3.011, 29, 3.239, -17.467, 3.467, -28, 1, 3.545, -30, 3.622, -30, 3.7, -30, 2, 4.133, -30, 2, 5.667, -30, 1, 5.834, -30, 6, -30, 6.167, -28, 1, 6.284, -26.541, 6.4, -10.548, 6.517, 2, 1, 6.567, 7.378, 6.617, 30, 6.667, 30, 0, 6.933, -23, 0, 11.783, 0, 2, 12.15, 0, 2, 12.55, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.667, 0, 0, 2.783, -30, 1, 3.011, -30, 3.239, -20.243, 3.467, 0, 1, 3.689, 19.75, 3.911, 30, 4.133, 30, 0, 5.667, 20, 0, 6.167, 30, 1, 6.284, 30, 6.4, 26.135, 6.517, 7, 1, 6.567, -1.201, 6.617, -30, 6.667, -30, 2, 6.933, -30, 0, 11.783, 0, 2, 12.15, 0, 2, 12.55, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamHandLREDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 2.767, 1, 2, 2.783, 3, 2, 6.933, 3, 2, 6.95, 2, 2, 12.367, 2, 2, 12.383, 1, 2, 13, 1]}, {"Target": "Parameter", "Id": "Param100", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 6.75, 0, 0, 7.05, 1, 2, 11.933, 1, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 4.445, 0, 0.467, 3.065, 0, 1.283, 6.082, 0, 1.833, 4.728, 0, 2.417, 7.859, 0, 2.683, 7.08, 0, 2.983, 8.666, 1, 3.244, 8.666, 3.506, 3.876, 3.767, 3.293, 1, 4, 2.772, 4.234, 2.889, 4.467, 2.889, 0, 5.417, 7.967, 0, 5.867, 4.309, 0, 6.3, 8.935, 0, 7.983, 0.208, 1, 8.122, 0.208, 8.261, 0.036, 8.4, 0.391, 1, 8.739, 1.258, 9.078, 6.466, 9.417, 6.466, 0, 10.083, 3.376, 0, 11.3, 7.689, 0, 11.8, 5.918, 0, 12.217, 9.575, 0, 12.883, 4.239, 0, 13, 4.445]}, {"Target": "Parameter", "Id": "Param2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.296, 0, 0.417, 5.235, 0, 1.283, -0.463, 1, 1.555, -0.463, 1.828, -0.528, 2.1, 0.8, 1, 2.3, 1.776, 2.5, 7.86, 2.7, 7.86, 0, 3.417, 1, 0, 5.8, 4.8, 0, 6.5, -3.701, 0, 8.117, 8.436, 1, 8.789, 8.436, 9.461, 8.434, 10.133, 7, 1, 11.089, 4.962, 12.044, 2.296, 13, 2.296]}, {"Target": "Parameter", "Id": "Param", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 7.479, 0, 0.317, 9.705, 0, 1.55, -2.661, 0, 1.8, -2.482, 0, 2.217, -3.433, 0, 4.433, 12.523, 1, 4.594, 12.523, 4.756, 12.844, 4.917, 12.259, 1, 5.372, 10.604, 5.828, -2.191, 6.283, -2.191, 0, 6.567, 0.488, 0, 9.167, -13.892, 0, 10.083, 3.273, 0, 10.867, 0.431, 0, 11.8, 8.007, 0, 12.417, -0.88, 0, 13, 7.479]}, {"Target": "Parameter", "Id": "Param6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.347, 0, 0.267, -4.73, 0, 0.8, -5.426, 0, 2.233, -4.492, 0, 3.517, -5.33, 0, 5.4, -4.741, 0, 5.967, -4.771, 0, 6.533, -4.627, 0, 7.95, -4.946, 0, 9.317, -4.633, 0, 9.9, -4.992, 0, 12.15, -4.713, 0, 12.75, -5.505, 0, 13, -5.347]}, {"Target": "Parameter", "Id": "Param48", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -8.069, 0, 0.283, -12.741, 0, 0.8, -7.487, 0, 2.233, -10, 0, 2.7, -4.56, 0, 3.517, -8.197, 0, 5.8, -3.91, 0, 6.5, -9.551, 0, 8.183, 0.666, 1, 8.794, 0.666, 9.406, 0.858, 10.017, -0.845, 1, 11.011, -3.616, 12.006, -8.069, 13, -8.069]}, {"Target": "Parameter", "Id": "Param37", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 8.923, 0, 0.583, 12.676, 0, 1.15, 10.573, 0, 2.617, 16.115, 0, 3.833, 8.858, 0, 4.35, 9.335, 1, 4.561, 9.335, 4.772, 9.621, 4.983, 8.265, 1, 5.411, 5.517, 5.839, 0.812, 6.267, 0.812, 0, 9.233, 20.791, 0, 10.133, 1.968, 0, 12.633, 12.107, 0, 13, 8.923]}, {"Target": "Parameter", "Id": "Param38", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.8, -4.185, 2, 11.717, -4.185, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param60", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 8.8, -13, 2, 11.717, -13, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param171", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.667, 1, 2, 11.933, 1, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.9, 2, 0.133, 0.9, 0, 0.483, 0, 0, 0.883, 0.9, 2, 1.817, 0.9, 2, 3.033, 0.9, 0, 3.35, 0, 0, 3.667, 0.9, 2, 4.517, 0.9, 0, 4.717, 0, 0, 4.933, 0.9, 2, 7.267, 0.9, 0, 7.467, 0, 0, 7.683, 0.9, 2, 9.083, 0.9, 0, 9.433, 0, 0, 9.833, 0.9, 2, 11.883, 0.9, 0, 12.233, 0, 0, 12.633, 0.9, 2, 13, 0.9]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.317, 0, 0, 0.617, 0.518, 0, 1.117, 0, 2, 1.217, 0, 0, 1.517, 0.518, 0, 1.817, 0, 2, 3.2, 0, 0, 3.45, 0.466, 0, 3.883, 0, 2, 4.617, 0, 0, 4.783, 0.518, 0, 5.067, 0, 2, 7.367, 0, 0, 7.533, 0.518, 0, 7.817, 0, 2, 9.267, 0, 0, 9.567, 0.518, 0, 10.067, 0, 2, 12.067, 0, 0, 12.367, 0.518, 0, 12.867, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.9, 2, 0.133, 0.9, 0, 0.483, 0, 0, 0.883, 0.9, 2, 1.817, 0.9, 2, 3.033, 0.9, 0, 3.35, 0, 0, 3.667, 0.9, 2, 4.517, 0.9, 0, 4.717, 0, 0, 4.933, 0.9, 2, 7.267, 0.9, 0, 7.467, 0, 0, 7.683, 0.9, 2, 9.083, 0.9, 0, 9.433, 0, 0, 9.833, 0.9, 2, 11.883, 0.9, 0, 12.233, 0, 0, 12.633, 0.9, 2, 13, 0.9]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.317, 0, 0, 0.617, 0.518, 0, 1.117, 0, 2, 1.217, 0, 0, 1.517, 0.518, 0, 1.817, 0, 2, 3.2, 0, 0, 3.45, 0.466, 0, 3.883, 0, 2, 4.617, 0, 0, 4.783, 0.518, 0, 5.067, 0, 2, 7.367, 0, 0, 7.533, 0.518, 0, 7.817, 0, 2, 9.267, 0, 0, 9.567, 0.518, 0, 10.067, 0, 2, 12.067, 0, 0, 12.367, 0.518, 0, 12.867, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.683, 0.07, 2, 1.283, 0.07, 0, 1.817, 0, 2, 3.033, 0, 0, 3.333, 0.07, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, 0.07, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, 0.07, 0, 7.683, 0, 2, 9.083, 0, 0, 9.433, 0.07, 0, 9.833, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.683, 0.364, 2, 1.283, 0.364, 0, 1.817, 0, 2, 3.033, 0, 0, 3.333, 0.364, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, 0.364, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, 0.364, 0, 7.683, 0, 2, 9.083, 0, 0, 9.433, 0.364, 0, 9.833, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Param4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.683, -0.586, 2, 1.283, -0.586, 0, 1.817, 0, 2, 3.033, 0, 0, 3.333, -0.586, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, -0.586, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, -0.586, 0, 7.683, 0, 2, 9.083, 0, 0, 9.433, -0.586, 0, 9.833, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.683, 0.262, 2, 1.283, 0.262, 0, 1.817, 0, 2, 3.033, 0, 0, 3.333, 0.262, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, 0.262, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, 0.262, 0, 7.683, 0, 2, 9.083, 0, 0, 9.433, 0.262, 0, 9.833, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.683, 0.366, 2, 1.283, 0.366, 0, 1.817, 0, 2, 3.033, 0, 0, 3.333, 0.366, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, 0.366, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, 0.366, 0, 7.683, 0, 2, 9.083, 0, 0, 9.433, 0.366, 0, 9.833, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Param11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.817, -0.572, 2, 1.283, -0.572, 0, 1.817, 0, 2, 3.033, 0, 0, 3.333, -0.572, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, -0.572, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, -0.572, 0, 7.683, 0, 2, 9.033, 0, 0, 9.233, -0.572, 0, 9.45, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.15, -0.244, 0, 0.333, -0.01, 0, 0.45, -0.319, 2, 0.633, -0.319, 2, 0.817, -0.319, 0, 0.9, 0.656, 0, 0.95, -0.319, 2, 1.133, -0.319, 2, 1.333, -0.319, 2, 1.367, -0.319, 2, 1.533, -0.319, 2, 1.683, -0.319, 0, 1.75, 0.34, 1, 1.817, 0.34, 1.883, 0.257, 1.95, -0.01, 1, 1.989, -0.166, 2.028, -0.319, 2.067, -0.319, 0, 2.283, -0.244, 0, 2.4, -0.319, 0, 2.617, 0.808, 2, 3.6, 0.808, 0, 3.8, 0, 0, 4.067, 1, 0, 4.15, 0.656, 0, 4.2, 1, 0, 4.383, -0.319, 0, 4.583, 0.224, 2, 4.717, 0.224, 0, 4.8, 0, 0, 4.883, 0.34, 0, 5.45, -0.244, 0, 5.75, 1, 2, 5.933, 1, 0, 6.117, -0.319, 0, 6.2, 0.656, 0, 6.25, -0.319, 2, 6.433, -0.319, 2, 6.633, -0.319, 2, 6.667, -0.319, 0, 6.767, 0.808, 0, 6.833, -0.319, 0, 6.967, 0, 0, 6.983, -0.319, 1, 7.005, -0.319, 7.028, 0.148, 7.05, 0.34, 1, 7.111, 0.869, 7.172, 1, 7.233, 1, 1, 7.239, 1, 7.244, 0.829, 7.25, 0.8, 1, 7.272, 0.685, 7.295, 0.656, 7.317, 0.656, 0, 7.367, 1, 0, 7.55, -0.319, 0, 7.75, 0.224, 2, 7.883, 0.224, 0, 7.967, 0, 0, 8.05, 0.34, 0, 8.117, 0, 2, 9.333, 0, 0, 9.467, -0.319, 2, 9.65, -0.319, 2, 9.833, -0.319, 0, 9.917, 0.656, 0, 9.967, -0.319, 2, 10.15, -0.319, 2, 10.35, -0.319, 0, 10.383, 0, 2, 10.967, 0, 0, 11.1, -0.319, 2, 11.283, -0.319, 2, 11.467, -0.319, 0, 11.55, 0.656, 0, 11.6, -0.319, 2, 11.783, -0.319, 2, 11.983, -0.319, 2, 12.017, -0.319, 0, 12.167, 0.6, 0, 12.283, -0.319, 2, 12.417, -0.319, 0, 12.567, 0.6, 2, 13, 0.6]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 0.05, 0, 0.1, 0.185, 0.15, 0.5, 1, 0.206, 0.85, 0.261, 1, 0.317, 1, 0, 0.5, 0.102, 0, 0.683, 0.969, 0, 0.9, 0.012, 0, 1.1, 0.663, 0, 1.183, 0.024, 0, 1.333, 0.871, 2, 1.367, 0.871, 0, 1.65, 0, 0, 1.933, 1, 0, 2.1, 0.102, 0, 2.283, 0.5, 1, 2.333, 0.5, 2.383, 0.188, 2.433, 0.102, 1, 2.494, 0, 2.556, 0, 2.617, 0, 2, 3.6, 0, 0, 3.8, 0.5, 0, 4.083, 0.323, 0, 4.2, 0.9, 0, 4.283, 0.016, 0, 4.35, 0.663, 0, 4.433, 0.024, 0, 4.583, 0.871, 2, 4.717, 0.871, 0, 4.8, 0, 2, 4.883, 0, 1, 5.072, 0, 5.261, 0.2, 5.45, 0.5, 1, 5.55, 0.659, 5.65, 0.777, 5.75, 0.9, 1, 5.811, 0.975, 5.872, 1, 5.933, 1, 0, 6.2, 0.012, 0, 6.4, 0.663, 0, 6.483, 0.024, 0, 6.633, 0.871, 2, 6.667, 0.871, 0, 6.767, 0, 2, 6.95, 0, 0, 7.017, 0.5, 0, 7.25, 0.323, 0, 7.367, 0.9, 0, 7.45, 0.016, 0, 7.517, 0.663, 0, 7.6, 0.024, 0, 7.75, 0.871, 2, 7.883, 0.871, 0, 7.967, 0, 2, 8.05, 0, 2, 8.117, 0, 2, 9.333, 0, 1, 9.394, 0, 9.456, 0, 9.517, 0.102, 1, 9.578, 0.224, 9.639, 0.969, 9.7, 0.969, 0, 9.917, 0.012, 0, 10.117, 0.663, 0, 10.2, 0.024, 0, 10.35, 0.871, 0, 10.383, 0, 2, 10.967, 0, 1, 11.028, 0, 11.089, 0, 11.15, 0.102, 1, 11.211, 0.224, 11.272, 0.969, 11.333, 0.969, 0, 11.55, 0.012, 0, 11.75, 0.663, 0, 11.833, 0.024, 0, 11.983, 0.871, 2, 12.017, 0.871, 0, 12.167, 0, 0, 12.283, 0.871, 2, 12.417, 0.871, 0, 12.567, 0, 2, 13, 0]}, {"Target": "Parameter", "Id": "Hair_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.183, 5.087, 0, 0.617, -9.604, 0, 1.183, 10.027, 0, 1.667, -2.715, 0, 2.15, 15.509, 0, 2.567, -14.084, 0, 2.917, 11.385, 0, 3.4, -17.935, 0, 4.067, 11.055, 0, 4.5, -6.168, 0, 4.817, 13.101, 0, 5.167, 1.475, 0, 5.417, 17.75, 0, 5.75, -9.604, 0, 6.333, 17.479, 0, 6.7, 0.28, 0, 7.033, 10.779, 0, 7.7, -3.304, 0, 9.083, 9.099, 0, 9.733, -2.303, 0, 11.2, 6.115, 0, 11.733, -2.583, 0, 12.217, 13.075, 0, 12.7, -6.803, 0, 13, 0]}, {"Target": "Parameter", "Id": "Hair_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.183, 2.298, 0, 0.617, -4.483, 0, 1.183, 4.579, 0, 1.667, -1.303, 0, 2.15, 7.109, 0, 2.567, -6.551, 0, 2.917, 5.205, 0, 3.4, -5.061, 0, 4.067, 5.053, 0, 4.5, -1.329, 0, 4.817, 1.435, 0, 5.167, -1.934, 0, 5.417, 2.782, 0, 5.75, -3.028, 0, 6.333, 8.018, 0, 6.7, 0.08, 0, 7.033, 4.926, 0, 7.7, -1.575, 0, 9.083, 4.15, 0, 9.733, -1.113, 0, 11.2, 2.773, 0, 11.733, -2.583, 0, 12.217, 13.075, 0, 12.7, -6.803, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param69", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.183, 2.298, 0, 0.617, -4.483, 0, 1.183, 4.579, 0, 1.667, -1.303, 0, 2.15, 7.109, 0, 2.567, -6.551, 0, 2.917, 5.205, 0, 3.4, -5.061, 0, 4.067, 5.053, 0, 4.5, -1.329, 0, 4.817, 1.435, 0, 5.167, -1.934, 0, 5.417, 2.782, 0, 5.75, -3.028, 0, 6.333, 8.018, 0, 6.7, 0.08, 0, 7.033, 4.926, 0, 7.7, -1.575, 0, 9.083, 4.15, 0, 9.733, -1.113, 0, 11.2, 2.773, 0, 11.733, -2.583, 0, 12.217, 13.075, 0, 12.7, -6.803, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param153", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.167, -6.225, 0, 0.5, 10.479, 0, 0.933, -12.79, 0, 1.433, 11.008, 0, 1.95, -13.354, 0, 2.417, 23.195, 0, 2.817, -27.6, 0, 3.217, 25.425, 0, 3.683, -18.849, 0, 4.317, 14.313, 0, 4.733, -20.799, 0, 5.1, 15.582, 0, 5.383, -17.426, 0, 5.683, 23.423, 0, 6.083, -19.971, 0, 6.567, 18.762, 0, 6.95, -14.966, 0, 7.367, 10.141, 0, 7.867, -4.225, 0, 8.283, -0.868, 0, 8.617, -1.489, 0, 9.433, 4.656, 0, 9.933, -2.574, 0, 10.283, -0.638, 0, 10.583, -1.116, 0, 11.533, 4.394, 0, 12.033, -10.071, 0, 12.5, 14.445, 0, 12.917, -11.565, 1, 12.945, -11.565, 12.972, -11.77, 13, -9.821]}, {"Target": "Parameter", "Id": "Param154", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.15, 3.979, 0, 0.433, -9.033, 0, 0.8, 10.324, 0, 1.233, -9.958, 0, 1.733, 9.308, 0, 2.283, -13.248, 0, 2.7, 22.727, 0, 3.083, -26.553, 0, 3.483, 23.186, 0, 3.933, -14.012, 0, 4.617, 13.263, 0, 4.983, -19.246, 0, 5.333, 20.609, 0, 5.633, -20.57, 0, 5.967, 19.39, 0, 6.4, -16.594, 0, 6.833, 18.4, 0, 7.217, -15.08, 0, 7.633, 8.874, 0, 8.083, -4.256, 0, 8.5, 1.602, 0, 8.95, -0.884, 0, 9.117, -0.643, 0, 9.3, -1.19, 0, 9.767, 2.673, 0, 10.15, -1.793, 0, 10.517, 0.929, 0, 10.95, -0.47, 0, 11.2, -0.187, 0, 11.433, -1.397, 0, 11.9, 4.728, 0, 12.35, -9.085, 0, 12.783, 11.975, 1, 12.855, 11.975, 12.928, 7.011, 13, -1.346]}, {"Target": "Parameter", "Id": "Param155", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.117, 0, 0, 0.367, 2.605, 0, 0.667, -3.991, 0, 0.983, 8.289, 0, 1.417, -10.266, 0, 1.883, 10.518, 0, 2.4, -13.129, 0, 2.85, 20.661, 0, 3.267, -24.982, 0, 3.683, 23.95, 0, 4.117, -17.677, 0, 4.7, 13.082, 0, 5.15, -17.482, 0, 5.517, 16.916, 0, 5.833, -14.463, 0, 6.183, 15.647, 0, 6.583, -17.351, 0, 7, 18.358, 0, 7.417, -15.858, 0, 7.833, 11.297, 0, 8.267, -6.301, 0, 8.7, 2.891, 0, 9.15, -1.533, 0, 9.9, 2.3, 0, 10.35, -2.09, 0, 10.75, 1.093, 0, 11.183, -0.679, 0, 11.367, -0.482, 0, 11.583, -0.82, 0, 12.05, 4.078, 0, 12.5, -8.342, 0, 12.95, 11.569, 1, 12.967, 11.569, 12.983, 12.147, 13, 10.689]}, {"Target": "Parameter", "Id": "Param156", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.154, 0, 0.167, 0, 2, 0.583, 0, 0, 0.817, -3.003, 0, 1.167, 7.066, 0, 1.583, -10.166, 0, 2.05, 11.485, 0, 2.55, -13.86, 0, 3, 19.756, 0, 3.433, -24.197, 0, 3.85, 24.715, 0, 4.3, -20.616, 0, 4.817, 15.827, 0, 5.3, -17.211, 0, 5.7, 15.436, 0, 6.033, -11.357, 0, 6.383, 12.654, 0, 6.767, -16.543, 0, 7.183, 18.249, 0, 7.6, -16.849, 0, 8.017, 13.295, 0, 8.467, -8.486, 0, 8.9, 4.511, 0, 9.35, -2.537, 0, 10.033, 2.082, 0, 10.483, -2.091, 0, 10.933, 1.413, 0, 11.383, -0.956, 0, 12.2, 3.208, 0, 12.667, -7.677, 1, 12.778, -7.677, 12.889, -0.621, 13, 8.089]}, {"Target": "Parameter", "Id": "Param82", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.167, -5.803, 0, 0.5, 9.769, 0, 0.933, -11.923, 0, 1.433, 10.262, 0, 1.95, -12.449, 0, 2.417, 21.623, 0, 2.817, -25.73, 0, 3.217, 23.702, 0, 3.683, -17.571, 0, 4.317, 13.343, 0, 4.733, -19.389, 0, 5.1, 14.526, 0, 5.383, -16.245, 0, 5.683, 21.836, 0, 6.083, -18.617, 0, 6.567, 17.491, 0, 6.95, -13.952, 0, 7.367, 9.454, 0, 7.867, -3.938, 0, 8.283, -0.809, 0, 8.617, -1.388, 0, 9.433, 4.34, 0, 9.933, -2.4, 0, 10.283, -0.594, 0, 10.583, -1.04, 0, 11.533, 4.096, 0, 12.033, -9.388, 0, 12.5, 13.466, 0, 12.917, -10.781, 1, 12.945, -10.781, 12.972, -10.973, 13, -9.155]}, {"Target": "Parameter", "Id": "Param83", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.15, 3.812, 0, 0.433, -8.654, 0, 0.8, 9.891, 0, 1.233, -9.541, 0, 1.733, 8.918, 0, 2.283, -12.693, 0, 2.7, 21.774, 0, 3.083, -25.44, 0, 3.483, 22.214, 0, 3.933, -13.424, 0, 4.617, 12.707, 0, 4.983, -18.439, 0, 5.333, 19.744, 0, 5.633, -19.708, 0, 5.967, 18.577, 0, 6.4, -15.898, 0, 6.833, 17.628, 0, 7.217, -14.448, 0, 7.633, 8.502, 0, 8.083, -4.077, 0, 8.5, 1.535, 0, 8.95, -0.847, 0, 9.117, -0.616, 0, 9.3, -1.14, 0, 9.767, 2.561, 0, 10.15, -1.718, 0, 10.517, 0.89, 0, 10.95, -0.451, 0, 11.2, -0.18, 0, 11.433, -1.338, 0, 11.9, 4.53, 0, 12.35, -8.704, 0, 12.783, 11.473, 1, 12.855, 11.473, 12.928, 6.717, 13, -1.289]}, {"Target": "Parameter", "Id": "Param84", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.117, 0, 0, 0.367, 2.663, 0, 0.667, -4.08, 0, 0.983, 8.473, 0, 1.417, -10.494, 0, 1.883, 10.752, 0, 2.4, -13.421, 0, 2.85, 21.12, 0, 3.267, -25.537, 0, 3.683, 24.482, 0, 4.117, -18.07, 0, 4.7, 13.373, 0, 5.15, -17.87, 0, 5.517, 17.292, 0, 5.833, -14.785, 0, 6.183, 15.994, 0, 6.583, -17.736, 0, 7, 18.766, 0, 7.417, -16.211, 0, 7.833, 11.548, 0, 8.267, -6.441, 0, 8.7, 2.955, 0, 9.15, -1.567, 0, 9.9, 2.352, 0, 10.35, -2.137, 0, 10.75, 1.118, 0, 11.183, -0.694, 0, 11.367, -0.493, 0, 11.583, -0.838, 0, 12.05, 4.168, 0, 12.5, -8.527, 0, 12.95, 11.827, 1, 12.967, 11.827, 12.983, 12.416, 13, 10.926]}, {"Target": "Parameter", "Id": "Param85", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.157, 0, 0.167, 0, 2, 0.583, 0, 0, 0.817, -3.062, 0, 1.167, 7.204, 0, 1.583, -10.365, 0, 2.05, 11.71, 0, 2.55, -14.131, 0, 3, 20.143, 0, 3.433, -24.671, 0, 3.85, 25.198, 0, 4.3, -21.019, 0, 4.817, 16.137, 0, 5.3, -17.548, 0, 5.7, 15.738, 0, 6.033, -11.579, 0, 6.383, 12.901, 0, 6.767, -16.866, 0, 7.183, 18.606, 0, 7.6, -17.179, 0, 8.017, 13.555, 0, 8.467, -8.652, 0, 8.9, 4.599, 0, 9.35, -2.586, 0, 10.033, 2.123, 0, 10.483, -2.132, 0, 10.933, 1.44, 0, 11.383, -0.974, 0, 12.2, 3.271, 0, 12.667, -7.827, 1, 12.778, -7.827, 12.889, -0.633, 13, 8.247]}, {"Target": "Parameter", "Id": "Param66", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.1, 0, 0, 0.267, 6.231, 0, 0.5, -12.682, 0, 0.817, 15.479, 0, 1.167, -12.498, 0, 1.65, 6.079, 0, 2.3, -10.755, 0, 2.733, 23.165, 0, 3.05, -30, 2, 3.133, -30, 0, 3.45, 27.527, 0, 3.817, -14.811, 0, 4.15, 3.162, 0, 4.367, -2.182, 0, 4.667, 7.711, 0, 5.017, -11.783, 0, 5.367, 15.159, 0, 5.683, -20.63, 0, 5.983, 22.911, 0, 6.333, -17.607, 0, 6.867, 14.601, 0, 7.217, -18.555, 0, 7.55, 12.909, 0, 7.917, -4.621, 0, 8.317, 1.618, 0, 8.7, -0.768, 0, 9.033, -0.032, 0, 9.383, -1.535, 0, 9.767, 2.82, 0, 10.133, -2.304, 0, 10.45, 0.918, 0, 10.933, -0.323, 0, 11.233, -0.106, 0, 11.517, -2.21, 0, 11.967, 8.102, 0, 12.367, -18.855, 0, 12.783, 24.803, 1, 12.855, 24.803, 12.928, 13.211, 13, -6.319]}, {"Target": "Parameter", "Id": "Param67", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.133, -10.118, 0, 0.383, 17.571, 0, 0.7, -16.009, 0, 1.333, 8.212, 0, 1.817, -11.681, 0, 2.317, 22.918, 0, 2.65, -30, 2, 2.733, -30, 0, 2.967, 30, 2, 3.05, 30, 0, 3.35, -11.697, 0, 3.717, -2.98, 0, 3.783, -3.073, 0, 4.217, 10.87, 0, 4.6, -12.001, 0, 4.933, 12.51, 0, 5.3, -19.18, 0, 5.567, 26.887, 0, 5.867, -24.759, 0, 6.183, 5.423, 0, 6.3, 3.713, 0, 6.517, 15.17, 0, 6.817, -21.793, 0, 7.117, 17.599, 0, 7.4, -3.898, 0, 7.617, 1.673, 0, 7.833, -3.329, 0, 8.083, 0.013, 0, 8.317, -2.308, 0, 8.617, -0.044, 0, 8.8, -0.437, 0, 9.317, 4.427, 0, 9.767, -2.955, 0, 10.033, 0.495, 0, 10.3, -1.269, 0, 10.6, -0.248, 0, 10.767, -0.418, 0, 11.433, 6.251, 0, 11.933, -20.895, 0, 12.35, 29.545, 0, 12.783, -27.108, 1, 12.855, -27.108, 12.928, -9.713, 13, 12.415]}, {"Target": "Parameter", "Id": "Param68", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.117, 4.412, 0, 0.3, -11.236, 0, 0.55, 14.175, 0, 0.85, -11.203, 0, 1.117, 3.276, 0, 1.317, -1.699, 0, 1.567, 4.465, 0, 2, -5.967, 0, 2.517, 14.326, 0, 2.867, -22.836, 0, 3.167, 23.398, 0, 3.45, -10.232, 0, 3.733, 2.758, 0, 4.017, -3.99, 0, 4.4, 6.599, 0, 4.767, -8, 0, 5.083, 8.951, 0, 5.467, -17.468, 0, 5.733, 22.591, 0, 6.017, -19.414, 0, 6.283, 7.993, 0, 6.483, -5.223, 0, 6.717, 12.657, 0, 7, -16.614, 0, 7.267, 14.346, 0, 7.533, -7.79, 0, 7.767, 5.037, 0, 8.017, -3.068, 0, 8.267, 2.541, 0, 8.517, -1.968, 0, 8.767, 0.566, 0, 9.017, -0.69, 0, 9.117, -0.496, 0, 9.2, -0.687, 0, 9.483, 2.305, 0, 9.917, -1.441, 0, 10.183, 1.028, 0, 10.433, -0.581, 0, 10.7, 0.071, 0, 10.967, -0.367, 0, 11.2, -0.005, 0, 11.35, -1.435, 0, 11.633, 3.383, 0, 11.75, 1.991, 0, 11.85, 2.95, 0, 12.117, -12.711, 0, 12.517, 14.834, 0, 12.967, -16.007, 1, 12.978, -16.007, 12.989, -16.886, 13, -14.559]}, {"Target": "Parameter", "Id": "Param62", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.1, 0, 0, 0.267, -6.231, 0, 0.5, 12.682, 0, 0.817, -15.479, 0, 1.167, 12.498, 0, 1.65, -6.079, 0, 2.3, 10.755, 0, 2.733, -23.165, 0, 3.05, 30, 2, 3.133, 30, 0, 3.45, -27.527, 0, 3.817, 14.811, 0, 4.15, -3.162, 0, 4.367, 2.182, 0, 4.667, -7.711, 0, 5.017, 11.783, 0, 5.367, -15.159, 0, 5.683, 20.63, 0, 5.983, -22.911, 0, 6.333, 17.607, 0, 6.867, -14.601, 0, 7.217, 18.555, 0, 7.55, -12.909, 0, 7.917, 4.621, 0, 8.317, -1.618, 0, 8.7, 0.768, 0, 9.033, 0.032, 0, 9.383, 1.535, 0, 9.767, -2.82, 0, 10.133, 2.304, 0, 10.45, -0.918, 0, 10.933, 0.323, 0, 11.233, 0.106, 0, 11.517, 2.21, 0, 11.967, -8.102, 0, 12.367, 18.855, 0, 12.783, -24.803, 1, 12.855, -24.803, 12.928, -13.211, 13, 6.319]}, {"Target": "Parameter", "Id": "Param63", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.133, 10.118, 0, 0.383, -17.571, 0, 0.7, 16.009, 0, 1.333, -8.212, 0, 1.817, 11.681, 0, 2.317, -22.918, 0, 2.65, 30, 2, 2.733, 30, 0, 2.967, -30, 2, 3.05, -30, 0, 3.35, 11.697, 0, 3.717, 2.98, 0, 3.783, 3.073, 0, 4.217, -10.87, 0, 4.6, 12.001, 0, 4.933, -12.51, 0, 5.3, 19.18, 0, 5.567, -26.887, 0, 5.867, 24.759, 0, 6.183, -5.423, 0, 6.3, -3.713, 0, 6.517, -15.17, 0, 6.817, 21.793, 0, 7.117, -17.599, 0, 7.4, 3.898, 0, 7.617, -1.673, 0, 7.833, 3.329, 0, 8.083, -0.013, 0, 8.317, 2.308, 0, 8.617, 0.044, 0, 8.8, 0.437, 0, 9.317, -4.427, 0, 9.767, 2.955, 0, 10.033, -0.495, 0, 10.3, 1.269, 0, 10.6, 0.248, 0, 10.767, 0.418, 0, 11.433, -6.251, 0, 11.933, 20.895, 0, 12.35, -29.545, 0, 12.783, 27.108, 1, 12.855, 27.108, 12.928, 9.713, 13, -12.415]}, {"Target": "Parameter", "Id": "Param64", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.117, -4.412, 0, 0.3, 11.236, 0, 0.55, -14.175, 0, 0.85, 11.203, 0, 1.117, -3.276, 0, 1.317, 1.699, 0, 1.567, -4.465, 0, 2, 5.967, 0, 2.517, -14.326, 0, 2.867, 22.836, 0, 3.167, -23.398, 0, 3.45, 10.232, 0, 3.733, -2.758, 0, 4.017, 3.99, 0, 4.4, -6.599, 0, 4.767, 8, 0, 5.083, -8.951, 0, 5.467, 17.468, 0, 5.733, -22.591, 0, 6.017, 19.414, 0, 6.283, -7.993, 0, 6.483, 5.223, 0, 6.717, -12.657, 0, 7, 16.614, 0, 7.267, -14.346, 0, 7.533, 7.79, 0, 7.767, -5.037, 0, 8.017, 3.068, 0, 8.267, -2.541, 0, 8.517, 1.968, 0, 8.767, -0.566, 0, 9.017, 0.69, 0, 9.117, 0.496, 0, 9.2, 0.687, 0, 9.483, -2.305, 0, 9.917, 1.441, 0, 10.183, -1.028, 0, 10.433, 0.581, 0, 10.7, -0.071, 0, 10.967, 0.367, 0, 11.2, 0.005, 0, 11.35, 1.435, 0, 11.633, -3.383, 0, 11.75, -1.991, 0, 11.85, -2.95, 0, 12.117, 12.711, 0, 12.517, -14.834, 0, 12.967, 16.007, 1, 12.978, 16.007, 12.989, 16.886, 13, 14.559]}, {"Target": "Parameter", "Id": "Param86", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.183, -1.005, 0, 0.617, 1.961, 0, 1.183, -2.003, 0, 1.667, 0.57, 0, 2.15, -3.11, 0, 2.567, 2.866, 0, 2.917, -2.277, 0, 3.4, 2.213, 0, 4.067, -2.21, 0, 4.5, 0.581, 0, 4.817, -0.628, 0, 5.167, 0.846, 0, 5.417, -1.217, 0, 5.75, 1.325, 0, 6.333, -3.507, 0, 6.7, -0.035, 0, 7.033, -2.155, 0, 7.7, 0.689, 0, 9.083, -1.815, 0, 9.733, 0.487, 0, 11.2, -1.213, 0, 11.733, 1.13, 0, 12.217, -5.721, 0, 12.7, 2.976, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param89", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.183, -8.207, 0, 0.517, 10.516, 0, 0.883, -11.388, 0, 1.383, 8.287, 0, 1.917, -10.799, 0, 2.383, 20.298, 0, 2.783, -25.275, 0, 3.133, 19.523, 0, 3.583, -10.959, 0, 4.3, 8.935, 0, 4.7, -8.026, 0, 5.033, 7.961, 0, 5.367, -12.298, 0, 5.65, 13.566, 0, 6.017, -13.745, 0, 6.55, 14.234, 0, 6.9, -13.251, 0, 7.267, 8.627, 0, 7.783, -2.6, 0, 8.083, -0.943, 0, 8.217, -1.247, 0, 8.317, -0.964, 0, 8.583, -1.465, 0, 9.4, 4.176, 0, 9.867, -1.929, 0, 10.183, -0.578, 0, 10.283, -0.708, 0, 10.367, -0.522, 0, 10.667, -0.974, 0, 11.5, 5.413, 0, 12, -18.352, 0, 12.467, 26.39, 0, 12.883, -21.295, 1, 12.922, -21.295, 12.961, -19.376, 13, -12.394]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.183, 7.363, 0, 0.483, -8.228, 0, 0.75, 13.056, 0, 1.1, -9.989, 0, 1.583, 5.312, 0, 2.267, -9.685, 0, 2.65, 20.127, 0, 3, -26.168, 0, 3.35, 17.984, 0, 3.733, -8.366, 0, 4.05, -0.015, 0, 4.233, -2.182, 0, 4.567, 7.281, 0, 4.917, -9.323, 0, 5.283, 12.143, 0, 5.567, -16.493, 0, 5.867, 16.596, 0, 6.233, -10.674, 0, 6.767, 13.527, 0, 7.117, -14.29, 0, 7.45, 7.612, 0, 7.9, -2.602, 0, 8.217, 1.023, 0, 8.383, -0.25, 0, 8.583, -0.025, 0, 8.9, -0.357, 0, 9.083, -0.281, 0, 9.283, -1.414, 0, 9.7, 2.181, 0, 10.033, -1.565, 0, 10.283, 0.404, 0, 10.417, 0.201, 0, 10.517, 0.228, 0, 10.9, -0.288, 0, 11.2, -0.116, 0, 11.4, -2.218, 0, 11.883, 7.686, 0, 12.3, -16.025, 0, 12.717, 20.542, 1, 12.811, 20.542, 12.906, 2.489, 13, -17.8]}, {"Target": "Parameter", "Id": "Param88", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.45, 0, 0, 0.633, 8.334, 0, 0.883, -13.033, 0, 1.217, 11.227, 0, 1.617, -6.018, 0, 2.333, 7.548, 0, 2.75, -17.196, 0, 3.1, 24.33, 0, 3.45, -20.488, 0, 3.817, 11.033, 0, 4.15, -2.388, 0, 4.383, 1.616, 0, 4.667, -5.621, 0, 5.017, 8.639, 0, 5.367, -11.146, 0, 5.683, 15.177, 0, 5.983, -16.88, 0, 6.333, 12.989, 0, 6.867, -10.746, 0, 7.217, 13.654, 0, 7.55, -9.213, 0, 7.95, 3.463, 0, 8.317, -1.715, 0, 8.6, 0.613, 0, 8.85, 0.036, 0, 9.4, 1.005, 0, 9.783, -1.933, 0, 10.15, 1.662, 0, 10.55, -0.247, 0, 10.983, 0.276, 0, 11.233, 0.07, 0, 11.517, 1.621, 0, 11.967, -5.793, 0, 12.383, 13.912, 0, 12.8, -18.806, 1, 12.867, -18.806, 12.933, -10.681, 13, 3.632]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.6, 0, 0, 0.767, -6.546, 0, 1, 11.543, 0, 1.333, -12.082, 0, 1.683, 7.668, 0, 2.4, -5.92, 0, 2.833, 14.756, 0, 3.2, -22.471, 0, 3.55, 21.779, 0, 3.9, -13.789, 0, 4.233, 5.022, 0, 4.5, -2.141, 0, 4.783, 5.544, 0, 5.117, -8.184, 0, 5.467, 10.569, 0, 5.783, -14.204, 0, 6.1, 16.849, 0, 6.433, -14.825, 0, 6.9, 9.017, 0, 7.317, -12.496, 0, 7.65, 10.446, 0, 8.017, -5.137, 0, 8.4, 2.012, 0, 8.717, -1, 0, 9, 0.198, 0, 9.483, -0.746, 0, 9.867, 1.787, 0, 10.25, -1.693, 0, 10.517, 0.857, 0, 10.983, -0.217, 0, 11.317, -0.052, 0, 11.617, -1.253, 0, 12.033, 4.662, 0, 12.467, -11.996, 0, 12.883, 17.327, 1, 12.922, 17.327, 12.961, 16.294, 13, 9.11]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.183, 1.005, 0, 0.617, -1.961, 0, 1.183, 2.003, 0, 1.667, -0.57, 0, 2.15, 3.11, 0, 2.567, -2.866, 0, 2.917, 2.277, 0, 3.4, -2.213, 0, 4.067, 2.21, 0, 4.5, -0.581, 0, 4.817, 0.628, 0, 5.167, -0.846, 0, 5.417, 1.217, 0, 5.75, -1.325, 0, 6.333, 3.507, 0, 6.7, 0.035, 0, 7.033, 2.155, 0, 7.7, -0.689, 0, 9.083, 1.815, 0, 9.733, -0.487, 0, 11.2, 1.213, 0, 11.733, -1.13, 0, 12.217, 5.721, 0, 12.7, -2.976, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.183, -8.207, 0, 0.517, 10.516, 0, 0.883, -11.388, 0, 1.383, 8.287, 0, 1.917, -10.799, 0, 2.383, 20.298, 0, 2.783, -25.275, 0, 3.133, 19.523, 0, 3.583, -10.959, 0, 4.3, 8.935, 0, 4.7, -8.026, 0, 5.033, 7.961, 0, 5.367, -12.298, 0, 5.65, 13.566, 0, 6.017, -13.745, 0, 6.55, 14.234, 0, 6.9, -13.251, 0, 7.267, 8.627, 0, 7.783, -2.6, 0, 8.083, -0.943, 0, 8.217, -1.247, 0, 8.317, -0.964, 0, 8.583, -1.465, 0, 9.4, 4.176, 0, 9.867, -1.929, 0, 10.183, -0.578, 0, 10.283, -0.708, 0, 10.367, -0.522, 0, 10.667, -0.974, 0, 11.5, 5.413, 0, 12, -18.352, 0, 12.467, 26.39, 0, 12.883, -21.295, 1, 12.922, -21.295, 12.961, -19.376, 13, -12.394]}, {"Target": "Parameter", "Id": "Param126", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.183, 7.363, 0, 0.483, -8.228, 0, 0.75, 13.056, 0, 1.1, -9.989, 0, 1.583, 5.312, 0, 2.267, -9.685, 0, 2.65, 20.127, 0, 3, -26.168, 0, 3.35, 17.984, 0, 3.733, -8.366, 0, 4.05, -0.015, 0, 4.233, -2.182, 0, 4.567, 7.281, 0, 4.917, -9.323, 0, 5.283, 12.143, 0, 5.567, -16.493, 0, 5.867, 16.596, 0, 6.233, -10.674, 0, 6.767, 13.527, 0, 7.117, -14.29, 0, 7.45, 7.612, 0, 7.9, -2.602, 0, 8.217, 1.023, 0, 8.383, -0.25, 0, 8.583, -0.025, 0, 8.9, -0.357, 0, 9.083, -0.281, 0, 9.283, -1.414, 0, 9.7, 2.181, 0, 10.033, -1.565, 0, 10.283, 0.404, 0, 10.417, 0.201, 0, 10.517, 0.228, 0, 10.9, -0.288, 0, 11.2, -0.116, 0, 11.4, -2.218, 0, 11.883, 7.686, 0, 12.3, -16.025, 0, 12.717, 20.542, 1, 12.811, 20.542, 12.906, 2.489, 13, -17.8]}, {"Target": "Parameter", "Id": "Param128", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.45, 0, 0, 0.633, 8.334, 0, 0.883, -13.033, 0, 1.217, 11.227, 0, 1.617, -6.018, 0, 2.333, 7.548, 0, 2.75, -17.196, 0, 3.1, 24.33, 0, 3.45, -20.488, 0, 3.817, 11.033, 0, 4.15, -2.388, 0, 4.383, 1.616, 0, 4.667, -5.621, 0, 5.017, 8.639, 0, 5.367, -11.146, 0, 5.683, 15.177, 0, 5.983, -16.88, 0, 6.333, 12.989, 0, 6.867, -10.746, 0, 7.217, 13.654, 0, 7.55, -9.213, 0, 7.95, 3.463, 0, 8.317, -1.715, 0, 8.6, 0.613, 0, 8.85, 0.036, 0, 9.4, 1.005, 0, 9.783, -1.933, 0, 10.15, 1.662, 0, 10.55, -0.247, 0, 10.983, 0.276, 0, 11.233, 0.07, 0, 11.517, 1.621, 0, 11.967, -5.793, 0, 12.383, 13.912, 0, 12.8, -18.806, 1, 12.867, -18.806, 12.933, -10.681, 13, 3.632]}, {"Target": "Parameter", "Id": "Param129", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.6, 0, 0, 0.767, -6.546, 0, 1, 11.543, 0, 1.333, -12.082, 0, 1.683, 7.668, 0, 2.4, -5.92, 0, 2.833, 14.756, 0, 3.2, -22.471, 0, 3.55, 21.779, 0, 3.9, -13.789, 0, 4.233, 5.022, 0, 4.5, -2.141, 0, 4.783, 5.544, 0, 5.117, -8.184, 0, 5.467, 10.569, 0, 5.783, -14.204, 0, 6.1, 16.849, 0, 6.433, -14.825, 0, 6.9, 9.017, 0, 7.317, -12.496, 0, 7.65, 10.446, 0, 8.017, -5.137, 0, 8.4, 2.012, 0, 8.717, -1, 0, 9, 0.198, 0, 9.483, -0.746, 0, 9.867, 1.787, 0, 10.25, -1.693, 0, 10.517, 0.857, 0, 10.983, -0.217, 0, 11.317, -0.052, 0, 11.617, -1.253, 0, 12.033, 4.662, 0, 12.467, -11.996, 0, 12.883, 17.327, 1, 12.922, 17.327, 12.961, 16.294, 13, 9.11]}, {"Target": "Parameter", "Id": "Param16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.267, 0.223, 0, 6.6, -0.46, 0, 10.317, 0.033, 1, 11.211, 0.033, 12.106, -0.205, 13, -0.521]}, {"Target": "Parameter", "Id": "Param19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 4.333, 0.217, 8.667, 0.433, 13, 0.65]}, {"Target": "Parameter", "Id": "Param142", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.812, 2, 0.017, 0.127, 1, 2.561, 0.305, 5.106, 0.482, 7.65, 0.66, 1, 8.1, 0.711, 8.55, 0.761, 9, 0.812, 2, 9.017, 0.127, 1, 10.345, 0.22, 11.672, 0.312, 13, 0.405]}, {"Target": "Parameter", "Id": "Param143", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.76, 1, 0.422, 0.82, 0.845, 0.88, 1.267, 0.94, 2, 1.283, 0.2, 1, 3.855, 0.387, 6.428, 0.573, 9, 0.76, 1, 9.422, 0.82, 9.845, 0.88, 10.267, 0.94, 2, 10.283, 0.2, 1, 11.189, 0.266, 12.094, 0.331, 13, 0.397]}, {"Target": "Parameter", "Id": "Param18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 0, 2, 0.35, 1, 2, 0.4, 2, 2, 0.45, 3, 2, 0.5, 4, 2, 0.55, 5, 2, 0.6, 0, 2, 0.65, 1, 2, 0.7, 2, 2, 0.75, 3, 2, 0.8, 4, 2, 0.85, 5, 2, 0.9, 0, 2, 0.95, 1, 2, 1, 2, 2, 1.05, 3, 2, 1.1, 4, 2, 1.15, 5, 2, 1.2, 0, 2, 1.25, 1, 2, 1.3, 2, 2, 1.35, 3, 2, 1.4, 4, 2, 1.45, 5, 2, 1.5, 0, 2, 1.55, 1, 2, 1.6, 2, 2, 1.65, 3, 2, 1.7, 4, 2, 1.75, 5, 2, 1.8, 0, 2, 1.85, 1, 2, 1.9, 2, 2, 1.95, 3, 2, 2, 4, 2, 2.05, 5, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 0, 2, 2.45, 1, 2, 2.5, 2, 2, 2.55, 3, 2, 2.6, 4, 2, 2.65, 5, 2, 2.7, 0, 2, 2.75, 1, 2, 2.8, 2, 2, 2.85, 3, 2, 2.9, 4, 2, 2.95, 5, 2, 3, 0, 2, 3.05, 1, 2, 3.1, 2, 2, 3.15, 3, 2, 3.2, 4, 2, 3.25, 5, 2, 3.3, 0, 2, 3.35, 1, 2, 3.4, 2, 2, 3.45, 3, 2, 3.5, 4, 2, 3.55, 5, 2, 3.6, 0, 2, 3.65, 1, 2, 3.7, 2, 2, 3.75, 3, 2, 3.8, 4, 2, 3.85, 5, 2, 3.9, 0, 2, 3.95, 1, 2, 4, 2, 2, 4.05, 3, 2, 4.1, 4, 2, 4.15, 5, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 0, 2, 4.55, 1, 2, 4.6, 2, 2, 4.65, 3, 2, 4.7, 4, 2, 4.75, 5, 2, 4.8, 0, 2, 4.85, 1, 2, 4.9, 2, 2, 4.95, 3, 2, 5, 4, 2, 5.05, 5, 2, 5.1, 0, 2, 5.15, 1, 2, 5.2, 2, 2, 5.25, 3, 2, 5.3, 4, 2, 5.35, 5, 2, 5.4, 0, 2, 5.45, 1, 2, 5.5, 2, 2, 5.55, 3, 2, 5.6, 4, 2, 5.65, 5, 2, 5.7, 0, 2, 5.75, 1, 2, 5.8, 2, 2, 5.85, 3, 2, 5.9, 4, 2, 5.95, 5, 2, 6, 0, 2, 6.05, 1, 2, 6.1, 2, 2, 6.15, 3, 2, 6.2, 4, 2, 6.25, 5, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 0, 2, 6.65, 1, 2, 6.7, 2, 2, 6.75, 3, 2, 6.8, 4, 2, 6.85, 5, 2, 6.9, 0, 2, 6.95, 1, 2, 7, 2, 2, 7.05, 3, 2, 7.1, 4, 2, 7.15, 5, 2, 7.2, 0, 2, 7.25, 1, 2, 7.3, 2, 2, 7.35, 3, 2, 7.4, 4, 2, 7.45, 5, 2, 7.5, 0, 2, 7.55, 1, 2, 7.6, 2, 2, 7.65, 3, 2, 7.7, 4, 2, 7.75, 5, 2, 7.8, 0, 2, 7.85, 1, 2, 7.9, 2, 2, 7.95, 3, 2, 8, 4, 2, 8.05, 5, 2, 8.1, 0, 2, 8.15, 1, 2, 8.2, 2, 2, 8.25, 3, 2, 8.3, 4, 2, 8.35, 5, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 0, 2, 8.75, 1, 2, 8.8, 2, 2, 8.85, 3, 2, 8.9, 4, 2, 8.95, 5, 2, 9, 0, 2, 9.05, 1, 2, 9.1, 2, 2, 9.15, 3, 2, 9.2, 4, 2, 9.25, 5, 2, 9.3, 0, 2, 9.35, 1, 2, 9.4, 2, 2, 9.45, 3, 2, 9.5, 4, 2, 9.55, 5, 2, 9.6, 0, 2, 9.65, 1, 2, 9.7, 2, 2, 9.75, 3, 2, 9.8, 4, 2, 9.85, 5, 2, 9.9, 0, 2, 9.95, 1, 2, 10, 2, 2, 10.05, 3, 2, 10.1, 4, 2, 10.15, 5, 2, 10.2, 0, 2, 10.25, 1, 2, 10.3, 2, 2, 10.35, 3, 2, 10.4, 4, 2, 10.45, 5, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 0, 2, 10.85, 1, 2, 10.9, 2, 2, 10.95, 3, 2, 11, 4, 2, 11.05, 5, 2, 11.1, 0, 2, 11.15, 1, 2, 11.2, 2, 2, 11.25, 3, 2, 11.3, 4, 2, 11.35, 5, 2, 11.4, 0, 2, 11.45, 1, 2, 11.5, 2, 2, 11.55, 3, 2, 11.6, 4, 2, 11.65, 5, 2, 11.7, 0, 2, 11.75, 1, 2, 11.8, 2, 2, 11.85, 3, 2, 11.9, 4, 2, 11.95, 5, 2, 12, 0, 2, 12.05, 1, 2, 12.1, 2, 2, 12.15, 3, 2, 12.2, 4, 2, 12.25, 5, 2, 12.3, 0, 2, 12.35, 1, 2, 12.4, 2, 2, 12.45, 3, 2, 12.5, 4, 2, 12.55, 5, 2, 12.6, 0, 2, 12.65, 1, 2, 12.7, 2, 2, 12.75, 3, 2, 12.8, 4, 2, 12.85, 5, 2, 12.9, 0, 2, 12.95, 1, 2, 13, 2]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 6, 2, 0.35, 0, 2, 0.4, 1, 2, 0.45, 2, 2, 0.5, 3, 2, 0.55, 4, 2, 0.6, 5, 2, 0.65, 6, 2, 0.7, 0, 2, 0.75, 1, 2, 0.8, 2, 2, 0.85, 3, 2, 0.9, 4, 2, 0.95, 5, 2, 1, 6, 2, 1.05, 0, 2, 1.1, 1, 2, 1.15, 2, 2, 1.2, 3, 2, 1.25, 4, 2, 1.3, 5, 2, 1.35, 6, 2, 1.4, 0, 2, 1.45, 1, 2, 1.5, 2, 2, 1.55, 3, 2, 1.6, 4, 2, 1.65, 5, 2, 1.7, 6, 2, 1.75, 0, 2, 1.8, 1, 2, 1.85, 2, 2, 1.9, 3, 2, 1.95, 4, 2, 2, 5, 2, 2.05, 6, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 6, 2, 2.45, 0, 2, 2.5, 1, 2, 2.55, 2, 2, 2.6, 3, 2, 2.65, 4, 2, 2.7, 5, 2, 2.75, 6, 2, 2.8, 0, 2, 2.85, 1, 2, 2.9, 2, 2, 2.95, 3, 2, 3, 4, 2, 3.05, 5, 2, 3.1, 6, 2, 3.15, 0, 2, 3.2, 1, 2, 3.25, 2, 2, 3.3, 3, 2, 3.35, 4, 2, 3.4, 5, 2, 3.45, 6, 2, 3.5, 0, 2, 3.55, 1, 2, 3.6, 2, 2, 3.65, 3, 2, 3.7, 4, 2, 3.75, 5, 2, 3.8, 6, 2, 3.85, 0, 2, 3.9, 1, 2, 3.95, 2, 2, 4, 3, 2, 4.05, 4, 2, 4.1, 5, 2, 4.15, 6, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 6, 2, 4.55, 0, 2, 4.6, 1, 2, 4.65, 2, 2, 4.7, 3, 2, 4.75, 4, 2, 4.8, 5, 2, 4.85, 6, 2, 4.9, 0, 2, 4.95, 1, 2, 5, 2, 2, 5.05, 3, 2, 5.1, 4, 2, 5.15, 5, 2, 5.2, 6, 2, 5.25, 0, 2, 5.3, 1, 2, 5.35, 2, 2, 5.4, 3, 2, 5.45, 4, 2, 5.5, 5, 2, 5.55, 6, 2, 5.6, 0, 2, 5.65, 1, 2, 5.7, 2, 2, 5.75, 3, 2, 5.8, 4, 2, 5.85, 5, 2, 5.9, 6, 2, 5.95, 0, 2, 6, 1, 2, 6.05, 2, 2, 6.1, 3, 2, 6.15, 4, 2, 6.2, 5, 2, 6.25, 6, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 6, 2, 6.65, 0, 2, 6.7, 1, 2, 6.75, 2, 2, 6.8, 3, 2, 6.85, 4, 2, 6.9, 5, 2, 6.95, 6, 2, 7, 0, 2, 7.05, 1, 2, 7.1, 2, 2, 7.15, 3, 2, 7.2, 4, 2, 7.25, 5, 2, 7.3, 6, 2, 7.35, 0, 2, 7.4, 1, 2, 7.45, 2, 2, 7.5, 3, 2, 7.55, 4, 2, 7.6, 5, 2, 7.65, 6, 2, 7.7, 0, 2, 7.75, 1, 2, 7.8, 2, 2, 7.85, 3, 2, 7.9, 4, 2, 7.95, 5, 2, 8, 6, 2, 8.05, 0, 2, 8.1, 1, 2, 8.15, 2, 2, 8.2, 3, 2, 8.25, 4, 2, 8.3, 5, 2, 8.35, 6, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 6, 2, 8.75, 0, 2, 8.8, 1, 2, 8.85, 2, 2, 8.9, 3, 2, 8.95, 4, 2, 9, 5, 2, 9.05, 6, 2, 9.1, 0, 2, 9.15, 1, 2, 9.2, 2, 2, 9.25, 3, 2, 9.3, 4, 2, 9.35, 5, 2, 9.4, 6, 2, 9.45, 0, 2, 9.5, 1, 2, 9.55, 2, 2, 9.6, 3, 2, 9.65, 4, 2, 9.7, 5, 2, 9.75, 6, 2, 9.8, 0, 2, 9.85, 1, 2, 9.9, 2, 2, 9.95, 3, 2, 10, 4, 2, 10.05, 5, 2, 10.1, 6, 2, 10.15, 0, 2, 10.2, 1, 2, 10.25, 2, 2, 10.3, 3, 2, 10.35, 4, 2, 10.4, 5, 2, 10.45, 6, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 6, 2, 10.85, 0, 2, 10.9, 1, 2, 10.95, 2, 2, 11, 3, 2, 11.05, 4, 2, 11.1, 5, 2, 11.15, 6, 2, 11.2, 0, 2, 11.25, 1, 2, 11.3, 2, 2, 11.35, 3, 2, 11.4, 4, 2, 11.45, 5, 2, 11.5, 6, 2, 11.55, 0, 2, 11.6, 1, 2, 11.65, 2, 2, 11.7, 3, 2, 11.75, 4, 2, 11.8, 5, 2, 11.85, 6, 2, 11.9, 0, 2, 11.95, 1, 2, 12, 2, 2, 12.05, 3, 2, 12.1, 4, 2, 12.15, 5, 2, 12.2, 6, 2, 12.25, 0, 2, 12.3, 1, 2, 12.35, 2, 2, 12.4, 3, 2, 12.45, 4, 2, 12.5, 5, 2, 12.55, 6, 2, 12.6, 0, 2, 12.65, 1, 2, 12.7, 2, 2, 12.75, 3, 2, 12.8, 4, 2, 12.85, 5, 2, 12.9, 6, 2, 12.95, 0, 2, 13, 1]}, {"Target": "Parameter", "Id": "Param51", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 6, 2, 0.35, 0, 2, 0.4, 1, 2, 0.45, 2, 2, 0.5, 3, 2, 0.55, 4, 2, 0.6, 5, 2, 0.65, 6, 2, 0.7, 0, 2, 0.75, 1, 2, 0.8, 2, 2, 0.85, 3, 2, 0.9, 4, 2, 0.95, 5, 2, 1, 6, 2, 1.05, 0, 2, 1.1, 1, 2, 1.15, 2, 2, 1.2, 3, 2, 1.25, 4, 2, 1.3, 5, 2, 1.35, 6, 2, 1.4, 0, 2, 1.45, 1, 2, 1.5, 2, 2, 1.55, 3, 2, 1.6, 4, 2, 1.65, 5, 2, 1.7, 6, 2, 1.75, 0, 2, 1.8, 1, 2, 1.85, 2, 2, 1.9, 3, 2, 1.95, 4, 2, 2, 5, 2, 2.05, 6, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 6, 2, 2.45, 0, 2, 2.5, 1, 2, 2.55, 2, 2, 2.6, 3, 2, 2.65, 4, 2, 2.7, 5, 2, 2.75, 6, 2, 2.8, 0, 2, 2.85, 1, 2, 2.9, 2, 2, 2.95, 3, 2, 3, 4, 2, 3.05, 5, 2, 3.1, 6, 2, 3.15, 0, 2, 3.2, 1, 2, 3.25, 2, 2, 3.3, 3, 2, 3.35, 4, 2, 3.4, 5, 2, 3.45, 6, 2, 3.5, 0, 2, 3.55, 1, 2, 3.6, 2, 2, 3.65, 3, 2, 3.7, 4, 2, 3.75, 5, 2, 3.8, 6, 2, 3.85, 0, 2, 3.9, 1, 2, 3.95, 2, 2, 4, 3, 2, 4.05, 4, 2, 4.1, 5, 2, 4.15, 6, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 6, 2, 4.55, 0, 2, 4.6, 1, 2, 4.65, 2, 2, 4.7, 3, 2, 4.75, 4, 2, 4.8, 5, 2, 4.85, 6, 2, 4.9, 0, 2, 4.95, 1, 2, 5, 2, 2, 5.05, 3, 2, 5.1, 4, 2, 5.15, 5, 2, 5.2, 6, 2, 5.25, 0, 2, 5.3, 1, 2, 5.35, 2, 2, 5.4, 3, 2, 5.45, 4, 2, 5.5, 5, 2, 5.55, 6, 2, 5.6, 0, 2, 5.65, 1, 2, 5.7, 2, 2, 5.75, 3, 2, 5.8, 4, 2, 5.85, 5, 2, 5.9, 6, 2, 5.95, 0, 2, 6, 1, 2, 6.05, 2, 2, 6.1, 3, 2, 6.15, 4, 2, 6.2, 5, 2, 6.25, 6, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 6, 2, 6.65, 0, 2, 6.7, 1, 2, 6.75, 2, 2, 6.8, 3, 2, 6.85, 4, 2, 6.9, 5, 2, 6.95, 6, 2, 7, 0, 2, 7.05, 1, 2, 7.1, 2, 2, 7.15, 3, 2, 7.2, 4, 2, 7.25, 5, 2, 7.3, 6, 2, 7.35, 0, 2, 7.4, 1, 2, 7.45, 2, 2, 7.5, 3, 2, 7.55, 4, 2, 7.6, 5, 2, 7.65, 6, 2, 7.7, 0, 2, 7.75, 1, 2, 7.8, 2, 2, 7.85, 3, 2, 7.9, 4, 2, 7.95, 5, 2, 8, 6, 2, 8.05, 0, 2, 8.1, 1, 2, 8.15, 2, 2, 8.2, 3, 2, 8.25, 4, 2, 8.3, 5, 2, 8.35, 6, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 6, 2, 8.75, 0, 2, 8.8, 1, 2, 8.85, 2, 2, 8.9, 3, 2, 8.95, 4, 2, 9, 5, 2, 9.05, 6, 2, 9.1, 0, 2, 9.15, 1, 2, 9.2, 2, 2, 9.25, 3, 2, 9.3, 4, 2, 9.35, 5, 2, 9.4, 6, 2, 9.45, 0, 2, 9.5, 1, 2, 9.55, 2, 2, 9.6, 3, 2, 9.65, 4, 2, 9.7, 5, 2, 9.75, 6, 2, 9.8, 0, 2, 9.85, 1, 2, 9.9, 2, 2, 9.95, 3, 2, 10, 4, 2, 10.05, 5, 2, 10.1, 6, 2, 10.15, 0, 2, 10.2, 1, 2, 10.25, 2, 2, 10.3, 3, 2, 10.35, 4, 2, 10.4, 5, 2, 10.45, 6, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 6, 2, 10.85, 0, 2, 10.9, 1, 2, 10.95, 2, 2, 11, 3, 2, 11.05, 4, 2, 11.1, 5, 2, 11.15, 6, 2, 11.2, 0, 2, 11.25, 1, 2, 11.3, 2, 2, 11.35, 3, 2, 11.4, 4, 2, 11.45, 5, 2, 11.5, 6, 2, 11.55, 0, 2, 11.6, 1, 2, 11.65, 2, 2, 11.7, 3, 2, 11.75, 4, 2, 11.8, 5, 2, 11.85, 6, 2, 11.9, 0, 2, 11.95, 1, 2, 12, 2, 2, 12.05, 3, 2, 12.1, 4, 2, 12.15, 5, 2, 12.2, 6, 2, 12.25, 0, 2, 12.3, 1, 2, 12.35, 2, 2, 12.4, 3, 2, 12.45, 4, 2, 12.5, 5, 2, 12.55, 6, 2, 12.6, 0, 2, 12.65, 1, 2, 12.7, 2, 2, 12.75, 3, 2, 12.8, 4, 2, 12.85, 5, 2, 12.9, 6, 2, 12.95, 0, 2, 13, 1]}, {"Target": "Parameter", "Id": "Param42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 4.333, 0.048, 8.667, 0.096, 13, 0.144]}, {"Target": "Parameter", "Id": "Param45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.4, 1, 4.333, -0.352, 8.667, -0.304, 13, -0.256]}, {"Target": "Parameter", "Id": "Param46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.3, 1, 4.333, 0.348, 8.667, 0.396, 13, 0.444]}, {"Target": "Parameter", "Id": "Param50", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 4.333, 0.867, 8.667, 1.733, 13, 2.6]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2.5, 10, 0, 5.017, -10, 0, 7.5, 10, 0, 10, -10, 0, 12.5, 26.92, 1, 12.667, 26.92, 12.833, 27.548, 13, 23.125]}, {"Target": "Parameter", "Id": "Param17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.76, 0, 2.5, 0.66, 0, 5.017, -5.76, 0, 7.5, 0.66, 0, 10, -5.76, 0, 12.5, 21.06, 1, 12.667, 21.06, 12.833, 21.523, 13, 18.303]}, {"Target": "Parameter", "Id": "Param49", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.35, 0, 1.25, 0, 0, 3.75, 0.7, 0, 6.267, 0, 0, 8.767, 0.7, 0, 11.283, 0, 1, 11.855, 0, 12.428, 0.228, 13, 0.537]}, {"Target": "Parameter", "Id": "Param40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.25, 0, 0.917, 10, 0, 2.917, -10, 0, 4.917, 10, 0, 6.917, -10, 0, 8.917, 10, 0, 10.917, -10, 0, 12.917, 10, 1, 12.945, 10, 12.972, 10.335, 13, 9.898]}, {"Target": "Parameter", "Id": "Param41", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2, 10, 0, 4, -10, 0, 6, 10, 0, 8, -10, 0, 10, 10, 0, 12, -10, 1, 12.333, -10, 12.667, -6.667, 13, 0]}, {"Target": "Parameter", "Id": "Param32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2, 10, 0, 4, -10, 0, 6, 10, 0, 8, -10, 0, 10, 10, 0, 12, -10, 1, 12.333, -10, 12.667, -6.667, 13, 0]}, {"Target": "Parameter", "Id": "Param33", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.886, 0, 0.583, 10, 0, 2.583, -10, 0, 4.583, 10, 0, 6.583, -10, 0, 8.583, 10, 0, 10.583, -10, 0, 12.583, 10, 1, 12.722, 10, 12.861, 10.293, 13, 7.759]}, {"Target": "Parameter", "Id": "Param34", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -14.451, 0, 1.333, 30, 0, 3.333, -30, 0, 5.333, 30, 0, 7.333, -30, 0, 9.333, 30, 0, 11.333, -30, 1, 11.889, -30, 12.444, -3.57, 13, 25.551]}, {"Target": "Parameter", "Id": "Param28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -15, 0, 2, 15, 0, 4, -15, 0, 6, 15, 0, 8, -15, 0, 10, 15, 0, 12, -15, 1, 12.333, -15, 12.667, -10, 13, 0]}, {"Target": "Parameter", "Id": "Param29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.886, 0, 0.583, 10, 0, 2.583, -10, 0, 4.583, 10, 0, 6.583, -10, 0, 8.583, 10, 0, 10.583, -10, 0, 12.583, 10, 1, 12.722, 10, 12.861, 10.293, 13, 7.759]}, {"Target": "Parameter", "Id": "Param35", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -14.451, 0, 1.333, 30, 0, 3.333, -30, 0, 5.333, 30, 0, 7.333, -30, 0, 9.333, 30, 0, 11.333, -30, 1, 11.889, -30, 12.444, -3.57, 13, 25.551]}, {"Target": "Parameter", "Id": "Param30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.959, 0, 0.8, 10, 0, 2.8, -10, 0, 4.8, 10, 0, 6.8, -10, 0, 8.8, 10, 0, 10.8, -10, 0, 12.8, 10, 1, 12.867, 10, 12.933, 10.555, 13, 9.44]}, {"Target": "Parameter", "Id": "Param31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 9.44, 0, 1.8, -10, 0, 3.8, 10, 0, 5.8, -10, 0, 7.8, 10, 0, 9.8, -10, 0, 11.8, 10, 1, 12.2, 10, 12.6, 4.959, 13, -2.959]}, {"Target": "Parameter", "Id": "Param36", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 13.773, 0, 0.683, 30, 0, 2.683, -30, 0, 4.683, 30, 0, 6.683, -30, 0, 8.683, 30, 0, 10.683, -30, 0, 12.683, 30, 1, 12.789, 30, 12.894, 30, 13, 25.964]}, {"Target": "Parameter", "Id": "GL1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 4.333, 0.193, 8.667, 0.386, 13, 0.579]}, {"Target": "Parameter", "Id": "GL2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.39, 1, 4.333, 0.583, 8.667, 0.776, 13, 0.969]}, {"Target": "Parameter", "Id": "GL3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.72, 1, 4.333, 0.913, 8.667, 1.106, 13, 1.299]}, {"Target": "Parameter", "Id": "GL4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.21, 1, 4.333, 0.403, 8.667, 0.596, 13, 0.789]}, {"Target": "Parameter", "Id": "GL5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.53, 1, 4.333, 0.723, 8.667, 0.915, 13, 1.108]}, {"Target": "Parameter", "Id": "GL6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.89, 1, 4.333, 1.083, 8.667, 1.276, 13, 1.469]}, {"Target": "Parameter", "Id": "GL7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.77, 1, 4.333, 0.963, 8.667, 1.156, 13, 1.349]}, {"Target": "Parameter", "Id": "GL8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.09, 1, 4.333, 0.283, 8.667, 0.475, 13, 0.668]}, {"Target": "Parameter", "Id": "GL9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.301, 1, 4.333, 0.494, 8.667, 0.687, 13, 0.88]}, {"Target": "Parameter", "Id": "GL10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.45, 1, 4.333, 0.643, 8.667, 0.836, 13, 1.029]}, {"Target": "Parameter", "Id": "GL11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.64, 1, 4.333, 0.833, 8.667, 1.026, 13, 1.219]}, {"Target": "Parameter", "Id": "GL12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.15, 1, 4.333, 0.343, 8.667, 0.536, 13, 0.729]}, {"Target": "Parameter", "Id": "GL13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.585, 1, 4.333, 0.778, 8.667, 0.97, 13, 1.163]}, {"Target": "Parameter", "Id": "GL14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 4.333, 0.193, 8.667, 0.386, 13, 0.579]}, {"Target": "Parameter", "Id": "Param161", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.51, 1, 4.333, 0.751, 8.667, 0.992, 13, 1.233]}, {"Target": "Parameter", "Id": "Param162", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.31, 1, 4.333, 0.551, 8.667, 0.792, 13, 1.033]}, {"Target": "Parameter", "Id": "Param163", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.72, 1, 4.333, 0.961, 8.667, 1.202, 13, 1.443]}, {"Target": "Parameter", "Id": "Param164", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 4.333, 0.241, 8.667, 0.482, 13, 0.723]}, {"Target": "Parameter", "Id": "Param165", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.17, 1, 4.333, 0.411, 8.667, 0.652, 13, 0.893]}, {"Target": "Parameter", "Id": "touch_drag21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param54", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param118", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "man_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "man_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param78", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param79", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "man_<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "man_<PERSON>ze", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param189", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "shangguang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param186", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param96", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param97", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param98", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param111", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "shangguang2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param53", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param99", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param76", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param77", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "MB_xingfengTMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param127", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param103", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param104", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param106", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param105", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param110", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param130", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param107", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param108", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param109", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Mouthfunnel", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param293", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param72", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param74", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param75", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param73", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "MB_fenweiTMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "MB_fenweiliangdu", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 13, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiandu", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 13, 0.6]}, {"Target": "Parameter", "Id": "MB_fenweiqiu1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 13, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 13, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 13, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 13, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 13, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 13, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiushangxia", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "MB_fenweiqiudaxiao", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param102", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param167", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param169", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param196", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param56", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param59", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10, 0, 13, 10]}, {"Target": "Parameter", "Id": "Param44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param57", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param124", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param80", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param47", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param81", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param95", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param125", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param166", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param123", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 13, 1]}, {"Target": "Parameter", "Id": "Param13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param39", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param61", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 13, 1]}, {"Target": "Parameter", "Id": "Param71", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 13, 1]}, {"Target": "Parameter", "Id": "Param173", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param174", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param175", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param176", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param190", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param191", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param192", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param193", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param65", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 13, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamShoulderStretch3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamShoulderStretch4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamChestZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamWaistZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamButtZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param87", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param94", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param52", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param70", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param208", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param112", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param115", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param113", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param116", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param114", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param117", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "Param23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "touch_drag23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "touch_drag24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "touch_drag25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "touch_drag26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "touch_drag27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "touch_drag28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "touch_drag29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "touch_drag30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "touch_drag31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "touch_drag32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "Parameter", "Id": "empty23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13, 0]}, {"Target": "PartOpacity", "Id": "Part7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 13, 1]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 12.5, "Value": ""}]}