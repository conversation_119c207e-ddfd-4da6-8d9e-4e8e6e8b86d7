{"Version": 3, "Meta": {"Duration": 13.5, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 286, "TotalSegmentCount": 3207, "TotalPointCount": 3639, "UserDataCount": 2, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.883, 0.052, 1, 1.278, 0.052, 1.672, 0.009, 2.067, 0, 1, 3.134, -0.023, 4.2, -0.032, 5.267, -0.054, 1, 5.556, -0.06, 5.844, -0.156, 6.133, -0.156, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.883, -0.044, 1, 1.278, -0.044, 1.672, -0.008, 2.067, 0, 1, 3.134, 0.023, 4.2, 0.032, 5.267, 0.054, 1, 5.556, 0.06, 5.844, 0.166, 6.133, 0.166, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "All_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.45, -0.176, 0, 0.95, 0.301, 0, 2.45, -0.331, 0, 2.917, 0.356, 0, 3.733, -0.216, 0, 4.383, -0.094, 0, 4.733, -0.155, 0, 6.067, 0.193, 0, 6.8, -0.261, 0, 7.133, -0.021, 0, 7.667, -0.205, 0, 11.017, 0.101, 0, 11.817, -0.057, 0, 12.617, 0.121, 0, 13.167, -0.031, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "All_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.367, -0.077, 0, 0.733, 0.156, 0, 1.133, -0.141, 0, 1.817, 0.27, 0, 2.267, 0.217, 0, 2.75, 0.34, 0, 3.4, -0.171, 0, 4.017, 0.109, 0, 4.683, -0.025, 0, 5.083, 0.133, 0, 5.383, 0.073, 0, 5.8, 0.193, 0, 6.35, 0.101, 0, 6.767, 0.357, 0, 7.133, 0.008, 0, 7.633, 0.199, 0, 11.017, -0.175, 0, 11.6, 0.123, 0, 12.183, 0.012, 0, 12.983, 0.308, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.883, 0.117, 0, 2.067, 0, 0, 3.333, 2.127, 1, 3.978, 2.127, 4.622, 2.094, 5.267, 1.907, 1, 5.556, 1.823, 5.844, 1.282, 6.133, 1.259, 1, 6.583, 1.223, 7.033, 1.237, 7.483, 1.19, 1, 8.233, 1.112, 8.983, 0.88, 9.733, 0.88, 0, 11.167, 2, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.883, 5, 0, 2.933, -3.23, 0, 4.183, -2.863, 0, 5.267, -5.303, 1, 5.561, -5.303, 5.856, 7.801, 6.15, 9, 1, 8.183, 17.278, 10.217, 20.034, 12.25, 20.034, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamAngleX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.304, 0, 0.317, -7.924, 0, 0.817, 13.741, 0, 2.267, -16.092, 0, 2.783, 15.778, 0, 3.583, -11.29, 0, 4.167, -6, 0, 4.567, -9.729, 0, 4.883, -6.787, 0, 5.167, -8.84, 0, 5.917, 18.11, 0, 6.683, -12.699, 0, 7.083, -1.81, 0, 7.65, -11.045, 0, 8.283, -6.219, 0, 8.65, -9.554, 0, 8.95, -6.539, 0, 9.233, -8.188, 0, 9.983, 4.75, 0, 10.733, -12.518, 0, 11.383, -0.006, 0, 11.833, -1.37, 0, 12.383, 5.833, 0, 13.083, -13.636, 0, 13.5, -1.298]}, {"Target": "Parameter", "Id": "ParamAngleY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.124, 0, 0.233, -3.369, 0, 0.6, 7.281, 0, 1.05, -8.315, 0, 1.767, 8.592, 0, 2.1, 7.382, 0, 2.617, 14.228, 0, 3.217, -10.473, 0, 3.867, 3.774, 0, 4.533, -4.223, 0, 4.933, 3.457, 0, 5.25, -0.554, 0, 5.7, 4.814, 0, 6.167, 0.469, 0, 6.617, 15.487, 0, 7, -1.152, 0, 7.917, 3.847, 0, 8.6, -3.974, 0, 8.967, 3.538, 0, 9.317, 0.272, 0, 9.75, 6.884, 0, 10.233, 2.253, 0, 10.683, 15.304, 0, 11.067, -0.332, 0, 11.783, 3.613, 0, 12.583, -3.737, 0, 13.017, 12.474, 0, 13.5, -0.119]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.543, 0, 0.3, -3.35, 0, 0.7, -1.899, 0, 1.083, -5.918, 1, 1.3, -5.918, 1.516, -3.664, 1.733, -3.342, 1, 1.972, -2.987, 2.211, -3.007, 2.45, -2.728, 1, 2.539, -2.624, 2.628, 6, 2.717, 6, 1, 2.861, 6, 3.006, -4.235, 3.15, -7, 1, 3.272, -9.34, 3.395, -9, 3.517, -9, 0, 3.967, -4.851, 0, 4.433, -6.136, 0, 4.817, -4.097, 0, 5.417, -4.855, 0, 5.8, 2.8, 0, 6.217, -6.2, 0, 6.7, -1.2, 0, 7.583, -9, 0, 8.033, -4.851, 0, 8.5, -6.136, 0, 8.883, -4.097, 0, 9.483, -4.855, 0, 9.867, 2.8, 0, 10.283, -6.2, 1, 10.444, -6.2, 10.606, -1.274, 10.767, -1.2, 1, 11.172, -1.015, 11.578, -1, 11.983, -1, 0, 12.367, -7.533, 0, 13.033, 6, 0, 13.5, 0.543]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -4.357, 0, 0.517, -0.087, 0, 0.9, -10.506, 1, 1.117, -10.506, 1.333, 3.398, 1.55, 4.273, 1, 1.789, 5.237, 2.028, 5.164, 2.267, 5.867, 1, 2.356, 6.129, 2.444, 9, 2.533, 9, 1, 2.678, 9, 2.822, -6.595, 2.967, -9.735, 1, 3.089, -12.392, 3.211, -11.942, 3.333, -11.942, 0, 3.783, -7.739, 0, 4.383, -9.438, 0, 4.8, -8.239, 0, 5.233, -9.2, 0, 5.617, -1.048, 0, 6.033, -9.2, 0, 6.583, -4.2, 1, 7.005, -4.2, 7.428, -5.37, 7.85, -7.739, 1, 8.05, -8.861, 8.25, -9.438, 8.45, -9.438, 0, 8.867, -8.239, 0, 9.3, -9.2, 0, 9.683, -1.048, 0, 10.1, -9.2, 1, 10.283, -9.2, 10.467, -4.293, 10.65, -4.2, 1, 11.033, -4.005, 11.417, -4, 11.8, -4, 0, 12.167, -10.128, 0, 12.933, 3, 0, 13.5, -4.357]}, {"Target": "Parameter", "Id": "ParamBodyAngleX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -8.417, 0, 0.183, -13, 0, 0.633, 8.898, 1, 0.822, 8.898, 1.011, -2.416, 1.2, -3.294, 1, 1.522, -4.791, 1.845, -4.877, 2.167, -6.258, 1, 2.306, -6.853, 2.444, -22.203, 2.583, -22.203, 0, 2.95, 28.811, 0, 3.533, 0.722, 0, 4.083, 3.784, 1, 4.344, 3.784, 4.606, 3.695, 4.867, 2.496, 1, 5, 1.884, 5.134, -1.72, 5.267, -1.72, 0, 5.733, 20, 0, 6.45, -7.606, 0, 6.8, 1.013, 0, 7.6, 0.722, 0, 8.15, 3.784, 1, 8.411, 3.784, 8.672, 3.738, 8.933, 2.496, 1, 9.094, 1.73, 9.256, -1.72, 9.417, -1.72, 0, 10, 20, 0, 10.517, -7.606, 0, 10.867, 1.013, 1, 11.222, 1.013, 11.578, 1.064, 11.933, 0, 1, 12.15, -0.648, 12.366, -23.539, 12.583, -23.539, 0, 13.167, 1.013, 0, 13.5, -8.417]}, {"Target": "Parameter", "Id": "ParamBodyAngleY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -9.558, 0, 0.417, 8.992, 0, 0.9, -9, 0, 1.483, -0.032, 0, 1.933, -3.696, 0, 2.433, 4.939, 1, 2.583, 4.939, 2.733, -6.69, 2.883, -18.303, 1, 2.983, -26.045, 3.083, -26.947, 3.183, -26.947, 0, 3.7, 1.602, 0, 4.383, -7.125, 0, 4.767, 0.512, 0, 5.083, -4.082, 0, 5.533, 6, 0, 5.75, 2, 1, 5.844, 2, 5.939, 6.159, 6.033, 9, 1, 6.172, 13.178, 6.311, 14, 6.45, 14, 0, 6.867, -2, 0, 7.767, 1.602, 0, 8.45, -7.125, 0, 8.833, 0.512, 0, 9.15, -4.082, 0, 9.6, 6, 0, 9.817, 2, 1, 9.911, 2, 10.006, 6.159, 10.1, 9, 1, 10.239, 13.178, 10.378, 14, 10.517, 14, 0, 10.933, -2, 0, 11.817, 4, 0, 12.217, -1, 0, 12.817, 14, 0, 13.5, -9.558]}, {"Target": "Parameter", "Id": "ParamWaistZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -13.805, 0, 0.2, -15.74, 0, 0.7, -2.638, 0, 1.317, -9.855, 0, 1.517, -9.106, 0, 2.167, -13.939, 0, 2.717, -3.179, 0, 4.1, -13.392, 0, 4.533, -11.806, 0, 4.917, -13.536, 0, 5.783, -1.095, 0, 6.533, -16.857, 0, 7.65, -11.457, 0, 9.133, -14.576, 0, 9.85, -4.549, 0, 10.483, -17.176, 0, 11.867, -10.044, 0, 12.55, -16.629, 0, 13.05, -12.72, 0, 13.5, -13.821]}, {"Target": "Parameter", "Id": "ParamShoulderStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.893, 0, 0.283, -0.235, 0, 0.783, -9.529, 0, 1.35, 1.943, 0, 1.8, 1.239, 0, 2.3, 2.897, 1, 2.45, 2.897, 2.6, -5.111, 2.75, -6.639, 1, 2.85, -7.658, 2.95, -7.365, 3.05, -7.365, 0, 3.567, -2.298, 0, 4.25, -4.735, 0, 4.633, -2.881, 0, 4.95, -4.164, 0, 5.4, -3.573, 0, 5.9, -9.971, 0, 6.333, 2.28, 0, 6.917, -6.765, 0, 7.633, -2.298, 0, 8.317, -4.735, 0, 8.7, -2.881, 0, 9.017, -4.164, 0, 9.467, -3.573, 0, 9.967, -9.971, 0, 10.4, 2.28, 0, 10.983, -6.765, 0, 11.733, -6.232, 0, 12.083, -11.507, 0, 12.783, 1.9, 0, 13.5, -2.893]}, {"Target": "Parameter", "Id": "ParamShoulderStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.371, 0, 0.483, -0.235, 0, 0.983, -9.529, 0, 1.55, 1.943, 0, 2, 1.239, 0, 2.5, 2.897, 1, 2.65, 2.897, 2.8, -5.111, 2.95, -6.639, 1, 3.05, -7.658, 3.15, -7.365, 3.25, -7.365, 0, 3.767, -2.298, 0, 4.45, -4.735, 0, 4.833, -2.881, 0, 5.15, -4.164, 0, 5.6, -3.573, 0, 6.1, -10.317, 0, 6.533, 0.605, 0, 7.017, -7.306, 0, 7.833, -2.298, 0, 8.517, -4.735, 0, 8.9, -2.881, 0, 9.217, -4.164, 0, 9.667, -3.573, 0, 10.167, -10.317, 0, 10.6, 0.605, 0, 11.083, -7.306, 0, 11.817, -6.888, 0, 12.2, -12.163, 0, 12.883, 1.704, 0, 13.5, -3.371]}, {"Target": "Parameter", "Id": "ParamButtXA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -6.39, 0, 0.183, -5.227, 0, 0.617, -13, 0, 1.183, -8.63, 0, 1.667, -9.332, 0, 2.15, -7.243, 0, 2.567, -15.371, 0, 2.917, -7.944, 0, 3.4, -11, 0, 4.067, -8, 0, 4.5, -9, 0, 4.817, -5.725, 0, 5.167, -7.701, 0, 5.417, -4.935, 0, 5.75, -13, 0, 6.333, 1.33, 0, 6.7, -3.539, 0, 7.033, -2.215, 0, 8.4, -9.004, 0, 8.883, -5.725, 0, 9.233, -6.34, 0, 9.483, -4.935, 0, 9.817, -7.571, 0, 10.4, 1.33, 0, 10.767, -3.539, 0, 11.1, -2.215, 0, 11.917, -6.285, 0, 12.4, 2, 0, 13.067, -8.518, 0, 13.5, -6.39]}, {"Target": "Parameter", "Id": "Param43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.915, 0, 0.25, -0.088, 0, 0.75, -10, 0, 1.317, 2.265, 0, 1.783, 1.26, 0, 2.267, 2.726, 0, 2.717, -7, 0, 3.017, -2, 0, 3.533, -6, 0, 4.217, -3.029, 0, 4.6, -5, 0, 4.917, -2.104, 0, 5.25, -6, 0, 5.5, -0.499, 0, 5.85, -6, 0, 6.417, 0.272, 0, 6.8, -1.253, 0, 7.133, 0.087, 0, 7.6, -6, 0, 8.283, -3.029, 0, 8.667, -5, 0, 8.983, -2.104, 0, 9.317, -2.812, 0, 9.567, -0.499, 0, 9.917, -2.63, 0, 10.483, 0.272, 0, 10.867, -1.253, 0, 11.2, 0.087, 0, 12.033, -6.247, 0, 12.667, 3.44, 0, 13.167, -3.148, 0, 13.5, -1.915]}, {"Target": "Parameter", "Id": "ParamButtZA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.239, 0, 0.45, -7, 0, 0.917, -1.802, 0, 1.467, -2.377, 0, 1.917, -0.482, 0, 2.35, -2, 0, 2.833, 5, 0, 3.133, -1, 0, 4.333, 1.404, 0, 4.683, -0.029, 0, 5.017, 0.42, 0, 5.317, -5, 0, 5.717, 8.117, 0, 6.317, -10.914, 0, 6.917, -4.449, 0, 7.25, -6.913, 0, 8.4, 1.404, 0, 8.75, -0.029, 0, 9.083, 0.42, 0, 9.4, -1.265, 0, 9.783, 8.117, 1, 9.833, 8.117, 9.883, 8.088, 9.933, 5.143, 1, 10.083, -3.691, 10.233, -10.914, 10.383, -10.914, 0, 10.983, -4.449, 0, 11.317, -6.913, 0, 12.017, -2.782, 0, 12.567, -11.734, 0, 13.5, -5.239]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.783, 0, 0, 3.267, 15, 2, 5.867, 15, 2, 6.333, 15, 2, 12.733, 15, 2, 13.5, 15]}, {"Target": "Parameter", "Id": "Param58", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.75, 0, 0, 2.783, 21, 0, 3.267, 0, 2, 5.867, 0, 0, 6.333, 15, 2, 12.733, 15, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.75, 0, 0, 2.783, -30, 0, 3.267, 0, 2, 5.867, 0, 0, 6.333, -30, 2, 12.733, -30, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 24.977, 2, 2.783, 24.977, 0, 3.417, 0.657, 2, 5.867, 0.657, 0, 6.417, 22.74, 2, 12.5, 22.74, 0, 13.5, 24.977]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -9.423, 2, 2.783, -9.423, 0, 3.417, 6, 2, 5.867, 6, 0, 6.417, 14.94, 2, 12.5, 14.94, 1, 12.694, 14.94, 12.889, -4.619, 13.083, -7.764, 1, 13.222, -10.01, 13.361, -9.422, 13.5, -9.422]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.036, 0, 0.5, 0.006, 0, 1.117, 2.036, 0, 2.55, 0.035, 1, 2.694, 0.035, 2.839, 3.135, 2.983, 11.87, 1, 3.083, 17.917, 3.183, 22.676, 3.283, 22.676, 1, 3.528, 22.676, 3.772, 21.947, 4.017, 21.497, 1, 4.478, 20.647, 4.939, 20.457, 5.4, 20.457, 0, 5.8, 22, 1, 5.978, 22, 6.155, 2.562, 6.333, 2, 1, 6.716, 0.788, 7.1, 0.735, 7.483, 0.735, 0, 8.617, 1.442, 0, 9.783, 0.645, 0, 10.5, 2.011, 0, 11.233, 0.797, 0, 11.917, 2, 0, 12.383, 0, 0, 13.5, 2.037]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.871, 0, 0.5, 6.031, 0, 1.117, -1.289, 0, 2.633, 3.84, 0, 3.067, -9, 0, 3.45, -0.762, 1, 3.639, -0.762, 3.828, -1.719, 4.017, -2.127, 1, 4.45, -3.062, 4.884, -3.258, 5.317, -3.258, 0, 5.6, -2.797, 0, 5.883, -5, 1, 6.061, -5, 6.239, 10.555, 6.417, 11, 1, 6.8, 11.96, 7.184, 12, 7.567, 12, 0, 8.717, 10.7, 0, 9.883, 12, 0, 10.583, 10.928, 1, 10.833, 10.928, 11.083, 11.344, 11.333, 11.573, 1, 11.566, 11.787, 11.8, 11.777, 12.033, 12, 1, 12.189, 12.148, 12.344, 13.46, 12.5, 13.46, 0, 13.25, -0.537, 0, 13.5, 0.864]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.132, 0, 0.5, 8.172, 1, 0.706, 8.172, 0.911, -1.096, 1.117, -4.788, 1, 1.528, -12.171, 1.939, -13.469, 2.35, -13.469, 0, 2.717, -7, 0, 3.15, -15, 0, 3.633, -3.333, 1, 3.833, -3.333, 4.033, -4.626, 4.233, -5.079, 1, 4.616, -5.946, 5, -6.065, 5.383, -6.065, 0, 5.817, 6, 0, 6.183, -20.315, 0, 6.717, -13.015, 0, 7.6, -14.95, 0, 8.733, -11.319, 0, 9.833, -15.102, 0, 10.983, -12.115, 0, 12.15, -15.6, 1, 12.306, -15.6, 12.461, -15.355, 12.617, -12, 1, 12.911, -5.649, 13.206, 0.13, 13.5, 0.13]}, {"Target": "Parameter", "Id": "Param5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.75, 0, 2, 2.783, 0, 0, 3.267, 8.04, 2, 5.867, 8.04, 0, 6.333, 0, 2, 12.733, 0, 2, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.75, 0, 0, 2.783, 30, 0, 3.267, 0, 2, 5.867, 0, 0, 6.333, -30, 2, 12.733, -30, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.75, 0, 0, 2, 30, 2, 2.783, 30, 0, 3.267, 0, 2, 5.867, 0, 0, 6.333, -19, 2, 12.8, -19, 2, 12.817, 0, 2, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamHandRREDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.75, 7, 2, 2.783, 7, 2, 3.267, 0, 2, 5.867, 0, 2, 6.333, 7, 2, 12.8, 7, 2, 12.817, 0, 2, 13.5, 0]}, {"Target": "Parameter", "Id": "Param55", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.75, 1, 2, 2.467, 1, 0, 2.983, 6, 2, 5.383, 6, 1, 5.411, 6, 5.439, 6.673, 5.467, 7.14, 1, 5.828, 13.212, 6.189, 15.99, 6.55, 15.99, 2, 6.867, 15.99, 1, 6.934, 15.99, 7, 15.772, 7.067, 15.403, 1, 8.928, 5.113, 10.789, 0, 12.65, 0, 0, 12.883, 15.99, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.733, 0, 2, 0.75, 30, 2, 2.467, 30, 1, 2.667, 30, 2.867, 17.355, 3.067, -9.633, 2, 3.083, -23.52, 2, 5.383, -23.52, 2, 5.75, -23.52, 2, 5.767, 20, 2, 6.25, 20, 2, 6.267, 20, 2, 6.867, 20, 2, 7.067, 29.879, 1, 9.039, 29.879, 11.011, 26.604, 12.983, 20, 2, 13, 0, 2, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.75, 0, 2, 2.467, 0, 2, 2.983, 0, 2, 5.383, 0, 2, 5.467, 0, 0, 6.55, 30, 2, 6.867, 30, 0, 7.067, 0, 2, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.75, 5.64, 2, 2.467, 5.64, 0, 3.35, -17.92, 2, 5.383, -17.92, 0, 6.233, 11, 1, 6.444, 11, 6.656, 8.155, 6.867, 8.009, 1, 8.795, 6.677, 10.722, 5.671, 12.65, 4.38, 1, 12.783, 4.291, 12.917, 0, 13.05, 0, 2, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.75, -1.02, 2, 2.467, -1.02, 0, 3.35, 18.6, 2, 5.383, 18.6, 0, 6.183, -16.263, 1, 6.411, -16.263, 6.639, -14.848, 6.867, -14.712, 1, 8.795, -13.561, 10.722, -12.695, 12.65, -11.58, 1, 12.783, -11.503, 12.917, 0, 13.05, 0, 2, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.089, 0, 0.217, -3.374, 0, 0.817, 15, 0, 1.3, 11.97, 0, 1.933, 14.3, 1, 2.133, 14.3, 2.333, 14.132, 2.533, 11.97, 1, 2.716, 9.988, 2.9, 2.449, 3.083, 1.5, 1, 3.205, 0.867, 3.328, 0.621, 3.45, 0.589, 1, 4.094, 0.423, 4.739, 0.376, 5.383, 0.376, 0, 5.967, 5, 0, 6.867, 1.993, 0, 7.983, 3.628, 0, 9.267, 1.281, 0, 10.767, 3.293, 0, 11.95, 1.993, 0, 12.8, 3.549, 0, 13.5, -0.089]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.769, 0, 0.3, -2, 0, 0.75, 10.516, 0, 1.233, 3.06, 0, 1.883, 4.767, 0, 2.467, 3.06, 1, 2.639, 3.06, 2.811, 18.789, 2.983, 26.029, 1, 3.105, 31.167, 3.228, 30.68, 3.35, 30.68, 1, 4.028, 30.68, 4.705, 30.457, 5.383, 30, 2, 5.4, -30, 1, 5.5, -30, 5.6, -30.775, 5.7, -28.442, 1, 5.878, -24.295, 6.055, 0.961, 6.233, 0.961, 0, 7, -1.479, 0, 8.083, -0.48, 0, 9.433, -2.028, 0, 10.95, 0.213, 1, 11.322, 0.213, 11.695, -0.202, 12.067, -1.479, 1, 12.261, -2.146, 12.456, -2.817, 12.65, -2.817, 0, 13.15, 5.449, 0, 13.5, 0.769]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.105, 0, 0.5, 7, 0, 0.967, -12, 0, 1.367, 0.48, 0, 1.983, -4.177, 1, 2.116, -4.177, 2.25, -4.651, 2.383, 0.48, 1, 2.561, 7.321, 2.739, 23.283, 2.917, 23.283, 0, 3.25, -3.754, 2, 5.383, -3.754, 1, 5.533, -3.754, 5.683, 7.418, 5.833, 10, 1, 6.028, 13.347, 6.222, 13.281, 6.417, 16.594, 1, 6.561, 19.056, 6.706, 30, 6.85, 30, 0, 7.283, 27.373, 2, 8.35, 27.373, 2, 10.233, 27.373, 0, 12.183, 30, 1, 12.372, 30, 12.561, 30, 12.75, 26, 1, 12.928, 21.766, 13.105, -11, 13.283, -11, 0, 13.5, -3.105]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.75, 0, 2, 2.467, 0, 0, 2.983, -11, 2, 12.65, -11, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.75, 30, 2, 2.467, 30, 0, 2.983, 0, 2, 5.383, 0, 2, 5.467, 0, 2, 6.55, 0, 2, 6.867, 0, 2, 7.067, 0, 2, 12.283, 0, 2, 12.65, 0, 2, 13.05, 0, 2, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.75, 0, 2, 2.467, 0, 0, 2.983, 21, 2, 5.383, 21, 0, 5.467, 0, 2, 6.55, 0, 2, 6.867, 0, 2, 7.067, 0, 2, 12.283, 0, 2, 12.65, 0, 2, 13.05, 0, 2, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamHandLREDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.6, 6, 2, 2.467, 6, 2, 2.983, 7, 2, 5.383, 7, 2, 5.467, 2, 2, 6.767, 2, 2, 7.067, 2, 2, 12.867, 2, 2, 12.883, 1, 2, 13.5, 1]}, {"Target": "Parameter", "Id": "Param100", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 6.733, 0, 0, 7.367, 1, 2, 12.783, 1, 2, 13.5, 1]}, {"Target": "Parameter", "Id": "Param3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 4.502, 0, 0.283, 6.211, 0, 0.867, 1.897, 0, 1.367, 3.39, 0, 1.767, 2.709, 0, 2.317, 5.087, 0, 2.717, 2.433, 0, 3.1, 5.343, 0, 4.35, 2.561, 0, 5.3, 5.945, 0, 5.75, -0.107, 0, 6.35, 7.425, 0, 6.867, 4.875, 0, 7.183, 5.632, 0, 8.483, 0.208, 1, 8.622, 0.208, 8.761, 0.036, 8.9, 0.391, 1, 9.239, 1.258, 9.578, 6.466, 9.917, 6.466, 0, 10.583, 3.376, 0, 11.8, 7.689, 0, 12.3, 5.918, 0, 12.717, 9.575, 0, 13.5, 4.445]}, {"Target": "Parameter", "Id": "Param2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.204, 0, 0.283, -0.854, 0, 0.867, 7.879, 0, 1.35, 4.482, 0, 1.767, 5.012, 0, 2.317, 1.686, 0, 2.717, 6.673, 0, 3.1, 0.739, 0, 4.35, 6.468, 0, 5.3, -0.327, 0, 5.75, 12.357, 0, 6.35, -2.923, 0, 6.867, 1.549, 0, 7.183, 0.244, 1, 7.661, 0.244, 8.139, 0.971, 8.617, 2, 1, 9.289, 3.448, 9.961, 4, 10.633, 4, 0, 13.5, 2.296]}, {"Target": "Parameter", "Id": "Param", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 7.526, 0, 0.3, 9.547, 0, 2.183, -2.068, 0, 4.35, 9.551, 0, 4.7, 8.94, 0, 4.983, 9.455, 0, 5.367, 5.79, 0, 5.733, 10.602, 0, 6.317, 3.689, 0, 6.9, 7.052, 1, 7.017, 7.052, 7.133, 7.76, 7.25, 6.294, 1, 7.983, -2.92, 8.717, -13.65, 9.45, -13.65, 1, 9.567, -13.65, 9.683, -13.304, 9.8, -10, 1, 10.061, -2.605, 10.322, 3.273, 10.583, 3.273, 0, 11.367, 0.431, 0, 12.3, 8.007, 0, 12.917, -0.88, 0, 13.5, 7.479]}, {"Target": "Parameter", "Id": "Param6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.323, 0, 0.267, -4.71, 0, 0.9, -6.2, 0, 1.233, -6.005, 0, 1.55, -6.105, 0, 2.2, -5.669, 0, 2.617, -6.123, 0, 3.1, -5.075, 0, 4.5, -5.202, 0, 4.917, -4.9, 0, 5.217, -5.033, 0, 5.417, -4.891, 0, 5.8, -5.782, 0, 6.383, -5.112, 0, 6.817, -5.269, 1, 6.922, -5.269, 7.028, -5.201, 7.133, -5.179, 1, 7.572, -5.089, 8.011, -5.059, 8.45, -4.946, 1, 8.906, -4.829, 9.361, -4.633, 9.817, -4.633, 0, 10.4, -4.992, 0, 12.65, -4.713, 0, 13.5, -5.347]}, {"Target": "Parameter", "Id": "Param48", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -8.246, 0, 0.267, -12.887, 0, 0.9, -1.649, 0, 1.233, -3.125, 0, 1.55, -2.378, 0, 2.2, -5.687, 0, 2.617, -2.272, 0, 3.1, -10.137, 0, 4.5, -9.191, 0, 4.917, -11.456, 0, 5.2, -10.448, 0, 5.417, -11.53, 0, 5.8, -4.835, 0, 6.383, -9.814, 0, 6.817, -8.649, 0, 7.133, -9.328, 1, 7.65, -9.328, 8.166, -8.667, 8.683, -7, 1, 9.294, -5.028, 9.906, -3.845, 10.517, -3.845, 0, 13.5, -8.069]}, {"Target": "Parameter", "Id": "Param37", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 8.94, 0, 0.583, 12.731, 0, 1.017, 12.399, 0, 2.6, 18.883, 0, 3.233, 11.534, 0, 4.333, 14.52, 0, 5.267, 10.101, 0, 5.7, 18.777, 0, 6.317, 5.161, 0, 6.883, 8.419, 0, 7.217, 7.664, 1, 7.95, 7.664, 8.684, 11.313, 9.417, 20.22, 1, 9.522, 21.502, 9.628, 23.716, 9.733, 23.716, 0, 10.383, 1.968, 0, 13.133, 12.107, 0, 13.5, 8.923]}, {"Target": "Parameter", "Id": "Param38", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 0.628, 0, 1.255, -3.689, 1.883, -3.775, 1, 4.355, -4.115, 6.828, -4.185, 9.3, -4.185, 2, 12.217, -4.185, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param60", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9.3, -13, 2, 12.217, -13, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.9, 2, 0.133, 0.9, 0, 0.483, 0, 0, 0.883, 0.9, 2, 1.817, 0.9, 2, 3.033, 0.9, 0, 3.35, 0, 0, 3.667, 0.9, 2, 4.517, 0.9, 0, 4.717, 0, 0, 4.933, 0.9, 2, 7.267, 0.9, 0, 7.467, 0, 0, 7.683, 0.9, 2, 9.083, 0.9, 0, 9.433, 0, 0, 9.833, 0.9, 2, 11.917, 0.9, 0, 12.267, 0, 0, 12.667, 0.9, 2, 13.5, 0.9]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.317, 0, 0, 0.617, 0.457, 0, 1.117, 0, 2, 1.217, 0, 0, 1.517, 0.457, 0, 1.817, 0, 2, 3.2, 0, 0, 3.45, 0.411, 0, 3.883, 0, 2, 4.617, 0, 0, 4.783, 0.457, 0, 5.067, 0, 2, 7.367, 0, 0, 7.533, 0.457, 0, 7.817, 0, 2, 9.267, 0, 0, 9.567, 0.457, 0, 10.067, 0, 2, 12.1, 0, 0, 12.4, 0.457, 0, 12.9, 0, 2, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.9, 2, 0.133, 0.9, 0, 0.483, 0, 0, 0.883, 0.9, 2, 1.817, 0.9, 2, 3.033, 0.9, 0, 3.35, 0, 0, 3.667, 0.9, 2, 4.517, 0.9, 0, 4.717, 0, 0, 4.933, 0.9, 2, 7.267, 0.9, 0, 7.467, 0, 0, 7.683, 0.9, 2, 9.083, 0.9, 0, 9.433, 0, 0, 9.833, 0.9, 2, 11.917, 0.9, 0, 12.267, 0, 0, 12.667, 0.9, 2, 13.5, 0.9]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.317, 0, 0, 0.617, 0.457, 0, 1.117, 0, 2, 1.217, 0, 0, 1.517, 0.457, 0, 1.817, 0, 2, 3.2, 0, 0, 3.45, 0.411, 0, 3.883, 0, 2, 4.617, 0, 0, 4.783, 0.457, 0, 5.067, 0, 2, 7.367, 0, 0, 7.533, 0.457, 0, 7.817, 0, 2, 9.267, 0, 0, 9.567, 0.457, 0, 10.067, 0, 2, 12.1, 0, 0, 12.4, 0.457, 0, 12.9, 0, 2, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.683, 0.07, 2, 1.283, 0.07, 0, 1.817, 0, 2, 3.033, 0, 0, 3.333, 0.07, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, 0.07, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, 0.07, 0, 7.683, 0, 2, 9.083, 0, 0, 9.433, 0.07, 0, 9.833, 0, 2, 11.917, 0, 0, 12.267, 0.07, 0, 12.667, 0, 2, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.683, 0.364, 2, 1.283, 0.364, 0, 1.817, 0, 2, 3.033, 0, 0, 3.333, 0.364, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, 0.364, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, 0.364, 0, 7.683, 0, 2, 9.083, 0, 0, 9.433, 0.364, 0, 9.833, 0, 2, 11.917, 0, 0, 12.267, 0.364, 0, 12.667, 0, 2, 13.5, 0]}, {"Target": "Parameter", "Id": "Param4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.683, -0.586, 2, 1.283, -0.586, 0, 1.817, 0, 2, 3.033, 0, 0, 3.333, -0.586, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, -0.586, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, -0.586, 0, 7.683, 0, 2, 9.083, 0, 0, 9.433, -0.586, 0, 9.833, 0, 2, 11.917, 0, 0, 12.267, -0.586, 0, 12.667, 0, 2, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.683, 0.262, 2, 1.283, 0.262, 0, 1.817, 0, 2, 3.033, 0, 0, 3.333, 0.262, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, 0.262, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, 0.262, 0, 7.683, 0, 2, 9.083, 0, 0, 9.433, 0.262, 0, 9.833, 0, 2, 11.917, 0, 0, 12.267, 0.262, 0, 12.667, 0, 2, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.683, 0.366, 2, 1.283, 0.366, 0, 1.817, 0, 2, 3.033, 0, 0, 3.333, 0.366, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, 0.366, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, 0.366, 0, 7.683, 0, 2, 9.083, 0, 0, 9.433, 0.366, 0, 9.833, 0, 2, 11.917, 0, 0, 12.267, 0.366, 0, 12.667, 0, 2, 13.5, 0]}, {"Target": "Parameter", "Id": "Param11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.817, -0.572, 2, 1.283, -0.572, 0, 1.817, 0, 2, 3.033, 0, 0, 3.333, -0.572, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, -0.572, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, -0.572, 0, 7.683, 0, 2, 9.083, 0, 0, 9.433, -0.594, 0, 9.833, 0, 2, 11.917, 0, 0, 12.267, -0.594, 0, 12.667, 0, 2, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.15, -0.244, 0, 0.333, -0.01, 0, 0.45, -0.319, 2, 0.633, -0.319, 2, 0.817, -0.319, 0, 0.9, 0.656, 0, 0.95, -0.319, 2, 1.133, -0.319, 2, 1.333, -0.319, 2, 1.367, -0.319, 0, 1.533, 0.5, 1, 2.028, 0.5, 2.522, 0.36, 3.017, 0, 1, 3.067, -0.036, 3.117, -0.244, 3.167, -0.244, 0, 3.35, -0.01, 0, 3.467, -0.319, 2, 3.65, -0.319, 2, 3.833, -0.319, 0, 3.917, 0.656, 0, 3.967, -0.319, 2, 4.15, -0.319, 2, 4.35, -0.319, 2, 4.383, -0.319, 0, 4.55, 0.5, 0, 4.65, -0.319, 2, 4.683, -0.319, 1, 4.739, -0.319, 4.794, 0.475, 4.85, 0.5, 1, 5.356, 0.727, 5.861, 0.808, 6.367, 0.808, 0, 6.583, -0.319, 1, 6.605, -0.319, 6.628, 0.148, 6.65, 0.34, 1, 6.711, 0.869, 6.772, 1, 6.833, 1, 1, 6.839, 1, 6.844, 0.829, 6.85, 0.8, 1, 6.872, 0.685, 6.895, 0.656, 6.917, 0.656, 0, 6.967, 1, 0, 7.15, -0.319, 0, 7.35, 0.224, 2, 7.483, 0.224, 0, 7.567, 0, 0, 7.65, 0.34, 1, 7.672, 0.34, 7.695, 0.012, 7.717, 0, 1, 8.128, -0.224, 8.539, -0.319, 8.95, -0.319, 0, 8.983, 0.808, 1, 9.016, 0.808, 9.05, 0.586, 9.083, 0, 1, 9.089, -0.098, 9.094, -0.319, 9.1, -0.319, 1, 9.122, -0.319, 9.145, 0.148, 9.167, 0.34, 1, 9.228, 0.869, 9.289, 1, 9.35, 1, 0, 9.367, 0.5, 0, 10.617, 0.808, 0, 10.833, -0.319, 1, 10.855, -0.319, 10.878, 0.148, 10.9, 0.34, 1, 10.961, 0.869, 11.022, 1, 11.083, 1, 1, 11.089, 1, 11.094, 0.829, 11.1, 0.8, 1, 11.122, 0.685, 11.145, 0.656, 11.167, 0.656, 0, 11.217, 1, 0, 11.4, -0.319, 1, 11.467, -0.319, 11.533, 0.142, 11.6, 0.224, 1, 11.644, 0.279, 11.689, 0.241, 11.733, 0.3, 1, 11.828, 0.426, 11.922, 0.808, 12.017, 0.808, 0, 12.233, -0.319, 1, 12.255, -0.319, 12.278, 0.148, 12.3, 0.34, 1, 12.361, 0.869, 12.422, 1, 12.483, 1, 1, 12.489, 1, 12.494, 0.829, 12.5, 0.8, 1, 12.522, 0.685, 12.545, 0.656, 12.567, 0.656, 0, 12.617, 1, 0, 12.8, -0.319, 0, 13, 0.224, 2, 13.133, 0.224, 0, 13.217, 0, 2, 13.367, 0, 2, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 0.05, 0, 0.1, 0.185, 0.15, 0.5, 1, 0.206, 0.85, 0.261, 1, 0.317, 1, 0, 0.5, 0.102, 0, 0.683, 0.969, 0, 0.9, 0.012, 0, 1.1, 0.663, 0, 1.183, 0.024, 0, 1.333, 0.871, 2, 1.367, 0.871, 0, 1.533, 0, 2, 3.017, 0, 1, 3.067, 0, 3.117, 0.185, 3.167, 0.5, 1, 3.222, 0.85, 3.278, 1, 3.333, 1, 0, 3.517, 0.102, 0, 3.7, 0.969, 0, 3.917, 0.012, 0, 4.117, 0.663, 0, 4.2, 0.024, 0, 4.35, 0.871, 2, 4.383, 0.871, 0, 4.55, 0, 0, 4.65, 0.871, 2, 4.683, 0.871, 0, 4.85, 0, 2, 6.367, 0, 1, 6.417, 0, 6.467, 0.232, 6.517, 0.396, 1, 6.55, 0.505, 6.584, 0.5, 6.617, 0.5, 0, 6.85, 0.323, 0, 6.967, 0.9, 0, 7.05, 0.016, 0, 7.117, 0.663, 0, 7.2, 0.024, 0, 7.35, 0.871, 2, 7.483, 0.871, 0, 7.567, 0, 2, 7.65, 0, 2, 7.717, 0, 2, 8.983, 0, 2, 9.067, 0, 0, 9.133, 0.5, 0, 9.367, 0, 2, 10.617, 0, 1, 10.667, 0, 10.717, 0.233, 10.767, 0.396, 1, 10.8, 0.505, 10.834, 0.5, 10.867, 0.5, 0, 11.1, 0.323, 0, 11.217, 0.9, 0, 11.3, 0.016, 0, 11.367, 0.663, 0, 11.45, 0.024, 0, 11.6, 0.871, 0, 11.733, 0, 2, 12.017, 0, 1, 12.067, 0, 12.117, 0.233, 12.167, 0.396, 1, 12.2, 0.505, 12.234, 0.5, 12.267, 0.5, 0, 12.5, 0.323, 0, 12.617, 0.9, 0, 12.7, 0.016, 0, 12.767, 0.663, 0, 12.85, 0.024, 0, 13, 0.871, 2, 13.133, 0.871, 0, 13.217, 0, 2, 13.367, 0, 2, 13.5, 0]}, {"Target": "Parameter", "Id": "Hair_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.01, 0, 0.183, 3.954, 0, 0.617, -10.316, 0, 1.183, 6.212, 0, 1.667, 3.557, 0, 2.15, 11.458, 0, 2.567, -19.284, 0, 2.917, 8.806, 0, 3.4, -2.752, 0, 4.067, 8.595, 0, 4.5, 4.812, 0, 4.817, 17.199, 0, 5.167, 9.725, 0, 5.417, 20.187, 0, 5.75, -10.316, 0, 6.333, 17.407, 0, 6.7, -1.008, 0, 7.033, 3.999, 0, 8.4, -6.549, 0, 8.883, 5.852, 0, 9.233, 3.526, 0, 9.483, 8.84, 0, 9.817, -4.859, 0, 10.4, 17.407, 0, 10.767, -1.008, 0, 11.1, 3.999, 0, 11.917, -11.394, 0, 12.4, 19.941, 0, 13.067, -19.84, 0, 13.5, 0.01]}, {"Target": "Parameter", "Id": "Hair_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.01, 0, 0.183, 1.173, 0, 0.617, -2.6, 0, 1.183, 1.77, 0, 1.667, 1.068, 0, 2.15, 3.157, 0, 2.567, -4.971, 0, 2.917, 2.456, 0, 3.4, -0.6, 0, 4.067, 2.4, 0, 4.5, 1.4, 0, 4.817, 4.675, 0, 5.167, 2.699, 0, 5.417, 5.465, 0, 5.75, -2.6, 0, 6.333, 4.73, 0, 6.7, -0.139, 0, 7.033, 1.185, 0, 8.4, -1.604, 0, 8.883, 1.675, 0, 9.233, 1.06, 0, 9.483, 2.465, 0, 9.817, -1.157, 0, 10.4, 4.73, 0, 10.767, -0.139, 0, 11.1, 1.185, 0, 11.917, -2.885, 0, 12.4, 5.4, 0, 13.067, -5.118, 0, 13.5, 0.01]}, {"Target": "Parameter", "Id": "Param69", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.01, 0, 0.183, 1.173, 0, 0.617, -2.6, 0, 1.183, 1.77, 0, 1.667, 1.068, 0, 2.15, 3.157, 0, 2.567, -4.971, 0, 2.917, 2.456, 0, 3.4, -0.6, 0, 4.067, 2.4, 0, 4.5, 1.4, 0, 4.817, 4.675, 0, 5.167, 2.699, 0, 5.417, 5.465, 0, 5.75, -2.6, 0, 6.333, 4.73, 0, 6.7, -0.139, 0, 7.033, 1.185, 0, 8.4, -1.604, 0, 8.883, 1.675, 0, 9.233, 1.06, 0, 9.483, 2.465, 0, 9.817, -1.157, 0, 10.4, 4.73, 0, 10.767, -0.139, 0, 11.1, 1.185, 0, 11.917, -2.885, 0, 12.4, 5.4, 0, 13.067, -5.118, 0, 13.5, 0.01]}, {"Target": "Parameter", "Id": "Param153", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.014, 0, 0.167, -5.172, 0, 0.5, 9.667, 0, 0.933, -11.131, 0, 1.383, 5.659, 0, 1.95, -5.486, 0, 2.433, 21.07, 0, 2.817, -28.993, 0, 3.217, 16.589, 0, 3.667, -9.165, 0, 4.267, 4.258, 0, 4.733, -11.11, 0, 5.083, 9.644, 0, 5.383, -11.45, 0, 5.683, 24.746, 0, 6.067, -21.029, 0, 6.567, 19.793, 0, 6.95, -11.35, 0, 7.383, 4.7, 0, 7.833, 0.437, 0, 8.1, 0.862, 0, 8.717, -7.005, 0, 9.117, 4.874, 0, 9.45, -6.016, 0, 9.75, 11.51, 0, 10.15, -14.156, 0, 10.65, 17.747, 0, 11.017, -10.743, 0, 11.467, 7.721, 0, 12.217, -17.699, 0, 12.7, 20.788, 0, 13.317, -17.231, 1, 13.378, -17.231, 13.439, -13.986, 13.5, -6.504]}, {"Target": "Parameter", "Id": "Param154", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.009, 0, 0.167, 3.502, 0, 0.45, -7.046, 0, 0.783, 9.465, 0, 1.217, -8.601, 0, 1.633, 5.447, 0, 2.333, -9.173, 0, 2.717, 21.394, 0, 3.067, -24.082, 0, 3.483, 16.4, 0, 3.917, -8.498, 0, 4.45, 3.689, 0, 4.517, 3.583, 0, 4.65, 5.135, 0, 4.983, -10.202, 0, 5.333, 12.633, 0, 5.633, -17.635, 0, 5.95, 19.887, 0, 6.383, -16.985, 0, 6.817, 17.959, 0, 7.2, -11.803, 0, 7.617, 5.255, 0, 8.05, -1.827, 0, 8.6, 2.874, 0, 8.983, -5.195, 0, 9.383, 6.449, 0, 9.7, -9.143, 0, 10.017, 11.442, 0, 10.483, -11.82, 0, 10.883, 15.375, 0, 11.283, -11.518, 0, 11.717, 6.122, 0, 11.95, 1.895, 0, 12.133, 4.116, 0, 12.517, -13.549, 0, 12.983, 11.812, 1, 13.155, 11.812, 13.328, -0.278, 13.5, -12.16]}, {"Target": "Parameter", "Id": "Param155", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.15, 0, 0, 0.367, 1.528, 0, 0.65, -4.057, 0, 0.983, 7.874, 0, 1.383, -8.873, 0, 1.833, 6.858, 0, 2.433, -7.769, 0, 2.867, 18.088, 0, 3.267, -21.977, 0, 3.667, 18.049, 0, 4.117, -11.556, 0, 4.617, 6.194, 0, 5.15, -8.821, 0, 5.5, 9.813, 0, 5.817, -11.675, 0, 6.15, 15.92, 0, 6.567, -18.016, 0, 7, 18.115, 0, 7.4, -13.462, 0, 7.833, 7.535, 0, 8.25, -3.348, 0, 8.733, 3.009, 0, 9.15, -4.95, 0, 9.55, 5.677, 0, 9.883, -6.626, 0, 10.217, 9.303, 0, 10.65, -12.353, 0, 11.067, 14.653, 0, 11.483, -12.477, 0, 11.917, 8.234, 0, 12.667, -11.008, 0, 13.15, 12.98, 1, 13.267, 12.98, 13.383, 5.746, 13.5, -4.819]}, {"Target": "Parameter", "Id": "Param156", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.176, 0, 0.217, 0, 2, 0.567, 0, 0, 0.8, -3.108, 0, 1.167, 6.817, 0, 1.583, -8.886, 0, 2.017, 8.014, 0, 2.55, -8.344, 0, 3.017, 16.048, 0, 3.433, -20.867, 0, 3.867, 19.308, 0, 4.3, -14.279, 0, 4.767, 9.143, 0, 5.3, -8.905, 0, 5.683, 8.491, 0, 6, -8.179, 0, 6.35, 12.839, 0, 6.75, -17.564, 0, 7.167, 18.574, 0, 7.6, -15.223, 0, 8.033, 9.843, 0, 8.45, -5.176, 0, 8.9, 3.831, 0, 9.317, -4.902, 0, 9.717, 5.222, 0, 10.067, -5.198, 0, 10.417, 7.784, 0, 10.817, -11.994, 0, 11.25, 14.462, 0, 11.667, -13.487, 0, 12.1, 10.252, 0, 12.8, -9.441, 0, 13.3, 13.27, 1, 13.367, 13.27, 13.433, 11.392, 13.5, 4.896]}, {"Target": "Parameter", "Id": "Param82", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.013, 0, 0.167, -4.822, 0, 0.5, 9.012, 0, 0.933, -10.376, 0, 1.383, 5.276, 0, 1.95, -5.114, 0, 2.433, 19.642, 0, 2.817, -27.028, 0, 3.217, 15.464, 0, 3.667, -8.543, 0, 4.267, 3.969, 0, 4.733, -10.357, 0, 5.083, 8.99, 0, 5.383, -10.674, 0, 5.683, 23.069, 0, 6.067, -19.604, 0, 6.567, 18.451, 0, 6.95, -10.581, 0, 7.383, 4.382, 0, 7.833, 0.408, 0, 8.1, 0.804, 0, 8.717, -6.53, 0, 9.117, 4.544, 0, 9.45, -5.608, 0, 9.75, 10.73, 0, 10.15, -13.197, 0, 10.65, 16.544, 0, 11.017, -10.015, 0, 11.467, 7.197, 0, 12.217, -16.5, 0, 12.7, 19.38, 0, 13.317, -16.063, 1, 13.378, -16.063, 13.439, -13.038, 13.5, -6.063]}, {"Target": "Parameter", "Id": "Param83", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.009, 0, 0.167, 3.355, 0, 0.45, -6.751, 0, 0.783, 9.068, 0, 1.217, -8.24, 0, 1.633, 5.219, 0, 2.333, -8.789, 0, 2.717, 20.497, 0, 3.067, -23.072, 0, 3.483, 15.713, 0, 3.917, -8.141, 0, 4.45, 3.535, 0, 4.517, 3.433, 0, 4.65, 4.92, 0, 4.983, -9.774, 0, 5.333, 12.104, 0, 5.633, -16.895, 0, 5.95, 19.053, 0, 6.383, -16.273, 0, 6.817, 17.206, 0, 7.2, -11.308, 0, 7.617, 5.034, 0, 8.05, -1.75, 0, 8.6, 2.754, 0, 8.983, -4.978, 0, 9.383, 6.178, 0, 9.7, -8.759, 0, 10.017, 10.962, 0, 10.483, -11.325, 0, 10.883, 14.73, 0, 11.283, -11.035, 0, 11.717, 5.865, 0, 11.95, 1.816, 0, 12.133, 3.944, 0, 12.517, -12.98, 0, 12.983, 11.316, 1, 13.155, 11.316, 13.328, -0.256, 13.5, -11.651]}, {"Target": "Parameter", "Id": "Param84", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.15, 0, 0, 0.367, 1.562, 0, 0.65, -4.147, 0, 0.983, 8.05, 0, 1.383, -9.07, 0, 1.833, 7.01, 0, 2.433, -7.941, 0, 2.867, 18.49, 0, 3.267, -22.466, 0, 3.667, 18.451, 0, 4.117, -11.813, 0, 4.617, 6.331, 0, 5.15, -9.017, 0, 5.5, 10.031, 0, 5.817, -11.934, 0, 6.15, 16.274, 0, 6.567, -18.417, 0, 7, 18.517, 0, 7.4, -13.761, 0, 7.833, 7.702, 0, 8.25, -3.422, 0, 8.733, 3.075, 0, 9.15, -5.06, 0, 9.55, 5.803, 0, 9.883, -6.773, 0, 10.217, 9.51, 0, 10.65, -12.628, 0, 11.067, 14.978, 0, 11.483, -12.754, 0, 11.917, 8.417, 0, 12.667, -11.253, 0, 13.15, 13.269, 1, 13.267, 13.269, 13.383, 5.874, 13.5, -4.926]}, {"Target": "Parameter", "Id": "Param85", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.179, 0, 0.217, 0, 2, 0.567, 0, 0, 0.8, -3.169, 0, 1.167, 6.951, 0, 1.583, -9.06, 0, 2.017, 8.171, 0, 2.55, -8.507, 0, 3.017, 16.362, 0, 3.433, -21.275, 0, 3.867, 19.686, 0, 4.3, -14.558, 0, 4.767, 9.322, 0, 5.3, -9.08, 0, 5.683, 8.657, 0, 6, -8.339, 0, 6.35, 13.09, 0, 6.75, -17.908, 0, 7.167, 18.938, 0, 7.6, -15.521, 0, 8.033, 10.035, 0, 8.45, -5.277, 0, 8.9, 3.906, 0, 9.317, -4.998, 0, 9.717, 5.324, 0, 10.067, -5.3, 0, 10.417, 7.936, 0, 10.817, -12.229, 0, 11.25, 14.745, 0, 11.667, -13.751, 0, 12.1, 10.452, 0, 12.8, -9.626, 0, 13.3, 13.53, 1, 13.367, 13.53, 13.433, 11.614, 13.5, 4.991]}, {"Target": "Parameter", "Id": "Param66", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.45, 0, 0, 0.633, -6.65, 0, 0.883, 10.034, 0, 1.2, -7.973, 0, 1.567, 4.543, 0, 2.017, -3.735, 0, 2.233, -1.308, 0, 2.433, -4.537, 0, 2.767, 14.03, 0, 3.1, -19.688, 0, 3.433, 15.37, 0, 3.783, -7.77, 0, 4.117, 2.361, 0, 4.383, -0.733, 0, 4.75, 3.236, 0, 5.05, -7.353, 0, 5.383, 10.016, 0, 5.683, -16.447, 0, 5.983, 21.318, 0, 6.3, -16.451, 0, 6.817, 7.91, 0, 7.183, -8.482, 0, 7.517, 5.312, 0, 7.833, -2.325, 0, 8.167, 0.697, 0, 8.45, 0.132, 0, 8.7, 1.453, 0, 9.017, -3.752, 0, 9.4, 4.073, 0, 9.733, -7.339, 0, 10.05, 10.723, 0, 10.383, -8.847, 0, 10.917, 8.225, 0, 11.267, -8.439, 0, 11.617, 5.08, 0, 11.917, -1.733, 0, 12.217, 4.497, 0, 12.567, -9.701, 0, 12.917, 7.873, 1, 13.111, 7.873, 13.306, 2.096, 13.5, -4.509]}, {"Target": "Parameter", "Id": "Param67", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.071, 0, 0.15, -7.281, 0, 0.4, 9.99, 0, 0.7, -9.2, 0, 1.033, 1.68, 0, 1.133, 1.483, 0, 1.233, 2.106, 0, 1.883, -2.68, 0, 2.35, 13.499, 0, 2.7, -21.81, 0, 3, 19.942, 0, 3.283, -8.473, 0, 3.567, 0.927, 0, 3.8, -2.228, 0, 4.133, 2.53, 0, 4.683, -7.588, 0, 4.95, 10.438, 0, 5.3, -12.303, 0, 5.583, 25.64, 0, 5.85, -25.302, 0, 6.15, 7.386, 0, 6.333, 0.975, 0, 6.533, 9.113, 0, 6.8, -10.747, 0, 7.1, 6.64, 0, 7.35, -2.448, 0, 7.617, 1.974, 0, 7.9, -0.357, 0, 8.133, 0.416, 0, 8.617, -4.366, 0, 8.95, 4.491, 0, 9.367, -4.676, 0, 9.65, 11.383, 0, 9.933, -12.795, 0, 10.25, 2.924, 0, 10.367, 2.288, 0, 10.583, 9.192, 0, 10.867, -11.136, 0, 11.167, 7.048, 0, 11.433, -0.85, 0, 11.667, 1.669, 0, 12.117, -11.031, 0, 12.5, 12.601, 0, 12.85, 0.502, 0, 12.867, 0.508, 0, 13.217, -9.186, 1, 13.311, -9.186, 13.406, -1.348, 13.5, 6.974]}, {"Target": "Parameter", "Id": "Param68", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.041, 0, 0.15, 4, 0, 0.333, -7.35, 0, 0.583, 8.604, 0, 0.85, -7.014, 0, 1.133, 2.589, 0, 1.35, -0.352, 0, 1.533, 0.259, 0, 1.667, 0.061, 0, 1.8, 0.49, 0, 2.067, -1.762, 0, 2.167, -1.314, 0, 2.267, -2.561, 0, 2.533, 9.75, 0, 2.883, -15.336, 0, 3.15, 15.574, 0, 3.433, -9.128, 0, 3.7, 4.452, 0, 3.967, -3.185, 0, 4.267, 1.963, 0, 4.517, -0.272, 0, 4.633, 1.364, 0, 4.867, -6.611, 0, 5.117, 8.16, 0, 5.483, -12.56, 0, 5.75, 21.876, 0, 6.017, -20.264, 0, 6.283, 10.343, 0, 6.5, -5.9, 0, 6.733, 8.288, 0, 6.983, -8.61, 0, 7.25, 6.627, 0, 7.5, -3.995, 0, 7.767, 2.541, 0, 8.033, -1.111, 0, 8.3, 0.785, 0, 8.433, 0.337, 0, 8.533, 0.733, 0, 8.817, -3.26, 0, 9.1, 3.288, 0, 9.567, -4.677, 0, 9.833, 9.559, 0, 10.1, -9.337, 0, 10.367, 3.791, 0, 10.55, -2.639, 0, 10.783, 7.547, 0, 11.033, -8.54, 0, 11.317, 6.308, 0, 11.567, -3.119, 0, 11.833, 2.508, 0, 11.95, 1.389, 0, 12.033, 1.663, 0, 12.333, -6.693, 0, 12.65, 6.785, 0, 12.9, -1.482, 0, 13.133, 3.11, 0, 13.417, -5.963, 1, 13.445, -5.963, 13.472, -5.79, 13.5, -3.594]}, {"Target": "Parameter", "Id": "Param62", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.45, 0, 0, 0.633, 6.65, 0, 0.883, -10.034, 0, 1.2, 7.973, 0, 1.567, -4.543, 0, 2.017, 3.735, 0, 2.233, 1.308, 0, 2.433, 4.537, 0, 2.767, -14.03, 0, 3.1, 19.688, 0, 3.433, -15.37, 0, 3.783, 7.77, 0, 4.117, -2.361, 0, 4.383, 0.733, 0, 4.75, -3.236, 0, 5.05, 7.353, 0, 5.383, -10.016, 0, 5.683, 16.447, 0, 5.983, -21.318, 0, 6.3, 16.451, 0, 6.817, -7.91, 0, 7.183, 8.482, 0, 7.517, -5.312, 0, 7.833, 2.325, 0, 8.167, -0.697, 0, 8.45, -0.132, 0, 8.7, -1.453, 0, 9.017, 3.752, 0, 9.4, -4.073, 0, 9.733, 7.339, 0, 10.05, -10.723, 0, 10.383, 8.847, 0, 10.917, -8.225, 0, 11.267, 8.439, 0, 11.617, -5.08, 0, 11.917, 1.733, 0, 12.217, -4.497, 0, 12.567, 9.701, 0, 12.917, -7.873, 1, 13.111, -7.873, 13.306, -2.096, 13.5, 4.509]}, {"Target": "Parameter", "Id": "Param63", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.071, 0, 0.15, 7.281, 0, 0.4, -9.99, 0, 0.7, 9.2, 0, 1.033, -1.68, 0, 1.133, -1.483, 0, 1.233, -2.106, 0, 1.883, 2.68, 0, 2.35, -13.499, 0, 2.7, 21.81, 0, 3, -19.942, 0, 3.283, 8.473, 0, 3.567, -0.927, 0, 3.8, 2.228, 0, 4.133, -2.53, 0, 4.683, 7.588, 0, 4.95, -10.438, 0, 5.3, 12.303, 0, 5.583, -25.64, 0, 5.85, 25.302, 0, 6.15, -7.386, 0, 6.333, -0.975, 0, 6.533, -9.113, 0, 6.8, 10.747, 0, 7.1, -6.64, 0, 7.35, 2.448, 0, 7.617, -1.974, 0, 7.9, 0.357, 0, 8.133, -0.416, 0, 8.617, 4.366, 0, 8.95, -4.491, 0, 9.367, 4.676, 0, 9.65, -11.383, 0, 9.933, 12.795, 0, 10.25, -2.924, 0, 10.367, -2.288, 0, 10.583, -9.192, 0, 10.867, 11.136, 0, 11.167, -7.048, 0, 11.433, 0.85, 0, 11.667, -1.669, 0, 12.117, 11.031, 0, 12.5, -12.601, 0, 12.85, -0.502, 0, 12.867, -0.508, 0, 13.217, 9.186, 1, 13.311, 9.186, 13.406, 1.348, 13.5, -6.974]}, {"Target": "Parameter", "Id": "Param64", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.041, 0, 0.15, -4, 0, 0.333, 7.35, 0, 0.583, -8.604, 0, 0.85, 7.014, 0, 1.133, -2.589, 0, 1.35, 0.352, 0, 1.533, -0.259, 0, 1.667, -0.061, 0, 1.8, -0.49, 0, 2.067, 1.762, 0, 2.167, 1.314, 0, 2.267, 2.561, 0, 2.533, -9.75, 0, 2.883, 15.336, 0, 3.15, -15.574, 0, 3.433, 9.128, 0, 3.7, -4.452, 0, 3.967, 3.185, 0, 4.267, -1.963, 0, 4.517, 0.272, 0, 4.633, -1.364, 0, 4.867, 6.611, 0, 5.117, -8.16, 0, 5.483, 12.56, 0, 5.75, -21.876, 0, 6.017, 20.264, 0, 6.283, -10.343, 0, 6.5, 5.9, 0, 6.733, -8.288, 0, 6.983, 8.61, 0, 7.25, -6.627, 0, 7.5, 3.995, 0, 7.767, -2.541, 0, 8.033, 1.111, 0, 8.3, -0.785, 0, 8.433, -0.337, 0, 8.533, -0.733, 0, 8.817, 3.26, 0, 9.1, -3.288, 0, 9.567, 4.677, 0, 9.833, -9.559, 0, 10.1, 9.337, 0, 10.367, -3.791, 0, 10.55, 2.639, 0, 10.783, -7.547, 0, 11.033, 8.54, 0, 11.317, -6.308, 0, 11.567, 3.119, 0, 11.833, -2.508, 0, 11.95, -1.389, 0, 12.033, -1.663, 0, 12.333, 6.693, 0, 12.65, -6.785, 0, 12.9, 1.482, 0, 13.133, -3.11, 0, 13.417, 5.963, 1, 13.445, 5.963, 13.472, 5.79, 13.5, 3.594]}, {"Target": "Parameter", "Id": "Param86", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.004, 0, 0.183, -0.513, 0, 0.617, 1.137, 0, 1.183, -0.774, 0, 1.667, -0.467, 0, 2.15, -1.381, 0, 2.567, 2.174, 0, 2.917, -1.074, 0, 3.4, 0.262, 0, 4.067, -1.05, 0, 4.5, -0.612, 0, 4.817, -2.045, 0, 5.167, -1.181, 0, 5.417, -2.39, 0, 5.75, 1.137, 0, 6.333, -2.069, 0, 6.7, 0.061, 0, 7.033, -0.518, 0, 8.4, 0.702, 0, 8.883, -0.733, 0, 9.233, -0.464, 0, 9.483, -1.078, 0, 9.817, 0.506, 0, 10.4, -2.069, 0, 10.767, 0.061, 0, 11.1, -0.518, 0, 11.917, 1.262, 0, 12.4, -2.362, 0, 13.067, 2.239, 0, 13.5, -0.004]}, {"Target": "Parameter", "Id": "Param89", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.036, 0, 0.183, -4.188, 0, 0.567, 8.574, 0, 0.917, -6.397, 0, 1.317, 2.677, 0, 1.917, -2.399, 0, 2.4, 11.165, 0, 2.783, -15.523, 0, 3.117, 8.923, 0, 3.55, -3.972, 0, 4.283, 1.477, 0, 4.717, -5.991, 0, 5.033, 5.418, 0, 5.367, -7.455, 0, 5.65, 15.498, 0, 5.983, -11.526, 0, 6.533, 8.821, 0, 6.883, -5.653, 0, 7.233, 2.298, 0, 7.567, 0.324, 0, 7.667, 0.453, 0, 7.75, 0.265, 0, 8.017, 0.642, 0, 8.683, -3.709, 0, 9.033, 2.481, 0, 9.417, -3.612, 0, 9.717, 7.119, 0, 10.083, -7.373, 0, 10.617, 8.479, 0, 10.967, -5.142, 0, 11.367, 3.392, 0, 12.2, -9.413, 0, 12.65, 9.703, 0, 13.283, -7.638, 1, 13.355, -7.638, 13.428, -5.156, 13.5, -1.195]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.032, 0, 0.183, 3.757, 0, 0.55, -7.561, 0, 0.817, 10.918, 0, 1.15, -6.335, 0, 1.5, 3.27, 0, 2.317, -5.13, 0, 2.667, 12.263, 0, 2.983, -15.569, 0, 3.333, 9.609, 0, 3.733, -4.68, 0, 4.05, 1.105, 0, 4.283, -0.298, 0, 4.65, 3.251, 0, 4.933, -6.368, 0, 5.283, 7.894, 0, 5.567, -13.78, 0, 5.867, 16.437, 0, 6.2, -9.915, 0, 6.75, 7.593, 0, 7.083, -6.406, 0, 7.417, 2.986, 0, 7.667, -0.632, 0, 8.25, 0.298, 0, 8.4, 0.201, 0, 8.6, 1.501, 0, 8.9, -4.086, 0, 9.317, 3.14, 0, 9.633, -6.355, 0, 9.933, 8.175, 0, 10.283, -5.503, 0, 10.817, 7.18, 0, 11.183, -4.943, 0, 11.483, 2.332, 0, 11.833, 0.166, 0, 12.1, 3.826, 0, 12.45, -7.862, 0, 12.833, 5.04, 0, 13.5, -5.306]}, {"Target": "Parameter", "Id": "Param88", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.55, 0, 0, 0.75, 7.087, 0, 0.983, -10.832, 0, 1.283, 8.057, 0, 1.6, -4.728, 0, 1.917, 1.103, 0, 2.1, 0.381, 0, 2.433, 3.826, 0, 2.767, -10.732, 0, 3.1, 14.555, 0, 3.433, -11.171, 0, 3.783, 5.671, 0, 4.133, -2.112, 0, 4.417, 0.71, 0, 4.75, -2.504, 0, 5.05, 5.429, 0, 5.383, -7.361, 0, 5.683, 12.071, 0, 5.983, -15.684, 0, 6.3, 12.118, 0, 6.817, -5.804, 0, 7.183, 6.225, 0, 7.517, -3.903, 0, 7.817, 1.698, 0, 8.167, -0.507, 0, 8.45, -0.096, 0, 8.7, -1.067, 0, 8.717, -1.054, 0, 8.75, -1.178, 0, 9.05, 2.921, 0, 9.383, -3.322, 0, 9.733, 5.493, 0, 10.05, -7.953, 0, 10.383, 6.515, 0, 10.883, -5.468, 0, 11.267, 6.087, 0, 11.6, -3.595, 0, 11.917, 0.774, 0, 12.217, -3.032, 0, 12.567, 6.983, 0, 12.917, -5.719, 1, 13.111, -5.719, 13.306, -1.216, 13.5, 3.651]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.75, 0, 0, 0.917, -5.343, 0, 1.133, 9.047, 0, 1.417, -8.482, 0, 1.717, 6.022, 0, 2.033, -2.687, 0, 2.283, 0.082, 0, 2.533, -2.923, 0, 2.867, 9.235, 0, 3.2, -13.672, 0, 3.533, 12.38, 0, 3.883, -7.636, 0, 4.217, 3.428, 2, 4.233, 3.428, 0, 4.533, -1.406, 0, 4.85, 2.261, 0, 5.15, -4.729, 0, 5.483, 6.896, 0, 5.783, -10.818, 0, 6.1, 14.895, 0, 6.417, -13.574, 0, 6.783, 7.058, 0, 7.283, -6.09, 0, 7.617, 4.596, 0, 7.917, -2.87, 0, 8.25, 1.155, 0, 8.55, -0.184, 0, 8.817, 0.938, 0, 9.133, -2.669, 0, 9.483, 3.341, 0, 9.833, -4.633, 0, 10.167, 7.31, 0, 10.5, -7.142, 0, 10.967, 5.045, 0, 11.367, -5.48, 0, 11.717, 4.145, 0, 12.017, -1.624, 0, 12.317, 2.74, 0, 12.667, -6.29, 0, 13.017, 6.114, 0, 13.367, -1.379, 0, 13.5, -1.015]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.004, 0, 0.183, 0.513, 0, 0.617, -1.137, 0, 1.183, 0.774, 0, 1.667, 0.467, 0, 2.15, 1.381, 0, 2.567, -2.174, 0, 2.917, 1.074, 0, 3.4, -0.262, 0, 4.067, 1.05, 0, 4.5, 0.612, 0, 4.817, 2.045, 0, 5.167, 1.181, 0, 5.417, 2.39, 0, 5.75, -1.137, 0, 6.333, 2.069, 0, 6.7, -0.061, 0, 7.033, 0.518, 0, 8.4, -0.702, 0, 8.883, 0.733, 0, 9.233, 0.464, 0, 9.483, 1.078, 0, 9.817, -0.506, 0, 10.4, 2.069, 0, 10.767, -0.061, 0, 11.1, 0.518, 0, 11.917, -1.262, 0, 12.4, 2.362, 0, 13.067, -2.239, 0, 13.5, 0.004]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.036, 0, 0.183, -4.188, 0, 0.567, 8.574, 0, 0.917, -6.397, 0, 1.317, 2.677, 0, 1.917, -2.399, 0, 2.4, 11.165, 0, 2.783, -15.523, 0, 3.117, 8.923, 0, 3.55, -3.972, 0, 4.283, 1.477, 0, 4.717, -5.991, 0, 5.033, 5.418, 0, 5.367, -7.455, 0, 5.65, 15.498, 0, 5.983, -11.526, 0, 6.533, 8.821, 0, 6.883, -5.653, 0, 7.233, 2.298, 0, 7.567, 0.324, 0, 7.667, 0.453, 0, 7.75, 0.265, 0, 8.017, 0.642, 0, 8.683, -3.709, 0, 9.033, 2.481, 0, 9.417, -3.612, 0, 9.717, 7.119, 0, 10.083, -7.373, 0, 10.617, 8.479, 0, 10.967, -5.142, 0, 11.367, 3.392, 0, 12.2, -9.413, 0, 12.65, 9.703, 0, 13.283, -7.638, 1, 13.355, -7.638, 13.428, -5.156, 13.5, -1.195]}, {"Target": "Parameter", "Id": "Param126", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.032, 0, 0.183, 3.757, 0, 0.55, -7.561, 0, 0.817, 10.918, 0, 1.15, -6.335, 0, 1.5, 3.27, 0, 2.317, -5.13, 0, 2.667, 12.263, 0, 2.983, -15.569, 0, 3.333, 9.609, 0, 3.733, -4.68, 0, 4.05, 1.105, 0, 4.283, -0.298, 0, 4.65, 3.251, 0, 4.933, -6.368, 0, 5.283, 7.894, 0, 5.567, -13.78, 0, 5.867, 16.437, 0, 6.2, -9.915, 0, 6.75, 7.593, 0, 7.083, -6.406, 0, 7.417, 2.986, 0, 7.667, -0.632, 0, 8.25, 0.298, 0, 8.4, 0.201, 0, 8.6, 1.501, 0, 8.9, -4.086, 0, 9.317, 3.14, 0, 9.633, -6.355, 0, 9.933, 8.175, 0, 10.283, -5.503, 0, 10.817, 7.18, 0, 11.183, -4.943, 0, 11.483, 2.332, 0, 11.833, 0.166, 0, 12.1, 3.826, 0, 12.45, -7.862, 0, 12.833, 5.04, 0, 13.5, -5.306]}, {"Target": "Parameter", "Id": "Param128", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.55, 0, 0, 0.75, 7.087, 0, 0.983, -10.832, 0, 1.283, 8.057, 0, 1.6, -4.728, 0, 1.917, 1.103, 0, 2.1, 0.381, 0, 2.433, 3.826, 0, 2.767, -10.732, 0, 3.1, 14.555, 0, 3.433, -11.171, 0, 3.783, 5.671, 0, 4.133, -2.112, 0, 4.417, 0.71, 0, 4.75, -2.504, 0, 5.05, 5.429, 0, 5.383, -7.361, 0, 5.683, 12.071, 0, 5.983, -15.684, 0, 6.3, 12.118, 0, 6.817, -5.804, 0, 7.183, 6.225, 0, 7.517, -3.903, 0, 7.817, 1.698, 0, 8.167, -0.507, 0, 8.45, -0.096, 0, 8.7, -1.067, 0, 8.717, -1.054, 0, 8.75, -1.178, 0, 9.05, 2.921, 0, 9.383, -3.322, 0, 9.733, 5.493, 0, 10.05, -7.953, 0, 10.383, 6.515, 0, 10.883, -5.468, 0, 11.267, 6.087, 0, 11.6, -3.595, 0, 11.917, 0.774, 0, 12.217, -3.032, 0, 12.567, 6.983, 0, 12.917, -5.719, 1, 13.111, -5.719, 13.306, -1.216, 13.5, 3.651]}, {"Target": "Parameter", "Id": "Param129", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.75, 0, 0, 0.917, -5.343, 0, 1.133, 9.047, 0, 1.417, -8.482, 0, 1.717, 6.022, 0, 2.033, -2.687, 0, 2.283, 0.082, 0, 2.533, -2.923, 0, 2.867, 9.235, 0, 3.2, -13.672, 0, 3.533, 12.38, 0, 3.883, -7.636, 0, 4.217, 3.428, 2, 4.233, 3.428, 0, 4.533, -1.406, 0, 4.85, 2.261, 0, 5.15, -4.729, 0, 5.483, 6.896, 0, 5.783, -10.818, 0, 6.1, 14.895, 0, 6.417, -13.574, 0, 6.783, 7.058, 0, 7.283, -6.09, 0, 7.617, 4.596, 0, 7.917, -2.87, 0, 8.25, 1.155, 0, 8.55, -0.184, 0, 8.817, 0.938, 0, 9.133, -2.669, 0, 9.483, 3.341, 0, 9.833, -4.633, 0, 10.167, 7.31, 0, 10.5, -7.142, 0, 10.967, 5.045, 0, 11.367, -5.48, 0, 11.717, 4.145, 0, 12.017, -1.624, 0, 12.317, 2.74, 0, 12.667, -6.29, 0, 13.017, 6.114, 0, 13.367, -1.379, 0, 13.5, -1.015]}, {"Target": "Parameter", "Id": "Param16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.267, 0.223, 0, 6.6, -0.46, 0, 10.317, 0.033, 0, 13.05, -0.522, 1, 13.2, -0.522, 13.35, -0.534, 13.5, -0.516]}, {"Target": "Parameter", "Id": "Param19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 4.5, 0.225, 9, 0.45, 13.5, 0.675]}, {"Target": "Parameter", "Id": "Param142", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.812, 2, 0.017, 0.127, 1, 2.561, 0.305, 5.106, 0.482, 7.65, 0.66, 1, 8.1, 0.711, 8.55, 0.761, 9, 0.812, 2, 9.017, 0.127, 1, 10.511, 0.231, 12.006, 0.336, 13.5, 0.44]}, {"Target": "Parameter", "Id": "Param143", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.76, 1, 0.422, 0.82, 0.845, 0.88, 1.267, 0.94, 2, 1.283, 0.2, 1, 3.855, 0.387, 6.428, 0.573, 9, 0.76, 1, 9.422, 0.82, 9.845, 0.88, 10.267, 0.94, 2, 10.283, 0.2, 1, 11.355, 0.278, 12.428, 0.355, 13.5, 0.433]}, {"Target": "Parameter", "Id": "Param18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 0, 2, 0.35, 1, 2, 0.4, 2, 2, 0.45, 3, 2, 0.5, 4, 2, 0.55, 5, 2, 0.6, 0, 2, 0.65, 1, 2, 0.7, 2, 2, 0.75, 3, 2, 0.8, 4, 2, 0.85, 5, 2, 0.9, 0, 2, 0.95, 1, 2, 1, 2, 2, 1.05, 3, 2, 1.1, 4, 2, 1.15, 5, 2, 1.2, 0, 2, 1.25, 1, 2, 1.3, 2, 2, 1.35, 3, 2, 1.4, 4, 2, 1.45, 5, 2, 1.5, 0, 2, 1.55, 1, 2, 1.6, 2, 2, 1.65, 3, 2, 1.7, 4, 2, 1.75, 5, 2, 1.8, 0, 2, 1.85, 1, 2, 1.9, 2, 2, 1.95, 3, 2, 2, 4, 2, 2.05, 5, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 0, 2, 2.45, 1, 2, 2.5, 2, 2, 2.55, 3, 2, 2.6, 4, 2, 2.65, 5, 2, 2.7, 0, 2, 2.75, 1, 2, 2.8, 2, 2, 2.85, 3, 2, 2.9, 4, 2, 2.95, 5, 2, 3, 0, 2, 3.05, 1, 2, 3.1, 2, 2, 3.15, 3, 2, 3.2, 4, 2, 3.25, 5, 2, 3.3, 0, 2, 3.35, 1, 2, 3.4, 2, 2, 3.45, 3, 2, 3.5, 4, 2, 3.55, 5, 2, 3.6, 0, 2, 3.65, 1, 2, 3.7, 2, 2, 3.75, 3, 2, 3.8, 4, 2, 3.85, 5, 2, 3.9, 0, 2, 3.95, 1, 2, 4, 2, 2, 4.05, 3, 2, 4.1, 4, 2, 4.15, 5, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 0, 2, 4.55, 1, 2, 4.6, 2, 2, 4.65, 3, 2, 4.7, 4, 2, 4.75, 5, 2, 4.8, 0, 2, 4.85, 1, 2, 4.9, 2, 2, 4.95, 3, 2, 5, 4, 2, 5.05, 5, 2, 5.1, 0, 2, 5.15, 1, 2, 5.2, 2, 2, 5.25, 3, 2, 5.3, 4, 2, 5.35, 5, 2, 5.4, 0, 2, 5.45, 1, 2, 5.5, 2, 2, 5.55, 3, 2, 5.6, 4, 2, 5.65, 5, 2, 5.7, 0, 2, 5.75, 1, 2, 5.8, 2, 2, 5.85, 3, 2, 5.9, 4, 2, 5.95, 5, 2, 6, 0, 2, 6.05, 1, 2, 6.1, 2, 2, 6.15, 3, 2, 6.2, 4, 2, 6.25, 5, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 0, 2, 6.65, 1, 2, 6.7, 2, 2, 6.75, 3, 2, 6.8, 4, 2, 6.85, 5, 2, 6.9, 0, 2, 6.95, 1, 2, 7, 2, 2, 7.05, 3, 2, 7.1, 4, 2, 7.15, 5, 2, 7.2, 0, 2, 7.25, 1, 2, 7.3, 2, 2, 7.35, 3, 2, 7.4, 4, 2, 7.45, 5, 2, 7.5, 0, 2, 7.55, 1, 2, 7.6, 2, 2, 7.65, 3, 2, 7.7, 4, 2, 7.75, 5, 2, 7.8, 0, 2, 7.85, 1, 2, 7.9, 2, 2, 7.95, 3, 2, 8, 4, 2, 8.05, 5, 2, 8.1, 0, 2, 8.15, 1, 2, 8.2, 2, 2, 8.25, 3, 2, 8.3, 4, 2, 8.35, 5, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 0, 2, 8.75, 1, 2, 8.8, 2, 2, 8.85, 3, 2, 8.9, 4, 2, 8.95, 5, 2, 9, 0, 2, 9.05, 1, 2, 9.1, 2, 2, 9.15, 3, 2, 9.2, 4, 2, 9.25, 5, 2, 9.3, 0, 2, 9.35, 1, 2, 9.4, 2, 2, 9.45, 3, 2, 9.5, 4, 2, 9.55, 5, 2, 9.6, 0, 2, 9.65, 1, 2, 9.7, 2, 2, 9.75, 3, 2, 9.8, 4, 2, 9.85, 5, 2, 9.9, 0, 2, 9.95, 1, 2, 10, 2, 2, 10.05, 3, 2, 10.1, 4, 2, 10.15, 5, 2, 10.2, 0, 2, 10.25, 1, 2, 10.3, 2, 2, 10.35, 3, 2, 10.4, 4, 2, 10.45, 5, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 0, 2, 10.85, 1, 2, 10.9, 2, 2, 10.95, 3, 2, 11, 4, 2, 11.05, 5, 2, 11.1, 0, 2, 11.15, 1, 2, 11.2, 2, 2, 11.25, 3, 2, 11.3, 4, 2, 11.35, 5, 2, 11.4, 0, 2, 11.45, 1, 2, 11.5, 2, 2, 11.55, 3, 2, 11.6, 4, 2, 11.65, 5, 2, 11.7, 0, 2, 11.75, 1, 2, 11.8, 2, 2, 11.85, 3, 2, 11.9, 4, 2, 11.95, 5, 2, 12, 0, 2, 12.05, 1, 2, 12.1, 2, 2, 12.15, 3, 2, 12.2, 4, 2, 12.25, 5, 2, 12.3, 0, 2, 12.35, 1, 2, 12.4, 2, 2, 12.45, 3, 2, 12.5, 4, 2, 12.55, 5, 2, 12.6, 0, 2, 12.65, 1, 2, 12.7, 2, 2, 12.75, 3, 2, 12.8, 4, 2, 12.85, 5, 2, 12.9, 0, 2, 12.95, 1, 2, 13, 2, 2, 13.05, 3, 2, 13.1, 4, 2, 13.15, 5, 2, 13.2, 0, 2, 13.25, 1, 2, 13.3, 2, 2, 13.35, 3, 2, 13.4, 4, 2, 13.45, 5, 2, 13.5, 0]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 6, 2, 0.35, 0, 2, 0.4, 1, 2, 0.45, 2, 2, 0.5, 3, 2, 0.55, 4, 2, 0.6, 5, 2, 0.65, 6, 2, 0.7, 0, 2, 0.75, 1, 2, 0.8, 2, 2, 0.85, 3, 2, 0.9, 4, 2, 0.95, 5, 2, 1, 6, 2, 1.05, 0, 2, 1.1, 1, 2, 1.15, 2, 2, 1.2, 3, 2, 1.25, 4, 2, 1.3, 5, 2, 1.35, 6, 2, 1.4, 0, 2, 1.45, 1, 2, 1.5, 2, 2, 1.55, 3, 2, 1.6, 4, 2, 1.65, 5, 2, 1.7, 6, 2, 1.75, 0, 2, 1.8, 1, 2, 1.85, 2, 2, 1.9, 3, 2, 1.95, 4, 2, 2, 5, 2, 2.05, 6, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 6, 2, 2.45, 0, 2, 2.5, 1, 2, 2.55, 2, 2, 2.6, 3, 2, 2.65, 4, 2, 2.7, 5, 2, 2.75, 6, 2, 2.8, 0, 2, 2.85, 1, 2, 2.9, 2, 2, 2.95, 3, 2, 3, 4, 2, 3.05, 5, 2, 3.1, 6, 2, 3.15, 0, 2, 3.2, 1, 2, 3.25, 2, 2, 3.3, 3, 2, 3.35, 4, 2, 3.4, 5, 2, 3.45, 6, 2, 3.5, 0, 2, 3.55, 1, 2, 3.6, 2, 2, 3.65, 3, 2, 3.7, 4, 2, 3.75, 5, 2, 3.8, 6, 2, 3.85, 0, 2, 3.9, 1, 2, 3.95, 2, 2, 4, 3, 2, 4.05, 4, 2, 4.1, 5, 2, 4.15, 6, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 6, 2, 4.55, 0, 2, 4.6, 1, 2, 4.65, 2, 2, 4.7, 3, 2, 4.75, 4, 2, 4.8, 5, 2, 4.85, 6, 2, 4.9, 0, 2, 4.95, 1, 2, 5, 2, 2, 5.05, 3, 2, 5.1, 4, 2, 5.15, 5, 2, 5.2, 6, 2, 5.25, 0, 2, 5.3, 1, 2, 5.35, 2, 2, 5.4, 3, 2, 5.45, 4, 2, 5.5, 5, 2, 5.55, 6, 2, 5.6, 0, 2, 5.65, 1, 2, 5.7, 2, 2, 5.75, 3, 2, 5.8, 4, 2, 5.85, 5, 2, 5.9, 6, 2, 5.95, 0, 2, 6, 1, 2, 6.05, 2, 2, 6.1, 3, 2, 6.15, 4, 2, 6.2, 5, 2, 6.25, 6, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 6, 2, 6.65, 0, 2, 6.7, 1, 2, 6.75, 2, 2, 6.8, 3, 2, 6.85, 4, 2, 6.9, 5, 2, 6.95, 6, 2, 7, 0, 2, 7.05, 1, 2, 7.1, 2, 2, 7.15, 3, 2, 7.2, 4, 2, 7.25, 5, 2, 7.3, 6, 2, 7.35, 0, 2, 7.4, 1, 2, 7.45, 2, 2, 7.5, 3, 2, 7.55, 4, 2, 7.6, 5, 2, 7.65, 6, 2, 7.7, 0, 2, 7.75, 1, 2, 7.8, 2, 2, 7.85, 3, 2, 7.9, 4, 2, 7.95, 5, 2, 8, 6, 2, 8.05, 0, 2, 8.1, 1, 2, 8.15, 2, 2, 8.2, 3, 2, 8.25, 4, 2, 8.3, 5, 2, 8.35, 6, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 6, 2, 8.75, 0, 2, 8.8, 1, 2, 8.85, 2, 2, 8.9, 3, 2, 8.95, 4, 2, 9, 5, 2, 9.05, 6, 2, 9.1, 0, 2, 9.15, 1, 2, 9.2, 2, 2, 9.25, 3, 2, 9.3, 4, 2, 9.35, 5, 2, 9.4, 6, 2, 9.45, 0, 2, 9.5, 1, 2, 9.55, 2, 2, 9.6, 3, 2, 9.65, 4, 2, 9.7, 5, 2, 9.75, 6, 2, 9.8, 0, 2, 9.85, 1, 2, 9.9, 2, 2, 9.95, 3, 2, 10, 4, 2, 10.05, 5, 2, 10.1, 6, 2, 10.15, 0, 2, 10.2, 1, 2, 10.25, 2, 2, 10.3, 3, 2, 10.35, 4, 2, 10.4, 5, 2, 10.45, 6, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 6, 2, 10.85, 0, 2, 10.9, 1, 2, 10.95, 2, 2, 11, 3, 2, 11.05, 4, 2, 11.1, 5, 2, 11.15, 6, 2, 11.2, 0, 2, 11.25, 1, 2, 11.3, 2, 2, 11.35, 3, 2, 11.4, 4, 2, 11.45, 5, 2, 11.5, 6, 2, 11.55, 0, 2, 11.6, 1, 2, 11.65, 2, 2, 11.7, 3, 2, 11.75, 4, 2, 11.8, 5, 2, 11.85, 6, 2, 11.9, 0, 2, 11.95, 1, 2, 12, 2, 2, 12.05, 3, 2, 12.1, 4, 2, 12.15, 5, 2, 12.2, 6, 2, 12.25, 0, 2, 12.3, 1, 2, 12.35, 2, 2, 12.4, 3, 2, 12.45, 4, 2, 12.5, 5, 2, 12.55, 6, 2, 12.6, 0, 2, 12.65, 1, 2, 12.7, 2, 2, 12.75, 3, 2, 12.8, 4, 2, 12.85, 5, 2, 12.9, 6, 2, 12.95, 0, 2, 13, 1, 2, 13.05, 2, 2, 13.1, 3, 2, 13.15, 4, 2, 13.2, 5, 2, 13.25, 6, 2, 13.3, 0, 2, 13.35, 1, 2, 13.4, 2, 2, 13.45, 3, 2, 13.5, 4]}, {"Target": "Parameter", "Id": "Param51", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 6, 2, 0.35, 0, 2, 0.4, 1, 2, 0.45, 2, 2, 0.5, 3, 2, 0.55, 4, 2, 0.6, 5, 2, 0.65, 6, 2, 0.7, 0, 2, 0.75, 1, 2, 0.8, 2, 2, 0.85, 3, 2, 0.9, 4, 2, 0.95, 5, 2, 1, 6, 2, 1.05, 0, 2, 1.1, 1, 2, 1.15, 2, 2, 1.2, 3, 2, 1.25, 4, 2, 1.3, 5, 2, 1.35, 6, 2, 1.4, 0, 2, 1.45, 1, 2, 1.5, 2, 2, 1.55, 3, 2, 1.6, 4, 2, 1.65, 5, 2, 1.7, 6, 2, 1.75, 0, 2, 1.8, 1, 2, 1.85, 2, 2, 1.9, 3, 2, 1.95, 4, 2, 2, 5, 2, 2.05, 6, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 6, 2, 2.45, 0, 2, 2.5, 1, 2, 2.55, 2, 2, 2.6, 3, 2, 2.65, 4, 2, 2.7, 5, 2, 2.75, 6, 2, 2.8, 0, 2, 2.85, 1, 2, 2.9, 2, 2, 2.95, 3, 2, 3, 4, 2, 3.05, 5, 2, 3.1, 6, 2, 3.15, 0, 2, 3.2, 1, 2, 3.25, 2, 2, 3.3, 3, 2, 3.35, 4, 2, 3.4, 5, 2, 3.45, 6, 2, 3.5, 0, 2, 3.55, 1, 2, 3.6, 2, 2, 3.65, 3, 2, 3.7, 4, 2, 3.75, 5, 2, 3.8, 6, 2, 3.85, 0, 2, 3.9, 1, 2, 3.95, 2, 2, 4, 3, 2, 4.05, 4, 2, 4.1, 5, 2, 4.15, 6, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 6, 2, 4.55, 0, 2, 4.6, 1, 2, 4.65, 2, 2, 4.7, 3, 2, 4.75, 4, 2, 4.8, 5, 2, 4.85, 6, 2, 4.9, 0, 2, 4.95, 1, 2, 5, 2, 2, 5.05, 3, 2, 5.1, 4, 2, 5.15, 5, 2, 5.2, 6, 2, 5.25, 0, 2, 5.3, 1, 2, 5.35, 2, 2, 5.4, 3, 2, 5.45, 4, 2, 5.5, 5, 2, 5.55, 6, 2, 5.6, 0, 2, 5.65, 1, 2, 5.7, 2, 2, 5.75, 3, 2, 5.8, 4, 2, 5.85, 5, 2, 5.9, 6, 2, 5.95, 0, 2, 6, 1, 2, 6.05, 2, 2, 6.1, 3, 2, 6.15, 4, 2, 6.2, 5, 2, 6.25, 6, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 6, 2, 6.65, 0, 2, 6.7, 1, 2, 6.75, 2, 2, 6.8, 3, 2, 6.85, 4, 2, 6.9, 5, 2, 6.95, 6, 2, 7, 0, 2, 7.05, 1, 2, 7.1, 2, 2, 7.15, 3, 2, 7.2, 4, 2, 7.25, 5, 2, 7.3, 6, 2, 7.35, 0, 2, 7.4, 1, 2, 7.45, 2, 2, 7.5, 3, 2, 7.55, 4, 2, 7.6, 5, 2, 7.65, 6, 2, 7.7, 0, 2, 7.75, 1, 2, 7.8, 2, 2, 7.85, 3, 2, 7.9, 4, 2, 7.95, 5, 2, 8, 6, 2, 8.05, 0, 2, 8.1, 1, 2, 8.15, 2, 2, 8.2, 3, 2, 8.25, 4, 2, 8.3, 5, 2, 8.35, 6, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 6, 2, 8.75, 0, 2, 8.8, 1, 2, 8.85, 2, 2, 8.9, 3, 2, 8.95, 4, 2, 9, 5, 2, 9.05, 6, 2, 9.1, 0, 2, 9.15, 1, 2, 9.2, 2, 2, 9.25, 3, 2, 9.3, 4, 2, 9.35, 5, 2, 9.4, 6, 2, 9.45, 0, 2, 9.5, 1, 2, 9.55, 2, 2, 9.6, 3, 2, 9.65, 4, 2, 9.7, 5, 2, 9.75, 6, 2, 9.8, 0, 2, 9.85, 1, 2, 9.9, 2, 2, 9.95, 3, 2, 10, 4, 2, 10.05, 5, 2, 10.1, 6, 2, 10.15, 0, 2, 10.2, 1, 2, 10.25, 2, 2, 10.3, 3, 2, 10.35, 4, 2, 10.4, 5, 2, 10.45, 6, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 6, 2, 10.85, 0, 2, 10.9, 1, 2, 10.95, 2, 2, 11, 3, 2, 11.05, 4, 2, 11.1, 5, 2, 11.15, 6, 2, 11.2, 0, 2, 11.25, 1, 2, 11.3, 2, 2, 11.35, 3, 2, 11.4, 4, 2, 11.45, 5, 2, 11.5, 6, 2, 11.55, 0, 2, 11.6, 1, 2, 11.65, 2, 2, 11.7, 3, 2, 11.75, 4, 2, 11.8, 5, 2, 11.85, 6, 2, 11.9, 0, 2, 11.95, 1, 2, 12, 2, 2, 12.05, 3, 2, 12.1, 4, 2, 12.15, 5, 2, 12.2, 6, 2, 12.25, 0, 2, 12.3, 1, 2, 12.35, 2, 2, 12.4, 3, 2, 12.45, 4, 2, 12.5, 5, 2, 12.55, 6, 2, 12.6, 0, 2, 12.65, 1, 2, 12.7, 2, 2, 12.75, 3, 2, 12.8, 4, 2, 12.85, 5, 2, 12.9, 6, 2, 12.95, 0, 2, 13, 1, 2, 13.05, 2, 2, 13.1, 3, 2, 13.15, 4, 2, 13.2, 5, 2, 13.25, 6, 2, 13.3, 0, 2, 13.35, 1, 2, 13.4, 2, 2, 13.45, 3, 2, 13.5, 4]}, {"Target": "Parameter", "Id": "Param42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 4.5, 0.05, 9, 0.1, 13.5, 0.15]}, {"Target": "Parameter", "Id": "Param45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.4, 1, 4.5, -0.35, 9, -0.3, 13.5, -0.25]}, {"Target": "Parameter", "Id": "Param46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.3, 1, 4.5, 0.35, 9, 0.4, 13.5, 0.45]}, {"Target": "Parameter", "Id": "Param50", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 4.5, 0.9, 9, 1.8, 13.5, 2.7]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2.5, 10, 0, 5.017, -10, 0, 7.5, 10, 0, 10, -10, 0, 12.5, 26.92, 1, 12.833, 26.92, 13.167, 23.736, 13.5, 14.065]}, {"Target": "Parameter", "Id": "Param17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.76, 0, 2.5, 0.66, 0, 5.017, -5.76, 0, 7.5, 0.66, 0, 10, -5.76, 0, 12.5, 21.06, 1, 12.833, 21.06, 13.167, 18.749, 13.5, 11.722]}, {"Target": "Parameter", "Id": "Param49", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.35, 0, 1.25, 0, 0, 3.75, 0.7, 0, 6.267, 0, 0, 8.767, 0.7, 0, 11.283, 0, 1, 12.022, 0, 12.761, 0.323, 13.5, 0.675]}, {"Target": "Parameter", "Id": "Param40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.25, 0, 0.917, 10, 0, 2.917, -10, 0, 4.917, 10, 0, 6.917, -10, 0, 8.917, 10, 0, 10.917, -10, 0, 12.917, 10, 1, 13.111, 10, 13.306, 9.602, 13.5, 5.886]}, {"Target": "Parameter", "Id": "Param41", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2, 10, 0, 4, -10, 0, 6, 10, 0, 8, -10, 0, 10, 10, 0, 12, -10, 1, 12.5, -10, 13, -2.482, 13.5, 6.875]}, {"Target": "Parameter", "Id": "Param32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2, 10, 0, 4, -10, 0, 6, 10, 0, 8, -10, 0, 10, 10, 0, 12, -10, 1, 12.5, -10, 13, -2.482, 13.5, 6.875]}, {"Target": "Parameter", "Id": "Param33", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.886, 0, 0.583, 10, 0, 2.583, -10, 0, 4.583, 10, 0, 6.583, -10, 0, 8.583, 10, 0, 10.583, -10, 0, 12.583, 10, 1, 12.889, 10, 13.194, 7.35, 13.5, 1.25]}, {"Target": "Parameter", "Id": "Param34", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -14.451, 0, 1.333, 30, 0, 3.333, -30, 0, 5.333, 30, 0, 7.333, -30, 0, 9.333, 30, 0, 11.333, -30, 0, 13.333, 30, 1, 13.389, 30, 13.444, 30, 13.5, 28.822]}, {"Target": "Parameter", "Id": "Param28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -15, 0, 2, 15, 0, 4, -15, 0, 6, 15, 0, 8, -15, 0, 10, 15, 0, 12, -15, 1, 12.5, -15, 13, -3.696, 13.5, 10.312]}, {"Target": "Parameter", "Id": "Param29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.886, 0, 0.583, 10, 0, 2.583, -10, 0, 4.583, 10, 0, 6.583, -10, 0, 8.583, 10, 0, 10.583, -10, 0, 12.583, 10, 1, 12.889, 10, 13.194, 7.35, 13.5, 1.25]}, {"Target": "Parameter", "Id": "Param35", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -14.451, 0, 1.333, 30, 0, 3.333, -30, 0, 5.333, 30, 0, 7.333, -30, 0, 9.333, 30, 0, 11.333, -30, 0, 13.333, 30, 1, 13.389, 30, 13.444, 30, 13.5, 28.822]}, {"Target": "Parameter", "Id": "Param30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.959, 0, 0.8, 10, 0, 2.8, -10, 0, 4.8, 10, 0, 6.8, -10, 0, 8.8, 10, 0, 10.8, -10, 0, 12.8, 10, 1, 13.033, 10, 13.267, 8.924, 13.5, 4.366]}, {"Target": "Parameter", "Id": "Param31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 9.44, 0, 1.8, -10, 0, 3.8, 10, 0, 5.8, -10, 0, 7.8, 10, 0, 9.8, -10, 0, 11.8, 10, 1, 12.367, 10, 12.933, 1.124, 13.5, -8.785]}, {"Target": "Parameter", "Id": "Param36", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 13.773, 0, 0.683, 30, 0, 2.683, -30, 0, 4.683, 30, 0, 6.683, -30, 0, 8.683, 30, 0, 10.683, -30, 0, 12.683, 30, 1, 12.955, 30, 13.228, 24.335, 13.5, 8.158]}, {"Target": "Parameter", "Id": "GL1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 4.5, 0.2, 9, 0.401, 13.5, 0.601]}, {"Target": "Parameter", "Id": "GL2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.39, 1, 4.5, 0.59, 9, 0.791, 13.5, 0.991]}, {"Target": "Parameter", "Id": "GL3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.72, 1, 4.5, 0.92, 9, 1.121, 13.5, 1.321]}, {"Target": "Parameter", "Id": "GL4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.21, 1, 4.5, 0.41, 9, 0.611, 13.5, 0.811]}, {"Target": "Parameter", "Id": "GL5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.53, 1, 4.5, 0.73, 9, 0.931, 13.5, 1.131]}, {"Target": "Parameter", "Id": "GL6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.89, 1, 4.5, 1.09, 9, 1.291, 13.5, 1.491]}, {"Target": "Parameter", "Id": "GL7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.77, 1, 4.5, 0.97, 9, 1.171, 13.5, 1.371]}, {"Target": "Parameter", "Id": "GL8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.09, 1, 4.5, 0.29, 9, 0.491, 13.5, 0.691]}, {"Target": "Parameter", "Id": "GL9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.301, 1, 4.5, 0.501, 9, 0.702, 13.5, 0.902]}, {"Target": "Parameter", "Id": "GL10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.45, 1, 4.5, 0.65, 9, 0.851, 13.5, 1.051]}, {"Target": "Parameter", "Id": "GL11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.64, 1, 4.5, 0.84, 9, 1.041, 13.5, 1.241]}, {"Target": "Parameter", "Id": "GL12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.15, 1, 4.5, 0.35, 9, 0.551, 13.5, 0.751]}, {"Target": "Parameter", "Id": "GL13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.585, 1, 4.5, 0.785, 9, 0.986, 13.5, 1.186]}, {"Target": "Parameter", "Id": "GL14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 4.5, 0.2, 9, 0.401, 13.5, 0.601]}, {"Target": "Parameter", "Id": "Param161", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.51, 1, 4.5, 0.76, 9, 1.011, 13.5, 1.261]}, {"Target": "Parameter", "Id": "Param162", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.31, 1, 4.5, 0.56, 9, 0.811, 13.5, 1.061]}, {"Target": "Parameter", "Id": "Param163", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.72, 1, 4.5, 0.97, 9, 1.221, 13.5, 1.471]}, {"Target": "Parameter", "Id": "Param164", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 4.5, 0.25, 9, 0.501, 13.5, 0.751]}, {"Target": "Parameter", "Id": "Param165", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.17, 1, 4.5, 0.42, 9, 0.671, 13.5, 0.921]}, {"Target": "Parameter", "Id": "touch_drag21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param54", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param118", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "man_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "man_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param78", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param79", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "man_<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "man_<PERSON>ze", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param189", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "shangguang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param186", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param96", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param97", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param98", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param111", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "shangguang2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param53", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param99", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param76", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param77", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "MB_xingfengTMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param127", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param103", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param104", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param106", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param105", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param110", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param130", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param107", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param108", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param109", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Mouthfunnel", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param293", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param72", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param74", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param75", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param73", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "MB_fenweiTMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "MB_fenweiliangdu", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 13.5, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiandu", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 13.5, 0.6]}, {"Target": "Parameter", "Id": "MB_fenweiqiu1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 13.5, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 13.5, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 13.5, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 13.5, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 13.5, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 13.5, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiushangxia", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "MB_fenweiqiudaxiao", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param102", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param101", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param167", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param169", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param196", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param56", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param59", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10, 0, 13.5, 10]}, {"Target": "Parameter", "Id": "Param44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param57", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param124", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param80", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param47", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param81", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param95", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param125", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param171", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param166", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param123", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 13.5, 1]}, {"Target": "Parameter", "Id": "Param13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param39", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param61", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 13.5, 1]}, {"Target": "Parameter", "Id": "Param71", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 13.5, 1]}, {"Target": "Parameter", "Id": "Param173", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param174", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param175", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param176", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param190", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param191", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param192", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param193", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param65", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 13.5, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamShoulderStretch3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamShoulderStretch4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamChestZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamWaistZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamButtZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param87", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param94", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param52", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param70", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param208", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param112", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param115", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param113", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param116", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param114", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param117", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "Param23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "touch_drag23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "touch_drag24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "touch_drag25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "touch_drag26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "touch_drag27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "touch_drag28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "touch_drag29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "touch_drag30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "touch_drag31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "touch_drag32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "empty1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "empty2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "empty3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "empty4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "empty5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "empty6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "empty7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "empty8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "empty9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "empty10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "empty11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "empty12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "empty13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "empty14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "empty15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "empty16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "empty17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "empty18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "empty19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "empty20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "empty21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "empty22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "Parameter", "Id": "empty23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 13.5, 0]}, {"Target": "PartOpacity", "Id": "Part7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 13.5, 1]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 13.0, "Value": ""}]}