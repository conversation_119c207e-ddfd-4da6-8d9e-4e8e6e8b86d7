{"Version": 3, "Meta": {"Duration": 11.0, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 215, "TotalSegmentCount": 2672, "TotalPointCount": 3064, "UserDataCount": 2, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.617, 0.093, 0, 2.067, 0.001, 1, 2.65, 0.001, 3.234, 0.086, 3.817, 0.107, 1, 4.5, 0.132, 5.184, 0.13, 5.867, 0.13, 0, 11, 0]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.617, 0.093, 0, 2.067, 0.001, 1, 2.65, 0.001, 3.234, 0.087, 3.817, 0.107, 1, 4.5, 0.132, 5.184, 0.13, 5.867, 0.13, 0, 11, 0]}, {"Target": "Parameter", "Id": "All_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.183, 0.141, 0, 0.617, -0.651, 0, 1.183, 0.182, 0, 1.667, 0.13, 0, 2.15, 0.283, 0, 2.567, -0.088, 0, 2.917, 0.232, 0, 3.4, 0.008, 0, 4.067, 0.228, 0, 4.5, 0.154, 0, 5.417, 0.452, 0, 5.75, -0.138, 0, 6.333, 0.911, 0, 6.7, 0.188, 0, 7.033, 0.285, 0, 7.7, 0.029, 0, 8.017, 0.24, 0, 8.7, -0.195, 0, 9.217, 0.178, 0, 9.75, -0.159, 0, 10.233, 0.448, 0, 10.717, -0.323, 0, 11, 0]}, {"Target": "Parameter", "Id": "All_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.25, 0.295, 0, 0.75, -0.431, 0, 1.317, 0.467, 0, 1.783, 0.393, 0, 2.267, 0.501, 0, 2.717, -0.169, 0, 3.017, 0.154, 0, 3.533, -0.138, 0, 4.217, 0.079, 0, 4.6, -0.065, 0, 5.5, 0.265, 0, 5.85, -0.138, 0, 6.417, 0.321, 0, 6.8, 0.209, 0, 7.133, 0.307, 0, 7.8, 0.029, 0, 8.117, 0.233, 0, 8.8, -0.173, 0, 9.317, 0.145, 0, 9.867, -0.157, 0, 10.317, 0.553, 0, 10.817, -0.189, 0, 11, 0]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.617, 0, 1, 1.684, 0, 2.75, 1.002, 3.817, 1.213, 1, 5.75, 1.596, 7.684, 1.621, 9.617, 1.621, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.867, 20.02, 0, 7.25, 6.261, 0, 9.617, 23, 0, 11, 0]}, {"Target": "Parameter", "Id": "ParamAngleX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.304, 0, 0.317, -7.889, 0, 0.817, 14.216, 0, 1.4, -9.165, 0, 2.783, 16.774, 0, 3.55, -9.567, 0, 4.25, -3.98, 0, 4.6, -6.936, 0, 5.933, 8.566, 0, 6.683, -11.537, 0, 7.1, 0.222, 0, 7.8, -7.644, 1, 7.956, -7.644, 8.111, -8.057, 8.267, -6.947, 1, 8.528, -5.083, 8.789, 11.511, 9.05, 11.511, 0, 9.067, 11.51, 0, 9.083, 11.521, 0, 10.7, -23.083, 0, 11, -1.304]}, {"Target": "Parameter", "Id": "ParamAngleY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.124, 0, 0.233, -3.335, 0, 0.6, 7.514, 0, 1, -6.276, 0, 1.633, 11.723, 0, 2.217, 6.42, 0, 2.633, 15.813, 0, 3.25, -7.727, 0, 3.867, 5.322, 0, 4.55, -0.881, 0, 4.95, 6.449, 0, 5.25, 3.57, 0, 5.667, 8.438, 0, 6.15, 5.045, 0, 6.633, 17.279, 1, 6.761, 17.279, 6.889, 2.073, 7.017, 1.004, 1, 7.261, -1.041, 7.506, -1.109, 7.75, -1.109, 0, 8.133, 5.671, 0, 8.65, 0.2, 0, 9.367, 5.043, 0, 9.85, 3.482, 0, 10.633, 16.79, 0, 11, -0.124]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.543, 0, 0.3, -3.35, 0, 0.7, -1.899, 0, 1.083, -5.918, 1, 1.3, -5.918, 1.516, -3.664, 1.733, -3.342, 1, 1.972, -2.987, 2.211, -3.007, 2.45, -2.728, 1, 2.539, -2.624, 2.628, 6, 2.717, 6, 1, 2.861, 6, 3.006, -4.235, 3.15, -7, 1, 3.272, -9.34, 3.395, -9, 3.517, -9, 0, 3.967, -4.851, 0, 4.433, -6.136, 0, 4.817, -4.097, 0, 5.4, -4.655, 0, 5.783, 0, 0, 6.2, -6, 0, 6.683, -1, 0, 7.633, -6.136, 0, 8.017, -4.097, 0, 9.1, -7.427, 0, 9.817, -1, 0, 10.25, -2, 0, 10.683, 2, 0, 11, 0.543]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -4.357, 0, 0.517, -0.087, 0, 0.9, -10.506, 1, 1.117, -10.506, 1.333, 3.398, 1.55, 4.273, 1, 1.789, 5.237, 2.028, 5.164, 2.267, 5.867, 1, 2.356, 6.129, 2.444, 9, 2.533, 9, 1, 2.678, 9, 2.822, -6.595, 2.967, -9.735, 1, 3.089, -12.392, 3.211, -11.942, 3.333, -11.942, 0, 3.783, -7.739, 0, 4.383, -9.438, 0, 4.8, -8.239, 0, 5.217, -9, 0, 5.6, -3.848, 0, 6.017, -9, 0, 6.567, -4, 0, 7.583, -9.438, 0, 8, -8.239, 1, 8.139, -8.239, 8.278, -8.4, 8.417, -9, 1, 8.6, -9.792, 8.784, -10.378, 8.967, -10.378, 0, 9.633, -4, 0, 10.067, -8, 0, 10.583, -0.775, 0, 11, -4.357]}, {"Target": "Parameter", "Id": "ParamBodyAngleX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -8.417, 0, 0.183, -13, 0, 0.633, 8.898, 1, 0.822, 8.898, 1.011, -2.416, 1.2, -3.294, 1, 1.522, -4.791, 1.845, -4.895, 2.167, -6.258, 1, 2.306, -6.845, 2.444, -14.323, 2.583, -14.323, 0, 2.95, 11.446, 0, 3.533, 0.722, 0, 4.083, 3.784, 1, 4.344, 3.784, 4.606, 3.695, 4.867, 2.496, 1, 5, 1.884, 5.134, -1.72, 5.267, -1.72, 0, 5.733, 6.328, 0, 6.45, -7.606, 1, 6.567, -7.606, 6.683, -0.936, 6.8, 1.013, 1, 6.961, 3.704, 7.122, 3.784, 7.283, 3.784, 1, 7.544, 3.784, 7.806, 3.614, 8.067, 2.496, 1, 8.2, 1.925, 8.334, -1.188, 8.467, -1.72, 1, 8.745, -2.829, 9.022, -3.002, 9.3, -3.002, 0, 9.767, 0, 0, 10.35, -14.891, 0, 10.817, 1.013, 0, 11, -8.417]}, {"Target": "Parameter", "Id": "ParamBodyAngleY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -9.558, 0, 0.417, 8.992, 0, 0.9, -9, 0, 1.483, -0.032, 0, 1.933, -3.696, 0, 2.433, 4.939, 1, 2.583, 4.939, 2.733, -6.69, 2.883, -18.303, 1, 2.983, -26.045, 3.083, -26.947, 3.183, -26.947, 0, 3.7, 1.602, 0, 4.383, -7.125, 0, 4.767, 0.512, 0, 5.083, -4.082, 0, 5.533, 6, 0, 5.75, 2, 1, 5.844, 2, 5.939, 6.159, 6.033, 9, 1, 6.172, 13.178, 6.311, 14, 6.45, 14, 1, 6.589, 14, 6.728, 0.778, 6.867, -2, 1, 7.106, -6.779, 7.344, -7.125, 7.583, -7.125, 0, 7.967, 0.512, 0, 8.283, -4.082, 0, 9.65, 4, 0, 10.05, -1, 0, 10.467, 14, 0, 11, -9.558]}, {"Target": "Parameter", "Id": "ParamWaistZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -13.805, 0, 0.2, -15.74, 0, 0.683, -8, 0, 1.317, -16.846, 0, 2.567, -8.04, 0, 3.117, -15.932, 0, 3.65, -14.28, 0, 5.15, -19.265, 0, 5.8, -8.966, 0, 6.383, -19.825, 0, 7.017, -15.177, 0, 7.333, -15.898, 0, 7.75, -15.064, 0, 8.083, -16.843, 0, 8.817, -7.722, 0, 9.233, -11.183, 0, 9.633, -8.774, 0, 10.267, -21.022, 0, 11, -13.805]}, {"Target": "Parameter", "Id": "ParamShoulderStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.893, 0, 0.283, -0.235, 0, 0.783, -9.529, 0, 1.35, 1.943, 0, 1.8, 1.239, 0, 2.3, 2.897, 1, 2.45, 2.897, 2.6, -5.111, 2.75, -6.639, 1, 2.85, -7.658, 2.95, -7.365, 3.05, -7.365, 0, 3.567, -2.298, 0, 4.25, -4.735, 0, 4.633, -2.881, 0, 4.95, -4.164, 0, 5.4, -3.573, 0, 5.9, -9.971, 0, 6.333, 2.28, 0, 6.917, -6.765, 0, 8.517, -2, 0, 9, -5.026, 0, 9.5, 18.881, 0, 9.9, -8.35, 0, 10.45, 12, 0, 11, -2.893]}, {"Target": "Parameter", "Id": "ParamShoulderStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.371, 0, 0.483, -0.235, 0, 0.983, -9.529, 0, 1.55, 1.943, 0, 2, 1.239, 0, 2.5, 2.897, 1, 2.65, 2.897, 2.8, -5.111, 2.95, -6.639, 1, 3.05, -7.658, 3.15, -7.365, 3.25, -7.365, 0, 3.767, -2.298, 0, 4.45, -4.735, 0, 4.833, -2.881, 0, 5.15, -4.164, 0, 5.6, -3.573, 0, 6.1, -10.317, 0, 6.533, 0.605, 0, 7.017, -7.306, 1, 7.539, -7.306, 8.061, -6.804, 8.583, -3.453, 1, 8.783, -2.169, 8.983, 6.568, 9.183, 6.568, 0, 10.017, -9.006, 0, 10.55, 13, 0, 11, -3.371]}, {"Target": "Parameter", "Id": "ParamButtXA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -6.39, 0, 0.183, -5.227, 0, 0.617, -13, 0, 1.183, -1.63, 0, 1.667, -2.332, 0, 2.15, -0.243, 0, 2.567, -5.314, 0, 2.917, -0.944, 0, 3.4, -4, 0, 4.067, -1, 0, 4.5, -2, 0, 5.417, 2.065, 0, 5.75, -6, 0, 6.333, 8.33, 0, 6.7, -1.539, 0, 7.033, -0.215, 0, 7.7, -3.72, 0, 8.017, -0.833, 0, 8.7, -6.778, 0, 9.217, -1.683, 0, 9.75, -6.285, 0, 10.233, 2, 0, 10.717, -8.518, 0, 11, -6.39]}, {"Target": "Parameter", "Id": "Param43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.915, 0, 0.25, -0.088, 0, 0.75, -10, 0, 1.317, 2.265, 0, 1.783, 1.26, 0, 2.267, 2.726, 0, 2.717, -6.417, 0, 3.017, -2, 0, 3.533, -6, 0, 4.217, -3.029, 0, 4.6, -5, 0, 5.5, -0.499, 0, 5.85, -6, 0, 6.417, 0.272, 0, 6.8, -1.253, 0, 7.133, 0.087, 0, 7.8, -3.72, 0, 8.117, -0.933, 0, 8.8, -6.476, 0, 9.317, -2.127, 0, 9.867, -6.247, 0, 10.317, 3.44, 0, 10.817, -3.148, 0, 11, -1.915]}, {"Target": "Parameter", "Id": "ParamButtZA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.239, 0, 0.45, -7, 0, 0.917, -1.802, 0, 1.467, -2.377, 0, 1.917, -0.482, 0, 2.35, -2, 0, 2.833, 1, 0, 3.133, -1, 0, 4.333, 1.404, 0, 4.683, -0.029, 0, 5.017, 0.42, 0, 5.483, -0.478, 0, 5.983, 0, 0, 6.467, -2, 0, 6.917, -1.449, 0, 7.3, -3.913, 0, 7.883, -0.029, 0, 8.3, -3.676, 0, 8.933, 0.621, 0, 9.333, -5.695, 0, 9.85, -2.782, 0, 10.483, -12, 0, 11, -5.239]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 15, 0, 1.7, 30, 2, 2.883, 30, 2, 3.283, 30, 2, 6.3, 30, 0, 6.85, 0, 2, 9.05, 0, 2, 9.417, 0, 0, 10.467, 30, 2, 10.483, 15, 2, 11, 15]}, {"Target": "Parameter", "Id": "Param58", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.7, 21, 2, 2.883, 21, 0, 3.283, 15, 2, 6.3, 15, 2, 6.85, 15, 2, 9.05, 15, 0, 9.817, 19, 1, 9.922, 19, 10.028, 15.571, 10.133, 10, 1, 10.266, 2.962, 10.4, 0, 10.533, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.7, 30, 2, 2.6, 30, 0, 3.367, 0, 2, 6.3, 0, 2, 6.85, 0, 2, 9.05, 0, 2, 9.417, 0, 2, 9.817, 0, 2, 10.533, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 24.977, 0, 2.6, 25.08, 0, 3.367, 23, 2, 5.817, 23, 0, 6.85, 12.9, 2, 9.05, 12.9, 2, 9.417, 12.9, 1, 9.656, 12.9, 9.894, 21.253, 10.133, 23, 1, 10.422, 25.113, 10.711, 24.977, 11, 24.977]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -9.423, 1, 0.344, -9.423, 0.689, -7.982, 1.033, -1, 1, 1.155, 1.478, 1.278, 13.2, 1.4, 13.2, 2, 2.6, 13.2, 0, 3.367, 6, 2, 5.817, 6, 0, 6.85, 0, 2, 9.05, 0, 2, 9.417, 0, 1, 9.656, 0, 9.894, 0.232, 10.133, -0.78, 1, 10.422, -2.004, 10.711, -9.422, 11, -9.422]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.036, 0, 0.7, 0.76, 0, 0.967, 5, 0, 1.317, 0.665, 1, 1.717, 0.665, 2.117, 0.68, 2.517, 1.015, 1, 2.706, 1.173, 2.894, 4, 3.083, 4, 0, 4.667, 2.145, 0, 5.767, 4, 0, 6.25, 0, 0, 6.8, 1.575, 1, 6.978, 1.575, 7.155, 1.039, 7.333, 0.904, 1, 7.889, 0.481, 8.444, 0.385, 9, 0.385, 2, 9.417, 0.385, 0, 9.767, 2.285, 0, 10.05, 1.48, 0, 10.617, 3.707, 0, 11, 2.037]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.871, 0, 0.717, 8, 0, 1.033, 4, 1, 1.155, 4, 1.278, 9.853, 1.4, 10, 1, 1.744, 10.415, 2.089, 10.464, 2.433, 10.464, 0, 2.667, 9, 0, 3.25, 13.784, 1, 3.872, 13.784, 4.495, 11.175, 5.117, 9.74, 1, 5.367, 9.163, 5.617, 9.34, 5.867, 9.34, 0, 6.35, 10.901, 0, 6.9, 3.48, 0, 7.433, 5, 0, 9.1, 3.48, 2, 9.567, 3.48, 0, 10.133, 8, 0, 10.7, -3.413, 0, 11, 0.864]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.132, 0, 0.8, 6, 0, 1.183, -8, 0, 1.567, 8.44, 1, 1.872, 8.44, 2.178, 8.344, 2.483, 7.319, 1, 2.605, 6.909, 2.728, -3, 2.85, -3, 1, 3.028, -3, 3.205, 12.915, 3.383, 14.784, 1, 3.983, 21.093, 4.583, 22.42, 5.183, 22.42, 2, 5.95, 22.42, 0, 6.433, 25.058, 0, 6.983, -7, 0, 7.517, -2.913, 0, 9.183, -13.26, 2, 9.567, -13.26, 1, 9.678, -13.26, 9.789, 0.529, 9.9, 4, 1, 10.006, 7.298, 10.111, 7, 10.217, 7, 0, 10.783, -7.999, 0, 11, 0.13]}, {"Target": "Parameter", "Id": "Param5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.7, -16, 2, 2.883, -16, 2, 3.283, -16, 2, 6.3, -16, 0, 6.85, -9, 2, 9.05, -9, 2, 9.417, -9, 0, 9.817, -19.6, 1, 9.995, -19.6, 10.172, -19.925, 10.35, -16, 1, 10.567, -11.216, 10.783, 0, 11, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 0.322, 0, 0.645, 0.301, 0.967, 4.131, 1, 1.117, 5.913, 1.267, 30, 1.417, 30, 2, 2.417, 30, 0, 2.917, 0, 2, 5.817, 0, 0, 6.85, -30, 2, 9.05, -30, 2, 9.417, -30, 2, 9.817, -30, 0, 11, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.7, 7, 1, 2.106, 7, 2.511, 4.724, 2.917, 0, 2, 2.933, -30, 0, 3.167, 30, 1, 3.606, 30, 4.044, 28.318, 4.483, 17.855, 1, 4.739, 11.763, 4.994, -1.536, 5.25, -1.536, 0, 5.817, 30, 0, 6.85, -19, 2, 9.05, -19, 2, 9.417, -19, 0, 9.817, -30, 2, 10.517, -30, 0, 11, 0]}, {"Target": "Parameter", "Id": "ParamHandRREDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.867, 5, 2, 2.917, 5, 2, 2.933, 4, 2, 6.567, 4, 2, 6.583, 7, 2, 10.333, 7, 2, 10.367, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "Param55", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.167, 0, 0, 1.467, 25, 2, 2.5, 25, 0, 3.217, 0, 2, 6.317, 0, 0, 7, 18, 2, 9.05, 18, 1, 9.489, 18, 9.928, 17.909, 10.367, 15.99, 1, 10.578, 15.067, 10.789, 0, 11, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 1.35, 10, 2, 2.817, 10, 2, 2.833, 0, 2, 6.65, 0, 2, 6.667, 10, 2, 10.467, 10, 2, 10.483, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.467, 30, 2, 2.5, 30, 0, 3.05, 0, 2, 6.317, 0, 2, 7, 0, 2, 9.05, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.467, -7.28, 2, 2.5, -7.28, 0, 3.05, 0, 2, 6.317, 0, 0, 7, 8, 2, 10.133, 8, 0, 10.533, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.467, -7.36, 2, 2.5, -7.36, 0, 3.05, -5.76, 2, 6.317, -5.76, 0, 7, 6, 2, 10.133, 6, 0, 10.533, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.089, 0, 0.733, -3.107, 0, 1.167, 1.822, 1, 1.267, 1.822, 1.367, 0.205, 1.467, 0.14, 1, 1.767, -0.055, 2.067, -0.125, 2.367, -0.306, 1, 2.434, -0.346, 2.5, -2, 2.567, -2, 0, 2.967, 3, 1, 3.278, 3, 3.589, 0.788, 3.9, -1.155, 1, 4.094, -2.369, 4.289, -2.388, 4.483, -2.388, 0, 5.533, -1.155, 0, 6.167, -2.275, 1, 6.395, -2.275, 6.622, -1.146, 6.85, -0.105, 1, 7.211, 1.546, 7.572, 2, 7.933, 2, 0, 9.583, -0.105, 1, 9.744, -0.105, 9.906, -0.131, 10.067, 1, 1, 10.2, 1.936, 10.334, 5.056, 10.467, 5.056, 0, 11, -0.089]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.769, 0, 0.7, -1.529, 0, 1.133, 6, 1, 1.233, 6, 1.333, -0.758, 1.433, -0.84, 1, 1.722, -1.077, 2.011, -1.155, 2.3, -1.37, 1, 2.372, -1.424, 2.445, -1.98, 2.517, -1.98, 0, 2.933, 5, 1, 3.216, 5, 3.5, 2.347, 3.783, 0.42, 1, 3.961, -0.789, 4.139, -0.724, 4.317, -0.724, 1, 4.772, -0.724, 5.228, -0.665, 5.683, -0.18, 1, 5.894, 0.045, 6.106, 1.08, 6.317, 1.08, 0, 7, -2.22, 0, 8.083, -1.56, 0, 9.15, -2.22, 0, 9.683, -1.82, 0, 10.133, -3.36, 0, 10.633, 5.449, 0, 11, 0.769]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.105, 0, 0.817, 7.26, 0, 1.233, 3, 1, 1.339, 3, 1.444, 24.045, 1.55, 24.36, 1, 1.817, 25.156, 2.083, 25.302, 2.35, 26.091, 1, 2.472, 26.452, 2.595, 28.841, 2.717, 28.841, 1, 2.845, 28.841, 2.972, -5.02, 3.1, -7.503, 1, 3.272, -10.849, 3.445, -10.661, 3.617, -10.661, 0, 4.333, -8.793, 1, 4.828, -8.793, 5.322, -9.017, 5.817, -10, 1, 5.945, -10.254, 6.072, -12.761, 6.2, -12.761, 1, 6.511, -12.761, 6.822, 18.962, 7.133, 20, 1, 7.494, 21.205, 7.856, 21.09, 8.217, 22, 1, 8.495, 22.7, 8.772, 24, 9.05, 24, 0, 9.383, 22, 0, 9.85, 30, 1, 9.978, 30, 10.105, 30, 10.233, 26, 1, 10.411, 19.14, 10.589, -11, 10.767, -11, 0, 11, -3.105]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 1.467, -11, 2, 2.5, -11, 0, 3.05, 0, 2, 6.317, 0, 0, 7, -15.36, 2, 9.05, -15.36, 0, 9.767, -13.44, 2, 10.133, -13.44, 0, 10.533, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.917, 0, 0, 1.467, 30, 2, 2.383, 30, 0, 2.817, 0, 2, 2.833, 0, 0, 3.05, 30, 2, 3.9, 30, 2, 7, 30, 0, 8.167, 8, 0, 9.683, 30, 1, 9.889, 30, 10.094, 4.049, 10.3, 2, 1, 10.533, -0.326, 10.767, 0, 11, 0]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.917, 0, 0, 1.467, -4, 2, 2.383, -4, 0, 2.817, 30, 2, 2.833, -30, 2, 3.05, -30, 0, 3.9, -7, 0, 6.317, -15, 0, 7, 30, 2, 8.167, 30, 2, 9.683, 30, 0, 10.3, -30, 0, 11, 0]}, {"Target": "Parameter", "Id": "ParamHandLREDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.917, 5, 2, 2.817, 5, 2, 2.833, 6, 2, 6.65, 6, 2, 6.667, 4, 2, 10.45, 4, 2, 10.467, 1, 2, 11, 1]}, {"Target": "Parameter", "Id": "Param3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 4.502, 0, 0.783, 1.735, 0, 1.533, 4.614, 0, 1.933, 4.435, 0, 2.317, 6.017, 0, 2.717, 3.379, 0, 3.1, 6.071, 1, 3.278, 6.071, 3.455, 4.713, 3.633, 4.653, 1, 3.889, 4.567, 4.144, 4.575, 4.4, 4.575, 0, 5.417, 7.168, 0, 6.983, 1.041, 0, 7.133, 1.078, 0, 7.833, -0.829, 0, 8.3, 0.772, 0, 8.733, 0.049, 0, 9.417, 4.509, 0, 9.867, 2.929, 0, 10.383, 6.76, 0, 10.833, 4.114, 0, 11, 4.514]}, {"Target": "Parameter", "Id": "Param2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.204, 0, 0.783, 8.233, 0, 1.383, 2.551, 0, 1.767, 2.959, 0, 2.317, -0.328, 0, 2.717, 4.511, 0, 3.1, -0.545, 0, 3.65, 2.295, 0, 4.167, 1.783, 0, 4.4, 2.013, 0, 5.417, -2.554, 0, 7, 9.958, 0, 7.117, 9.898, 0, 7.833, 14.905, 0, 8.317, 11.376, 0, 8.717, 12.314, 0, 9.483, 2.517, 0, 9.867, 5.425, 0, 10.367, -1.708, 0, 10.833, 2.86, 0, 11, 2.193]}, {"Target": "Parameter", "Id": "Param", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 7.526, 0, 0.317, 9.548, 0, 2.183, -3.983, 0, 4.333, 9.953, 0, 4.75, 8.966, 0, 5.7, 11.503, 0, 8.267, -6.209, 0, 9.917, 7.093, 0, 10.417, 0.957, 0, 11, 7.531]}, {"Target": "Parameter", "Id": "Param6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.323, 0, 0.717, -6.115, 0, 2.217, -5.099, 0, 2.667, -5.618, 0, 2.967, -5.197, 0, 3.533, -5.479, 0, 4.233, -5.286, 0, 4.55, -5.358, 0, 5.283, -5.077, 0, 5.817, -5.422, 0, 6.35, -5.007, 0, 6.85, -5.592, 0, 7.033, -5.575, 0, 7.75, -6.142, 0, 8.117, -5.917, 0, 8.7, -6.28, 0, 9.417, -5.411, 0, 9.867, -6.044, 0, 10.367, -5.285, 0, 10.75, -5.455, 0, 11, -5.324]}, {"Target": "Parameter", "Id": "Param48", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -8.246, 0, 0.717, -2.291, 0, 2.217, -9.968, 0, 2.667, -6.065, 0, 2.967, -9.224, 0, 3.533, -7.077, 0, 4.233, -8.53, 0, 4.55, -7.987, 0, 5.283, -10.099, 0, 5.817, -7.501, 0, 6.35, -10.665, 0, 6.85, -6.284, 0, 7.033, -6.411, 0, 7.75, -2.152, 0, 8.117, -3.847, 0, 8.7, -1.114, 0, 9.417, -7.631, 0, 9.867, -2.81, 0, 10.367, -8.543, 0, 10.75, -7.263, 0, 11, -8.237]}, {"Target": "Parameter", "Id": "Param37", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 8.94, 0, 0.567, 13.135, 0, 1.15, 10.648, 0, 2.617, 16.351, 0, 3.883, 9.166, 0, 4.35, 9.548, 0, 5.45, 7.13, 0, 7.883, 22.779, 0, 8.267, 22.123, 0, 8.7, 22.828, 0, 10.067, 7.755, 0, 10.65, 11.823, 0, 11, 8.936]}, {"Target": "Parameter", "Id": "Param38", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 5.717, 0, 0, 7.35, -3.69, 1, 8.067, -3.69, 8.783, -3.716, 9.5, -3.382, 1, 10, -3.149, 10.5, 0, 11, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.9, 2, 0.133, 0.9, 0, 0.483, 0, 0, 0.883, 0.9, 2, 1.817, 0.9, 2, 3.033, 0.9, 0, 3.35, 0, 0, 3.667, 0.9, 2, 4.517, 0.9, 0, 4.717, 0, 0, 4.933, 0.9, 2, 7.267, 0.9, 0, 7.467, 0, 0, 7.683, 0.9, 2, 9.083, 0.9, 0, 9.433, 0, 0, 9.833, 0.9, 2, 11, 0.9]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.317, 0, 0, 0.617, 0.598, 0, 1.117, 0, 2, 1.217, 0, 0, 1.517, 0.598, 0, 1.817, 0, 2, 3.2, 0, 0, 3.45, 0.538, 0, 3.883, 0, 2, 4.617, 0, 0, 4.783, 0.598, 0, 5.067, 0, 2, 7.367, 0, 0, 7.533, 0.598, 0, 7.817, 0, 2, 9.267, 0, 0, 9.567, 0.598, 0, 10.067, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.9, 2, 0.133, 0.9, 0, 0.483, 0, 0, 0.883, 0.9, 2, 1.817, 0.9, 2, 3.033, 0.9, 0, 3.35, 0, 0, 3.667, 0.9, 2, 4.517, 0.9, 0, 4.717, 0, 0, 4.933, 0.9, 2, 7.267, 0.9, 0, 7.467, 0, 0, 7.683, 0.9, 2, 9.083, 0.9, 0, 9.433, 0, 0, 9.833, 0.9, 2, 11, 0.9]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.317, 0, 0, 0.617, 0.598, 0, 1.117, 0, 2, 1.217, 0, 0, 1.517, 0.598, 0, 1.817, 0, 2, 3.2, 0, 0, 3.45, 0.538, 0, 3.883, 0, 2, 4.617, 0, 0, 4.783, 0.598, 0, 5.067, 0, 2, 7.367, 0, 0, 7.533, 0.598, 0, 7.817, 0, 2, 9.267, 0, 0, 9.567, 0.598, 0, 10.067, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.683, 0.07, 2, 1.283, 0.07, 0, 1.817, 0, 2, 3.033, 0, 0, 3.333, 0.07, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, 0.07, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, 0.07, 0, 7.683, 0, 2, 9.083, 0, 0, 9.433, 0.07, 0, 9.833, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.683, 0.364, 2, 1.283, 0.364, 0, 1.817, 0, 2, 3.033, 0, 0, 3.333, 0.364, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, 0.364, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, 0.364, 0, 7.683, 0, 2, 9.083, 0, 0, 9.433, 0.364, 0, 9.833, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "Param4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.683, -0.586, 2, 1.283, -0.586, 0, 1.817, 0, 2, 3.033, 0, 0, 3.333, -0.586, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, -0.586, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, -0.586, 0, 7.683, 0, 2, 9.083, 0, 0, 9.433, -0.586, 0, 9.833, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.683, 0.262, 2, 1.283, 0.262, 0, 1.817, 0, 2, 3.033, 0, 0, 3.333, 0.262, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, 0.262, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, 0.262, 0, 7.683, 0, 2, 9.083, 0, 0, 9.433, 0.262, 0, 9.833, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.683, 0.366, 2, 1.283, 0.366, 0, 1.817, 0, 2, 3.033, 0, 0, 3.333, 0.366, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, 0.366, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, 0.366, 0, 7.683, 0, 2, 9.083, 0, 0, 9.433, 0.366, 0, 9.833, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "Param11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.817, -0.572, 2, 1.283, -0.572, 0, 1.817, 0, 2, 3.033, 0, 0, 3.333, -0.572, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, -0.572, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, -0.572, 0, 7.683, 0, 2, 9.033, 0, 0, 9.233, -0.572, 0, 9.45, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.15, -0.244, 0, 0.333, -0.01, 0, 0.45, -0.319, 2, 0.633, -0.319, 2, 0.817, -0.319, 0, 0.9, 0.656, 0, 0.95, -0.319, 2, 1.133, -0.319, 2, 1.333, -0.319, 2, 1.367, -0.319, 2, 1.533, -0.319, 2, 1.683, -0.319, 0, 1.75, 0.34, 1, 1.817, 0.34, 1.883, 0.257, 1.95, -0.01, 1, 1.989, -0.166, 2.028, -0.319, 2.067, -0.319, 0, 2.283, -0.244, 0, 2.4, -0.319, 0, 2.517, 0.808, 2, 2.617, 0.808, 0, 2.717, 0, 0, 2.833, 1, 0, 2.917, 0.656, 0, 2.967, 1, 0, 3.15, -0.319, 0, 3.35, 0.224, 2, 3.483, 0.224, 0, 3.567, 0, 0, 3.65, 0.34, 0, 4.833, -0.244, 0, 5.317, 0.4, 0, 5.5, -0.319, 2, 5.633, -0.319, 0, 6.583, 0.808, 0, 6.65, -0.319, 0, 6.783, 0, 0, 6.8, -0.319, 1, 6.822, -0.319, 6.845, 0.148, 6.867, 0.34, 1, 6.928, 0.869, 6.989, 1, 7.05, 1, 1, 7.056, 1, 7.061, 0.829, 7.067, 0.8, 1, 7.089, 0.685, 7.111, 0.656, 7.133, 0.656, 0, 7.183, 1, 0, 7.367, -0.319, 0, 7.567, 0.224, 2, 7.7, 0.224, 0, 7.783, 0, 0, 7.867, 0.34, 0, 7.933, 0, 0, 8.983, 0.808, 0, 9.05, -0.319, 0, 9.183, 0, 0, 9.2, -0.319, 1, 9.222, -0.319, 9.245, 0.148, 9.267, 0.34, 1, 9.328, 0.869, 9.389, 1, 9.45, 1, 1, 9.456, 1, 9.461, 0.829, 9.467, 0.8, 1, 9.489, 0.685, 9.511, 0.656, 9.533, 0.656, 0, 9.583, 1, 0, 9.767, -0.319, 0, 9.967, 0.224, 2, 10.1, 0.224, 0, 10.183, 0, 0, 10.233, 0.224, 0, 10.317, 0, 0, 10.4, 0.34, 0, 10.467, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 0.05, 0, 0.1, 0.185, 0.15, 0.5, 1, 0.206, 0.85, 0.261, 1, 0.317, 1, 0, 0.5, 0.102, 0, 0.683, 0.969, 0, 0.9, 0.012, 0, 1.1, 0.663, 0, 1.183, 0.024, 0, 1.333, 0.871, 2, 1.367, 0.871, 0, 1.65, 0, 0, 1.933, 1, 0, 2.1, 0.102, 0, 2.283, 0.5, 1, 2.333, 0.5, 2.383, 0.289, 2.433, 0.102, 1, 2.461, 0, 2.489, 0, 2.517, 0, 2, 2.617, 0, 0, 2.717, 0.5, 0, 2.85, 0.323, 0, 2.967, 0.9, 0, 3.05, 0.016, 0, 3.117, 0.663, 0, 3.2, 0.024, 0, 3.35, 0.871, 2, 3.483, 0.871, 0, 3.567, 0, 2, 3.65, 0, 1, 4.044, 0, 4.439, 0.113, 4.833, 0.5, 1, 4.933, 0.598, 5.033, 0.9, 5.133, 0.9, 1, 5.194, 0.9, 5.256, 0.527, 5.317, 0.4, 1, 5.406, 0.216, 5.494, 0.015, 5.583, 0.012, 1, 5.916, 0.002, 6.25, 0, 6.583, 0, 2, 6.767, 0, 0, 6.833, 0.5, 0, 7.067, 0.323, 0, 7.183, 0.9, 0, 7.267, 0.016, 0, 7.333, 0.663, 0, 7.417, 0.024, 0, 7.567, 0.871, 2, 7.7, 0.871, 0, 7.783, 0, 2, 7.867, 0, 2, 7.933, 0, 2, 8.983, 0, 2, 9.167, 0, 0, 9.233, 0.5, 0, 9.467, 0.323, 0, 9.583, 0.9, 0, 9.667, 0.016, 0, 9.733, 0.663, 0, 9.817, 0.024, 0, 9.967, 0.871, 2, 10.1, 0.871, 0, 10.183, 0, 0, 10.233, 0.871, 0, 10.317, 0, 2, 10.4, 0, 2, 10.467, 0, 2, 11, 0]}, {"Target": "Parameter", "Id": "Hair_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.183, 4.369, 0, 0.617, -12.082, 0, 1.183, 6.971, 0, 1.667, 3.911, 0, 2.15, 13.019, 0, 2.567, -9.091, 0, 2.917, 9.963, 0, 3.4, -3.361, 0, 4.067, 9.718, 0, 4.5, 5.358, 0, 5.167, 13.783, 0, 5.717, -2.783, 0, 6.333, 15.917, 0, 6.7, -10.072, 0, 7.033, 13.141, 0, 7.7, -2.141, 0, 8.017, 10.446, 0, 8.7, -15.473, 0, 9.217, 6.74, 0, 9.75, -13.324, 0, 10.233, 5.358, 0, 10.717, -9.98, 0, 11, 0]}, {"Target": "Parameter", "Id": "Hair_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.183, 2.39, 0, 0.617, -6.211, 0, 1.183, 3.751, 0, 1.667, 2.151, 0, 2.15, 6.912, 0, 2.567, -4.647, 0, 2.917, 5.315, 0, 3.4, -1.652, 0, 4.067, 5.187, 0, 4.5, 2.907, 0, 5.167, 7.312, 0, 5.717, -1.349, 0, 6.333, 8.428, 0, 6.7, -5.16, 0, 7.033, 6.976, 0, 7.7, -1.013, 0, 8.017, 5.568, 0, 8.7, -7.984, 0, 9.217, 3.63, 0, 9.75, -6.86, 0, 10.233, 2.907, 0, 10.717, -5.112, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param69", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.183, 2.39, 0, 0.617, -6.211, 0, 1.183, 3.751, 0, 1.667, 2.151, 0, 2.15, 6.912, 0, 2.567, -4.647, 0, 2.917, 5.315, 0, 3.4, -1.652, 0, 4.067, 5.187, 0, 4.5, 2.907, 0, 5.167, 7.312, 0, 5.717, -1.349, 0, 6.333, 8.428, 0, 6.7, -5.16, 0, 7.033, 6.976, 0, 7.7, -1.013, 0, 8.017, 5.568, 0, 8.7, -7.984, 0, 9.217, 3.63, 0, 9.75, -6.86, 0, 10.233, 2.907, 0, 10.717, -5.112, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param153", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.167, -5.551, 0, 0.5, 11.208, 0, 0.917, -12.85, 0, 1.383, 6.521, 0, 1.95, -6.32, 0, 2.433, 16.079, 0, 2.817, -20.413, 0, 3.217, 14.521, 0, 3.683, -9.358, 0, 4.267, 4.785, 0, 4.8, -4.463, 0, 5.483, 8.6, 0, 6.017, -10.303, 0, 6.583, 21.092, 0, 6.95, -25.804, 0, 7.35, 14.405, 0, 7.917, -13.357, 0, 8.35, 14.221, 0, 8.967, -14.008, 0, 9.483, 14.305, 0, 10.017, -14.505, 0, 10.5, 13.348, 0, 10.933, -13.643, 1, 10.955, -13.643, 10.978, -13.092, 11, -12.182]}, {"Target": "Parameter", "Id": "Param154", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.167, 3.645, 0, 0.433, -8.262, 0, 0.783, 10.728, 0, 1.217, -9.841, 0, 1.633, 6.245, 0, 2.317, -7.641, 0, 2.717, 15.99, 0, 3.067, -18.256, 0, 3.483, 13.909, 0, 3.917, -7.75, 0, 4.483, 3.881, 0, 5.033, -2.562, 0, 5.2, -1.864, 0, 5.333, -2.175, 0, 5.8, 5.994, 0, 6.3, -6.051, 0, 6.333, -5.989, 0, 6.483, -8.116, 0, 6.85, 20.883, 0, 7.2, -21.504, 0, 7.617, 12.744, 0, 8.167, -13.045, 0, 8.617, 9.527, 0, 9.267, -9.468, 0, 9.783, 10.106, 0, 10.3, -11.417, 0, 10.8, 11.753, 1, 10.867, 11.753, 10.933, 4.843, 11, -1.438]}, {"Target": "Parameter", "Id": "Param155", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.133, 0, 0, 0.35, 1.868, 0, 0.633, -4.424, 0, 0.983, 8.738, 0, 1.383, -9.942, 0, 1.833, 7.794, 0, 2.4, -7.353, 0, 2.867, 13.958, 0, 3.267, -17.017, 0, 3.683, 15.027, 0, 4.117, -10.284, 0, 4.617, 5.796, 0, 5.15, -3.139, 0, 5.95, 5.197, 0, 6.533, -7.33, 0, 7.017, 17.283, 0, 7.4, -19.968, 0, 7.833, 15.425, 0, 8.317, -13.931, 0, 8.783, 11.644, 0, 9.383, -9.63, 0, 9.917, 10.681, 0, 10.45, -11.78, 0, 10.933, 12.554, 1, 10.955, 12.554, 10.978, 11.937, 11, 10.902]}, {"Target": "Parameter", "Id": "Param156", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.348, 0, 0.35, -0.2, 0, 0.55, -0.057, 0, 0.8, -3.023, 0, 1.15, 7.307, 0, 1.567, -9.798, 0, 2.017, 9.018, 0, 2.517, -8.467, 0, 3.017, 13.011, 0, 3.433, -16.47, 0, 3.867, 16.02, 0, 4.3, -12.543, 0, 4.767, 8.135, 0, 5.267, -4.701, 0, 6.1, 4.393, 0, 6.65, -7.495, 0, 7.15, 15.063, 0, 7.567, -19.161, 0, 8.017, 17.581, 0, 8.483, -15.829, 0, 8.95, 13.791, 0, 9.5, -11.024, 0, 10.067, 11.214, 0, 10.583, -12.37, 1, 10.722, -12.369, 10.861, 5.493, 11, 11.447]}, {"Target": "Parameter", "Id": "Param82", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.167, -5.175, 0, 0.5, 10.449, 0, 0.917, -11.979, 0, 1.383, 6.079, 0, 1.95, -5.891, 0, 2.433, 14.99, 0, 2.817, -19.03, 0, 3.217, 13.537, 0, 3.683, -8.724, 0, 4.267, 4.461, 0, 4.8, -4.161, 0, 5.483, 8.017, 0, 6.017, -9.604, 0, 6.583, 19.663, 0, 6.95, -24.055, 0, 7.35, 13.429, 0, 7.917, -12.451, 0, 8.35, 13.257, 0, 8.967, -13.059, 0, 9.483, 13.335, 0, 10.017, -13.522, 0, 10.5, 12.444, 0, 10.933, -12.718, 1, 10.955, -12.718, 10.978, -12.205, 11, -11.356]}, {"Target": "Parameter", "Id": "Param83", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.167, 3.492, 0, 0.433, -7.916, 0, 0.783, 10.278, 0, 1.217, -9.429, 0, 1.633, 5.983, 0, 2.317, -7.321, 0, 2.717, 15.32, 0, 3.067, -17.49, 0, 3.483, 13.326, 0, 3.917, -7.425, 0, 4.483, 3.718, 0, 5.033, -2.454, 0, 5.2, -1.786, 0, 5.333, -2.084, 0, 5.8, 5.742, 0, 6.3, -5.797, 0, 6.333, -5.738, 0, 6.483, -7.776, 0, 6.85, 20.007, 0, 7.2, -20.602, 0, 7.617, 12.21, 0, 8.167, -12.498, 0, 8.617, 9.127, 0, 9.267, -9.071, 0, 9.783, 9.682, 0, 10.3, -10.938, 0, 10.8, 11.26, 1, 10.867, 11.26, 10.933, 4.64, 11, -1.377]}, {"Target": "Parameter", "Id": "Param84", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.133, 0, 0, 0.35, 1.91, 0, 0.633, -4.523, 0, 0.983, 8.932, 0, 1.383, -10.163, 0, 1.833, 7.967, 0, 2.4, -7.516, 0, 2.867, 14.268, 0, 3.267, -17.395, 0, 3.683, 15.361, 0, 4.117, -10.513, 0, 4.617, 5.925, 0, 5.15, -3.208, 0, 5.95, 5.313, 0, 6.533, -7.493, 0, 7.017, 17.667, 0, 7.4, -20.412, 0, 7.833, 15.768, 0, 8.317, -14.24, 0, 8.783, 11.903, 0, 9.383, -9.844, 0, 9.917, 10.918, 0, 10.45, -12.042, 0, 10.933, 12.833, 1, 10.955, 12.833, 10.978, 12.203, 11, 11.144]}, {"Target": "Parameter", "Id": "Param85", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.355, 0, 0.35, -0.203, 0, 0.55, -0.058, 0, 0.8, -3.082, 0, 1.15, 7.45, 0, 1.567, -9.99, 0, 2.017, 9.194, 0, 2.517, -8.633, 0, 3.017, 13.266, 0, 3.433, -16.792, 0, 3.867, 16.333, 0, 4.3, -12.789, 0, 4.767, 8.294, 0, 5.267, -4.793, 0, 6.1, 4.479, 0, 6.65, -7.642, 0, 7.15, 15.358, 0, 7.567, -19.537, 0, 8.017, 17.925, 0, 8.483, -16.139, 0, 8.95, 14.061, 0, 9.5, -11.24, 0, 10.067, 11.434, 0, 10.583, -12.612, 1, 10.722, -12.612, 10.861, 5.601, 11, 11.671]}, {"Target": "Parameter", "Id": "Param66", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.1, 0, 0, 0.267, 6.421, 0, 0.5, -14.024, 0, 0.8, 18.049, 0, 1.15, -14.339, 0, 1.55, 7.895, 0, 1.933, -2.388, 0, 2.083, -1.392, 0, 2.4, -7.607, 0, 2.75, 19.83, 0, 3.1, -27.999, 0, 3.433, 22.428, 0, 3.8, -11.567, 0, 4.133, 2.397, 0, 4.367, -0.448, 0, 4.667, 2.902, 0, 5.033, -2.64, 0, 5.267, -0.929, 0, 5.467, -2.383, 0, 5.833, 6.785, 0, 6.25, -6.198, 0, 6.433, -4.057, 0, 6.583, -6.73, 0, 6.917, 26.441, 0, 7.183, -30, 2, 7.283, -30, 0, 7.55, 25.657, 0, 7.867, -8.046, 0, 8.05, -1.096, 0, 8.267, -10.091, 0, 8.583, 11.094, 0, 8.917, -0.199, 0, 9, -0.006, 0, 9.35, -9.244, 0, 9.75, 10.366, 0, 10.317, -10.177, 0, 10.767, 10.769, 1, 10.845, 10.769, 10.922, 3.396, 11, -3.409]}, {"Target": "Parameter", "Id": "Param67", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.133, -10.522, 0, 0.383, 20.698, 0, 0.7, -19.249, 0, 1.25, 5.307, 0, 1.567, -1.421, 0, 1.667, -1.297, 0, 1.883, -6.036, 0, 2.333, 19.444, 0, 2.7, -29.451, 0, 3, 27.935, 0, 3.317, -9.547, 0, 3.65, -1.913, 0, 3.783, -2.509, 0, 4.167, 5.122, 0, 4.6, -4.101, 0, 5.383, 9.621, 0, 5.833, -9.748, 0, 6.517, 26.678, 0, 6.75, -30, 2, 6.9, -30, 0, 7.067, 30, 2, 7.167, 30, 0, 7.4, -12.16, 0, 7.633, 5.578, 0, 7.883, -18.877, 0, 8.15, 23.086, 0, 8.45, -3.936, 0, 8.617, -0.174, 0, 8.9, -15.209, 0, 9.317, 14.819, 0, 9.9, -13.403, 0, 10.333, 13.951, 0, 10.85, -15.403, 1, 10.9, -15.403, 10.95, -4.475, 11, 4.267]}, {"Target": "Parameter", "Id": "Param68", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.117, 4.588, 0, 0.3, -11.807, 0, 0.55, 16.25, 0, 0.85, -13.51, 0, 1.117, 4.483, 0, 1.3, -0.534, 0, 1.483, 1.097, 0, 1.683, -0.34, 0, 1.817, 1.03, 0, 2.083, -3.965, 0, 2.183, -2.926, 0, 2.233, -3.159, 0, 2.533, 13.196, 0, 2.867, -20.185, 0, 3.15, 20.346, 0, 3.433, -9.853, 0, 3.7, 3.238, 0, 3.983, -3.356, 0, 4.333, 3.152, 0, 4.717, -1.659, 0, 5, -0.151, 0, 5.283, -2.03, 0, 5.567, 5.663, 0, 5.967, -3.867, 0, 6.233, -0.712, 0, 6.45, -6.2, 0, 6.717, 21.776, 0, 6.967, -30, 2, 7.033, -30, 0, 7.267, 30, 2, 7.283, 30, 0, 7.533, -17.684, 0, 7.817, 14.249, 0, 8.05, -19.647, 0, 8.317, 17.498, 0, 8.583, -7.505, 0, 8.817, 6.957, 0, 9.083, -10.376, 0, 9.45, 7.206, 0, 9.733, 0.037, 0, 9.85, 0.85, 0, 10.117, -6.701, 0, 10.5, 6.882, 0, 10.75, 0.041, 0, 10.8, 0.331, 1, 10.867, 0.331, 10.933, -8.364, 11, -10.848]}, {"Target": "Parameter", "Id": "Param62", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.1, 0, 0, 0.267, -6.421, 0, 0.5, 14.024, 0, 0.8, -18.049, 0, 1.15, 14.339, 0, 1.55, -7.895, 0, 1.933, 2.388, 0, 2.083, 1.392, 0, 2.4, 7.607, 0, 2.75, -19.83, 0, 3.1, 27.999, 0, 3.433, -22.428, 0, 3.8, 11.567, 0, 4.133, -2.397, 0, 4.367, 0.448, 0, 4.667, -2.902, 0, 5.033, 2.64, 0, 5.267, 0.929, 0, 5.467, 2.383, 0, 5.833, -6.785, 0, 6.25, 6.198, 0, 6.433, 4.057, 0, 6.583, 6.73, 0, 6.917, -26.441, 0, 7.183, 30, 2, 7.283, 30, 0, 7.55, -25.657, 0, 7.867, 8.046, 0, 8.05, 1.096, 0, 8.267, 10.091, 0, 8.583, -11.094, 0, 8.917, 0.199, 0, 9, 0.006, 0, 9.35, 9.244, 0, 9.75, -10.366, 0, 10.317, 10.177, 0, 10.767, -10.769, 1, 10.845, -10.769, 10.922, -3.396, 11, 3.409]}, {"Target": "Parameter", "Id": "Param63", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.133, 10.522, 0, 0.383, -20.698, 0, 0.7, 19.249, 0, 1.25, -5.307, 0, 1.567, 1.421, 0, 1.667, 1.297, 0, 1.883, 6.036, 0, 2.333, -19.444, 0, 2.7, 29.451, 0, 3, -27.935, 0, 3.317, 9.547, 0, 3.65, 1.913, 0, 3.783, 2.509, 0, 4.167, -5.122, 0, 4.6, 4.101, 0, 5.383, -9.621, 0, 5.833, 9.748, 0, 6.517, -26.678, 0, 6.75, 30, 2, 6.9, 30, 0, 7.067, -30, 2, 7.167, -30, 0, 7.4, 12.16, 0, 7.633, -5.578, 0, 7.883, 18.877, 0, 8.15, -23.086, 0, 8.45, 3.936, 0, 8.617, 0.174, 0, 8.9, 15.209, 0, 9.317, -14.819, 0, 9.9, 13.403, 0, 10.333, -13.951, 0, 10.85, 15.403, 1, 10.9, 15.403, 10.95, 4.475, 11, -4.267]}, {"Target": "Parameter", "Id": "Param64", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.117, -4.588, 0, 0.3, 11.807, 0, 0.55, -16.25, 0, 0.85, 13.51, 0, 1.117, -4.483, 0, 1.3, 0.534, 0, 1.483, -1.097, 0, 1.683, 0.34, 0, 1.817, -1.03, 0, 2.083, 3.965, 0, 2.183, 2.926, 0, 2.233, 3.159, 0, 2.533, -13.196, 0, 2.867, 20.185, 0, 3.15, -20.346, 0, 3.433, 9.853, 0, 3.7, -3.238, 0, 3.983, 3.356, 0, 4.333, -3.152, 0, 4.717, 1.659, 0, 5, 0.151, 0, 5.283, 2.03, 0, 5.567, -5.663, 0, 5.967, 3.867, 0, 6.233, 0.712, 0, 6.45, 6.2, 0, 6.717, -21.776, 0, 6.967, 30, 2, 7.033, 30, 0, 7.267, -30, 2, 7.283, -30, 0, 7.533, 17.684, 0, 7.817, -14.249, 0, 8.05, 19.647, 0, 8.317, -17.498, 0, 8.583, 7.505, 0, 8.817, -6.957, 0, 9.083, 10.376, 0, 9.45, -7.206, 0, 9.733, -0.037, 0, 9.85, -0.85, 0, 10.117, 6.701, 0, 10.5, -6.882, 0, 10.75, -0.041, 0, 10.8, -0.331, 1, 10.867, -0.331, 10.933, 8.364, 11, 10.848]}, {"Target": "Parameter", "Id": "Param86", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.183, -1.045, 0, 0.617, 2.717, 0, 1.183, -1.641, 0, 1.667, -0.941, 0, 2.15, -3.024, 0, 2.567, 2.033, 0, 2.917, -2.325, 0, 3.4, 0.722, 0, 4.067, -2.269, 0, 4.5, -1.272, 0, 5.167, -3.198, 0, 5.717, 0.59, 0, 6.333, -3.687, 0, 6.7, 2.257, 0, 7.033, -3.052, 0, 7.7, 0.443, 0, 8.017, -2.435, 0, 8.7, 3.493, 0, 9.217, -1.588, 0, 9.75, 3.001, 0, 10.233, -1.272, 0, 10.717, 2.236, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param89", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.183, -8.536, 0, 0.5, 12.894, 0, 0.867, -12.895, 0, 1.317, 5.417, 0, 1.933, -5.487, 0, 2.4, 16.419, 0, 2.783, -21.308, 0, 3.133, 14.833, 0, 3.583, -7.628, 0, 4.267, 3.657, 0, 4.75, -3.871, 0, 5.45, 8.581, 0, 5.967, -9.353, 0, 6.567, 21.541, 0, 6.917, -27.98, 0, 7.25, 15.118, 0, 7.917, -11.866, 0, 8.283, 13.195, 0, 8.967, -12.804, 0, 9.45, 12.902, 0, 9.983, -12.933, 0, 10.45, 11.835, 0, 10.9, -13.07, 1, 10.933, -13.07, 10.967, -11.065, 11, -8.391]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.183, 7.658, 0, 0.467, -9.509, 0, 0.733, 14.576, 0, 1.083, -9.856, 0, 1.483, 5.552, 0, 1.783, 0.175, 0, 1.85, 0.249, 0, 2.3, -7.64, 0, 2.667, 17.032, 0, 2.983, -21.645, 0, 3.333, 14.205, 0, 3.733, -6.197, 0, 4.05, 0.084, 0, 4.2, -0.352, 0, 4.55, 2.485, 0, 4.95, -1.786, 0, 5.167, -0.992, 0, 5.35, -2.797, 0, 5.75, 5.789, 0, 6.133, -5.238, 0, 6.333, -2.734, 0, 6.5, -8.33, 0, 6.817, 23.883, 0, 7.117, -28.467, 0, 7.45, 15.32, 0, 7.733, -2.508, 0, 7.883, 1.75, 0, 8.133, -10.965, 0, 8.483, 7.601, 0, 8.75, 2.309, 0, 8.833, 2.444, 0, 9.25, -8.259, 0, 9.683, 7.668, 0, 10.25, -8.739, 0, 10.683, 8.169, 1, 10.789, 8.169, 10.894, -4.781, 11, -10.177]}, {"Target": "Parameter", "Id": "Param88", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 0, 0.6, 9.027, 0, 0.85, -13.936, 0, 1.183, 11.688, 0, 1.567, -6.763, 0, 1.867, 1.057, 0, 2.017, -0.048, 0, 2.4, 5.725, 0, 2.75, -15.332, 0, 3.1, 21.548, 0, 3.433, -16.632, 0, 3.8, 8.286, 0, 4.133, -1.823, 0, 4.383, 0.332, 0, 4.667, -2.126, 0, 5.033, 1.94, 0, 5.267, 0.683, 0, 5.467, 1.746, 0, 5.833, -4.925, 0, 6.25, 5.258, 0, 6.45, 2.489, 0, 6.6, 4.611, 0, 6.917, -19.488, 0, 7.233, 26.654, 0, 7.55, -19.088, 0, 7.867, 6.18, 0, 8.05, 0.734, 0, 8.267, 7.385, 0, 8.583, -7.668, 0, 8.933, -0.034, 0, 8.967, -0.043, 0, 9.35, 6.775, 0, 9.75, -7.606, 0, 10.317, 7.488, 0, 10.767, -7.922, 1, 10.845, -7.922, 10.922, -2.501, 11, 2.503]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.55, 0, 0, 0.7, -6.649, 0, 0.983, 12.468, 0, 1.3, -12.918, 0, 1.65, 8.457, 0, 1.967, -4.221, 0, 2.217, 0.29, 0, 2.5, -4.647, 0, 2.867, 12.571, 0, 3.2, -19.698, 0, 3.533, 18.491, 0, 3.883, -11.069, 0, 4.233, 4.043, 0, 4.517, -1.168, 0, 4.783, 2.005, 0, 5.133, -2.091, 0, 5.4, -0.271, 0, 5.583, -1.016, 0, 5.933, 4.672, 0, 6.333, -5.039, 0, 6.583, -1.62, 0, 6.7, -2.127, 0, 7.017, 15.596, 0, 7.333, -24.472, 0, 7.667, 21.373, 0, 7.983, -10.156, 0, 8.217, -0.253, 0, 8.417, -4.075, 0, 8.717, 7.86, 0, 9.05, -1.988, 0, 9.183, -1.432, 0, 9.467, -4.752, 0, 9.85, 7.331, 0, 10.383, -6.355, 0, 10.85, 7.425, 1, 10.9, 7.425, 10.95, 5.548, 11, 3.045]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.183, 1.045, 0, 0.617, -2.717, 0, 1.183, 1.641, 0, 1.667, 0.941, 0, 2.15, 3.024, 0, 2.567, -2.033, 0, 2.917, 2.325, 0, 3.4, -0.722, 0, 4.067, 2.269, 0, 4.5, 1.272, 0, 5.167, 3.198, 0, 5.717, -0.59, 0, 6.333, 3.687, 0, 6.7, -2.257, 0, 7.033, 3.052, 0, 7.7, -0.443, 0, 8.017, 2.435, 0, 8.7, -3.493, 0, 9.217, 1.588, 0, 9.75, -3.001, 0, 10.233, 1.272, 0, 10.717, -2.236, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.183, -8.536, 0, 0.5, 12.894, 0, 0.867, -12.895, 0, 1.317, 5.417, 0, 1.933, -5.487, 0, 2.4, 16.419, 0, 2.783, -21.308, 0, 3.133, 14.833, 0, 3.583, -7.628, 0, 4.267, 3.657, 0, 4.75, -3.871, 0, 5.45, 8.581, 0, 5.967, -9.353, 0, 6.567, 21.541, 0, 6.917, -27.98, 0, 7.25, 15.118, 0, 7.917, -11.866, 0, 8.283, 13.195, 0, 8.967, -12.804, 0, 9.45, 12.902, 0, 9.983, -12.933, 0, 10.45, 11.835, 0, 10.9, -13.07, 1, 10.933, -13.07, 10.967, -11.065, 11, -8.391]}, {"Target": "Parameter", "Id": "Param126", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.183, 7.658, 0, 0.467, -9.509, 0, 0.733, 14.576, 0, 1.083, -9.856, 0, 1.483, 5.552, 0, 1.783, 0.175, 0, 1.85, 0.249, 0, 2.3, -7.64, 0, 2.667, 17.032, 0, 2.983, -21.645, 0, 3.333, 14.205, 0, 3.733, -6.197, 0, 4.05, 0.084, 0, 4.2, -0.352, 0, 4.55, 2.485, 0, 4.95, -1.786, 0, 5.167, -0.992, 0, 5.35, -2.797, 0, 5.75, 5.789, 0, 6.133, -5.238, 0, 6.333, -2.734, 0, 6.5, -8.33, 0, 6.817, 23.883, 0, 7.117, -28.467, 0, 7.45, 15.32, 0, 7.733, -2.508, 0, 7.883, 1.75, 0, 8.133, -10.965, 0, 8.483, 7.601, 0, 8.75, 2.309, 0, 8.833, 2.444, 0, 9.25, -8.259, 0, 9.683, 7.668, 0, 10.25, -8.739, 0, 10.683, 8.169, 1, 10.789, 8.169, 10.894, -4.781, 11, -10.177]}, {"Target": "Parameter", "Id": "Param128", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.417, 0, 0, 0.6, 9.027, 0, 0.85, -13.936, 0, 1.183, 11.688, 0, 1.567, -6.763, 0, 1.867, 1.057, 0, 2.017, -0.048, 0, 2.4, 5.725, 0, 2.75, -15.332, 0, 3.1, 21.548, 0, 3.433, -16.632, 0, 3.8, 8.286, 0, 4.133, -1.823, 0, 4.383, 0.332, 0, 4.667, -2.126, 0, 5.033, 1.94, 0, 5.267, 0.683, 0, 5.467, 1.746, 0, 5.833, -4.925, 0, 6.25, 5.258, 0, 6.45, 2.489, 0, 6.6, 4.611, 0, 6.917, -19.488, 0, 7.233, 26.654, 0, 7.55, -19.088, 0, 7.867, 6.18, 0, 8.05, 0.734, 0, 8.267, 7.385, 0, 8.583, -7.668, 0, 8.933, -0.034, 0, 8.967, -0.043, 0, 9.35, 6.775, 0, 9.75, -7.606, 0, 10.317, 7.488, 0, 10.767, -7.922, 1, 10.845, -7.922, 10.922, -2.501, 11, 2.503]}, {"Target": "Parameter", "Id": "Param129", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.55, 0, 0, 0.7, -6.649, 0, 0.983, 12.468, 0, 1.3, -12.918, 0, 1.65, 8.457, 0, 1.967, -4.221, 0, 2.217, 0.29, 0, 2.5, -4.647, 0, 2.867, 12.571, 0, 3.2, -19.698, 0, 3.533, 18.491, 0, 3.883, -11.069, 0, 4.233, 4.043, 0, 4.517, -1.168, 0, 4.783, 2.005, 0, 5.133, -2.091, 0, 5.4, -0.271, 0, 5.583, -1.016, 0, 5.933, 4.672, 0, 6.333, -5.039, 0, 6.583, -1.62, 0, 6.7, -2.127, 0, 7.017, 15.596, 0, 7.333, -24.472, 0, 7.667, 21.373, 0, 7.983, -10.156, 0, 8.217, -0.253, 0, 8.417, -4.075, 0, 8.717, 7.86, 0, 9.05, -1.988, 0, 9.183, -1.432, 0, 9.467, -4.752, 0, 9.85, 7.331, 0, 10.383, -6.355, 0, 10.85, 7.425, 1, 10.9, 7.425, 10.95, 5.548, 11, 3.045]}, {"Target": "Parameter", "Id": "Param16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.267, 0.223, 0, 6.6, -0.46, 0, 10.317, 0.033, 1, 10.545, 0.033, 10.772, -0.002, 11, -0.054]}, {"Target": "Parameter", "Id": "Param19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 3.667, 0.183, 7.333, 0.367, 11, 0.55]}, {"Target": "Parameter", "Id": "Param142", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.812, 2, 0.017, 0.127, 1, 2.561, 0.305, 5.106, 0.482, 7.65, 0.66, 1, 8.1, 0.711, 8.55, 0.761, 9, 0.812, 2, 9.017, 0.127, 1, 9.678, 0.173, 10.339, 0.219, 11, 0.265]}, {"Target": "Parameter", "Id": "Param143", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.76, 1, 0.422, 0.82, 0.845, 0.88, 1.267, 0.94, 2, 1.283, 0.2, 1, 3.855, 0.387, 6.428, 0.573, 9, 0.76, 1, 9.422, 0.82, 9.845, 0.88, 10.267, 0.94, 2, 10.283, 0.2, 1, 10.522, 0.217, 10.761, 0.235, 11, 0.252]}, {"Target": "Parameter", "Id": "Param18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 0, 2, 0.35, 1, 2, 0.4, 2, 2, 0.45, 3, 2, 0.5, 4, 2, 0.55, 5, 2, 0.6, 0, 2, 0.65, 1, 2, 0.7, 2, 2, 0.75, 3, 2, 0.8, 4, 2, 0.85, 5, 2, 0.9, 0, 2, 0.95, 1, 2, 1, 2, 2, 1.05, 3, 2, 1.1, 4, 2, 1.15, 5, 2, 1.2, 0, 2, 1.25, 1, 2, 1.3, 2, 2, 1.35, 3, 2, 1.4, 4, 2, 1.45, 5, 2, 1.5, 0, 2, 1.55, 1, 2, 1.6, 2, 2, 1.65, 3, 2, 1.7, 4, 2, 1.75, 5, 2, 1.8, 0, 2, 1.85, 1, 2, 1.9, 2, 2, 1.95, 3, 2, 2, 4, 2, 2.05, 5, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 0, 2, 2.45, 1, 2, 2.5, 2, 2, 2.55, 3, 2, 2.6, 4, 2, 2.65, 5, 2, 2.7, 0, 2, 2.75, 1, 2, 2.8, 2, 2, 2.85, 3, 2, 2.9, 4, 2, 2.95, 5, 2, 3, 0, 2, 3.05, 1, 2, 3.1, 2, 2, 3.15, 3, 2, 3.2, 4, 2, 3.25, 5, 2, 3.3, 0, 2, 3.35, 1, 2, 3.4, 2, 2, 3.45, 3, 2, 3.5, 4, 2, 3.55, 5, 2, 3.6, 0, 2, 3.65, 1, 2, 3.7, 2, 2, 3.75, 3, 2, 3.8, 4, 2, 3.85, 5, 2, 3.9, 0, 2, 3.95, 1, 2, 4, 2, 2, 4.05, 3, 2, 4.1, 4, 2, 4.15, 5, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 0, 2, 4.55, 1, 2, 4.6, 2, 2, 4.65, 3, 2, 4.7, 4, 2, 4.75, 5, 2, 4.8, 0, 2, 4.85, 1, 2, 4.9, 2, 2, 4.95, 3, 2, 5, 4, 2, 5.05, 5, 2, 5.1, 0, 2, 5.15, 1, 2, 5.2, 2, 2, 5.25, 3, 2, 5.3, 4, 2, 5.35, 5, 2, 5.4, 0, 2, 5.45, 1, 2, 5.5, 2, 2, 5.55, 3, 2, 5.6, 4, 2, 5.65, 5, 2, 5.7, 0, 2, 5.75, 1, 2, 5.8, 2, 2, 5.85, 3, 2, 5.9, 4, 2, 5.95, 5, 2, 6, 0, 2, 6.05, 1, 2, 6.1, 2, 2, 6.15, 3, 2, 6.2, 4, 2, 6.25, 5, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 0, 2, 6.65, 1, 2, 6.7, 2, 2, 6.75, 3, 2, 6.8, 4, 2, 6.85, 5, 2, 6.9, 0, 2, 6.95, 1, 2, 7, 2, 2, 7.05, 3, 2, 7.1, 4, 2, 7.15, 5, 2, 7.2, 0, 2, 7.25, 1, 2, 7.3, 2, 2, 7.35, 3, 2, 7.4, 4, 2, 7.45, 5, 2, 7.5, 0, 2, 7.55, 1, 2, 7.6, 2, 2, 7.65, 3, 2, 7.7, 4, 2, 7.75, 5, 2, 7.8, 0, 2, 7.85, 1, 2, 7.9, 2, 2, 7.95, 3, 2, 8, 4, 2, 8.05, 5, 2, 8.1, 0, 2, 8.15, 1, 2, 8.2, 2, 2, 8.25, 3, 2, 8.3, 4, 2, 8.35, 5, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 0, 2, 8.75, 1, 2, 8.8, 2, 2, 8.85, 3, 2, 8.9, 4, 2, 8.95, 5, 2, 9, 0, 2, 9.05, 1, 2, 9.1, 2, 2, 9.15, 3, 2, 9.2, 4, 2, 9.25, 5, 2, 9.3, 0, 2, 9.35, 1, 2, 9.4, 2, 2, 9.45, 3, 2, 9.5, 4, 2, 9.55, 5, 2, 9.6, 0, 2, 9.65, 1, 2, 9.7, 2, 2, 9.75, 3, 2, 9.8, 4, 2, 9.85, 5, 2, 9.9, 0, 2, 9.95, 1, 2, 10, 2, 2, 10.05, 3, 2, 10.1, 4, 2, 10.15, 5, 2, 10.2, 0, 2, 10.25, 1, 2, 10.3, 2, 2, 10.35, 3, 2, 10.4, 4, 2, 10.45, 5, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 0, 2, 10.85, 1, 2, 10.9, 2, 2, 10.95, 3, 2, 11, 4]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 6, 2, 0.35, 0, 2, 0.4, 1, 2, 0.45, 2, 2, 0.5, 3, 2, 0.55, 4, 2, 0.6, 5, 2, 0.65, 6, 2, 0.7, 0, 2, 0.75, 1, 2, 0.8, 2, 2, 0.85, 3, 2, 0.9, 4, 2, 0.95, 5, 2, 1, 6, 2, 1.05, 0, 2, 1.1, 1, 2, 1.15, 2, 2, 1.2, 3, 2, 1.25, 4, 2, 1.3, 5, 2, 1.35, 6, 2, 1.4, 0, 2, 1.45, 1, 2, 1.5, 2, 2, 1.55, 3, 2, 1.6, 4, 2, 1.65, 5, 2, 1.7, 6, 2, 1.75, 0, 2, 1.8, 1, 2, 1.85, 2, 2, 1.9, 3, 2, 1.95, 4, 2, 2, 5, 2, 2.05, 6, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 6, 2, 2.45, 0, 2, 2.5, 1, 2, 2.55, 2, 2, 2.6, 3, 2, 2.65, 4, 2, 2.7, 5, 2, 2.75, 6, 2, 2.8, 0, 2, 2.85, 1, 2, 2.9, 2, 2, 2.95, 3, 2, 3, 4, 2, 3.05, 5, 2, 3.1, 6, 2, 3.15, 0, 2, 3.2, 1, 2, 3.25, 2, 2, 3.3, 3, 2, 3.35, 4, 2, 3.4, 5, 2, 3.45, 6, 2, 3.5, 0, 2, 3.55, 1, 2, 3.6, 2, 2, 3.65, 3, 2, 3.7, 4, 2, 3.75, 5, 2, 3.8, 6, 2, 3.85, 0, 2, 3.9, 1, 2, 3.95, 2, 2, 4, 3, 2, 4.05, 4, 2, 4.1, 5, 2, 4.15, 6, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 6, 2, 4.55, 0, 2, 4.6, 1, 2, 4.65, 2, 2, 4.7, 3, 2, 4.75, 4, 2, 4.8, 5, 2, 4.85, 6, 2, 4.9, 0, 2, 4.95, 1, 2, 5, 2, 2, 5.05, 3, 2, 5.1, 4, 2, 5.15, 5, 2, 5.2, 6, 2, 5.25, 0, 2, 5.3, 1, 2, 5.35, 2, 2, 5.4, 3, 2, 5.45, 4, 2, 5.5, 5, 2, 5.55, 6, 2, 5.6, 0, 2, 5.65, 1, 2, 5.7, 2, 2, 5.75, 3, 2, 5.8, 4, 2, 5.85, 5, 2, 5.9, 6, 2, 5.95, 0, 2, 6, 1, 2, 6.05, 2, 2, 6.1, 3, 2, 6.15, 4, 2, 6.2, 5, 2, 6.25, 6, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 6, 2, 6.65, 0, 2, 6.7, 1, 2, 6.75, 2, 2, 6.8, 3, 2, 6.85, 4, 2, 6.9, 5, 2, 6.95, 6, 2, 7, 0, 2, 7.05, 1, 2, 7.1, 2, 2, 7.15, 3, 2, 7.2, 4, 2, 7.25, 5, 2, 7.3, 6, 2, 7.35, 0, 2, 7.4, 1, 2, 7.45, 2, 2, 7.5, 3, 2, 7.55, 4, 2, 7.6, 5, 2, 7.65, 6, 2, 7.7, 0, 2, 7.75, 1, 2, 7.8, 2, 2, 7.85, 3, 2, 7.9, 4, 2, 7.95, 5, 2, 8, 6, 2, 8.05, 0, 2, 8.1, 1, 2, 8.15, 2, 2, 8.2, 3, 2, 8.25, 4, 2, 8.3, 5, 2, 8.35, 6, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 6, 2, 8.75, 0, 2, 8.8, 1, 2, 8.85, 2, 2, 8.9, 3, 2, 8.95, 4, 2, 9, 5, 2, 9.05, 6, 2, 9.1, 0, 2, 9.15, 1, 2, 9.2, 2, 2, 9.25, 3, 2, 9.3, 4, 2, 9.35, 5, 2, 9.4, 6, 2, 9.45, 0, 2, 9.5, 1, 2, 9.55, 2, 2, 9.6, 3, 2, 9.65, 4, 2, 9.7, 5, 2, 9.75, 6, 2, 9.8, 0, 2, 9.85, 1, 2, 9.9, 2, 2, 9.95, 3, 2, 10, 4, 2, 10.05, 5, 2, 10.1, 6, 2, 10.15, 0, 2, 10.2, 1, 2, 10.25, 2, 2, 10.3, 3, 2, 10.35, 4, 2, 10.4, 5, 2, 10.45, 6, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 6, 2, 10.85, 0, 2, 10.9, 1, 2, 10.95, 2, 2, 11, 3]}, {"Target": "Parameter", "Id": "Param51", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 6, 2, 0.35, 0, 2, 0.4, 1, 2, 0.45, 2, 2, 0.5, 3, 2, 0.55, 4, 2, 0.6, 5, 2, 0.65, 6, 2, 0.7, 0, 2, 0.75, 1, 2, 0.8, 2, 2, 0.85, 3, 2, 0.9, 4, 2, 0.95, 5, 2, 1, 6, 2, 1.05, 0, 2, 1.1, 1, 2, 1.15, 2, 2, 1.2, 3, 2, 1.25, 4, 2, 1.3, 5, 2, 1.35, 6, 2, 1.4, 0, 2, 1.45, 1, 2, 1.5, 2, 2, 1.55, 3, 2, 1.6, 4, 2, 1.65, 5, 2, 1.7, 6, 2, 1.75, 0, 2, 1.8, 1, 2, 1.85, 2, 2, 1.9, 3, 2, 1.95, 4, 2, 2, 5, 2, 2.05, 6, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 6, 2, 2.45, 0, 2, 2.5, 1, 2, 2.55, 2, 2, 2.6, 3, 2, 2.65, 4, 2, 2.7, 5, 2, 2.75, 6, 2, 2.8, 0, 2, 2.85, 1, 2, 2.9, 2, 2, 2.95, 3, 2, 3, 4, 2, 3.05, 5, 2, 3.1, 6, 2, 3.15, 0, 2, 3.2, 1, 2, 3.25, 2, 2, 3.3, 3, 2, 3.35, 4, 2, 3.4, 5, 2, 3.45, 6, 2, 3.5, 0, 2, 3.55, 1, 2, 3.6, 2, 2, 3.65, 3, 2, 3.7, 4, 2, 3.75, 5, 2, 3.8, 6, 2, 3.85, 0, 2, 3.9, 1, 2, 3.95, 2, 2, 4, 3, 2, 4.05, 4, 2, 4.1, 5, 2, 4.15, 6, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 6, 2, 4.55, 0, 2, 4.6, 1, 2, 4.65, 2, 2, 4.7, 3, 2, 4.75, 4, 2, 4.8, 5, 2, 4.85, 6, 2, 4.9, 0, 2, 4.95, 1, 2, 5, 2, 2, 5.05, 3, 2, 5.1, 4, 2, 5.15, 5, 2, 5.2, 6, 2, 5.25, 0, 2, 5.3, 1, 2, 5.35, 2, 2, 5.4, 3, 2, 5.45, 4, 2, 5.5, 5, 2, 5.55, 6, 2, 5.6, 0, 2, 5.65, 1, 2, 5.7, 2, 2, 5.75, 3, 2, 5.8, 4, 2, 5.85, 5, 2, 5.9, 6, 2, 5.95, 0, 2, 6, 1, 2, 6.05, 2, 2, 6.1, 3, 2, 6.15, 4, 2, 6.2, 5, 2, 6.25, 6, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 6, 2, 6.65, 0, 2, 6.7, 1, 2, 6.75, 2, 2, 6.8, 3, 2, 6.85, 4, 2, 6.9, 5, 2, 6.95, 6, 2, 7, 0, 2, 7.05, 1, 2, 7.1, 2, 2, 7.15, 3, 2, 7.2, 4, 2, 7.25, 5, 2, 7.3, 6, 2, 7.35, 0, 2, 7.4, 1, 2, 7.45, 2, 2, 7.5, 3, 2, 7.55, 4, 2, 7.6, 5, 2, 7.65, 6, 2, 7.7, 0, 2, 7.75, 1, 2, 7.8, 2, 2, 7.85, 3, 2, 7.9, 4, 2, 7.95, 5, 2, 8, 6, 2, 8.05, 0, 2, 8.1, 1, 2, 8.15, 2, 2, 8.2, 3, 2, 8.25, 4, 2, 8.3, 5, 2, 8.35, 6, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 6, 2, 8.75, 0, 2, 8.8, 1, 2, 8.85, 2, 2, 8.9, 3, 2, 8.95, 4, 2, 9, 5, 2, 9.05, 6, 2, 9.1, 0, 2, 9.15, 1, 2, 9.2, 2, 2, 9.25, 3, 2, 9.3, 4, 2, 9.35, 5, 2, 9.4, 6, 2, 9.45, 0, 2, 9.5, 1, 2, 9.55, 2, 2, 9.6, 3, 2, 9.65, 4, 2, 9.7, 5, 2, 9.75, 6, 2, 9.8, 0, 2, 9.85, 1, 2, 9.9, 2, 2, 9.95, 3, 2, 10, 4, 2, 10.05, 5, 2, 10.1, 6, 2, 10.15, 0, 2, 10.2, 1, 2, 10.25, 2, 2, 10.3, 3, 2, 10.35, 4, 2, 10.4, 5, 2, 10.45, 6, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 6, 2, 10.85, 0, 2, 10.9, 1, 2, 10.95, 2, 2, 11, 3]}, {"Target": "Parameter", "Id": "Param42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 3.667, 0.041, 7.333, 0.081, 11, 0.122]}, {"Target": "Parameter", "Id": "Param45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.4, 1, 3.667, -0.359, 7.333, -0.319, 11, -0.278]}, {"Target": "Parameter", "Id": "Param46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.3, 1, 3.667, 0.341, 7.333, 0.381, 11, 0.422]}, {"Target": "Parameter", "Id": "Param50", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 3.667, 0.733, 7.333, 1.467, 11, 2.2]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2.5, 10, 0, 5.017, -10, 0, 7.5, 10, 0, 10, -10, 1, 10.333, -10, 10.667, -4.093, 11, 2.996]}, {"Target": "Parameter", "Id": "Param17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.76, 0, 2.5, 0.66, 0, 5.017, -5.76, 0, 7.5, 0.66, 0, 10, -5.76, 1, 10.333, -5.76, 10.667, -1.469, 11, 3.681]}, {"Target": "Parameter", "Id": "Param49", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.35, 0, 1.25, 0, 0, 3.75, 0.7, 0, 6.267, 0, 0, 8.767, 0.7, 1, 9.511, 0.7, 10.256, 0.149, 11, 0.025]}, {"Target": "Parameter", "Id": "Param40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.25, 0, 0.917, 10, 0, 2.917, -10, 0, 4.917, 10, 0, 6.917, -10, 0, 8.917, 10, 0, 10.917, -10, 1, 10.945, -10, 10.972, -9.965, 11, -9.899]}, {"Target": "Parameter", "Id": "Param41", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2, 10, 0, 4, -10, 0, 6, 10, 0, 8, -10, 0, 10, 10, 1, 10.333, 10, 10.667, 5, 11, 0]}, {"Target": "Parameter", "Id": "Param32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2, 10, 0, 4, -10, 0, 6, 10, 0, 8, -10, 0, 10, 10, 1, 10.333, 10, 10.667, 5, 11, 0]}, {"Target": "Parameter", "Id": "Param33", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.886, 0, 0.583, 10, 0, 2.583, -10, 0, 4.583, 10, 0, 6.583, -10, 0, 8.583, 10, 0, 10.583, -10, 1, 10.722, -10, 10.861, -9.132, 11, -7.758]}, {"Target": "Parameter", "Id": "Param34", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -14.451, 0, 1.333, 30, 0, 3.333, -30, 0, 5.333, 30, 0, 7.333, -30, 0, 9.333, 30, 1, 9.889, 30, 10.444, -11.667, 11, -25.556]}, {"Target": "Parameter", "Id": "Param28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -15, 0, 2, 15, 0, 4, -15, 0, 6, 15, 0, 8, -15, 0, 10, 15, 1, 10.333, 15, 10.667, 7.5, 11, 0]}, {"Target": "Parameter", "Id": "Param29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.886, 0, 0.583, 10, 0, 2.583, -10, 0, 4.583, 10, 0, 6.583, -10, 0, 8.583, 10, 0, 10.583, -10, 1, 10.722, -10, 10.861, -9.132, 11, -7.758]}, {"Target": "Parameter", "Id": "Param35", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -14.451, 0, 1.333, 30, 0, 3.333, -30, 0, 5.333, 30, 0, 7.333, -30, 0, 9.333, 30, 1, 9.889, 30, 10.444, -11.667, 11, -25.556]}, {"Target": "Parameter", "Id": "Param30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.959, 0, 0.8, 10, 0, 2.8, -10, 0, 4.8, 10, 0, 6.8, -10, 0, 8.8, 10, 0, 10.8, -10, 1, 10.867, -10, 10.933, -9.8, 11, -9.44]}, {"Target": "Parameter", "Id": "Param31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 9.44, 0, 1.8, -10, 0, 3.8, 10, 0, 5.8, -10, 0, 7.8, 10, 0, 9.8, -10, 1, 10.2, -10, 10.6, -2.8, 11, 2.96]}, {"Target": "Parameter", "Id": "Param36", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 13.773, 0, 0.683, 30, 0, 2.683, -30, 0, 4.683, 30, 0, 6.683, -30, 0, 8.683, 30, 0, 10.683, -30, 1, 10.789, -30, 10.894, -28.496, 11, -25.964]}, {"Target": "Parameter", "Id": "GL1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 3.667, 0.163, 7.333, 0.327, 11, 0.49]}, {"Target": "Parameter", "Id": "GL2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.39, 1, 3.667, 0.553, 7.333, 0.717, 11, 0.88]}, {"Target": "Parameter", "Id": "GL3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.72, 1, 3.667, 0.883, 7.333, 1.047, 11, 1.21]}, {"Target": "Parameter", "Id": "GL4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.21, 1, 3.667, 0.373, 7.333, 0.537, 11, 0.7]}, {"Target": "Parameter", "Id": "GL5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.53, 1, 3.667, 0.693, 7.333, 0.857, 11, 1.02]}, {"Target": "Parameter", "Id": "GL6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.89, 1, 3.667, 1.053, 7.333, 1.217, 11, 1.38]}, {"Target": "Parameter", "Id": "GL7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.77, 1, 3.667, 0.933, 7.333, 1.097, 11, 1.26]}, {"Target": "Parameter", "Id": "GL8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.09, 1, 3.667, 0.253, 7.333, 0.417, 11, 0.58]}, {"Target": "Parameter", "Id": "GL9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.301, 1, 3.667, 0.464, 7.333, 0.628, 11, 0.791]}, {"Target": "Parameter", "Id": "GL10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.45, 1, 3.667, 0.613, 7.333, 0.777, 11, 0.94]}, {"Target": "Parameter", "Id": "GL11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.64, 1, 3.667, 0.803, 7.333, 0.967, 11, 1.13]}, {"Target": "Parameter", "Id": "GL12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.15, 1, 3.667, 0.313, 7.333, 0.477, 11, 0.64]}, {"Target": "Parameter", "Id": "GL13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.585, 1, 3.667, 0.748, 7.333, 0.912, 11, 1.075]}, {"Target": "Parameter", "Id": "GL14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 3.667, 0.163, 7.333, 0.327, 11, 0.49]}, {"Target": "Parameter", "Id": "Param161", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.51, 1, 3.667, 0.714, 7.333, 0.918, 11, 1.122]}, {"Target": "Parameter", "Id": "Param162", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.31, 1, 3.667, 0.514, 7.333, 0.718, 11, 0.922]}, {"Target": "Parameter", "Id": "Param163", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.72, 1, 3.667, 0.924, 7.333, 1.128, 11, 1.332]}, {"Target": "Parameter", "Id": "Param164", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 3.667, 0.204, 7.333, 0.408, 11, 0.612]}, {"Target": "Parameter", "Id": "Param165", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.17, 1, 3.667, 0.374, 7.333, 0.578, 11, 0.782]}, {"Target": "Parameter", "Id": "touch_drag21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param167", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param169", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param196", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param56", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param59", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10, 0, 11, 10]}, {"Target": "Parameter", "Id": "Param44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param57", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param124", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param60", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param47", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param125", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param171", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param166", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 11, 1]}, {"Target": "Parameter", "Id": "Param13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param39", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param173", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param174", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param175", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param176", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param190", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param191", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param192", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param193", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param65", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 11, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "ParamShoulderStretch3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "ParamShoulderStretch4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "ParamChestZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "ParamWaistZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "ParamButtZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param87", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param94", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param70", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param208", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "Param27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "touch_drag23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "touch_drag24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "touch_drag25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "touch_drag26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "touch_drag27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "touch_drag28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "touch_drag29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "touch_drag30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "touch_drag31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "touch_drag32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "Parameter", "Id": "empty23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 11, 0]}, {"Target": "PartOpacity", "Id": "Part7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 11, 1]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 10.5, "Value": ""}]}