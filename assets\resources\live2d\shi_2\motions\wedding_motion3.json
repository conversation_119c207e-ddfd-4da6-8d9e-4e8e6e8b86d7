{"Version": 3, "Meta": {"Duration": 26.5, "Fps": 60.0, "Loop": true, "AreBeziersRestricted": true, "FadeInTime": 0.0, "FadeOutTime": 0.0, "CurveCount": 284, "TotalSegmentCount": 6163, "TotalPointCount": 7313, "UserDataCount": 2, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "All_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.883, 0.052, 1, 1.278, 0.052, 1.672, 0.009, 2.067, 0, 1, 3.134, -0.023, 4.2, -0.032, 5.267, -0.054, 1, 5.556, -0.06, 5.844, -0.156, 6.133, -0.156, 1, 8.589, -0.156, 11.044, -0.115, 13.5, 0, 1, 13.794, 0.014, 14.089, 0.2, 14.383, 0.2, 0, 15.567, 0, 2, 18.767, 0, 0, 19.633, -0.156, 1, 19.966, -0.156, 20.3, -0.046, 20.633, -0.041, 1, 22.589, -0.009, 24.544, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "All_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.883, -0.044, 1, 1.278, -0.044, 1.672, -0.008, 2.067, 0, 1, 3.134, 0.023, 4.2, 0.032, 5.267, 0.054, 1, 5.556, 0.06, 5.844, 0.166, 6.133, 0.166, 1, 8.589, 0.166, 11.044, 0.122, 13.5, 0, 1, 13.794, -0.015, 14.089, -0.145, 14.383, -0.145, 0, 15.567, 0, 2, 18.767, 0, 1, 19.056, 0, 19.344, 0.162, 19.633, 0.166, 1, 19.966, 0.171, 20.3, 0.17, 20.633, 0.17, 0, 24.167, -0.19, 2, 25.25, -0.19, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "All_X2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.45, -0.176, 0, 0.95, 0.301, 0, 2.45, -0.331, 0, 2.917, 0.356, 0, 3.733, -0.216, 0, 4.383, -0.094, 0, 4.733, -0.155, 0, 6.067, 0.193, 0, 6.8, -0.261, 0, 7.133, -0.021, 0, 7.667, -0.205, 0, 11.017, 0.101, 0, 11.817, -0.057, 0, 12.617, 0.121, 0, 13.167, -0.031, 0, 13.5, 0, 0, 13.95, -0.272, 0, 14.45, 0.447, 0, 15.95, -0.506, 0, 16.417, 0.53, 0, 17.233, -0.332, 0, 17.883, -0.149, 0, 18.233, -0.241, 0, 19.567, 0.285, 0, 20.3, -0.401, 0, 20.633, -0.038, 0, 21.167, -0.316, 0, 24.017, 0.145, 0, 24.817, -0.092, 0, 25.617, 0.176, 0, 26.167, -0.053, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "All_Y2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.367, -0.077, 0, 0.733, 0.156, 0, 1.133, -0.141, 0, 1.817, 0.27, 0, 2.267, 0.217, 0, 2.75, 0.34, 0, 3.4, -0.171, 0, 4.017, 0.109, 0, 4.683, -0.025, 0, 5.083, 0.133, 0, 5.383, 0.073, 0, 5.8, 0.193, 0, 6.35, 0.101, 0, 6.767, 0.357, 0, 7.133, 0.008, 0, 7.633, 0.199, 0, 11.017, -0.175, 0, 11.6, 0.123, 0, 12.183, 0.012, 0, 12.983, 0.308, 1, 13.155, 0.308, 13.328, 0.155, 13.5, 0, 1, 13.622, -0.11, 13.745, -0.123, 13.867, -0.123, 0, 14.233, 0.229, 0, 14.633, -0.219, 0, 15.317, 0.401, 0, 15.767, 0.321, 0, 16.25, 0.507, 0, 16.9, -0.265, 0, 17.517, 0.158, 0, 18.183, -0.044, 0, 18.583, 0.194, 0, 18.883, 0.103, 0, 19.3, 0.285, 0, 19.85, 0.146, 0, 20.267, 0.532, 0, 20.633, 0.006, 0, 21.133, 0.294, 0, 24.017, -0.271, 0, 24.6, 0.179, 0, 25.183, 0.011, 0, 25.983, 0.458, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "All_Size", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.883, 0.117, 0, 2.067, 0, 0, 3.333, 2.127, 1, 3.978, 2.127, 4.622, 2.094, 5.267, 1.907, 1, 5.556, 1.823, 5.844, 1.282, 6.133, 1.259, 1, 6.583, 1.223, 7.033, 1.237, 7.483, 1.19, 1, 8.233, 1.112, 8.983, 0.88, 9.733, 0.88, 0, 11.167, 2, 0, 13.5, 0, 0, 14.383, 0.435, 0, 15.567, 0, 0, 16.833, 3.38, 1, 17.478, 3.38, 18.122, 3.327, 18.767, 3.03, 1, 19.056, 2.897, 19.344, 2.077, 19.633, 2, 1, 21.144, 1.595, 22.656, 1.406, 24.167, 0.941, 1, 24.945, 0.701, 25.722, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.883, 5, 0, 2.933, -3.23, 0, 4.183, -2.863, 0, 5.267, -5.303, 1, 5.561, -5.303, 5.856, 7.801, 6.15, 9, 1, 8.183, 17.278, 10.217, 20.034, 12.25, 20.034, 0, 13.5, 0, 0, 14.383, 5, 0, 16.433, -3.23, 0, 17.683, -2.863, 0, 18.767, -5.303, 1, 19.056, -5.303, 19.344, 12.914, 19.633, 16.227, 1, 19.966, 20.05, 20.3, 19.719, 20.633, 19.719, 0, 22.75, 15.877, 0, 24.167, 21.196, 1, 24.528, 21.196, 24.889, 21.575, 25.25, 20.034, 1, 25.667, 18.256, 26.083, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamAngleX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.304, 0, 0.317, -7.924, 0, 0.817, 13.741, 0, 2.267, -16.092, 0, 2.783, 15.778, 0, 3.583, -11.29, 0, 4.167, -6, 0, 4.567, -9.729, 0, 4.883, -6.787, 0, 5.167, -8.84, 0, 5.917, 18.11, 0, 6.683, -12.699, 0, 7.083, -1.81, 0, 7.65, -11.045, 0, 8.283, -6.219, 0, 8.65, -9.554, 0, 8.95, -6.539, 0, 9.233, -8.188, 0, 9.983, 4.75, 0, 10.733, -12.518, 0, 11.383, -0.006, 0, 11.833, -1.37, 0, 12.383, 5.833, 0, 13.083, -13.636, 0, 13.5, -1.304, 0, 13.817, -7.891, 0, 14.317, 14.216, 0, 15.817, -15.09, 0, 16.283, 16.76, 0, 17.083, -9.76, 0, 17.75, -4.125, 0, 18.1, -6.93, 0, 18.367, -4.735, 0, 18.65, -6.907, 0, 19.433, 19.748, 0, 20.183, -11.521, 0, 20.517, -0.544, 0, 21.55, -3.776, 0, 22.867, 4.907, 0, 23.683, -3.079, 0, 24.783, 2.221, 0, 25.3, 0.196, 0, 25.483, 1.384, 0, 26.183, -23.17, 0, 26.5, -1.425]}, {"Target": "Parameter", "Id": "ParamAngleY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.124, 0, 0.233, -3.369, 0, 0.6, 7.281, 0, 1.05, -8.315, 0, 1.767, 8.592, 0, 2.1, 7.382, 0, 2.617, 14.228, 0, 3.217, -10.473, 0, 3.867, 3.774, 0, 4.533, -4.223, 0, 4.933, 3.457, 0, 5.25, -0.554, 0, 5.7, 4.814, 0, 6.167, 0.469, 0, 6.617, 15.487, 0, 7, -1.152, 0, 7.917, 3.847, 0, 8.6, -3.974, 0, 8.967, 3.538, 0, 9.317, 0.272, 0, 9.75, 6.884, 0, 10.233, 2.253, 0, 10.683, 15.304, 0, 11.067, -0.332, 0, 11.783, 3.613, 0, 12.583, -3.737, 0, 13.017, 12.474, 1, 13.178, 12.474, 13.339, 6.324, 13.5, -0.124, 1, 13.578, -3.237, 13.655, -3.335, 13.733, -3.335, 0, 14.1, 7.514, 0, 14.5, -6.278, 0, 15.2, 12.806, 0, 15.633, 10.351, 0, 16.117, 16.072, 0, 16.75, -7.702, 0, 17.383, 5.33, 0, 18.05, -0.867, 0, 18.417, 6.042, 0, 18.733, 2.121, 0, 19.1, 7.744, 0, 19.667, 3.891, 0, 20.117, 17.398, 1, 20.245, 17.398, 20.372, 2.189, 20.5, 1.035, 1, 20.711, -0.872, 20.922, -0.85, 21.133, -0.85, 0, 22.167, 2.503, 0, 23.05, -8.069, 0, 24.783, 7.863, 0, 25.367, 3.724, 0, 26.117, 16.664, 0, 26.5, -0.298]}, {"Target": "Parameter", "Id": "ParamAngleZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.543, 0, 0.3, -3.35, 0, 0.7, -1.899, 0, 1.083, -5.918, 1, 1.3, -5.918, 1.516, -3.664, 1.733, -3.342, 1, 1.972, -2.987, 2.211, -3.007, 2.45, -2.728, 1, 2.539, -2.624, 2.628, 6, 2.717, 6, 1, 2.861, 6, 3.006, -4.235, 3.15, -7, 1, 3.272, -9.34, 3.395, -9, 3.517, -9, 0, 3.967, -4.851, 0, 4.433, -6.136, 0, 4.817, -4.097, 0, 5.417, -4.855, 0, 5.8, 2.8, 0, 6.217, -6.2, 0, 6.7, -1.2, 0, 7.583, -9, 0, 8.033, -4.851, 0, 8.5, -6.136, 0, 8.883, -4.097, 0, 9.483, -4.855, 0, 9.867, 2.8, 0, 10.283, -6.2, 1, 10.444, -6.2, 10.606, -1.274, 10.767, -1.2, 1, 11.172, -1.015, 11.578, -1, 11.983, -1, 0, 12.367, -7.533, 0, 13.033, 6, 1, 13.189, 6, 13.344, 4.327, 13.5, 0.543, 1, 13.6, -1.89, 13.7, -3.35, 13.8, -3.35, 0, 14.2, -1.899, 0, 14.583, -5.918, 1, 14.8, -5.918, 15.016, -3.664, 15.233, -3.342, 1, 15.472, -2.987, 15.711, -3.007, 15.95, -2.728, 1, 16.039, -2.624, 16.128, 6, 16.217, 6, 1, 16.361, 6, 16.506, -4.235, 16.65, -7, 1, 16.772, -9.34, 16.895, -9, 17.017, -9, 0, 17.467, -4.851, 0, 17.933, -6.136, 0, 18.317, -4.097, 0, 18.917, -4.855, 0, 19.3, 2.8, 0, 19.717, -6.2, 1, 19.878, -6.2, 20.039, -2.03, 20.2, -1.2, 1, 20.456, 0.117, 20.711, 0.144, 20.967, 0.144, 0, 22.183, -7.427, 0, 22.917, -3.112, 1, 23.078, -3.112, 23.239, -7.591, 23.4, -9.007, 1, 23.683, -11.498, 23.967, -11.757, 24.25, -11.757, 1, 24.389, -11.757, 24.528, -5.723, 24.667, -3.912, 1, 24.878, -1.16, 25.089, -1, 25.3, -1, 0, 25.733, -2, 0, 26.167, 2, 0, 26.5, 0.543]}, {"Target": "Parameter", "Id": "ParamNeckZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -4.357, 0, 0.517, -0.087, 0, 0.9, -10.506, 1, 1.117, -10.506, 1.333, 3.398, 1.55, 4.273, 1, 1.789, 5.237, 2.028, 5.164, 2.267, 5.867, 1, 2.356, 6.129, 2.444, 9, 2.533, 9, 1, 2.678, 9, 2.822, -6.595, 2.967, -9.735, 1, 3.089, -12.392, 3.211, -11.942, 3.333, -11.942, 0, 3.783, -7.739, 0, 4.383, -9.438, 0, 4.8, -8.239, 0, 5.233, -9.2, 0, 5.617, -1.048, 0, 6.033, -9.2, 0, 6.583, -4.2, 1, 7.005, -4.2, 7.428, -5.37, 7.85, -7.739, 1, 8.05, -8.861, 8.25, -9.438, 8.45, -9.438, 0, 8.867, -8.239, 0, 9.3, -9.2, 0, 9.683, -1.048, 0, 10.1, -9.2, 1, 10.283, -9.2, 10.467, -4.293, 10.65, -4.2, 1, 11.033, -4.005, 11.417, -4, 11.8, -4, 0, 12.167, -10.128, 0, 12.933, 3, 0, 13.5, -4.357, 0, 14.017, -0.087, 0, 14.4, -10.506, 1, 14.617, -10.506, 14.833, 3.398, 15.05, 4.273, 1, 15.289, 5.237, 15.528, 5.164, 15.767, 5.867, 1, 15.856, 6.129, 15.944, 9, 16.033, 9, 1, 16.178, 9, 16.322, -6.595, 16.467, -9.735, 1, 16.589, -12.392, 16.711, -11.942, 16.833, -11.942, 0, 17.283, -7.739, 0, 17.883, -9.438, 0, 18.3, -8.239, 0, 18.733, -9.2, 0, 19.117, -1.048, 0, 19.533, -9.2, 1, 19.716, -9.2, 19.9, -5.45, 20.083, -4.2, 1, 20.344, -2.419, 20.606, -2.339, 20.867, -2.339, 0, 22.05, -10.378, 0, 22.783, -6.063, 1, 22.944, -6.063, 23.106, -10.6, 23.267, -11.958, 1, 23.561, -14.439, 23.856, -14.708, 24.15, -14.708, 1, 24.289, -14.708, 24.428, -8.947, 24.567, -6.863, 1, 24.75, -4.112, 24.934, -4, 25.117, -4, 0, 25.55, -8, 0, 26.067, -0.775, 0, 26.5, -4.357]}, {"Target": "Parameter", "Id": "ParamBodyAngleX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -8.417, 0, 0.183, -13, 0, 0.633, 8.898, 1, 0.822, 8.898, 1.011, -2.416, 1.2, -3.294, 1, 1.522, -4.791, 1.845, -4.877, 2.167, -6.258, 1, 2.306, -6.853, 2.444, -22.203, 2.583, -22.203, 0, 2.95, 28.811, 0, 3.533, 0.722, 0, 4.083, 3.784, 1, 4.344, 3.784, 4.606, 3.695, 4.867, 2.496, 1, 5, 1.884, 5.134, -1.72, 5.267, -1.72, 0, 5.733, 20, 0, 6.45, -7.606, 0, 6.8, 1.013, 0, 7.6, 0.722, 0, 8.15, 3.784, 1, 8.411, 3.784, 8.672, 3.738, 8.933, 2.496, 1, 9.094, 1.73, 9.256, -1.72, 9.417, -1.72, 0, 10, 20, 0, 10.517, -7.606, 0, 10.867, 1.013, 1, 11.222, 1.013, 11.578, 1.064, 11.933, 0, 1, 12.15, -0.648, 12.366, -23.539, 12.583, -23.539, 0, 13.167, 1.013, 1, 13.278, 1.013, 13.389, -2.414, 13.5, -8.417, 1, 13.561, -11.719, 13.622, -13, 13.683, -13, 0, 14.133, 8.898, 1, 14.322, 8.898, 14.511, -2.416, 14.7, -3.294, 1, 15.022, -4.791, 15.345, -4.877, 15.667, -6.258, 1, 15.806, -6.853, 15.944, -22.203, 16.083, -22.203, 0, 16.45, 28.811, 0, 17.033, 0.722, 0, 17.583, 3.784, 1, 17.844, 3.784, 18.106, 3.695, 18.367, 2.496, 1, 18.5, 1.884, 18.634, -1.72, 18.767, -1.72, 0, 19.233, 20, 0, 19.95, -7.606, 0, 20.3, 1.013, 0, 24.783, -3.002, 0, 25.25, 0, 0, 25.833, -14.891, 0, 26.3, 1.013, 0, 26.5, -8.417]}, {"Target": "Parameter", "Id": "ParamBodyAngleY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -9.558, 0, 0.417, 8.992, 0, 0.9, -9, 0, 1.483, -0.032, 0, 1.933, -3.696, 0, 2.433, 4.939, 1, 2.583, 4.939, 2.733, -6.69, 2.883, -18.303, 1, 2.983, -26.045, 3.083, -26.947, 3.183, -26.947, 0, 3.7, 1.602, 0, 4.383, -7.125, 0, 4.767, 0.512, 0, 5.083, -4.082, 0, 5.533, 6, 0, 5.75, 2, 1, 5.844, 2, 5.939, 6.159, 6.033, 9, 1, 6.172, 13.178, 6.311, 14, 6.45, 14, 0, 6.867, -2, 0, 7.767, 1.602, 0, 8.45, -7.125, 0, 8.833, 0.512, 0, 9.15, -4.082, 0, 9.6, 6, 0, 9.817, 2, 1, 9.911, 2, 10.006, 6.159, 10.1, 9, 1, 10.239, 13.178, 10.378, 14, 10.517, 14, 0, 10.933, -2, 0, 11.817, 4, 0, 12.217, -1, 0, 12.817, 14, 0, 13.5, -9.558, 0, 13.917, 8.992, 0, 14.4, -9, 0, 14.983, -0.032, 0, 15.433, -3.696, 0, 15.933, 4.939, 1, 16.083, 4.939, 16.233, -6.69, 16.383, -18.303, 1, 16.483, -26.045, 16.583, -26.947, 16.683, -26.947, 0, 17.2, 1.602, 0, 17.883, -7.125, 0, 18.267, 0.512, 0, 18.583, -4.082, 0, 19.033, 6, 0, 19.25, 2, 1, 19.344, 2, 19.439, 6.159, 19.533, 9, 1, 19.672, 13.178, 19.811, 14, 19.95, 14, 0, 20.367, -2, 0, 22.633, -1.879, 0, 23.15, -9.071, 1, 23.256, -9.071, 23.361, 2.459, 23.467, 3.289, 1, 23.767, 5.647, 24.067, 6, 24.367, 6, 0, 24.783, -8, 0, 25.133, 4, 0, 25.533, -1, 0, 25.95, 14, 0, 26.5, -9.558]}, {"Target": "Parameter", "Id": "ParamWaistZ", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -13.805, 0, 0.2, -15.74, 0, 0.7, -2.638, 0, 1.317, -9.855, 0, 1.517, -9.106, 0, 2.167, -13.939, 0, 2.717, -3.179, 0, 4.1, -13.392, 0, 4.533, -11.806, 0, 4.917, -13.536, 0, 5.783, -1.095, 0, 6.533, -16.857, 0, 7.65, -11.457, 0, 9.133, -14.576, 0, 9.85, -4.549, 0, 10.483, -17.176, 0, 11.867, -10.044, 0, 12.55, -16.629, 0, 13.05, -12.72, 1, 13.2, -12.72, 13.35, -12.866, 13.5, -13.805, 1, 13.567, -14.222, 13.633, -15.74, 13.7, -15.74, 0, 14.183, -2.856, 0, 14.817, -16.65, 0, 15.117, -15.519, 0, 15.65, -17.675, 0, 16.217, -6.257, 0, 16.583, -15.102, 0, 17.1, -14.028, 0, 18.333, -17.441, 0, 18.683, -16.532, 0, 18.833, -17.024, 0, 19.3, -4.965, 0, 19.9, -19.613, 0, 21.183, -11.504, 0, 22.333, -15.251, 0, 23.417, -5.976, 0, 24.767, -14.259, 0, 25.183, -10.531, 0, 25.783, -20.693, 0, 26.5, -13.518]}, {"Target": "Parameter", "Id": "ParamShoulderStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -2.893, 0, 0.283, -0.235, 0, 0.783, -9.529, 0, 1.35, 1.943, 0, 1.8, 1.239, 0, 2.3, 2.897, 1, 2.45, 2.897, 2.6, -5.111, 2.75, -6.639, 1, 2.85, -7.658, 2.95, -7.365, 3.05, -7.365, 0, 3.567, -2.298, 0, 4.25, -4.735, 0, 4.633, -2.881, 0, 4.95, -4.164, 0, 5.4, -3.573, 0, 5.9, -9.971, 0, 6.333, 2.28, 0, 6.917, -6.765, 0, 7.633, -2.298, 0, 8.317, -4.735, 0, 8.7, -2.881, 0, 9.017, -4.164, 0, 9.467, -3.573, 0, 9.967, -9.971, 0, 10.4, 2.28, 0, 10.983, -6.765, 0, 11.733, -6.232, 0, 12.083, -11.507, 0, 12.783, 1.9, 0, 13.5, -2.893, 0, 13.783, -0.235, 0, 14.283, -9.529, 0, 14.85, 1.943, 0, 15.3, 1.239, 0, 15.8, 2.897, 1, 15.95, 2.897, 16.1, -5.111, 16.25, -6.639, 1, 16.35, -7.658, 16.45, -7.365, 16.55, -7.365, 0, 17.067, -2.298, 0, 17.75, -4.735, 0, 18.133, -2.881, 0, 18.45, -4.164, 0, 18.9, -3.573, 0, 19.4, -9.971, 0, 19.833, 2.28, 0, 20.417, -6.765, 0, 22.317, -5.668, 0, 22.867, -8.35, 0, 23.35, -4.623, 0, 24.067, -15.764, 0, 25.05, -6.232, 0, 25.4, -11.507, 0, 25.917, 1.9, 0, 26.5, -2.893]}, {"Target": "Parameter", "Id": "ParamShoulderStretch2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.371, 0, 0.483, -0.235, 0, 0.983, -9.529, 0, 1.55, 1.943, 0, 2, 1.239, 0, 2.5, 2.897, 1, 2.65, 2.897, 2.8, -5.111, 2.95, -6.639, 1, 3.05, -7.658, 3.15, -7.365, 3.25, -7.365, 0, 3.767, -2.298, 0, 4.45, -4.735, 0, 4.833, -2.881, 0, 5.15, -4.164, 0, 5.6, -3.573, 0, 6.1, -10.317, 0, 6.533, 0.605, 0, 7.017, -7.306, 0, 7.833, -2.298, 0, 8.517, -4.735, 0, 8.9, -2.881, 0, 9.217, -4.164, 0, 9.667, -3.573, 0, 10.167, -10.317, 0, 10.6, 0.605, 0, 11.083, -7.306, 0, 11.817, -6.888, 0, 12.2, -12.163, 0, 12.883, 1.704, 0, 13.5, -3.371, 0, 13.983, -0.235, 0, 14.483, -9.529, 0, 15.05, 1.943, 0, 15.5, 1.239, 0, 16, 2.897, 1, 16.15, 2.897, 16.3, -5.111, 16.45, -6.639, 1, 16.55, -7.658, 16.65, -7.365, 16.75, -7.365, 0, 17.267, -2.298, 0, 17.95, -4.735, 0, 18.333, -2.881, 0, 18.65, -4.164, 0, 19.1, -3.573, 0, 19.6, -10.317, 0, 20.033, 0.605, 0, 20.517, -7.306, 0, 22.4, -6.324, 0, 22.933, -9.006, 0, 23.4, -5.279, 0, 24.133, -16.42, 0, 25.133, -6.888, 0, 25.517, -12.163, 0, 26.017, 1.704, 0, 26.5, -3.371]}, {"Target": "Parameter", "Id": "ParamButtXA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -6.39, 0, 0.183, -5.227, 0, 0.617, -13, 0, 1.183, -8.63, 0, 1.667, -9.332, 0, 2.15, -7.243, 0, 2.567, -15.371, 0, 2.917, -7.944, 0, 3.4, -11, 0, 4.067, -8, 0, 4.5, -9, 0, 4.817, -5.725, 0, 5.167, -7.701, 0, 5.417, -4.935, 0, 5.75, -13, 0, 6.333, 1.33, 0, 6.7, -3.539, 0, 7.033, -2.215, 0, 8.4, -9.004, 0, 8.883, -5.725, 0, 9.233, -6.34, 0, 9.483, -4.935, 0, 9.817, -7.571, 0, 10.4, 1.33, 0, 10.767, -3.539, 0, 11.1, -2.215, 0, 11.917, -6.285, 0, 12.4, 2, 0, 13.067, -8.518, 1, 13.211, -8.518, 13.356, -7.911, 13.5, -6.39, 1, 13.561, -5.747, 13.622, -5.227, 13.683, -5.227, 0, 14.117, -13, 0, 14.683, -8.63, 0, 15.167, -9.332, 0, 15.65, -7.243, 0, 16.067, -15.371, 0, 16.417, -7.944, 0, 16.9, -11, 0, 17.567, -8, 0, 18, -9, 0, 18.317, -5.725, 0, 18.667, -7.701, 0, 18.917, -4.935, 0, 19.25, -13, 0, 19.833, 1.33, 0, 20.2, -3.539, 0, 20.533, -2.215, 0, 21.2, -13.778, 0, 22.583, -7.104, 0, 23.233, -13.137, 0, 24.7, -1.683, 0, 25.233, -6.285, 0, 25.717, 2, 0, 26.2, -8.518, 0, 26.5, -6.39]}, {"Target": "Parameter", "Id": "Param43", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -1.915, 0, 0.25, -0.088, 0, 0.75, -10, 0, 1.317, 2.265, 0, 1.783, 1.26, 0, 2.267, 2.726, 0, 2.717, -7, 0, 3.017, -2, 0, 3.533, -6, 0, 4.217, -3.029, 0, 4.6, -5, 0, 4.917, -2.104, 0, 5.25, -6, 0, 5.5, -0.499, 0, 5.85, -6, 0, 6.417, 0.272, 0, 6.8, -1.253, 0, 7.133, 0.087, 0, 7.6, -6, 0, 8.283, -3.029, 0, 8.667, -5, 0, 8.983, -2.104, 0, 9.317, -2.812, 0, 9.567, -0.499, 0, 9.917, -2.63, 0, 10.483, 0.272, 0, 10.867, -1.253, 0, 11.2, 0.087, 0, 12.033, -6.247, 0, 12.667, 3.44, 0, 13.167, -3.148, 1, 13.278, -3.148, 13.389, -2.957, 13.5, -1.915, 1, 13.583, -1.134, 13.667, -0.088, 13.75, -0.088, 0, 14.25, -10, 0, 14.817, 2.265, 0, 15.283, 1.26, 0, 15.767, 2.726, 0, 16.217, -7, 0, 16.517, -2, 0, 17.033, -6, 0, 17.717, -3.029, 0, 18.1, -5, 0, 18.417, -2.104, 0, 18.75, -6, 0, 19, -0.499, 0, 19.35, -6, 0, 19.917, 0.272, 0, 20.3, -1.253, 0, 20.633, 0.087, 0, 21.4, -6.476, 0, 22.783, -0.015, 0, 24.133, -11.047, 0, 24.8, -2.127, 0, 25.35, -6.247, 0, 25.8, 3.44, 0, 26.3, -3.148, 0, 26.5, -1.915]}, {"Target": "Parameter", "Id": "ParamButtZA", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.239, 0, 0.45, -7, 0, 0.917, -1.802, 0, 1.467, -2.377, 0, 1.917, -0.482, 0, 2.35, -2, 0, 2.833, 5, 0, 3.133, -1, 0, 4.333, 1.404, 0, 4.683, -0.029, 0, 5.017, 0.42, 0, 5.317, -5, 0, 5.717, 8.117, 0, 6.317, -10.914, 0, 6.917, -4.449, 0, 7.25, -6.913, 0, 8.4, 1.404, 0, 8.75, -0.029, 0, 9.083, 0.42, 0, 9.4, -1.265, 0, 9.783, 8.117, 1, 9.833, 8.117, 9.883, 8.088, 9.933, 5.143, 1, 10.083, -3.691, 10.233, -10.914, 10.383, -10.914, 0, 10.983, -4.449, 0, 11.317, -6.913, 0, 12.017, -2.782, 0, 12.567, -11.734, 0, 13.5, -5.239, 0, 13.95, -7, 0, 14.417, -1.802, 0, 14.967, -2.377, 0, 15.417, -0.482, 0, 15.85, -2, 0, 16.333, 5, 0, 16.633, -1, 0, 17.833, 1.404, 0, 18.183, -0.029, 0, 18.517, 0.42, 0, 18.817, -5, 0, 19.217, 8.117, 0, 19.817, -10.914, 0, 20.417, -4.449, 0, 20.8, -6.913, 0, 21.433, 0.621, 0, 22.633, -3.464, 0, 23.5, -0.678, 0, 24.1, -7.869, 0, 25.333, -2.782, 0, 25.967, -12, 0, 26.5, -5.239]}, {"Target": "Parameter", "Id": "Param9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 2.783, 0, 0, 3.267, 15, 2, 5.867, 15, 2, 6.333, 15, 2, 12.733, 15, 0, 13.5, 0, 0, 14.333, 15, 2, 14.933, 15, 2, 16.45, 15, 2, 16.467, 0, 2, 19.667, 0, 2, 19.683, 30, 2, 25.983, 30, 2, 26, 15, 2, 26.5, 15]}, {"Target": "Parameter", "Id": "Param58", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.75, 0, 0, 2.783, 21, 0, 3.267, 0, 2, 5.867, 0, 0, 6.333, 15, 2, 12.733, 15, 0, 13.5, 0, 0, 14.333, 15, 0, 14.933, 0, 2, 16.167, 0, 0, 16.75, 7.65, 2, 19.667, 7.65, 0, 20.25, 30, 2, 25.333, 30, 1, 25.439, 30, 25.544, 17.017, 25.65, 10, 1, 25.783, 1.136, 25.917, 0, 26.05, 0, 2, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamForearmRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.75, 0, 0, 2.783, -30, 0, 3.267, 0, 2, 5.867, 0, 0, 6.333, -30, 2, 12.733, -30, 0, 13.5, 0, 2, 14.333, 0, 2, 14.933, 0, 2, 16.167, 0, 0, 16.75, -7, 2, 19.667, -7, 0, 20.25, 0, 2, 25.333, 0, 2, 26.05, 0, 2, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamArmRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 24.977, 2, 2.783, 24.977, 0, 3.417, 0.657, 2, 5.867, 0.657, 0, 6.417, 22.74, 2, 12.5, 22.74, 1, 12.833, 22.74, 13.167, 23.151, 13.5, 24.978, 1, 13.589, 25.465, 13.678, 30, 13.767, 30, 0, 14.167, 0, 0, 14.933, 12.72, 2, 16.167, 12.72, 0, 16.75, 10.9, 2, 19.667, 10.9, 0, 20.25, 23, 2, 25.283, 23, 2, 25.65, 23, 0, 26.5, 24.978]}, {"Target": "Parameter", "Id": "ParamHandRStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -9.423, 2, 2.783, -9.423, 0, 3.417, 6, 2, 5.867, 6, 0, 6.417, 14.94, 2, 12.5, 14.94, 1, 12.694, 14.94, 12.889, -4.619, 13.083, -7.764, 1, 13.222, -10.01, 13.361, -9.422, 13.5, -9.422, 1, 13.722, -9.422, 13.945, 7.772, 14.167, 9, 1, 14.422, 10.412, 14.678, 10.2, 14.933, 10.2, 2, 16.167, 10.2, 1, 16.261, 10.2, 16.356, 1.566, 16.45, -3.48, 1, 16.55, -8.823, 16.65, -9.3, 16.75, -9.3, 2, 19.45, -9.3, 0, 19.867, -13.443, 1, 19.928, -13.443, 19.989, -2.189, 20.05, -1.621, 1, 20.144, -0.743, 20.239, -0.78, 20.333, -0.78, 2, 25.283, -0.78, 2, 25.65, -0.78, 0, 26.5, -9.422]}, {"Target": "Parameter", "Id": "ParamArmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.036, 0, 0.5, 0.006, 0, 1.117, 2.036, 0, 2.55, 0.035, 1, 2.694, 0.035, 2.839, 3.135, 2.983, 11.87, 1, 3.083, 17.917, 3.183, 22.676, 3.283, 22.676, 1, 3.528, 22.676, 3.772, 21.948, 4.017, 21.497, 1, 4.478, 20.647, 4.939, 20.457, 5.4, 20.457, 0, 5.8, 22, 1, 5.978, 22, 6.155, 2.562, 6.333, 2, 1, 6.716, 0.788, 7.1, 0.735, 7.483, 0.735, 0, 8.617, 1.442, 0, 9.783, 0.645, 0, 10.5, 2.011, 0, 11.233, 0.797, 0, 11.917, 2, 0, 12.383, 0, 1, 12.755, 0, 13.128, -0.141, 13.5, 2.038, 1, 13.761, 3.567, 14.022, 20.925, 14.283, 20.925, 1, 14.472, 20.925, 14.661, 20.477, 14.85, 19.355, 1, 15.289, 16.749, 15.728, 14.775, 16.167, 11.287, 1, 16.361, 9.742, 16.556, -3.125, 16.75, -3.145, 1, 17.6, -3.231, 18.45, -3.25, 19.3, -3.25, 0, 20.033, 2.278, 0, 23.233, 1.249, 0, 25.2, 2.285, 0, 25.567, 1, 0, 26.133, 3.707, 0, 26.5, 2.038]}, {"Target": "Parameter", "Id": "ParamForearmRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.871, 0, 0.5, 6.031, 0, 1.117, -1.289, 0, 2.633, 3.84, 0, 3.067, -9, 0, 3.45, -0.762, 1, 3.639, -0.762, 3.828, -1.719, 4.017, -2.127, 1, 4.45, -3.062, 4.884, -3.258, 5.317, -3.258, 0, 5.6, -2.797, 0, 5.883, -5, 1, 6.061, -5, 6.239, 10.555, 6.417, 11, 1, 6.8, 11.96, 7.184, 12, 7.567, 12, 0, 8.717, 10.7, 0, 9.883, 12, 0, 10.583, 10.928, 1, 10.833, 10.928, 11.083, 11.344, 11.333, 11.573, 1, 11.566, 11.787, 11.8, 11.777, 12.033, 12, 1, 12.189, 12.148, 12.344, 13.46, 12.5, 13.46, 0, 13.25, -0.537, 0, 13.5, 0.871, 0, 13.767, -6, 1, 14.178, -6, 14.589, 0.568, 15, 2.075, 1, 15.389, 3.5, 15.778, 3.291, 16.167, 3.291, 2, 16.183, -57.142, 1, 16.272, -57.142, 16.361, -43.161, 16.45, -39.174, 1, 16.55, -34.688, 16.65, -30.34, 16.75, -30.24, 1, 17.533, -29.454, 18.317, -28.917, 19.1, -28.14, 1, 19.2, -28.041, 19.3, -27.041, 19.4, -27.041, 1, 19.5, -27.041, 19.6, -26.545, 19.7, -30.76, 1, 19.817, -35.677, 19.933, -56.455, 20.05, -56.455, 1, 20.2, -56.455, 20.35, -53.653, 20.5, -47.526, 2, 20.517, 12.8, 0, 23.233, 9.7, 1, 23.916, 9.7, 24.6, 9.974, 25.283, 10.9, 1, 25.405, 11.066, 25.528, 13, 25.65, 13, 0, 26.217, -3.413, 0, 26.5, 0.871]}, {"Target": "Parameter", "Id": "ParamHand<PERSON>ngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.132, 0, 0.5, 8.172, 1, 0.706, 8.172, 0.911, -1.096, 1.117, -4.788, 1, 1.528, -12.171, 1.939, -13.469, 2.35, -13.469, 0, 2.717, -7, 0, 3.15, -15, 0, 3.633, -3.333, 1, 3.833, -3.333, 4.033, -4.627, 4.233, -5.079, 1, 4.616, -5.946, 5, -6.065, 5.383, -6.065, 0, 5.817, 6, 0, 6.183, -20.315, 0, 6.717, -13.015, 0, 7.6, -14.95, 0, 8.733, -11.319, 0, 9.833, -15.102, 0, 10.983, -12.115, 0, 12.15, -15.6, 1, 12.306, -15.6, 12.461, -15.355, 12.617, -12, 1, 12.911, -5.649, 13.206, 0.131, 13.5, 0.131, 0, 13.917, -18.2, 1, 14.228, -18.2, 14.539, -2.89, 14.85, 0.31, 1, 15.061, 2.481, 15.272, 1.941, 15.483, 3.471, 1, 15.711, 5.121, 15.939, 7.744, 16.167, 7.744, 0, 16.45, -13.649, 0, 16.75, -9.12, 0, 19.05, -16.02, 1, 19.2, -16.02, 19.35, -15.803, 19.5, -11, 1, 19.6, -7.798, 19.7, 8, 19.8, 8, 0, 20.05, -2, 1, 20.222, -2, 20.395, 22.653, 20.567, 23.116, 1, 21.456, 25.506, 22.344, 26.14, 23.233, 26.14, 0, 25.367, 23.5, 0, 25.733, 26.8, 0, 26.3, -7.999, 0, 26.5, 0.131]}, {"Target": "Parameter", "Id": "Param5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.75, 0, 2, 2.783, 0, 0, 3.267, 8.04, 2, 5.867, 8.04, 0, 6.333, 0, 2, 12.733, 0, 2, 13.5, 0, 0, 14.933, -2.52, 2, 16.167, -2.52, 0, 16.75, -19.56, 2, 19.667, -19.56, 0, 20.25, -19.6, 2, 25.333, -19.6, 1, 25.511, -19.6, 25.689, -19.917, 25.867, -16, 1, 26.078, -11.348, 26.289, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamHandRChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.75, 0, 0, 2.783, 30, 0, 3.267, 0, 2, 5.867, 0, 0, 6.333, -30, 2, 12.733, -30, 0, 13.5, 0, 2, 14.35, 0, 0, 14.7, -30, 2, 16.167, -30, 2, 16.45, -30, 0, 16.75, 30, 2, 19.667, 30, 0, 20.25, 0, 2, 25.333, 0, 2, 25.633, 0, 0, 25.8, -8.033, 0, 26.033, 0, 2, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamHandRAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.75, 0, 0, 2, 30, 2, 2.783, 30, 0, 3.267, 0, 2, 5.867, 0, 0, 6.333, -19, 2, 12.8, -19, 2, 12.817, 0, 2, 13.5, 0, 2, 14.35, 0, 0, 14.7, -22.26, 0, 16.167, 0, 0, 16.45, -30, 2, 16.75, -30, 2, 19.667, -30, 0, 20.25, 0, 2, 25.333, 0, 0, 25.633, -30, 0, 25.8, 0, 0, 26.033, -30, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamHandRREDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.75, 7, 2, 2.783, 7, 2, 3.267, 0, 2, 5.867, 0, 2, 6.333, 7, 2, 12.8, 7, 2, 12.817, 0, 2, 13.5, 0, 2, 14.083, 7, 2, 14.933, 7, 2, 16.45, 7, 2, 16.467, 3, 2, 19.667, 3, 2, 20.25, 2, 2, 25.867, 2, 2, 25.883, 0, 2, 26.5, 0]}, {"Target": "Parameter", "Id": "Param101", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 13.5, 0, 2, 20.25, 0, 0, 20.55, 1, 2, 25.433, 1, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param55", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.75, 1, 2, 2.467, 1, 0, 2.983, 6, 2, 5.383, 6, 1, 5.411, 6, 5.439, 6.673, 5.467, 7.14, 1, 5.828, 13.212, 6.189, 15.99, 6.55, 15.99, 2, 6.867, 15.99, 1, 6.934, 15.99, 7, 15.772, 7.067, 15.403, 1, 8.928, 5.113, 10.789, 0, 12.65, 0, 0, 12.883, 15.99, 0, 13.5, 0, 2, 16.167, 0, 0, 16.75, 20.39, 1, 17.722, 20.39, 18.695, 18.246, 19.667, 11, 1, 19.861, 9.551, 20.056, 0, 20.25, 0, 2, 25.65, 0, 0, 25.883, 15.99, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamFore<PERSON><PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.733, 0, 2, 0.75, 30, 2, 2.467, 30, 1, 2.667, 30, 2.867, 17.355, 3.067, -9.633, 2, 3.083, -23.52, 2, 5.383, -23.52, 2, 5.75, -23.52, 2, 5.767, 20, 2, 6.25, 20, 2, 6.267, 20, 2, 6.867, 20, 2, 7.067, 29.879, 1, 9.039, 29.879, 11.011, 26.604, 12.983, 20, 2, 13, 0, 2, 13.5, 0, 2, 16.733, 0, 2, 16.75, 5.2, 2, 20, 5.2, 2, 20.017, 20, 2, 25.983, 20, 2, 26, 0, 2, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamForearmLChange2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.75, 0, 2, 2.467, 0, 2, 2.983, 0, 2, 5.383, 0, 2, 5.467, 0, 0, 6.55, 30, 2, 6.867, 30, 0, 7.067, 0, 2, 13.5, 0, 2, 16.167, 0, 2, 16.75, 0, 2, 19.667, 0, 2, 20.25, 0, 2, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamArmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.75, 5.64, 2, 2.467, 5.64, 0, 3.35, -17.92, 2, 5.383, -17.92, 0, 6.233, 11, 1, 6.444, 11, 6.656, 8.155, 6.867, 8.009, 1, 8.795, 6.677, 10.722, 5.671, 12.65, 4.38, 1, 12.783, 4.291, 12.917, 0, 13.05, 0, 2, 13.5, 0, 2, 16.167, 0, 0, 16.45, 6.54, 0, 16.75, 0, 2, 19.667, 0, 1, 19.861, 0, 20.056, 4.125, 20.25, 4.38, 1, 20.389, 4.562, 20.528, 4.5, 20.667, 4.5, 1, 22.328, 4.5, 23.989, 4.466, 25.65, 4.38, 1, 25.783, 4.373, 25.917, 0, 26.05, 0, 2, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamForearmLStretch", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.75, -1.02, 2, 2.467, -1.02, 0, 3.35, 18.6, 2, 5.383, 18.6, 0, 6.183, -16.263, 1, 6.411, -16.263, 6.639, -14.848, 6.867, -14.712, 1, 8.795, -13.561, 10.722, -12.695, 12.65, -11.58, 1, 12.783, -11.503, 12.917, 0, 13.05, 0, 2, 13.5, 0, 2, 16.167, 0, 0, 16.45, 1, 0, 16.75, -2.94, 2, 19.667, -2.94, 1, 19.861, -2.94, 20.056, -7.196, 20.25, -11.58, 1, 20.389, -14.712, 20.528, -15.12, 20.667, -15.12, 1, 22.328, -15.12, 23.989, -14.123, 25.65, -11.58, 1, 25.783, -11.376, 25.917, 0, 26.05, 0, 2, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamArmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.089, 0, 0.217, -3.374, 0, 0.817, 15, 0, 1.3, 11.97, 0, 1.933, 14.3, 1, 2.133, 14.3, 2.333, 14.132, 2.533, 11.97, 1, 2.716, 9.988, 2.9, 2.449, 3.083, 1.5, 1, 3.205, 0.867, 3.328, 0.621, 3.45, 0.589, 1, 4.094, 0.423, 4.739, 0.376, 5.383, 0.376, 0, 5.967, 5, 0, 6.867, 1.993, 0, 7.983, 3.628, 0, 9.267, 1.281, 0, 10.767, 3.293, 0, 11.95, 1.993, 0, 12.8, 3.549, 1, 13.033, 3.549, 13.267, 2.261, 13.5, -0.089, 1, 13.633, -1.432, 13.767, -2, 13.9, -2, 0, 14.6, -0.168, 1, 15.006, -0.168, 15.411, -0.368, 15.817, -1, 1, 15.878, -1.095, 15.939, -3, 16, -3, 0, 16.433, 2, 0, 16.833, 0.945, 2, 19.167, 0.945, 1, 19.445, 0.945, 19.722, 1.621, 20, 4, 1, 20.067, 4.571, 20.133, 6.974, 20.2, 6.974, 1, 20.344, 6.974, 20.489, 3.895, 20.633, 3.815, 1, 21.95, 3.085, 23.266, 2.825, 24.583, 2.825, 1, 24.794, 2.825, 25.006, 2.868, 25.217, 3.185, 1, 25.339, 3.368, 25.461, 4.015, 25.583, 4.415, 1, 25.716, 4.852, 25.85, 5.056, 25.983, 5.056, 0, 26.5, -0.089]}, {"Target": "Parameter", "Id": "ParamForearmLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.769, 0, 0.3, -2, 0, 0.75, 10.516, 0, 1.233, 3.06, 0, 1.883, 4.767, 0, 2.467, 3.06, 1, 2.639, 3.06, 2.811, 18.789, 2.983, 26.029, 1, 3.105, 31.167, 3.228, 30.68, 3.35, 30.68, 1, 4.028, 30.68, 4.705, 30.457, 5.383, 30, 2, 5.4, -30, 1, 5.5, -30, 5.6, -30.775, 5.7, -28.442, 1, 5.878, -24.295, 6.055, 0.961, 6.233, 0.961, 0, 7, -1.479, 0, 8.083, -0.48, 0, 9.433, -2.028, 0, 10.95, 0.213, 1, 11.322, 0.213, 11.695, -0.202, 12.067, -1.479, 1, 12.261, -2.146, 12.456, -2.817, 12.65, -2.817, 0, 13.15, 5.449, 1, 13.267, 5.449, 13.383, 1.72, 13.5, 0.769, 1, 13.633, -0.318, 13.767, -0.251, 13.9, -0.251, 0, 14.6, 2.44, 1, 15.028, 2.44, 15.455, 2.015, 15.883, 0.769, 1, 15.944, 0.591, 16.006, -0.851, 16.067, -0.851, 0, 16.517, 5.82, 1, 16.65, 5.82, 16.784, -0.842, 16.917, -1.14, 1, 17.667, -2.818, 18.417, -3.3, 19.167, -3.3, 1, 19.445, -3.3, 19.722, -1.925, 20, 1.8, 1, 20.1, 3.141, 20.2, 4.544, 20.3, 4.544, 0, 20.733, -5.88, 0, 24.583, -1.785, 1, 24.822, -1.785, 25.061, -1.932, 25.3, -2.88, 1, 25.417, -3.343, 25.533, -4.68, 25.65, -4.68, 0, 26.15, 5.449, 0, 26.5, 0.769]}, {"Target": "Parameter", "Id": "ParamHandLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -3.105, 0, 0.5, 7, 0, 0.967, -12, 0, 1.367, 0.48, 0, 1.983, -4.177, 1, 2.116, -4.177, 2.25, -4.651, 2.383, 0.48, 1, 2.561, 7.321, 2.739, 23.283, 2.917, 23.283, 0, 3.25, -3.754, 2, 5.383, -3.754, 1, 5.533, -3.754, 5.683, 7.418, 5.833, 10, 1, 6.028, 13.347, 6.222, 13.28, 6.417, 16.594, 1, 6.561, 19.056, 6.706, 30, 6.85, 30, 0, 7.283, 27.373, 2, 8.35, 27.373, 2, 10.233, 27.373, 0, 12.183, 30, 1, 12.372, 30, 12.561, 30, 12.75, 26, 1, 12.928, 21.766, 13.105, -11, 13.283, -11, 1, 13.355, -11, 13.428, -7.605, 13.5, -3.105, 1, 13.633, 5.202, 13.767, 8.475, 13.9, 8.475, 0, 14.6, -5, 0, 15.283, 0.651, 0, 15.983, -3.105, 0, 16.283, 7.816, 0, 16.633, -0.58, 1, 16.766, -0.58, 16.9, 20.969, 17.033, 22.601, 1, 17.233, 25.048, 17.433, 24.96, 17.633, 24.96, 0, 19.167, 20.82, 0, 20, 27.66, 0, 20.417, 3.84, 1, 20.561, 3.84, 20.706, 24.57, 20.85, 25, 1, 22.094, 28.702, 23.339, 30, 24.583, 30, 2, 25.367, 30, 1, 25.495, 30, 25.622, 30, 25.75, 26, 1, 25.928, 19.14, 26.105, -11, 26.283, -11, 0, 26.5, -3.105]}, {"Target": "Parameter", "Id": "Param7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.75, 0, 2, 2.467, 0, 0, 2.983, -11, 2, 12.65, -11, 0, 13.5, 0, 2, 16.167, 0, 0, 16.75, -3.96, 2, 19.667, -3.96, 0, 20.25, -13.44, 2, 25.283, -13.44, 2, 25.65, -13.44, 0, 26.05, 0, 2, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamHandLChange", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.75, 30, 2, 2.467, 30, 0, 2.983, 0, 2, 5.383, 0, 2, 5.467, 0, 2, 6.55, 0, 2, 6.867, 0, 2, 7.067, 0, 2, 12.283, 0, 2, 12.65, 0, 2, 13.05, 0, 2, 13.5, 0, 2, 16.167, 0, 0, 16.283, 29, 1, 16.511, 29, 16.739, -17.467, 16.967, -28, 1, 17.045, -30, 17.122, -30, 17.2, -30, 2, 17.633, -30, 2, 19.167, -30, 1, 19.334, -30, 19.5, -30, 19.667, -28, 1, 19.784, -26.541, 19.9, -10.548, 20.017, 2, 1, 20.067, 7.378, 20.117, 30, 20.167, 30, 0, 20.433, -23, 0, 25.283, 0, 2, 25.65, 0, 2, 26.05, 0, 2, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamHandLAnimation", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.75, 0, 2, 2.467, 0, 0, 2.983, 21, 2, 5.383, 21, 0, 5.467, 0, 2, 6.55, 0, 2, 6.867, 0, 2, 7.067, 0, 2, 12.283, 0, 2, 12.65, 0, 2, 13.05, 0, 2, 13.5, 0, 2, 16.167, 0, 0, 16.283, -30, 1, 16.511, -30, 16.739, -20.243, 16.967, 0, 1, 17.189, 19.75, 17.411, 30, 17.633, 30, 0, 19.167, 20, 0, 19.667, 30, 1, 19.784, 30, 19.9, 26.135, 20.017, 7, 1, 20.067, -1.201, 20.117, -30, 20.167, -30, 2, 20.433, -30, 0, 25.283, 0, 2, 25.65, 0, 2, 26.05, 0, 2, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamHandLREDisplay", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 2, 0.6, 6, 2, 2.467, 6, 2, 2.983, 7, 2, 5.383, 7, 2, 5.467, 2, 2, 6.767, 2, 2, 7.067, 2, 2, 12.867, 2, 2, 12.883, 1, 2, 13.5, 1, 2, 16.267, 1, 2, 16.283, 3, 2, 20.433, 3, 2, 20.45, 2, 2, 25.867, 2, 2, 25.883, 1, 2, 26.5, 1]}, {"Target": "Parameter", "Id": "Param100", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 6.733, 0, 0, 7.367, 1, 2, 12.783, 1, 0, 13.5, 0, 2, 20.25, 0, 0, 20.55, 1, 2, 25.433, 1, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 4.502, 0, 0.283, 6.211, 0, 0.867, 1.897, 0, 1.367, 3.39, 0, 1.767, 2.709, 0, 2.317, 5.087, 0, 2.717, 2.433, 0, 3.1, 5.343, 0, 4.35, 2.561, 0, 5.3, 5.945, 0, 5.75, -0.107, 0, 6.35, 7.425, 0, 6.867, 4.875, 0, 7.183, 5.632, 0, 8.483, 0.208, 1, 8.622, 0.208, 8.761, 0.036, 8.9, 0.391, 1, 9.239, 1.258, 9.578, 6.466, 9.917, 6.466, 0, 10.583, 3.376, 0, 11.8, 7.689, 0, 12.3, 5.918, 0, 12.717, 9.575, 1, 12.978, 9.575, 13.239, 6.795, 13.5, 4.445, 1, 13.656, 3.045, 13.811, 3.065, 13.967, 3.065, 0, 14.783, 6.082, 0, 15.333, 4.728, 0, 15.917, 7.859, 0, 16.183, 7.08, 0, 16.483, 8.666, 1, 16.744, 8.666, 17.006, 3.876, 17.267, 3.293, 1, 17.5, 2.772, 17.734, 2.889, 17.967, 2.889, 0, 18.917, 7.967, 0, 19.367, 4.309, 0, 19.8, 8.935, 0, 21.483, 0.208, 1, 21.622, 0.208, 21.761, 0.036, 21.9, 0.391, 1, 22.239, 1.258, 22.578, 6.466, 22.917, 6.466, 0, 23.583, 3.376, 0, 24.8, 7.689, 0, 25.3, 5.918, 0, 25.717, 9.575, 0, 26.383, 4.239, 0, 26.5, 4.445]}, {"Target": "Parameter", "Id": "Param2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.204, 0, 0.283, -0.854, 0, 0.867, 7.879, 0, 1.35, 4.482, 0, 1.767, 5.012, 0, 2.317, 1.686, 0, 2.717, 6.673, 0, 3.1, 0.739, 0, 4.35, 6.468, 0, 5.3, -0.327, 0, 5.75, 12.357, 0, 6.35, -2.923, 0, 6.867, 1.549, 0, 7.183, 0.244, 1, 7.661, 0.244, 8.139, 0.971, 8.617, 2, 1, 9.289, 3.448, 9.961, 4, 10.633, 4, 0, 13.5, 2.296, 0, 13.917, 5.235, 0, 14.783, -0.463, 1, 15.055, -0.463, 15.328, -0.528, 15.6, 0.8, 1, 15.8, 1.776, 16, 7.86, 16.2, 7.86, 0, 16.917, 1, 0, 19.3, 4.8, 0, 20, -3.701, 0, 21.617, 8.436, 1, 22.289, 8.436, 22.961, 8.434, 23.633, 7, 1, 24.589, 4.962, 25.544, 2.296, 26.5, 2.296]}, {"Target": "Parameter", "Id": "Param", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 7.526, 0, 0.3, 9.547, 0, 2.183, -2.068, 0, 4.35, 9.551, 0, 4.7, 8.94, 0, 4.983, 9.455, 0, 5.367, 5.79, 0, 5.733, 10.602, 0, 6.317, 3.689, 0, 6.9, 7.052, 1, 7.017, 7.052, 7.133, 7.76, 7.25, 6.294, 1, 7.983, -2.92, 8.717, -13.65, 9.45, -13.65, 1, 9.567, -13.65, 9.683, -13.304, 9.8, -10, 1, 10.061, -2.605, 10.322, 3.273, 10.583, 3.273, 0, 11.367, 0.431, 0, 12.3, 8.007, 0, 12.917, -0.88, 1, 13.111, -0.88, 13.306, 3.415, 13.5, 7.479, 1, 13.606, 9.685, 13.711, 9.705, 13.817, 9.705, 0, 15.05, -2.661, 0, 15.3, -2.482, 0, 15.717, -3.433, 0, 17.933, 12.523, 1, 18.094, 12.523, 18.256, 12.844, 18.417, 12.259, 1, 18.872, 10.604, 19.328, -2.191, 19.783, -2.191, 0, 20.067, 0.488, 0, 22.667, -13.892, 0, 23.583, 3.273, 0, 24.367, 0.431, 0, 25.3, 8.007, 0, 25.917, -0.88, 0, 26.5, 7.479]}, {"Target": "Parameter", "Id": "Param6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.323, 0, 0.267, -4.71, 0, 0.9, -6.2, 0, 1.233, -6.005, 0, 1.55, -6.105, 0, 2.2, -5.669, 0, 2.617, -6.123, 0, 3.1, -5.075, 0, 4.5, -5.202, 0, 4.917, -4.9, 0, 5.217, -5.033, 0, 5.417, -4.891, 0, 5.8, -5.782, 0, 6.383, -5.112, 0, 6.817, -5.269, 1, 6.922, -5.269, 7.028, -5.201, 7.133, -5.179, 1, 7.572, -5.089, 8.011, -5.059, 8.45, -4.946, 1, 8.906, -4.829, 9.361, -4.633, 9.817, -4.633, 0, 10.4, -4.992, 0, 12.65, -4.713, 0, 13.5, -5.347, 0, 13.767, -4.73, 0, 14.3, -5.426, 0, 15.733, -4.492, 0, 17.017, -5.33, 0, 18.9, -4.741, 0, 19.467, -4.771, 0, 20.033, -4.627, 0, 21.45, -4.946, 0, 22.817, -4.633, 0, 23.4, -4.992, 0, 25.65, -4.713, 0, 26.25, -5.505, 0, 26.5, -5.347]}, {"Target": "Parameter", "Id": "Param48", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -8.246, 0, 0.267, -12.887, 0, 0.9, -1.649, 0, 1.233, -3.125, 0, 1.55, -2.378, 0, 2.2, -5.687, 0, 2.617, -2.272, 0, 3.1, -10.137, 0, 4.5, -9.191, 0, 4.917, -11.456, 0, 5.2, -10.448, 0, 5.417, -11.53, 0, 5.8, -4.835, 0, 6.383, -9.814, 0, 6.817, -8.649, 0, 7.133, -9.328, 1, 7.65, -9.328, 8.166, -8.667, 8.683, -7, 1, 9.294, -5.028, 9.906, -3.845, 10.517, -3.845, 1, 11.511, -3.845, 12.506, -5.012, 13.5, -8.069, 1, 13.594, -8.359, 13.689, -12.741, 13.783, -12.741, 0, 14.3, -7.487, 0, 15.733, -10, 0, 16.2, -4.56, 0, 17.017, -8.197, 0, 19.3, -3.91, 0, 20, -9.551, 0, 21.683, 0.666, 1, 22.294, 0.666, 22.906, 0.858, 23.517, -0.845, 1, 24.511, -3.616, 25.506, -8.069, 26.5, -8.069]}, {"Target": "Parameter", "Id": "Param37", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 8.94, 0, 0.583, 12.731, 0, 1.017, 12.399, 0, 2.6, 18.883, 0, 3.233, 11.534, 0, 4.333, 14.52, 0, 5.267, 10.101, 0, 5.7, 18.777, 0, 6.317, 5.161, 0, 6.883, 8.419, 0, 7.217, 7.664, 1, 7.95, 7.664, 8.684, 11.313, 9.417, 20.22, 1, 9.522, 21.502, 9.628, 23.716, 9.733, 23.716, 0, 10.383, 1.968, 0, 13.133, 12.107, 0, 13.5, 8.923, 0, 14.083, 12.676, 0, 14.65, 10.573, 0, 16.117, 16.115, 0, 17.333, 8.858, 0, 17.85, 9.335, 1, 18.061, 9.335, 18.272, 9.621, 18.483, 8.265, 1, 18.911, 5.517, 19.339, 0.812, 19.767, 0.812, 0, 22.733, 20.791, 0, 23.633, 1.968, 0, 26.133, 12.107, 0, 26.5, 8.923]}, {"Target": "Parameter", "Id": "Param38", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 0.628, 0, 1.255, -3.689, 1.883, -3.775, 1, 4.355, -4.115, 6.828, -4.185, 9.3, -4.185, 2, 12.217, -4.185, 0, 13.5, 0, 0, 22.3, -4.185, 2, 25.217, -4.185, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param60", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 9.3, -13, 2, 12.217, -13, 0, 13.5, 0, 0, 22.3, -13, 2, 25.217, -13, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param171", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 13.5, 0, 0, 14.167, 1, 2, 25.433, 1, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.9, 2, 0.133, 0.9, 0, 0.483, 0, 0, 0.883, 0.9, 2, 1.817, 0.9, 2, 3.033, 0.9, 0, 3.35, 0, 0, 3.667, 0.9, 2, 4.517, 0.9, 0, 4.717, 0, 0, 4.933, 0.9, 2, 7.267, 0.9, 0, 7.467, 0, 0, 7.683, 0.9, 2, 9.083, 0.9, 0, 9.433, 0, 0, 9.833, 0.9, 2, 11.917, 0.9, 0, 12.267, 0, 0, 12.667, 0.9, 2, 13.5, 0.9, 2, 13.633, 0.9, 0, 13.983, 0, 0, 14.383, 0.9, 2, 15.317, 0.9, 2, 16.533, 0.9, 0, 16.85, 0, 0, 17.167, 0.9, 2, 18.017, 0.9, 0, 18.217, 0, 0, 18.433, 0.9, 2, 20.767, 0.9, 0, 20.967, 0, 0, 21.183, 0.9, 2, 22.583, 0.9, 0, 22.933, 0, 0, 23.333, 0.9, 2, 25.383, 0.9, 0, 25.733, 0, 0, 26.133, 0.9, 2, 26.5, 0.9]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.317, 0, 0, 0.617, 0.457, 0, 1.117, 0, 2, 1.217, 0, 0, 1.517, 0.457, 0, 1.817, 0, 2, 3.2, 0, 0, 3.45, 0.411, 0, 3.883, 0, 2, 4.617, 0, 0, 4.783, 0.457, 0, 5.067, 0, 2, 7.367, 0, 0, 7.533, 0.457, 0, 7.817, 0, 2, 9.267, 0, 0, 9.567, 0.457, 0, 10.067, 0, 2, 12.1, 0, 0, 12.4, 0.457, 0, 12.9, 0, 2, 13.5, 0, 2, 13.817, 0, 0, 14.117, 0.518, 0, 14.617, 0, 2, 14.717, 0, 0, 15.017, 0.518, 0, 15.317, 0, 2, 16.7, 0, 0, 16.95, 0.466, 0, 17.383, 0, 2, 18.117, 0, 0, 18.283, 0.518, 0, 18.567, 0, 2, 20.867, 0, 0, 21.033, 0.518, 0, 21.317, 0, 2, 22.767, 0, 0, 23.067, 0.518, 0, 23.567, 0, 2, 25.567, 0, 0, 25.867, 0.518, 0, 26.367, 0, 2, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.9, 2, 0.133, 0.9, 0, 0.483, 0, 0, 0.883, 0.9, 2, 1.817, 0.9, 2, 3.033, 0.9, 0, 3.35, 0, 0, 3.667, 0.9, 2, 4.517, 0.9, 0, 4.717, 0, 0, 4.933, 0.9, 2, 7.267, 0.9, 0, 7.467, 0, 0, 7.683, 0.9, 2, 9.083, 0.9, 0, 9.433, 0, 0, 9.833, 0.9, 2, 11.917, 0.9, 0, 12.267, 0, 0, 12.667, 0.9, 2, 13.5, 0.9, 2, 13.633, 0.9, 0, 13.983, 0, 0, 14.383, 0.9, 2, 15.317, 0.9, 2, 16.533, 0.9, 0, 16.85, 0, 0, 17.167, 0.9, 2, 18.017, 0.9, 0, 18.217, 0, 0, 18.433, 0.9, 2, 20.767, 0.9, 0, 20.967, 0, 0, 21.183, 0.9, 2, 22.583, 0.9, 0, 22.933, 0, 0, 23.333, 0.9, 2, 25.383, 0.9, 0, 25.733, 0, 0, 26.133, 0.9, 2, 26.5, 0.9]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.317, 0, 0, 0.617, 0.457, 0, 1.117, 0, 2, 1.217, 0, 0, 1.517, 0.457, 0, 1.817, 0, 2, 3.2, 0, 0, 3.45, 0.411, 0, 3.883, 0, 2, 4.617, 0, 0, 4.783, 0.457, 0, 5.067, 0, 2, 7.367, 0, 0, 7.533, 0.457, 0, 7.817, 0, 2, 9.267, 0, 0, 9.567, 0.457, 0, 10.067, 0, 2, 12.1, 0, 0, 12.4, 0.457, 0, 12.9, 0, 2, 13.5, 0, 2, 13.817, 0, 0, 14.117, 0.518, 0, 14.617, 0, 2, 14.717, 0, 0, 15.017, 0.518, 0, 15.317, 0, 2, 16.7, 0, 0, 16.95, 0.466, 0, 17.383, 0, 2, 18.117, 0, 0, 18.283, 0.518, 0, 18.567, 0, 2, 20.867, 0, 0, 21.033, 0.518, 0, 21.317, 0, 2, 22.767, 0, 0, 23.067, 0.518, 0, 23.567, 0, 2, 25.567, 0, 0, 25.867, 0.518, 0, 26.367, 0, 2, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.683, 0.07, 2, 1.283, 0.07, 0, 1.817, 0, 2, 3.033, 0, 0, 3.333, 0.07, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, 0.07, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, 0.07, 0, 7.683, 0, 2, 9.083, 0, 0, 9.433, 0.07, 0, 9.833, 0, 2, 11.917, 0, 0, 12.267, 0.07, 0, 12.667, 0, 2, 13.5, 0, 0, 14.183, 0.07, 2, 14.783, 0.07, 0, 15.317, 0, 2, 16.533, 0, 0, 16.833, 0.07, 0, 17.25, 0, 2, 18.017, 0, 0, 18.217, 0.07, 0, 18.433, 0, 2, 20.767, 0, 0, 20.967, 0.07, 0, 21.183, 0, 2, 22.583, 0, 0, 22.933, 0.07, 0, 23.333, 0, 2, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.683, 0.364, 2, 1.283, 0.364, 0, 1.817, 0, 2, 3.033, 0, 0, 3.333, 0.364, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, 0.364, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, 0.364, 0, 7.683, 0, 2, 9.083, 0, 0, 9.433, 0.364, 0, 9.833, 0, 2, 11.917, 0, 0, 12.267, 0.364, 0, 12.667, 0, 2, 13.5, 0, 0, 14.183, 0.364, 2, 14.783, 0.364, 0, 15.317, 0, 2, 16.533, 0, 0, 16.833, 0.364, 0, 17.25, 0, 2, 18.017, 0, 0, 18.217, 0.364, 0, 18.433, 0, 2, 20.767, 0, 0, 20.967, 0.364, 0, 21.183, 0, 2, 22.583, 0, 0, 22.933, 0.364, 0, 23.333, 0, 2, 26.5, 0]}, {"Target": "Parameter", "Id": "Param4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.683, -0.586, 2, 1.283, -0.586, 0, 1.817, 0, 2, 3.033, 0, 0, 3.333, -0.586, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, -0.586, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, -0.586, 0, 7.683, 0, 2, 9.083, 0, 0, 9.433, -0.586, 0, 9.833, 0, 2, 11.917, 0, 0, 12.267, -0.586, 0, 12.667, 0, 2, 13.5, 0, 0, 14.183, -0.586, 2, 14.783, -0.586, 0, 15.317, 0, 2, 16.533, 0, 0, 16.833, -0.586, 0, 17.25, 0, 2, 18.017, 0, 0, 18.217, -0.586, 0, 18.433, 0, 2, 20.767, 0, 0, 20.967, -0.586, 0, 21.183, 0, 2, 22.583, 0, 0, 22.933, -0.586, 0, 23.333, 0, 2, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.683, 0.262, 2, 1.283, 0.262, 0, 1.817, 0, 2, 3.033, 0, 0, 3.333, 0.262, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, 0.262, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, 0.262, 0, 7.683, 0, 2, 9.083, 0, 0, 9.433, 0.262, 0, 9.833, 0, 2, 11.917, 0, 0, 12.267, 0.262, 0, 12.667, 0, 2, 13.5, 0, 0, 14.183, 0.262, 2, 14.783, 0.262, 0, 15.317, 0, 2, 16.533, 0, 0, 16.833, 0.262, 0, 17.25, 0, 2, 18.017, 0, 0, 18.217, 0.262, 0, 18.433, 0, 2, 20.767, 0, 0, 20.967, 0.262, 0, 21.183, 0, 2, 22.583, 0, 0, 22.933, 0.262, 0, 23.333, 0, 2, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.683, 0.366, 2, 1.283, 0.366, 0, 1.817, 0, 2, 3.033, 0, 0, 3.333, 0.366, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, 0.366, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, 0.366, 0, 7.683, 0, 2, 9.083, 0, 0, 9.433, 0.366, 0, 9.833, 0, 2, 11.917, 0, 0, 12.267, 0.366, 0, 12.667, 0, 2, 13.5, 0, 0, 14.183, 0.366, 2, 14.783, 0.366, 0, 15.317, 0, 2, 16.533, 0, 0, 16.833, 0.366, 0, 17.25, 0, 2, 18.017, 0, 0, 18.217, 0.366, 0, 18.433, 0, 2, 20.767, 0, 0, 20.967, 0.366, 0, 21.183, 0, 2, 22.583, 0, 0, 22.933, 0.366, 0, 23.333, 0, 2, 26.5, 0]}, {"Target": "Parameter", "Id": "Param11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.817, -0.572, 2, 1.283, -0.572, 0, 1.817, 0, 2, 3.033, 0, 0, 3.333, -0.572, 0, 3.75, 0, 2, 4.517, 0, 0, 4.717, -0.572, 0, 4.933, 0, 2, 7.267, 0, 0, 7.467, -0.572, 0, 7.683, 0, 2, 9.083, 0, 0, 9.433, -0.594, 0, 9.833, 0, 2, 11.917, 0, 0, 12.267, -0.594, 0, 12.667, 0, 2, 13.5, 0, 0, 14.317, -0.572, 2, 14.783, -0.572, 0, 15.317, 0, 2, 16.533, 0, 0, 16.833, -0.572, 0, 17.25, 0, 2, 18.017, 0, 0, 18.217, -0.572, 0, 18.433, 0, 2, 20.767, 0, 0, 20.967, -0.572, 0, 21.183, 0, 2, 22.533, 0, 0, 22.733, -0.572, 0, 22.95, 0, 2, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 0.15, -0.244, 0, 0.333, -0.01, 0, 0.45, -0.319, 2, 0.633, -0.319, 2, 0.817, -0.319, 0, 0.9, 0.656, 0, 0.95, -0.319, 2, 1.133, -0.319, 0, 2.067, 0, 0, 2.217, -0.244, 0, 2.4, -0.01, 0, 2.517, -0.319, 2, 2.7, -0.319, 2, 2.883, -0.319, 0, 2.967, 0.656, 0, 3.017, -0.319, 2, 3.2, -0.319, 2, 3.4, -0.319, 2, 3.433, -0.319, 0, 3.6, 0.5, 0, 3.7, -0.319, 2, 3.733, -0.319, 1, 3.789, -0.319, 3.844, 0.198, 3.9, 0.5, 1, 3.933, 0.681, 3.967, 0.656, 4, 0.656, 0, 4.05, -0.319, 2, 4.233, -0.319, 2, 4.433, -0.319, 2, 4.467, -0.319, 1, 4.522, -0.319, 4.578, 0.478, 4.633, 0.5, 1, 5.211, 0.724, 5.789, 0.808, 6.367, 0.808, 0, 6.583, -0.319, 1, 6.605, -0.319, 6.628, 0.148, 6.65, 0.34, 1, 6.711, 0.869, 6.772, 1, 6.833, 1, 1, 6.839, 1, 6.844, 0.803, 6.85, 0.8, 1, 7.317, 0.53, 7.783, 0.29, 8.25, 0, 1, 8.3, -0.031, 8.35, -0.244, 8.4, -0.244, 0, 8.583, -0.01, 0, 8.7, -0.319, 2, 8.883, -0.319, 2, 9.067, -0.319, 0, 9.15, 0.656, 0, 9.2, -0.319, 1, 9.25, -0.319, 9.3, 0.473, 9.35, 0.5, 1, 9.772, 0.728, 10.195, 0.808, 10.617, 0.808, 0, 10.833, -0.319, 1, 10.855, -0.319, 10.878, 0.148, 10.9, 0.34, 1, 10.961, 0.869, 11.022, 1, 11.083, 1, 1, 11.089, 1, 11.094, 0.829, 11.1, 0.8, 1, 11.122, 0.685, 11.145, 0.656, 11.167, 0.656, 0, 11.217, 1, 0, 11.4, -0.319, 1, 11.467, -0.319, 11.533, 0.142, 11.6, 0.224, 1, 11.644, 0.279, 11.689, 0.251, 11.733, 0.3, 1, 11.783, 0.355, 11.833, 0.808, 11.883, 0.808, 0, 12.1, -0.319, 1, 12.122, -0.319, 12.145, 0.148, 12.167, 0.34, 1, 12.228, 0.869, 12.289, 1, 12.35, 1, 1, 12.356, 1, 12.361, 0.829, 12.367, 0.8, 1, 12.389, 0.685, 12.411, 0.656, 12.433, 0.656, 0, 12.483, 1, 0, 12.667, -0.319, 0, 12.867, 0.224, 2, 13, 0.224, 0, 13.083, 0, 2, 13.233, 0, 2, 13.367, 0, 0, 13.517, -0.244, 1, 13.578, -0.244, 13.639, -0.134, 13.7, -0.01, 1, 13.778, 0.148, 13.855, 0.354, 13.933, 0.4, 1, 14.444, 0.7, 14.956, 0.808, 15.467, 0.808, 0, 15.667, 0, 0, 15.817, 1, 0, 15.9, 0.656, 0, 15.95, 1, 0, 16.133, -0.319, 0, 16.25, 0.224, 2, 16.383, 0.224, 0, 16.467, 0, 1, 16.495, 0, 16.522, 0.33, 16.55, 0.34, 1, 16.911, 0.469, 17.272, 0.568, 17.633, 0.7, 1, 17.672, 0.714, 17.711, 1, 17.75, 1, 2, 17.833, 1, 0, 18.017, -0.319, 0, 18.1, 0.656, 0, 18.15, -0.319, 2, 18.333, -0.319, 2, 18.533, -0.319, 2, 18.567, -0.319, 0, 18.667, 0.808, 0, 18.733, -0.319, 0, 18.867, 0, 0, 18.883, -0.319, 1, 18.905, -0.319, 18.928, 0.148, 18.95, 0.34, 1, 19.011, 0.869, 19.072, 1, 19.133, 1, 1, 19.139, 1, 19.144, 0.829, 19.15, 0.8, 1, 19.172, 0.685, 19.195, 0.656, 19.217, 0.656, 0, 19.267, 1, 0, 19.45, -0.319, 0, 19.65, 0.224, 2, 19.783, 0.224, 0, 19.867, 0, 0, 19.95, 0.34, 0, 20.017, 0, 2, 20.783, 0, 0, 20.917, -0.319, 2, 21.1, -0.319, 2, 21.283, -0.319, 0, 21.367, 0.656, 0, 21.417, -0.319, 2, 21.6, -0.319, 2, 21.8, -0.319, 0, 21.833, 0, 2, 22.417, 0, 0, 22.55, -0.319, 2, 22.733, -0.319, 2, 22.917, -0.319, 0, 23, 0.656, 0, 23.05, -0.319, 2, 23.233, -0.319, 2, 23.433, -0.319, 2, 23.467, -0.319, 1, 23.517, -0.319, 23.567, 0.219, 23.617, 0.6, 1, 23.656, 0.897, 23.694, 0.9, 23.733, 0.9, 2, 26.5, 0.9]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 0.05, 0, 0.1, 0.185, 0.15, 0.5, 1, 0.206, 0.85, 0.261, 1, 0.317, 1, 0, 0.5, 0.102, 0, 0.683, 0.969, 0, 0.9, 0.012, 0, 1.1, 0.663, 1, 1.128, 0.663, 1.155, 0.026, 1.183, 0.024, 1, 1.478, 0.006, 1.772, 0, 2.067, 0, 1, 2.117, 0, 2.167, 0.185, 2.217, 0.5, 1, 2.272, 0.85, 2.328, 1, 2.383, 1, 0, 2.567, 0.102, 0, 2.75, 0.969, 0, 2.967, 0.012, 0, 3.167, 0.663, 0, 3.25, 0.024, 0, 3.4, 0.871, 2, 3.433, 0.871, 0, 3.6, 0, 0, 3.7, 0.871, 2, 3.733, 0.871, 0, 3.9, 0, 1, 3.933, 0, 3.967, 0, 4, 0.012, 1, 4.067, 0.057, 4.133, 0.663, 4.2, 0.663, 0, 4.283, 0.024, 0, 4.433, 0.871, 2, 4.467, 0.871, 0, 4.633, 0, 2, 6.367, 0, 1, 6.417, 0, 6.467, 0.233, 6.517, 0.396, 1, 6.55, 0.505, 6.584, 0.5, 6.617, 0.5, 1, 6.695, 0.5, 6.772, 0.363, 6.85, 0.323, 1, 7.317, 0.084, 7.783, 0, 8.25, 0, 1, 8.3, 0, 8.35, 0.185, 8.4, 0.5, 1, 8.456, 0.85, 8.511, 1, 8.567, 1, 0, 8.75, 0.102, 0, 8.933, 0.969, 1, 9.005, 0.969, 9.078, 0.03, 9.15, 0.012, 1, 9.217, 0, 9.283, 0, 9.35, 0, 2, 10.617, 0, 1, 10.667, 0, 10.717, 0.233, 10.767, 0.396, 1, 10.8, 0.505, 10.834, 0.5, 10.867, 0.5, 0, 11.1, 0.323, 0, 11.217, 0.9, 0, 11.3, 0.016, 0, 11.367, 0.663, 0, 11.45, 0.024, 0, 11.6, 0.871, 0, 11.733, 0, 2, 11.883, 0, 1, 11.933, 0, 11.983, 0.233, 12.033, 0.396, 1, 12.066, 0.505, 12.1, 0.5, 12.133, 0.5, 0, 12.367, 0.323, 0, 12.483, 0.9, 0, 12.567, 0.016, 0, 12.633, 0.663, 0, 12.717, 0.024, 0, 12.867, 0.871, 2, 13, 0.871, 0, 13.083, 0, 2, 13.233, 0, 2, 13.367, 0, 1, 13.417, 0, 13.467, 0.185, 13.517, 0.5, 1, 13.572, 0.85, 13.628, 1, 13.683, 1, 0, 13.933, 0, 2, 15.467, 0, 0, 15.667, 0.5, 0, 15.833, 0.323, 0, 15.95, 0.9, 0, 16.033, 0.016, 0, 16.1, 0.663, 0, 16.183, 0.024, 0, 16.25, 0.871, 2, 16.383, 0.871, 0, 16.467, 0, 2, 16.55, 0, 2, 17.633, 0, 1, 17.672, 0, 17.711, 0.716, 17.75, 0.9, 1, 17.778, 1, 17.805, 1, 17.833, 1, 0, 18.1, 0.012, 0, 18.3, 0.663, 0, 18.383, 0.024, 0, 18.533, 0.871, 2, 18.567, 0.871, 0, 18.667, 0, 2, 18.85, 0, 0, 18.917, 0.5, 0, 19.15, 0.323, 0, 19.267, 0.9, 0, 19.35, 0.016, 0, 19.417, 0.663, 0, 19.5, 0.024, 0, 19.65, 0.871, 2, 19.783, 0.871, 0, 19.867, 0, 2, 19.95, 0, 2, 20.017, 0, 2, 20.783, 0, 1, 20.844, 0, 20.906, 0, 20.967, 0.102, 1, 21.028, 0.224, 21.089, 0.969, 21.15, 0.969, 0, 21.367, 0.012, 0, 21.567, 0.663, 0, 21.65, 0.024, 0, 21.8, 0.871, 0, 21.833, 0, 2, 22.417, 0, 1, 22.478, 0, 22.539, 0, 22.6, 0.102, 1, 22.661, 0.224, 22.722, 0.969, 22.783, 0.969, 0, 23, 0.012, 0, 23.2, 0.663, 0, 23.283, 0.024, 0, 23.433, 0.871, 2, 23.467, 0.871, 0, 23.617, 0, 2, 23.733, 0, 2, 26.5, 0]}, {"Target": "Parameter", "Id": "Hair_physics", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.01, 0, 0.183, 3.954, 0, 0.617, -10.316, 0, 1.183, 6.212, 0, 1.667, 3.557, 0, 2.15, 11.458, 0, 2.567, -19.284, 0, 2.917, 8.806, 0, 3.4, -2.752, 0, 4.067, 8.595, 0, 4.5, 4.812, 0, 4.817, 17.199, 0, 5.167, 9.725, 0, 5.417, 20.187, 0, 5.75, -10.316, 0, 6.333, 17.407, 0, 6.7, -1.008, 0, 7.033, 3.999, 0, 8.4, -6.549, 0, 8.883, 5.852, 0, 9.233, 3.526, 0, 9.483, 8.84, 0, 9.817, -4.859, 0, 10.4, 17.407, 0, 10.767, -1.008, 0, 11.1, 3.999, 0, 11.917, -11.394, 0, 12.4, 19.941, 0, 13.067, -19.84, 1, 13.211, -19.84, 13.356, -10.986, 13.5, 0, 1, 13.561, 4.648, 13.622, 5.087, 13.683, 5.087, 0, 14.117, -9.604, 0, 14.683, 10.027, 0, 15.167, -2.715, 0, 15.65, 15.509, 0, 16.067, -14.084, 0, 16.417, 11.385, 0, 16.9, -17.935, 0, 17.567, 11.055, 0, 18, -6.168, 0, 18.317, 13.101, 0, 18.667, 1.475, 0, 18.917, 17.75, 0, 19.25, -9.604, 0, 19.833, 17.479, 0, 20.2, 0.28, 0, 20.533, 10.779, 0, 21.2, -3.304, 0, 22.583, 9.099, 0, 23.233, -2.303, 0, 24.7, 6.115, 0, 25.233, -2.583, 0, 25.717, 13.075, 0, 26.2, -6.803, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Hair_physics2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.01, 0, 0.183, 1.173, 0, 0.617, -2.6, 0, 1.183, 1.77, 0, 1.667, 1.068, 0, 2.15, 3.157, 0, 2.567, -4.971, 0, 2.917, 2.456, 0, 3.4, -0.6, 0, 4.067, 2.4, 0, 4.5, 1.4, 0, 4.817, 4.675, 0, 5.167, 2.699, 0, 5.417, 5.465, 0, 5.75, -2.6, 0, 6.333, 4.73, 0, 6.7, -0.139, 0, 7.033, 1.185, 0, 8.4, -1.604, 0, 8.883, 1.675, 0, 9.233, 1.06, 0, 9.483, 2.465, 0, 9.817, -1.157, 0, 10.4, 4.73, 0, 10.767, -0.139, 0, 11.1, 1.185, 0, 11.917, -2.885, 0, 12.4, 5.4, 0, 13.067, -5.118, 1, 13.211, -5.118, 13.356, -3.471, 13.5, 0, 1, 13.561, 1.469, 13.622, 2.298, 13.683, 2.298, 0, 14.117, -4.483, 0, 14.683, 4.579, 0, 15.167, -1.303, 0, 15.65, 7.109, 0, 16.067, -6.551, 0, 16.417, 5.205, 0, 16.9, -5.061, 0, 17.567, 5.053, 0, 18, -1.329, 0, 18.317, 1.435, 0, 18.667, -1.934, 0, 18.917, 2.782, 0, 19.25, -3.028, 0, 19.833, 8.018, 0, 20.2, 0.08, 0, 20.533, 4.926, 0, 21.2, -1.575, 0, 22.583, 4.15, 0, 23.233, -1.113, 0, 24.7, 2.773, 0, 25.233, -2.583, 0, 25.717, 13.075, 0, 26.2, -6.803, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param69", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.01, 0, 0.183, 1.173, 0, 0.617, -2.6, 0, 1.183, 1.77, 0, 1.667, 1.068, 0, 2.15, 3.157, 0, 2.567, -4.971, 0, 2.917, 2.456, 0, 3.4, -0.6, 0, 4.067, 2.4, 0, 4.5, 1.4, 0, 4.817, 4.675, 0, 5.167, 2.699, 0, 5.417, 5.465, 0, 5.75, -2.6, 0, 6.333, 4.73, 0, 6.7, -0.139, 0, 7.033, 1.185, 0, 8.4, -1.604, 0, 8.883, 1.675, 0, 9.233, 1.06, 0, 9.483, 2.465, 0, 9.817, -1.157, 0, 10.4, 4.73, 0, 10.767, -0.139, 0, 11.1, 1.185, 0, 11.917, -2.885, 0, 12.4, 5.4, 0, 13.067, -5.118, 1, 13.211, -5.118, 13.356, -3.471, 13.5, 0, 1, 13.561, 1.469, 13.622, 2.298, 13.683, 2.298, 0, 14.117, -4.483, 0, 14.683, 4.579, 0, 15.167, -1.303, 0, 15.65, 7.109, 0, 16.067, -6.551, 0, 16.417, 5.205, 0, 16.9, -5.061, 0, 17.567, 5.053, 0, 18, -1.329, 0, 18.317, 1.435, 0, 18.667, -1.934, 0, 18.917, 2.782, 0, 19.25, -3.028, 0, 19.833, 8.018, 0, 20.2, 0.08, 0, 20.533, 4.926, 0, 21.2, -1.575, 0, 22.583, 4.15, 0, 23.233, -1.113, 0, 24.7, 2.773, 0, 25.233, -2.583, 0, 25.717, 13.075, 0, 26.2, -6.803, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param153", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.014, 0, 0.167, -5.172, 0, 0.5, 9.667, 0, 0.933, -11.131, 0, 1.383, 5.659, 0, 1.95, -5.486, 0, 2.433, 21.07, 0, 2.817, -28.993, 0, 3.217, 16.589, 0, 3.667, -9.165, 0, 4.267, 4.258, 0, 4.733, -11.11, 0, 5.083, 9.644, 0, 5.383, -11.45, 0, 5.683, 24.746, 0, 6.067, -21.029, 0, 6.567, 19.793, 0, 6.95, -11.35, 0, 7.383, 4.7, 0, 7.833, 0.437, 0, 8.1, 0.862, 0, 8.717, -7.005, 0, 9.117, 4.874, 0, 9.45, -6.016, 0, 9.75, 11.51, 0, 10.15, -14.156, 0, 10.65, 17.747, 0, 11.017, -10.743, 0, 11.467, 7.721, 0, 12.217, -17.699, 0, 12.7, 20.788, 0, 13.317, -17.231, 0, 13.5, 0, 0, 13.667, -6.225, 0, 14, 10.479, 0, 14.433, -12.79, 0, 14.933, 11.008, 0, 15.45, -13.354, 0, 15.917, 23.195, 0, 16.317, -27.6, 0, 16.717, 25.425, 0, 17.183, -18.849, 0, 17.817, 14.313, 0, 18.233, -20.799, 0, 18.6, 15.582, 0, 18.883, -17.426, 0, 19.183, 23.423, 0, 19.583, -19.971, 0, 20.067, 18.762, 0, 20.45, -14.966, 0, 20.867, 10.141, 0, 21.367, -4.225, 0, 21.783, -0.868, 0, 22.117, -1.489, 0, 22.933, 4.656, 0, 23.433, -2.574, 0, 23.783, -0.638, 0, 24.083, -1.116, 0, 25.033, 4.394, 0, 25.533, -10.071, 0, 26, 14.445, 0, 26.417, -11.565, 0, 26.5, -9.821]}, {"Target": "Parameter", "Id": "Param154", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.009, 0, 0.167, 3.502, 0, 0.45, -7.046, 0, 0.783, 9.465, 0, 1.217, -8.601, 0, 1.633, 5.447, 0, 2.333, -9.173, 0, 2.717, 21.394, 0, 3.067, -24.082, 0, 3.483, 16.4, 0, 3.917, -8.498, 0, 4.45, 3.689, 0, 4.517, 3.583, 0, 4.65, 5.135, 0, 4.983, -10.202, 0, 5.333, 12.633, 0, 5.633, -17.635, 0, 5.95, 19.887, 0, 6.383, -16.985, 0, 6.817, 17.959, 0, 7.2, -11.803, 0, 7.617, 5.255, 0, 8.05, -1.827, 0, 8.6, 2.874, 0, 8.983, -5.195, 0, 9.383, 6.449, 0, 9.7, -9.143, 0, 10.017, 11.442, 0, 10.483, -11.82, 0, 10.883, 15.375, 0, 11.283, -11.518, 0, 11.717, 6.122, 0, 11.95, 1.895, 0, 12.133, 4.116, 0, 12.517, -13.549, 0, 12.983, 11.812, 0, 13.5, 0, 0, 13.65, 3.979, 0, 13.933, -9.033, 0, 14.3, 10.324, 0, 14.733, -9.958, 0, 15.233, 9.308, 0, 15.783, -13.248, 0, 16.2, 22.727, 0, 16.583, -26.553, 0, 16.983, 23.186, 0, 17.433, -14.012, 0, 18.117, 13.263, 0, 18.483, -19.246, 0, 18.833, 20.609, 0, 19.133, -20.57, 0, 19.467, 19.39, 0, 19.9, -16.594, 0, 20.333, 18.4, 0, 20.717, -15.08, 0, 21.133, 8.874, 0, 21.583, -4.256, 0, 22, 1.602, 0, 22.45, -0.884, 0, 22.617, -0.643, 0, 22.8, -1.19, 0, 23.267, 2.673, 0, 23.65, -1.793, 0, 24.017, 0.929, 0, 24.45, -0.47, 0, 24.7, -0.187, 0, 24.933, -1.397, 0, 25.4, 4.728, 0, 25.85, -9.085, 0, 26.283, 11.975, 0, 26.5, -1.346]}, {"Target": "Parameter", "Id": "Param155", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.15, 0, 0, 0.367, 1.528, 0, 0.65, -4.057, 0, 0.983, 7.874, 0, 1.383, -8.873, 0, 1.833, 6.858, 0, 2.433, -7.769, 0, 2.867, 18.088, 0, 3.267, -21.977, 0, 3.667, 18.049, 0, 4.117, -11.556, 0, 4.617, 6.194, 0, 5.15, -8.821, 0, 5.5, 9.813, 0, 5.817, -11.675, 0, 6.15, 15.92, 0, 6.567, -18.016, 0, 7, 18.115, 0, 7.4, -13.462, 0, 7.833, 7.535, 0, 8.25, -3.348, 0, 8.733, 3.009, 0, 9.15, -4.95, 0, 9.55, 5.677, 0, 9.883, -6.626, 0, 10.217, 9.303, 0, 10.65, -12.353, 0, 11.067, 14.653, 0, 11.483, -12.477, 0, 11.917, 8.234, 0, 12.667, -11.008, 0, 13.15, 12.98, 0, 13.5, 0, 2, 13.617, 0, 0, 13.867, 2.605, 0, 14.167, -3.991, 0, 14.483, 8.289, 0, 14.917, -10.266, 0, 15.383, 10.518, 0, 15.9, -13.129, 0, 16.35, 20.661, 0, 16.767, -24.982, 0, 17.183, 23.95, 0, 17.617, -17.677, 0, 18.2, 13.082, 0, 18.65, -17.482, 0, 19.017, 16.916, 0, 19.333, -14.463, 0, 19.683, 15.647, 0, 20.083, -17.351, 0, 20.5, 18.358, 0, 20.917, -15.858, 0, 21.333, 11.297, 0, 21.767, -6.301, 0, 22.2, 2.891, 0, 22.65, -1.533, 0, 23.4, 2.3, 0, 23.85, -2.09, 0, 24.25, 1.093, 0, 24.683, -0.679, 0, 24.867, -0.482, 0, 25.083, -0.82, 0, 25.55, 4.078, 0, 26, -8.342, 0, 26.45, 11.569, 0, 26.5, 10.689]}, {"Target": "Parameter", "Id": "Param156", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.176, 0, 0.217, 0, 2, 0.567, 0, 0, 0.8, -3.108, 0, 1.167, 6.817, 0, 1.583, -8.886, 0, 2.017, 8.014, 0, 2.55, -8.344, 0, 3.017, 16.048, 0, 3.433, -20.867, 0, 3.867, 19.308, 0, 4.3, -14.279, 0, 4.767, 9.143, 0, 5.3, -8.905, 0, 5.683, 8.491, 0, 6, -8.179, 0, 6.35, 12.839, 0, 6.75, -17.564, 0, 7.167, 18.574, 0, 7.6, -15.223, 0, 8.033, 9.843, 0, 8.45, -5.176, 0, 8.9, 3.831, 0, 9.317, -4.902, 0, 9.717, 5.222, 0, 10.067, -5.198, 0, 10.417, 7.784, 0, 10.817, -11.994, 0, 11.25, 14.462, 0, 11.667, -13.487, 0, 12.1, 10.252, 0, 12.8, -9.441, 0, 13.3, 13.27, 0, 13.5, -0.154, 0, 13.667, 0, 2, 14.083, 0, 0, 14.317, -3.003, 0, 14.667, 7.066, 0, 15.083, -10.166, 0, 15.55, 11.485, 0, 16.05, -13.86, 0, 16.5, 19.756, 0, 16.933, -24.197, 0, 17.35, 24.715, 0, 17.8, -20.616, 0, 18.317, 15.827, 0, 18.8, -17.211, 0, 19.2, 15.436, 0, 19.533, -11.357, 0, 19.883, 12.654, 0, 20.267, -16.543, 0, 20.683, 18.249, 0, 21.1, -16.849, 0, 21.517, 13.295, 0, 21.967, -8.486, 0, 22.4, 4.511, 0, 22.85, -2.537, 0, 23.533, 2.082, 0, 23.983, -2.091, 0, 24.433, 1.413, 0, 24.883, -0.956, 0, 25.7, 3.208, 0, 26.167, -7.677, 0, 26.5, 8.089]}, {"Target": "Parameter", "Id": "Param82", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.013, 0, 0.167, -4.822, 0, 0.5, 9.012, 0, 0.933, -10.376, 0, 1.383, 5.276, 0, 1.95, -5.114, 0, 2.433, 19.642, 0, 2.817, -27.028, 0, 3.217, 15.464, 0, 3.667, -8.543, 0, 4.267, 3.969, 0, 4.733, -10.357, 0, 5.083, 8.99, 0, 5.383, -10.674, 0, 5.683, 23.069, 0, 6.067, -19.604, 0, 6.567, 18.451, 0, 6.95, -10.581, 0, 7.383, 4.382, 0, 7.833, 0.408, 0, 8.1, 0.804, 0, 8.717, -6.53, 0, 9.117, 4.544, 0, 9.45, -5.608, 0, 9.75, 10.73, 0, 10.15, -13.197, 0, 10.65, 16.544, 0, 11.017, -10.015, 0, 11.467, 7.197, 0, 12.217, -16.5, 0, 12.7, 19.38, 0, 13.317, -16.063, 0, 13.5, 0, 0, 13.667, -5.803, 0, 14, 9.769, 0, 14.433, -11.923, 0, 14.933, 10.262, 0, 15.45, -12.449, 0, 15.917, 21.623, 0, 16.317, -25.73, 0, 16.717, 23.702, 0, 17.183, -17.571, 0, 17.817, 13.343, 0, 18.233, -19.389, 0, 18.6, 14.526, 0, 18.883, -16.245, 0, 19.183, 21.836, 0, 19.583, -18.617, 0, 20.067, 17.491, 0, 20.45, -13.952, 0, 20.867, 9.454, 0, 21.367, -3.938, 0, 21.783, -0.809, 0, 22.117, -1.388, 0, 22.933, 4.34, 0, 23.433, -2.4, 0, 23.783, -0.594, 0, 24.083, -1.04, 0, 25.033, 4.096, 0, 25.533, -9.388, 0, 26, 13.466, 0, 26.417, -10.781, 0, 26.5, -9.155]}, {"Target": "Parameter", "Id": "Param83", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.009, 0, 0.167, 3.355, 0, 0.45, -6.751, 0, 0.783, 9.068, 0, 1.217, -8.24, 0, 1.633, 5.219, 0, 2.333, -8.789, 0, 2.717, 20.497, 0, 3.067, -23.072, 0, 3.483, 15.713, 0, 3.917, -8.141, 0, 4.45, 3.535, 0, 4.517, 3.433, 0, 4.65, 4.92, 0, 4.983, -9.774, 0, 5.333, 12.104, 0, 5.633, -16.895, 0, 5.95, 19.053, 0, 6.383, -16.273, 0, 6.817, 17.206, 0, 7.2, -11.308, 0, 7.617, 5.034, 0, 8.05, -1.75, 0, 8.6, 2.754, 0, 8.983, -4.978, 0, 9.383, 6.178, 0, 9.7, -8.759, 0, 10.017, 10.962, 0, 10.483, -11.325, 0, 10.883, 14.73, 0, 11.283, -11.035, 0, 11.717, 5.865, 0, 11.95, 1.816, 0, 12.133, 3.944, 0, 12.517, -12.98, 0, 12.983, 11.316, 0, 13.5, 0, 0, 13.65, 3.812, 0, 13.933, -8.654, 0, 14.3, 9.891, 0, 14.733, -9.541, 0, 15.233, 8.918, 0, 15.783, -12.693, 0, 16.2, 21.774, 0, 16.583, -25.44, 0, 16.983, 22.214, 0, 17.433, -13.424, 0, 18.117, 12.707, 0, 18.483, -18.439, 0, 18.833, 19.744, 0, 19.133, -19.708, 0, 19.467, 18.577, 0, 19.9, -15.898, 0, 20.333, 17.628, 0, 20.717, -14.448, 0, 21.133, 8.502, 0, 21.583, -4.077, 0, 22, 1.535, 0, 22.45, -0.847, 0, 22.617, -0.616, 0, 22.8, -1.14, 0, 23.267, 2.561, 0, 23.65, -1.718, 0, 24.017, 0.89, 0, 24.45, -0.451, 0, 24.7, -0.18, 0, 24.933, -1.338, 0, 25.4, 4.53, 0, 25.85, -8.704, 0, 26.283, 11.473, 0, 26.5, -1.289]}, {"Target": "Parameter", "Id": "Param84", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.15, 0, 0, 0.367, 1.562, 0, 0.65, -4.147, 0, 0.983, 8.05, 0, 1.383, -9.07, 0, 1.833, 7.01, 0, 2.433, -7.941, 0, 2.867, 18.49, 0, 3.267, -22.466, 0, 3.667, 18.451, 0, 4.117, -11.813, 0, 4.617, 6.331, 0, 5.15, -9.017, 0, 5.5, 10.031, 0, 5.817, -11.934, 0, 6.15, 16.274, 0, 6.567, -18.417, 0, 7, 18.517, 0, 7.4, -13.761, 0, 7.833, 7.702, 0, 8.25, -3.422, 0, 8.733, 3.075, 0, 9.15, -5.06, 0, 9.55, 5.803, 0, 9.883, -6.773, 0, 10.217, 9.51, 0, 10.65, -12.628, 0, 11.067, 14.978, 0, 11.483, -12.754, 0, 11.917, 8.417, 0, 12.667, -11.253, 0, 13.15, 13.269, 0, 13.5, 0, 2, 13.617, 0, 0, 13.867, 2.663, 0, 14.167, -4.08, 0, 14.483, 8.473, 0, 14.917, -10.494, 0, 15.383, 10.752, 0, 15.9, -13.421, 0, 16.35, 21.12, 0, 16.767, -25.537, 0, 17.183, 24.482, 0, 17.617, -18.07, 0, 18.2, 13.373, 0, 18.65, -17.87, 0, 19.017, 17.292, 0, 19.333, -14.785, 0, 19.683, 15.994, 0, 20.083, -17.736, 0, 20.5, 18.766, 0, 20.917, -16.211, 0, 21.333, 11.548, 0, 21.767, -6.441, 0, 22.2, 2.955, 0, 22.65, -1.567, 0, 23.4, 2.352, 0, 23.85, -2.137, 0, 24.25, 1.118, 0, 24.683, -0.694, 0, 24.867, -0.493, 0, 25.083, -0.838, 0, 25.55, 4.168, 0, 26, -8.527, 0, 26.45, 11.827, 0, 26.5, 10.926]}, {"Target": "Parameter", "Id": "Param85", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.179, 0, 0.217, 0, 2, 0.567, 0, 0, 0.8, -3.169, 0, 1.167, 6.951, 0, 1.583, -9.06, 0, 2.017, 8.171, 0, 2.55, -8.507, 0, 3.017, 16.362, 0, 3.433, -21.275, 0, 3.867, 19.686, 0, 4.3, -14.558, 0, 4.767, 9.322, 0, 5.3, -9.08, 0, 5.683, 8.657, 0, 6, -8.339, 0, 6.35, 13.09, 0, 6.75, -17.908, 0, 7.167, 18.938, 0, 7.6, -15.521, 0, 8.033, 10.035, 0, 8.45, -5.277, 0, 8.9, 3.906, 0, 9.317, -4.998, 0, 9.717, 5.324, 0, 10.067, -5.3, 0, 10.417, 7.936, 0, 10.817, -12.229, 0, 11.25, 14.745, 0, 11.667, -13.751, 0, 12.1, 10.452, 0, 12.8, -9.626, 0, 13.3, 13.53, 0, 13.5, -0.157, 0, 13.667, 0, 2, 14.083, 0, 0, 14.317, -3.062, 0, 14.667, 7.204, 0, 15.083, -10.365, 0, 15.55, 11.71, 0, 16.05, -14.131, 0, 16.5, 20.143, 0, 16.933, -24.671, 0, 17.35, 25.198, 0, 17.8, -21.019, 0, 18.317, 16.137, 0, 18.8, -17.548, 0, 19.2, 15.738, 0, 19.533, -11.579, 0, 19.883, 12.901, 0, 20.267, -16.866, 0, 20.683, 18.606, 0, 21.1, -17.179, 0, 21.517, 13.555, 0, 21.967, -8.652, 0, 22.4, 4.599, 0, 22.85, -2.586, 0, 23.533, 2.123, 0, 23.983, -2.132, 0, 24.433, 1.44, 0, 24.883, -0.974, 0, 25.7, 3.271, 0, 26.167, -7.827, 0, 26.5, 8.247]}, {"Target": "Parameter", "Id": "Param66", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.45, 0, 0, 0.633, -6.65, 0, 0.883, 10.034, 0, 1.2, -7.973, 0, 1.567, 4.543, 0, 2.017, -3.735, 0, 2.233, -1.308, 0, 2.433, -4.537, 0, 2.767, 14.03, 0, 3.1, -19.688, 0, 3.433, 15.37, 0, 3.783, -7.77, 0, 4.117, 2.361, 0, 4.383, -0.733, 0, 4.75, 3.236, 0, 5.05, -7.353, 0, 5.383, 10.016, 0, 5.683, -16.447, 0, 5.983, 21.318, 0, 6.3, -16.451, 0, 6.817, 7.91, 0, 7.183, -8.482, 0, 7.517, 5.312, 0, 7.833, -2.325, 0, 8.167, 0.697, 0, 8.45, 0.132, 0, 8.7, 1.453, 0, 9.017, -3.752, 0, 9.4, 4.073, 0, 9.733, -7.339, 0, 10.05, 10.723, 0, 10.383, -8.847, 0, 10.917, 8.225, 0, 11.267, -8.439, 0, 11.617, 5.08, 0, 11.917, -1.733, 0, 12.217, 4.497, 0, 12.567, -9.701, 0, 12.917, 7.873, 0, 13.5, 0, 2, 13.6, 0, 0, 13.767, 6.231, 0, 14, -12.682, 0, 14.317, 15.479, 0, 14.667, -12.498, 0, 15.15, 6.079, 0, 15.8, -10.755, 0, 16.233, 23.165, 0, 16.55, -30, 2, 16.633, -30, 0, 16.95, 27.527, 0, 17.317, -14.811, 0, 17.65, 3.162, 0, 17.867, -2.182, 0, 18.167, 7.711, 0, 18.517, -11.783, 0, 18.867, 15.159, 0, 19.183, -20.63, 0, 19.483, 22.911, 0, 19.833, -17.607, 0, 20.367, 14.601, 0, 20.717, -18.555, 0, 21.05, 12.909, 0, 21.417, -4.621, 0, 21.817, 1.618, 0, 22.2, -0.768, 0, 22.533, -0.032, 0, 22.883, -1.535, 0, 23.267, 2.82, 0, 23.633, -2.304, 0, 23.95, 0.918, 0, 24.433, -0.323, 0, 24.733, -0.106, 0, 25.017, -2.21, 0, 25.467, 8.102, 0, 25.867, -18.855, 0, 26.283, 24.803, 0, 26.5, -6.319]}, {"Target": "Parameter", "Id": "Param67", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.071, 0, 0.15, -7.281, 0, 0.4, 9.99, 0, 0.7, -9.2, 0, 1.033, 1.68, 0, 1.133, 1.483, 0, 1.233, 2.106, 0, 1.883, -2.68, 0, 2.35, 13.499, 0, 2.7, -21.81, 0, 3, 19.942, 0, 3.283, -8.473, 0, 3.567, 0.927, 0, 3.8, -2.228, 0, 4.133, 2.53, 0, 4.683, -7.588, 0, 4.95, 10.438, 0, 5.3, -12.303, 0, 5.583, 25.64, 0, 5.85, -25.302, 0, 6.15, 7.386, 0, 6.333, 0.975, 0, 6.533, 9.113, 0, 6.8, -10.747, 0, 7.1, 6.64, 0, 7.35, -2.448, 0, 7.617, 1.974, 0, 7.9, -0.357, 0, 8.133, 0.416, 0, 8.617, -4.366, 0, 8.95, 4.491, 0, 9.367, -4.676, 0, 9.65, 11.383, 0, 9.933, -12.795, 0, 10.25, 2.924, 0, 10.367, 2.288, 0, 10.583, 9.192, 0, 10.867, -11.136, 0, 11.167, 7.048, 0, 11.433, -0.85, 0, 11.667, 1.669, 0, 12.117, -11.031, 0, 12.5, 12.601, 0, 12.85, 0.502, 0, 12.867, 0.508, 0, 13.217, -9.186, 0, 13.5, 0, 0, 13.633, -10.118, 0, 13.883, 17.571, 0, 14.2, -16.009, 0, 14.833, 8.212, 0, 15.317, -11.681, 0, 15.817, 22.918, 0, 16.15, -30, 2, 16.233, -30, 1, 16.311, -30, 16.389, 30, 16.467, 30, 2, 16.55, 30, 0, 16.85, -11.697, 0, 17.217, -2.98, 0, 17.283, -3.073, 0, 17.717, 10.87, 0, 18.1, -12.001, 0, 18.433, 12.51, 0, 18.8, -19.18, 0, 19.067, 26.887, 0, 19.367, -24.759, 0, 19.683, 5.423, 0, 19.8, 3.713, 0, 20.017, 15.17, 0, 20.317, -21.793, 0, 20.617, 17.599, 0, 20.9, -3.898, 0, 21.117, 1.673, 0, 21.333, -3.329, 0, 21.583, 0.013, 0, 21.817, -2.308, 0, 22.117, -0.044, 0, 22.3, -0.437, 0, 22.817, 4.427, 0, 23.267, -2.955, 0, 23.533, 0.495, 0, 23.8, -1.269, 0, 24.1, -0.248, 0, 24.267, -0.418, 0, 24.933, 6.251, 0, 25.433, -20.895, 0, 25.85, 29.545, 0, 26.283, -27.108, 0, 26.5, 12.415]}, {"Target": "Parameter", "Id": "Param68", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.041, 0, 0.15, 4, 0, 0.333, -7.35, 0, 0.583, 8.604, 0, 0.85, -7.014, 0, 1.133, 2.589, 0, 1.35, -0.352, 0, 1.533, 0.259, 0, 1.667, 0.061, 0, 1.8, 0.49, 0, 2.067, -1.762, 0, 2.167, -1.314, 0, 2.267, -2.561, 0, 2.533, 9.75, 0, 2.883, -15.336, 0, 3.15, 15.574, 0, 3.433, -9.128, 0, 3.7, 4.452, 0, 3.967, -3.185, 0, 4.267, 1.963, 0, 4.517, -0.272, 0, 4.633, 1.364, 0, 4.867, -6.611, 0, 5.117, 8.16, 0, 5.483, -12.56, 0, 5.75, 21.876, 0, 6.017, -20.264, 0, 6.283, 10.343, 0, 6.5, -5.9, 0, 6.733, 8.288, 0, 6.983, -8.61, 0, 7.25, 6.627, 0, 7.5, -3.995, 0, 7.767, 2.541, 0, 8.033, -1.111, 0, 8.3, 0.785, 0, 8.433, 0.337, 0, 8.533, 0.733, 0, 8.817, -3.26, 0, 9.1, 3.288, 0, 9.567, -4.677, 0, 9.833, 9.559, 0, 10.1, -9.337, 0, 10.367, 3.791, 0, 10.55, -2.639, 0, 10.783, 7.547, 0, 11.033, -8.54, 0, 11.317, 6.308, 0, 11.567, -3.119, 0, 11.833, 2.508, 0, 11.95, 1.389, 0, 12.033, 1.663, 0, 12.333, -6.693, 0, 12.65, 6.785, 0, 12.9, -1.482, 0, 13.133, 3.11, 0, 13.417, -5.963, 1, 13.445, -5.963, 13.472, -2.579, 13.5, 0, 1, 13.539, 3.61, 13.578, 4.412, 13.617, 4.412, 0, 13.8, -11.236, 0, 14.05, 14.175, 0, 14.35, -11.203, 0, 14.617, 3.276, 0, 14.817, -1.699, 0, 15.067, 4.465, 0, 15.5, -5.967, 0, 16.017, 14.326, 0, 16.367, -22.836, 0, 16.667, 23.398, 0, 16.95, -10.232, 0, 17.233, 2.758, 0, 17.517, -3.99, 0, 17.9, 6.599, 0, 18.267, -8, 0, 18.583, 8.951, 0, 18.967, -17.468, 0, 19.233, 22.591, 0, 19.517, -19.414, 0, 19.783, 7.993, 0, 19.983, -5.223, 0, 20.217, 12.657, 0, 20.5, -16.614, 0, 20.767, 14.346, 0, 21.033, -7.79, 0, 21.267, 5.037, 0, 21.517, -3.068, 0, 21.767, 2.541, 0, 22.017, -1.968, 0, 22.267, 0.566, 0, 22.517, -0.69, 0, 22.617, -0.496, 0, 22.7, -0.687, 0, 22.983, 2.305, 0, 23.417, -1.441, 0, 23.683, 1.028, 0, 23.933, -0.581, 0, 24.2, 0.071, 0, 24.467, -0.367, 0, 24.7, -0.005, 0, 24.85, -1.435, 0, 25.133, 3.383, 0, 25.25, 1.991, 0, 25.35, 2.95, 0, 25.617, -12.711, 0, 26.017, 14.834, 0, 26.467, -16.007, 0, 26.5, -14.559]}, {"Target": "Parameter", "Id": "Param62", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.45, 0, 0, 0.633, 6.65, 0, 0.883, -10.034, 0, 1.2, 7.973, 0, 1.567, -4.543, 0, 2.017, 3.735, 0, 2.233, 1.308, 0, 2.433, 4.537, 0, 2.767, -14.03, 0, 3.1, 19.688, 0, 3.433, -15.37, 0, 3.783, 7.77, 0, 4.117, -2.361, 0, 4.383, 0.733, 0, 4.75, -3.236, 0, 5.05, 7.353, 0, 5.383, -10.016, 0, 5.683, 16.447, 0, 5.983, -21.318, 0, 6.3, 16.451, 0, 6.817, -7.91, 0, 7.183, 8.482, 0, 7.517, -5.312, 0, 7.833, 2.325, 0, 8.167, -0.697, 0, 8.45, -0.132, 0, 8.7, -1.453, 0, 9.017, 3.752, 0, 9.4, -4.073, 0, 9.733, 7.339, 0, 10.05, -10.723, 0, 10.383, 8.847, 0, 10.917, -8.225, 0, 11.267, 8.439, 0, 11.617, -5.08, 0, 11.917, 1.733, 0, 12.217, -4.497, 0, 12.567, 9.701, 0, 12.917, -7.873, 0, 13.5, 0, 2, 13.6, 0, 0, 13.767, -6.231, 0, 14, 12.682, 0, 14.317, -15.479, 0, 14.667, 12.498, 0, 15.15, -6.079, 0, 15.8, 10.755, 0, 16.233, -23.165, 0, 16.55, 30, 2, 16.633, 30, 0, 16.95, -27.527, 0, 17.317, 14.811, 0, 17.65, -3.162, 0, 17.867, 2.182, 0, 18.167, -7.711, 0, 18.517, 11.783, 0, 18.867, -15.159, 0, 19.183, 20.63, 0, 19.483, -22.911, 0, 19.833, 17.607, 0, 20.367, -14.601, 0, 20.717, 18.555, 0, 21.05, -12.909, 0, 21.417, 4.621, 0, 21.817, -1.618, 0, 22.2, 0.768, 0, 22.533, 0.032, 0, 22.883, 1.535, 0, 23.267, -2.82, 0, 23.633, 2.304, 0, 23.95, -0.918, 0, 24.433, 0.323, 0, 24.733, 0.106, 0, 25.017, 2.21, 0, 25.467, -8.102, 0, 25.867, 18.855, 0, 26.283, -24.803, 0, 26.5, 6.319]}, {"Target": "Parameter", "Id": "Param63", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.071, 0, 0.15, 7.281, 0, 0.4, -9.99, 0, 0.7, 9.2, 0, 1.033, -1.68, 0, 1.133, -1.483, 0, 1.233, -2.106, 0, 1.883, 2.68, 0, 2.35, -13.499, 0, 2.7, 21.81, 0, 3, -19.942, 0, 3.283, 8.473, 0, 3.567, -0.927, 0, 3.8, 2.228, 0, 4.133, -2.53, 0, 4.683, 7.588, 0, 4.95, -10.438, 0, 5.3, 12.303, 0, 5.583, -25.64, 0, 5.85, 25.302, 0, 6.15, -7.386, 0, 6.333, -0.975, 0, 6.533, -9.113, 0, 6.8, 10.747, 0, 7.1, -6.64, 0, 7.35, 2.448, 0, 7.617, -1.974, 0, 7.9, 0.357, 0, 8.133, -0.416, 0, 8.617, 4.366, 0, 8.95, -4.491, 0, 9.367, 4.676, 0, 9.65, -11.383, 0, 9.933, 12.795, 0, 10.25, -2.924, 0, 10.367, -2.288, 0, 10.583, -9.192, 0, 10.867, 11.136, 0, 11.167, -7.048, 0, 11.433, 0.85, 0, 11.667, -1.669, 0, 12.117, 11.031, 0, 12.5, -12.601, 0, 12.85, -0.502, 0, 12.867, -0.508, 0, 13.217, 9.186, 0, 13.5, 0, 0, 13.633, 10.118, 0, 13.883, -17.571, 0, 14.2, 16.009, 0, 14.833, -8.212, 0, 15.317, 11.681, 0, 15.817, -22.918, 0, 16.15, 30, 2, 16.233, 30, 1, 16.311, 30, 16.389, -30, 16.467, -30, 2, 16.55, -30, 0, 16.85, 11.697, 0, 17.217, 2.98, 0, 17.283, 3.073, 0, 17.717, -10.87, 0, 18.1, 12.001, 0, 18.433, -12.51, 0, 18.8, 19.18, 0, 19.067, -26.887, 0, 19.367, 24.759, 0, 19.683, -5.423, 0, 19.8, -3.713, 0, 20.017, -15.17, 0, 20.317, 21.793, 0, 20.617, -17.599, 0, 20.9, 3.898, 0, 21.117, -1.673, 0, 21.333, 3.329, 0, 21.583, -0.013, 0, 21.817, 2.308, 0, 22.117, 0.044, 0, 22.3, 0.437, 0, 22.817, -4.427, 0, 23.267, 2.955, 0, 23.533, -0.495, 0, 23.8, 1.269, 0, 24.1, 0.248, 0, 24.267, 0.418, 0, 24.933, -6.251, 0, 25.433, 20.895, 0, 25.85, -29.545, 0, 26.283, 27.108, 0, 26.5, -12.415]}, {"Target": "Parameter", "Id": "Param64", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.041, 0, 0.15, -4, 0, 0.333, 7.35, 0, 0.583, -8.604, 0, 0.85, 7.014, 0, 1.133, -2.589, 0, 1.35, 0.352, 0, 1.533, -0.259, 0, 1.667, -0.061, 0, 1.8, -0.49, 0, 2.067, 1.762, 0, 2.167, 1.314, 0, 2.267, 2.561, 0, 2.533, -9.75, 0, 2.883, 15.336, 0, 3.15, -15.574, 0, 3.433, 9.128, 0, 3.7, -4.452, 0, 3.967, 3.185, 0, 4.267, -1.963, 0, 4.517, 0.272, 0, 4.633, -1.364, 0, 4.867, 6.611, 0, 5.117, -8.16, 0, 5.483, 12.56, 0, 5.75, -21.876, 0, 6.017, 20.264, 0, 6.283, -10.343, 0, 6.5, 5.9, 0, 6.733, -8.288, 0, 6.983, 8.61, 0, 7.25, -6.627, 0, 7.5, 3.995, 0, 7.767, -2.541, 0, 8.033, 1.111, 0, 8.3, -0.785, 0, 8.433, -0.337, 0, 8.533, -0.733, 0, 8.817, 3.26, 0, 9.1, -3.288, 0, 9.567, 4.677, 0, 9.833, -9.559, 0, 10.1, 9.337, 0, 10.367, -3.791, 0, 10.55, 2.639, 0, 10.783, -7.547, 0, 11.033, 8.54, 0, 11.317, -6.308, 0, 11.567, 3.119, 0, 11.833, -2.508, 0, 11.95, -1.389, 0, 12.033, -1.663, 0, 12.333, 6.693, 0, 12.65, -6.785, 0, 12.9, 1.482, 0, 13.133, -3.11, 0, 13.417, 5.963, 1, 13.445, 5.963, 13.472, 2.579, 13.5, 0, 1, 13.539, -3.61, 13.578, -4.412, 13.617, -4.412, 0, 13.8, 11.236, 0, 14.05, -14.175, 0, 14.35, 11.203, 0, 14.617, -3.276, 0, 14.817, 1.699, 0, 15.067, -4.465, 0, 15.5, 5.967, 0, 16.017, -14.326, 0, 16.367, 22.836, 0, 16.667, -23.398, 0, 16.95, 10.232, 0, 17.233, -2.758, 0, 17.517, 3.99, 0, 17.9, -6.599, 0, 18.267, 8, 0, 18.583, -8.951, 0, 18.967, 17.468, 0, 19.233, -22.591, 0, 19.517, 19.414, 0, 19.783, -7.993, 0, 19.983, 5.223, 0, 20.217, -12.657, 0, 20.5, 16.614, 0, 20.767, -14.346, 0, 21.033, 7.79, 0, 21.267, -5.037, 0, 21.517, 3.068, 0, 21.767, -2.541, 0, 22.017, 1.968, 0, 22.267, -0.566, 0, 22.517, 0.69, 0, 22.617, 0.496, 0, 22.7, 0.687, 0, 22.983, -2.305, 0, 23.417, 1.441, 0, 23.683, -1.028, 0, 23.933, 0.581, 0, 24.2, -0.071, 0, 24.467, 0.367, 0, 24.7, 0.005, 0, 24.85, 1.435, 0, 25.133, -3.383, 0, 25.25, -1.991, 0, 25.35, -2.95, 0, 25.617, 12.711, 0, 26.017, -14.834, 0, 26.467, 16.007, 0, 26.5, 14.559]}, {"Target": "Parameter", "Id": "Param86", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.004, 0, 0.183, -0.513, 0, 0.617, 1.137, 0, 1.183, -0.774, 0, 1.667, -0.467, 0, 2.15, -1.381, 0, 2.567, 2.174, 0, 2.917, -1.074, 0, 3.4, 0.262, 0, 4.067, -1.05, 0, 4.5, -0.612, 0, 4.817, -2.045, 0, 5.167, -1.181, 0, 5.417, -2.39, 0, 5.75, 1.137, 0, 6.333, -2.069, 0, 6.7, 0.061, 0, 7.033, -0.518, 0, 8.4, 0.702, 0, 8.883, -0.733, 0, 9.233, -0.464, 0, 9.483, -1.078, 0, 9.817, 0.506, 0, 10.4, -2.069, 0, 10.767, 0.061, 0, 11.1, -0.518, 0, 11.917, 1.262, 0, 12.4, -2.362, 0, 13.067, 2.239, 1, 13.211, 2.239, 13.356, 1.519, 13.5, 0, 1, 13.561, -0.642, 13.622, -1.005, 13.683, -1.005, 0, 14.117, 1.961, 0, 14.683, -2.003, 0, 15.167, 0.57, 0, 15.65, -3.11, 0, 16.067, 2.866, 0, 16.417, -2.277, 0, 16.9, 2.213, 0, 17.567, -2.21, 0, 18, 0.581, 0, 18.317, -0.628, 0, 18.667, 0.846, 0, 18.917, -1.217, 0, 19.25, 1.325, 0, 19.833, -3.507, 0, 20.2, -0.035, 0, 20.533, -2.155, 0, 21.2, 0.689, 0, 22.583, -1.815, 0, 23.233, 0.487, 0, 24.7, -1.213, 0, 25.233, 1.13, 0, 25.717, -5.721, 0, 26.2, 2.976, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param89", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.036, 0, 0.183, -4.188, 0, 0.567, 8.574, 0, 0.917, -6.397, 0, 1.317, 2.677, 0, 1.917, -2.399, 0, 2.4, 11.165, 0, 2.783, -15.523, 0, 3.117, 8.923, 0, 3.55, -3.972, 0, 4.283, 1.477, 0, 4.717, -5.991, 0, 5.033, 5.418, 0, 5.367, -7.455, 0, 5.65, 15.498, 0, 5.983, -11.526, 0, 6.533, 8.821, 0, 6.883, -5.653, 0, 7.233, 2.298, 0, 7.567, 0.324, 0, 7.667, 0.453, 0, 7.75, 0.265, 0, 8.017, 0.642, 0, 8.683, -3.709, 0, 9.033, 2.481, 0, 9.417, -3.612, 0, 9.717, 7.119, 0, 10.083, -7.373, 0, 10.617, 8.479, 0, 10.967, -5.142, 0, 11.367, 3.392, 0, 12.2, -9.413, 0, 12.65, 9.703, 0, 13.283, -7.638, 0, 13.5, 0, 0, 13.683, -8.207, 0, 14.017, 10.516, 0, 14.383, -11.388, 0, 14.883, 8.287, 0, 15.417, -10.799, 0, 15.883, 20.298, 0, 16.283, -25.275, 0, 16.633, 19.523, 0, 17.083, -10.959, 0, 17.8, 8.935, 0, 18.2, -8.026, 0, 18.533, 7.961, 0, 18.867, -12.298, 0, 19.15, 13.566, 0, 19.517, -13.745, 0, 20.05, 14.234, 0, 20.4, -13.251, 0, 20.767, 8.627, 0, 21.283, -2.6, 0, 21.583, -0.943, 0, 21.717, -1.247, 0, 21.817, -0.964, 0, 22.083, -1.465, 0, 22.9, 4.176, 0, 23.367, -1.929, 0, 23.683, -0.578, 0, 23.783, -0.708, 0, 23.867, -0.522, 0, 24.167, -0.974, 0, 25, 5.413, 0, 25.5, -18.352, 0, 25.967, 26.39, 0, 26.383, -21.295, 0, 26.5, -12.394]}, {"Target": "Parameter", "Id": "Param90", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.032, 0, 0.183, 3.757, 0, 0.55, -7.561, 0, 0.817, 10.918, 0, 1.15, -6.335, 0, 1.5, 3.27, 0, 2.317, -5.13, 0, 2.667, 12.263, 0, 2.983, -15.569, 0, 3.333, 9.609, 0, 3.733, -4.68, 0, 4.05, 1.105, 0, 4.283, -0.298, 0, 4.65, 3.251, 0, 4.933, -6.368, 0, 5.283, 7.894, 0, 5.567, -13.78, 0, 5.867, 16.437, 0, 6.2, -9.915, 0, 6.75, 7.593, 0, 7.083, -6.406, 0, 7.417, 2.986, 0, 7.667, -0.632, 0, 8.25, 0.298, 0, 8.4, 0.201, 0, 8.6, 1.501, 0, 8.9, -4.086, 0, 9.317, 3.14, 0, 9.633, -6.355, 0, 9.933, 8.175, 0, 10.283, -5.503, 0, 10.817, 7.18, 0, 11.183, -4.943, 0, 11.483, 2.332, 0, 11.833, 0.166, 0, 12.1, 3.826, 0, 12.45, -7.862, 0, 12.833, 5.04, 0, 13.5, 0, 0, 13.683, 7.363, 0, 13.983, -8.228, 0, 14.25, 13.056, 0, 14.6, -9.989, 0, 15.083, 5.312, 0, 15.767, -9.685, 0, 16.15, 20.127, 0, 16.5, -26.168, 0, 16.85, 17.984, 0, 17.233, -8.366, 0, 17.55, -0.015, 0, 17.733, -2.182, 0, 18.067, 7.281, 0, 18.417, -9.323, 0, 18.783, 12.143, 0, 19.067, -16.493, 0, 19.367, 16.596, 0, 19.733, -10.674, 0, 20.267, 13.527, 0, 20.617, -14.29, 0, 20.95, 7.612, 0, 21.4, -2.602, 0, 21.717, 1.023, 0, 21.883, -0.25, 0, 22.083, -0.025, 0, 22.4, -0.357, 0, 22.583, -0.281, 0, 22.783, -1.414, 0, 23.2, 2.181, 0, 23.533, -1.565, 0, 23.783, 0.404, 0, 23.917, 0.201, 0, 24.017, 0.228, 0, 24.4, -0.288, 0, 24.7, -0.116, 0, 24.9, -2.218, 0, 25.383, 7.686, 0, 25.8, -16.025, 0, 26.217, 20.542, 0, 26.5, -17.8]}, {"Target": "Parameter", "Id": "Param88", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.55, 0, 0, 0.75, 7.087, 0, 0.983, -10.832, 0, 1.283, 8.057, 0, 1.6, -4.728, 0, 1.917, 1.103, 0, 2.1, 0.381, 0, 2.433, 3.826, 0, 2.767, -10.732, 0, 3.1, 14.555, 0, 3.433, -11.171, 0, 3.783, 5.671, 0, 4.133, -2.112, 0, 4.417, 0.71, 0, 4.75, -2.504, 0, 5.05, 5.429, 0, 5.383, -7.361, 0, 5.683, 12.071, 0, 5.983, -15.684, 0, 6.3, 12.118, 0, 6.817, -5.804, 0, 7.183, 6.225, 0, 7.517, -3.903, 0, 7.817, 1.698, 0, 8.167, -0.507, 0, 8.45, -0.096, 0, 8.7, -1.067, 0, 8.717, -1.054, 0, 8.75, -1.178, 0, 9.05, 2.921, 0, 9.383, -3.322, 0, 9.733, 5.493, 0, 10.05, -7.953, 0, 10.383, 6.515, 0, 10.883, -5.468, 0, 11.267, 6.087, 0, 11.6, -3.595, 0, 11.917, 0.774, 0, 12.217, -3.032, 0, 12.567, 6.983, 0, 12.917, -5.719, 0, 13.5, 0, 2, 13.95, 0, 0, 14.133, 8.334, 0, 14.383, -13.033, 0, 14.717, 11.227, 0, 15.117, -6.018, 0, 15.833, 7.548, 0, 16.25, -17.196, 0, 16.6, 24.33, 0, 16.95, -20.488, 0, 17.317, 11.033, 0, 17.65, -2.388, 0, 17.883, 1.616, 0, 18.167, -5.621, 0, 18.517, 8.639, 0, 18.867, -11.146, 0, 19.183, 15.177, 0, 19.483, -16.88, 0, 19.833, 12.989, 0, 20.367, -10.746, 0, 20.717, 13.654, 0, 21.05, -9.213, 0, 21.45, 3.463, 0, 21.817, -1.715, 0, 22.1, 0.613, 0, 22.35, 0.036, 0, 22.9, 1.005, 0, 23.283, -1.933, 0, 23.65, 1.662, 0, 24.05, -0.247, 0, 24.483, 0.276, 0, 24.733, 0.07, 0, 25.017, 1.621, 0, 25.467, -5.793, 0, 25.883, 13.912, 0, 26.3, -18.806, 0, 26.5, 3.632]}, {"Target": "Parameter", "Id": "Param91", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.75, 0, 0, 0.917, -5.343, 0, 1.133, 9.047, 0, 1.417, -8.482, 0, 1.717, 6.022, 0, 2.033, -2.687, 0, 2.283, 0.082, 0, 2.533, -2.923, 0, 2.867, 9.235, 0, 3.2, -13.672, 0, 3.533, 12.38, 0, 3.883, -7.636, 0, 4.217, 3.428, 2, 4.233, 3.428, 0, 4.533, -1.406, 0, 4.85, 2.261, 0, 5.15, -4.729, 0, 5.483, 6.896, 0, 5.783, -10.818, 0, 6.1, 14.895, 0, 6.417, -13.574, 0, 6.783, 7.058, 0, 7.283, -6.09, 0, 7.617, 4.596, 0, 7.917, -2.87, 0, 8.25, 1.155, 0, 8.55, -0.184, 0, 8.817, 0.938, 0, 9.133, -2.669, 0, 9.483, 3.341, 0, 9.833, -4.633, 0, 10.167, 7.31, 0, 10.5, -7.142, 0, 10.967, 5.045, 0, 11.367, -5.48, 0, 11.717, 4.145, 0, 12.017, -1.624, 0, 12.317, 2.74, 0, 12.667, -6.29, 0, 13.017, 6.114, 0, 13.367, -1.379, 0, 13.5, 0, 2, 14.1, 0, 0, 14.267, -6.546, 0, 14.5, 11.543, 0, 14.833, -12.082, 0, 15.183, 7.668, 0, 15.9, -5.92, 0, 16.333, 14.756, 0, 16.7, -22.471, 0, 17.05, 21.779, 0, 17.4, -13.789, 0, 17.733, 5.022, 0, 18, -2.141, 0, 18.283, 5.544, 0, 18.617, -8.184, 0, 18.967, 10.569, 0, 19.283, -14.204, 0, 19.6, 16.849, 0, 19.933, -14.825, 0, 20.4, 9.017, 0, 20.817, -12.496, 0, 21.15, 10.446, 0, 21.517, -5.137, 0, 21.9, 2.012, 0, 22.217, -1, 0, 22.5, 0.198, 0, 22.983, -0.746, 0, 23.367, 1.787, 0, 23.75, -1.693, 0, 24.017, 0.857, 0, 24.483, -0.217, 0, 24.817, -0.052, 0, 25.117, -1.253, 0, 25.533, 4.662, 0, 25.967, -11.996, 0, 26.383, 17.327, 0, 26.5, 9.11]}, {"Target": "Parameter", "Id": "Param92", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.004, 0, 0.183, 0.513, 0, 0.617, -1.137, 0, 1.183, 0.774, 0, 1.667, 0.467, 0, 2.15, 1.381, 0, 2.567, -2.174, 0, 2.917, 1.074, 0, 3.4, -0.262, 0, 4.067, 1.05, 0, 4.5, 0.612, 0, 4.817, 2.045, 0, 5.167, 1.181, 0, 5.417, 2.39, 0, 5.75, -1.137, 0, 6.333, 2.069, 0, 6.7, -0.061, 0, 7.033, 0.518, 0, 8.4, -0.702, 0, 8.883, 0.733, 0, 9.233, 0.464, 0, 9.483, 1.078, 0, 9.817, -0.506, 0, 10.4, 2.069, 0, 10.767, -0.061, 0, 11.1, 0.518, 0, 11.917, -1.262, 0, 12.4, 2.362, 0, 13.067, -2.239, 1, 13.211, -2.239, 13.356, -1.519, 13.5, 0, 1, 13.561, 0.642, 13.622, 1.005, 13.683, 1.005, 0, 14.117, -1.961, 0, 14.683, 2.003, 0, 15.167, -0.57, 0, 15.65, 3.11, 0, 16.067, -2.866, 0, 16.417, 2.277, 0, 16.9, -2.213, 0, 17.567, 2.21, 0, 18, -0.581, 0, 18.317, 0.628, 0, 18.667, -0.846, 0, 18.917, 1.217, 0, 19.25, -1.325, 0, 19.833, 3.507, 0, 20.2, 0.035, 0, 20.533, 2.155, 0, 21.2, -0.689, 0, 22.583, 1.815, 0, 23.233, -0.487, 0, 24.7, 1.213, 0, 25.233, -1.13, 0, 25.717, 5.721, 0, 26.2, -2.976, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param93", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.036, 0, 0.183, -4.188, 0, 0.567, 8.574, 0, 0.917, -6.397, 0, 1.317, 2.677, 0, 1.917, -2.399, 0, 2.4, 11.165, 0, 2.783, -15.523, 0, 3.117, 8.923, 0, 3.55, -3.972, 0, 4.283, 1.477, 0, 4.717, -5.991, 0, 5.033, 5.418, 0, 5.367, -7.455, 0, 5.65, 15.498, 0, 5.983, -11.526, 0, 6.533, 8.821, 0, 6.883, -5.653, 0, 7.233, 2.298, 0, 7.567, 0.324, 0, 7.667, 0.453, 0, 7.75, 0.265, 0, 8.017, 0.642, 0, 8.683, -3.709, 0, 9.033, 2.481, 0, 9.417, -3.612, 0, 9.717, 7.119, 0, 10.083, -7.373, 0, 10.617, 8.479, 0, 10.967, -5.142, 0, 11.367, 3.392, 0, 12.2, -9.413, 0, 12.65, 9.703, 0, 13.283, -7.638, 0, 13.5, 0, 0, 13.683, -8.207, 0, 14.017, 10.516, 0, 14.383, -11.388, 0, 14.883, 8.287, 0, 15.417, -10.799, 0, 15.883, 20.298, 0, 16.283, -25.275, 0, 16.633, 19.523, 0, 17.083, -10.959, 0, 17.8, 8.935, 0, 18.2, -8.026, 0, 18.533, 7.961, 0, 18.867, -12.298, 0, 19.15, 13.566, 0, 19.517, -13.745, 0, 20.05, 14.234, 0, 20.4, -13.251, 0, 20.767, 8.627, 0, 21.283, -2.6, 0, 21.583, -0.943, 0, 21.717, -1.247, 0, 21.817, -0.964, 0, 22.083, -1.465, 0, 22.9, 4.176, 0, 23.367, -1.929, 0, 23.683, -0.578, 0, 23.783, -0.708, 0, 23.867, -0.522, 0, 24.167, -0.974, 0, 25, 5.413, 0, 25.5, -18.352, 0, 25.967, 26.39, 0, 26.383, -21.295, 0, 26.5, -12.394]}, {"Target": "Parameter", "Id": "Param126", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.032, 0, 0.183, 3.757, 0, 0.55, -7.561, 0, 0.817, 10.918, 0, 1.15, -6.335, 0, 1.5, 3.27, 0, 2.317, -5.13, 0, 2.667, 12.263, 0, 2.983, -15.569, 0, 3.333, 9.609, 0, 3.733, -4.68, 0, 4.05, 1.105, 0, 4.283, -0.298, 0, 4.65, 3.251, 0, 4.933, -6.368, 0, 5.283, 7.894, 0, 5.567, -13.78, 0, 5.867, 16.437, 0, 6.2, -9.915, 0, 6.75, 7.593, 0, 7.083, -6.406, 0, 7.417, 2.986, 0, 7.667, -0.632, 0, 8.25, 0.298, 0, 8.4, 0.201, 0, 8.6, 1.501, 0, 8.9, -4.086, 0, 9.317, 3.14, 0, 9.633, -6.355, 0, 9.933, 8.175, 0, 10.283, -5.503, 0, 10.817, 7.18, 0, 11.183, -4.943, 0, 11.483, 2.332, 0, 11.833, 0.166, 0, 12.1, 3.826, 0, 12.45, -7.862, 0, 12.833, 5.04, 0, 13.5, 0, 0, 13.683, 7.363, 0, 13.983, -8.228, 0, 14.25, 13.056, 0, 14.6, -9.989, 0, 15.083, 5.312, 0, 15.767, -9.685, 0, 16.15, 20.127, 0, 16.5, -26.168, 0, 16.85, 17.984, 0, 17.233, -8.366, 0, 17.55, -0.015, 0, 17.733, -2.182, 0, 18.067, 7.281, 0, 18.417, -9.323, 0, 18.783, 12.143, 0, 19.067, -16.493, 0, 19.367, 16.596, 0, 19.733, -10.674, 0, 20.267, 13.527, 0, 20.617, -14.29, 0, 20.95, 7.612, 0, 21.4, -2.602, 0, 21.717, 1.023, 0, 21.883, -0.25, 0, 22.083, -0.025, 0, 22.4, -0.357, 0, 22.583, -0.281, 0, 22.783, -1.414, 0, 23.2, 2.181, 0, 23.533, -1.565, 0, 23.783, 0.404, 0, 23.917, 0.201, 0, 24.017, 0.228, 0, 24.4, -0.288, 0, 24.7, -0.116, 0, 24.9, -2.218, 0, 25.383, 7.686, 0, 25.8, -16.025, 0, 26.217, 20.542, 0, 26.5, -17.8]}, {"Target": "Parameter", "Id": "Param128", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.55, 0, 0, 0.75, 7.087, 0, 0.983, -10.832, 0, 1.283, 8.057, 0, 1.6, -4.728, 0, 1.917, 1.103, 0, 2.1, 0.381, 0, 2.433, 3.826, 0, 2.767, -10.732, 0, 3.1, 14.555, 0, 3.433, -11.171, 0, 3.783, 5.671, 0, 4.133, -2.112, 0, 4.417, 0.71, 0, 4.75, -2.504, 0, 5.05, 5.429, 0, 5.383, -7.361, 0, 5.683, 12.071, 0, 5.983, -15.684, 0, 6.3, 12.118, 0, 6.817, -5.804, 0, 7.183, 6.225, 0, 7.517, -3.903, 0, 7.817, 1.698, 0, 8.167, -0.507, 0, 8.45, -0.096, 0, 8.7, -1.067, 0, 8.717, -1.054, 0, 8.75, -1.178, 0, 9.05, 2.921, 0, 9.383, -3.322, 0, 9.733, 5.493, 0, 10.05, -7.953, 0, 10.383, 6.515, 0, 10.883, -5.468, 0, 11.267, 6.087, 0, 11.6, -3.595, 0, 11.917, 0.774, 0, 12.217, -3.032, 0, 12.567, 6.983, 0, 12.917, -5.719, 0, 13.5, 0, 2, 13.95, 0, 0, 14.133, 8.334, 0, 14.383, -13.033, 0, 14.717, 11.227, 0, 15.117, -6.018, 0, 15.833, 7.548, 0, 16.25, -17.196, 0, 16.6, 24.33, 0, 16.95, -20.488, 0, 17.317, 11.033, 0, 17.65, -2.388, 0, 17.883, 1.616, 0, 18.167, -5.621, 0, 18.517, 8.639, 0, 18.867, -11.146, 0, 19.183, 15.177, 0, 19.483, -16.88, 0, 19.833, 12.989, 0, 20.367, -10.746, 0, 20.717, 13.654, 0, 21.05, -9.213, 0, 21.45, 3.463, 0, 21.817, -1.715, 0, 22.1, 0.613, 0, 22.35, 0.036, 0, 22.9, 1.005, 0, 23.283, -1.933, 0, 23.65, 1.662, 0, 24.05, -0.247, 0, 24.483, 0.276, 0, 24.733, 0.07, 0, 25.017, 1.621, 0, 25.467, -5.793, 0, 25.883, 13.912, 0, 26.3, -18.806, 0, 26.5, 3.632]}, {"Target": "Parameter", "Id": "Param129", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.75, 0, 0, 0.917, -5.343, 0, 1.133, 9.047, 0, 1.417, -8.482, 0, 1.717, 6.022, 0, 2.033, -2.687, 0, 2.283, 0.082, 0, 2.533, -2.923, 0, 2.867, 9.235, 0, 3.2, -13.672, 0, 3.533, 12.38, 0, 3.883, -7.636, 0, 4.217, 3.428, 2, 4.233, 3.428, 0, 4.533, -1.406, 0, 4.85, 2.261, 0, 5.15, -4.729, 0, 5.483, 6.896, 0, 5.783, -10.818, 0, 6.1, 14.895, 0, 6.417, -13.574, 0, 6.783, 7.058, 0, 7.283, -6.09, 0, 7.617, 4.596, 0, 7.917, -2.87, 0, 8.25, 1.155, 0, 8.55, -0.184, 0, 8.817, 0.938, 0, 9.133, -2.669, 0, 9.483, 3.341, 0, 9.833, -4.633, 0, 10.167, 7.31, 0, 10.5, -7.142, 0, 10.967, 5.045, 0, 11.367, -5.48, 0, 11.717, 4.145, 0, 12.017, -1.624, 0, 12.317, 2.74, 0, 12.667, -6.29, 0, 13.017, 6.114, 0, 13.367, -1.379, 0, 13.5, 0, 2, 14.1, 0, 0, 14.267, -6.546, 0, 14.5, 11.543, 0, 14.833, -12.082, 0, 15.183, 7.668, 0, 15.9, -5.92, 0, 16.333, 14.756, 0, 16.7, -22.471, 0, 17.05, 21.779, 0, 17.4, -13.789, 0, 17.733, 5.022, 0, 18, -2.141, 0, 18.283, 5.544, 0, 18.617, -8.184, 0, 18.967, 10.569, 0, 19.283, -14.204, 0, 19.6, 16.849, 0, 19.933, -14.825, 0, 20.4, 9.017, 0, 20.817, -12.496, 0, 21.15, 10.446, 0, 21.517, -5.137, 0, 21.9, 2.012, 0, 22.217, -1, 0, 22.5, 0.198, 0, 22.983, -0.746, 0, 23.367, 1.787, 0, 23.75, -1.693, 0, 24.017, 0.857, 0, 24.483, -0.217, 0, 24.817, -0.052, 0, 25.117, -1.253, 0, 25.533, 4.662, 0, 25.967, -11.996, 0, 26.383, 17.327, 0, 26.5, 9.11]}, {"Target": "Parameter", "Id": "Param16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 5.267, 0.223, 0, 6.6, -0.46, 0, 10.317, 0.033, 0, 13.05, -0.522, 1, 15.367, -0.522, 17.683, -0.338, 20, 0, 1, 21.078, 0.157, 22.155, 0.222, 23.233, 0.222, 0, 25, -0.009, 0, 25.017, 0, 1, 25.511, 0.042, 26.006, 0.078, 26.5, 0.108]}, {"Target": "Parameter", "Id": "Param19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 6.667, 0.333, 13.333, 0.667, 20, 1, 2, 20.017, 0, 2, 25, 0, 2, 25.017, 0, 1, 25.511, 0.025, 26.006, 0.049, 26.5, 0.074]}, {"Target": "Parameter", "Id": "Param142", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.812, 2, 0.017, 0.127, 1, 2.561, 0.305, 5.106, 0.482, 7.65, 0.66, 1, 8.1, 0.711, 8.55, 0.761, 9, 0.812, 2, 9.017, 0.127, 1, 11.561, 0.305, 14.106, 0.482, 16.65, 0.66, 1, 17.1, 0.711, 17.55, 0.761, 18, 0.812, 2, 18.017, 0.127, 1, 18.678, 0.173, 19.339, 0.219, 20, 0.265, 1, 21.667, 0.382, 23.333, 0.498, 25, 0.615, 1, 25.006, 0.681, 25.011, 0.746, 25.017, 0.812, 2, 25.033, 0.127, 1, 25.522, 0.161, 26.011, 0.195, 26.5, 0.229]}, {"Target": "Parameter", "Id": "Param143", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.76, 1, 0.422, 0.82, 0.845, 0.88, 1.267, 0.94, 2, 1.283, 0.2, 1, 3.855, 0.387, 6.428, 0.573, 9, 0.76, 1, 9.422, 0.82, 9.845, 0.88, 10.267, 0.94, 2, 10.283, 0.2, 1, 12.855, 0.387, 15.428, 0.573, 18, 0.76, 1, 18.422, 0.82, 18.845, 0.88, 19.267, 0.94, 2, 19.283, 0.2, 1, 19.522, 0.217, 19.761, 0.235, 20, 0.252, 1, 21.667, 0.373, 23.333, 0.494, 25, 0.615, 1, 25.006, 0.663, 25.011, 0.712, 25.017, 0.76, 1, 25.439, 0.82, 25.861, 0.88, 26.283, 0.94, 2, 26.3, 0.2, 1, 26.367, 0.205, 26.433, 0.21, 26.5, 0.215]}, {"Target": "Parameter", "Id": "Param18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 0, 2, 0.35, 1, 2, 0.4, 2, 2, 0.45, 3, 2, 0.5, 4, 2, 0.55, 5, 2, 0.6, 0, 2, 0.65, 1, 2, 0.7, 2, 2, 0.75, 3, 2, 0.8, 4, 2, 0.85, 5, 2, 0.9, 0, 2, 0.95, 1, 2, 1, 2, 2, 1.05, 3, 2, 1.1, 4, 2, 1.15, 5, 2, 1.2, 0, 2, 1.25, 1, 2, 1.3, 2, 2, 1.35, 3, 2, 1.4, 4, 2, 1.45, 5, 2, 1.5, 0, 2, 1.55, 1, 2, 1.6, 2, 2, 1.65, 3, 2, 1.7, 4, 2, 1.75, 5, 2, 1.8, 0, 2, 1.85, 1, 2, 1.9, 2, 2, 1.95, 3, 2, 2, 4, 2, 2.05, 5, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 0, 2, 2.45, 1, 2, 2.5, 2, 2, 2.55, 3, 2, 2.6, 4, 2, 2.65, 5, 2, 2.7, 0, 2, 2.75, 1, 2, 2.8, 2, 2, 2.85, 3, 2, 2.9, 4, 2, 2.95, 5, 2, 3, 0, 2, 3.05, 1, 2, 3.1, 2, 2, 3.15, 3, 2, 3.2, 4, 2, 3.25, 5, 2, 3.3, 0, 2, 3.35, 1, 2, 3.4, 2, 2, 3.45, 3, 2, 3.5, 4, 2, 3.55, 5, 2, 3.6, 0, 2, 3.65, 1, 2, 3.7, 2, 2, 3.75, 3, 2, 3.8, 4, 2, 3.85, 5, 2, 3.9, 0, 2, 3.95, 1, 2, 4, 2, 2, 4.05, 3, 2, 4.1, 4, 2, 4.15, 5, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 0, 2, 4.55, 1, 2, 4.6, 2, 2, 4.65, 3, 2, 4.7, 4, 2, 4.75, 5, 2, 4.8, 0, 2, 4.85, 1, 2, 4.9, 2, 2, 4.95, 3, 2, 5, 4, 2, 5.05, 5, 2, 5.1, 0, 2, 5.15, 1, 2, 5.2, 2, 2, 5.25, 3, 2, 5.3, 4, 2, 5.35, 5, 2, 5.4, 0, 2, 5.45, 1, 2, 5.5, 2, 2, 5.55, 3, 2, 5.6, 4, 2, 5.65, 5, 2, 5.7, 0, 2, 5.75, 1, 2, 5.8, 2, 2, 5.85, 3, 2, 5.9, 4, 2, 5.95, 5, 2, 6, 0, 2, 6.05, 1, 2, 6.1, 2, 2, 6.15, 3, 2, 6.2, 4, 2, 6.25, 5, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 0, 2, 6.65, 1, 2, 6.7, 2, 2, 6.75, 3, 2, 6.8, 4, 2, 6.85, 5, 2, 6.9, 0, 2, 6.95, 1, 2, 7, 2, 2, 7.05, 3, 2, 7.1, 4, 2, 7.15, 5, 2, 7.2, 0, 2, 7.25, 1, 2, 7.3, 2, 2, 7.35, 3, 2, 7.4, 4, 2, 7.45, 5, 2, 7.5, 0, 2, 7.55, 1, 2, 7.6, 2, 2, 7.65, 3, 2, 7.7, 4, 2, 7.75, 5, 2, 7.8, 0, 2, 7.85, 1, 2, 7.9, 2, 2, 7.95, 3, 2, 8, 4, 2, 8.05, 5, 2, 8.1, 0, 2, 8.15, 1, 2, 8.2, 2, 2, 8.25, 3, 2, 8.3, 4, 2, 8.35, 5, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 0, 2, 8.75, 1, 2, 8.8, 2, 2, 8.85, 3, 2, 8.9, 4, 2, 8.95, 5, 2, 9, 0, 2, 9.05, 1, 2, 9.1, 2, 2, 9.15, 3, 2, 9.2, 4, 2, 9.25, 5, 2, 9.3, 0, 2, 9.35, 1, 2, 9.4, 2, 2, 9.45, 3, 2, 9.5, 4, 2, 9.55, 5, 2, 9.6, 0, 2, 9.65, 1, 2, 9.7, 2, 2, 9.75, 3, 2, 9.8, 4, 2, 9.85, 5, 2, 9.9, 0, 2, 9.95, 1, 2, 10, 2, 2, 10.05, 3, 2, 10.1, 4, 2, 10.15, 5, 2, 10.2, 0, 2, 10.25, 1, 2, 10.3, 2, 2, 10.35, 3, 2, 10.4, 4, 2, 10.45, 5, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 0, 2, 10.85, 1, 2, 10.9, 2, 2, 10.95, 3, 2, 11, 4, 2, 11.05, 5, 2, 11.1, 0, 2, 11.15, 1, 2, 11.2, 2, 2, 11.25, 3, 2, 11.3, 4, 2, 11.35, 5, 2, 11.4, 0, 2, 11.45, 1, 2, 11.5, 2, 2, 11.55, 3, 2, 11.6, 4, 2, 11.65, 5, 2, 11.7, 0, 2, 11.75, 1, 2, 11.8, 2, 2, 11.85, 3, 2, 11.9, 4, 2, 11.95, 5, 2, 12, 0, 2, 12.05, 1, 2, 12.1, 2, 2, 12.15, 3, 2, 12.2, 4, 2, 12.25, 5, 2, 12.3, 0, 2, 12.35, 1, 2, 12.4, 2, 2, 12.45, 3, 2, 12.5, 4, 2, 12.55, 5, 2, 12.6, 0, 2, 12.65, 1, 2, 12.7, 2, 2, 12.75, 3, 2, 12.8, 4, 2, 12.85, 5, 2, 12.9, 0, 2, 12.95, 1, 2, 13, 2, 2, 13.05, 3, 2, 13.1, 4, 2, 13.15, 5, 2, 13.2, 0, 2, 13.25, 1, 2, 13.3, 2, 2, 13.35, 3, 2, 13.4, 4, 2, 13.45, 5, 2, 13.5, 0, 2, 13.55, 1, 2, 13.6, 2, 2, 13.65, 3, 2, 13.7, 4, 2, 13.75, 5, 2, 13.8, 0, 2, 13.85, 1, 2, 13.9, 2, 2, 13.95, 3, 2, 14, 4, 2, 14.05, 5, 2, 14.1, 0, 2, 14.15, 1, 2, 14.2, 2, 2, 14.25, 3, 2, 14.3, 4, 2, 14.35, 5, 2, 14.4, 0, 2, 14.45, 1, 2, 14.5, 2, 2, 14.55, 3, 2, 14.6, 4, 2, 14.65, 5, 2, 14.7, 0, 2, 14.75, 1, 2, 14.8, 2, 2, 14.85, 3, 2, 14.9, 4, 2, 14.95, 5, 2, 15, 0, 2, 15.05, 1, 2, 15.1, 2, 2, 15.15, 3, 2, 15.2, 4, 2, 15.25, 5, 2, 15.3, 0, 2, 15.35, 1, 2, 15.4, 2, 2, 15.45, 3, 2, 15.5, 4, 2, 15.55, 5, 2, 15.6, 0, 2, 15.65, 1, 2, 15.7, 2, 2, 15.75, 3, 2, 15.8, 4, 2, 15.85, 5, 2, 15.9, 0, 2, 15.95, 1, 2, 16, 2, 2, 16.05, 3, 2, 16.1, 4, 2, 16.15, 5, 2, 16.2, 0, 2, 16.25, 1, 2, 16.3, 2, 2, 16.35, 3, 2, 16.4, 4, 2, 16.45, 5, 2, 16.5, 0, 2, 16.55, 1, 2, 16.6, 2, 2, 16.65, 3, 2, 16.7, 4, 2, 16.75, 5, 2, 16.8, 0, 2, 16.85, 1, 2, 16.9, 2, 2, 16.95, 3, 2, 17, 4, 2, 17.05, 5, 2, 17.1, 0, 2, 17.15, 1, 2, 17.2, 2, 2, 17.25, 3, 2, 17.3, 4, 2, 17.35, 5, 2, 17.4, 0, 2, 17.45, 1, 2, 17.5, 2, 2, 17.55, 3, 2, 17.6, 4, 2, 17.65, 5, 2, 17.7, 0, 2, 17.75, 1, 2, 17.8, 2, 2, 17.85, 3, 2, 17.9, 4, 2, 17.95, 5, 2, 18, 0, 2, 18.05, 1, 2, 18.1, 2, 2, 18.15, 3, 2, 18.2, 4, 2, 18.25, 5, 2, 18.3, 0, 2, 18.35, 1, 2, 18.4, 2, 2, 18.45, 3, 2, 18.5, 4, 2, 18.55, 5, 2, 18.6, 0, 2, 18.65, 1, 2, 18.7, 2, 2, 18.75, 3, 2, 18.8, 4, 2, 18.85, 5, 2, 18.9, 0, 2, 18.95, 1, 2, 19, 2, 2, 19.05, 3, 2, 19.1, 4, 2, 19.15, 5, 2, 19.2, 0, 2, 19.25, 1, 2, 19.3, 2, 2, 19.35, 3, 2, 19.4, 4, 2, 19.45, 5, 2, 19.5, 0, 2, 19.55, 1, 2, 19.6, 2, 2, 19.65, 3, 2, 19.7, 4, 2, 19.75, 5, 2, 19.8, 0, 2, 19.85, 1, 2, 19.9, 2, 2, 19.95, 3, 2, 20, 4, 2, 20.05, 5, 2, 20.1, 0, 2, 20.15, 1, 2, 20.2, 2, 2, 20.25, 3, 2, 20.3, 4, 2, 20.35, 5, 2, 20.4, 0, 2, 20.45, 1, 2, 20.5, 2, 2, 20.55, 3, 2, 20.6, 4, 2, 20.65, 5, 2, 20.7, 0, 2, 20.75, 1, 2, 20.8, 2, 2, 20.85, 3, 2, 20.9, 4, 2, 20.95, 5, 2, 21, 0, 2, 21.05, 1, 2, 21.1, 2, 2, 21.15, 3, 2, 21.2, 4, 2, 21.25, 5, 2, 21.3, 0, 2, 21.35, 1, 2, 21.4, 2, 2, 21.45, 3, 2, 21.5, 4, 2, 21.55, 5, 2, 21.6, 0, 2, 21.65, 1, 2, 21.7, 2, 2, 21.75, 3, 2, 21.8, 4, 2, 21.85, 5, 2, 21.9, 0, 2, 21.95, 1, 2, 22, 2, 2, 22.05, 3, 2, 22.1, 4, 2, 22.15, 5, 2, 22.2, 0, 2, 22.25, 1, 2, 22.3, 2, 2, 22.35, 3, 2, 22.4, 4, 2, 22.45, 5, 2, 22.5, 0, 2, 22.55, 1, 2, 22.6, 2, 2, 22.65, 3, 2, 22.7, 4, 2, 22.75, 5, 2, 22.8, 0, 2, 22.85, 1, 2, 22.9, 2, 2, 22.95, 3, 2, 23, 4, 2, 23.05, 5, 2, 23.1, 0, 2, 23.15, 1, 2, 23.2, 2, 2, 23.25, 3, 2, 23.3, 4, 2, 23.35, 5, 2, 23.4, 0, 2, 23.45, 1, 2, 23.5, 2, 2, 23.55, 3, 2, 23.6, 4, 2, 23.65, 5, 2, 23.7, 0, 2, 23.75, 1, 2, 23.8, 2, 2, 23.85, 3, 2, 23.9, 4, 2, 23.95, 5, 2, 24, 0, 2, 24.05, 1, 2, 24.1, 2, 2, 24.15, 3, 2, 24.2, 4, 2, 24.25, 5, 2, 24.3, 0, 2, 24.35, 1, 2, 24.4, 2, 2, 24.45, 3, 2, 24.5, 4, 2, 24.55, 5, 2, 24.6, 0, 2, 24.65, 1, 2, 24.7, 2, 2, 24.75, 3, 2, 24.8, 4, 2, 24.85, 5, 2, 24.9, 0, 2, 24.95, 1, 2, 25, 2, 2, 25.017, 0, 2, 25.067, 1, 2, 25.117, 2, 2, 25.167, 3, 2, 25.217, 4, 2, 25.267, 5, 2, 25.317, 0, 2, 25.367, 1, 2, 25.417, 2, 2, 25.467, 3, 2, 25.517, 4, 2, 25.567, 5, 2, 25.617, 0, 2, 25.667, 1, 2, 25.717, 2, 2, 25.767, 3, 2, 25.817, 4, 2, 25.867, 5, 2, 25.917, 0, 2, 25.967, 1, 2, 26.017, 2, 2, 26.067, 3, 2, 26.117, 4, 2, 26.167, 5, 2, 26.217, 0, 2, 26.267, 1, 2, 26.317, 2, 2, 26.367, 3, 2, 26.417, 4, 2, 26.467, 5, 2, 26.5, 5]}, {"Target": "Parameter", "Id": "Param8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 6, 2, 0.35, 0, 2, 0.4, 1, 2, 0.45, 2, 2, 0.5, 3, 2, 0.55, 4, 2, 0.6, 5, 2, 0.65, 6, 2, 0.7, 0, 2, 0.75, 1, 2, 0.8, 2, 2, 0.85, 3, 2, 0.9, 4, 2, 0.95, 5, 2, 1, 6, 2, 1.05, 0, 2, 1.1, 1, 2, 1.15, 2, 2, 1.2, 3, 2, 1.25, 4, 2, 1.3, 5, 2, 1.35, 6, 2, 1.4, 0, 2, 1.45, 1, 2, 1.5, 2, 2, 1.55, 3, 2, 1.6, 4, 2, 1.65, 5, 2, 1.7, 6, 2, 1.75, 0, 2, 1.8, 1, 2, 1.85, 2, 2, 1.9, 3, 2, 1.95, 4, 2, 2, 5, 2, 2.05, 6, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 6, 2, 2.45, 0, 2, 2.5, 1, 2, 2.55, 2, 2, 2.6, 3, 2, 2.65, 4, 2, 2.7, 5, 2, 2.75, 6, 2, 2.8, 0, 2, 2.85, 1, 2, 2.9, 2, 2, 2.95, 3, 2, 3, 4, 2, 3.05, 5, 2, 3.1, 6, 2, 3.15, 0, 2, 3.2, 1, 2, 3.25, 2, 2, 3.3, 3, 2, 3.35, 4, 2, 3.4, 5, 2, 3.45, 6, 2, 3.5, 0, 2, 3.55, 1, 2, 3.6, 2, 2, 3.65, 3, 2, 3.7, 4, 2, 3.75, 5, 2, 3.8, 6, 2, 3.85, 0, 2, 3.9, 1, 2, 3.95, 2, 2, 4, 3, 2, 4.05, 4, 2, 4.1, 5, 2, 4.15, 6, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 6, 2, 4.55, 0, 2, 4.6, 1, 2, 4.65, 2, 2, 4.7, 3, 2, 4.75, 4, 2, 4.8, 5, 2, 4.85, 6, 2, 4.9, 0, 2, 4.95, 1, 2, 5, 2, 2, 5.05, 3, 2, 5.1, 4, 2, 5.15, 5, 2, 5.2, 6, 2, 5.25, 0, 2, 5.3, 1, 2, 5.35, 2, 2, 5.4, 3, 2, 5.45, 4, 2, 5.5, 5, 2, 5.55, 6, 2, 5.6, 0, 2, 5.65, 1, 2, 5.7, 2, 2, 5.75, 3, 2, 5.8, 4, 2, 5.85, 5, 2, 5.9, 6, 2, 5.95, 0, 2, 6, 1, 2, 6.05, 2, 2, 6.1, 3, 2, 6.15, 4, 2, 6.2, 5, 2, 6.25, 6, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 6, 2, 6.65, 0, 2, 6.7, 1, 2, 6.75, 2, 2, 6.8, 3, 2, 6.85, 4, 2, 6.9, 5, 2, 6.95, 6, 2, 7, 0, 2, 7.05, 1, 2, 7.1, 2, 2, 7.15, 3, 2, 7.2, 4, 2, 7.25, 5, 2, 7.3, 6, 2, 7.35, 0, 2, 7.4, 1, 2, 7.45, 2, 2, 7.5, 3, 2, 7.55, 4, 2, 7.6, 5, 2, 7.65, 6, 2, 7.7, 0, 2, 7.75, 1, 2, 7.8, 2, 2, 7.85, 3, 2, 7.9, 4, 2, 7.95, 5, 2, 8, 6, 2, 8.05, 0, 2, 8.1, 1, 2, 8.15, 2, 2, 8.2, 3, 2, 8.25, 4, 2, 8.3, 5, 2, 8.35, 6, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 6, 2, 8.75, 0, 2, 8.8, 1, 2, 8.85, 2, 2, 8.9, 3, 2, 8.95, 4, 2, 9, 5, 2, 9.05, 6, 2, 9.1, 0, 2, 9.15, 1, 2, 9.2, 2, 2, 9.25, 3, 2, 9.3, 4, 2, 9.35, 5, 2, 9.4, 6, 2, 9.45, 0, 2, 9.5, 1, 2, 9.55, 2, 2, 9.6, 3, 2, 9.65, 4, 2, 9.7, 5, 2, 9.75, 6, 2, 9.8, 0, 2, 9.85, 1, 2, 9.9, 2, 2, 9.95, 3, 2, 10, 4, 2, 10.05, 5, 2, 10.1, 6, 2, 10.15, 0, 2, 10.2, 1, 2, 10.25, 2, 2, 10.3, 3, 2, 10.35, 4, 2, 10.4, 5, 2, 10.45, 6, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 6, 2, 10.85, 0, 2, 10.9, 1, 2, 10.95, 2, 2, 11, 3, 2, 11.05, 4, 2, 11.1, 5, 2, 11.15, 6, 2, 11.2, 0, 2, 11.25, 1, 2, 11.3, 2, 2, 11.35, 3, 2, 11.4, 4, 2, 11.45, 5, 2, 11.5, 6, 2, 11.55, 0, 2, 11.6, 1, 2, 11.65, 2, 2, 11.7, 3, 2, 11.75, 4, 2, 11.8, 5, 2, 11.85, 6, 2, 11.9, 0, 2, 11.95, 1, 2, 12, 2, 2, 12.05, 3, 2, 12.1, 4, 2, 12.15, 5, 2, 12.2, 6, 2, 12.25, 0, 2, 12.3, 1, 2, 12.35, 2, 2, 12.4, 3, 2, 12.45, 4, 2, 12.5, 5, 2, 12.55, 6, 2, 12.6, 0, 2, 12.65, 1, 2, 12.7, 2, 2, 12.75, 3, 2, 12.8, 4, 2, 12.85, 5, 2, 12.9, 6, 2, 12.95, 0, 2, 13, 1, 2, 13.05, 2, 2, 13.1, 3, 2, 13.15, 4, 2, 13.2, 5, 2, 13.25, 6, 2, 13.3, 0, 2, 13.35, 1, 2, 13.4, 2, 2, 13.45, 3, 2, 13.5, 4, 2, 13.55, 5, 2, 13.6, 6, 2, 13.65, 0, 2, 13.7, 1, 2, 13.75, 2, 2, 13.8, 3, 2, 13.85, 4, 2, 13.9, 5, 2, 13.95, 6, 2, 14, 0, 2, 14.05, 1, 2, 14.1, 2, 2, 14.15, 3, 2, 14.2, 4, 2, 14.25, 5, 2, 14.3, 6, 2, 14.35, 0, 2, 14.4, 1, 2, 14.45, 2, 2, 14.5, 3, 2, 14.55, 4, 2, 14.6, 5, 2, 14.65, 6, 2, 14.7, 0, 2, 14.75, 1, 2, 14.8, 2, 2, 14.85, 3, 2, 14.9, 4, 2, 14.95, 5, 2, 15, 6, 2, 15.05, 0, 2, 15.1, 1, 2, 15.15, 2, 2, 15.2, 3, 2, 15.25, 4, 2, 15.3, 5, 2, 15.35, 6, 2, 15.4, 0, 2, 15.45, 1, 2, 15.5, 2, 2, 15.55, 3, 2, 15.6, 4, 2, 15.65, 5, 2, 15.7, 6, 2, 15.75, 0, 2, 15.8, 1, 2, 15.85, 2, 2, 15.9, 3, 2, 15.95, 4, 2, 16, 5, 2, 16.05, 6, 2, 16.1, 0, 2, 16.15, 1, 2, 16.2, 2, 2, 16.25, 3, 2, 16.3, 4, 2, 16.35, 5, 2, 16.4, 6, 2, 16.45, 0, 2, 16.5, 1, 2, 16.55, 2, 2, 16.6, 3, 2, 16.65, 4, 2, 16.7, 5, 2, 16.75, 6, 2, 16.8, 0, 2, 16.85, 1, 2, 16.9, 2, 2, 16.95, 3, 2, 17, 4, 2, 17.05, 5, 2, 17.1, 6, 2, 17.15, 0, 2, 17.2, 1, 2, 17.25, 2, 2, 17.3, 3, 2, 17.35, 4, 2, 17.4, 5, 2, 17.45, 6, 2, 17.5, 0, 2, 17.55, 1, 2, 17.6, 2, 2, 17.65, 3, 2, 17.7, 4, 2, 17.75, 5, 2, 17.8, 6, 2, 17.85, 0, 2, 17.9, 1, 2, 17.95, 2, 2, 18, 3, 2, 18.05, 4, 2, 18.1, 5, 2, 18.15, 6, 2, 18.2, 0, 2, 18.25, 1, 2, 18.3, 2, 2, 18.35, 3, 2, 18.4, 4, 2, 18.45, 5, 2, 18.5, 6, 2, 18.55, 0, 2, 18.6, 1, 2, 18.65, 2, 2, 18.7, 3, 2, 18.75, 4, 2, 18.8, 5, 2, 18.85, 6, 2, 18.9, 0, 2, 18.95, 1, 2, 19, 2, 2, 19.05, 3, 2, 19.1, 4, 2, 19.15, 5, 2, 19.2, 6, 2, 19.25, 0, 2, 19.3, 1, 2, 19.35, 2, 2, 19.4, 3, 2, 19.45, 4, 2, 19.5, 5, 2, 19.55, 6, 2, 19.6, 0, 2, 19.65, 1, 2, 19.7, 2, 2, 19.75, 3, 2, 19.8, 4, 2, 19.85, 5, 2, 19.9, 6, 2, 19.95, 0, 2, 20, 1, 2, 20.05, 2, 2, 20.1, 3, 2, 20.15, 4, 2, 20.2, 5, 2, 20.25, 6, 2, 20.3, 0, 2, 20.35, 1, 2, 20.4, 2, 2, 20.45, 3, 2, 20.5, 4, 2, 20.55, 5, 2, 20.6, 6, 2, 20.65, 0, 2, 20.7, 1, 2, 20.75, 2, 2, 20.8, 3, 2, 20.85, 4, 2, 20.9, 5, 2, 20.95, 6, 2, 21, 0, 2, 21.05, 1, 2, 21.1, 2, 2, 21.15, 3, 2, 21.2, 4, 2, 21.25, 5, 2, 21.3, 6, 2, 21.35, 0, 2, 21.4, 1, 2, 21.45, 2, 2, 21.5, 3, 2, 21.55, 4, 2, 21.6, 5, 2, 21.65, 6, 2, 21.7, 0, 2, 21.75, 1, 2, 21.8, 2, 2, 21.85, 3, 2, 21.9, 4, 2, 21.95, 5, 2, 22, 6, 2, 22.05, 0, 2, 22.1, 1, 2, 22.15, 2, 2, 22.2, 3, 2, 22.25, 4, 2, 22.3, 5, 2, 22.35, 6, 2, 22.4, 0, 2, 22.45, 1, 2, 22.5, 2, 2, 22.55, 3, 2, 22.6, 4, 2, 22.65, 5, 2, 22.7, 6, 2, 22.75, 0, 2, 22.8, 1, 2, 22.85, 2, 2, 22.9, 3, 2, 22.95, 4, 2, 23, 5, 2, 23.05, 6, 2, 23.1, 0, 2, 23.15, 1, 2, 23.2, 2, 2, 23.25, 3, 2, 23.3, 4, 2, 23.35, 5, 2, 23.4, 6, 2, 23.45, 0, 2, 23.5, 1, 2, 23.55, 2, 2, 23.6, 3, 2, 23.65, 4, 2, 23.7, 5, 2, 23.75, 6, 2, 23.8, 0, 2, 23.85, 1, 2, 23.9, 2, 2, 23.95, 3, 2, 24, 4, 2, 24.05, 5, 2, 24.1, 6, 2, 24.15, 0, 2, 24.2, 1, 2, 24.25, 2, 2, 24.3, 3, 2, 24.35, 4, 2, 24.4, 5, 2, 24.45, 6, 2, 24.5, 0, 2, 24.55, 1, 2, 24.6, 2, 2, 24.65, 3, 2, 24.7, 4, 2, 24.75, 5, 2, 24.8, 6, 2, 24.85, 0, 2, 24.9, 1, 2, 24.95, 2, 2, 25, 3, 2, 25.017, 0, 2, 25.067, 1, 2, 25.117, 2, 2, 25.167, 3, 2, 25.217, 4, 2, 25.267, 5, 2, 25.317, 6, 2, 25.367, 0, 2, 25.417, 1, 2, 25.467, 2, 2, 25.517, 3, 2, 25.567, 4, 2, 25.617, 5, 2, 25.667, 6, 2, 25.717, 0, 2, 25.767, 1, 2, 25.817, 2, 2, 25.867, 3, 2, 25.917, 4, 2, 25.967, 5, 2, 26.017, 6, 2, 26.067, 0, 2, 26.117, 1, 2, 26.167, 2, 2, 26.217, 3, 2, 26.267, 4, 2, 26.317, 5, 2, 26.367, 6, 2, 26.417, 0, 2, 26.467, 1, 2, 26.5, 1]}, {"Target": "Parameter", "Id": "Param51", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 2, 0.05, 1, 2, 0.1, 2, 2, 0.15, 3, 2, 0.2, 4, 2, 0.25, 5, 2, 0.3, 6, 2, 0.35, 0, 2, 0.4, 1, 2, 0.45, 2, 2, 0.5, 3, 2, 0.55, 4, 2, 0.6, 5, 2, 0.65, 6, 2, 0.7, 0, 2, 0.75, 1, 2, 0.8, 2, 2, 0.85, 3, 2, 0.9, 4, 2, 0.95, 5, 2, 1, 6, 2, 1.05, 0, 2, 1.1, 1, 2, 1.15, 2, 2, 1.2, 3, 2, 1.25, 4, 2, 1.3, 5, 2, 1.35, 6, 2, 1.4, 0, 2, 1.45, 1, 2, 1.5, 2, 2, 1.55, 3, 2, 1.6, 4, 2, 1.65, 5, 2, 1.7, 6, 2, 1.75, 0, 2, 1.8, 1, 2, 1.85, 2, 2, 1.9, 3, 2, 1.95, 4, 2, 2, 5, 2, 2.05, 6, 2, 2.1, 0, 2, 2.15, 1, 2, 2.2, 2, 2, 2.25, 3, 2, 2.3, 4, 2, 2.35, 5, 2, 2.4, 6, 2, 2.45, 0, 2, 2.5, 1, 2, 2.55, 2, 2, 2.6, 3, 2, 2.65, 4, 2, 2.7, 5, 2, 2.75, 6, 2, 2.8, 0, 2, 2.85, 1, 2, 2.9, 2, 2, 2.95, 3, 2, 3, 4, 2, 3.05, 5, 2, 3.1, 6, 2, 3.15, 0, 2, 3.2, 1, 2, 3.25, 2, 2, 3.3, 3, 2, 3.35, 4, 2, 3.4, 5, 2, 3.45, 6, 2, 3.5, 0, 2, 3.55, 1, 2, 3.6, 2, 2, 3.65, 3, 2, 3.7, 4, 2, 3.75, 5, 2, 3.8, 6, 2, 3.85, 0, 2, 3.9, 1, 2, 3.95, 2, 2, 4, 3, 2, 4.05, 4, 2, 4.1, 5, 2, 4.15, 6, 2, 4.2, 0, 2, 4.25, 1, 2, 4.3, 2, 2, 4.35, 3, 2, 4.4, 4, 2, 4.45, 5, 2, 4.5, 6, 2, 4.55, 0, 2, 4.6, 1, 2, 4.65, 2, 2, 4.7, 3, 2, 4.75, 4, 2, 4.8, 5, 2, 4.85, 6, 2, 4.9, 0, 2, 4.95, 1, 2, 5, 2, 2, 5.05, 3, 2, 5.1, 4, 2, 5.15, 5, 2, 5.2, 6, 2, 5.25, 0, 2, 5.3, 1, 2, 5.35, 2, 2, 5.4, 3, 2, 5.45, 4, 2, 5.5, 5, 2, 5.55, 6, 2, 5.6, 0, 2, 5.65, 1, 2, 5.7, 2, 2, 5.75, 3, 2, 5.8, 4, 2, 5.85, 5, 2, 5.9, 6, 2, 5.95, 0, 2, 6, 1, 2, 6.05, 2, 2, 6.1, 3, 2, 6.15, 4, 2, 6.2, 5, 2, 6.25, 6, 2, 6.3, 0, 2, 6.35, 1, 2, 6.4, 2, 2, 6.45, 3, 2, 6.5, 4, 2, 6.55, 5, 2, 6.6, 6, 2, 6.65, 0, 2, 6.7, 1, 2, 6.75, 2, 2, 6.8, 3, 2, 6.85, 4, 2, 6.9, 5, 2, 6.95, 6, 2, 7, 0, 2, 7.05, 1, 2, 7.1, 2, 2, 7.15, 3, 2, 7.2, 4, 2, 7.25, 5, 2, 7.3, 6, 2, 7.35, 0, 2, 7.4, 1, 2, 7.45, 2, 2, 7.5, 3, 2, 7.55, 4, 2, 7.6, 5, 2, 7.65, 6, 2, 7.7, 0, 2, 7.75, 1, 2, 7.8, 2, 2, 7.85, 3, 2, 7.9, 4, 2, 7.95, 5, 2, 8, 6, 2, 8.05, 0, 2, 8.1, 1, 2, 8.15, 2, 2, 8.2, 3, 2, 8.25, 4, 2, 8.3, 5, 2, 8.35, 6, 2, 8.4, 0, 2, 8.45, 1, 2, 8.5, 2, 2, 8.55, 3, 2, 8.6, 4, 2, 8.65, 5, 2, 8.7, 6, 2, 8.75, 0, 2, 8.8, 1, 2, 8.85, 2, 2, 8.9, 3, 2, 8.95, 4, 2, 9, 5, 2, 9.05, 6, 2, 9.1, 0, 2, 9.15, 1, 2, 9.2, 2, 2, 9.25, 3, 2, 9.3, 4, 2, 9.35, 5, 2, 9.4, 6, 2, 9.45, 0, 2, 9.5, 1, 2, 9.55, 2, 2, 9.6, 3, 2, 9.65, 4, 2, 9.7, 5, 2, 9.75, 6, 2, 9.8, 0, 2, 9.85, 1, 2, 9.9, 2, 2, 9.95, 3, 2, 10, 4, 2, 10.05, 5, 2, 10.1, 6, 2, 10.15, 0, 2, 10.2, 1, 2, 10.25, 2, 2, 10.3, 3, 2, 10.35, 4, 2, 10.4, 5, 2, 10.45, 6, 2, 10.5, 0, 2, 10.55, 1, 2, 10.6, 2, 2, 10.65, 3, 2, 10.7, 4, 2, 10.75, 5, 2, 10.8, 6, 2, 10.85, 0, 2, 10.9, 1, 2, 10.95, 2, 2, 11, 3, 2, 11.05, 4, 2, 11.1, 5, 2, 11.15, 6, 2, 11.2, 0, 2, 11.25, 1, 2, 11.3, 2, 2, 11.35, 3, 2, 11.4, 4, 2, 11.45, 5, 2, 11.5, 6, 2, 11.55, 0, 2, 11.6, 1, 2, 11.65, 2, 2, 11.7, 3, 2, 11.75, 4, 2, 11.8, 5, 2, 11.85, 6, 2, 11.9, 0, 2, 11.95, 1, 2, 12, 2, 2, 12.05, 3, 2, 12.1, 4, 2, 12.15, 5, 2, 12.2, 6, 2, 12.25, 0, 2, 12.3, 1, 2, 12.35, 2, 2, 12.4, 3, 2, 12.45, 4, 2, 12.5, 5, 2, 12.55, 6, 2, 12.6, 0, 2, 12.65, 1, 2, 12.7, 2, 2, 12.75, 3, 2, 12.8, 4, 2, 12.85, 5, 2, 12.9, 6, 2, 12.95, 0, 2, 13, 1, 2, 13.05, 2, 2, 13.1, 3, 2, 13.15, 4, 2, 13.2, 5, 2, 13.25, 6, 2, 13.3, 0, 2, 13.35, 1, 2, 13.4, 2, 2, 13.45, 3, 2, 13.5, 4, 2, 13.55, 5, 2, 13.6, 6, 2, 13.65, 0, 2, 13.7, 1, 2, 13.75, 2, 2, 13.8, 3, 2, 13.85, 4, 2, 13.9, 5, 2, 13.95, 6, 2, 14, 0, 2, 14.05, 1, 2, 14.1, 2, 2, 14.15, 3, 2, 14.2, 4, 2, 14.25, 5, 2, 14.3, 6, 2, 14.35, 0, 2, 14.4, 1, 2, 14.45, 2, 2, 14.5, 3, 2, 14.55, 4, 2, 14.6, 5, 2, 14.65, 6, 2, 14.7, 0, 2, 14.75, 1, 2, 14.8, 2, 2, 14.85, 3, 2, 14.9, 4, 2, 14.95, 5, 2, 15, 6, 2, 15.05, 0, 2, 15.1, 1, 2, 15.15, 2, 2, 15.2, 3, 2, 15.25, 4, 2, 15.3, 5, 2, 15.35, 6, 2, 15.4, 0, 2, 15.45, 1, 2, 15.5, 2, 2, 15.55, 3, 2, 15.6, 4, 2, 15.65, 5, 2, 15.7, 6, 2, 15.75, 0, 2, 15.8, 1, 2, 15.85, 2, 2, 15.9, 3, 2, 15.95, 4, 2, 16, 5, 2, 16.05, 6, 2, 16.1, 0, 2, 16.15, 1, 2, 16.2, 2, 2, 16.25, 3, 2, 16.3, 4, 2, 16.35, 5, 2, 16.4, 6, 2, 16.45, 0, 2, 16.5, 1, 2, 16.55, 2, 2, 16.6, 3, 2, 16.65, 4, 2, 16.7, 5, 2, 16.75, 6, 2, 16.8, 0, 2, 16.85, 1, 2, 16.9, 2, 2, 16.95, 3, 2, 17, 4, 2, 17.05, 5, 2, 17.1, 6, 2, 17.15, 0, 2, 17.2, 1, 2, 17.25, 2, 2, 17.3, 3, 2, 17.35, 4, 2, 17.4, 5, 2, 17.45, 6, 2, 17.5, 0, 2, 17.55, 1, 2, 17.6, 2, 2, 17.65, 3, 2, 17.7, 4, 2, 17.75, 5, 2, 17.8, 6, 2, 17.85, 0, 2, 17.9, 1, 2, 17.95, 2, 2, 18, 3, 2, 18.05, 4, 2, 18.1, 5, 2, 18.15, 6, 2, 18.2, 0, 2, 18.25, 1, 2, 18.3, 2, 2, 18.35, 3, 2, 18.4, 4, 2, 18.45, 5, 2, 18.5, 6, 2, 18.55, 0, 2, 18.6, 1, 2, 18.65, 2, 2, 18.7, 3, 2, 18.75, 4, 2, 18.8, 5, 2, 18.85, 6, 2, 18.9, 0, 2, 18.95, 1, 2, 19, 2, 2, 19.05, 3, 2, 19.1, 4, 2, 19.15, 5, 2, 19.2, 6, 2, 19.25, 0, 2, 19.3, 1, 2, 19.35, 2, 2, 19.4, 3, 2, 19.45, 4, 2, 19.5, 5, 2, 19.55, 6, 2, 19.6, 0, 2, 19.65, 1, 2, 19.7, 2, 2, 19.75, 3, 2, 19.8, 4, 2, 19.85, 5, 2, 19.9, 6, 2, 19.95, 0, 2, 20, 1, 2, 20.05, 2, 2, 20.1, 3, 2, 20.15, 4, 2, 20.2, 5, 2, 20.25, 6, 2, 20.3, 0, 2, 20.35, 1, 2, 20.4, 2, 2, 20.45, 3, 2, 20.5, 4, 2, 20.55, 5, 2, 20.6, 6, 2, 20.65, 0, 2, 20.7, 1, 2, 20.75, 2, 2, 20.8, 3, 2, 20.85, 4, 2, 20.9, 5, 2, 20.95, 6, 2, 21, 0, 2, 21.05, 1, 2, 21.1, 2, 2, 21.15, 3, 2, 21.2, 4, 2, 21.25, 5, 2, 21.3, 6, 2, 21.35, 0, 2, 21.4, 1, 2, 21.45, 2, 2, 21.5, 3, 2, 21.55, 4, 2, 21.6, 5, 2, 21.65, 6, 2, 21.7, 0, 2, 21.75, 1, 2, 21.8, 2, 2, 21.85, 3, 2, 21.9, 4, 2, 21.95, 5, 2, 22, 6, 2, 22.05, 0, 2, 22.1, 1, 2, 22.15, 2, 2, 22.2, 3, 2, 22.25, 4, 2, 22.3, 5, 2, 22.35, 6, 2, 22.4, 0, 2, 22.45, 1, 2, 22.5, 2, 2, 22.55, 3, 2, 22.6, 4, 2, 22.65, 5, 2, 22.7, 6, 2, 22.75, 0, 2, 22.8, 1, 2, 22.85, 2, 2, 22.9, 3, 2, 22.95, 4, 2, 23, 5, 2, 23.05, 6, 2, 23.1, 0, 2, 23.15, 1, 2, 23.2, 2, 2, 23.25, 3, 2, 23.3, 4, 2, 23.35, 5, 2, 23.4, 6, 2, 23.45, 0, 2, 23.5, 1, 2, 23.55, 2, 2, 23.6, 3, 2, 23.65, 4, 2, 23.7, 5, 2, 23.75, 6, 2, 23.8, 0, 2, 23.85, 1, 2, 23.9, 2, 2, 23.95, 3, 2, 24, 4, 2, 24.05, 5, 2, 24.1, 6, 2, 24.15, 0, 2, 24.2, 1, 2, 24.25, 2, 2, 24.3, 3, 2, 24.35, 4, 2, 24.4, 5, 2, 24.45, 6, 2, 24.5, 0, 2, 24.55, 1, 2, 24.6, 2, 2, 24.65, 3, 2, 24.7, 4, 2, 24.75, 5, 2, 24.8, 6, 2, 24.85, 0, 2, 24.9, 1, 2, 24.95, 2, 2, 25, 3, 2, 25.017, 0, 2, 25.067, 1, 2, 25.117, 2, 2, 25.167, 3, 2, 25.217, 4, 2, 25.267, 5, 2, 25.317, 6, 2, 25.367, 0, 2, 25.417, 1, 2, 25.467, 2, 2, 25.517, 3, 2, 25.567, 4, 2, 25.617, 5, 2, 25.667, 6, 2, 25.717, 0, 2, 25.767, 1, 2, 25.817, 2, 2, 25.867, 3, 2, 25.917, 4, 2, 25.967, 5, 2, 26.017, 6, 2, 26.067, 0, 2, 26.117, 1, 2, 26.167, 2, 2, 26.217, 3, 2, 26.267, 4, 2, 26.317, 5, 2, 26.367, 6, 2, 26.417, 0, 2, 26.467, 1, 2, 26.5, 1]}, {"Target": "Parameter", "Id": "Param42", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 6.667, 0.074, 13.333, 0.148, 20, 0.222, 1, 21.667, 0.241, 23.333, 0.259, 25, 0.278, 1, 25.006, 0.185, 25.011, 0.093, 25.017, 0, 1, 25.511, 0.005, 26.006, 0.011, 26.5, 0.016]}, {"Target": "Parameter", "Id": "Param45", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -0.4, 1, 6.667, -0.326, 13.333, -0.252, 20, -0.178, 1, 21.667, -0.159, 23.333, -0.141, 25, -0.122, 1, 25.006, -0.215, 25.011, -0.307, 25.017, -0.4, 1, 25.511, -0.395, 26.006, -0.389, 26.5, -0.384]}, {"Target": "Parameter", "Id": "Param46", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.3, 1, 6.667, 0.374, 13.333, 0.448, 20, 0.522, 1, 21.667, 0.541, 23.333, 0.559, 25, 0.578, 1, 25.006, 0.485, 25.011, 0.393, 25.017, 0.3, 1, 25.511, 0.305, 26.006, 0.311, 26.5, 0.316]}, {"Target": "Parameter", "Id": "Param50", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 6.667, 1.333, 13.333, 2.667, 20, 4, 1, 21.667, 4.333, 23.333, 4.667, 25, 5, 1, 25.006, 3.333, 25.011, 1.667, 25.017, 0, 1, 25.511, 0.099, 26.006, 0.198, 26.5, 0.297]}, {"Target": "Parameter", "Id": "Param10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2.5, 10, 0, 5.017, -10, 0, 7.5, 10, 0, 10, -10, 0, 12.5, 26.92, 0, 15.017, -10, 0, 17.5, 10, 0, 20, -10, 0, 22.5, 8.76, 1, 23.333, 8.76, 24.167, -1.149, 25, -9.998, 1, 25.006, -10.057, 25.011, -10, 25.017, -10, 1, 25.511, -10, 26.006, -2.959, 26.5, 2.767]}, {"Target": "Parameter", "Id": "Param17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -5.76, 0, 2.5, 0.66, 0, 5.017, -5.76, 0, 7.5, 0.66, 0, 10, -5.76, 0, 12.5, 21.06, 0, 15.017, -5.76, 0, 17.5, 0.66, 0, 20, -5.76, 0, 22.5, -1.54, 1, 23.333, -1.54, 24.167, -3.375, 25, -5.759, 1, 25.006, -5.775, 25.011, -5.76, 25.017, -5.76, 1, 25.511, -5.76, 26.006, -3.5, 26.5, -1.662]}, {"Target": "Parameter", "Id": "Param49", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.35, 0, 1.25, 0, 0, 3.75, 0.7, 0, 6.267, 0, 0, 8.767, 0.7, 0, 11.283, 0, 0, 13.783, 0.7, 0, 16.3, 0, 0, 18.8, 0.7, 1, 19.2, 0.7, 19.6, 0.596, 20, 0.374, 1, 20.439, 0.13, 20.878, 0, 21.317, 0, 0, 23.817, 0.7, 1, 24.211, 0.7, 24.606, 0.596, 25, 0.381, 1, 25.006, 0.378, 25.011, 0.353, 25.017, 0.35, 1, 25.434, 0.115, 25.85, 0, 26.267, 0, 1, 26.345, 0, 26.422, 0.006, 26.5, 0.017]}, {"Target": "Parameter", "Id": "Param40", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1.25, 0, 0.917, 10, 0, 2.917, -10, 0, 4.917, 10, 0, 6.917, -10, 0, 8.917, 10, 0, 10.917, -10, 0, 12.917, 10, 0, 14.917, -10, 0, 16.917, 10, 0, 18.917, -10, 1, 19.278, -10, 19.639, -5.959, 20, 1.25, 1, 20.306, 7.35, 20.611, 10, 20.917, 10, 0, 22.917, -10, 0, 24.917, 10, 1, 24.945, 10, 24.972, 9.979, 25, 9.898, 1, 25.006, 9.882, 25.011, 1.25, 25.017, 1.25, 0, 25.933, 10, 1, 26.122, 10, 26.311, 8.394, 26.5, 6.093]}, {"Target": "Parameter", "Id": "Param41", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2, 10, 0, 4, -10, 0, 6, 10, 0, 8, -10, 0, 10, 10, 0, 12, -10, 0, 14, 10, 0, 16, -10, 0, 18, 10, 0, 20, -10, 0, 22, 10, 0, 24, -10, 0, 25, 0, 1, 25.006, 0, 25.011, -10, 25.017, -10, 1, 25.511, -10, 26.006, 1.001, 26.5, 6.685]}, {"Target": "Parameter", "Id": "Param32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -10, 0, 2, 10, 0, 4, -10, 0, 6, 10, 0, 8, -10, 0, 10, 10, 0, 12, -10, 0, 14, 10, 0, 16, -10, 0, 18, 10, 0, 20, -10, 0, 22, 10, 0, 24, -10, 0, 25, 0, 1, 25.006, 0, 25.011, -10, 25.017, -10, 1, 25.511, -10, 26.006, 1.001, 26.5, 6.685]}, {"Target": "Parameter", "Id": "Param33", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.886, 0, 0.583, 10, 0, 2.583, -10, 0, 4.583, 10, 0, 6.583, -10, 0, 8.583, 10, 0, 10.583, -10, 0, 12.583, 10, 0, 14.583, -10, 0, 16.583, 10, 0, 18.583, -10, 1, 19.055, -10, 19.528, -3.137, 20, 5.886, 1, 20.194, 9.602, 20.389, 10, 20.583, 10, 0, 22.583, -10, 0, 24.583, 10, 1, 24.722, 10, 24.861, 9.306, 25, 7.759, 1, 25.006, 7.697, 25.011, 5.886, 25.017, 5.886, 0, 25.6, 10, 1, 25.9, 10, 26.2, 5.95, 26.5, 1.495]}, {"Target": "Parameter", "Id": "Param34", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -14.451, 0, 1.333, 30, 0, 3.333, -30, 0, 5.333, 30, 0, 7.333, -30, 0, 9.333, 30, 0, 11.333, -30, 0, 13.333, 30, 0, 15.333, -30, 0, 17.333, 30, 0, 19.333, -30, 1, 19.555, -30, 19.778, -27.362, 20, -14.451, 1, 20.444, 11.37, 20.889, 30, 21.333, 30, 0, 23.333, -30, 0, 25, 25.551, 1, 25.006, 25.551, 25.011, -14.451, 25.017, -14.451, 0, 26.35, 30, 1, 26.4, 30, 26.45, 29.662, 26.5, 29.038]}, {"Target": "Parameter", "Id": "Param28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -15, 0, 2, 15, 0, 4, -15, 0, 6, 15, 0, 8, -15, 0, 10, 15, 0, 12, -15, 0, 14, 15, 0, 16, -15, 0, 18, 15, 0, 20, -15, 0, 22, 15, 0, 24, -15, 0, 25, 0, 1, 25.006, 0, 25.011, -15, 25.017, -15, 1, 25.511, -15, 26.006, 1.502, 26.5, 10.028]}, {"Target": "Parameter", "Id": "Param29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 5.886, 0, 0.583, 10, 0, 2.583, -10, 0, 4.583, 10, 0, 6.583, -10, 0, 8.583, 10, 0, 10.583, -10, 0, 12.583, 10, 0, 14.583, -10, 0, 16.583, 10, 0, 18.583, -10, 1, 19.055, -10, 19.528, -3.137, 20, 5.886, 1, 20.194, 9.602, 20.389, 10, 20.583, 10, 0, 22.583, -10, 0, 24.583, 10, 1, 24.722, 10, 24.861, 9.306, 25, 7.759, 1, 25.006, 7.697, 25.011, 5.886, 25.017, 5.886, 0, 25.6, 10, 1, 25.9, 10, 26.2, 5.95, 26.5, 1.495]}, {"Target": "Parameter", "Id": "Param35", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, -14.451, 0, 1.333, 30, 0, 3.333, -30, 0, 5.333, 30, 0, 7.333, -30, 0, 9.333, 30, 0, 11.333, -30, 0, 13.333, 30, 0, 15.333, -30, 0, 17.333, 30, 0, 19.333, -30, 1, 19.555, -30, 19.778, -27.362, 20, -14.451, 1, 20.444, 11.37, 20.889, 30, 21.333, 30, 0, 23.333, -30, 0, 25, 25.551, 1, 25.006, 25.551, 25.011, -14.451, 25.017, -14.451, 0, 26.35, 30, 1, 26.4, 30, 26.45, 29.662, 26.5, 29.038]}, {"Target": "Parameter", "Id": "Param30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 2.959, 0, 0.8, 10, 0, 2.8, -10, 0, 4.8, 10, 0, 6.8, -10, 0, 8.8, 10, 0, 10.8, -10, 0, 12.8, 10, 0, 14.8, -10, 0, 16.8, 10, 0, 18.8, -10, 1, 19.2, -10, 19.6, -4.959, 20, 2.959, 1, 20.267, 8.237, 20.533, 10, 20.8, 10, 0, 22.8, -10, 0, 24.8, 10, 1, 24.867, 10, 24.933, 9.843, 25, 9.44, 1, 25.006, 9.406, 25.011, 2.959, 25.017, 2.959, 0, 25.817, 10, 1, 26.045, 10, 26.272, 7.665, 26.5, 4.591]}, {"Target": "Parameter", "Id": "Param31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 9.44, 0, 1.8, -10, 0, 3.8, 10, 0, 5.8, -10, 0, 7.8, 10, 0, 9.8, -10, 0, 11.8, 10, 0, 13.8, -10, 0, 15.8, 10, 0, 17.8, -10, 0, 19.8, 10, 1, 19.867, 10, 19.933, 10.555, 20, 9.44, 1, 20.6, -0.593, 21.2, -10, 21.8, -10, 0, 23.8, 10, 0, 25, -2.959, 1, 25.006, -2.959, 25.011, 9.44, 25.017, 9.44, 1, 25.511, 9.44, 26.006, -3.762, 26.5, -8.407]}, {"Target": "Parameter", "Id": "Param36", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 13.773, 0, 0.683, 30, 0, 2.683, -30, 0, 4.683, 30, 0, 6.683, -30, 0, 8.683, 30, 0, 10.683, -30, 0, 12.683, 30, 0, 14.683, -30, 0, 16.683, 30, 0, 18.683, -30, 1, 19.122, -30, 19.561, -11.809, 20, 13.773, 1, 20.228, 27.05, 20.455, 30, 20.683, 30, 0, 22.683, -30, 0, 24.683, 30, 1, 24.789, 30, 24.894, 28.77, 25, 25.964, 1, 25.006, 25.816, 25.011, 13.773, 25.017, 13.773, 0, 25.7, 30, 1, 25.967, 30, 26.233, 20.4, 26.5, 8.88]}, {"Target": "Parameter", "Id": "GL1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 6.667, 0.297, 13.333, 0.593, 20, 0.89, 1, 20.828, 0.927, 21.655, 0.963, 22.483, 1, 2, 22.5, 0, 1, 23.333, 0.037, 24.167, 0.074, 25, 0.111, 1, 25.006, 0.074, 25.011, 0.037, 25.017, 0, 1, 25.511, 0.022, 26.006, 0.044, 26.5, 0.066]}, {"Target": "Parameter", "Id": "GL2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.39, 1, 6.667, 0.687, 13.333, 0.983, 20, 1.28, 1, 20.828, 1.317, 21.655, 1.353, 22.483, 1.39, 2, 22.5, 0.39, 1, 23.333, 0.427, 24.167, 0.464, 25, 0.501, 1, 25.006, 0.464, 25.011, 0.427, 25.017, 0.39, 1, 25.511, 0.412, 26.006, 0.434, 26.5, 0.456]}, {"Target": "Parameter", "Id": "GL3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.72, 1, 6.667, 1.017, 13.333, 1.313, 20, 1.61, 1, 20.828, 1.647, 21.655, 1.683, 22.483, 1.72, 2, 22.5, 0.72, 1, 23.333, 0.757, 24.167, 0.794, 25, 0.831, 1, 25.006, 0.794, 25.011, 0.757, 25.017, 0.72, 1, 25.511, 0.742, 26.006, 0.764, 26.5, 0.786]}, {"Target": "Parameter", "Id": "GL4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.21, 1, 6.667, 0.507, 13.333, 0.803, 20, 1.1, 1, 20.828, 1.137, 21.655, 1.173, 22.483, 1.21, 2, 22.5, 0.21, 1, 23.333, 0.247, 24.167, 0.284, 25, 0.321, 1, 25.006, 0.284, 25.011, 0.247, 25.017, 0.21, 1, 25.511, 0.232, 26.006, 0.254, 26.5, 0.276]}, {"Target": "Parameter", "Id": "GL5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.53, 1, 6.667, 0.827, 13.333, 1.123, 20, 1.42, 1, 20.828, 1.457, 21.655, 1.493, 22.483, 1.53, 2, 22.5, 0.53, 1, 23.333, 0.567, 24.167, 0.604, 25, 0.641, 1, 25.006, 0.604, 25.011, 0.567, 25.017, 0.53, 1, 25.511, 0.552, 26.006, 0.574, 26.5, 0.596]}, {"Target": "Parameter", "Id": "GL6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.89, 1, 6.667, 1.187, 13.333, 1.483, 20, 1.78, 1, 20.828, 1.817, 21.655, 1.853, 22.483, 1.89, 2, 22.5, 0.89, 1, 23.333, 0.927, 24.167, 0.964, 25, 1.001, 1, 25.006, 0.964, 25.011, 0.927, 25.017, 0.89, 1, 25.511, 0.912, 26.006, 0.934, 26.5, 0.956]}, {"Target": "Parameter", "Id": "GL7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.77, 1, 6.667, 1.067, 13.333, 1.363, 20, 1.66, 1, 20.828, 1.697, 21.655, 1.733, 22.483, 1.77, 2, 22.5, 0.77, 1, 23.333, 0.807, 24.167, 0.844, 25, 0.881, 1, 25.006, 0.844, 25.011, 0.807, 25.017, 0.77, 1, 25.511, 0.792, 26.006, 0.814, 26.5, 0.836]}, {"Target": "Parameter", "Id": "GL8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.09, 1, 6.667, 0.387, 13.333, 0.683, 20, 0.98, 1, 20.828, 1.017, 21.655, 1.053, 22.483, 1.09, 2, 22.5, 0.09, 1, 23.333, 0.127, 24.167, 0.164, 25, 0.201, 1, 25.006, 0.164, 25.011, 0.127, 25.017, 0.09, 1, 25.511, 0.112, 26.006, 0.134, 26.5, 0.156]}, {"Target": "Parameter", "Id": "GL9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.301, 1, 6.667, 0.598, 13.333, 0.894, 20, 1.191, 1, 20.828, 1.228, 21.655, 1.264, 22.483, 1.301, 2, 22.5, 0.301, 1, 23.333, 0.338, 24.167, 0.375, 25, 0.412, 1, 25.006, 0.375, 25.011, 0.338, 25.017, 0.301, 1, 25.511, 0.323, 26.006, 0.345, 26.5, 0.367]}, {"Target": "Parameter", "Id": "GL10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.45, 1, 6.667, 0.747, 13.333, 1.043, 20, 1.34, 1, 20.828, 1.377, 21.655, 1.413, 22.483, 1.45, 2, 22.5, 0.45, 1, 23.333, 0.487, 24.167, 0.524, 25, 0.561, 1, 25.006, 0.524, 25.011, 0.487, 25.017, 0.45, 1, 25.511, 0.472, 26.006, 0.494, 26.5, 0.516]}, {"Target": "Parameter", "Id": "GL11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.64, 1, 6.667, 0.937, 13.333, 1.233, 20, 1.53, 1, 20.828, 1.567, 21.655, 1.603, 22.483, 1.64, 2, 22.5, 0.64, 1, 23.333, 0.677, 24.167, 0.714, 25, 0.751, 1, 25.006, 0.714, 25.011, 0.677, 25.017, 0.64, 1, 25.511, 0.662, 26.006, 0.684, 26.5, 0.706]}, {"Target": "Parameter", "Id": "GL12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.15, 1, 6.667, 0.447, 13.333, 0.743, 20, 1.04, 1, 20.828, 1.077, 21.655, 1.113, 22.483, 1.15, 2, 22.5, 0.15, 1, 23.333, 0.187, 24.167, 0.224, 25, 0.261, 1, 25.006, 0.224, 25.011, 0.187, 25.017, 0.15, 1, 25.511, 0.172, 26.006, 0.194, 26.5, 0.216]}, {"Target": "Parameter", "Id": "GL13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.585, 1, 6.667, 0.882, 13.333, 1.178, 20, 1.475, 1, 20.828, 1.512, 21.655, 1.548, 22.483, 1.585, 2, 22.5, 0.585, 1, 23.333, 0.622, 24.167, 0.659, 25, 0.696, 1, 25.006, 0.659, 25.011, 0.622, 25.017, 0.585, 1, 25.511, 0.607, 26.006, 0.629, 26.5, 0.651]}, {"Target": "Parameter", "Id": "GL14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 6.667, 0.297, 13.333, 0.593, 20, 0.89, 1, 20.828, 0.927, 21.655, 0.963, 22.483, 1, 2, 22.5, 0, 1, 23.333, 0.037, 24.167, 0.074, 25, 0.111, 1, 25.006, 0.074, 25.011, 0.037, 25.017, 0, 1, 25.511, 0.022, 26.006, 0.044, 26.5, 0.066]}, {"Target": "Parameter", "Id": "Param161", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.51, 1, 5.994, 0.843, 11.989, 1.177, 17.983, 1.51, 2, 18, 0.51, 1, 18.667, 0.547, 19.333, 0.584, 20, 0.621, 1, 21.667, 0.714, 23.333, 0.806, 25, 0.899, 1, 25.006, 0.769, 25.011, 0.64, 25.017, 0.51, 1, 25.511, 0.537, 26.006, 0.565, 26.5, 0.592]}, {"Target": "Parameter", "Id": "Param162", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.31, 1, 5.994, 0.643, 11.989, 0.977, 17.983, 1.31, 2, 18, 0.31, 1, 18.667, 0.347, 19.333, 0.384, 20, 0.421, 1, 21.667, 0.514, 23.333, 0.606, 25, 0.699, 1, 25.006, 0.569, 25.011, 0.44, 25.017, 0.31, 1, 25.511, 0.337, 26.006, 0.365, 26.5, 0.392]}, {"Target": "Parameter", "Id": "Param163", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.72, 1, 5.994, 1.053, 11.989, 1.387, 17.983, 1.72, 2, 18, 0.72, 1, 18.667, 0.757, 19.333, 0.794, 20, 0.831, 1, 21.667, 0.924, 23.333, 1.016, 25, 1.109, 1, 25.006, 0.979, 25.011, 0.85, 25.017, 0.72, 1, 25.511, 0.747, 26.006, 0.775, 26.5, 0.802]}, {"Target": "Parameter", "Id": "Param164", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 1, 5.994, 0.333, 11.989, 0.667, 17.983, 1, 2, 18, 0, 1, 18.667, 0.037, 19.333, 0.074, 20, 0.111, 1, 21.667, 0.204, 23.333, 0.296, 25, 0.389, 1, 25.006, 0.259, 25.011, 0.13, 25.017, 0, 1, 25.511, 0.027, 26.006, 0.055, 26.5, 0.082]}, {"Target": "Parameter", "Id": "Param165", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.17, 1, 5.994, 0.503, 11.989, 0.837, 17.983, 1.17, 2, 18, 0.17, 1, 18.667, 0.207, 19.333, 0.244, 20, 0.281, 1, 21.667, 0.374, 23.333, 0.466, 25, 0.559, 1, 25.006, 0.429, 25.011, 0.3, 25.017, 0.17, 1, 25.511, 0.197, 26.006, 0.225, 26.5, 0.252]}, {"Target": "Parameter", "Id": "Param112", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.04, 1, 8.333, 0.41, 16.667, 0.781, 25, 1.151, 1, 25.006, 0.781, 25.011, 0.41, 25.017, 0.04, 1, 25.511, 0.062, 26.006, 0.084, 26.5, 0.106]}, {"Target": "Parameter", "Id": "Param115", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.68, 1, 8.333, 1.05, 16.667, 1.421, 25, 1.791, 1, 25.006, 1.421, 25.011, 1.05, 25.017, 0.68, 1, 25.511, 0.702, 26.006, 0.724, 26.5, 0.746]}, {"Target": "Parameter", "Id": "Param113", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.91, 1, 8.333, 1.28, 16.667, 1.651, 25, 2.021, 1, 25.006, 1.651, 25.011, 1.28, 25.017, 0.91, 1, 25.511, 0.932, 26.006, 0.954, 26.5, 0.976]}, {"Target": "Parameter", "Id": "Param116", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.44, 1, 8.333, 0.81, 16.667, 1.181, 25, 1.551, 1, 25.006, 1.181, 25.011, 0.81, 25.017, 0.44, 1, 25.511, 0.462, 26.006, 0.484, 26.5, 0.506]}, {"Target": "Parameter", "Id": "Param114", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.58, 1, 8.333, 0.95, 16.667, 1.321, 25, 1.691, 1, 25.006, 1.321, 25.011, 0.95, 25.017, 0.58, 1, 25.511, 0.602, 26.006, 0.624, 26.5, 0.646]}, {"Target": "Parameter", "Id": "Param117", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.08, 1, 8.333, 0.45, 16.667, 0.821, 25, 1.191, 1, 25.006, 0.821, 25.011, 0.45, 25.017, 0.08, 1, 25.511, 0.102, 26.006, 0.124, 26.5, 0.146]}, {"Target": "Parameter", "Id": "touch_drag21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param54", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param118", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "All_Angle", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "man_X", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "man_Y", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param78", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param79", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "man_<PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "man_<PERSON>ze", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param189", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "shangguang", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param186", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param96", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param97", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param98", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param111", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "shangguang2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param53", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param99", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param76", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param77", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "MB_xingfengTMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param127", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param103", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param104", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param106", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param105", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param110", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "MB_xingfengyaobai2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param130", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param107", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param108", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param109", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Mouthfunnel", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param293", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param72", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param74", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param75", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param73", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "MB_fenweiTMD", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "MB_fenweiliangdu", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 26.5, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiandu", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.6, 0, 26.5, 0.6]}, {"Target": "Parameter", "Id": "MB_fenweiqiu1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 26.5, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 26.5, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 26.5, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 26.5, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 26.5, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiu6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0.7, 0, 26.5, 0.7]}, {"Target": "Parameter", "Id": "MB_fenweiqiushangxia", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "MB_fenweiqiudaxiao", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param102", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param167", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param169", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param196", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param56", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param59", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 10, 0, 26.5, 10]}, {"Target": "Parameter", "Id": "Param44", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param57", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param124", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param80", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param47", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param81", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param95", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param125", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param166", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param123", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamCheek", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 26.5, 1]}, {"Target": "Parameter", "Id": "Param13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param39", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param61", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 26.5, 1]}, {"Target": "Parameter", "Id": "Param71", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 26.5, 1]}, {"Target": "Parameter", "Id": "Param173", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param174", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param175", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param176", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param190", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param191", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param192", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param193", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param65", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 1, 0, 26.5, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamNeckZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamShoulderStretch3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamShoulderStretch4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamChestZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamWaistZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamButtZ2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param87", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param94", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param70", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param208", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "Param23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "touch_drag23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "touch_drag24", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "touch_drag25", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "touch_drag26", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "touch_drag27", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "touch_drag28", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "touch_drag29", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "touch_drag30", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "touch_drag31", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "touch_drag32", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON>", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "empty1", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "empty2", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "empty3", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "empty4", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "empty5", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "empty6", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "empty7", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "empty8", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "empty9", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "empty10", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "empty11", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "empty12", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "empty13", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "empty14", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "empty15", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "empty16", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "empty17", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "empty18", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "empty19", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "empty20", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "empty21", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "empty22", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}, {"Target": "Parameter", "Id": "empty23", "FadeInTime": -1.0, "FadeOutTime": -1.0, "Segments": [0, 0, 0, 26.5, 0]}], "UserData": [{"Time": 0.0, "Value": ""}, {"Time": 26.0, "Value": ""}]}